# multime_app

## Getting Started

`flutter pub get`
`flutter pub run build_runner build --delete-conflicting-outputs`
IOS
`cd ios && pod install`

# Build Android

Run command before build

`flutter pub get`
`flutter pub run intl_utils:generate`
`flutter pub run build_runner build --delete-conflicting-outputs`
`flutter build apk --release --flavor ${schemeName}`

# Get l10n

`dart run easy_localization:generate -S assets/translations -O lib/core/l10n -o locale_keys.g.dart -f keys`

# Build iOS

Run command before build

`flutter pub get`
`flutter pub run build_runner build --delete-conflicting-outputs`
`flutter build ios --release --flavor ${schemeName}`

---

`flutter run -t lib/main.dart --flavor dev`

`
app_initializer.dart

- Firebase services
- Database initialization
- API clients
- Background services
- Third-party SDKs
- Storage systems
- Network configurations
- Push notifications
- Analytics services
- Crash reporting
- Payment gateways
- AI/ML services
- `
`
  // app.dart

- BlocProviders
- Theme management
- Language management
- App lifecycle handling
- Global state management
- Navigation setup
- Error handling widgets
- App-level listeners
- Global event handlers
- User session management
  `

## 👥 Development Team

**Multime App** is proudly developed by our talented team:

| Developer             | GitHub Profile                                         | Role                |
| --------------------- | ------------------------------------------------------ | ------------------- |
| **Nguyen Duc Kien**   | [@DuckienDev](https://github.com/DuckienDev)           | Lead Developer      |
| **Do Viet Soai**      | [@DVSoai](https://github.com/DVSoai)                   | FullStack Developer |
| **Nguyen Hoang Tam**  | [@nghtamm](https://github.com/nghtamm)                 | Mobile Developer    |
| **Truong Manh Hoang** | [@TruongManhHoang](https://github.com/TruongManhHoang) | Software Engineer   |

_Made with by the Multime Development Team_
