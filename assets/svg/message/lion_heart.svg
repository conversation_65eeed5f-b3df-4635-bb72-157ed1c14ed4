<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" version="1.1" width="64.1434" height="76.240238" viewBox="0 0 64.1434 76.240238">
<defs>
<clipPath id="clip_0">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
<clipPath id="clip_1">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
<clipPath id="clip_2">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
<clipPath id="clip_3">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
<clipPath id="clip_4">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
<clipPath id="clip_5">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
<clipPath id="clip_6">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
<clipPath id="clip_7">
<path transform="matrix(1,0,0,-1,0,76.240238)" d="M0 0H64.1434V76.240238H0Z"/>
</clipPath>
</defs>
<g>
<path transform="matrix(1,0,0,-1,47.886598,59.34375)" d="M0 .106361 .066115 1.600562 .026472 .84685 .052911 1.600562C.132249 1.600562 1.957016 1.56089 3.186754 3.412109L4.442938 2.592286C2.737172 .00058 .105784 .093138 0 .106361Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,47.68698,59.44336)" d="M.265723 .007218C.226054 .007218 .186404 .007218 .186404 .007218 .08062 .007218-.01196 .09978 .001263 .218787L.067378 1.712987C.067378 1.818771 .159939 1.911331 .265723 1.898108 .397953 1.898108 2.077279 1.898109 3.214457 3.630321 3.240903 3.66999 3.293795 3.709656 3.346687 3.709656 3.399579 3.722879 3.452483 3.709658 3.492152 3.683212L4.748337 2.863385C4.840898 2.79727 4.867331 2.678262 4.801216 2.585701 3.188011 .126225 .754974 .007218 .265723 .007218ZM.424425 .946051 .397953 .403908C1.019433 .430354 2.963213 .668371 4.364851 2.638597L3.439241 3.246851C2.394625 1.792322 .979784 1.554311 .450864 1.514642L.424425 .946051Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,50.068208,60.148439)" d="M1.203457 2.684468C1.203457 2.684468-.660978 4.535686 .251408 6.32079 1.163795 8.105894 3.596826 7.960441 4.152191 8.291016 4.152191 8.291016 3.795171 7.457967 3.424927 7.233176 3.424927 7.233176 5.302585 8.317462 7.021574 7.576975 7.021574 7.576975 6.995135 6.651362 6.598445 5.672861 6.598445 5.672861 6.823236 5.302618 7.325709 5.48774 7.312487 5.500962 5.024902 .026646 1.203457 2.684468Z" fill="#dd1f37"/>
<path transform="matrix(1,0,0,-1,49.859285,58.40039)" d="M3.461972 .011509C2.747931 .011509 2.020679 .262744 1.293415 .778441 1.280192 .791664 1.280166 .791666 1.266943 .804889 1.187605 .884227-.69006 2.788336 .275218 4.679224 .976037 6.067638 2.509904 6.358545 3.514852 6.543666 3.832203 6.609781 4.123109 6.662673 4.242116 6.728787 4.321454 6.768456 4.414021 6.768455 4.480136 6.70234 4.546252 6.636225 4.559494 6.55689 4.533048 6.477551 4.519825 6.437882 4.427258 6.239538 4.295028 6.014747 5.035515 6.265984 6.185909 6.503998 7.29664 6.027971 7.375978 6.001524 7.415635 5.922184 7.415635 5.842845 7.415635 5.803176 7.389188 4.943685 7.018945 3.978407 7.08506 3.925515 7.230519 3.859397 7.45531 3.938735 7.534648 3.965181 7.613999 3.951959 7.666891 3.885844 7.719783 3.832952 7.733012 3.740391 7.706566 3.674275 7.653674 3.555269 6.463592 .765219 4.321467 .130516 4.043784 .051178 3.752878 .011509 3.461972 .011509ZM1.531403 1.082569C2.443789 .447865 3.342978 .262744 4.202473 .513981 5.762786 .963563 6.807403 2.761891 7.177647 3.502378 6.886741 3.528824 6.688389 3.700723 6.609051 3.832953 6.582605 3.885845 6.569369 3.951958 6.595815 4.00485 6.886721 4.732115 6.979295 5.41971 7.005741 5.710616 5.432205 6.292427 3.726419 5.340373 3.713196 5.327149 3.620636 5.274258 3.501628 5.300704 3.448736 5.393265 3.395844 5.485826 3.422291 5.604833 3.514852 5.657724 3.647081 5.737062 3.818987 5.975078 3.951217 6.213092 3.845433 6.186646 3.713196 6.160199 3.580966 6.133753 2.655357 5.961854 1.24051 5.697394 .619029 4.480879-.161128 2.920566 1.38595 1.241244 1.531403 1.082569Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,40.19098,49.478517)" d="M4.099128-.000852C8.515607 .871866 12.178382 3.860261 14.386622 7.78749 13.831257 8.67343 13.037877 9.665156 12.337058 10.445312 8.661067 7.95939 4.403256 6.319736 0 5.711479 1.229738 3.780922 2.856166 1.929706 4.099128-.000852Z" fill="#ea2d2d"/>
<path transform="matrix(1,0,0,-1,42.139894,44.398439)" d="M1.330294 .553217C.933605 .473879 .536915 .420985 .140225 .381316-.071343 .35487-.031667 .037519 .179901 .063965 .57659 .103634 .97328 .156524 1.36997 .235862 1.568315 .275531 1.528639 .592886 1.330294 .553217Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,43.50177,45.552736)" d="M1.290619 .740802C.920375 .582126 .536915 .476343 .140225 .423451-.071343 .397005-.031667 .066431 .179901 .1061 .603036 .172215 1.012936 .278 1.422849 .449899 1.607971 .516014 1.488964 .82014 1.290619 .740802Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,44.878938,43.816408)" d="M.91853 .802989C.65407 .670759 .376362 .551751 .098679 .445967-.099666 .366629 .032583 .075725 .217705 .14184 .495388 .247624 .773064 .366632 1.037524 .498862 1.235869 .591423 1.116875 .89555 .91853 .802989Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,45.685487,44.35547)" d="M.931743 .851031C.667283 .705579 .389594 .573349 .098688 .467565-.099657 .388228 .03256 .0841 .217682 .163438 .508588 .282445 .773054 .401451 1.050737 .546904 1.235859 .652688 1.116865 .956815 .931743 .851031Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,46.252656,45.38086)" d="M.959676 .954006C.681993 .782107 .40431 .610211 .100181 .477981-.084941 .38542 .007627 .081289 .205972 .17385 .523324 .332526 .840656 .504424 1.144785 .689546 1.316684 .79533 1.131575 1.05979 .959676 .954006Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,47.27826,43.05664)" d="M.872969 .995212C.621733 .81009 .370509 .638188 .092826 .492736-.092296 .400175 .026698 .096047 .21182 .188608 .515949 .347284 .806854 .519184 1.071314 .730752 1.243213 .862982 1.044868 1.127442 .872969 .995212Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,48.17221,43.57617)" d="M.825276 1.080111C.560816 .881766 .309573 .683421 .058336 .485076-.10034 .352846 .098005 .088387 .256681 .23384 .507917 .445408 .759161 .643753 1.023621 .828875 1.19552 .947882 .983952 1.199118 .825276 1.080111Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,45.302827,49.117189)" d="M6.960718 7.377625C7.63509 7.390848 8.256557 7.099943 8.7987 6.663585 8.957376 6.914822 9.116053 7.17928 9.261505 7.44374 8.70614 8.32968 7.91276 9.321406 7.211942 10.101562 6.484677 9.599089 5.717763 9.149508 4.95083 8.713148 3.430185 7.35118 1.764069 6.174332 .66656 4.256998-.100373 2.934699-.179685 1.374384 .296342-.000807 .970715 .197538 1.618616 .461999 2.253319 .752905 1.816961 3.754525 3.972322 7.166058 6.960718 7.377625Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,40.19098,49.478517)" d="M10.075913 9.058594C6.955287 7.339605 3.530539 6.189205 0 5.699955 1.229738 3.769398 2.856166 1.931401 4.099128 .000844 4.548709 .093405 4.985087 .199189 5.408223 .331419 4.932196 1.719833 5.024731 3.280144 5.778441 4.58922 6.889173 6.519777 8.542047 7.696626 10.075913 9.058594Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,19.93509,71.13281)" d="M5.93532 29.666344C5.816313 26.717617 4.665913 23.927565 3.422952 21.25652 2.179991 18.585476 .818022 15.91443 .249433 13.031818-.319155 10.149206 .024643 6.909575 1.902307 4.635221 3.489067 2.704664 5.961766 1.726158 8.381574 1.144348 11.793105 .337746 15.376534 .205515 18.840958 .747658 21.63101 1.184017 24.526852 2.188967 26.219395 4.436876 27.34335 5.931074 27.8326 7.821955 27.872269 9.686398 27.911938 11.550838 27.541687 13.402061 27.10533 15.213612 25.902037 20.172234 24.156615 24.985404 21.895483 29.560559 16.40794 29.944025 11.422862 30.155596 5.93532 29.666344Z" fill="#ea2d2d"/>
<path transform="matrix(1,0,0,-1,27.325715,67.46484)" d="M7.959414 .360624C9.466835 .400293 10.987486 .863102 12.098217 1.828382 13.433739 2.992004 14.041981 4.737435 14.21388 6.456425 14.425447 8.638218 14.015551 10.872904 13.023827 12.869576 12.11144 14.707571 10.643682 16.38689 8.686678 17.21994 6.729675 18.066212 4.256979 17.947205 2.656997 16.624905 1.612381 15.76541 1.030576 14.509226 .647109 13.253042-.18594 10.542328-.212395 7.620049 .554538 4.882889 .858667 3.811827 1.334692 2.740761 2.154517 1.920936 3.609046 .43996 5.962742 .307734 7.959414 .360624Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,47.780885,61.91992)" d="M.079319-.000605C.052873 .012618 .026446 .02584 0 .039062 0-.000606 .03965-.000605 .079319-.000605Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,28.541443,57.316408)" d="M.11901 1.874783C.039672 1.372309 0 .856612 0 .354138 0 .102902 .38347 .102902 .38347 .354138 .38347 .830166 .409922 1.306192 .48926 1.78222 .528929 2.007011 .158679 2.112797 .11901 1.874783Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,30.270569,55.498048)" d="M.174874 1.223382C.108759 .972145 .042638 .707686 .002969 .443226-.0367 .205212 .33355 .09943 .373219 .337444 .412888 .601904 .47901 .866363 .545125 1.1176 .61124 1.368837 .240989 1.461396 .174874 1.223382Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,30.11496,58.359376)" d="M.39669 1.39751C.409913 1.648747 .026443 1.648747 .01322 1.39751-.000003 1.040489 0 .670244 0 .313223 0 .061986 .38347 .061986 .38347 .313223 .370247 .670244 .370244 1.027266 .39669 1.39751Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,28.218262,61.414064)" d="M.561081 .407171C.428851 1.015429 .375953 1.623685 .389176 2.245166 .402399 2.496403 .018929 2.496403 .005706 2.245166-.02074 1.597239 .045378 .949316 .190831 .314613 .243723 .063376 .613973 .169157 .561081 .407171Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,30.20462,62.61133)" d="M.809584 .429278C.61124 .984644 .46578 1.540008 .373219 2.121819 .33355 2.359833-.0367 2.267274 .002969 2.016037 .09553 1.434226 .240989 .878862 .439334 .323496 .518672 .098705 .888922 .204487 .809584 .429278Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,29.469178,64.93945)" d="M.936684 .506302C.725116 .942661 .54 1.37902 .381325 1.828601 .301987 2.053392-.068264 1.960833 .011074 1.722819 .182973 1.246791 .381318 .770762 .606109 .307957 .711893 .096389 1.042468 .281511 .936684 .506302Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,31.55484,66.01367)" d="M1.33703 .559305C.94034 1.02211 .609772 1.551028 .384981 2.119617 .29242 2.344408-.07783 2.251848 .014731 2.013835 .279191 1.379131 .622989 .810546 1.07257 .281626 1.218023 .096504 1.495706 .374184 1.33703 .559305Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,23.509278,52.98633)" d="M.496652 1.431035C.324753 1.140129 .166064 .836002 .020611 .531873-.085173 .307082 .245402 .12196 .351186 .333528 .496639 .637657 .655328 .941784 .827227 1.23269 .946234 1.444258 .615659 1.642602 .496652 1.431035Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,24.579865,54.15039)" d="M.338445 1.340569C.206216 1.062886 .087209 .77198 .007871 .467851-.058244 .229837 .312006 .124055 .378121 .362068 .457459 .626528 .550013 .890987 .66902 1.142224 .774804 1.367015 .444229 1.56536 .338445 1.340569Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,23.029541,54.728517)" d="M.249095 1.152822C.169757 .914808 .090412 .663571 .011074 .425557-.068264 .187543 .301987 .09498 .381325 .319771 .460663 .557784 .539991 .809021 .619329 1.047035 .685444 1.285049 .31521 1.377612 .249095 1.152822Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,24.30487,56.953126)" d="M.362186 1.682117C.203511 1.272204 .084494 .862292 .005156 .439157-.047736 .201143 .322514 .09536 .375406 .333374 .454744 .75651 .573745 1.166422 .732421 1.576335 .811759 1.814349 .454747 1.906908 .362186 1.682117Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,22.741639,56.68164)" d="M.166789 1.055291C.113897 .843723 .061012 .645376 .00812 .433809-.057995 .195795 .299016 .103235 .378354 .328026 .444469 .526371 .49737 .737937 .537039 .949504 .589931 1.187518 .219681 1.293305 .166789 1.055291Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,25.888642,55.18164)" d="M.285799 1.338368 .008119 .425983C-.057996 .187969 .299032 .095405 .37837 .320196 .47093 .624325 .563488 .928453 .656049 1.232582 .722164 1.470596 .365137 1.563159 .285799 1.338368Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,28.971467,69.83984)" d="M1.487298 .486248C1.077386 .525917 .680696 .631704 .297229 .803603 .072438 .896164-.125907 .578812 .098884 .473028 .548466 .274683 .998048 .155678 1.487298 .102786 1.725312 .07634 1.725312 .459802 1.487298 .486248Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,28.111939,68.47266)" d="M1.315409 .496026C.958388 .562141 .627804 .66792 .297229 .813373 .072438 .919157-.125907 .588582 .098884 .482798 .455905 .324122 .826152 .205122 1.209619 .125784 1.447632 .086115 1.553423 .443134 1.315409 .496026Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,31.805542,69.03125)" d="M1.205226 .419649C.874651 .419649 .557293 .446092 .239941 .498984 .001927 .538653-.103847 .168411 .134166 .128742 .491187 .07585 .848205 .036179 1.205226 .036179 1.456463 .036179 1.456463 .419649 1.205226 .419649Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,33.774354,69.97461)" d="M1.550402 .488928C1.100821 .436036 .638009 .422813 .188428 .422813-.062809 .422813-.062809 .039351 .188428 .039351 .651232 .026128 1.100821 .052574 1.550402 .105466 1.801639 .131912 1.801639 .515374 1.550402 .488928Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,30.3378,67.61719)" d="M1.813491 .446963C1.284571 .526301 .755648 .618865 .239951 .698203 .001937 .737872-.103853 .36763 .134161 .327961 .663081 .248623 1.192004 .156058 1.7077 .07672 1.958937 .037051 2.051505 .407294 1.813491 .446963Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,22.630738,59.09375)" d="M19.212998 17.537682C13.712234 17.907925 8.727175 18.119496 3.239633 17.630246 3.120626 14.681517 1.970226 11.891464 .727265 9.22042 .489251 8.704723 .238014 8.189025 0 7.673328 3.504093 7.184077 6.70406 4.129566 9.904024 1.987442 11.689129 .837042 13.72546 .202341 15.761801 .969275 18.684082 2.119675 19.583243 5.174183 22.122057 6.946064 22.4923 7.184077 22.862545 7.342752 23.246012 7.46176 22.161726 10.912961 20.81298 14.284825 19.212998 17.537682Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,19.921875,70.91211)" d="M27.131785 15.007641C26.787987 16.448948 26.391297 17.863806 25.941716 19.278666 25.558249 19.172883 25.174782 19.000985 24.81776 18.76297 22.278946 16.977866 21.37977 13.923357 18.457489 12.78618 16.421149 12.019246 14.384799 12.653948 12.599695 13.804348 9.399731 15.933249 6.19978 19.000985 2.695687 19.490235 2.44445 18.961316 2.206436 18.432396 1.968422 17.903475 3.198161 17.797692 4.348561 17.414227 5.208055 16.607625 8.89727 13.169645 13.221189 8.211022 18.695509 12.283703 19.462443 12.918407 20.864073 13.169643 21.498778 12.415934 23.667347 9.996126 21.882245 5.804438 19.978133 3.635867 16.791392 .078882 10.682368 1.982996 8.011323 5.672212 6.358449 8.211026 5.72374 10.89529 4.070866 13.434105 3.317155 14.571283 2.259328 15.391109 1.108928 15.814244 .751907 14.835743 .447778 13.844017 .249433 12.825848-.319155 9.930012 .024643 6.703602 1.902307 4.429247 3.489067 2.49869 5.961766 1.520185 8.381574 .938374 11.793105 .131771 15.376534-.00046 18.840958 .541683 21.63101 .978043 24.526835 1.982994 26.219378 4.230902 27.343333 5.725101 27.832585 7.61599 27.872254 9.480432 27.938368 11.344873 27.568144 13.196091 27.131785 15.007641Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,15.569626,72.791019)" d="M4.747057 16.898438 0 9.96959C0 9.96959 5.117295 5.36799 7.418096 3.979576L7.299085 3.556439C7.299085 3.556439 10.710621 1.334974 14.690742 .885393L14.664302 .396141C14.664302 .396141 22.690659-.000547 25.374926 .687048L25.414586 1.268863C25.414586 1.268863 29.103819 2.035796 31.087269 4.045691L30.875687 4.693613C30.875687 4.693613 34.300458 6.941525 36.244237 10.76297L32.224428 15.774483C32.19798 15.774483 18.022942 9.467115 4.747057 16.898438Z" fill="#c60021"/>
<path transform="matrix(1,0,0,-1,15.368637,72.7207)" d="M19.731358 .021927C17.258658 .021927 15.023985 .127714 14.852086 .140938 14.799194 .140938 14.746303 .167383 14.719856 .207052 14.680187 .246721 14.666961 .29961 14.666961 .352503L14.680181 .65663C10.779398 1.14588 7.420761 3.301229 7.394315 3.327675 7.3282 3.380568 7.288535 3.459907 7.314981 3.552467L7.394315 3.830148C5.053845 5.284677 .280342 9.582153 .068774 9.767275-.010564 9.833389-.02378 9.939174 .042335 10.031734L4.789376 16.96058C4.842268 17.039919 4.961275 17.066367 5.053836 17.026698 11.863677 13.218475 18.911536 13.11269 23.618922 13.707725 28.73622 14.342428 32.293195 15.902743 32.31964 15.915966 32.39898 15.955635 32.50477 15.929186 32.55766 15.86307L36.57747 10.851562C36.63036 10.785446 36.630357 10.706104 36.60391 10.639989 34.871698 7.228457 31.93618 5.033442 31.301476 4.583859L31.473381 4.081388C31.499827 4.015272 31.473395 3.935935 31.420502 3.883042 29.622176 2.05827 26.514773 1.251665 25.80073 1.079766L25.774258 .64341C25.774258 .550848 25.708163 .48473 25.628825 .458284 24.346196 .114487 21.95282 .021927 19.731358 .021927ZM15.063651 .524399C16.293389 .471508 22.865218 .220274 25.377585 .775639L25.404025 1.211996C25.404025 1.304558 25.47015 1.370676 25.56271 1.397121 25.602379 1.410345 29.106472 2.150827 31.050251 4.028492L30.878348 4.557412C30.8519 4.63675 30.878359 4.742536 30.957697 4.782205 30.997366 4.80865 34.28987 7.003671 36.193986 10.653217L32.34611 15.453161C31.63207 15.162255 28.273415 13.839956 23.685036 13.271368 18.93798 12.676332 11.87691 12.782116 5.014177 16.550669L.465464 9.912724C1.272066 9.18546 5.675326 5.297898 7.72489 4.06816 7.804228 4.028491 7.843891 3.935936 7.817445 3.843375L7.73811 3.565687C8.386037 3.168998 11.453777 1.397119 14.918201 1.013651 15.023985 1.000429 15.103313 .907871 15.090091 .802088L15.063651 .524399Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,19.417542,63.652345)" d="M29.447615 5.170038C25.441048 3.728732 12.54862-.000153 0 6.214653L.938829 7.589844C14.214714 .158522 28.389775 6.465889 28.389775 6.465889L29.447615 5.170038Z" fill="#a30000"/>
<path transform="matrix(1,0,0,-1,16.019227,71.984378)" d="M19.490694 .000766C17.17667 .000766 14.98165 .185887 14.809752 .199111L14.611406 .212331 14.651066 .847033C14.584951 .847033 14.532072 .847033 14.465957 .847033 12.099041 .847033 7.735451 2.671808 7.537106 2.751147L7.351981 2.830481 7.656116 3.557746C5.910681 4.364348 .251237 9.653548 0 9.891562L.27768 10.18247C2.036338 8.529596 6.862736 4.179225 7.986691 3.861873L8.198256 3.795758 7.8809 3.042054C8.80651 2.67181 12.958523 1.032159 14.849411 1.25695L15.087431 1.283389 15.047755 .569353C16.171711 .490015 21.870823 .093322 24.02617 .847033L24.065846 1.600744 24.211297 1.627192C24.250966 1.640415 27.715395 2.394122 29.27571 3.359401L28.892222 4.179228 29.050925 4.271791C29.07737 4.285014 32.290544 6.083339 35.05415 10.724609L35.39796 10.526264C32.898817 6.334576 30.108757 4.456914 29.407938 4.033779L29.791392 3.240399 29.645958 3.147836C28.257544 2.209003 25.163359 1.455294 24.449316 1.296618L24.409642 .582573 24.29063 .529685C23.246014 .119773 21.32869 .000766 19.490694 .000766Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,16.746476,71.166019)" d="M19.094004 .000918C17.17667 .000918 15.232881 .199265 15.100651 .225711L14.915525 .252158 14.968421 1.124873C12.416384 .49017 8.092464 2.354614 7.89412 2.433952L7.735451 2.500068 7.986691 3.280227C5.011518 4.562857 .211568 9.693376 0 9.918167L.290899 10.182627C.343791 10.129735 5.43464 4.695081 8.304029 3.571126L8.475935 3.505012 8.224695 2.72486C9.084189 2.381062 13.011415 .860416 15.113871 1.574458L15.391566 1.667021 15.325452 .595953C17.057663 .424054 21.130343 .172822 22.280743 .807526L22.346858 1.9447H22.531983C22.558428 1.9447 25.295582 1.971148 27.384814 3.095102L27.120354 3.822367 27.252585 3.901701C27.305476 3.94137 32.674 7.392573 33.6525 10.671875L34.035985 10.552869C33.08393 7.392574 28.455889 4.219058 27.596396 3.650469L27.860856 2.896757 27.71539 2.817422C25.798056 1.733137 23.391469 1.561236 22.70387 1.53479L22.650977 .569513 22.571642 .516619C21.976607 .119928 20.53531 .000918 19.094004 .000918Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,19.536576,61.498048)" d="M4.112348 13.539425 0 6.18744C0 6.18744 4.667716 1.546169 12.958532 .766014 21.24935-.000921 30.029429 4.706464 30.029429 4.706464L23.946852 18.498047C23.960076 18.498047 14.175044 11.674982 4.112348 13.539425Z" fill="#c60021"/>
<path transform="matrix(1,0,0,-1,19.342362,61.226564)" d="M15.017188 .2304C14.382484 .2304 13.76101 .256847 13.139529 .309738 4.875159 1.076672 .101656 5.731164 .061987 5.784056-.004128 5.850171-.017357 5.94273 .022312 6.022068L4.134676 13.360829C4.174345 13.440167 4.266902 13.479837 4.34624 13.453392 14.19737 11.628619 23.942719 18.319453 24.035279 18.385567 24.088171 18.425236 24.14107 18.425238 24.207184 18.412014 24.260077 18.39879 24.312968 18.359124 24.339415 18.306233L30.42199 4.514647C30.461659 4.422087 30.421965 4.303081 30.329403 4.263412 30.250065 4.223742 22.686524 .2304 15.017188 .2304ZM.445457 5.955954C1.199168 5.268357 5.747867 1.394024 13.179189 .706429 20.703072 .00561 28.742665 3.906392 29.985625 4.541096L24.074954 17.94921C23.413805 17.51285 21.16589 16.111212 18.084933 14.881474 14.713069 13.532728 9.622211 12.144316 4.425575 13.056702L.445457 5.955954Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,13.990784,76.38086)" d="M13.22835 14.232119C12.752321 15.792432 12.117621 17.511424 10.636646 18.185797 9.724259 18.595709 8.600311 18.489926 7.767263 17.921338 6.947438 17.352749 6.45818 16.321352 6.537519 15.316405 5.757362 15.91144 4.844982 16.453585 3.866481 16.466809 2.940871 16.48003 2.041704 15.924662 1.631792 15.091613 1.235102 14.258564 1.354099 13.200724 1.935911 12.486682 .573942 12.208999-.166532 10.661913 .031813 9.299944 .243381 7.924753 1.182213 6.774356 2.213607 5.848746 5.651585 2.754566 12.382084 .162859 15.582048 3.83885 17.909296 6.523117 13.942391 11.918095 13.22835 14.232119Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,13.788086,74.71289)" d="M11.421149 .174122C8.102178 .174122 4.399744 2.131125 2.284065 4.035236 .948543 5.225306 .221278 6.388929 .036156 7.605445-.162189 8.914521 .459279 10.488059 1.808024 10.937641 1.331996 11.704575 1.265885 12.696299 1.662575 13.516125 2.098933 14.415288 3.050989 15.010321 4.042713 15.010321 4.055936 15.010321 4.069166 15.010321 4.082388 15.010321 5.034444 14.997098 5.880709 14.534296 6.555082 14.058268 6.607974 14.9971 7.110447 15.896263 7.877381 16.425183 8.763322 17.046664 9.953387 17.152449 10.931889 16.702866 12.478978 16.002049 13.140131 14.230165 13.629382 12.630182 13.814504 12.04837 14.197981 11.268212 14.647562 10.355825 15.956638 7.737674 17.75496 4.141021 15.930186 2.051787 14.951685 .927834 13.602933 .293129 11.923613 .200569 11.764937 .174124 11.593048 .174122 11.421149 .174122ZM4.042713 14.613631C3.209665 14.613631 2.376626 14.097938 2.019605 13.344227 1.649361 12.590517 1.768365 11.598787 2.297285 10.950861 2.336954 10.897968 2.35017 10.81863 2.323724 10.752516 2.297278 10.686401 2.244389 10.633509 2.178274 10.620285 .908867 10.369049 .247724 8.874855 .432846 7.65834 .604745 6.547608 1.29234 5.450098 2.548525 4.326144 4.717095 2.382364 8.578202 .372471 11.897173 .58404 13.457486 .6766 14.713664 1.258411 15.626051 2.303028 17.265702 4.193915 15.546716 7.645117 14.290532 10.170709 13.827727 11.096318 13.431034 11.8897 13.245913 12.51118 12.783108 14.031824 12.161636 15.711141 10.759999 16.345845 9.900505 16.729311 8.855879 16.636752 8.088945 16.094609 7.322012 15.552465 6.872437 14.600411 6.938552 13.674802 6.938552 13.595464 6.898876 13.529346 6.832761 13.489676 6.766646 13.450007 6.674088 13.463232 6.621197 13.502901 5.946824 14.005374 5.060893 14.600409 4.069169 14.613631 4.055946 14.613631 4.055936 14.613631 4.042713 14.613631Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,23.662049,74.57617)" d="M3.556988 12.429296C3.094183 13.936716 2.472713 15.616036 1.071075 16.330079 2.261145 12.918547 3.900786 9.745026 5.209862 6.121926 5.897458 3.940132 4.945399 1.665785 2.909058 .872405 1.983449 .54183 .991724 .383147 0 .369925 2.274355-.000319 4.442938 .369929 5.897468 2.036027 8.237937 4.720294 4.27103 10.115272 3.556988 12.429296Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,18.203766,73.18359)" d="M.129505 7.071168C-.320077 5.603415 .46007 3.990215 1.557578 2.919153 3.276567 1.253056 5.841837 .512568 8.19553 1.001819 8.975686 1.160495 9.861618 1.596853 9.954179 2.390233 10.020293 2.932376 9.702949 3.434848 9.39882 3.88443 8.47321 5.246398 7.547603 6.621589 6.608771 7.983558 6.053405 8.79016 5.445141 9.649652 4.519532 9.980227 2.760874 10.61493 .618755 8.67115 .129505 7.071168Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,14.846848,68.59375)" d="M.352553 .775714C.498006 .524477 .709584 .312906 .974043 .193899 1.238503 .074892 1.569072 .074895 1.793862 .246794 2.045099 .431916 2.13766 .775712 2.190552 1.079841 2.243444 1.436862 2.269887 1.793879 2.269887 2.1509 2.269887 2.494698 2.230224 2.851722 2.045103 3.129405 1.807089 3.473202 1.344281 3.645098 .934368 3.539314-.242478 3.208739-.149921 1.582316 .352553 .775714Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,16.256073,64.078128)" d="M.8343 2.846295C.477279 2.661173 .173147 2.370267 .05414 1.9868-.091313 1.537219 .067359 1.008299 .397934 .664501 .728509 .320703 1.217766 .148806 1.693794 .162029 2.222714 .175252 2.751637 .413262 3.068988 .836398 3.38634 1.259534 3.478891 1.854567 3.240878 2.330595 2.804519 3.203312 1.601233 3.229762 .8343 2.846295Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,21.736634,62.396486)" d="M.061063 2.354845C-.150505 1.693695 .206519 .913537 .841223 .609408 1.462704 .305279 2.282532 .490401 2.745337 1.006098 2.983351 1.257335 3.128804 1.58791 3.142027 1.931707 3.15525 2.315174 2.996564 2.698639 2.718881 2.963099 1.978394 3.637472 .378415 3.359792 .061063 2.354845Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,18.190567,73.18359)" d="M9.41204 3.88443C8.48643 5.246398 7.560823 6.621589 6.621991 7.983558 6.066625 8.79016 5.458361 9.649652 4.532752 9.980227 2.76087 10.61493 .618755 8.67115 .129505 7.071168-.320077 5.590192 .46007 3.990215 1.557578 2.919153 3.276567 1.253056 5.841837 .512564 8.19553 .988591 8.975686 1.147267 9.861614 1.583626 9.967399 2.377006 10.046737 2.919148 9.729391 3.434848 9.41204 3.88443ZM9.094685 2.853038C9.002123 2.138996 8.208743 1.742307 7.507925 1.596853 5.392246 1.160495 3.078229 1.821641 1.531139 3.329062 .539414 4.294341-.161411 5.735645 .235279 7.071168 .671638 8.512474 2.615418 10.271131 4.202177 9.689319 5.035225 9.38519 5.5906 8.618259 6.079851 7.890995 6.912899 6.661257 7.745949 5.431515 8.59222 4.201777 8.869903 3.791864 9.160799 3.342288 9.094685 2.853038Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,14.855621,68.60742)" d="M2.261128 2.149844C2.261128 1.792823 2.234686 1.435797 2.181794 1.078777 2.128902 .761425 2.036341 .430852 1.785104 .24573 1.54709 .073831 1.229745 .073836 .965285 .192843 .700825 .31185 .50247 .53664 .343795 .787877 .14545 1.118452 0 1.581257 0 2.044062 .013223 1.673818 .13224 1.290349 .290916 1.02589 .423145 .814322 .595031 .629198 .806599 .536637 1.03139 .430853 1.309083 .430852 1.494205 .576304 1.705773 .734981 1.78511 1.012662 1.824779 1.277122 1.877671 1.568027 1.890894 1.872163 1.890894 2.176291 1.890894 2.467197 1.864448 2.771325 1.692549 3.009338 1.494205 3.300244 1.097518 3.445694 .75372 3.353133 .290916 3.220903 .066115 2.863887 0 2.440751 .079338 2.956448 .343804 3.40603 .89917 3.564706 1.309083 3.67049 1.771891 3.498586 2.009905 3.154788 2.221473 2.850659 2.247905 2.493642 2.261128 2.149844Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,16.256073,64.078128)" d="M3.240878 2.330595C2.804519 3.203312 1.58801 3.229762 .8343 2.846295 .477279 2.661173 .173147 2.370267 .05414 1.9868-.091313 1.537219 .067359 1.008299 .397934 .664501 .728509 .320703 1.217766 .148806 1.693794 .162029 2.222714 .175252 2.751637 .413262 3.068988 .836398 3.399563 1.259534 3.478891 1.854567 3.240878 2.330595ZM2.659079 1.352098C2.407842 1.008301 1.984703 .796732 1.548344 .757063 1.164878 .717395 .754968 .823177 .477285 1.061191 .199602 1.299205 .054146 1.695894 .15993 2.052915 .252491 2.343821 .490502 2.595056 .768185 2.753732 1.376442 3.09753 2.368166 3.150424 2.751633 2.51572 2.963201 2.158699 2.910316 1.695896 2.659079 1.352098Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,21.736634,62.396486)" d="M2.718881 2.963099C1.978394 3.637472 .378415 3.359792 .061063 2.354845-.150505 1.693695 .206519 .913537 .841223 .609408 1.462704 .305279 2.282532 .490401 2.745337 1.006098 2.983351 1.257335 3.128804 1.58791 3.142027 1.931707 3.15525 2.315174 2.996564 2.698639 2.718881 2.963099ZM2.824672 2.063937C2.811449 1.773031 2.679222 1.495349 2.480877 1.283782 2.084188 .847423 1.396582 .688743 .867663 .953202 .338743 1.204439 .021391 1.878814 .206513 2.43418 .484196 3.280451 1.832954 3.518468 2.467658 2.949879 2.705671 2.725089 2.837895 2.394512 2.824672 2.063937Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,22.06958,60.5625)" d="M.904947 1.193773C.600818 1.127658 .323128 .955762 .138007 .704525 .032223 .559072-.047106 .360725 .032232 .202049 .124793 .043373 .349574 .016928 .534696 .030151 .944609 .056597 1.394191 .188828 1.724766 .440065 2.028895 .678079 1.962789 1.021875 1.618991 1.140882 1.380978 1.246666 1.129737 1.246665 .904947 1.193773Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,18.777146,68.240238)" d="M2.108067 4.101068C1.552701 3.836608 1.089903 3.426694 .693213 2.950666 .375861 2.567199 .098182 2.144065 .018844 1.654814-.060494 1.165564 .111395 .623423 .521308 .358963 .693207 .253179 .904781 .200284 1.089903 .239953 1.314694 .292845 1.473366 .477963 1.632042 .649862 2.279969 1.377127 2.927905 2.104395 3.575832 2.818436 3.893183 3.175457 4.342765 3.86305 3.840292 4.286185 3.430379 4.643206 2.51798 4.299413 2.108067 4.101068Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,16.663025,62.92578)" d="M.26861 1.081463C.149603 .988902 .030596 .856677 .00415 .698001-.035519 .433541 .215714 .195523 .480174 .155854 .744634 .116185 1.009084 .221975 1.247098 .327759 1.471889 .433543 2.490063 1.107914 1.895028 1.359151 1.392554 1.570719 .678522 1.412038 .26861 1.081463Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,15.152252,68.009769)" d="M.007464 1.589869C-.032205 1.15351 .086799 .717149 .351259 .373352 .430597 .280791 .523151 .175012 .642158 .188235 .747942 .188235 .827286 .267571 .880178 .360132 .919847 .452693 .93307 .545255 .946293 .651039 .985962 1.087398 .985966 1.510532 .933074 1.946891 .906628 2.171682 .761175 2.766716 .404154 2.541926 .16614 2.370027 .03391 1.867551 .007464 1.589869Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,50.055238,46.064455)" d="M9.03131 1.837846C8.991641 1.692392 8.938743 1.533718 8.859405 1.401488 8.713952 1.137029 8.5156 .912237 8.29081 .727116 8.29081 .727116 8.277574 .727115 8.277574 .713891 8.052783 .555216 7.814782 .43621 7.563545 .343649 7.510653 .317204 7.457755 .303981 7.39164 .290758 7.325525 .264312 7.259423 .25109 7.206531 .237866 7.140416 .224644 7.074282 .211417 7.021389 .184972 6.955275 .171749 6.889173 .158527 6.836281 .145305 6.756943 .132081 6.677611 .11886 6.611496 .105637 6.532158 .092414 6.452827 .079189 6.386712 .079189 6.320597 .065967 6.254482 .065965 6.188367 .052742 6.122252 .052742 6.056137 .039522 5.990022 .039522H5.976786C5.910671 .039522 5.844556 .026299 5.778441 .026299 4.813163-.000148 3.861094 .184974 3.054492 .753563 2.829701 .912239 2.61814 1.097362 2.433018 1.295706 1.851206 1.903964 1.42807 2.670894 1.110718 3.424605 .489238 4.90558 .013223 6.492342 0 8.105547 0 8.660913 .079332 9.295614 .555359 9.58652 .581805 9.599743 .595028 9.612968 .621474 9.626191 .634697 9.626191 .634691 9.639411 .647914 9.639411 .67436 9.652635 .687583 9.652636 .714029 9.665859 .740475 9.679082 .766934 9.679084 .79338 9.692307 .819826 9.692307 .846252 9.705526 .872698 9.705526 .899144 9.705526 .938839 9.71875 .965285 9.71875 1.004954 9.71875 1.057827 9.71875 1.110718 9.71875H1.123954C1.137177 9.71875 1.150387 9.71875 1.176833 9.71875 1.190056 9.71875 1.216522 9.718749 1.229745 9.705526 1.242968 9.705526 1.269401 9.705529 1.282624 9.692307 1.295847 9.692307 1.32228 9.679083 1.335503 9.679083 1.375172 9.665859 1.401624 9.665858 1.441293 9.652635 1.454516 9.652635 1.480982 9.639411 1.494205 9.639411 1.626435 9.586519 1.732199 9.520404 1.837983 9.441067 2.208227 9.150161 2.433011 8.727027 2.591687 8.290668 2.73714 7.907202 2.909045 7.312166 3.094167 6.88903 3.120613 6.822916 3.147072 6.770025 3.173518 6.717133 3.292525 7.008039 3.530545 7.232829 3.808228 7.431174 4.059465 7.58985 4.323925 7.708855 4.601608 7.801416 4.694169 7.827862 4.78671 7.867532 4.879272 7.880755 5.487529 8.039431 6.148692 8.079101 6.545381 7.497289 6.558604 7.484066 6.558598 7.47084 6.571821 7.457618 6.849504 7.021259 6.862714 6.479116 6.690815 6.003088 6.664369 5.936974 6.637929 5.857638 6.59826 5.791524 7.087511 5.857638 7.563552 5.791524 7.867681 5.328719 7.99991 5.130374 8.079255 4.918805 8.1057 4.707237 8.158593 4.336993 8.079255 3.940304 7.907356 3.596506 7.894133 3.583283 7.880884 3.556838 7.880884 3.543615 8.251128 3.543615 8.608155 3.424607 8.846169 3.067586 9.084183 2.750235 9.137094 2.274204 9.03131 1.837846Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,55.9653,40.035158)" d="M.093089 .198289C-.197817 .740432 .251778 .965221 .635244 1.494141 .912927 1.057782 .926137 .515639 .754238 .039611 .45011-.000058 .198873-.000056 .093089 .198289Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,50.848085,45.640626)" d="M8.238387 1.415362C7.180548 .410415 5.606986 .000504 4.178903 .410418 3.663206 .555871 3.173955 .807106 2.724373 1.177349 .397127 3.253359-.197901 6.373986 .053335 9.322714 .079781 9.322714 .119444 9.335938 .14589 9.335938 .198782 9.335938 .238464 9.335938 .291356 9.335938H.304592C.317815 9.335938 .331025 9.335938 .357471 9.335938 .370694 9.335938 .397127 9.335937 .41035 9.322714 .423573 9.322714 .450038 9.322713 .463261 9.30949 .476484 9.30949 .502917 9.296267 .51614 9.296267 .555809 9.283043 .582262 9.283046 .62193 9.269822 .635153 9.269822 .661587 9.256598 .67481 9.256598 .727702 7.088028 1.150857 5.038465 2.18225 3.121131 2.645055 2.407089 3.372306 1.812054 4.152463 1.587263 4.787167 1.402141 5.474782 1.468256 6.122708 1.917838 6.254939 2.050068 6.281365 2.235188 6.268142 2.42031 6.254919 2.618655 6.188804 2.843447 6.135912 3.041792 6.069797 3.319475 6.030135 3.583936 6.241703 3.716166 6.479716 3.967402 6.929298 4.152524 7.299542 4.364091 7.352434 3.993848 7.273096 3.597159 7.101197 3.253361 7.087974 3.240138 7.074758 3.213689 7.074758 3.200466 7.445001 3.200466 7.802029 3.081462 8.040042 2.724442 8.291279 2.327752 8.344172 1.85172 8.238387 1.415362Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,50.055269,46.0625)" d="M9.03131 1.837398C8.991641 1.691945 8.938743 1.533271 8.859405 1.40104 8.713952 1.13658 8.5156 .911789 8.29081 .726667 8.29081 .726667 8.277574 .726666 8.277574 .713444 8.052783 .554768 7.814782 .435762 7.563545 .343201 7.510653 .316755 7.457755 .303533 7.39164 .29031 7.325525 .263864 7.259423 .250642 7.206531 .237419 7.140416 .224195 7.074282 .21097 7.021389 .184524 6.955275 .171301 6.889173 .158079 6.836281 .144856 6.756943 .131633 6.677611 .118412 6.611496 .105189 6.532158 .091966 6.452827 .078741 6.386712 .078741 6.320597 .065518 6.254482 .065516 6.188367 .052294 6.122252 .052294 6.056137 .039074 5.990022 .039074H5.976786C5.910671 .039074 5.844556 .02585 5.778441 .02585 4.813163-.000596 3.861094 .184526 3.054492 .753115 2.829701 .911791 2.61814 1.096913 2.433018 1.295259 1.851206 1.903516 1.42807 2.670446 1.110718 3.424157 .489238 4.905132 .013223 6.491894 0 8.1051 0 8.660465 .079332 9.295166 .555359 9.586072 .581805 9.599295 .595028 9.61252 .621474 9.625744 .634697 9.625744 .634691 9.638963 .647914 9.638963 .67436 9.652186 .687583 9.652187 .714029 9.665411 .740475 9.678634 .766934 9.678636 .79338 9.691858 .819826 9.691858 .846252 9.705078 .872698 9.705078 .608238 6.756351 1.216522 3.635728 3.543768 1.559718 3.99335 1.189474 4.482601 .938235 4.998298 .792782 6.399935 .422538 7.960248 .83245 9.03131 1.837398Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,23.529908,55.11328)" d="M0 7.048828 .50248 .000975 3.080964 1.283607 1.071075 .648901C1.071075 .635678 .119007 5.316616 0 7.048828Z" fill="#a30000"/>
<path transform="matrix(1,0,0,-1,23.820893,57.83789)" d="M0 10.234247C.912386 5.817768 3.927229 2.181446 7.867681-.000347 8.753621 .555018 9.732125 1.361622 10.49906 2.075664 7.986691 5.738433 6.320594 9.983013 5.672667 14.373047 3.74211 13.130086 1.917334 11.490432 0 10.234247Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,23.5961,58.0625)" d="M8.092481-.000575 7.986691 .052316C3.808225 2.36634 .912396 6.148115 .026456 10.419142L0 10.551371 .11901 10.630711C1.071066 11.252191 2.023128 11.979456 2.935514 12.693498 3.861124 13.407539 4.813179 14.148025 5.778457 14.769505L6.042917 14.941406 6.082577 14.637275C6.71728 10.273687 8.383384 6.055553 10.882529 2.41923L10.975083 2.273777 10.842854 2.154771C9.930468 1.321722 8.965196 .55479 8.171816 .065539L8.092481-.000575ZM.449585 10.36625C1.335525 6.293569 4.099141 2.696916 8.079261 .46223 8.793303 .925035 9.639574 1.586181 10.4594 2.326668 8.039593 5.883653 6.413155 10.009229 5.738782 14.253809 4.866065 13.671997 4.006567 13.010849 3.173518 12.362923 2.287578 11.68855 1.375194 10.974508 .449585 10.36625Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,23.648865,54.371095)" d="M0 6.426926C0 6.426926 .396687 1.441857 1.071059 .013774L8.423056 2.684816C8.423056 2.684816 6.307367 5.263299 5.884232 6.63849 5.897455 6.651713 3.477647 5.937675 0 6.426926Z" fill="#c60021"/>
<path transform="matrix(1,0,0,-1,23.450562,54.57422)" d="M1.269404 .01873C1.190066 .01873 1.123964 .058398 1.084295 .137736 .409922 1.579042 .013223 6.405435 0 6.617002 0 6.683117 .013223 6.736007 .066115 6.775676 .105784 6.815345 .171909 6.841794 .224801 6.828571 3.609887 6.352543 6.003235 7.040136 6.029681 7.040136 6.135465 7.066582 6.241252 7.01369 6.280921 6.907906 6.690834 5.585607 8.753624 3.046793 8.78007 3.020347 8.819739 2.967455 8.832969 2.901341 8.819746 2.84845 8.806522 2.782335 8.753628 2.742665 8.700735 2.716219L1.348755 .045173C1.322309 .03195 1.29585 .01873 1.269404 .01873ZM.423145 6.405434C.515706 5.426932 .859495 1.83028 1.388414 .481534L8.304046 2.993903C7.814795 3.615384 6.413148 5.426933 5.963566 6.617002 5.355309 6.458326 3.266089 6.048413 .423145 6.405434Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,.138458,57.115236)" d="M59.371236 35.60482C60.719984 35.631269 61.68526 35.763498 61.68526 35.763498 61.51336 32.45775 59.701799 31.677594 58.101816 31.637925 58.286939 31.161896 58.41918 30.699091 58.51174 30.24951 61.923276 27.274336 62.34641 23.254548 62.34641 23.254548 61.47369 25.092543 59.397684 25.58179 58.379514 25.71402 58.313398 25.727244 58.24728 25.727246 58.181169 25.740468 57.943155 25.766915 57.79768 25.766917 57.79768 25.766917 57.92991 25.634686 58.06214 25.502453 58.19437 25.383446 58.19437 25.290884 58.19439 25.198325 58.181169 25.118987 58.181169 25.118987 58.20761 25.1851 58.24728 25.317332 62.293519 21.006634 60.243936 15.783558 60.243936 15.783558 60.40261 18.520718 58.260488 20.49094 58.260488 20.49094 59.635679 14.765381 55.94646 11.605091 55.94646 11.605091 56.51505 14.844723 53.645677 16.576935 53.645677 16.576935 54.91508 12.834827 53.328336 11.816658 53.328336 11.816658 53.328336 11.816658 53.16964 15.162075 50.43248 13.800106 47.761438 12.47781 46.425909 14.381916 46.35979 14.487701 46.716814 13.443085 48.15813 12.861275 48.15813 12.861275 44.42925 11.499306 42.69703 13.443085 42.69703 13.443085 42.247449 12.094341 44.455694 9.872875 44.455694 9.872875 41.6392 8.775368 40.528474 10.441463 40.528474 10.441463 39.933439 9.410072 40.26401 7.029934 40.26401 7.029934 40.26401 7.029934 39.906988 6.712582 37.711969 7.307617 35.490507 7.902653 34.353326 8.960491 34.353326 8.960491 33.943414 7.267948 32.012865 6.990265 32.012865 6.990265 32.065755 7.862984 30.756678 9.952217 30.756678 9.952217 30.849238 6.514236 26.2873 5.19194 26.2873 5.19194 26.2873 5.19194 26.750102 5.615074 27.093899 7.439846 27.437697 9.291065 26.40631 9.291065 26.40631 9.291065 26.181519 6.024986 23.166676 6.315891 23.166676 6.315891 24.449307 6.977043 24.502196 8.748924 24.502196 8.748924 24.502196 8.748924 23.272454 6.937374 21.8047 5.932426 20.33695 4.927479 20.667529 2.428333 20.667529 2.428333 17.335333 4.067986 18.009709 8.537354 18.009709 8.537354 16.95187 5.720856 18.723755 2.018421 18.723755 2.018421 11.609784 4.491119 12.297383 10.692703 12.297383 10.692703 10.697401 9.938995 10.644509 6.672913 10.644509 6.672913 6.082577 11.102615 9.018071 16.65627 9.692444 17.780224 9.414761 17.383534 9.057737 17.079407 8.647824 16.854615 8.581709 16.352143 8.581709 15.995121 8.581709 15.995121 8.383365 16.193467 8.198249 16.37859 8.02635 16.576935 5.791664 15.823227 2.684258 16.643052 2.684258 16.643052 4.482585 16.497597 6.637936 18.520714 6.637936 18.520714 4.072675 16.86784 2.142115 19.618226 2.089223 19.684338 2.128892 19.657895 3.398309 18.758728 5.223082 19.406655 5.553657 19.525662 5.910668 19.724007 6.254466 19.975243 6.175128 20.292595 6.135465 20.623173 6.095796 20.940526 5.818113 20.676064 5.646211 20.49094 5.646211 20.49094 5.646211 20.49094 5.262738 21.2711 6.201571 23.320664 6.532146 25.145436 7.299092 26.573519 7.60322 27.089215 5.871008 24.563622 .595035 25.96526 .595035 25.96526 2.393362 25.819808 4.548697 27.842923 4.548697 27.842923 1.983436 26.190049 .052892 28.940434 0 29.006548 .039669 28.980103 1.30907 28.080939 3.133843 28.728865 4.971838 29.390015 7.39164 32.497419 7.39164 32.497419 5.725543 32.07428 3.570208 29.81315 3.570208 29.81315 3.570208 29.81315 3.067731 30.818097 4.575152 33.502367 5.328863 34.864336 6.651155 35.750276 7.77511 36.305639 6.637932 36.345308 5.685887 36.940343 5.685887 36.940343 5.685887 36.940343 8.780061 37.270917 9.348649 39.518827 9.533772 40.23287 9.86434 41.105585 10.234583 41.95186 9.388311 43.194818 7.49743 44.252656 7.49743 44.252656 7.999904 44.477449 8.502384 44.636125 8.991634 44.768354 9.85113 44.99314 10.684178 45.085706 11.437888 45.112154 11.543673 45.112154 11.66268 45.112154 11.768463 45.112154 13.685797 45.098928 15.034539 44.60968 15.087431 44.596456 14.492396 45.508844 11.874237 46.090654 11.874237 46.090654 11.874237 46.090654 12.799844 47.94187 16.515505 49.237726 20.006375 50.45424 20.693984 48.748476 20.760098 48.55013 19.464245 52.70215 22.70387 54.83105 22.70387 54.83105 21.289011 52.00133 23.431136 51.16828 23.431136 51.16828 24.55509 56.69549 30.082308 56.431035 30.082308 56.431035 27.688946 55.293855 27.265807 52.56992 27.252585 52.49058 31.9203 58.705389 36.93181 56.179796 36.93181 56.179796 33.21615 55.240964 32.40955 52.56992 32.356657 52.371576 32.42277 52.51703 33.11036 53.971559 36.614454 54.857496 40.290445 55.783105 42.392896 53.759988 42.392896 53.759988 41.77141 53.68065 42.11523 52.398019 42.11523 52.398019 43.4772 53.931886 45.11683 54.52692 46.35979 54.751714 47.483747 54.950059 48.290365 54.844274 48.290365 54.844274 48.290365 54.844274 46.54494 53.0195 47.047414 50.573247 47.07386 50.45424 47.087083 50.33523 47.11353 50.216226 49.440778 47.412954 54.465494 49.065828 54.465494 49.065828 52.958074 46.632799 50.101926 46.7518 50.022588 46.7518 52.825864 44.199766 59.582784 45.112154 59.582784 45.112154 59.278657 39.294038 53.896924 41.290708 53.52668 41.436159 54.63741 40.933687 55.562997 40.32543 56.34315 39.66428 56.38282 39.637834 56.40927 39.61139 56.44894 39.571718 57.90347 38.28909 58.802648 36.847784 59.371236 35.60482Z" fill="#dd1f37"/>
<path transform="matrix(1,0,0,-1,0,55.445314)" d="M18.888625 .122665C18.862177 .122665 18.848956 .122665 18.82251 .135891 15.080402 1.444965 13.493639 3.785439 12.819266 5.517651 12.303569 6.866394 12.237467 8.04324 12.237467 8.625053 11.060621 7.686222 10.994503 5.001953 10.994503 4.975506 10.994503 4.896168 10.941607 4.830055 10.875493 4.790386 10.796155 4.763939 10.71682 4.777161 10.663928 4.830051 7.384626 8.016792 7.900313 11.772125 8.720139 14.112595 8.68047 14.112595 8.62759 14.139038 8.601145 14.165485 8.429245 14.337383 8.270557 14.496059 8.125104 14.667957 5.877195 13.96714 2.928478 14.734074 2.796248 14.773743 2.690464 14.80019 2.637575 14.89275 2.650798 14.998531 2.664021 15.104317 2.756579 15.170433 2.862363 15.157211 3.787972 15.077873 4.845802 15.63324 5.612736 16.162159 5.45406 16.135712 5.30862 16.122487 5.163167 16.122487 3.351617 16.122487 2.121869 17.881146 2.095423 17.907593 2.042531 17.98693 2.055763 18.092713 2.121878 18.158829 2.187993 18.224942 2.293764 18.238167 2.373102 18.17205 2.386325 18.158829 3.602847 17.299336 5.321836 17.920815 5.586296 18.013375 5.877195 18.17205 6.20777 18.383622 6.181324 18.529072 6.154881 18.687748 6.128436 18.833203 6.022652 18.727417 5.969766 18.674527 5.969766 18.674527 5.930097 18.621636 5.863976 18.60841 5.797861 18.621632 5.731746 18.634854 5.678857 18.674527 5.652411 18.727417 5.612742 18.81998 5.255718 19.653027 6.194551 21.70259 6.392895 22.786877 6.749913 23.73893 7.067265 24.439749 4.859025 23.024888 .918586 24.043063 .733464 24.08273 .62768 24.109177 .574775 24.201735 .587998 24.307518 .601221 24.413304 .693795 24.47942 .799579 24.466197 1.725189 24.400082 2.783018 24.942225 3.549952 25.471144 3.391276 25.444699 3.24582 25.431473 3.100367 25.431473 1.288817 25.431473 .059085 27.19013 .032639 27.216578-.020253 27.295916-.007036 27.401703 .059079 27.467818 .125194 27.533932 .230981 27.547152 .310319 27.481038 .323542 27.467814 1.540063 26.60832 3.259053 27.229802 4.554906 27.692606 6.168114 29.438042 6.948271 30.363652 5.493742 29.64961 3.920192 28.00996 3.893746 27.996737 3.854077 27.943846 3.787972 27.930619 3.721857 27.943842 3.655742 27.957064 3.602854 27.996737 3.576407 28.049628 3.549962 28.089297 3.047485 29.17358 4.581352 31.923964 5.255724 33.12726 6.340007 33.960306 7.305285 34.50245 6.445791 34.674348 5.797867 35.071039 5.771421 35.08426 5.705307 35.123929 5.665627 35.216489 5.678851 35.295827 5.692074 35.375165 5.771418 35.44128 5.850756 35.454507 5.877202 35.454507 8.812709 35.7983 9.341629 37.87431 9.500304 38.49579 9.791214 39.28917 10.187903 40.241228 9.354855 41.39163 7.596188 42.38335 7.582965 42.396577 7.51685 42.436246 7.47719 42.502359 7.47719 42.581697 7.47719 42.661035 7.530069 42.72715 7.596185 42.753595 8.085435 42.965166 8.601131 43.150285 9.116828 43.282518 9.92343 43.494085 10.756482 43.61309 11.615977 43.626314 11.734983 43.626314 11.840765 43.626314 11.959771 43.626314 12.977942 43.626314 13.837446 43.480859 14.432481 43.34863 13.665547 43.771768 12.52836 44.08912 12.025887 44.208124 11.959771 44.221349 11.920102 44.261018 11.893657 44.313909 11.86721 44.3668 11.86721 44.432916 11.893657 44.485807 11.933326 44.565145 12.898607 46.416364 16.640715 47.72544 18.148135 48.25436 19.35143 48.30725 20.197703 47.89734 20.329934 47.831224 20.448937 47.765108 20.541499 47.68577 19.906794 51.335317 22.74974 53.265874 22.776186 53.279096 22.855525 53.33199 22.948092 53.318765 23.014208 53.265874 23.080323 53.19976 23.093535 53.107198 23.053868 53.02786 22.577839 52.089029 22.472058 51.295648 22.710072 50.64772 22.895194 50.17169 23.238986 49.89401 23.463776 49.748559 24.733183 55.117094 30.207507 54.918748 30.260397 54.918748 30.352959 54.918748 30.432299 54.85263 30.445523 54.76007 30.458746 54.66751 30.41907 54.57495 30.339732 54.535284 28.87198 53.83446 28.171172 52.51216 27.853819 51.62622 30.128174 54.284044 32.415754 55.011308 33.98929 55.130315 35.85373 55.27577 37.136358 54.654287 37.189249 54.627843 37.268587 54.588174 37.308259 54.508836 37.295038 54.429498 37.281816 54.35016 37.22891 54.284044 37.14957 54.2576 35.139677 53.755125 34.002515 52.723733 33.394258 51.890684 34.055406 52.36671 35.08679 52.90885 36.739664 53.318765 40.48177 54.2576 42.62389 52.260927 42.703229 52.168367 42.75612 52.11547 42.782587 52.036138 42.756139 51.97002 42.729696 51.903905 42.66355 51.851015 42.59744 51.83779 42.544546 51.824567 42.50489 51.811346 42.478445 51.758455 42.385885 51.613 42.385885 51.335317 42.412328 51.08408 43.78752 52.485719 45.374269 53.014638 46.49822 53.212984 47.6354 53.411329 48.455236 53.305543 48.494905 53.305543 48.57424 53.29232 48.62713 53.239427 48.653577 53.173315 48.68002 53.107198 48.66679 53.02786 48.6139 52.974969 48.600679 52.961744 46.947805 51.189867 47.42383 48.87584 47.450277 48.770059 47.463518 48.664274 47.47674 48.558488 49.698205 45.95356 54.53781 47.50065 54.57748 47.51387 54.65682 47.540319 54.749395 47.51387 54.789064 47.447759 54.82873 47.38164 54.841936 47.28908 54.802267 47.222967 53.62542 45.332078 51.681634 44.922166 50.729578 44.842828 53.638639 42.793268 59.655099 43.57342 59.721216 43.57342 59.774106 43.586645 59.840246 43.560197 59.879915 43.52053 59.919584 43.480859 59.94603 43.427968 59.94603 43.36185 59.85347 41.563524 59.271646 40.307344 58.213808 39.619749 57.27497 39.01149 56.124578 38.98504 55.212194 39.10405 55.714666 38.786699 56.190687 38.442899 56.627046 38.072656 56.666715 38.04621 56.69317 38.00654 56.732835 37.980096 57.949354 36.922258 58.914617 35.599954 59.62866 34.07931 60.884843 34.10576 61.77079 34.224767 61.78401 34.224767 61.836904 34.237989 61.90301 34.21154 61.94268 34.17187 61.98235 34.132204 62.008798 34.07931 62.008798 34.0132 61.8369 30.707449 60.065019 29.83473 58.504705 29.715723 58.62371 29.358704 58.7295 28.988459 58.808839 28.618216 62.193925 25.629818 62.64352 21.70259 62.656744 21.53069 62.669969 21.43813 62.603837 21.345574 62.511278 21.319127 62.418718 21.292679 62.32615 21.332348 62.27326 21.424908 61.45343 23.143898 59.53609 23.633147 58.465028 23.778599H58.438589L58.451826 23.765379C58.465047 23.752156 58.478265 23.738932 58.478265 23.725708 58.491487 23.712485 58.504716 23.712489 58.51794 23.699265 62.603845 19.3489 60.580717 14.006809 60.554269 13.953918 60.5146 13.861355 60.422044 13.808464 60.329484 13.834911 60.236925 13.861359 60.170816 13.940697 60.170816 14.046478 60.276597 15.844807 59.324525 17.325783 58.72949 18.066269 59.655099 12.737404 56.230363 9.735783 56.203916 9.709335 56.137804 9.656445 56.045248 9.643223 55.97913 9.682892 55.913019 9.722561 55.87332 9.801895 55.886545 9.894459 56.283235 12.195259 54.90805 13.689457 54.141119 14.324158 55.09317 10.952297 53.62543 9.94735 53.55932 9.907681 53.493204 9.868011 53.427087 9.868011 53.360975 9.894459 53.294858 9.920902 53.255186 9.987019 53.255186 10.066357 53.255186 10.079582 53.175859 11.547333 52.342809 12.023361 51.919675 12.274597 51.33784 12.221703 50.637025 11.877907 49.499849 11.32254 48.587469 11.322544 47.89987 11.520889 48.151109 11.375435 48.336236 11.296097 48.336236 11.296097 48.415575 11.26965 48.45523 11.190311 48.45523 11.110973 48.45523 11.031635 48.402338 10.952297 48.322999 10.925854 45.32138 9.841568 43.57595 10.806847 42.94125 11.309319 42.94125 10.277924 44.170976 8.810173 44.713117 8.281254 44.766008 8.228363 44.77925 8.162247 44.76603 8.096134 44.752805 8.030018 44.699895 7.977127 44.647005 7.95068 42.385869 7.064739 41.156145 7.897789 40.69334 8.34737 40.30987 7.289532 40.57433 5.345749 40.57433 5.332523 40.58755 5.266411 40.561109 5.200294 40.508214 5.160625 40.40243 5.068066 39.913187 4.81683 37.77106 5.385418 36.09174 5.835003 35.020669 6.53582 34.571088 6.879616 34.0025 5.332527 32.23063 5.068069 32.15129 5.054844 32.0984 5.041622 32.032278 5.068066 31.992606 5.107735 31.952937 5.147404 31.92649 5.200294 31.92649 5.266411 31.952937 5.768883 31.43725 6.826725 31.027338 7.553989 30.590979 4.499477 26.637307 3.322632 26.452185 3.269741 26.359625 3.243294 26.267054 3.282963 26.227385 3.362301 26.187716 3.441639 26.200945 3.547424 26.26706 3.600315 26.26706 3.600315 26.690194 4.036671 27.007544 5.742439 27.16622 6.575485 27.020768 6.972179 26.875315 7.157299 26.822422 7.223415 26.756302 7.276306 26.70341 7.302753 26.597626 6.363922 26.240605 5.623432 25.645569 5.12096 24.640624 4.261467 23.318324 4.380474 23.26543 4.393696 23.17287 4.406918 23.106766 4.47303 23.093543 4.552368 23.080319 4.631706 23.119978 4.72427 23.199316 4.763939 23.820797 5.081291 24.138159 5.715992 24.283612 6.218468 23.781138 5.583763 22.961312 4.658154 22.035702 4.023449 20.686957 3.097839 20.991084 .730923 20.991084 .704475 21.004305 .638363 20.96464 .559025 20.911749 .519356 20.858857 .479687 20.779519 .466465 20.713404 .506134 18.941523 1.378849 18.253924 3.031723 18.01591 4.420139 18.10847 2.317684 19.034087 .373905 19.04731 .347458 19.086979 .281345 19.073746 .188782 19.020853 .135891 18.994408 .149113 18.941515 .122665 18.888625 .122665ZM12.462252 9.193642C12.501921 9.193642 12.541593 9.18042 12.581262 9.153973 12.634153 9.114304 12.67382 9.048187 12.660597 8.968849 12.660597 8.955624 12.501918 7.434982 13.202736 5.636658 14.088676 3.362301 15.873791 1.68298 18.518389 .66481 18.082032 1.735874 17.103518 4.592041 17.976235 6.906063 18.015904 6.998627 18.10847 7.051517 18.214255 7.02507 18.306817 6.998623 18.372927 6.906063 18.359705 6.800278 18.359705 6.760609 17.777895 2.754044 20.594393 1.074722 20.554724 1.841656 20.634066 3.573872 21.837359 4.393696 23.252219 5.358974 24.468733 7.144077 24.481957 7.157299 24.534848 7.236637 24.627404 7.263084 24.706742 7.236637 24.786078 7.210194 24.852194 7.130856 24.83897 7.038292 24.83897 6.972179 24.772866 5.663101 23.939818 4.843277 24.362954 4.896168 24.931538 5.05484 25.407567 5.464756 25.96293 5.940781 26.28028 6.668049 26.346396 7.60688 26.359618 7.712666 26.438956 7.792004 26.544739 7.792004 26.67697 7.792004 26.981102 7.725887 27.192669 7.461426 27.496798 7.104408 27.562907 6.509373 27.417454 5.715996 27.245556 4.803608 27.047213 4.235016 26.875315 3.877995 28.038939 4.340801 30.749659 5.689545 30.683543 8.254807 30.683543 8.347366 30.736432 8.426704 30.828993 8.453152 30.90833 8.479599 31.000885 8.439926 31.053778 8.373814 31.172783 8.175468 32.190954 6.522594 32.32318 5.544094 32.81243 5.663101 33.98928 6.086235 34.28019 7.315975 34.29341 7.382088 34.346303 7.434982 34.41242 7.461426 34.478536 7.487873 34.557874 7.46143 34.610765 7.421761 34.623987 7.408535 35.747934 6.390366 37.876836 5.821777 39.331365 5.438309 39.939634 5.451534 40.1512 5.491203 40.085088 6.059792 39.9264 7.937454 40.45532 8.863064 40.494989 8.915958 40.54789 8.955624 40.614007 8.968849 40.68012 8.968849 40.746225 8.942406 40.785894 8.876289 40.82556 8.810173 41.790849 7.46143 44.197435 8.281254 43.642068 8.889511 42.20075 10.621723 42.59744 11.825016 42.623884 11.891129 42.67679 11.944023 42.742906 11.957245 42.809019 11.970467 42.888345 11.944023 42.928014 11.891129 42.994127 11.825016 44.48835 10.211811 47.71476 11.203537 47.238737 11.467995 46.498235 11.983688 46.246999 12.7374 46.22055 12.82996 46.260229 12.935749 46.352788 12.975418 46.445348 13.015087 46.551118 12.98864 46.60401 12.909302 46.73624 12.710957 47.966 11.071304 50.425477 12.287819 51.258523 12.697731 51.959337 12.7374 52.501478 12.420048 53.202299 12.010136 53.453535 11.110973 53.55932 10.568829 53.903116 11.058083 54.405576 12.287823 53.54608 14.826637 53.519636 14.905975 53.54608 14.998531 53.612199 15.051422 53.67831 15.104317 53.770868 15.104317 53.836984 15.064648 53.863428 15.051426 56.468385 13.438221 56.309709 10.476273 57.19565 11.48122 59.231969 14.363831 58.17413 18.767086 58.147684 18.846425 58.187349 18.938984 58.26668 18.978653 58.34602 19.018322 58.438589 19.018322 58.504705 18.952206 58.58404 18.886094 60.104688 17.458008 60.48815 15.329109 60.81873 17.048096 61.00387 20.36707 58.425386 23.276127 58.385717 23.236458 58.319597 23.223237 58.25348 23.23646 58.16092 23.262906 58.09481 23.342244 58.09481 23.434805 58.09481 23.487696 58.09479 23.55381 58.108015 23.6067 57.989007 23.725708 57.883226 23.831494 57.77744 23.937277 57.72455 23.990168 57.698114 24.08273 57.72456 24.148846 57.751008 24.228184 57.83033 24.26785 57.909669 24.26785 57.909669 24.26785 58.068346 24.267849 58.30636 24.241404 58.346029 24.241404 58.398935 24.228184 58.451826 24.228184L58.504705 24.21496C59.45676 24.095953 61.0303 23.712485 62.048475 22.522416 61.704675 23.884383 60.779066 26.409977 58.478265 28.406649 58.451818 28.433094 58.425374 28.472764 58.412149 28.512433 58.319589 28.975237 58.174139 29.43804 58.01546 29.8744 57.989015 29.940514 58.00223 30.00663 58.0419 30.059523 58.08157 30.112416 58.134454 30.152083 58.20057 30.152083 60.250135 30.191752 61.37409 31.434714 61.572435 33.854524 61.202188 33.81485 60.43528 33.748739 59.483224 33.72229 59.403886 33.72229 59.337755 33.76196 59.298086 33.841299 58.61049 35.36194 57.64523 36.67102 56.441938 37.72886 56.402269 37.755304 56.375818 37.794977 56.336149 37.82142 55.51632 38.52224 54.59069 39.10405 53.57252 39.566854 53.47996 39.60652 53.427095 39.725534 53.466764 39.818094 53.50643 39.923875 53.625429 39.963544 53.717988 39.923875 53.98245 39.81809 56.33615 38.958597 57.96258 40.003213 58.861745 40.585023 59.377424 41.656087 59.49643 43.189958 58.240247 43.044504 52.594034 42.568475 50.01555 44.908945 49.949434 44.961835 49.936227 45.054399 49.96267 45.133737 49.989118 45.213075 50.068456 45.26596 50.161016 45.25274 50.2668 45.25274 52.62048 45.16018 54.114679 47.02462 52.72626 46.680826 49.037034 46.032899 47.093255 48.38659 47.06681 48.413038 47.05361 48.452707 47.05361 48.49237 47.04039 48.61138 47.013925 48.73039 47.0007 48.83617 46.60401 50.779954 47.556074 52.340265 48.0321 52.974969 47.701528 52.98819 47.1726 52.974969 46.537897 52.85596 45.40072 52.657617 43.761064 52.10225 42.399095 50.568384 42.3462 50.51549 42.280088 50.489046 42.20075 50.502267 42.134634 50.51549 42.06854 50.568384 42.055315 50.64772 42.02887 50.74028 41.817289 51.586557 42.108194 52.00969 42.13464 52.036138 42.14786 52.075807 42.17431 52.10225 41.552827 52.565057 39.661947 53.68901 36.792558 52.974969 33.39425 52.11547 32.733106 50.753507 32.666994 50.60805 32.627325 50.51549 32.521535 50.4626 32.415754 50.502267 32.32319 50.54194 32.257078 50.64772 32.28352 50.74028 32.309968 50.859289 33.05044 53.42455 36.422307 54.522058 35.893388 54.680734 35.020677 54.865858 33.94961 54.78652 31.609145 54.601396 29.387686 53.186536 27.523245 50.700616 27.470353 50.6345 27.377785 50.60805 27.285225 50.6345 27.205887 50.674169 27.15299 50.753507 27.166214 50.846067 27.179438 50.95185 27.53646 53.212984 29.400904 54.522058 28.871983 54.469167 28.131493 54.32371 27.36456 54.006364 25.420779 53.199758 24.204274 51.66589 23.754693 49.457654 23.741469 49.40476 23.701794 49.351869 23.648901 49.32542 23.596009 49.298978 23.543128 49.298978 23.477013 49.3122 23.437344 49.32542 22.643963 49.642774 22.286944 50.541936 22.075375 51.0973 22.08859 51.745229 22.313382 52.485719 21.480334 51.652669 20.171257 49.80145 21.057198 46.945283 21.083643 46.8395 21.030753 46.733716 20.924969 46.694047 20.819184 46.654378 20.713397 46.720495 20.673727 46.813055 20.647282 46.89239 20.475386 47.31553 19.933243 47.579988 19.192757 47.937009 18.068804 47.87089 16.68039 47.38164 13.850669 46.389917 12.66059 45.06762 12.290346 44.55192 12.991165 44.3668 14.829174 43.83788 15.344871 43.044504 15.397762 42.978387 15.384526 42.885828 15.331635 42.806489 15.278743 42.72715 15.186188 42.713926 15.106851 42.74037 15.053958 42.766817 13.731662 43.242845 11.853997 43.242845 11.748214 43.242845 11.642429 43.242845 11.523422 43.242845 10.703597 43.2164 9.896985 43.110616 9.116828 42.91227 8.746584 42.81971 8.37635 42.700704 8.01933 42.555253 8.614365 42.185007 9.830876 41.35196 10.478803 40.399904 10.518472 40.347009 10.518469 40.267675 10.492023 40.214784 10.068887 39.249505 9.777985 38.442899 9.619308 37.808198 9.18295 36.06276 7.358174 35.414834 6.392895 35.190046 6.763139 35.03137 7.292056 34.859468 7.860644 34.846246 7.953205 34.846246 8.032546 34.78013 8.04577 34.68757 8.058992 34.59501 8.019333 34.50245 7.939995 34.46278 6.935048 33.973527 5.58629 33.114038 4.819356 31.752066 3.867301 30.033078 3.748306 29.028128 3.774752 28.565325 4.409456 29.186804 6.062327 30.681002 7.424295 31.038023 7.503633 31.064469 7.596191 31.024802 7.63586 30.958687 7.688752 30.89257 7.675532 30.800009 7.62264 30.733895 7.516856 30.601664 5.149934 27.573599 3.285492 26.899226 2.280545 26.542206 1.43427 26.63477 .852458 26.806668 1.579723 26.132296 2.915252 25.32569 4.528457 26.357083 4.621017 26.409976 4.726801 26.396754 4.792917 26.317416 4.859032 26.238078 4.845815 26.11907 4.766477 26.052956 4.700362 25.986842 3.35161 24.74388 1.883858 24.281075 3.510286 24.043063 6.379673 23.884387 7.51685 25.550484 7.582965 25.643044 7.701972 25.669489 7.78131 25.603375 7.87387 25.550482 7.900317 25.431475 7.847425 25.338915 7.543296 24.823218 6.776362 23.421582 6.459011 21.636479 6.459011 21.623253 6.445791 21.59681 6.445791 21.583584 5.877202 20.36707 5.811093 19.62658 5.824317 19.243115 5.877208 19.296006 5.943324 19.362122 6.022661 19.428235 6.075553 19.481129 6.154888 19.494355 6.234226 19.467907 6.300341 19.44146 6.353233 19.375344 6.366456 19.296006 6.392902 18.978653 6.445791 18.674523 6.511906 18.357174 6.525128 18.277836 6.498686 18.198498 6.432571 18.145607 6.035881 17.867924 5.678851 17.669579 5.348276 17.550572 4.343328 17.19355 3.49707 17.28611 2.915258 17.458012 3.642523 16.783638 4.978035 15.977036 6.59124 17.00843 6.683801 17.061322 6.789585 17.048096 6.8557 16.968758 6.921815 16.88942 6.908598 16.770413 6.829261 16.7043 6.763146 16.638184 5.414394 15.395222 3.946642 14.932419 5.030927 14.773743 6.697031 14.641514 8.01933 15.091095 8.098668 15.117542 8.178002 15.091095 8.230895 15.038204 8.310232 14.945644 8.389577 14.866306 8.468914 14.773743 8.482138 14.89275 8.495351 15.0382 8.508574 15.196877 8.521797 15.262993 8.548249 15.315884 8.614364 15.342331 9.024277 15.567123 9.354855 15.858028 9.592869 16.201825 9.658984 16.294388 9.77799 16.320832 9.857328 16.254719 9.949889 16.201825 9.976336 16.082818 9.923444 15.990257 9.301963 14.958862 6.511909 9.722561 10.531698 5.438313 10.611036 6.390366 10.941605 8.545712 12.277126 9.167191 12.409356 9.180416 12.435805 9.193642 12.462252 9.193642ZM8.997834 15.038204C8.984612 14.932419 8.971378 14.826633 8.971378 14.734074 9.02427 14.866302 9.090392 14.998531 9.143284 15.117538 9.090392 15.104317 9.050726 15.064651 8.997834 15.038204Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,39.648805,78.28906)" d="M2.895699 15.405031C3.332057 17.12402 3.927086 19.028128 5.500622 19.847954 6.4659 20.350428 7.708875 20.297535 8.621262 19.715723 9.546871 19.133912 10.128676 18.049632 10.10223 16.952122 10.922056 17.639719 11.874111 18.274418 12.945173 18.340533 13.950121 18.406649 14.968279 17.851283 15.444306 16.965344 15.933557 16.079403 15.854232 14.929002 15.259197 14.122398 16.753396 13.897608 17.652546 12.257955 17.49387 10.763756 17.348418 9.256335 16.383139 7.960484 15.312077 6.889421 11.741868 3.345659 4.575022 .158918 .89903 3.980362-1.758791 6.770414 2.260995 12.852993 2.895699 15.405031Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,39.46158,76.4082)" d="M5.502738 .168076C5.476292 .168076 5.436629 .168076 5.410183 .168076 3.58541 .194521 2.091199 .789555 .967245 1.9664-1.121988 4.134971 .636683 8.128315 1.90609 11.050596 2.355672 12.068768 2.725916 12.941482 2.897815 13.576185 3.347396 15.348066 3.955642 17.291851 5.595293 18.138123 6.626686 18.667042 7.935775 18.61415 8.914276 17.992669 9.786994 17.450526 10.36878 16.485247 10.474564 15.467075 11.188605 16.022442 12.087769 16.59103 13.119163 16.657145 14.203447 16.723259 15.28774 16.128228 15.803436 15.176172 16.279465 14.316678 16.253012 13.219171 15.763761 12.359676 17.25796 11.949764 18.02489 10.270439 17.879439 8.842356 17.747208 7.520057 17.019959 6.224202 15.644767 4.84901 13.370412 2.601102 9.231622 .168076 5.502738 .168076ZM3.281269 13.483631C3.10937 12.809258 2.72589 11.923313 2.276308 10.891919 1.04657 8.075422-.645967 4.214314 1.258144 2.244087 2.302761 1.159801 3.704398 .591208 5.423387 .577984 5.449833 .577984 5.489496 .577984 5.515942 .577984 9.125818 .577984 13.158857 2.944903 15.393542 5.166366 16.70262 6.462219 17.390213 7.691956 17.50922 8.908471 17.64145 10.257216 16.834824 11.843973 15.433186 12.055541 15.367071 12.068764 15.300962 12.108437 15.274516 12.174551 15.248071 12.240665 15.261287 12.306782 15.300956 12.372896 15.843099 13.113384 15.90922 14.197672 15.472861 15.004275 15.023279 15.810878 14.071224 16.339795 13.158837 16.27368 12.074553 16.207564 11.135739 15.519967 10.43492 14.924932 10.368806 14.87204 10.289454 14.858816 10.22334 14.898485 10.157225 14.938153 10.104326 15.004272 10.117549 15.08361 10.143995 16.115004 9.601852 17.119952 8.729136 17.662095 7.856418 18.204236 6.706024 18.257128 5.793637 17.7811 4.286216 17.014166 3.704404 15.162951 3.281269 13.483631Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,41.98166,74.75781)" d="M10.453741 8.050126C11.02233 6.47659 10.268606 4.691484 9.118206 3.461746 7.346325 1.557635 4.595949 .605583 2.017466 1.002273 1.157971 1.134502 .179457 1.55764 .020781 2.417134-.085003 2.998946 .232368 3.567532 .536497 4.083228 1.475329 5.617095 2.400932 7.150961 3.339765 8.684828 3.89513 9.597215 4.516618 10.562499 5.495119 10.972412 7.386007 11.752569 9.832261 9.755892 10.453741 8.050126Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,53.780397,69.296878)" d="M2.159027 .911345C2.013574 .633662 1.802 .382428 1.524317 .236975 1.246634 .091522 .889607 .078296 .625147 .250195 .334241 .435317 .215254 .792339 .149139 1.136137 .069801 1.519604 .016896 1.903072 .003673 2.286539-.00955 2.656783 .003673 3.053474 .202018 3.370826 .440032 3.767516 .942505 3.979082 1.392087 3.873298 2.661494 3.582392 2.648278 1.810508 2.159027 .911345Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,50.80844,64.48047)" d="M2.69806 3.138162C3.094749 2.966263 3.438573 2.64891 3.584026 2.25222 3.769148 1.762969 3.623669 1.194381 3.293094 .797691 2.962519 .401001 2.433613 .202651 1.917916 .189428 1.349327 .176205 .75428 .400996 .384036 .850578 .013792 1.286937-.118438 1.934865 .119576 2.463785 .542712 3.41584 1.851788 3.521629 2.69806 3.138162Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,44.99771,62.91211)" d="M3.338597 2.61473C3.603056 1.913912 3.259265 1.041193 2.584893 .670949 1.923743 .313928 1.024573 .459381 .495653 1.001524 .231193 1.265984 .046078 1.609784 .006409 1.980028-.03326 2.389941 .112173 2.826301 .416302 3.117207 1.183236 3.897363 2.928684 3.67257 3.338597 2.61473Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,41.981599,74.76758)" d="M.02075 2.425625C.179426 1.566131 1.15794 1.142993 2.017435 1.010763 4.595918 .614073 7.346294 1.552902 9.118175 3.470236 10.255352 4.699975 11.009076 6.48508 10.453711 8.058617 9.83223 9.777605 7.399179 11.774282 5.508292 10.980903 4.516567 10.57099 3.908335 9.605705 3.35297 8.693318 2.414137 7.159451 1.488502 5.625586 .549669 4.091719 .232318 3.576022-.085034 3.007437 .02075 2.425625ZM1.4224 4.475189C2.268672 5.863603 3.101733 7.23879 3.948004 8.627203 4.450478 9.447029 5.005837 10.319752 5.891778 10.689995 7.597544 11.404037 9.792554 9.605706 10.347919 8.058617 10.863616 6.643756 10.176008 5.030553 9.144614 3.919821 7.544632 2.200832 5.071946 1.341336 2.744699 1.71158 1.964543 1.830587 1.091812 2.214059 .946359 2.980992 .853798 3.509912 1.144717 4.012384 1.4224 4.475189Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,53.767946,69.30078)" d="M.00293 2.288993C.016153 1.905526 .069058 1.508835 .148396 1.138591 .214511 .808016 .33353 .437771 .624436 .252649 .888896 .08075 1.245891 .093976 1.523574 .239429 1.801257 .384882 2.012831 .636116 2.158284 .913799 2.356629 1.284042 2.488878 1.799742 2.475655 2.288993 2.475655 1.87908 2.383094 1.455944 2.211195 1.165039 2.078965 .940248 1.907054 .715458 1.66904 .596452 1.431026 .477445 1.140127 .464218 .915336 .609671 .677322 .768347 .571538 1.072481 .518646 1.350164 .452531 1.667516 .412843 1.998088 .39962 2.31544 .386397 2.632792 .399645 2.963362 .558321 3.227822 .756666 3.558397 1.179802 3.74352 1.550046 3.650959 2.052519 3.531952 2.31696 3.148484 2.422744 2.698902 2.31696 3.254268 1.999621 3.730302 1.37814 3.862532 .928559 3.968316 .426085 3.743519 .188071 3.360052 .016172 3.055923-.010293 2.659236 .00293 2.288993Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,50.80844,64.48828)" d="M.384036 .849629C.75428 .41327 1.349327 .175253 1.917916 .201699 2.433613 .214922 2.962519 .426495 3.293094 .809962 3.636892 1.206651 3.769148 1.77524 3.584026 2.264491 3.438573 2.674404 3.081527 2.978534 2.69806 3.150433 1.851788 3.5339 .542712 3.428114 .119576 2.462836-.118438 1.933916 .013792 1.285988 .384036 .849629ZM.635292 2.6744C1.018759 3.388442 2.089815 3.38844 2.777411 3.044642 3.094762 2.885967 3.372452 2.62151 3.478235 2.304158 3.610466 1.920691 3.478242 1.484334 3.187336 1.206651 2.89643 .928969 2.460046 .796736 2.03691 .823182 1.560882 .849627 1.08488 1.061195 .807198 1.418216 .516292 1.775237 .436947 2.27771 .635292 2.6744Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,44.998109,62.916017)" d="M.006019 1.988542C.045688 1.618298 .230803 1.261277 .495263 .996817 1.037406 .467898 1.923353 .309222 2.584502 .666243 3.245652 1.023263 3.602666 1.895982 3.338206 2.610024 2.941517 3.681087 1.182846 3.892653 .415912 3.12572 .125006 2.834814-.03365 2.398455 .006019 1.988542ZM.680372 3.12572C1.328299 3.78687 2.822503 3.601745 3.166301 2.689359 3.391092 2.094324 3.10018 1.353837 2.531591 1.036485 1.963002 .732356 1.209305 .864591 .759723 1.314172 .534932 1.538963 .376243 1.829867 .349797 2.147219 .310128 2.50424 .442358 2.874483 .680372 3.12572Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,45.95044,60.916017)" d="M1.076638 1.2841C1.407212 1.231208 1.711341 1.059312 1.936132 .794852 2.068362 .649399 2.160923 .43783 2.068362 .252708 1.975801 .067586 1.737787 .027917 1.539442 .04114 1.08986 .054363 .600623 .173369 .230379 .424606-.113419 .662619-.060514 1.046089 .30973 1.204765 .547744 1.310549 .812178 1.336992 1.076638 1.2841Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,47.444398,69.12109)" d="M2.108327 4.407239C2.716585 4.142779 3.25874 3.71964 3.708322 3.23039 4.078566 2.8337 4.395918 2.384115 4.501702 1.855195 4.607486 1.326275 4.46202 .718023 4.025661 .413894 3.853762 .281664 3.628958 .215547 3.417391 .255216 3.166154 .308108 2.981051 .493227 2.809152 .665126 2.068665 1.418836 1.328171 2.172547 .587683 2.913034 .217439 3.283278-.298264 4.010543 .217433 4.499794 .627346 4.922929 1.645522 4.605584 2.108327 4.407239Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,51.73108,63.208986)" d="M1.986899 1.208942C2.132352 1.103158 2.264588 .970928 2.291034 .812253 2.343926 .521347 2.092696 .256885 1.80179 .19077 1.510884 .124655 1.219959 .230436 .955499 .33622 .704262 .442004-.446145 1.116378 .188559 1.420507 .743925 1.684967 1.524094 1.55274 1.986899 1.208942Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,54.823305,68.61328)" d="M1.063245 1.746946C1.12936 1.284141 1.023589 .794894 .745906 .411427 .666568 .305643 .57402 .186635 .428567 .186635 .322783 .186635 .216999 .265971 .164107 .358532 .111215 .451093 .084744 .55688 .071521 .675887 .005406 1.138692-.021028 1.614716 .018641 2.077521 .031864 2.328758 .164107 2.989912 .560797 2.751898 .864926 2.579999 1.023576 2.051075 1.063245 1.746946Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,35.985963,48.539064)" d="M0 .089657C.819826 .565684 1.586756 .671467 2.327243 .539237 1.586756 .697913 .819826 .59213 0 .089657Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,18.562516,34.24414)" d="M5.126041 2.961168C5.43017 1.33474 4.14754 .54136 3.010363 .382684 1.516165 .171116 .207092 1.043831 .02197 2.511583-.149929 3.952889 .696336 4.997508 2.230203 5.182629 3.711178 5.380974 4.940919 4.442143 5.126041 2.961168Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,34.28018,50.035158)" d="M.38347 .000642C.595253 .000642 .76694 .172325 .76694 .384108 .76694 .595891 .595253 .767578 .38347 .767578 .171687 .767578 0 .595891 0 .384108 0 .172325 .171687 .000642 .38347 .000642Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,38.849336,18.011719)" d="M.072087 5.347014C.072087 5.347014 4.872036 1.829698 5.149719 .943758 5.149719 .943758 12.73973 2.10738 11.509992 8.018058 10.280253 13.928736 3.774531 13.796506 1.632407 11.575043-.509718 9.35358 .072087 5.347014 .072087 5.347014Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,38.644624,17.646485)" d="M5.209005 .353374 5.156126 .512049C4.971004 1.107083 1.995825 3.474 .157829 4.809522L.091714 4.862415 .078494 4.941752C.052048 5.113651-.490085 9.067326 1.691709 11.328458 2.947893 12.637534 5.49993 13.15323 7.642055 12.544972 9.87674 11.897046 11.397392 10.178058 11.913089 7.678912 12.23044 6.118599 11.992427 4.743407 11.185824 3.566561 9.400721 1.001301 5.552823 .37982 5.394146 .366597L5.209005 .353374ZM.461964 5.087206C1.162783 4.571509 4.825558 1.834349 5.460261 .789733 6.174303 .921963 9.347828 1.649228 10.855249 3.804575 11.595737 4.875638 11.820508 6.158268 11.529603 7.599574 11.040352 9.940044 9.625498 11.553249 7.536265 12.161507 5.539593 12.743319 3.146232 12.26729 1.982608 11.050775 .09172 9.106995 .395849 5.669017 .461964 5.087206Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,40.20755,16.917969)" d="M.049415 3.644544C.049415 3.644544 3.315507 1.251182 3.500629 .642924 3.500629 .642924 8.657589 1.436306 7.824541 5.456095 6.991492 9.475884 2.561783 9.383323 1.107254 7.875902-.347275 6.368481 .049415 3.644544 .049415 3.644544Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,8.140381,23.751954)" d="M7.746667 1.644673C7.746667 1.644673 11.356541 6.523957 12.255704 6.80164 12.255704 6.80164 11.10531 14.576759 5.088849 13.373467-.940836 12.170175-.835062 5.505787 1.42607 3.297547 3.673978 1.076084 7.746667 1.644673 7.746667 1.644673Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,7.939194,22.558594)" d="M6.757769 .187624C5.342908 .187624 3.028881 .452083 1.481791 1.959504 .146269 3.26858-.369424 5.886732 .278502 8.068525 .952875 10.329658 2.711533 11.863524 5.237124 12.365998 6.823884 12.683351 8.22552 12.418891 9.402367 11.585842 12.007297 9.747847 12.628777 5.794171 12.642 5.622272L12.668439 5.450373 12.509769 5.397482C11.901512 5.21236 9.481699 2.184296 8.106507 .319854L8.053612 .253738 7.974277 .240515C7.921385 .253737 7.445364 .187624 6.757769 .187624ZM6.506528 12.101539C6.123062 12.101539 5.739592 12.061871 5.329679 11.982533 2.94954 11.506505 1.296673 10.078421 .675192 7.962742 .066934 5.913178 .529729 3.453702 1.772691 2.250409 3.75614 .306629 7.273456 .584313 7.855268 .650429 8.384187 1.36447 11.18747 5.093353 12.24531 5.728057 12.11308 6.455321 11.385818 9.734623 9.190802 11.281713 8.397423 11.823856 7.498253 12.101539 6.506528 12.101539Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,11.246063,21.84961)" d="M5.275703 1.104268C5.275703 1.104268 7.735189 4.423239 8.343447 4.608361 8.343447 4.608361 7.563287 9.897559 3.464159 9.077733-.634968 8.257908-.568857 3.722421 .965011 2.228222 2.498878 .734024 5.275703 1.104268 5.275703 1.104268Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,14.131454,50.140626)" d="M36.809759 21.924342C37.89404 16.740929 36.18827 9.878195 22.013224 6.241871-.201404 .542763-1.28569 15.788876 .65809 25.653229 2.588647 35.51758 13.497612 37.63326 13.497612 37.63326 19.619858 38.810106 23.78511 38.228296 26.680947 36.879549 27.461104 36.509305 28.161911 36.099393 28.77017 35.63659 28.783392 35.623365 28.796635 35.61014 28.82308 35.596918 29.444561 35.107667 30.15859 34.446519 30.925524 33.560579L30.938759 33.547357C32.67097 31.550683 34.680868 28.40361 36.4263 23.484656 36.63787 22.902844 36.756864 22.387146 36.809759 21.924342Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,13.929626,46.26953)" d="M13.567225 .930058C9.441651 .930058 6.215251 2.014343 3.940896 4.169691-1.004503 8.837408-.237576 17.234009 .661588 21.822388 1.679758 26.992577 5.157415 30.020642 7.894575 31.64707 10.856524 33.405729 13.62013 33.947874 13.646576 33.961095 19.107672 35.00571 23.577044 34.754476 26.935683 33.19416L26.948903 33.18094C27.702614 32.83714 28.41664 32.414007 29.064566 31.924754 29.07779 31.91153 29.104254 31.898308 29.117478 31.885085 29.844742 31.316496 30.558764 30.628899 31.246359 29.822297L31.259596 29.809075C33.414945 27.323153 35.279394 23.911622 36.786817 19.693486 36.998384 19.124897 37.11738 18.582756 37.17027 18.093506L37.183507 18.053833C38.254568 12.830751 36.50913 5.849012 22.24152 2.186241 19.028334 1.353195 16.132485 .930058 13.567225 .930058ZM19.755593 34.185884C17.917595 34.185884 15.907704 33.974317 13.72591 33.564405 13.699465 33.564405 10.988754 33.022264 8.092919 31.29005 5.421875 29.70329 2.036779 26.767788 1.058277 21.74305 .172337 17.234009-.594594 8.996084 4.218576 4.447374 7.81523 1.06229 13.84492 .414364 22.175406 2.556488 28.495997 4.182917 32.872816 6.5895 35.173616 9.736572 36.892606 12.090265 37.461175 14.840645 36.839696 17.90838L36.786817 17.948052 36.82649 18.053833C36.773599 18.503417 36.654584 18.992669 36.456239 19.53481 34.96204 23.713276 33.12404 27.071915 30.995135 29.531392L30.9819 29.544615C30.307528 30.324772 29.606715 30.999144 28.905896 31.55451 28.892674 31.567732 28.879445 31.580956 28.86622 31.59418 28.231518 32.056985 27.543934 32.466897 26.829894 32.81069L26.803454 32.823919C24.833228 33.736307 22.479529 34.185884 19.755593 34.185884Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,25.491944,42.44922)" d="M13.046126 15.517587C15.13536 15.213458 17.132037 14.234957 18.639458 12.753982 19.697299 11.709366 20.543562 10.387066 20.715462 8.90609 20.993145 6.618512 19.59152 4.370602 17.687409 3.061526 15.783298 1.75245 13.456042 1.236754 11.181688 .906179 9.608151 .681389 8.021401 .535935 6.447866 .694611 4.87433 .866509 3.300784 1.368983 2.057823 2.347485 1.211551 3.008635 .510736 3.907799 .193384 4.939193-.322313 6.605289 .259505 8.456507 1.158669 9.871367 3.5917 13.692812 8.550309 16.15229 13.046126 15.517587Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,30.181092,41.753908)" d="M12.535403 3.130478C14.214724 4.466001 14.598177 6.780023 13.976697 8.789917 13.077533 11.394847 10.670952 13.867547 8.01313 13.100614 5.394978 12.399796 2.221463 11.130386 0 12.743591 2.459477 14.383242 5.513994 15.229514 8.370161 14.832825 10.459394 14.528695 12.456072 13.550193 13.963493 12.069218 15.021333 11.024601 15.867598 9.702305 16.039496 8.221331 16.317179 5.933752 14.915523 3.685842 13.011412 2.376766 11.252753 1.173473 9.137085 .63133 7.021406 .300755 9.018078 .829674 10.922198 1.76851 12.535403 3.130478Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,31.184113,30.06836)" d="M4.669724 3.634785C5.423435 3.7009 6.111031 3.581894 5.925909 2.788515 5.436658 .633167 1.787115 .051353 .359032 1.505883-1.135166 3.026527 2.474708 3.224874 3.14908 3.357104 3.426763 3.409996 4.061467 3.581893 4.669724 3.634785Z" fill="#b70000"/>
<path transform="matrix(1,0,0,-1,31.212494,30.011719)" d="M0 1.92963C.899163 1.162696 2.036328 .673447 3.292512 .964353 4.456136 1.308151 5.381755 2.273428 5.699107 3.423828 5.884228 3.291598 5.976789 3.080031 5.897451 2.736233 5.408201 .580885 1.758658-.000928 .330575 1.453601 .145453 1.6255 .039669 1.784177 0 1.92963Z" fill="#b70000" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,31.344819,28.832032)" d="M2.565248 1.092216C3.424742 1.184777 4.244574 1.674026 4.654487 2.401291 4.866055 2.414514 5.077622 2.401293 5.249522 2.361624 4.601595 .959987 3.133836 .00793 1.441293 .431066 .859482 .550073 .383467 .867425 0 1.29056 .079338 1.383121 .185109 1.449237 .317339 1.528575 .965266 1.118662 1.771868 .986432 2.565248 1.092216Z" fill="#b70000" fill-opacity=".1"/>
<path transform="matrix(1,0,0,-1,31.178986,29.33789)" d="M.152498 1.798077C.535965 1.374942 1.011996 1.05759 1.593808 .938583 3.286351 .52867 4.767333 1.467503 5.402036 2.869141 5.521043 2.842695 5.62683 2.803025 5.719391 2.750133 5.40204 1.599732 4.47642 .634455 3.312797 .290657 2.056612-.000249 .919432 .489 .020268 1.255934-.032624 1.467502 .020268 1.652624 .152498 1.798077Z" fill="#b70000" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,32.62195,27.582032)" d="M.296317 .065547C.216979 .07877 .124425 .105214 .071533 .158106 .005418 .210998-.021031 .316783 .018638 .382898 .058307 .449013 .137644 .462236 .216982 .475459 .600449 .541574 .983913 .594465 1.354157 .66058 1.7244 .726695 2.1211 .83248 2.504566 .845703 2.795472 .845703 2.451665 .501904 2.345881 .43579 2.068198 .263891 1.737627 .144883 1.407052 .091991 1.050031-.00057 .666561-.000568 .296317 .065547Z" fill="#b70000" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,43.839509,30.65039)" d="M2.248858 5.302696C3.71661 5.50104 5.025673 4.601877 5.210795 3.253132 5.435586 1.692819 4.364531 .753986 2.936447 .46308 1.495141 .172174 .159606 1.190343 .014153 2.552311-.1313 3.887833 .860444 5.117574 2.248858 5.302696Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,19.17862,35.501955)" d="M2.248842 5.302696C3.716594 5.50104 5.025658 4.601877 5.21078 3.253132 5.435571 1.692819 4.364514 .753986 2.936431 .46308 1.495125 .172174 .159606 1.190347 .014153 2.552315-.1313 3.887837 .860428 5.104351 2.248842 5.302696Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,37.797365,20.1875)" d="M.555547 1.571409C1.163805 1.584632 1.943948 1.016043 1.930725 .60613 1.904279 .20944 1.639832 .063988 1.282812 .050765 .740669 .024319-.013051 .592907 .000172 1.029265 .000172 1.439178 .251418 1.597855 .555547 1.571409Z" fill="#b20623"/>
<path transform="matrix(1,0,0,-1,21.307129,23.375)" d="M.596377 .129653C.34514 .11643-.025104 .076761 .001342 .499897 .054234 1.17427 .556704 1.557736 1.138516 1.782527 1.442645 1.901534 1.786449 1.782527 1.839341 1.359392 1.905456 .936256 1.191411 .222214 .596377 .129653Z" fill="#b20623"/>
<path transform="matrix(1,0,0,-1,29.25293,17.847657)" d="M1.364517 .975826C1.430632 .658474 1.364511 .354345 1.060382 .248561 .650469 .103108 .597584 .473351 .505023 .737811 .346347 1.17417 .214114 1.61053 .055438 2.046889-.0239 2.284903-.050349 2.522915 .200888 2.655145 .47857 2.813821 .62403 2.602255 .769483 2.417133 .941382 2.179119 1.298402 1.332846 1.364517 .975826Z" fill="#dd1f37"/>
<path transform="matrix(1,0,0,-1,31.733063,17.509766)" d="M1.01331 2.119975C1.066202 1.498494 .854631 .929906 .695955 .348094 .643063 .14975 .52405 .043966 .325705 .083635 .140583 .123304 .021589 .268757 .008366 .453879-.031303 1.088583 .074474 1.710063 .23315 2.331543 .272819 2.516665 .431502 2.728234 .669516 2.675342 .933975 2.609227 .986864 2.371212 1.01331 2.119975Z" fill="#dd1f37"/>
<path transform="matrix(1,0,0,-1,18.30397,13.837891)" d="M17.060502 8.536985C15.962993 5.231237 17.126616 3.525473 17.126616 3.525473L10.647349 2.877544 4.458999 .880874C4.458999 .880874 4.842469 2.903992 2.528445 5.508922 .214421 8.113852-.116157 8.060959 .029296 8.563433 .161526 9.065907 1.563164 11.617944 8.492012 13.072473L8.571346 13.085695C15.500195 14.513779 17.814225 12.741898 18.131577 12.331985 18.46215 11.935295 18.158009 11.842734 17.060502 8.536985Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,18.097687,14.189453)" d="M4.678506 1.033495C4.625614 1.033495 4.585945 1.046717 4.546276 1.086386 4.493384 1.139277 4.466938 1.205394 4.480161 1.271509 4.480161 1.284732 4.81073 3.241735 2.589267 5.727657 1.742995 6.66649 1.16119 7.261524 .777723 7.658214 .063682 8.385479-.081768 8.530931 .037239 8.967291 .076908 9.086297 .949619 11.995355 8.658624 13.621783L8.737958 13.635006C16.460186 15.221765 18.417195 12.907742 18.496533 12.815181 18.774216 12.471383 18.708107 12.273037 18.337862 11.320982 18.139519 10.805285 17.848603 10.025129 17.451912 8.821836 16.407298 5.661541 17.491585 3.995444 17.504809 3.982222 17.544478 3.92933 17.544474 3.849992 17.518029 3.783876 17.491581 3.717762 17.425473 3.678092 17.359358 3.664869L10.906532 3.016944 4.731401 1.033495C4.718178 1.046718 4.691729 1.033495 4.678506 1.033495ZM13.564351 13.793682C12.308167 13.793682 10.747867 13.648229 8.817309 13.251539L8.737958 13.238316C1.266968 11.66478 .433932 8.901175 .420709 8.874729 .367817 8.676384 .367807 8.66316 1.055403 7.949119 1.43887 7.552429 2.020688 6.957395 2.880182 6.00534 4.731401 3.92933 4.916517 2.21034 4.890071 1.522745L10.800758 3.426855C10.813981 3.426855 10.827194 3.440079 10.840418 3.440079L17.015564 4.061559C16.724658 4.68304 16.195725 6.322692 17.068444 8.967291 17.465132 10.170582 17.769272 10.963962 17.954394 11.479658 18.311413 12.418491 18.298188 12.431714 18.165959 12.590389 18.17918 12.577167 17.147782 13.793682 13.564351 13.793682Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,23.080292,6.345703)" d="M0 1.202495 .02644 1.057042C.039663 1.017373 .079325 .990927 .118994 .990927L.423129 1.057042 .634694 .025648C.634694 .012425 .661146-.000797 .674369-.000797L.899154 .052094 .674369 1.109933 .965269 1.176048C1.018161 1.189271 1.044607 1.228941 1.031384 1.281833L1.004944 1.414062 0 1.202495Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,24.3497,6.1816408)" d="M1.190069 .484163C1.110731 .576724 1.044613 .629616 1.004944 .656062 .965275 .682508 .91239 .708954 .846275 .7354 .952059 .775069 1.03139 .827961 1.071059 .907299 1.110728 .986637 1.123958 1.065975 1.110735 1.158536 1.097512 1.237874 1.057833 1.303988 1.018164 1.343657 .965272 1.396549 .91239 1.422996 .846275 1.436219 .78016 1.449442 .674369 1.436218 .542139 1.409772L0 1.290766 .26446 .008135 .436349 .047805C.489241 .061028 .515687 .100696 .502464 .153588L.40991 .60317 .462805 .616393C.515697 .629616 .568589 .629617 .595035 .629617 .621481 .629617 .64792 .616393 .687589 .589947 .700812 .589947 .700822 .576725 .714045 .563502 .806606 .484164 .885937 .391602 .938829 .285818L.978505 .232927C1.004951 .193258 1.044607 .166812 1.097499 .180035L1.428074 .24615 1.190069 .484163ZM.766924 .920522C.740478 .907299 .661146 .880853 .542139 .854407L.357014 .814738 .290899 1.145313 .489244 1.184982C.595028 1.211428 .661146 1.211428 .674369 1.224651 .714038 1.224651 .753714 1.211428 .78016 1.198205 .806606 1.171759 .833039 1.145313 .833039 1.105644 .846262 1.065975 .833039 1.026306 .833039 .99986 .819816 .960191 .79337 .933745 .766924 .920522Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,25.723725,5.8378908)" d="M.953246 1.277811C.821016 1.370372 .662343 1.396818 .477221 1.357149 .371437 1.330703 .292096 1.304257 .225981 1.251365 .173089 1.211696 .13342 1.172027 .093751 1.105912 .054082 1.039797 .027639 .986905 .014416 .907567-.01203 .815006 .00119 .709222 .027636 .576992 .067305 .378647 .159866 .233194 .292096 .140633 .424326 .048072 .582999 .021626 .76812 .061295 .953242 .100964 1.085472 .180302 1.16481 .325755 1.244148 .457985 1.270594 .629884 1.230925 .828229 1.178033 1.039797 1.098698 1.18525 .953246 1.277811ZM.940026 .444762C.887134 .365424 .821009 .312533 .715225 .286087 .609441 .259641 .530103 .286086 .450765 .338978 .371427 .39187 .318542 .497654 .292096 .643107 .26565 .78856 .265644 .894344 .318536 .986905 .371427 1.066243 .437552 1.119135 .543336 1.145581 .64912 1.172027 .741681 1.145581 .807796 1.092689 .887134 1.039797 .940019 .934013 .966465 .78856 .992911 .643107 .979695 .5241 .940026 .444762Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,27.033936,5.591797)" d="M.79338 1.441248 .965285 .581754 .304135 1.282572C.277689 1.309018 .224794 1.322241 .185125 1.322241L0 1.282572 .26446-.000058 .50248 .052834 .330575 .885882 .965285 .21151C.991731 .171841 1.044626 .158618 1.084295 .171841L1.26942 .21151 1.00496 1.494141 .79338 1.441248Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,28.390717,5.234375)" d="M.758917 .740216C.692802 .726993 .653139 .660878 .666362 .607986L.679582 .515425 .970498 .58154 .996937 .422864C.970491 .383195 .930819 .356749 .877927 .31708 .825035 .290634 .785369 .264188 .732477 .250965 .626693 .224519 .534136 .250965 .454798 .303857 .37546 .369972 .322561 .462534 .282892 .607986 .256446 .740216 .269679 .859223 .309348 .938561 .36224 1.017899 .441568 1.070791 .547352 1.097237 .62669 1.11046 .679585 1.123683 .732477 1.097237 .732477 1.097237 .758923 1.084014 .798592 1.044345 .838261 1.004676 .877927 .991453 .944042 1.004676L1.089492 1.044345C1.076269 1.136906 1.0366 1.20302 .957262 1.255912 .825032 1.348473 .666369 1.348473 .507693 1.308804 .388686 1.282358 .296115 1.242689 .216777 1.189797 .124217 1.110459 .058098 1.017899 .031652 .912115-.008017 .806331-.008013 .674101 .018433 .555094 .044879 .436087 .084544 .330303 .163882 .237742 .229997 .145181 .322558 .079066 .428342 .05262 .534126 .012951 .653143 .012951 .785373 .039397 .891157 .065843 .983711 .105512 1.076272 .158404 1.168833 .211296 1.234948 .277412 1.274617 .343527L1.168843 .832777 .758917 .740216Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,24.667054,7.7597658)" d="M1.176833 .766585C1.123941 .806254 1.05783 .8327 .965269 .845923 1.018161 .885592 1.057836 .92526 1.071059 .991375 1.097505 1.044267 1.097502 1.110383 1.084279 1.163275 1.071056 1.216166 1.04461 1.269058 1.018164 1.308727 .978495 1.348396 .938823 1.374842 .899154 1.401288 .859485 1.414511 .806596 1.427734 .753704 1.427734 .700812 1.427734 .621468 1.414511 .515684 1.388065L0 1.282282 .26446-.000349 .714029 .092212C.885928 .131881 .991715 .158327 1.031384 .17155 1.097499 .197996 1.1504 .224442 1.190069 .264111 1.229738 .30378 1.256181 .356672 1.269404 .422787 1.282627 .488902 1.282627 .541793 1.269404 .607908 1.269404 .6608 1.229725 .726916 1.176833 .766585ZM.304119 1.110382 .449569 1.136828C.568576 1.163274 .647923 1.176497 .674369 1.176497 .714038 1.176497 .753698 1.176498 .780144 1.150052 .80659 1.123606 .819816 1.097159 .833039 1.05749 .846262 1.017821 .833042 .978153 .819819 .951707 .793373 .925261 .766934 .898815 .727265 .885592 .700819 .872369 .6347 .859146 .542139 .845923L.370234 .806253 .304119 1.110382ZM.991724 .422787C.978501 .396341 .93882 .369895 .912374 .356672 .885928 .343449 .833049 .330226 .727265 .317003L.489244 .264111 .423129 .607908 .634694 .647578C.753701 .674024 .833045 .687246 .872714 .674023 .912383 .674023 .938823 .660801 .965269 .634355 .991715 .607909 1.004941 .581462 1.018164 .541793 1.018164 .502124 1.004947 .462456 .991724 .422787Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,26.05429,7.451172)" d="M.979685 1.277811C.847455 1.370372 .688783 1.396818 .503661 1.357149 .397877 1.330703 .318539 1.304258 .239201 1.251366 .186309 1.211697 .13342 1.172028 .093751 1.105913 .054082 1.039798 .027639 .973683 .014416 .907568-.01203 .815007 .00119 .709223 .027636 .576993 .067305 .378648 .159866 .233195 .292096 .140634 .424326 .048073 .582995 .021627 .78134 .061296 .966462 .100965 1.098705 .193526 1.191266 .325756 1.283827 .457986 1.29705 .629884 1.257381 .828229 1.204489 1.039797 1.111915 1.18525 .979685 1.277811ZM.953246 .444763C.900354 .365425 .821022 .29931 .728461 .286087 .622677 .259641 .530103 .286087 .450765 .338979 .371427 .391871 .318545 .497655 .278876 .643108 .25243 .788561 .265647 .894345 .305316 .986906 .358208 1.066244 .437552 1.119135 .543336 1.145581 .64912 1.172027 .741678 1.145581 .821016 1.092689 .900354 1.039797 .953239 .934014 .979685 .788561 1.006131 .656331 1.006138 .537324 .953246 .444763Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,27.377747,7.205078)" d="M1.123954 1.149603C1.084285 1.228941 1.018164 1.281833 .952049 1.334725 .885934 1.374394 .806603 1.40084 .727265 1.414062 .66115 1.414062 .581808 1.414063 .476025 1.387617L0 1.281833 .26446-.000797 .766924 .104986C.859485 .131432 .938832 .144655 .991724 .184324 1.071062 .223993 1.123948 .276886 1.150394 .329777 1.203286 .395893 1.229725 .488453 1.242948 .581014 1.256171 .660352 1.242952 .752913 1.229729 .858697 1.216506 .977704 1.176846 1.083488 1.123954 1.149603ZM.978489 .567791C.978489 .501676 .952052 .462007 .938829 .435561 .912383 .409115 .885928 .382669 .846259 .356223 .819813 .343 .766927 .329777 .687589 .316554L.489244 .276885 .317339 1.123157 .436349 1.149603C.542133 1.176049 .621481 1.189271 .66115 1.176048 .714042 1.176048 .753711 1.162826 .79338 1.13638 .833049 1.109934 .859485 1.083488 .899154 1.030596 .9256 .977704 .952043 .911589 .978489 .805805 .978489 .713244 .991711 .633906 .978489 .567791Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,28.56781,6.861328)" d="M.925609 1.388514C.88594 1.375291 .846262 1.348845 .833039 1.309176L.79338 1.124054 .714029 .819925 .40991 1.216615C.383464 1.256284 .343788 1.269507 .304119 1.256284L0 1.190169 .634694 .542242 .727265 .066215C.740488 .026546 .76693 .0001 .806599 .013323L1.004944 .052992 .899154 .595135 1.229729 1.441406 .925609 1.388514Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,21.987305,6.640625)" d="M1.013631 .758405C.973962 .811297 .921076 .837743 .854962 .850965 .788847 .864188 .696292 .877411 .564062 .877411 .431832 .877411 .352491 .890634 .312822 .91708 .286376 .930303 .27315 .956749 .259927 .983195 .246704 1.009641 .25992 1.04931 .286366 1.062533 .326035 1.102202 .378937 1.128648 .445052 1.141871 .511167 1.155094 .577272 1.155094 .616941 1.128648 .643387 1.115425 .656613 1.102202 .669836 1.075756 .696282 1.036087 .749174 1.009642 .802066 1.022865L.947516 1.062533C.92107 1.18154 .854968 1.260878 .762407 1.31377 .669846 1.366662 .550833 1.379885 .392157 1.340216 .299596 1.326993 .220251 1.287324 .154136 1.247655 .088022 1.207986 .048353 1.155095 .021907 1.08898-.004539 1.022865-.004536 .969973 .008687 .903858 .035133 .811297 .088031 .731958 .180592 .679066 .246707 .639397 .352481 .626174 .484711 .612951 .590495 .612951 .669846 .599728 .696292 .599728 .735961 .599728 .77562 .586506 .802066 .56006 .828512 .546837 .841742 .520391 .841742 .493945 .854965 .454276 .841735 .401384 .802066 .361715 .762397 .322046 .709515 .2956 .630177 .269154 .550839 .255931 .484724 .25593 .431832 .282376 .405386 .295599 .378927 .322046 .352481 .361715 .326035 .414607 .259924 .427829 .207032 .414606L.074802 .374938C.114471 .242708 .193805 .150146 .286366 .084031 .378927 .031139 .51116 .017917 .669836 .044363 .77562 .070809 .868181 .097255 .934296 .150146 1.000411 .189815 1.04008 .255931 1.066526 .322046 1.092972 .388161 1.092969 .467499 1.079746 .533614 1.079746 .65262 1.0533 .705513 1.013631 .758405Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,30.020356,6.1484377)" d="M2.977199 2.908266C2.950753 2.987604 2.871412 3.040495 2.792074 3.027272H2.778854L.134255 2.471907H.121019C.041681 2.445461-.011198 2.366122 .002025 2.286784V2.273562L.160695 1.493405C.345817 1.731419 .61027 1.903318 .914399 1.969432 .927622 1.969432 .927635 1.969432 .927635 1.969432 .967304 1.982656 1.00696 1.982656 1.046629 1.982656 1.033406 1.982656 1.020193 1.995879 1.00697 1.995879 .993747 1.995879 .993737 2.009102 .980514 2.009102 .967291 2.009102 .967297 2.022325 .954074 2.022325 .940851 2.022325 .940858 2.035547 .927635 2.035547 .914412 2.035547 .901182 2.048771 .887959 2.048771 .874736 2.048771 .874743 2.048771 .86152 2.061994H.848284C.835061 2.061994 .821848 2.075217 .808625 2.075217 .768956 2.08844 .7425 2.101662 .716054 2.101662 .716054 2.101662 .72929 2.101663 .72929 2.114886 .72929 2.114886 .74251 2.114886 .74251 2.128109H.75573C.768953 2.141332 .782182 2.141332 .795405 2.154555 .808628 2.154555 .808622 2.167777 .821845 2.167777 .821845 2.167777 .835061 2.167778 .848284 2.181001H.86152C.86152 2.181001 .87474 2.181001 .87474 2.194224 .887963 2.194224 .887956 2.207447 .901179 2.207447 .914402 2.207447 .914412 2.22067 .927635 2.22067 .954081 2.233892 .980524 2.233893 1.00697 2.247116 1.073085 2.273562 1.139199 2.286784 1.205314 2.300007 1.324321 2.326453 1.430105 2.339677 1.535889 2.326454 1.588781 2.326454 1.654886 2.326454 1.707778 2.313231 1.747447 2.313231 1.78712 2.300007 1.826789 2.286784 1.853235 2.286784 1.853241 2.273562 1.866464 2.247116 1.866464 2.233893 1.866461 2.22067 1.879684 2.207447V2.194224C1.892907 2.128109 1.919353 2.061994 1.945799 1.995879 1.959022 1.969433 1.972248 1.95621 1.998694 1.942987 2.104478 1.890095 2.210265 1.850426 2.316049 1.797534 2.355718 1.784311 2.368941 1.771088 2.382164 1.757865 2.395387 1.744642 2.382164 1.718196 2.382164 1.678527V1.665304C2.474725 1.638858 2.55405 1.599189 2.633388 1.546297 2.646611 1.546297 2.659844 1.533074 2.659844 1.533074 2.673067 1.519851 2.699516 1.506628 2.712739 1.493405 2.739185 1.480182 2.752395 1.46696 2.765618 1.453737 2.778841 1.440514 2.79207 1.440513 2.805293 1.42729 2.844962 1.400844 2.871402 1.374398 2.897848 1.347952H2.884628C2.871405 1.347952 2.871412 1.347952 2.858189 1.347952H2.844969C2.818523 1.334729 2.792064 1.321506 2.765618 1.308283L2.752398 1.29506C2.739175 1.281837 2.725962 1.268615 2.712739 1.255392 2.712739 1.255392 2.712739 1.255391 2.712739 1.242168V1.228945H2.725959C2.792074 1.228945 2.844962 1.176053 2.871408 1.109938 2.884631 1.083492 2.884631 1.043823 2.871408 1.017377V1.004155C2.844962 .93804 2.778854 .898371 2.712739 .871925 2.699516 .871925 2.699506 .871925 2.686283 .858702 2.633391 .845479 2.580503 .845478 2.540833 .845478 2.527611 .845478 2.514381 .858702 2.501158 .858702 2.514381 .858702 2.527611 .858702 2.540833 .858702 2.580503 .871925 2.620175 .885147 2.659844 .911593 2.673067 .911593 2.67306 .924817 2.686283 .924817 2.699506 .93804 2.712739 .951262 2.712739 .964485 2.699516 .964485 2.68629 .964485 2.659844 .964485 2.659844 .964485 2.659847 .964485 2.646624 .964485 2.606955 .951262 2.567283 .951262 2.527614 .964485 2.448276 .977708 2.368941 1.017377 2.316049 1.109938 2.316049 1.109938 2.302816 1.123162 2.289593 1.123162 2.276371 1.123162 2.263154 1.123162 2.263154 1.123162 2.249931 1.123162 2.249921 1.123162 2.236698 1.123162 2.210252 1.109939 2.18381 1.109938 2.157364 1.096715 2.078026 1.070269 1.985474 1.017377 1.919359 .977708 1.892913 .964485 1.879687 .951263 1.866464 .93804 1.853241 .924817 1.840008 .924816 1.840008 .911593 1.694556 1.004154 1.575561 1.109938 1.456555 1.228945 1.456555 1.202499 1.469774 1.189276 1.469774 1.16283V1.149607C1.469774 1.136384 1.482994 1.109938 1.482994 1.096715 1.482994 1.083492 1.482991 1.083493 1.496214 1.07027 1.496214 1.057047 1.509434 1.030601 1.509434 1.004155V.990932C1.509434 .977709 1.522669 .964485 1.522669 .951262 1.522669 .93804 1.535889 .924816 1.535889 .911593 1.535889 .89837 1.549109 .885148 1.549109 .871925V.858702C1.549109 .845479 1.562329 .832256 1.562329 .819033 1.562329 .805809 1.575549 .792586 1.575549 .779363 1.588772 .76614 1.588781 .752918 1.602004 .739695 1.62845 .700026 1.641673 .660357 1.668119 .63391 1.681342 .607465 1.707791 .581019 1.721014 .554573 1.959028 .250444 2.316049 .052099 2.712739 .01243L3.400328 .157883C3.492889 .171106 3.559004 .27689 3.532558 .369451L3.003638 2.921489C2.977192 2.895042 2.977199 2.908266 2.977199 2.908266ZM2.593729 .422343C2.42183 .448789 2.210259 .554572 2.078029 .647133 2.197036 .607464 2.408604 .594242 2.540833 .620688 2.633394 .633911 2.699516 .647134 2.778854 .686803L2.792074 .700025C2.725959 .581018 2.659844 .501681 2.593729 .422343Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,30.260407,5.2734377)" d="M.50248 .541307C.489257 .541307 .489248 .528084 .476025 .528084H.462805C.449582 .528084 .436368 .514861 .423145 .514861 .409922 .514861 .396693 .501638 .38347 .501638 .370247 .501638 .357018 .488415 .343795 .488415 .330572 .488415 .317358 .475192 .304135 .475192 .290912 .461969 .277683 .461969 .26446 .448746 .251237 .448746 .23802 .435523 .23802 .435523 .171905 .395854 .11901 .356185 .052895 .303293 .039672 .29007 .026456 .29007 .026456 .276847 .013233 .263624 0 .263624 0 .250401L.052895-.000836C.079341 .052056 .105781 .118171 .14545 .171063 .158673 .184286 .171902 .210732 .185125 .223955 .304132 .382631 .449591 .514861 .62149 .607422H.608254C.555362 .55453 .528926 .541307 .50248 .541307Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,31.781006,4.7597658)" d="M.343795 .250897V.277344C.343795 .277344 .330578 .277344 .317355 .277344 .290909 .277344 .251234 .26412 .211565 .250897 .14545 .237674 .066112 .211229 .01322 .198006H0C.013223 .198006 .013233 .198005 .026456 .184782 .171909 .145113 .304139 .092222 .423145 .012884 .423145 .012884 .436365 .012884 .436365-.000339V.012884C.436365 .026107 .423133 .052553 .40991 .065776 .396687 .092222 .370241 .118668 .343795 .145114 .330572 .158337 .317355 .158336 .317355 .171559L.343795 .250897Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,31.288269,6.3847658)" d="M.043177 .63456C.096069 .409769 .17541 .198201 .294417-.000144L.572097 .052748C.294414 .303985 .096079 .647783 .003518 1.03125-.009705 .89902 .016731 .76679 .043177 .63456Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,30.511628,6.5839846)" d="M.423129 1.427734C.423129 1.414511 .423129 1.414511 .423129 1.401288 .423129 1.374842 .40991 1.348396 .40991 1.32195 .40991 1.295504 .39669 1.282281 .39669 1.255835 .39669 1.242612 .39669 1.229389 .39669 1.216166 .39669 1.202943 .396693 1.176497 .38347 1.163275 .38347 1.150052 .38347 1.150051 .38347 1.136828 .38347 1.123605 .38347 1.123605 .38347 1.110382 .38347 1.097159 .38347 1.083936 .38347 1.070713 .38347 1.044267 .38347 1.004598 .38347 .978152 .38347 .8327 .396683 .687246 .423129 .52857 .462798 .369895 .5157 .211219 .581815 .065766L.25124-.000349C.238017-.000349 .224788-.000349 .211565-.000349 .132227-.000349 .052898 .052543 .039675 .145103L0 .343448C.026446 .317002 .039669 .290557 .066115 .264111 .105784 .237665 .145456 .211219 .185125 .184773 .171902 .211219 .171892 .250887 .158669 .277333 .158669 .290556 .14545 .317003 .14545 .330226 .066112 .753361 .185115 1.150052 .423129 1.427734Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,19.969727,17.117188)" d="M18.21128 4.424346C17.920374 4.265671 17.57657 4.226001 17.245995 4.318562 10.343594 6.34168 1.960225 2.030983 1.867664 1.991314 1.246183 1.66074 .47925 1.911977 .148675 2.533458-.1819 3.154939 .056111 3.921872 .690815 4.252447 1.074281 4.450792 10.065914 9.078839 17.960041 6.778039 18.634413 6.579694 19.01788 5.878875 18.819535 5.204502 18.71375 4.847481 18.488965 4.583022 18.21128 4.424346Z" fill="#bc0625"/>
<path transform="matrix(1,0,0,-1,19.769288,17.148438)" d="M1.473073 1.678245C.944153 1.678245 .428453 1.969151 .163993 2.471624-.20625 3.185666 .071426 4.071607 .785468 4.455073 1.168934 4.653418 10.239918 9.321135 18.20016 6.993887 18.980317 6.769096 19.429898 5.949271 19.191884 5.169114 19.0861 4.79887 18.834856 4.48152 18.491059 4.296398 18.14726 4.111276 17.75057 4.058383 17.367104 4.17739 10.530817 6.174061 2.22678 1.903035 2.147442 1.863366 1.935874 1.731136 1.711087 1.678245 1.473073 1.678245ZM13.386986 7.231902C7.079618 7.231902 1.274718 4.243505 .983812 4.084829 .454893 3.807146 .256561 3.172443 .534244 2.643524 .811926 2.114604 1.45984 1.916258 1.975537 2.193941 2.054875 2.23361 10.504371 6.583974 17.499335 4.547634 17.777018 4.468296 18.067933 4.494741 18.31917 4.640194 18.570406 4.772424 18.755515 5.010438 18.834853 5.274899 19.006752 5.843487 18.676178 6.438522 18.107588 6.610421 16.547276 7.04678 14.947299 7.231902 13.386986 7.231902Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,26.540009,27.623047)" d="M2.636164 1.348362C2.39815 2.128518 1.551879 3.278918 .5866 2.961566-.405124 2.644215 .09735 1.150017 .388256 .488867 .480817 .264076 .824611 .462421 .73205 .687212 .546928 1.123571 .044461 2.604545 .903956 2.617768 1.578328 2.630991 2.107238 1.811166 2.265914 1.242577 2.332029 1.004564 2.715502 1.097125 2.636164 1.348362Z" fill="#300b00"/>
<path transform="matrix(1,0,0,-1,37.651674,24.75)" d="M2.433491 .370491C2.446714 1.190316 1.997133 2.539062 .978962 2.539062-.065654 2.539062-.039218 .965526 .026897 .251484 .053343 .000247 .436813 .079586 .410367 .330823 .370698 .806851 .344262 2.367164 1.164087 2.115927 1.812014 1.917582 2.050021 .97875 2.050021 .396938 2.036798 .145701 2.433491 .119254 2.433491 .370491Z" fill="#300b00"/>
<path transform="matrix(1,0,0,-1,31.067017,35.39258)" d="M1.626435 1.572297C1.573543 1.373952 1.520657 1.188831 1.467765 .990486 1.414873 .805365 1.375191 .633465 1.322299 .448343 1.269407 .249998 1.15041 .011983 .952065 .025206 .819835 .038429 .727265 .170658 .66115 .289665 .515697 .540902 .383467 .792142 .26446 1.056601 .171899 1.254946 .079338 1.453291 0 1.651636 .198345 1.704528 .396699 1.730975 .62149 1.730975 .952065 1.744198 1.29586 1.664858 1.626435 1.572297Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,38.4852,33.73047)" d="M1.216509 .490174C1.203286 .29183 1.123954 .027369 .925609 .014146 .79338 .000923 .674373 .119931 .595035 .225715 .52892 .305053 .476021 .384392 .423129 .450507 .277676 .635629 .13223 .833973 0 1.032318 .317352 1.283555 .634704 1.561239 .991724 1.78603 1.110731 1.865368 1.242948 1.931483 1.375178 1.984375 1.348732 1.733138 1.32229 1.4819 1.295844 1.230663 1.269398 .979426 1.242955 .728188 1.216509 .490174Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,29.689087,34.351564)" d="M11.3746 4.195457C10.885349 3.309516 10.343204 2.423574 9.536601 1.78887 8.716776 1.154166 7.658929 .823593 6.653982 .559134 6.60109 .545911 6.548207 .532685 6.482092 .519462 6.376308 .493016 6.270518 .466569 6.177957 .440124 5.834159 .360785 5.477139 .294671 5.120118 .241778 5.014334 .228556 4.90855 .215334 4.789543 .202111 4.723428 .202111 4.670533 .188887 4.604418 .188887 4.339958 .162441 4.075501 .149219 3.797819 .135996 2.449073 .10955 1.126781 .400455 .068941 1.299618-.116181 1.458294 .108601 1.775649 .306945 1.616973 1.351562 .731032 2.687084 .479795 4.009383 .54591 4.353181 .559133 4.696989 .598799 5.027564 .638468 5.133348 .651691 5.252354 .67814 5.358138 .691363 5.635821 .744255 5.913497 .797146 6.177957 .863261 6.32341 .90293 6.48209 .929374 6.627542 .969043 7.526706 1.193834 8.465542 1.511187 9.192806 2.040106 9.999409 2.621918 10.528322 3.494635 11.00435 4.35413 11.13658 4.565698 11.50683 4.420247 11.3746 4.195457Z" fill="#300b00"/>
<path transform="matrix(1,0,0,-1,28.62616,60.929689)" d="M27.551342 14.929619C27.14143 13.527982 26.34805 12.298244 25.501778 11.174291 25.382773 11.028837 25.276976 10.870162 25.157969 10.72471 25.144745 10.711486 25.131516 10.698261 25.118292 10.685038 25.012509 10.539585 24.893502 10.407358 24.787718 10.275128 24.774495 10.261906 24.774502 10.248681 24.761279 10.248681 24.655495 10.129674 24.562928 10.010665 24.457142 9.891659 24.417473 9.85199 24.377818 9.799097 24.338149 9.759428 24.25881 9.653645 24.16625 9.561085 24.073689 9.455301 24.047244 9.415632 24.00758 9.389188 23.981135 9.349519 23.862128 9.217289 23.743096 9.085058 23.624089 8.952829 23.359629 8.675146 23.095168 8.397463 22.830708 8.11978 22.7117 8.000772 22.59272 7.868544 22.460492 7.749537 22.447268 7.736315 22.434039 7.723089 22.420815 7.709866 22.262139 7.551189 22.090233 7.392517 21.918335 7.233841 21.838997 7.167727 21.772889 7.088387 21.69355 7.022272 21.667105 6.995827 21.653882 6.982603 21.627436 6.956158 21.548098 6.890043 21.481989 6.823927 21.40265 6.757812 21.21753 6.585915 21.032382 6.427235 20.84726 6.255337 20.767922 6.189222 20.701813 6.123106 20.622475 6.056992 19.829096 5.369396 19.022507 4.70825 18.189457 4.060324 18.03078 3.941317 17.872113 3.809084 17.700213 3.690077 17.488646 3.531401 17.263834 3.359505 17.052268 3.200829 16.89359 3.081821 16.734917 2.962814 16.589465 2.857029 16.034098 2.447117 15.478751 2.050426 14.923386 1.653736 14.791156 1.561176 14.658926 1.468615 14.526696 1.376053 14.262236 1.190931 13.997776 1.005812 13.733316 .82069 13.08539 .371107 12.252322 .490114 11.696956 1.08515 9.872183 3.081821 7.796176 4.774361 5.865619 6.652027 3.604487 8.847042 1.475589 11.399081 .497088 14.546153-.481414 17.693226-.058278 21.541117 2.149962 23.683243 2.202854 23.722912 2.242519 23.775803 2.295411 23.815472 2.33508 23.85514 2.361533 23.881585 2.401202 23.908032 2.440871 23.9477 2.480543 23.974148 2.520212 24.013817 2.652442 24.1196 2.784669 24.225384 2.930121 24.317944 2.96979 24.34439 3.022679 24.38406 3.075571 24.410508 3.168132 24.463398 3.26069 24.529513 3.353251 24.582404 3.432589 24.622073 3.525153 24.674965 3.604491 24.714634H3.617711C5.654051 25.693137 8.192876 25.441899 10.255663 24.30472 14.500243 21.951027 14.857265 16.64861 15.478745 16.926293 15.875435 17.09819 18.34812 19.584115 18.771256 19.91469 19.604306 20.562617 20.516686 21.091534 21.50841 21.395663 21.587748 21.422108 21.667092 21.448555 21.74643 21.461777 21.957997 21.51467 22.169585 21.55434 22.394375 21.580788 22.500159 21.59401 22.619166 21.607232 22.72495 21.607232 22.777842 21.607232 22.830729 21.607232 22.88362 21.620455 22.936512 21.620455 22.989398 21.620455 23.04229 21.620455 23.346419 21.620455 23.663769 21.594008 23.9679 21.541115 23.994345 21.541115 24.007569 21.527892 24.034014 21.527892 24.219136 21.488223 24.391047 21.448555 24.57617 21.395663 24.602616 21.382439 24.629042 21.382443 24.655488 21.369219 24.748049 21.342774 24.827387 21.316325 24.919947 21.276656 24.97284 21.25021 25.012522 21.236989 25.065413 21.223765 25.14475 21.184096 25.224097 21.157652 25.303435 21.117983 25.462109 21.038645 25.60755 20.959303 25.753003 20.866744 25.845564 20.81385 25.924902 20.747738 26.017462 20.694846 26.149693 20.602284 26.268706 20.509726 26.387713 20.390719 26.427382 20.35105 26.467038 20.3246 26.506707 20.284932 26.652159 20.139479 26.797606 19.980805 26.929837 19.795683 26.99595 19.703124 27.062087 19.623785 27.114977 19.531224 27.16787 19.438664 27.23399 19.3461 27.286883 19.240317 27.948033 17.957686 27.948033 16.331257 27.551342 14.929619Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,28.429566,60.9375)" d="M13.083597 .338848C12.607569 .338848 12.131541 .550417 11.761297 .947107 10.610897 2.203291 9.341494 3.353689 8.111755 4.477644 7.384491 5.138794 6.644003 5.813168 5.929962 6.500763 4.131635 8.246199 1.606043 10.983356 .508535 14.474227-.403852 17.422955-.23195 21.442746 2.214304 23.822884 2.267196 23.875777 2.306862 23.915446 2.359754 23.955115 2.399423 23.994784 2.439095 24.021227 2.478764 24.060896 2.518433 24.100565 2.558105 24.127015 2.597774 24.166684 2.730004 24.272467 2.87545 24.37825 3.020903 24.484034 3.073795 24.510479 3.113461 24.550147 3.166353 24.576592 3.258914 24.629485 3.351485 24.6956 3.457268 24.748494 3.549829 24.801386 3.629167 24.841055 3.721728 24.880724 5.731623 25.846 8.336549 25.687325 10.544788 24.47081 13.519962 22.817936 14.63068 19.763424 15.225715 18.11055 15.371168 17.700638 15.529857 17.251055 15.635641 17.118824 15.926547 17.31717 16.971152 18.295672 17.67197 18.970044 18.227335 19.498965 18.676938 19.9221 18.848835 20.05433 19.814114 20.794819 20.726494 21.28407 21.652104 21.574975 21.731442 21.601422 21.82399 21.627867 21.903328 21.64109 22.128119 21.693982 22.35291 21.73365 22.564476 21.77332 22.670262 21.786543 22.776059 21.799763 22.908288 21.799763 22.96118 21.799763 23.014065 21.799765 23.066958 21.812989 23.146296 21.812989 23.199187 21.812989 23.265303 21.812989 23.582653 21.812989 23.900019 21.786541 24.204148 21.733649 24.230593 21.733649 24.243803 21.720429 24.257027 21.720429 24.455372 21.68076 24.6405 21.64109 24.82562 21.5882L24.904973 21.56175C24.997534 21.535305 25.090075 21.495635 25.182637 21.469189L25.261986 21.442746C25.288434 21.429522 25.31486 21.416298 25.341305 21.416298 25.420643 21.376629 25.513224 21.350184 25.592562 21.310517 25.751238 21.231179 25.909893 21.15184 26.06857 21.046055 26.147908 20.993165 26.240483 20.940274 26.346266 20.860936 26.478495 20.768374 26.610713 20.662587 26.72972 20.54358 26.769389 20.50391 26.809077 20.464241 26.848746 20.424572 27.007422 20.27912 27.152882 20.107224 27.28511 19.9221 27.364449 19.816317 27.417342 19.723755 27.483456 19.631194 27.54957 19.538632 27.602437 19.43285 27.655329 19.327067 28.303256 18.084105 28.409066 16.457676 27.94626 14.857693 27.49668 13.310602 26.59751 12.001527 25.857022 11.036248 25.738015 10.877573 25.619015 10.732121 25.500008 10.586667 25.486784 10.573444 25.473555 10.560223 25.460331 10.533776 25.354548 10.401546 25.23554 10.256093 25.129757 10.123863 25.129757 10.11064 25.11654 10.097419 25.103318 10.084195 25.010756 9.965188 24.904967 9.84618 24.799183 9.727173L24.680155 9.594943C24.587595 9.489159 24.508257 9.396601 24.415696 9.290815 24.38925 9.251146 24.349587 9.224699 24.323142 9.18503 24.204134 9.052799 24.085135 8.92057 23.966128 8.78834 23.701668 8.497435 23.437209 8.219753 23.172747 7.955294 23.05374 7.836287 22.934727 7.704054 22.802498 7.585047L22.762822 7.54538C22.604146 7.386703 22.432272 7.228027 22.260375 7.069351L22.035557 6.857782C22.009112 6.831337 21.995888 6.818113 21.969443 6.791668L21.956238 6.778448C21.890124 6.712332 21.810772 6.646217 21.744658 6.580103 21.559536 6.408203 21.37442 6.249525 21.189299 6.077627L20.964514 5.879282C20.210804 5.218132 19.417424 4.570204 18.518261 3.882608 18.359585 3.763601 18.200915 3.631374 18.029017 3.512367 17.817448 3.353691 17.592638 3.181789 17.381072 3.023115 17.222395 2.904108 17.063736 2.785103 16.905062 2.679319 16.37614 2.295853 15.833985 1.899162 15.238951 1.476027 15.106721 1.383465 14.974491 1.290903 14.842261 1.198343 14.577802 1.013222 14.313329 .828098 14.035645 .642975 13.74474 .444632 13.414171 .338848 13.083597 .338848ZM6.604331 25.118737C5.652276 25.118737 4.726679 24.920392 3.906853 24.536925H3.893618C3.81428 24.497256 3.734951 24.457589 3.655613 24.41792 3.563052 24.365027 3.470495 24.312134 3.377934 24.259242 3.325042 24.232796 3.285373 24.206349 3.245704 24.179902 3.113474 24.087342 2.981244 23.994784 2.849014 23.888998 2.809345 23.862553 2.769673 23.822882 2.730004 23.796437 2.690335 23.76999 2.663882 23.730324 2.624213 23.703879 2.571321 23.66421 2.531656 23.62454 2.478764 23.571648 .495315 21.654314-.165828 17.978322 .865565 14.632904 1.923405 11.221372 4.409321 8.537104 6.181201 6.818115 6.895243 6.13052 7.635734 5.456148 8.349775 4.794998 9.592737 3.671043 10.862134 2.507418 12.025758 1.23801 12.515008 .709091 13.242292 .603308 13.797658 .999998 14.062118 1.18512 14.326577 1.370243 14.591037 1.555365 14.723268 1.647926 14.855497 1.740484 14.987727 1.833044 15.582762 2.25618 16.138108 2.65287 16.653805 3.023115 16.81248 3.14212 16.971158 3.247906 17.11661 3.366913 17.328179 3.525589 17.552989 3.684267 17.764557 3.856165 17.923233 3.975172 18.081889 4.107401 18.240565 4.226408 19.126505 4.914003 19.919892 5.56193 20.66038 6.209856L20.885163 6.408201C21.070286 6.566877 21.2554 6.738779 21.440524 6.910677 21.506638 6.976791 21.585989 7.042908 21.652104 7.109022L21.665339 7.122242C21.691786 7.148687 21.705008 7.161915 21.731455 7.175137L21.956238 7.386702C22.114914 7.545378 22.286807 7.704054 22.445483 7.86273L22.45872 7.875954 22.498362 7.915621C22.617369 8.034628 22.736382 8.153637 22.868613 8.285868 23.133072 8.550327 23.397532 8.828009 23.661992 9.118914 23.780999 9.251144 23.9 9.383375 24.019007 9.515604 24.045453 9.555273 24.085146 9.581721 24.111594 9.62139 24.204154 9.727175 24.283492 9.819734 24.376053 9.912294L24.495047 10.044524C24.587609 10.163531 24.693398 10.282539 24.799183 10.401546 24.812405 10.414769 24.812386 10.414767 24.812386 10.42799 24.931393 10.573442 25.037177 10.705674 25.14296 10.851128 25.156184 10.86435 25.169432 10.877572 25.169432 10.890795 25.288439 11.049471 25.407427 11.194927 25.51321 11.34038 26.227253 12.279212 27.099976 13.548618 27.536335 15.029593 27.972695 16.537014 27.880133 18.05766 27.271876 19.208062 27.218983 19.300621 27.166098 19.393179 27.113205 19.48574 27.060315 19.565079 27.007416 19.657639 26.9413 19.7502 26.822295 19.922099 26.67684 20.06755 26.544611 20.213006 26.504942 20.252675 26.478489 20.279122 26.43882 20.31879 26.333037 20.424576 26.214049 20.517134 26.095042 20.609694 26.002482 20.675808 25.9099 20.728703 25.843787 20.781596 25.698332 20.874157 25.552874 20.953494 25.407422 21.019608 25.328083 21.059277 25.24875 21.085726 25.182637 21.125393L25.037203 21.178286C24.957865 21.204731 24.87852 21.231179 24.799183 21.257624L24.719832 21.284068C24.547932 21.33696 24.376048 21.376629 24.204148 21.403075 24.190924 21.403075 24.177683 21.403075 24.151236 21.416298 23.847107 21.469189 23.556227 21.482415 23.252099 21.495636 23.199206 21.495636 23.146288 21.495636 23.093398 21.495636 23.040505 21.495636 22.98762 21.495636 22.934727 21.482413 22.81572 21.469189 22.723172 21.469193 22.617388 21.455969 22.40582 21.429524 22.207482 21.38985 21.995915 21.33696 21.916577 21.323737 21.837233 21.29729 21.757895 21.270844 20.885176 21.006384 20.012465 20.530358 19.086855 19.816315 18.92818 19.697308 18.452139 19.234503 17.949666 18.758477 16.786042 17.660967 16.00587 16.920483 15.754635 16.814698 15.675297 16.775029 15.582748 16.775029 15.50341 16.814698 15.27862 16.907259 15.146403 17.251057 14.855497 18.05766 14.273685 19.65764 13.202607 22.632815 10.359664 24.20635 9.169594 24.788163 7.860515 25.118737 6.604331 25.118737Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,50.18164,39.69336)" d="M.204178 .012159C.111617 .012159 .032279 .078271 .005833 .157609-.020613 .263393 .045482 .369179 .151266 .395625 .376057 .448517 .600861 .501409 .825652 .527855 .931436 .541078 1.037213 .461738 1.050436 .355954 1.063659 .25017 .984315 .144388 .878531 .131165 .666963 .104719 .468624 .065051 .257057 .012159 .230611 .012159 .217401 .012159 .204178 .012159Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,51.02054,39.351564)" d="M.330575 .027344C.224791 .014121 .105784 .014123 0 .0009 .119007 .0009 .224791 .014121 .330575 .027344Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,50.82074,39.5625)" d="M.199787 .000695C.107226 .000695 .014665 .080031 .001442 .172592-.011781 .278376 .067531 .384162 .173315 .397385 .279099 .410608 .398119 .423828 .517126 .423828 .62291 .423828 .715484 .344491 .728707 .238707 .74193 .132923 .649349 .040361 .543566 .027138 .424559 .013915 .332011 .013918 .226227 .000695 .226227 .000695 .21301 .000695 .199787 .000695Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,51.45685,52.82422)" d="M0 .000933C.26446 .278616 .52892 .556302 .79338 .833984 .542143 .556302 .277683 .278616 0 .000933Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,51.251924,53.039064)" d="M.204937 .017292C.152045 .017292 .099172 .030518 .059503 .070187-.019834 .149525-.019834 .268529 .059503 .347867 .323963 .612327 .588423 .890012 .852883 1.180918 .932221 1.260256 1.051208 1.260257 1.130546 1.194142 1.209884 1.114804 1.22312 .995796 1.143782 .916458 .879323 .625552 .614863 .34787 .350403 .070187 .310734 .030518 .257829 .017292 .204937 .017292Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,52.23706,39.36328)" d="M0 .013672C.039669 .013672 .092561 .000448 .13223 .000448 .092561 .013671 .039669 .013672 0 .013672Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,52.037263,39.57422)" d="M.332017 .012025C.318794 .012025 .3188 .012025 .305577 .012025 .279131 .012025 .265908 .012026 .239462 .025248 .226239 .025248 .226213 .025248 .21299 .025248H.173347C.067563 .038471-.011781 .131029 .001442 .236813 .014665 .342597 .107206 .421937 .21299 .408714H.252666C.279112 .408714 .318774 .408714 .34522 .395491 .451004 .382268 .530348 .289705 .517125 .170698 .517125 .09136 .424577 .012025 .332017 .012025Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,52.369264,39.402345)" d="M0 .039062C.079338 .02584 .158663 .012614 .251224-.000609 .158663 .025837 .079338 .039062 0 .039062Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,52.15622,39.59961)" d="M.451043 .011416C.43782 .011416 .42459 .011416 .411367 .011416 .358475 .024639 .30559 .02464 .252698 .037863 .226252 .037863 .199793 .051087 .173347 .051087 .067563 .06431-.011781 .170092 .001442 .275876 .014665 .38166 .107219 .461 .226226 .447777 .252672 .447777 .279144 .434553 .318813 .434553 .371705 .42133 .424571 .421329 .490686 .408106 .59647 .394883 .675834 .289101 .649388 .183317 .636165 .077533 .556827 .011416 .451043 .011416Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,52.11801,39.34961)" d="M0 .013672C.039669 .013672 .079357 .000452 .119026 .000452 .079357 .013675 .039669 .013672 0 .013672Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,51.93289,39.560548)" d="M.304135 .012655H.290899 .26446C.238014 .012655 .211555 .012652 .185109 .025875 .079325 .039098 0 .118436 0 .22422V.237444C.013223 .343228 .105797 .435788 .211581 .422565 .25125 .422565 .27767 .422568 .317339 .409345H.330575C.436359 .396122 .515703 .30356 .50248 .197776 .489257 .091992 .409919 .012655 .304135 .012655Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,51.972566,39.335939)" d="M0 .013672C.052892 .013672 .092542 .013671 .145434 .000448 .105765 .013671 .052892 .013672 0 .013672Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,51.77295,39.546876)" d="M.345042 .012203H.331838C.318615 .012203 .305373 .012203 .278927 .012203 .252481 .012203 .212818 .012203 .186372 .012203 .080588 .012203-.01196 .104765 .001263 .223772 .001263 .329556 .093805 .422116 .212812 .408893 .239258 .408893 .26573 .408893 .305399 .408893 .331845 .408893 .345068 .408893 .371514 .408893 .477298 .39567 .556623 .303112 .556623 .197328 .5434 .091544 .450826 .012203 .345042 .012203Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,51.509798,39.51953)" d="M.357014-.000205C.317345-.000205 .290906-.000205 .26446-.000205 .238014-.000205 .224791-.000205 .198345-.000205 .092561-.000205 0 .092356 0 .198139 0 .303923 .092561 .396484 .198345 .396484 .224791 .396484 .251217 .396484 .277663 .396484 .304109 .396484 .330568 .396484 .357014 .396484 .462798 .396484 .555359 .303923 .555359 .198139 .555359 .092356 .476021-.000205 .357014-.000205Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,51.324617,39.51953)" d="M.198345-.000205C.092561-.000205 0 .079133 0 .198139 0 .303923 .079357 .396484 .185141 .396484 .211587 .396484 .238014 .396484 .26446 .396484 .290906 .396484 .330601 .396484 .357047 .396484H.383486C.48927 .396484 .581831 .303923 .581831 .198139 .581831 .092356 .502493-.000205 .383486-.000205 .343817-.000205 .304129-.000205 .26446-.000205 .251237-.000205 .224791-.000205 .198345-.000205Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,51.668428,39.51953)" d="M.304135-.000205C.264466-.000205 .224791-.000205 .198345-.000205 .092561-.000205 0 .092356 0 .198139 0 .303923 .092561 .396484 .198345 .396484 .238014 .396484 .264466 .396484 .304135 .396484 .409919 .396484 .489244 .303923 .489244 .198139V.184916C.502467 .079132 .409919-.000205 .304135-.000205Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,52.660188,39.535158)" d="M.542156 .000583C.370257 .053475 .185122 .093144 0 .132812 .198345 .106367 .370257 .053475 .542156 .000583Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,52.46994,39.75586)" d="M.732391 .022143C.719168 .022143 .692702 .022144 .679479 .035367 .50758 .088259 .335694 .127927 .163795 .154373 .058011 .180819-.02132 .273382 .005126 .379166 .031572 .48495 .124126 .564289 .22991 .537843 .415032 .511397 .600148 .458505 .78527 .405613 .877831 .379167 .930735 .299826 .930735 .220488 .930735 .207265 .930723 .18082 .9175 .167597 .904277 .088259 .824951 .022143 .732391 .022143Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,54.37915,40.222658)" d="M0 .171875C.092561 .118983 .171899 .05287 .26446-.000022 .185122 .05287 .092561 .10576 0 .171875Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,54.180299,40.458986)" d="M.463323 .025097C.423654 .025097 .383997 .038319 .344329 .064765 .251768 .13088 .17241 .183774 .093072 .236666 .000511 .289558-.025935 .421789 .026957 .51435 .079849 .606911 .212092 .633357 .304653 .580465 .397214 .527573 .489756 .461454 .582317 .395339 .635208 .35567 .661667 .302781 .661667 .236666 .661667 .196997 .648438 .157329 .621992 .11766 .582323 .051545 .529438 .025097 .463323 .025097Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,38.822053,46.753908)" d="M6.605127 2.485898C6.155546 2.234662 5.61339 2.023093 5.137362 2.208215 4.753896 2.353668 4.502672 2.737136 4.291104 3.094157 3.577062 4.297449 2.995244 5.593301 2.13575 6.704032 1.858067 7.074276 1.474597 7.44452 1.025015 7.431297 .734109 7.418074 .456429 7.246177 .284531 7.008163 .112632 6.770149 .033297 6.479241 .006851 6.175112-.059264 5.315618 .363869 4.495794 .81345 3.755306 1.395262 2.803251 2.069628 1.877641 2.955569 1.176823 3.828286 .476004 4.925814 .013199 6.049768 .052868 7.147276 .092537 8.19188 .621456 9.011705 1.361944 9.831532 2.102431 10.439782 3.041264 10.955479 4.006542 11.18027 4.429678 11.43152 4.945376 11.233174 5.421404 11.087722 5.778425 10.704236 5.910652 10.386884 5.699084 9.976972 5.421401 9.646422 4.918927 9.289401 4.57513 8.919158 4.204885 8.522449 3.847868 8.112535 3.50407 7.623285 3.133827 7.134047 2.790027 6.605127 2.485898Z" fill="#ffffff" fill-opacity=".26"/>
<path transform="matrix(1,0,0,-1,30.772736,61.10547)" d="M.003439 4.765578C-.009784 4.487895 .016666 4.21021 .056335 3.932528 .267902 2.610229 1.219961 1.565611 2.291023 .825124 2.793497 .481326 3.362079 .203642 3.970337 .15075 4.578595 .097858 5.226521 .296204 5.623211 .745785 6.019901 1.195367 6.297584 1.935856 5.953786 2.425107 6.165354 2.120977 7.077747 2.17387 7.381876 2.239985 7.910796 2.358993 8.37359 2.729236 8.611604 3.218486 9.180193 4.38211 8.281039 5.069707 7.381876 5.625073 7.857903 5.704411 8.307482 5.968867 8.585164 6.365557 8.862847 6.762247 8.955405 7.291167 8.796729 7.753972 8.545492 8.507682 7.685995 8.93082 6.892615 8.93082 6.099236 8.944044 5.239741 8.639915 4.512477 8.335787 4.65793 8.772145 4.895947 9.182055 5.094292 9.60519 5.292636 10.028326 5.438083 10.491133 5.385191 10.953938 5.332299 11.416741 5.014954 11.866325 4.565372 11.972109 4.023229 12.091116 3.507529 11.707649 3.124062 11.31096 1.986885 10.160559 1.206735 8.70603 .585254 7.211832 .267902 6.458121 .029885 5.61185 .003439 4.765578Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,30.574402,61.242189)" d="M4.367027 .089643C4.300912 .089643 4.221581 .089643 4.142242 .102866 3.573654 .155759 2.978616 .393775 2.370358 .803688 1.457972 1.438392 .307571 2.522676 .056335 4.043321 .016666 4.321003-.009784 4.611906 .003439 4.916035 .016662 5.682968 .22823 6.52924 .598474 7.428403 1.352185 9.239953 2.17201 10.562256 3.176958 11.580426 3.758769 12.175461 4.287695 12.413474 4.803392 12.294467 5.305866 12.17546 5.702543 11.68621 5.781881 11.104398 5.847996 10.549032 5.636437 10.006889 5.477762 9.649868 5.411647 9.517638 5.345532 9.372185 5.279417 9.239955 5.213302 9.094501 5.133964 8.962271 5.081072 8.816817 5.662884 9.041609 6.403361 9.266401 7.10418 9.253179 7.89756 9.239956 8.88929 8.830044 9.193419 7.944103 9.365317 7.441629 9.27275 6.846592 8.955399 6.383788 8.743831 6.066436 8.426486 5.828424 8.069465 5.682971 8.757061 5.206943 9.576885 4.45323 8.995074 3.263161 8.730614 2.721018 8.214914 2.311108 7.61988 2.178878 7.48765 2.152432 6.945504 2.073093 6.509145 2.165654 6.562037 1.676403 6.310801 1.121037 5.980226 .750793 5.583536 .327657 5.001731 .089643 4.367027 .089643ZM.400129 4.902815C.386906 4.625132 .413355 4.360672 .453024 4.109435 .677815 2.721021 1.748868 1.729297 2.608362 1.134262 3.150505 .750795 3.679428 .539226 4.181902 .499557 4.763713 .446665 5.332309 .645009 5.676106 1.028476 6.04635 1.438389 6.271144 2.073095 5.993462 2.456562 5.927347 2.549123 5.953783 2.668126 6.033121 2.734241 6.125682 2.800356 6.244686 2.773911 6.310801 2.694574 6.429808 2.522675 7.157082 2.509454 7.527326 2.588792 7.99013 2.694575 8.413256 3.025147 8.624824 3.461506 9.074406 4.373892 8.532269 4.942483 7.474431 5.616856 7.408315 5.656525 7.368653 5.749086 7.381876 5.828424 7.395099 5.907762 7.461207 5.973875 7.540545 5.987098 7.976904 6.053213 8.37359 6.291226 8.611604 6.635024 8.862841 6.992044 8.928959 7.45485 8.796729 7.838316 8.558715 8.552359 7.73889 8.882933 7.077741 8.882933 7.064517 8.882933 7.064524 8.882933 7.051301 8.882933 6.244699 8.882933 5.371971 8.552359 4.776937 8.301122 4.710822 8.274675 4.618264 8.287901 4.565372 8.340794 4.51248 8.393685 4.486031 8.47302 4.512477 8.552358 4.618261 8.869709 4.763714 9.160617 4.909167 9.4383 4.975282 9.57053 5.041397 9.70276 5.107512 9.83499 5.332303 10.337463 5.42486 10.720929 5.385191 11.07795 5.345522 11.434971 5.094285 11.844883 4.724041 11.924221 4.367021 12.003559 3.943885 11.805217 3.467857 11.315967 2.502578 10.324242 1.709212 9.054834 .968724 7.282953 .611704 6.423459 .413352 5.630079 .400129 4.902815Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,52.536989,41.666017)" d="M4.1826 1.669566C4.156154 1.603451 4.129714 1.524117 4.090045 1.458002 4.090045 1.444779 4.07681 1.431553 4.07681 1.41833 3.772681 .849741 3.217315 .49272 2.622281 .281152 2.304929 .175368 1.974367 .096031 1.643792 .096031 1.564454 .096031 1.498346 .096032 1.419008 .109255 1.009095 .148924 .612412 .320821 .334729 .611727 .057046 .915856-.07521 1.352215 .043797 1.748905 .136358 2.053034 .334703 2.304273 .572717 2.515841 .691724 2.621625 .82396 2.727407 .942967 2.819968 1.048751 2.912529 1.167771 2.991866 1.286778 3.057981 1.538015 3.216656 1.802474 3.335666 2.080157 3.428227 2.172718 3.454673 2.26526 3.494343 2.357821 3.507565 2.966078 3.666241 3.627241 3.705907 4.023931 3.124095 4.037154 3.110873 4.037147 3.097651 4.05037 3.084428 4.341276 2.687738 4.367722 2.145594 4.1826 1.669566Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,52.365938,41.90039)" d="M1.801644 .144983C1.735529 .144983 1.656211 .144984 1.590096 .158207 1.100845 .211099 .677696 .409442 .38679 .726794 .056216 1.097038-.075995 1.612734 .043012 2.049093 .135573 2.353222 .320682 2.617682 .624811 2.908588 .743818 3.027595 .876035 3.120155 1.008265 3.225939 1.127272 3.3185 1.246285 3.397841 1.378515 3.477179 1.629752 3.635855 1.920664 3.768085 2.21157 3.873868 2.304131 3.913538 2.409921 3.939981 2.515705 3.966427 3.097517 4.11188 3.917336 4.217664 4.393364 3.503622 4.406587 3.490399 4.419803 3.463954 4.419803 3.450731 4.710709 2.987926 4.763601 2.406113 4.552033 1.837524 4.525587 1.758187 4.499135 1.67885 4.446243 1.599512 4.446243 1.586289 4.433026 1.573068 4.419803 1.559845 4.062782 .872249 3.37518 .515227 2.859483 .343328 2.48924 .211098 2.145468 .144983 1.828116 .144983 1.814893 .144983 1.814867 .144983 1.801644 .144983ZM3.24297 3.675524C3.057848 3.675524 2.846261 3.649076 2.595024 3.582961 2.502463 3.556515 2.409902 3.530068 2.330564 3.503622 2.066104 3.411061 1.814874 3.292057 1.57686 3.146604 1.471076 3.080489 1.352069 3.001149 1.246285 2.921812 1.127278 2.829251 1.008278 2.736688 .889271 2.630904 .624811 2.39289 .479345 2.181325 .41323 1.956535 .320669 1.652406 .426472 1.282161 .664486 1.017701 .889277 .779687 1.233068 .60779 1.616535 .56812 1.68265 .554898 1.761988 .554897 1.81488 .554897 2.092563 .554897 2.396679 .607787 2.727253 .726794 3.176835 .88547 3.75866 1.189601 4.062788 1.75819 4.062788 1.771413 4.076025 1.77141 4.076025 1.784633 4.115694 1.863971 4.14212 1.916863 4.155343 1.982978 4.314019 2.43256 4.287567 2.895366 4.049553 3.26561 4.03633 3.278833 4.036349 3.292054 4.036349 3.292054 3.877673 3.543291 3.613213 3.675524 3.24297 3.675524Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,53.899019,43.783205)" d="M4.050375 1.380925C4.037152 1.367702 4.023936 1.341257 4.023936 1.328034 3.838814 .997459 3.547908 .732996 3.230556 .547874 3.032211 .428867 2.82065 .336308 2.609082 .25697 2.225615 .124741 1.81569 .0454 1.405777 .085069H1.392541C.995851 .124738 .599161 .296639 .334702 .587545 .057019 .891674-.075205 1.328034 .043802 1.724723 .096694 1.909845 .202491 2.081743 .321498 2.227196 .493397 2.451986 .731404 2.637107 .942972 2.795783 1.28677 3.060242 1.67025 3.271812 2.080162 3.417265 2.172723 3.443711 2.265265 3.470158 2.357826 3.496603 2.490056 3.536273 2.622286 3.562719 2.754515 3.575942 3.243766 3.642057 3.719807 3.575942 4.023936 3.113137 4.156166 2.914793 4.23551 2.703223 4.261956 2.491655 4.301625 2.121412 4.222274 1.724723 4.050375 1.380925Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,53.688264,43.978517)" d="M1.828116 .069201C1.748778 .069201 1.669414 .069197 1.603299 .08242 1.590076 .08242 1.576879 .08242 1.563656 .08242 1.087628 .135312 .677696 .33366 .38679 .651011 .056216 1.008032-.075995 1.536952 .043012 1.973311 .095904 2.171656 .201675 2.356776 .360351 2.541898 .558696 2.779912 .796697 2.978257 1.008265 3.150156 1.378508 3.441062 1.775198 3.652629 2.198334 3.798082 2.304118 3.824528 2.396685 3.864199 2.502469 3.890645 2.661145 3.930314 2.793369 3.95676 2.925598 3.969983 3.599971 4.062544 4.076012 3.890645 4.393364 3.42784 4.538816 3.216272 4.631391 2.978259 4.67106 2.713799 4.723951 2.303886 4.644588 1.867525 4.446243 1.484058 4.43302 1.457612 4.419823 1.444389 4.4066 1.417943 4.221478 1.087368 3.930572 .796463 3.547105 .571673 3.34876 .452666 3.123969 .346883 2.885955 .267545 2.502488 .135315 2.158691 .069201 1.828116 .069201ZM1.629771 .47911C1.973569 .439441 2.3438 .505558 2.740489 .637788 2.952057 .717126 3.150402 .809687 3.335524 .915471 3.652876 1.113816 3.9041 1.351829 4.049553 1.629512 4.062776 1.655958 4.076025 1.66918 4.076025 1.682403 4.234701 1.999755 4.300803 2.343553 4.261134 2.674128 4.234688 2.872473 4.16856 3.057595 4.049553 3.216271 3.824762 3.546846 3.480971 3.665851 2.965274 3.586513 2.846267 3.57329 2.727253 3.546848 2.595024 3.507179 2.502463 3.480733 2.409902 3.454286 2.330564 3.42784 1.947097 3.29561 1.590083 3.097266 1.246285 2.846029 1.04794 2.700576 .823149 2.515453 .65125 2.303885 .532243 2.158432 .452899 2.012977 .41323 1.867524 .320669 1.563396 .426472 1.193155 .664486 .928695 .889277 .690681 1.233055 .518779 1.603299 .47911 1.616522 .47911 1.616548 .47911 1.629771 .47911Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,54.865694,46)" d="M4.220785 1.771985C4.181116 1.626532 4.128218 1.467858 4.04888 1.335628 3.903427 1.071168 3.705076 .846376 3.480285 .661254 3.480285 .661254 3.467049 .661253 3.467049 .64803 3.202589 .449686 2.911683 .304233 2.607554 .198449 2.263757 .079443 1.906736 .000105 1.549715 .013328 1.510046 .013328 1.457154 .013329 1.417485 .026552 1.007572 .066221 .610889 .238118 .333206 .529024 .267091 .595139 .214206 .674479 .174537 .753817 .015861 1.0315-.050254 1.362074 .042307 1.666202 .068753 1.758763 .108428 1.838101 .148097 1.930662 .227435 2.062892 .319977 2.181898 .42576 2.300904 .584436 2.472803 .769578 2.605036 .941477 2.750489 1.285274 3.014949 1.668722 3.226515 2.078635 3.371968 2.105081 3.38519 2.118304 3.385192 2.14475 3.398415 2.210865 3.424861 2.290228 3.438084 2.369566 3.46453 2.60758 3.517422 2.845568 3.570312 3.070359 3.570312 3.440603 3.570312 3.79763 3.451305 4.035644 3.094284 4.273658 2.684371 4.326569 2.208344 4.220785 1.771985Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,54.643068,46.185548)" d="M1.851729 .000504C1.81206 .000504 1.785607 .000504 1.745938 .000504 1.693046 .000504 1.640161 .000505 1.587269 .013728 1.098018 .06662 .674869 .264963 .383964 .582315 .317849 .661653 .251747 .740991 .198855 .846775 .00051 1.17735-.052395 1.574039 .053389 1.904614 .079835 2.010398 .119523 2.102962 .172415 2.208745 .251753 2.354198 .357543 2.486425 .47655 2.618655 .635226 2.790554 .820348 2.936007 1.00547 3.08146 1.375714 3.372365 1.772404 3.583937 2.195539 3.729389 2.221985 3.742612 2.235208 3.74261 2.261654 3.755833 2.327769 3.782279 2.407101 3.795502 2.486439 3.821948 2.777345 3.888063 3.015352 3.927734 3.240143 3.927734 3.729393 3.927734 4.112874 3.74261 4.377333 3.359143 4.655016 2.949231 4.734348 2.433534 4.602118 1.904614 4.562449 1.732715 4.496315 1.574042 4.416976 1.415366 4.271523 1.137683 4.059962 .899669 3.795502 .688101L3.782299 .674877C3.531062 .476532 3.213691 .317855 2.869893 .185625 2.512872 .066618 2.182303 .000504 1.851729 .000504ZM3.266582 3.531044C3.08146 3.531044 2.869905 3.504597 2.618669 3.438482 2.552554 3.425259 2.486426 3.398814 2.407088 3.385591 2.380642 3.372368 2.367419 3.372367 2.340973 3.372367 1.957506 3.240137 1.587269 3.041793 1.256694 2.790556 1.084795 2.658326 .912903 2.512872 .76745 2.367419 .661666 2.261635 .582308 2.142628 .516194 2.036844 .476525 1.957506 .450085 1.89139 .423639 1.825275 .357524 1.587261 .397212 1.309581 .542665 1.071567 .582334 1.005452 .622003 .939338 .674895 .886446 .899686 .635209 1.243477 .47653 1.626944 .436861 1.666613 .436861 1.706269 .436861 1.745938 .436861 2.06329 .423638 2.380655 .476532 2.750899 .608763 3.055027 .714546 3.332691 .859997 3.557482 1.031896L3.570718 1.04512H3.583954C3.808744 1.217019 3.98063 1.415363 4.099638 1.640154 4.165752 1.759161 4.218657 1.891391 4.245103 2.036844 4.350887 2.446757 4.284766 2.85667 4.073198 3.174022 3.888076 3.412036 3.636826 3.531044 3.266582 3.531044Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,53.404359,34.134767)" d="M2.680604 7.863955C1.51698 7.811064 .379809 7.13669 .075681 5.999513-.228448 4.822666 .419498 3.434253 1.384776 2.574759 2.363278 1.715264 3.593003 1.292126 4.809518 1.014443 5.840912 .776429 6.898751 .63098 7.916922 .340074 8.22105 .247513 8.538415 .379743 8.657422 .670649 9.490471 2.548314 10.283837 4.465644 10.495405 6.5681 10.548297 7.123466 10.561507 7.692056 10.416055 8.247421 10.270601 8.802787 9.926817 9.344931 9.437566 9.649059 8.630963 10.13831 7.626016 9.834182 7.057427 9.186255 6.792967 8.882126 6.594629 8.511882 6.435953 8.128415 6.356615 7.93007 6.039257 6.581324 5.933473 6.449094 5.801243 6.237526 4.597938 7.943294 2.680604 7.863955Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,53.194398,34.308595)" d="M8.325227 .289622C8.24589 .289622 8.166564 .302843 8.074003 .329289 7.4393 .514411 6.778124 .646644 6.130198 .765651 5.746731 .844989 5.36327 .911102 4.979804 1.003663 4.001302 1.228454 2.573238 1.638367 1.462507 2.603645 .523675 3.436694-.269724 4.877999 .087296 6.226744 .391425 7.377145 1.50217 8.183749 2.877361 8.236641 4.265775 8.289533 5.283933 7.469706 5.839298 7.020124 5.905413 6.967232 5.984764 6.901119 6.050879 6.86145 6.116994 7.073018 6.222765 7.482929 6.302103 7.747389 6.394664 8.077963 6.447562 8.289533 6.474008 8.368871 6.645907 8.831676 6.870685 9.188696 7.121922 9.492826 7.716957 10.167198 8.814485 10.550663 9.753317 9.995297 10.255791 9.691169 10.639238 9.135803 10.82436 8.474653 10.996259 7.83995 10.956603 7.205247 10.90371 6.715997 10.67892 4.587095 9.898764 2.682984 9.052492 .752427 8.933485 .461521 8.642579 .289622 8.325227 .289622ZM2.9038 7.839951C1.912076 7.800282 .774892 7.25814 .483986 6.120962 .219526 5.102792 .735223 3.780493 1.74017 2.894553 2.771564 1.982166 4.146749 1.585477 5.072358 1.373909 5.455825 1.281348 5.839304 1.215231 6.209548 1.135893 6.857475 1.003663 7.531847 .884657 8.192997 .686312 8.404565 .620197 8.616139 .712759 8.695477 .897881 9.528526 2.788768 10.308689 4.66643 10.520257 6.74244 10.573149 7.192022 10.599595 7.787058 10.454142 8.355647 10.295466 8.924236 9.978088 9.38704 9.568175 9.638277 8.827688 10.087858 7.915321 9.757284 7.439293 9.215141 7.214502 8.950682 7.016138 8.633329 6.857462 8.210194 6.831016 8.144078 6.764908 7.879619 6.698792 7.628383 6.500448 6.874672 6.407893 6.583765 6.341778 6.491204 6.302109 6.438313 6.249224 6.398645 6.183109 6.385422 6.024433 6.358976 5.892197 6.477982 5.614514 6.702773 5.085594 7.125909 4.146761 7.892843 2.9038 7.839951Z" fill="#231f20"/>
<g opacity=".4">
<g clip-path="url(#clip_0)">
<path transform="matrix(1,0,0,-1,53.404359,33.88867)" d="M7.070663 2.300475C5.629357 2.961625 4.320261 3.199638 2.720279 3.02774 1.503764 2.908733 .657492 4.363264 .538485 5.57978 .498816 5.950023 .512033 6.307042 .564925 6.664062 .340134 6.412826 .168242 6.095475 .075681 5.751677-.228448 4.574831 .419498 3.186417 1.384776 2.326922 2.363278 1.467428 3.593003 1.04429 4.809518 .766607 5.840912 .528593 6.898751 .383143 7.916922 .092237 8.22105-.000323 8.538415 .131907 8.657422 .422812 9.239234 1.745112 9.807822 3.10708 10.178066 4.52194 9.543363 3.027742 8.670645 1.559988 7.070663 2.300475Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,53.194398,34.04297)" d="M8.325227 .021106C8.24589 .021106 8.166564 .034327 8.074003 .060773 7.4393 .245895 6.778124 .378128 6.130198 .497134 5.746731 .576472 5.36327 .642586 4.979804 .735147 4.001302 .959938 2.573238 1.369851 1.462507 2.335129 .523675 3.168178-.269724 4.609484 .087296 5.958229 .179857 6.328473 .364979 6.659047 .616216 6.949953 .669108 7.016068 .774899 7.042515 .854236 7.002846 .933574 6.963177 .97325 6.883839 .960027 6.791278 .907135 6.44748 .893918 6.103682 .933587 5.759884 1.039371 4.688821 1.793069 3.260738 2.9038 3.379745 4.517005 3.551644 5.852521 3.326852 7.359942 2.639256 7.796301 2.440912 8.179788 2.414468 8.536808 2.546698 9.290519 2.837604 9.819419 3.882218 10.202886 4.754936 10.242555 4.847497 10.348358 4.900391 10.454142 4.860722 10.546702 4.821053 10.612818 4.72849 10.586371 4.622705 10.216128 3.154953 9.634316 1.779764 9.065727 .483911 8.933497 .193005 8.642579 .021106 8.325227 .021106ZM.523662 5.9979C.510439 5.945008 .497209 5.905338 .483986 5.852447 .219526 4.834276 .735223 3.511977 1.74017 2.626037 2.771564 1.71365 4.146749 1.316961 5.072358 1.105393 5.455825 1.012832 5.839304 .946714 6.209548 .867376 6.857475 .735147 7.531847 .61614 8.192997 .417796 8.404565 .351681 8.616139 .444242 8.695477 .629364 9.012829 1.343406 9.343404 2.097116 9.621087 2.877273 9.356627 2.546699 9.039263 2.282238 8.682241 2.150008 8.232659 1.978109 7.73018 2.004554 7.188037 2.255791 5.759953 2.91694 4.490566 3.128511 2.943476 2.956612 1.647622 2.824382 .68235 4.265686 .536898 5.693769 .536898 5.812776 .536885 5.905339 .523662 5.9979Z" fill="#231f20"/>
</g>
</g>
<g opacity=".15">
<g clip-path="url(#clip_1)">
<path transform="matrix(1,0,0,-1,54.352754,30.779297)" d="M6.59826 6.228516C6.452807 6.135955 6.307374 6.030169 6.188367 5.911162 6.783401 5.223567 7.193301 4.390518 7.166855 3.42524 7.166855 2.777313 6.849516 2.235171 6.254482 1.917819 4.416486 1.097994 2.380132 2.155833 1.414854 3.702923 1.269401 3.927714 1.150381 4.165727 1.071043 4.416964 .674353 4.324403 .304129 4.152506 0 3.914492 .013223 3.769039 .039675 3.610363 .10579 3.451687 .476034 2.7112 .938832 1.679807 1.851219 1.812037 3.041288 1.970713 3.927209 1.25667 4.839596 .674859 5.937104 .000486 7.378423 .701304 7.960235 1.851704 8.462708 2.869875 8.317243 3.954159 7.880884 4.945884 7.629647 5.607033 7.153626 5.977279 6.59826 6.228516Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,54.15442,30.5625)" d="M5.976786 .008054C5.606542 .008054 5.249535 .113835 4.932183 .298957 4.826399 .365072 4.707392 .444411 4.601608 .510526 3.808228 1.039446 3.054505 1.528698 2.076003 1.396468 1.071056 1.251015 .555353 2.282408 .185109 3.036119L.13223 3.141901C.052892 3.327023 .013223 3.498922 0 3.67082 0 3.736935 .026427 3.803051 .079319 3.84272 .409893 4.093957 .806609 4.279079 1.229745 4.384863 1.335529 4.411309 1.428064 4.358418 1.467733 4.252633 1.547071 4.01462 1.652874 3.789829 1.785104 3.591484 2.671044 2.163401 4.588372 1.079113 6.373476 1.872493 6.902396 2.163399 7.180091 2.612982 7.180091 3.194794 7.206537 4.027843 6.889185 4.821221 6.254482 5.561709 6.188367 5.641047 6.188347 5.760054 6.267685 5.826169 6.399915 5.958399 6.545375 6.077405 6.704051 6.183189 6.756943 6.222858 6.836268 6.222859 6.88916 6.196413 7.589978 5.865838 8.03956 5.42948 8.277574 4.821222 8.793271 3.631153 8.81973 2.533644 8.356925 1.568366 7.999904 .854324 7.338748 .312181 6.59826 .100613 6.373469 .034498 6.175131 .008054 5.976786 .008054ZM3.887547 1.422912C4.204899 1.251013 4.509027 1.05267 4.813156 .854325 4.91894 .78821 5.024744 .708871 5.130527 .642756 5.619778 .338627 6.122232 .391518 6.466031 .484078 7.087511 .655977 7.669342 1.145228 7.973471 1.740263 8.383384 2.599757 8.356912 3.578259 7.880884 4.662545 7.682539 5.151795 7.338748 5.508817 6.796605 5.7865 6.743713 5.74683 6.690841 5.720386 6.651172 5.680717 7.272652 4.90056 7.576756 4.067512 7.550309 3.194794 7.550309 2.467529 7.193295 1.872495 6.532146 1.515474H6.518942C5.646224 1.118784 4.720595 1.132006 3.887547 1.422912ZM2.406578 1.819602C2.72393 1.819602 3.014829 1.766709 3.292512 1.674148 2.525578 2.070837 1.877665 2.692319 1.454529 3.379915 1.335522 3.551814 1.242955 3.75016 1.150394 3.961728 .885934 3.88239 .634684 3.750159 .409893 3.604706 .423116 3.512145 .449575 3.406361 .489244 3.3138L.542123 3.22124C.885921 2.533644 1.29586 1.687374 2.023124 1.793158 2.155354 1.819604 2.274348 1.819602 2.406578 1.819602Z" fill="#231f20"/>
</g>
</g>
<g opacity=".3">
<g clip-path="url(#clip_2)">
<path transform="matrix(1,0,0,-1,53.910158,32.4375)" d="M8.944955 7.947568C8.336698 8.317812 7.609414 8.225251 7.040825 7.881454 7.596191 7.616993 8.072212 7.246749 8.323449 6.612045 8.759808 5.620321 8.905273 4.536036 8.4028 3.517866 7.820988 2.367465 6.379669 1.666648 5.282161 2.341021 4.369774 2.922832 3.483853 3.63687 2.293784 3.478195 1.381397 3.345964 .918599 4.364136 .548355 5.117846 .48224 5.276523 .442565 5.421976 .442565 5.580651 .297112 5.474868 .178086 5.355861 .059079 5.223631-.007036 4.866611-.020253 4.496365 .032639 4.139344 .151646 2.922829 .997918 1.455078 2.214433 1.587308 3.814415 1.759207 5.123511 1.521194 6.564817 .860044 8.164799 .132779 9.037516 1.587306 9.672219 3.081505 9.817673 3.663316 9.936666 4.271575 9.989558 4.879832 10.042451 5.435198 10.05566 6.003788 9.910208 6.559155 9.777978 7.11452 9.434206 7.656662 8.944955 7.947568Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,53.704164,32.09961)" d="M7.577384 .112535C7.286479 .112535 6.995566 .17865 6.678215 .324103 5.250131 .985253 3.980743 1.196819 2.433653 1.02492 1.1378 .892691 .172528 2.333998 .027075 3.762082-.012594 4.132325-.0126 4.515793 .053515 4.89926 .053515 4.938929 .079967 4.965374 .09319 4.99182 .212197 5.12405 .344407 5.256279 .503083 5.375286 .555975 5.414955 .635313 5.428178 .701428 5.401731 .767543 5.375286 .807232 5.309173 .820455 5.229835 .833678 5.097605 .860117 4.965374 .913009 4.833145L.965888 4.740582C1.309686 4.052986 1.719592 3.206717 2.446857 3.3125 3.570811 3.471176 4.417096 2.902586 5.236921 2.360444 5.342705 2.294328 5.448476 2.21499 5.55426 2.148875 6.043511 1.844746 6.545998 1.89764 6.889795 1.990201 7.511276 2.1621 8.093075 2.651351 8.397203 3.246386 8.807117 4.10588 8.780677 5.08438 8.304649 6.168666 8.106304 6.684362 7.722837 7.054607 7.11458 7.345513 7.048465 7.371959 7.008809 7.438073 6.995585 7.51741 6.995585 7.596748 7.022025 7.662865 7.08814 7.702534 7.762513 8.125669 8.582338 8.152115 9.203819 7.768649 9.706292 7.46452 10.089772 6.909154 10.274894 6.248004 10.446794 5.613301 10.407104 4.978597 10.354213 4.489346 10.288097 3.894311 10.182314 3.299278 10.023638 2.664575 10.023638 2.651351 10.023658 2.638127 10.010434 2.638127 9.574076 1.63318 9.018703 .548895 8.11954 .205097 7.98731 .152205 7.788952 .112535 7.577384 .112535ZM3.412142 1.487725C4.602211 1.487725 5.673286 1.236488 6.863356 .694345 7.299715 .496001 7.683168 .469557 8.040189 .601787 8.7939 .892693 9.322832 1.924087 9.706299 2.796804 9.864975 3.405062 9.957523 3.97365 10.023638 4.555461 10.07653 5.005043 10.102976 5.600077 9.957523 6.168666 9.798847 6.737254 9.481502 7.200059 9.071589 7.451295 8.661676 7.702532 8.172419 7.72898 7.709614 7.543858 8.212088 7.239729 8.542669 6.856262 8.741014 6.353789 9.256711 5.163719 9.28317 4.06621 8.820365 3.100932 8.463345 2.38689 7.802188 1.844747 7.061701 1.633179 6.466666 1.46128 5.898077 1.540619 5.408826 1.844748 5.303042 1.910862 5.184036 1.990201 5.078252 2.056316 4.284872 2.585236 3.531149 3.074484 2.552647 2.942254 1.5477 2.796802 1.031997 3.828198 .661753 4.581908L.608874 4.687691C.582428 4.740583 .555982 4.806698 .542759 4.859591 .516313 4.833144 .50309 4.819921 .476644 4.793475 .423752 4.4629 .423739 4.132324 .463408 3.814972 .569192 2.74391 1.322922 1.315827 2.433653 1.434834 2.751005 1.46128 3.081567 1.487725 3.412142 1.487725Z" fill="#231f20"/>
</g>
</g>
<g opacity=".3">
<g clip-path="url(#clip_3)">
<path transform="matrix(1,0,0,-1,56.79648,28.771485)" d="M3.057055 1.271847C2.951272 1.099948 2.819067 .914826 2.620723 .875157 2.462047 .848711 2.303371 .928052 2.157918 .994167 1.668667 1.258627 1.20585 1.575978 .690153 1.774323 .518254 1.840438 .319909 1.893328 .161233 1.787544 .068672 1.72143 .002564 1.615646 .002564 1.496639-.010659 1.377632 .02899 1.258625 .081882 1.166064 .240558 .861935 .544693 .676814 .848822 .518138 1.245512 .319793 1.655444 .147893 2.091803 .095001 2.528162 .042109 2.990947 .108223 3.361191 .346237 3.718212 .584251 3.969449 .980942 4.088456 1.390855 4.207462 1.800767 4.220692 2.25035 4.194246 2.686708 4.181023 2.87183 4.1678 3.096622 3.995901 3.228852 3.876894 3.321413 3.718205 3.281743 3.65209 3.149513 3.572752 2.977614 3.559549 2.739599 3.506657 2.541255 3.453765 2.34291 3.400866 2.131342 3.334751 1.94622 3.268636 1.708206 3.176063 1.483414 3.057055 1.271847Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,56.597414,28.917969)" d="M2.581731 .016478C2.475947 .016478 2.370144 .029699 2.26436 .042922 1.86767 .095814 1.444547 .241266 .955296 .492502 .637945 .651178 .280924 .87597 .095802 1.232991 .016464 1.378444-.009976 1.53712 .003247 1.66935 .01647 1.854472 .109018 2.026372 .254471 2.118933 .518931 2.290832 .849512 2.158602 .955296 2.118933 1.32554 1.97348 1.656121 1.775135 1.986696 1.590013 2.132149 1.510675 2.290812 1.418115 2.436265 1.338777 2.542049 1.285885 2.674292 1.219768 2.780076 1.232991 2.872637 1.246214 2.978427 1.351998 3.084211 1.53712 3.189995 1.722242 3.282537 1.920586 3.361875 2.1586 3.427989 2.330499 3.480888 2.528845 3.53378 2.740413 3.547003 2.806528 3.560232 2.872643 3.573455 2.938758 3.599901 3.084211 3.626334 3.242887 3.692449 3.375117 3.745341 3.494124 3.851138 3.573461 3.970145 3.613129 4.089152 3.639575 4.221388 3.613131 4.340395 3.533793 4.591632 3.348671 4.604836 3.031319 4.618059 2.846198 4.644505 2.304055 4.618072 1.867694 4.499065 1.484227 4.353612 .981753 4.062693 .571843 3.692449 .333829 3.375098 .122261 2.991644 .016478 2.581731 .016478ZM.571842 1.80158C.532174 1.80158 .492479 1.788357 .479256 1.775134 .439587 1.748688 .41316 1.695796 .399937 1.629681 .386714 1.550343 .42637 1.457784 .452816 1.404892 .585046 1.153655 .862723 .981754 1.140405 .836301 1.589987 .598287 1.973473 .466057 2.317271 .426388 2.75363 .373496 3.1503 .452835 3.454429 .65118 3.745335 .849525 3.983368 1.1801 4.102375 1.590013 4.208159 1.920588 4.234612 2.317277 4.208166 2.806528 4.194942 2.965204 4.1817 3.123879 4.089139 3.189994 4.075916 3.203217 4.0627 3.203218 4.0627 3.203218L4.049464 3.189994C4.009795 3.097433 3.983368 2.978427 3.970145 2.846198 3.956922 2.76686 3.943693 2.68752 3.93047 2.621405 3.877578 2.396614 3.811476 2.185047 3.745361 1.999925 3.6528 1.748688 3.560232 1.523898 3.441226 1.31233 3.335442 1.127208 3.163523 .875969 2.859394 .823077 2.647826 .783408 2.449482 .87597 2.26436 .968531 2.105684 1.047869 1.947008 1.14043 1.801555 1.232991 1.47098 1.418113 1.166864 1.603235 .823066 1.735465 .717283 1.775134 .637957 1.80158 .571842 1.80158Z" fill="#231f20"/>
</g>
</g>
<path transform="matrix(1,0,0,-1,47.278047,18.423829)" d="M2.472994 6.408585C1.719284 6.673044 .820107 6.527593 .34408 5.866443-.145171 5.19207-.079037 4.147453 .330876 3.340851 .740789 2.547471 1.428365 1.965659 2.129184 1.489632 2.737441 1.079719 3.372151 .722698 3.953963 .273117 4.125862 .140887 4.363869 .140886 4.509322 .299562 5.51427 1.291286 6.492784 2.322681 7.167157 3.618534 7.339056 3.962332 7.497738 4.319353 7.537407 4.716043 7.577076 5.112733 7.497725 5.54909 7.259711 5.853219 6.863021 6.368916 6.148993 6.421808 5.620073 6.157348 5.368836 6.025118 5.157268 5.839996 4.958923 5.628428 4.866363 5.522645 4.310984 4.742488 4.218423 4.676373 4.059747 4.583813 3.715955 5.985449 2.472994 6.408585Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,47.055208,18.621094)" d="M4.428063 .179161C4.295833 .179161 4.15038 .21883 4.031374 .311391 3.674353 .589074 3.277663 .840311 2.907419 1.078325 2.682628 1.223778 2.457811 1.369229 2.219798 1.514682 1.637986 1.911372 .81818 2.532854 .355375 3.44524-.041315 4.225396-.199997 5.375798 .381814 6.169177 .884288 6.856773 1.823127 7.094786 2.735514 6.777433 3.661123 6.460082 4.110711 5.666703 4.361948 5.230344 4.375171 5.203898 4.388368 5.177451 4.401591 5.151005 4.494152 5.270012 4.626382 5.455134 4.732166 5.587364 4.877619 5.785709 4.95697 5.904716 5.009862 5.944385 5.247876 6.195621 5.472654 6.380744 5.72389 6.512974 6.305702 6.80388 7.125534 6.777434 7.614785 6.155953 7.866022 5.825378 7.985048 5.362574 7.932156 4.873323 7.879264 4.410519 7.694142 4.013828 7.535466 3.7097 6.861094 2.387401 5.882586 1.36923 4.851192 .337837 4.745409 .245275 4.586739 .179161 4.428063 .179161ZM1.955338 6.526197C1.492533 6.526197 1.016518 6.354298 .712389 5.944385 .302476 5.375796 .302476 4.436964 .712389 3.630361 1.135525 2.797312 1.915695 2.215501 2.444614 1.845257 2.669405 1.699804 2.894177 1.554353 3.118968 1.4089 3.502434 1.170886 3.899131 .906425 4.282597 .628742 4.375158 .549404 4.494159 .562628 4.573497 .641966 5.578444 1.646914 6.543729 2.638637 7.191656 3.908045 7.337109 4.185727 7.495778 4.542748 7.54867 4.939438 7.588339 5.322905 7.495771 5.693148 7.310649 5.944385 6.966852 6.393967 6.345371 6.40719 5.922235 6.182399 5.710668 6.076615 5.512342 5.91794 5.313997 5.706372 5.287551 5.666703 5.168525 5.521251 5.062741 5.375798 4.732166 4.926216 4.613159 4.76754 4.533821 4.714648 4.480929 4.674979 4.414827 4.661755 4.348712 4.6882 4.216482 4.714646 4.150367 4.833653 4.018137 5.058444 3.780123 5.468357 3.396663 6.155953 2.603284 6.420413 2.418162 6.486528 2.193352 6.526197 1.955338 6.526197Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,47.264863,18.298829)" d="M3.953978 1.771021C3.200268 2.551178 2.420085 3.027205 1.349023 3.318111 .542421 3.542902 .370528 4.693302 .595319 5.499905 .661434 5.737919 .753988 5.975932 .886218 6.1875 .67465 6.081716 .489548 5.92304 .344095 5.724695-.145156 5.050323-.079054 4.005708 .330859 3.199105 .740772 2.405725 1.42838 1.823914 2.129199 1.347886 2.737457 .937973 3.372167 .580951 3.953978 .131369 4.125877-.000861 4.363885-.000859 4.509338 .157817 5.22338 .858635 5.924179 1.5859 6.505991 2.405725 5.725834 1.625569 4.80025 .898304 3.953978 1.771021Z" fill-opacity=".76"/>
<path transform="matrix(1,0,0,-1,58.24588,9.425781)" d="M2.070358 4.943133C1.673668 5.3266 1.091882 5.45883 .642301 5.128255 .179496 4.797681-.045302 4.057192 .00759 3.396043 .060482 2.734893 .324949 2.166305 .642301 1.650608 .90676 1.214249 1.21087 .804337 1.448884 .341532 1.528222 .196079 1.660465 .143187 1.792694 .209302 2.652189 .632438 3.511671 1.082019 4.225712 1.796061 4.410834 1.981183 4.595963 2.19275 4.728192 2.45721 4.847199 2.72167 4.913301 3.039022 4.847186 3.316705 4.741402 3.766287 4.318266 3.991078 3.921577 3.951409 3.736455 3.924963 3.564556 3.858848 3.392657 3.766287 3.300096 3.713395 2.784406 3.329928 2.705068 3.303482 2.572838 3.263813 2.718285 4.321653 2.070358 4.943133Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,58.046876,9.576172)" d="M1.87264 .134357C1.713964 .134357 1.555308 .226918 1.462747 .398817 1.317294 .6765 1.145402 .954182 .973503 1.218642 .867719 1.377318 .761928 1.535995 .669367 1.707893 .418131 2.131029 .06111 2.778955 .008218 3.545888-.044674 4.193815 .153658 5.040087 .722247 5.449999 1.22472 5.80702 1.899086 5.727682 2.40156 5.251655 2.877588 4.802073 2.970175 4.154146 3.023067 3.783903 3.089182 3.836795 3.168519 3.889686 3.221411 3.929355 3.366864 4.035139 3.446203 4.088031 3.485871 4.114477 3.697439 4.233484 3.895765 4.299599 4.09411 4.326045 4.543692 4.378937 5.085847 4.1277 5.2313 3.545888 5.310638 3.24176 5.257746 2.884739 5.09907 2.554164 4.953617 2.250035 4.728813 2.012022 4.556914 1.840123 3.816427 1.099636 2.956926 .650054 2.070985 .213695 2.00487 .14758 1.938755 .134357 1.87264 .134357ZM1.409836 5.264877C1.25116 5.264877 1.092484 5.225209 .947031 5.119425 .576787 4.854965 .33878 4.207038 .391672 3.572335 .444564 2.897962 .761928 2.302928 .999942 1.906238 1.092503 1.747562 1.198294 1.588886 1.304078 1.43021 1.475976 1.16575 1.661085 .888068 1.819761 .583939 1.832984 .557493 1.859443 .517824 1.899112 .54427 2.758607 .967405 3.578413 1.403764 4.292455 2.091359 4.437908 2.236812 4.636252 2.448381 4.75526 2.699618 4.874266 2.937632 4.913942 3.215314 4.86105 3.426882 4.768489 3.783903 4.437908 3.942579 4.160224 3.916132 4.014772 3.90291 3.869319 3.850018 3.69742 3.757457 3.670974 3.744234 3.565197 3.664896 3.472636 3.59878 3.115615 3.347544 3.03629 3.294652 2.956952 3.268206 2.890837 3.254983 2.837938 3.268206 2.785046 3.294652 2.679262 3.360767 2.666039 3.479773 2.652817 3.678118 2.613147 4.008693 2.547026 4.577282 2.150336 4.960749 1.938768 5.132648 1.674296 5.264877 1.409836 5.264877Z" fill="#231f20"/>
<g opacity=".4">
<g clip-path="url(#clip_4)">
<path transform="matrix(1,0,0,-1,58.24588,9.277344)" d="M1.845574 1.308359C1.581114 2.048847 1.224093 2.577766 .655504 3.053794 .219145 3.424038 .40428 4.257087 .734855 4.75956 .840639 4.905013 .946404 5.050466 1.078633 5.15625 .919958 5.129804 .77453 5.076913 .642301 4.984352 .179496 4.653777-.045302 3.913289 .00759 3.252139 .060482 2.590989 .324949 2.022401 .642301 1.506704 .90676 1.070345 1.21087 .660433 1.448884 .197628 1.528222 .052175 1.660465-.000717 1.792694 .065398 2.400952 .369527 3.009197 .673656 3.564563 1.083569 2.876967 .752994 2.136479 .488534 1.845574 1.308359Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,58.046876,9.449219)" d="M1.87264 .01178C1.713964 .01178 1.555308 .104341 1.462747 .27624 1.317294 .553923 1.145402 .831605 .973503 1.096065 .867719 1.254741 .761928 1.413418 .669367 1.585317 .418131 2.008452 .06111 2.656378 .008218 3.423312-.044674 4.071239 .153658 4.91751 .722247 5.327423 .880922 5.44643 1.066044 5.512545 1.251166 5.53899 1.343727 5.552214 1.423078 5.499322 1.462747 5.419984 1.502416 5.340646 1.475951 5.248085 1.409836 5.195193 1.290829 5.089409 1.185058 4.970403 1.092497 4.838172 .775145 4.375368 .656151 3.674549 .973503 3.396866 1.568537 2.894392 1.952004 2.352249 2.229687 1.572093 2.309025 1.373748 2.401573 1.241518 2.547026 1.188627 2.837932 1.06962 3.300737 1.267965 3.67098 1.453087 3.763541 1.505979 3.869325 1.466309 3.93544 1.373748 3.988332 1.281187 3.961899 1.175403 3.882561 1.109288 3.30075 .672929 2.666033 .355578 2.084221 .064672 2.004883 .025003 1.938755 .01178 1.87264 .01178ZM.391672 3.780333C.378449 3.661326 .378449 3.555542 .391672 3.436536 .444564 2.762163 .761928 2.167128 .999942 1.770438 1.092503 1.611762 1.198294 1.453086 1.304078 1.29441 1.475976 1.02995 1.661085 .752267 1.819761 .448138 1.832984 .421692 1.859443 .382024 1.899112 .40847 2.123903 .514254 2.335471 .633261 2.560262 .752267 2.50737 .765491 2.454452 .778714 2.40156 .80516 2.150323 .897721 1.978444 1.109289 1.859437 1.413417 1.6082 2.127459 1.264389 2.629932 .722247 3.079514 .510679 3.251413 .418118 3.50265 .391672 3.780333Z" fill="#231f20"/>
</g>
</g>
<g opacity=".15">
<g clip-path="url(#clip_5)">
<path transform="matrix(1,0,0,-1,59.292634,7.828125)" d="M3.139375 2.341157C3.060037 2.35438 2.980725 2.367603 2.901387 2.35438 2.888164 1.891575 2.755934 1.441994 2.438582 1.084973 2.240237 .833736 1.962548 .741175 1.671643 .833736 .812148 1.151088 .48158 2.235373 .653479 3.14776 .679925 3.27999 .719568 3.41222 .759237 3.531227 .600561 3.623788 .428675 3.689902 .243553 3.703125 .203884 3.63701 .164208 3.570895 .137762 3.491557 .031979 3.094867-.13992 2.552725 .203877 2.288265 .640236 1.944467 .706345 1.375879 .825352 .846959 .970805 .225478 1.671623 .000688 2.213766 .225479 2.689793 .437047 2.980712 .899851 3.152611 1.415548 3.284841 1.746123 3.245159 2.050251 3.139375 2.341157Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,59.10962,7.890625)" d="M1.973646 .010559C1.775301 .010559 1.576949 .050228 1.405051 .142789 1.114145 .288242 .915826 .552702 .836488 .870054 .823265 .936168 .810035 1.002283 .796812 1.068398 .704251 1.51798 .611671 1.941116 .281096 2.192353-.155263 2.522928 .016656 3.117963 .135663 3.554322L.148866 3.607214C.188535 3.712998 .228204 3.805558 .281096 3.884896 .320765 3.937788 .3869 3.977457 .466237 3.964234 .664582 3.937788 .87615 3.871673 1.061272 3.765889 1.14061 3.712997 1.18026 3.607213 1.140591 3.514652 1.087699 3.408868 1.061259 3.289861 1.048036 3.170855 .902583 2.364252 1.193483 1.359305 1.93397 1.081622 2.145538 1.02873 2.330686 1.094845 2.489362 1.279967 2.740599 1.570873 2.886032 1.95434 2.899255 2.430367 2.899255 2.536151 2.978613 2.615489 3.084397 2.615489 3.176957 2.615489 3.282735 2.615489 3.375296 2.589043 3.441411 2.575819 3.494283 2.522928 3.52073 2.456813 3.652959 2.073346 3.666208 1.742772 3.547201 1.412197 3.335633 .76427 2.991816 .327911 2.502565 .116343 2.330667 .050228 2.145545 .010559 1.973646 .010559ZM1.180266 1.12129C1.193489 1.068398 1.206719 1.015507 1.219941 .962615 1.286057 .698155 1.457962 .565925 1.590192 .49981 1.814983 .380803 2.092659 .380804 2.343896 .486588 2.71414 .658486 2.991829 1.015507 3.176951 1.55765 3.21662 1.663434 3.243066 1.782441 3.243066 1.888225 3.163728 1.544427 3.018269 1.266744 2.806701 1.02873 2.555464 .724601 2.211659 .605594 1.841416 .711378H1.828212C1.550529 .803939 1.352165 .949392 1.180266 1.12129ZM.545556 3.554322C.532333 3.541099 .53234 3.514652 .519117 3.501429L.505913 3.448537C.413352 3.104739 .294345 2.681604 .505913 2.509705 .545582 2.483259 .572002 2.456813 .611671 2.417144 .585225 2.694827 .585251 2.97251 .638143 3.236969 .651366 3.316308 .677812 3.408869 .704258 3.488207 .664589 3.514652 .598448 3.541099 .545556 3.554322Z" fill="#231f20"/>
</g>
</g>
<g opacity=".3">
<g clip-path="url(#clip_6)">
<path transform="matrix(1,0,0,-1,58.62802,8.78125)" d="M4.451951 2.675052C4.372613 3.018849 4.094911 3.230418 3.804005 3.296533 3.909789 3.005627 3.949484 2.714721 3.830477 2.384146 3.658578 1.868449 3.367659 1.405645 2.891631 1.194077 2.349488 .956063 1.64867 1.180853 1.503217 1.815557 1.38421 2.344477 1.318102 2.913066 .881743 3.256864 .537945 3.521323 .709844 4.063466 .815628 4.460155 .842074 4.539494 .881749 4.605608 .921418 4.671723 .84208 4.684947 .762736 4.684947 .683398 4.671723 .551168 4.552716 .432148 4.420486 .339587 4.275033-.004211 3.77256-.17609 2.939512 .260269 2.569268 .84208 2.093241 1.185878 1.56432 1.450338 .823833 1.741244 .004007 2.481719 .255244 3.156091 .599042 3.380882 .770941 3.605673 .956062 3.817241 1.16763 4.002363 1.352752 4.187491 1.56432 4.319721 1.82878 4.465174 2.080017 4.518066 2.397369 4.451951 2.675052Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,58.44394,8.714844)" d="M2.348432 .00459C2.229425 .00459 2.110405 .017813 2.004621 .070705 1.753384 .163266 1.581505 .374834 1.462498 .678963 1.211261 1.393004 .86745 1.895478 .325308 2.345059-.177166 2.768195-.04493 3.693805 .364983 4.315286 .470767 4.473961 .602984 4.619415 .748437 4.751645 .774883 4.77809 .814558 4.791313 .854227 4.804536 .946788 4.817759 1.039362 4.817759 1.131923 4.804536 1.198038 4.791313 1.264146 4.751645 1.290592 4.68553 1.317038 4.619415 1.317045 4.540077 1.264153 4.487185 1.237707 4.447516 1.211248 4.394623 1.184802 4.328508L1.171598 4.275616C1.079038 3.931818 .960031 3.508683 1.171598 3.336784 1.62118 2.992986 1.726964 2.450844 1.832748 1.974816 1.845971 1.908701 1.859168 1.842586 1.872391 1.789694 1.938506 1.525234 2.110412 1.393004 2.242642 1.326889 2.467432 1.207882 2.745109 1.207883 2.996346 1.313667 3.36659 1.485565 3.644279 1.842586 3.829401 2.384729 3.921962 2.635966 3.908745 2.87398 3.802961 3.164886 3.776515 3.231001 3.789745 3.310338 3.842637 3.363231 3.895529 3.416122 3.961631 3.442569 4.027746 3.429346 4.424435 3.350008 4.728564 3.045878 4.821125 2.662411 4.900463 2.358283 4.847571 2.001262 4.688895 1.670687 4.543442 1.366558 4.318671 1.128544 4.146772 .956645 3.935204 .745077 3.710387 .559955 3.459151 .374833 3.445927 .374833 3.445934 .36161 3.432711 .36161 3.088913 .176488 2.705453 .00459 2.348432 .00459ZM.735233 4.15661C.72201 4.130164 .708781 4.116941 .695558 4.090495 .378206 3.62769 .259212 2.926871 .576564 2.649189 1.171599 2.146715 1.555065 1.604572 1.832748 .824415 1.912086 .62607 2.004634 .49384 2.150087 .440948 2.440993 .321941 2.890562 .520286 3.260806 .705408 3.498819 .877307 3.697171 1.049207 3.895516 1.247552 4.040968 1.393005 4.239314 1.604572 4.35832 1.855809 4.477327 2.093823 4.517003 2.371506 4.464111 2.583074 4.437665 2.70208 4.384779 2.807864 4.305441 2.887202 4.318664 2.675634 4.305429 2.477289 4.22609 2.278944 4.014523 1.631018 3.670738 1.19466 3.181487 .983092 2.824466 .837639 2.41456 .837638 2.097208 1.009537 1.806302 1.15499 1.607951 1.41945 1.528613 1.736802 1.51539 1.802917 1.50216 1.869032 1.488937 1.935147 1.396376 2.384729 1.303828 2.807864 .973254 3.059101 .589787 3.297115 .642672 3.75992 .735233 4.15661Z" fill="#231f20"/>
</g>
</g>
<g opacity=".3">
<g clip-path="url(#clip_7)">
<path transform="matrix(1,0,0,-1,60.128297,6.7109377)" d="M1.034282 .485729C.941721 .459283 .84916 .432837 .769822 .485729 .703707 .525398 .677242 .617959 .650796 .684074 .571458 .948534 .518591 1.226217 .412807 1.477454 .373139 1.556792 .32024 1.649353 .240902 1.662576 .18801 1.675799 .135105 1.649353 .095436 1.609684 .055767 1.570015 .029341 1.517123 .016118 1.464231-.023551 1.292332 .016105 1.120434 .068997 .961758 .135112 .75019 .214463 .551845 .346693 .379946 .478922 .208047 .650815 .075817 .835937 .049371 1.034282 .009702 1.232633 .075817 1.404532 .194824 1.576431 .313831 1.721858 .472507 1.840865 .644406 1.893757 .723744 1.959878 .816305 1.946655 .908866 1.933432 .988204 1.867317 1.027873 1.814425 .988204 1.735087 .948535 1.655743 .869196 1.576405 .803081 1.497067 .750189 1.417742 .684075 1.325181 .644406 1.23262 .578291 1.140066 .525398 1.034282 .485729Z" fill="#ff1e00"/>
<path transform="matrix(1,0,0,-1,59.937318,6.904297)" d="M1.132655 .017667C1.079763 .017667 1.040094 .017668 1.000425 .030891 .762412 .07056 .550818 .216013 .378919 .440803 .259912 .599479 .167377 .811047 .074816 1.08873 .021924 1.273852-.030987 1.48542 .021905 1.696988 .048351 1.789549 .08802 1.882109 .154134 1.935001 .246695 2.027562 .365722 2.067231 .471506 2.040785 .669851 2.001116 .762399 1.802771 .788845 1.736656 .868183 1.551534 .921081 1.366413 .96075 1.168068 .987196 1.08873 1.000419 1.009392 1.026865 .916831 1.040088 .850716 1.053317 .82427 1.06654 .82427 1.06654 .82427 1.092967 .82427 1.145859 .850716 1.225197 .877162 1.317758 .930054 1.410319 .982946 1.476434 1.022615 1.555765 1.075506 1.635103 1.141621 1.661549 1.154844 1.674772 1.181291 1.701218 1.194514 1.75411 1.247406 1.820225 1.300297 1.899563 1.339966 1.978901 1.379635 2.084685 1.379635 2.164023 1.339966 2.243361 1.300297 2.309502 1.207737 2.322725 1.115176 2.349171 .930054 2.243374 .771378 2.177259 .705263 2.00536 .480472 1.859913 .321796 1.688015 .216012 1.529339 .083782 1.331 .017667 1.132655 .017667ZM.907839 .480472C.960731 .454026 1.013648 .42758 1.06654 .414357 1.19877 .387911 1.344217 .42758 1.48967 .533364 1.542562 .573033 1.59546 .612702 1.661575 .678817 1.648352 .665594 1.635103 .665594 1.635103 .665594 1.529319 .599479 1.410312 .546587 1.304528 .506918 1.238413 .467249 1.066514 .414357 .907839 .480472ZM.405391 1.61765V1.604426C.378945 1.485419 .405365 1.339966 .445034 1.207736 .511149 .969723 .590493 .811047 .683054 .678817 .696277 .652371 .709507 .639148 .72273 .625926 .669838 .705263 .643398 .784601 .630175 .837493 .603729 .916831 .577283 1.009392 .56406 1.101953 .511168 1.287075 .471506 1.44575 .405391 1.604426 .418614 1.604426 .418614 1.61765 .405391 1.61765Z" fill="#231f20"/>
</g>
</g>
</g>
</svg>
