<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" version="1.1" width="66.46927" height="81.28125" viewBox="0 0 66.46927 81.28125">
<path transform="matrix(1,0,0,-1,39.171754,59.572267)" d="M0 .09269 .066115 1.586886 .026448 .833174 .052891 1.586886C.132229 1.586886 1.957004 1.547219 3.186742 3.398438L4.442926 2.57861C2.73716 .000127 .105784 .09269 0 .09269Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,38.972139,59.685548)" d="M.265723 .007219C.226054 .007219 .186384 .007219 .186384 .007219 .0806 .007219-.01196 .099777 .001263 .218783L.067378 1.712984C.067378 1.818768 .159939 1.898105 .265723 1.898105 .265723 1.898105 .265724 1.898105 .278947 1.898105 .278947 1.898105 .292172 1.898105 .305395 1.898105 .543408 1.898105 2.143388 1.964221 3.240896 3.630318 3.267342 3.669987 3.320234 3.709656 3.373126 3.709656 3.426018 3.72288 3.478911 3.709655 3.51858 3.683209L4.774764 2.863382C4.867325 2.797267 4.89377 2.678263 4.827655 2.585702 3.188004 .139449 .754974 .007219 .265723 .007219ZM.424401 .946048 .397953 .403908C1.019434 .430354 2.963213 .668368 4.364851 2.638593L3.439241 3.246852C2.394625 1.792323 .979764 1.554308 .450844 1.514639L.424401 .946048Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,41.353334,60.378908)" d="M1.203465 2.684468C1.203465 2.684468-.660979 4.535686 .251408 6.32079 1.163794 8.105894 3.596825 7.960441 4.15219 8.291016 4.15219 8.291016 3.79517 7.457967 3.424926 7.233176 3.424926 7.233176 5.302592 8.317462 7.021581 7.576975 7.021581 7.576975 6.995134 6.651362 6.598444 5.672861 6.598444 5.672861 6.823235 5.302618 7.325708 5.48774 7.312486 5.500962 5.024909 .026646 1.203465 2.684468Z" fill="#dd1f37"/>
<path transform="matrix(1,0,0,-1,41.14441,58.63086)" d="M3.46196 .011509C2.747919 .011509 2.020655 .262744 1.29339 .778441 1.280167 .791664 1.280166 .791666 1.266943 .804889 1.187605 .884227-.69006 2.788336 .275218 4.679224 .976037 6.067638 2.509904 6.358545 3.514852 6.543666 3.832203 6.609781 4.123109 6.662673 4.242116 6.728787 4.321454 6.768456 4.414018 6.768455 4.480133 6.70234 4.546247 6.636225 4.55947 6.55689 4.533024 6.477551 4.519801 6.437882 4.427238 6.239538 4.295007 6.014747 5.035495 6.265984 6.185897 6.503998 7.296628 6.027971 7.375966 6.001524 7.415635 5.922184 7.415635 5.842845 7.415635 5.803176 7.389188 4.943685 7.018945 3.978407 7.08506 3.925515 7.230515 3.859397 7.455306 3.938735 7.534644 3.965181 7.613978 3.951959 7.666871 3.885844 7.719762 3.832952 7.732988 3.740391 7.706542 3.674275 7.65365 3.555269 6.46358 .765219 4.321455 .130516 4.043772 .051178 3.752866 .011509 3.46196 .011509ZM1.531403 1.082569C2.443789 .447865 3.342954 .262744 4.202449 .513981 5.762762 .963563 6.807378 2.761891 7.177622 3.502378 6.886716 3.528824 6.688369 3.700723 6.609031 3.832953 6.582585 3.885845 6.569366 3.951958 6.595811 4.00485 6.886717 4.732115 6.979275 5.41971 7.005721 5.710616 5.432185 6.292427 3.726419 5.340373 3.713196 5.327149 3.620636 5.274258 3.501628 5.300704 3.448736 5.393265 3.395844 5.485826 3.422291 5.604833 3.514852 5.657724 3.647081 5.737062 3.818983 5.975078 3.951213 6.213092 3.845429 6.186646 3.713196 6.160199 3.580966 6.133753 2.655357 5.961854 1.240498 5.697394 .619017 4.480879-.16114 2.920566 1.38595 1.241244 1.531403 1.082569Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,16.653016,76.37695)" d="M3.728884 .316432C1.890889 4.375891 .634704 8.686587 0 13.089844 2.406585 11.833659 5.024737 10.617146 7.418098 9.360962L7.497437 9.493193C6.558605 6.557688 6.294144 3.410611 6.717279 .3561 5.818116 .025525 4.879285-.000918 3.927229 .07842L3.728884 .316432Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,16.415009,76.54883)" d="M5.011513-.000546C4.747054-.000546 4.456149 .012684 4.165243 .03913L4.085904 .05235 3.808222 .382924 3.794998 .409372C1.943779 4.482054 .687595 8.805972 .052891 13.235675L0 13.619141 .343798 13.434019C1.54709 12.812539 2.816496 12.177831 4.046235 11.569572 5.223081 10.987761 6.45282 10.379503 7.60322 9.784469L8.383376 10.974538 7.947017 9.586124C7.021408 6.690289 6.756949 3.556444 7.180085 .541602L7.206531 .382924 7.061077 .330029C6.439597 .105238 5.778447-.000546 5.011513-.000546ZM4.271027 .422592C5.262752 .343254 6.05613 .40937 6.743726 .647385 6.36026 3.569666 6.611495 6.584509 7.457767 9.401007 6.294144 10.009264 5.051183 10.617523 3.861113 11.212559 2.737159 11.767924 1.586759 12.336512 .48925 12.9051 1.137177 8.647296 2.353692 4.508495 4.125573 .594489L4.271027 .422592Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,15.010986,81.3418)" d="M.002363 5.958118C.028809 4.675488 .280047 3.339964 1.086649 2.295347 1.483339 1.779651 2.038705 1.33007 2.699855 1.197841 3.056876 1.131725 3.466787 1.303624 3.731247 1.4623 3.850254 1.528416 3.929592 1.594525 3.969261 1.647417 3.982484 1.660641 3.982485 1.660642 3.982485 1.673865 3.995708 1.673865 5.172554 .497016 6.825428 .298671 7.830376 .179664 9.033668 .444123 9.694818 1.158165 9.70804 1.158165 9.70804 1.158169 9.721263 1.144945 9.813824 1.1185 9.919607 1.092053 10.012169 1.078831 11.1229 .893708 12.326194 1.409404 13.040235 2.242452 13.833614 3.154839 14.124521 4.371354 14.137744 5.5482 14.150967 6.30191 14.058405 7.082068 13.688161 7.756441 13.331141 8.430814 12.656767 8.999406 11.863387 9.131636 11.215461 9.23742 10.488198 8.986181 10.104732 8.510154 10.025393 8.404369 9.946055 8.298586 9.906386 8.179579 9.906386 8.166355 9.893163 8.166354 9.893163 8.153131 8.993999 9.528322 7.394015 10.374596 5.648581 10.163028 5.013877 10.08369 4.405619 9.8589 3.863477 9.528325 3.61224 9.36965 3.427119 9.224196 3.25522 8.999406 3.189105 8.906845 2.779192 8.391149 2.85853 8.272141 2.85853 8.285364 2.845307 8.285357 2.845307 8.298581 2.779191 8.391142 2.686631 8.483706 2.580847 8.549821 2.237049 8.787834 1.747798 8.840723 1.324663 8.695271 .78252 8.510149 .38583 8.060574 .187485 7.544877 .028809 7.042403-.01086 6.487038 .002363 5.958118Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,14.812195,81.44531)" d="M7.500234 .163243C7.328335 .163243 7.16966 .176468 7.010984 .189691 5.675462 .348367 4.617622 1.128526 4.194486 1.49877 4.141594 1.459101 4.07548 1.419426 4.03581 1.39298 3.837466 1.273973 3.334993 .996296 2.858965 1.10208 2.224261 1.23431 1.60278 1.644221 1.126752 2.265702 .399488 3.204535 .029244 4.447493 .002798 6.047474-.010425 6.563171 .016021 7.158211 .240812 7.713576 .478826 8.321834 .941631 8.784638 1.496996 8.969761 1.973024 9.128436 2.52839 9.062315 2.92508 8.797855 2.951526 8.784632 2.977971 8.758191 3.004417 8.744968 3.070532 8.863975 3.176317 9.009426 3.295324 9.181325L3.32177 9.207772C3.493669 9.432564 3.692014 9.604458 3.982919 9.789579 4.538285 10.146601 5.186211 10.371391 5.84736 10.450729 7.487011 10.649074 9.086994 9.96148 10.091941 8.625957 10.118387 8.665627 10.144834 8.705294 10.17128 8.73174 10.594416 9.26066 11.387794 9.538344 12.115059 9.419337 12.921662 9.287107 13.662148 8.744968 14.085284 7.951589 14.415859 7.330108 14.574536 6.576397 14.561313 5.637565 14.534867 4.235928 14.138178 3.045858 13.410913 2.199587 12.60431 1.273977 11.32168 .771505 10.197725 .969851 10.131611 .983073 10.052273 .996291 9.972935 1.022738 9.338231 .41448 8.359729 .163243 7.500234 .163243ZM4.194486 1.961575C4.273824 1.961575 4.313494 1.921906 4.33994 1.89546 4.630846 1.631 5.675462 .745056 7.050653 .58638 7.936594 .480597 9.113439 .678941 9.748142 1.37976 9.801035 1.432652 9.893597 1.459101 9.959711 1.432655H9.972935C10.065495 1.406209 10.158057 1.379763 10.237395 1.36654 11.229119 1.194641 12.366297 1.644221 13.080338 2.464046 13.92661 3.442548 14.124954 4.738398 14.138178 5.650785 14.151401 6.510279 14.019169 7.211098 13.71504 7.766463 13.344796 8.454059 12.723316 8.930092 12.035721 9.035875 11.453909 9.128436 10.805984 8.903644 10.462185 8.480508 10.382848 8.387947 10.329955 8.295384 10.290286 8.189601 10.290286 8.176377 10.277062 8.176376 10.277062 8.163153 10.250616 8.097038 10.184502 8.044151 10.118386 8.044151 10.039049 8.030928 9.972935 8.070594 9.933266 8.123486 9.034102 9.5119 7.487012 10.239161 5.88703 10.054039 5.291995 9.974701 4.696961 9.77636 4.181264 9.445785 3.930027 9.287109 3.771352 9.141655 3.625899 8.956532L3.599451 8.930085C3.334991 8.586287 3.282099 8.454058 3.268877 8.401165 3.295322 8.321827 3.255655 8.229273 3.18954 8.176381 3.096979 8.123489 2.977971 8.14993 2.911856 8.229268L2.898634 8.242496C2.845742 8.321834 2.779627 8.387949 2.687066 8.440841 2.39616 8.639186 1.973024 8.692078 1.616003 8.573071 1.179645 8.427618 .796177 8.044145 .597832 7.541671 .399488 7.05242 .386265 6.497055 .386265 6.021027 .412711 4.513606 .756509 3.349984 1.430881 2.477266 1.854017 1.921901 2.382936 1.564878 2.938302 1.459094 3.282099 1.39298 3.705236 1.630997 3.837466 1.710335 3.943249 1.776449 4.009365 1.829338 4.03581 1.855784 4.062256 1.935122 4.128371 1.961575 4.194486 1.961575Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,15.02594,81.19531)" d="M.212168 7.397557C.410513 7.900031 .807203 8.362829 1.349345 8.547952 1.613805 8.640512 1.904711 8.640513 2.182394 8.574399 1.203893 7.87358 .820426 6.551281 .701419 5.466996 .701419 4.329818 1.005547 2.346371 2.367515 2.91496 2.817097 3.060412 2.975774 4.47527 3.874938 4.052134 4.932777 3.483545 5.091453 1.923234 6.453422 1.500098 8.119518 1.076962 8.569099 3.483552 9.785615 3.629004 11.00213 3.774457 11.147582 1.500098 12.50955 2.491822 13.30293 3.113303 13.805404 3.893459 14.109532 4.739731 14.003749 3.774453 13.686397 2.835623 13.051693 2.081913 12.337652 1.248864 11.13436 .719946 10.023629 .918291 9.931068 .931514 9.825282 .95796 9.732721 .984406 9.719499 .984406 9.719499 .984403 9.706276 .997626 9.045126 .283585 7.841835 .019124 6.836887 .138131 5.184013 .336476 4.007167 1.513326 3.993944 1.513326 3.993944 1.513326 3.98072 1.500101 3.98072 1.486878 3.941051 1.447209 3.861715 1.381091 3.742708 1.301753 3.478248 1.143077 3.055112 .957955 2.711314 1.037293 2.050164 1.169523 1.494798 1.619103 1.098108 2.1348 .291506 3.179416 .040269 4.51494 .013823 5.79757-.025846 6.339713 .013823 6.895083 .212168 7.397557Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,24.930634,75.94531)" d="M.370244 .172704C.370244 .000805 .66115 .014025 .674373 .185924 .700819 1.111533 .542143 1.984256 .198345 2.84375 .119007 2.751189 .039669 2.645402 0 2.526395 .277683 1.759461 .39669 .979306 .370244 .172704Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,17.419922,75.16211)" d="M0 .17251C0 .000611 .304129 .040273 .304129 .198949 .304129 .754315 .357021 1.309681 .462805 1.851823 .39669 1.944384 .304129 2.023729 .198345 2.089844 .066115 1.45514 0 .820436 0 .17251Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,17.826325,79.76172)" d="M.00356 2.103013C.215128 1.375748 .519256 .701376 .915945 .053449 1.034952 .119564 1.114291 .185678 1.15396 .225347 .783716 .820381 .492811 1.468308 .307689 2.14268 .26802 2.301356-.036109 2.274912 .00356 2.103013Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,24.745484,80.14844)" d="M0 .13743C.092561 .110984 .198344 .084538 .290905 .071315 .62148 .838249 .806603 1.658068 .991724 2.477893 1.031393 2.649792 .740488 2.715907 .700819 2.544008 .515697 1.724183 .330575 .904364 0 .13743Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,9.671265,51.072267)" d="M10.380049 .727724C6.704057 .000459 2.948727 1.124413 0 3.438437 .145453 4.297932 .436359 5.302881 .727265 6.122707 4.337142 5.382219 8.13214 5.474781 11.702349 6.400391 11.371774 4.535949 10.710624 2.605389 10.380049 .727724Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,9.459686,50.808595)" d="M8.343709 .053806C5.474319 .053806 2.565261 1.0852 .092562 3.02898L0 3.095095 .013224 3.214101C.132231 3.981034 .409913 4.972759 .753711 5.938038L.806603 6.096712 .978502 6.057044C4.561933 5.316556 8.330484 5.422341 11.861024 6.334728L12.165154 6.414062 12.112262 6.109935C11.940363 5.171103 11.689125 4.1926 11.437888 3.253768 11.199875 2.328159 10.935414 1.362881 10.776739 .424048L10.750293 .291819 10.618063 .265371C9.877576 .119918 9.110642 .053806 8.343709 .053806ZM.423136 3.253768C3.411533 .952968 7.034632 .000914 10.406495 .635617 10.578394 1.534781 10.816408 2.460391 11.054422 3.359555 11.26599 4.192603 11.49078 5.052094 11.662679 5.885143 8.211478 5.038871 4.561933 4.946312 1.071063 5.64713 .780157 4.787636 .542143 3.941364 .423136 3.253768Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,3.427124,49.42578)" d="M6.336712 1.480198C6.191259 1.295076 6.032583 1.136402 5.860684 .977726 5.014413 .250461 3.85079 .091786 2.779727 .19757 2.277253 .250462 1.77478 .382689 1.351644 .673594 .928508 .9645 .624379 1.427306 .624379 1.943002 .611156 2.445476 .822724 3.053733 1.311975 3.185963 1.007846 3.106625 .465703 3.622325 .307028 3.833893 .029345 4.190914-.063216 4.680162 .042568 5.116521 .307028 6.161137 1.24586 6.108246 2.118577 5.989239 1.854118 6.293368 1.708665 6.703279 1.748334 7.099969 1.788003 7.496659 2.012794 7.880126 2.356592 8.091694 2.92518 8.435492 3.692114 8.237146 4.194587 7.827234 4.697061 7.404099 5.093751 6.769395 5.384657 6.17436 5.53011 6.531381 5.596224 6.914846 5.702008 7.28509 5.807792 7.655334 5.953246 8.025578 6.244152 8.290039 6.521834 8.554499 6.971416 8.673507 7.315214 8.501608 7.725126 8.290039 7.844133 7.774343 7.883801 7.311538 7.989585 5.976016 7.69868 4.627272 7.301991 3.357864 7.0772 2.683492 6.773071 2.02234 6.336712 1.480198Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,3.215668,49.54883)" d="M3.559769 .096157C3.348202 .096157 3.149857 .109382 2.964735 .122604 2.356477 .175496 1.84078 .347393 1.444091 .625076 .941617 .982097 .637488 1.511015 .624265 2.053157 .611042 2.46307 .743272 2.939101 1.020955 3.230007 .703603 3.415129 .412697 3.732481 .346582 3.825042 .02923 4.234954-.076554 4.777097 .055676 5.279571 .320136 6.33741 1.2193 6.416745 1.920118 6.35063 1.787888 6.628313 1.721773 6.932444 1.748219 7.236572 1.787888 7.7126 2.065571 8.148959 2.449038 8.373751 3.136633 8.783663 3.996128 8.519201 4.511825 8.082843 4.948184 7.725822 5.278759 7.22335 5.529995 6.786991 5.556442 6.879552 5.582888 6.985333 5.59611 7.077895 5.622557 7.196901 5.649002 7.329134 5.688671 7.448141 5.781232 7.765492 5.939908 8.228296 6.283706 8.545648 6.65395 8.889445 7.182869 8.982009 7.579559 8.783664 7.976249 8.572096 8.20104 8.135736 8.253932 7.434917 8.346493 6.244848 8.161371 4.962217 7.658897 3.401904 7.40766 2.634971 7.090308 1.987043 6.680396 1.471346 6.521719 1.273002 6.34982 1.101103 6.177921 .942427 5.410988 .268054 4.406041 .096157 3.559769 .096157ZM1.576321 3.097777C1.206077 3.005216 1.020955 2.502739 1.034178 2.053157 1.047401 1.643245 1.298638 1.22011 1.682104 .942427 2.012679 .704413 2.462261 .558962 3.004404 .50607 3.863898 .426732 5.067191 .492846 5.939908 1.233335 6.098584 1.365564 6.25726 1.524241 6.38949 1.709363 6.759734 2.172168 7.063862 2.780422 7.301876 3.507687 7.777904 5.015108 7.963026 6.244849 7.870465 7.39525 7.830796 7.937392 7.67212 8.294412 7.40766 8.426641 7.129977 8.572094 6.772956 8.439866 6.561388 8.254745 6.349821 8.056399 6.204368 7.765491 6.085361 7.342355 6.058915 7.223348 6.019246 7.104343 5.9928 6.985336 5.926685 6.734099 5.873793 6.456417 5.768009 6.20518 5.741563 6.139065 5.675448 6.08617 5.59611 6.08617 5.516773 6.08617 5.450657 6.125842 5.410988 6.191957 5.172975 6.667984 4.789507 7.35558 4.273811 7.778716 3.863898 8.122514 3.189525 8.347303 2.660605 8.029952 2.382923 7.858053 2.184578 7.540703 2.144909 7.196905 2.118463 6.86633 2.23747 6.496084 2.46226 6.231624 2.515152 6.165509 2.528376 6.07295 2.488707 6.006835 2.449038 5.927497 2.3697 5.887826 2.290362 5.901049 1.272191 6.046502 .637488 5.967164 .439143 5.173784 .346582 4.803541 .42592 4.367183 .663934 4.063054 .862279 3.811817 1.325083 3.454798 1.470536 3.494467 1.57632 3.520913 1.682104 3.454797 1.70855 3.349013 1.748219 3.243229 1.682104 3.124223 1.576321 3.097777Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,6.498352,48.14258)" d="M2.987755 2.915359C3.000978 2.492223 2.895193 2.055863 2.736517 1.632727 2.604287 1.315375 2.445612 .984802 2.181152 .733565 1.916692 .482328 1.520002 .297205 1.123312 .323651 .660508 .350097 .290264 .667448 .131588 1.037692-.027088 1.407936-.013865 1.817852 .025804 2.214542 .065473 2.598009 .131588 2.981475 .31671 3.338496 .501832 3.695517 .819184 4.02609 1.242319 4.171543 2.339828 4.568233 2.961308 3.708738 2.987755 2.915359Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,6.494812,48.128908)" d="M.135168 1.030174C.293844 .65993 .664088 .342579 1.126893 .316133 1.510359 .289687 1.90705 .47481 2.184732 .726047 2.449192 .977283 2.621091 1.307857 2.740098 1.625208 2.898774 2.035121 3.017781 2.471482 2.991335 2.90784 2.964889 3.70122 2.343408 4.560715 1.232676 4.177248 .809541 4.031796 .492189 3.701218 .307067 3.344197 .121945 2.987177 .05583 2.60371 .016162 2.220243-.010284 1.81033-.023508 1.400418 .135168 1.030174ZM.135168 2.378921C.174837 2.709496 .227729 3.040069 .399628 3.344197 .558304 3.648326 .84921 3.939235 1.219454 4.058242 2.197955 4.388817 2.740097 3.661551 2.766543 2.973955 2.779766 2.603712 2.673983 2.233469 2.52853 1.876449 2.409523 1.598766 2.26407 1.32108 2.026056 1.096289 1.788042 .871498 1.444245 .712824 1.087224 .73927 .677311 .765716 .346736 1.043397 .201283 1.360749 .082276 1.6781 .095499 2.035123 .135168 2.378921Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,4.117615,49.333986)" d="M4.707386 1.534492C6.479267 3.425379 6.756949 6.043532 6.32059 8.476562 6.413151 8.46334 6.518935 8.436893 6.598273 8.397223 7.008186 8.185656 7.127193 7.669959 7.166862 7.207154 7.272646 5.871632 6.98174 4.522888 6.58505 3.25348 6.373482 2.605554 6.082577 1.944404 5.646218 1.389038 5.500765 1.203916 5.342089 1.045241 5.17019 .886566 4.323919 .159301 3.160295 .000626 2.089233 .10641 1.586759 .159302 1.084285 .291529 .66115 .582434 .357021 .794002 .119007 1.098131 0 1.441929 1.295853 .317974 3.371863 .225415 4.707386 1.534492Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,35.851106,54.76172)" d="M.226571 1.414753C3.690995-.000107 7.591777 .383361 10.923971 2.089127 10.950418 2.961844 10.844633 4.006461 10.725626 4.865955 7.036411 4.826286 3.333972 5.646111 .001778 7.246094-.024668 5.341983 .253017 3.318864 .226571 1.414753Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,35.661806,54.484376)" d="M4.898478 .066906C3.324942 .066906 1.777852 .357814 .336545 .952849L.217539 1.00574V1.13797C.230762 2.076802 .164649 3.081749 .111757 4.033804 .045642 4.999083-.020475 6.004032 .005971 6.96931V7.273438L.283654 7.141208C3.576179 5.554449 7.25217 4.734625 10.914939 4.787517H11.086841L11.113284 4.615615C11.27196 3.597445 11.338076 2.579276 11.324853 1.799119V1.680113L11.21907 1.627218C9.18273 .595825 7.014157 .066906 4.898478 .066906ZM.614229 1.2702C3.814193 .000793 7.556299 .238806 10.914939 1.931349 10.928162 2.618945 10.862048 3.504886 10.729818 4.390827 10.703372 4.390827 10.676925 4.390827 10.650479 4.390827 7.11994 4.390827 3.576181 5.17098 .376217 6.651955 .376217 5.805684 .429108 4.919746 .495223 4.060252 .561338 3.134643 .627452 2.182586 .614229 1.2702Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,16.036438,69.54297)" d="M5.786744 26.128907C2.71901 21.72565 .77523 16.555459 .153749 11.226592-.031373 9.692724-.097488 8.119188 .233087 6.598545 .550439 5.0779 1.30415 3.610149 2.520665 2.658094 3.406606 1.957275 4.490892 1.560587 5.575177 1.26968 10.309009 .000273 15.492421 .621754 19.856009 2.85644 21.350207 3.623373 22.844405 4.707657 23.26754 6.320862 23.664229 7.854729 22.989857 9.441486 22.408045 10.935685 21.006409 14.598454 20.199807 18.486016 20.04113 22.40002 15.294076 23.193402 10.282561 24.38347 5.786744 26.128907Z" fill="#ea2d2d"/>
<path transform="matrix(1,0,0,-1,18.475892,55.009767)" d="M.27954 1.350432C.345655 1.509108 .610115 1.442993 .544 1.284317 .41177 .953742 .319208 .596718 .266316 .239697 .23987 .067799-.02459 .147137 .001856 .305812 .054748 .662833 .14731 1.019857 .27954 1.350432Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,19.284485,56.183595)" d="M.145454 1.344709C.185123 1.516608 .449583 1.43727 .409914 1.278594 .330576 .934797 .290907 .604223 .277684 .247203 .264461 .075304 0 .075304 0 .247203 .013223 .617446 .052893 .987689 .145454 1.344709Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,18.014893,58.103517)" d="M.224792 1.715803C.264461 1.887702 .528921 1.808364 .489252 1.649688 .370245 1.186883 .290907 .724078 .277684 .24805 .277684 .076151 0 .076151 0 .24805 .013223 .750524 .092563 1.239775 .224792 1.715803Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,17.24759,56.328126)" d="M.198802 1.702131C.225248 1.87403 .489708 1.794692 .463262 1.636016 .383924 1.173212 .317808 .710407 .278139 .247602 .264916 .075703-.012765 .075703 .000458 .247602 .053349 .72363 .119464 1.212881 .198802 1.702131Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,17.13504,59.93164)" d="M.046894 1.484357C.07334 1.656256 .337799 1.656256 .324576 1.484357 .271684 1.061221 .258462 .638085 .311354 .214949 .324577 .04305 .060117 .04305 .033671 .214949-.019221 .638085-.005997 1.061221 .046894 1.484357Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,18.740296,59.689455)" d="M.160661 1.411116C.213553 1.583014 .478013 1.503677 .425121 1.345001 .306114 .98798 .266444 .617738 .279667 .247494 .279667 .075595 .015206 .075595 .001983 .247494-.01124 .644184 .041654 1.040872 .160661 1.411116Z" fill="#ba0404"/>
<path transform="matrix(1,0,0,-1,22.628876,66.00781)" d="M11.359511 9.637756C11.967769 7.918767 12.364456 6.001431 11.650414 4.322111 11.002488 2.814691 9.561183 1.770072 8.027315 1.188261 6.58601 .632895 4.933135 .421331 3.518274 1.003143 2.169529 1.571733 1.164583 2.801466 .635664 4.176658 .106744 5.538626-.012264 7.032825 .000959 8.500578 .014182 10.391466 .238974 12.322023 1.098468 14.001343 1.957963 15.680663 3.571167 17.069075 5.448832 17.26742 9.706635 17.717001 10.328117 12.546814 11.359511 9.637756Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,33.34674,57.871095)" d="M.271415 1.340883 .588766 .309491C.641658 .137592 .3772 .071477 .324308 .243376 .218524 .587174 .112739 .93097 .006955 1.274768-.045937 1.446667 .218523 1.512782 .271415 1.340883Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,33.84247,59.61328)" d="M.000458 1.113263C-.012765 1.285162 .264918 1.285162 .278141 1.113263 .304587 .809134 .291364 .518226 .278141 .214097 .264918 .042199-.012765 .042199 .000458 .214097 .026904 .518226 .026904 .822357 .000458 1.113263Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,32.799989,58.41797)" d="M.2495 1.092856C.368507 .828396 .434622 .550715 .447845 .259809 .461068 .087911 .183384 .087911 .170161 .259809 .156938 .497823 .104047 .735835 .011486 .947403-.054629 1.119302 .183385 1.251532 .2495 1.092856Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,32.410829,56.273439)" d="M.268501 1.143618C.347839 .865935 .427178 .588249 .506516 .310567 .559408 .138668 .294948 .072553 .242056 .244452 .162718 .522134 .083379 .79982 .004041 1.077502-.035627 1.249401 .228832 1.315516 .268501 1.143618Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,31.764954,57.064455)" d="M.266405 .866274C.292851 .681152 .345744 .496028 .411859 .310906 .464751 .152231 .213514 .072893 .147399 .244791 .081284 .429913 .041614 .615037 .001945 .800159-.024501 .972058 .226736 1.038173 .266405 .866274Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,32.183168,59.039064)" d="M.271415 .694399C.311084 .548946 .33753 .390271 .33753 .244818 .33753 .072919 .059846 .072919 .059846 .244818 .059846 .377048 .046624 .496054 .006955 .628284-.045937 .800183 .218523 .866298 .271415 .694399Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,32.923555,61.041017)" d="M.006945 .280929C.112729 .63795 .165621 .994973 .178844 1.365216 .178844 1.537115 .456528 1.537115 .456528 1.365216 .456528 .968527 .390413 .585057 .284629 .201591 .218514 .042915-.045947 .10903 .006945 .280929Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,33.269319,62.240236)" d="M.031465 .394336C.216587 .592681 .322371 .843921 .36204 1.108381 .388486 1.28028 .652946 1.200942 .6265 1.042266 .586831 .724914 .441378 .447229 .22981 .209215 .110803 .076985-.074319 .27533 .031465 .394336Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,32.487276,54.714845)" d="M.271415 1.315493C.363976 .984918 .443314 .654342 .509429 .310544 .535875 .138645 .284638 .07253 .244969 .244429 .178854 .588227 .099516 .918803 .006955 1.249377-.045937 1.421276 .218523 1.487391 .271415 1.315493Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,31.617432,55.089845)" d="M.268501 .821135C.30817 .649236 .347838 .477338 .387507 .305439 .427176 .13354 .162717 .067425 .123048 .239324L.004041 .75502C-.035627 .913696 .228832 .979811 .268501 .821135Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,22.628846,65.603519)" d="M.622535 3.768003C1.151455 2.406035 2.143179 1.163078 3.505148 .59449 4.933231-.000546 6.572881 .224241 8.014187 .779606 9.548054 1.374641 11.002582 2.406036 11.637285 3.913457 12.351327 5.592777 11.954639 7.510113 11.346382 9.229102 10.605895 11.344781 10.063752 14.650526 8.19931 16.131503 9.905076 14.690195 10.394325 11.569571 11.095144 9.559677 11.663733 7.920026 12.033976 6.068807 11.372826 4.468824 10.777791 3.014296 9.415825 2.02257 7.987741 1.467204 6.638996 .938284 5.10513 .726717 3.769608 1.295306 2.5002 1.837449 1.57459 3.027518 1.08534 4.336595 .596089 5.645671 .477082 7.086976 .490305 8.488613 .503528 10.300163 .715096 12.151381 1.521699 13.764586 2.328301 15.377791 3.835722 16.700093 5.581157 16.885214 5.620826 16.885214 5.660494 16.898438 5.700163 16.898438 5.620825 16.898438 5.541489 16.885214 5.462151 16.885214 3.584486 16.686869 1.97128 15.298453 1.111785 13.619133 .239068 11.939814 .014278 9.996033 .001055 8.118367-.012168 6.637392 .093615 5.143195 .622535 3.768003Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,28.328919,49.484376)" d="M0 .76784C1.004947 .820732 1.798327 .543047 2.446254 .067019 1.81155 .569493 1.01817 .847178 0 .76784Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,17.287446,71.40625)" d="M4.615101 21.752196C9.467939 25.44141 15.286056 23.06127 19.37196 19.147263 19.424853 18.829913 19.490967 18.525784 19.557082 18.221653 15.206717 22.043099 8.529107 23.55052 4.615101 17.957196 2.168847 14.294425 1.494475 9.692823 4.747331 6.294514 7.193584 3.583801 11.120813 3.173889 14.24144 4.668087 17.362067 6.162287 18.036438 9.679601 18.856265 12.800227 19.160393 13.712614 19.543858 14.704338 20.284346 15.378711 20.548806 14.505993 20.839713 13.646499 21.157065 12.800227 21.725653 11.319252 22.413249 9.732493 22.01656 8.185404 21.593424 6.558975 20.086005 5.474693 18.605029 4.720984 14.241441 2.486298 9.058026 1.851593 4.324195 3.134224 3.23991 3.425129 2.155624 3.821819 1.269684 4.522638 1.097785 4.826767 .952332 5.144117 .833325 5.487915-.806326 11.187024-.264183 18.089426 4.615101 21.752196Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,16.03479,66.87109)" d="M.15537 8.554717C.776851 13.883583 2.733853 19.053776 5.788364 23.457032 10.284182 21.711596 15.295696 20.521527 20.04275 19.728146 20.108866 18.009158 20.293986 16.30339 20.611338 14.62407 16.538657 18.538077 10.707318 20.918217 5.85448 17.229 .975195 13.566232 .433053 6.650602 2.059481 .964716 2.178488 .620918 2.323941 .303568 2.49584-.000561 1.279325 .964718 .538837 2.432472 .221485 3.939894-.095867 5.447315-.029752 7.020849 .15537 8.554717Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,11.588623,70.13867)" d="M4.561933 12.443359 0 4.549231C0 4.549231 5.540434 2.116202 5.990016 1.997195L5.910677 1.732736C5.910677 1.732736 11.861024 .278206 13.315553 .278206V.000519C13.315553 .000519 19.186562 .040189 20.879105 .622001V.926128C20.879105 .926128 25.480706 2.393885 26.538546 3.055035L26.406315 3.583955C26.406315 3.583955 28.54844 4.549231 30.320322 6.850031L26.525322 12.31113C26.538544 12.31113 13.500675 9.283064 4.561933 12.443359Z" fill="#c60021"/>
<path transform="matrix(1,0,0,-1,11.40802,70.353519)" d="M13.509362 .017545C13.45647 .017545 13.403578 .043994 13.363909 .070439 13.32424 .110108 13.311017 .162997 13.311017 .215889V.308452C11.592029 .414236 6.289607 1.710089 6.051593 1.762981 5.998701 1.776204 5.959033 1.815875 5.932587 1.855544 5.906141 1.895212 5.892917 1.961329 5.919363 2.014221L5.945808 2.093555C4.887969 2.503469 .326038 4.500144 .11447 4.592705 .061578 4.619151 .021909 4.658815 .008686 4.711707-.004537 4.764599-.004538 4.817492 .021908 4.870385L4.570618 12.764513C4.62351 12.843851 4.716071 12.883521 4.808632 12.857076 13.601922 9.749672 26.547234 12.6984 26.679465 12.724846 26.7588 12.738069 26.838137 12.711621 26.891028 12.645507L30.686028 7.184409C30.73892 7.118293 30.73892 7.012511 30.686028 6.946396 29.15216 4.962947 27.367058 3.971226 26.85136 3.693542L26.943924 3.323293C26.97037 3.243955 26.930699 3.151397 26.85136 3.111728 25.87286 2.50347 22.07786 1.260506 21.28448 1.009269V.837372C21.28448 .758034 21.231589 .6787 21.15225 .652254 19.420039 .05722 13.747376 .017545 13.509362 .017545ZM6.342498 2.093555C7.387115 1.842319 12.239954 .691922 13.496139 .691922 13.601922 .691922 13.694483 .599361 13.694483 .493577V.414234C14.69943 .427457 19.24814 .506802 20.861346 .982829V1.154727C20.861346 1.247288 20.914237 1.313397 20.993575 1.339844 21.033244 1.353066 25.264603 2.701816 26.48112 3.376187L26.388556 3.759658C26.36211 3.852219 26.415 3.944773 26.494339 3.984442 26.520785 3.997665 28.543906 4.923277 30.236449 7.065403L26.600125 12.301708C25.21171 11.99758 13.205232 9.524879 4.795408 12.420714L.431821 4.857165C2.097918 4.1299 5.853249 2.490244 6.197046 2.397683 6.249938 2.38446 6.289607 2.344797 6.316052 2.305129 6.342498 2.26546 6.342499 2.19934 6.329277 2.159671L6.342498 2.093555Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,11.905945,69.6875)" d="M13.20977 .012372V.303279C11.490782 .409063 6.18836 1.704916 5.950346 1.757809L5.752002 1.810704 5.83134 2.088383C4.773501 2.498296 .211568 4.494971 0 4.587532L.158677 4.944554C1.692544 4.270182 5.765225 2.498294 6.122246 2.39251L6.307367 2.339623 6.241252 2.088383C7.285869 1.837146 12.138707 .686749 13.394892 .686749H13.593237V.409061C14.598184 .422285 19.146893 .501629 20.760098 .977657V1.281784L20.892329 1.321451C20.931998 1.334674 25.163357 2.683423 26.379872 3.357795L26.247643 3.899935 26.393092 3.96605C26.419539 3.979273 28.50877 4.944553 30.22776 7.166016L30.545115 6.927999C29.011248 4.944551 27.226143 3.952825 26.710448 3.675142L26.842677 3.159451 26.723668 3.080108C25.745165 2.47185 21.950167 1.228894 21.156788 .977657V.673522L21.024558 .633854C19.318793 .038819 13.646129-.000848 13.394892-.000848L13.20977 .012372Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,12.434906,68.76172)" d="M13.20977 .012372V.303279C11.490782 .409063 6.18836 1.704916 5.950346 1.757809L5.752002 1.810704 5.83134 2.088383C4.773501 2.498296 .211568 4.494971 0 4.587532L.158677 4.944554C1.692544 4.270182 5.765225 2.498294 6.122246 2.39251L6.307367 2.339623 6.241252 2.088383C7.285869 1.837146 12.138707 .686749 13.394892 .686749H13.593237V.409061C14.598184 .422285 19.146893 .501629 20.760098 .977657V1.281784L20.892329 1.321451C20.931998 1.334674 25.163357 2.683423 26.379872 3.357795L26.247643 3.899935 26.393092 3.96605C26.419539 3.979273 28.50877 4.944553 30.22776 7.166016L30.545115 6.927999C29.011248 4.944551 27.226143 3.952825 26.710448 3.675142L26.842677 3.159451 26.723668 3.080108C25.745165 2.47185 21.950167 1.228894 21.156788 .977657V.673522L21.024558 .633854C19.318793 .038819 13.646129-.000848 13.394892-.000848L13.20977 .012372Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,15.568756,62.228517)" d="M23.536927 2.988065C21.884053 2.631044 9.097419-.000333 0 3.093847L.833048 4.535156C9.67923 1.401307 22.57165 4.402926 22.57165 4.402926L23.536927 2.988065Z" fill="#a30000"/>
<path transform="matrix(1,0,0,-1,15.67453,52.458986)" d="M0 8.476562C0 8.476562 .370244 1.428707 0 .727888L.357021 .846898C.357021 .846898 .687596 .000625 .357021 8.410447L0 8.476562Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,22.40503,51.109376)" d="M0 4.972656 3.834668 .780966 6.241252 2.856977 3.596653 .00081 0 4.972656Z" fill="#a30000"/>
<path transform="matrix(1,0,0,-1,28.73883,52.0625)" d="M6.518934 2.737687 1.176846 .80713 0 3.755859 1.031394 .000526 6.518934 2.737687Z" fill="#a30000"/>
<path transform="matrix(1,0,0,-1,28.130555,49.66797)" d="M.330575 .00096C.513146 .00096 .66115 .148963 .66115 .331535 .66115 .514106 .513146 .662109 .330575 .662109 .148003 .662109 0 .514106 0 .331535 0 .148963 .148003 .00096 .330575 .00096Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,27.86612,50.859376)" d="M.330575 .00096C.513146 .00096 .66115 .148963 .66115 .331535 .66115 .514106 .513146 .662109 .330575 .662109 .148003 .662109 0 .514106 0 .331535 0 .148963 .148003 .00096 .330575 .00096Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,45.967897,55.04297)" d="M.648479 2.071924C.754263 1.87358 .886491 1.675235 1.018721 1.490113 1.706316 .617395 2.817046 .247153 3.901332 .128147 4.403805 .075254 4.932726 .114923 5.382308 .313268 5.845113 .524836 6.241801 .921524 6.347585 1.410775 6.453369 1.900025 6.36081 2.547954 5.911228 2.759521 6.188911 2.627292 6.823614 3.02398 7.021958 3.195879 7.352533 3.500007 7.550881 3.94959 7.524435 4.399172 7.45832 5.470234 6.53271 5.602464 5.659992 5.655355 5.977344 5.906592 6.202134 6.276838 6.241803 6.673528 6.281472 7.070217 6.136018 7.493351 5.831889 7.757811 5.342638 8.194169 4.562482 8.167725 3.98067 7.850373 3.398859 7.533021 2.896387 6.977655 2.499698 6.461959 2.42036 6.845426 2.433581 7.22889 2.407135 7.612357 2.380689 7.995824 2.301351 8.392516 2.07656 8.709867 1.851769 9.02722 1.441856 9.225565 1.071612 9.119781 .635253 8.987552 .410463 8.498301 .291456 8.061942-.065565 6.766088-.052341 5.390896 .106335 4.068597 .159227 3.394224 .331127 2.693405 .648479 2.071924Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,45.733247,55.210939)" d="M4.532677 .084231C4.400447 .084231 4.254996 .097452 4.109543 .110675 3.183934 .203236 1.901301 .520591 1.094699 1.538761 .949246 1.723883 .817016 1.935452 .698009 2.147019 .39388 2.715608 .195535 3.416424 .102974 4.223027-.082148 5.849455-.016029 7.14531 .288099 8.295711 .473221 8.970083 .777349 9.36677 1.200485 9.499 1.623621 9.618007 2.126093 9.419664 2.430222 9.009751 2.707905 8.626285 2.774022 8.137034 2.800468 7.819682 2.813691 7.687452 2.813689 7.568446 2.826911 7.436216 2.826911 7.330432 2.840135 7.237869 2.840135 7.132085 3.17071 7.515552 3.593846 7.951912 4.09632 8.216372 4.678131 8.533724 5.590516 8.639509 6.185551 8.097366 6.529348 7.793237 6.701248 7.317209 6.661579 6.841182 6.635133 6.537053 6.516126 6.246145 6.331005 6.008131 7.0186 5.928793 7.89132 5.690782 7.957435 4.593273 7.983881 4.077577 7.772311 3.5751 7.388844 3.231303 7.309506 3.151965 6.965709 2.900729 6.608688 2.781722 6.833479 2.437924 6.859923 1.948675 6.780585 1.551985 6.661579 1.023066 6.264892 .560257 5.709526 .30902 5.365728 .163567 4.982258 .084231 4.532677 .084231ZM1.055031 2.33214C1.160815 2.133796 1.279824 1.948672 1.412054 1.776773 2.126095 .864387 3.302939 .573483 4.149211 .494145 4.691353 .441253 5.154158 .494143 5.537625 .666042 5.973984 .864388 6.304558 1.23463 6.383895 1.63132 6.476457 2.067678 6.397119 2.596598 6.053321 2.755274 5.96076 2.808166 5.90787 2.91395 5.960763 3.019734 6.013654 3.125518 6.119438 3.165188 6.225222 3.112297 6.357452 3.046181 6.873147 3.310642 7.124384 3.52221 7.41529 3.78667 7.587191 4.183359 7.560745 4.566825 7.521076 5.386651 6.912817 5.584997 5.881423 5.637889 5.802085 5.637889 5.722748 5.704004 5.696302 5.770119 5.669857 5.836234 5.696302 5.928795 5.762417 5.981688 6.0401 6.206478 6.225221 6.523828 6.26489 6.867625 6.304558 7.211423 6.172328 7.568444 5.921091 7.793235 5.471509 8.203148 4.757469 8.110586 4.294664 7.85935 3.69963 7.541998 3.197158 6.933743 2.866583 6.510607 2.813691 6.444492 2.734353 6.418046 2.668238 6.444492 2.5889 6.457715 2.536007 6.523828 2.522784 6.603166 2.469892 6.880848 2.456669 7.145309 2.443445 7.409769 2.443445 7.528776 2.430222 7.661004 2.430222 7.780011 2.390553 8.229592 2.297993 8.546945 2.126095 8.771735 1.954196 9.009749 1.623621 9.194871 1.332715 9.10231 1.055032 9.022972 .830242 8.705621 .684789 8.176701 .38066 7.079192 .327765 5.82301 .499664 4.249475 .605448 3.508987 .790571 2.86106 1.055031 2.33214Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,47.35092,53.66992)" d="M.058698 3.318285C-.033863 2.895149-.00742 2.458788 .071918 2.022429 .138033 1.678632 .230596 1.334834 .442164 1.030705 .653732 .726576 1.01075 .475338 1.394217 .422446 1.857022 .369555 2.28016 .607572 2.504951 .938146 2.729742 1.268721 2.795857 1.678631 2.835526 2.07532 2.875195 2.458787 2.888418 2.855479 2.769411 3.238946 2.663627 3.622413 2.412389 4.00588 2.015699 4.23067 1.023975 4.812482 .24382 4.098442 .058698 3.318285Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,47.364259,53.683595)" d="M2.822305 2.088992C2.861974 2.472459 2.875197 2.869151 2.75619 3.252618 2.650406 3.636085 2.399168 4.019552 2.015702 4.244342 1.010754 4.826154 .230595 4.112113 .058696 3.331957-.033865 2.908821-.007418 2.47246 .07192 2.036101 .138035 1.692303 .230598 1.348505 .442166 1.044377 .653734 .740248 1.010752 .48901 1.394219 .436118 1.857024 .370003 2.280159 .621243 2.50495 .951818 2.729741 1.282393 2.795859 1.692302 2.822305 2.088992ZM2.478506 1.282393C2.280161 .991487 1.909918 .79314 1.500006 .846032 1.156208 .898924 .838853 1.110492 .653731 1.374952 .455386 1.639411 .376051 1.94354 .309936 2.234446 .230598 2.617913 .204152 3.001379 .296712 3.3584 .455388 4.032773 1.129762 4.641033 2.028925 4.125336 2.372723 3.926991 2.597511 3.596416 2.703295 3.265842 2.809079 2.935267 2.795856 2.591468 2.76941 2.260894 2.729741 1.917096 2.676851 1.573299 2.478506 1.282393Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,45.941316,54.97461)" d="M1.626979 1.965277C.238565 4.160294 .476579 6.791668 1.36252 9.092469 1.269958 9.105692 1.164177 9.092467 1.071616 9.066022 .635257 8.933791 .410463 8.444541 .291456 8.008183-.065565 6.712329-.052341 5.33714 .106335 4.014841 .185673 3.340468 .344352 2.626425 .674926 2.004945 .78071 1.8066 .912939 1.608255 1.045169 1.423134 1.732764 .550416 2.843494 .180174 3.927779 .061167 4.430253 .008275 4.959174 .047943 5.408756 .246287 5.739331 .391741 6.043461 .656201 6.228583 .973553 4.747608 .114058 2.684819 .418187 1.626979 1.965277Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,15.000122,59.466798)" d="M25.361699 4.601989 22.135289 5.752386 23.391472 1.798713C23.391472 1.798713 20.998114 .211955 12.231269 .106171 3.477648 .000387 .714042 1.997057 .714042 1.997057L2.644599 8.31765 0 7.53749C.304129 8.648222 0 15.603516 0 15.603516 0 15.603516 4.720609 14.757244 5.011514 14.611792 5.117298 14.558899 5.381758 14.360556 5.963569 14.043204L6.360259 14.810136 21.474139 12.945698 27.133578 10.155645 25.361699 4.601989Z" fill="#c60021"/>
<path transform="matrix(1,0,0,-1,14.801788,59.566408)" d="M11.768464 .007257C3.570209 .007257 .912386 1.858477 .806602 1.937815 .740487 1.990706 .714042 2.070043 .727265 2.162603L2.552037 8.126173 .26446 7.451799C.198345 7.425354 .119007 7.451799 .066115 7.504691 .013223 7.557583 -0 7.623698 .013223 7.703035 .251237 8.549307 .105784 13.309585 0 15.702947 0 15.769061 .026446 15.821955 .066115 15.861624 .105784 15.901293 .171899 15.914515 .238014 15.901291 1.348746 15.702947 5.011514 15.0418 5.30242 14.883123 5.328866 14.8699 5.39498 14.830229 5.461095 14.777337 5.593325 14.697999 5.791672 14.56577 6.082578 14.407095L6.386705 14.988906C6.426373 15.068243 6.505712 15.107915 6.58505 15.094692L21.69893 13.230249C21.725378 13.230249 21.7386 13.217026 21.765048 13.217026L27.424487 10.426973C27.517046 10.387304 27.556714 10.281522 27.530268 10.188961L25.745163 4.635304C25.731942 4.582413 25.692275 4.542745 25.639382 4.516298 25.586489 4.489852 25.533596 4.489852 25.480704 4.503075L22.63776 5.521242 23.774939 1.951035C23.801384 1.871696 23.774939 1.779139 23.6956 1.726246 23.589816 1.660131 21.143567 .113041 12.429614 .007257 12.218046 .007257 11.993256 .007257 11.768464 .007257ZM1.150401 2.189051C1.758659 1.83203 4.826393 .311386 12.429614 .403947 19.927052 .496508 22.743548 1.686577 23.351805 1.990706L22.135289 5.785703C22.108843 5.851817 22.135289 5.931155 22.188179 5.984047 22.241072 6.03694 22.32041 6.050164 22.386525 6.023719L25.414589 4.939432 27.080687 10.13607 21.606369 12.833559 6.664388 14.684778 6.333814 14.036852C6.307368 13.98396 6.267699 13.957513 6.214808 13.94429 6.161915 13.931067 6.109022 13.931067 6.05613 13.957514 5.646217 14.182304 5.394981 14.340981 5.236305 14.433542 5.17019 14.473211 5.130521 14.499654 5.104075 14.512877 4.866061 14.618661 2.195017 15.121135 .39669 15.438486 .449582 14.274863 .621481 9.448474 .436359 7.888161L2.763606 8.588978C2.82972 8.615423 2.909059 8.588979 2.961951 8.536087 3.014843 8.483194 3.028065 8.403856 3.014842 8.337742L1.150401 2.189051Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,31.805146,76.33203)" d="M2.117005 15.138278C2.394687 16.883713 2.81782 18.827494 4.312018 19.766325 5.224405 20.348137 6.454143 20.401026 7.419422 19.911776 8.3847 19.422524 9.059075 18.391135 9.125191 17.306849 9.878901 18.060559 10.764841 18.7746 11.82268 18.933276 12.814404 19.078728 13.872241 18.615921 14.427608 17.796097 14.982973 16.963047 15.009418 15.812647 14.493722 14.953154 16.001143 14.860592 17.019314 13.313503 17.00609 11.806083 16.992869 10.298661 16.146597 8.92347 15.181319 7.773069 11.954909 3.938402 5.118623 .156622 1.138502 3.621046-1.74411 6.159862 1.707092 12.546572 2.117005 15.138278Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,31.605713,74.43555)" d="M5.172603 .163374C3.625512 .163374 2.303214 .639404 1.218929 1.578236-1.042203 3.548462 .346212 7.660815 1.364382 10.662435 1.721403 11.707051 2.012309 12.606211 2.118093 13.254138 2.408998 15.039242 2.845357 17.035916 4.405671 18.02764 5.384172 18.64912 6.680025 18.702013 7.711419 18.186316 8.623805 17.72351 9.284952 16.824349 9.470075 15.819402 10.131224 16.440882 10.964273 17.075585 11.995667 17.234259 13.06673 17.392935 14.190685 16.903687 14.798943 16.004523 15.341085 15.184697 15.420424 14.10041 15.010511 13.21447 16.517933 12.936788 17.430317 11.350029 17.417094 9.908723 17.403872 8.586423 16.795613 7.237675 15.552651 5.7567 13.463418 3.270777 9.509746 .480728 5.794085 .189821 5.582517 .1766 5.384171 .163374 5.172603 .163374ZM2.514784 13.201246C2.409 12.513651 2.091647 11.601264 1.734626 10.543425 .756125 7.647589-.579399 3.667468 1.483388 1.882364 2.620566 .89064 4.061874 .454283 5.754417 .586512 9.364294 .864195 13.198959 3.588132 15.2353 6.007939 16.412146 7.409577 16.993958 8.692207 17.00718 9.908723 17.020403 11.257467 16.081574 12.764891 14.679936 12.844229 14.613822 12.844229 14.547706 12.883896 14.521259 12.95001 14.494813 13.016126 14.494813 13.082241 14.521259 13.148355 14.997287 13.928513 14.970841 15.012796 14.455144 15.77973 13.939447 16.546664 12.960944 16.983023 12.048558 16.83757 10.990719 16.678894 10.104781 15.911961 9.456855 15.264034 9.403963 15.211142 9.324624 15.184698 9.245286 15.224367 9.165948 15.250813 9.126279 15.316926 9.113056 15.396264 9.046941 16.414435 8.42546 17.379714 7.513074 17.842518 6.600687 18.305323 5.463509 18.25243 4.590792 17.710288 3.202377 16.811125 2.792467 14.907013 2.514784 13.201246Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,34.165315,72.703128)" d="M9.899033 8.57283C10.599852 7.052186 10.004818 5.214192 8.986648 3.905116 7.386665 1.855553 4.742065 .678701 2.150359 .8506 1.290864 .903492 .285918 1.247296 .047904 2.080345-.110772 2.648933 .15369 3.243967 .41815 3.772886 1.211529 5.372869 2.004909 6.986068 2.798289 8.58605 3.274316 9.538106 3.79001 10.556281 4.742066 11.045532 6.540393 11.984365 9.132099 10.212481 9.899033 8.57283Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,45.39032,66.46484)" d="M2.297096 .984491C2.164866 .693585 1.979746 .429126 1.728509 .257227 1.464049 .085328 1.107027 .045659 .842567 .191112 .538438 .349788 .392987 .693584 .287203 1.024159 .168197 1.394403 .088859 1.777869 .035967 2.148113-.016925 2.518357-.03015 2.901825 .128526 3.2324 .326871 3.642313 .802898 3.893551 1.25248 3.840659 2.561556 3.66876 2.707009 1.910101 2.297096 .984491Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,42.093996,61.953126)" d="M2.565363 3.242961C2.975276 3.097508 3.345522 2.819824 3.530644 2.436357 3.755435 1.973553 3.662872 1.39174 3.358743 .968604 3.054614 .545469 2.552141 .294232 2.049667 .24134 1.481078 .175225 .872822 .347127 .476132 .75704 .066219 1.153729-.105681 1.788432 .066218 2.330575 .396793 3.335522 1.692646 3.54709 2.565363 3.242961Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,36.172365,60.79492)" d="M3.263875 2.665129C3.59445 1.990757 3.316769 1.091593 2.695288 .68168 2.073807 .258544 1.174645 .337883 .592833 .827134 .301927 1.065148 .090357 1.395724 .024242 1.765967-.055096 2.17588 .063914 2.612237 .328373 2.929589 1.015969 3.749414 2.774624 3.6833 3.263875 2.665129Z" fill="#ffcaca"/>
<path transform="matrix(1,0,0,-1,34.16519,72.72656)" d="M.048026 2.08606C.28604 1.253012 1.277764 .922439 2.150481 .856323 4.742188 .684424 7.386786 1.861267 8.973545 3.910831 10.004939 5.23313 10.58675 7.057909 9.885931 8.578554 9.118998 10.231428 6.527292 11.990083 4.728965 11.038027 3.790132 10.548777 3.261215 9.530609 2.785187 8.578554 1.991808 6.978572 1.198428 5.365364 .405048 3.765382 .153812 3.249685-.11065 2.654649 .048026 2.08606ZM1.251319 4.241406C1.965361 5.682712 2.679404 7.137248 3.393445 8.578554 3.816581 9.438048 4.292607 10.350433 5.138878 10.800014 6.765306 11.646286 9.105777 10.059529 9.793372 8.578554 10.428076 7.216586 9.885931 5.563705 8.960321 4.373636 7.519015 2.53564 5.138877 1.46458 2.811631 1.623255 2.031474 1.676147 1.132312 1.980279 .920744 2.73399 .775291 3.236464 1.013305 3.765378 1.251319 4.241406Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,45.39032,66.47656)" d="M.035967 2.159568C.088859 1.776102 .168197 1.392635 .287203 1.035614 .392987 .705039 .538438 .361243 .842567 .202567 1.12025 .057114 1.464049 .110006 1.728509 .268682 1.992969 .440581 2.178089 .705041 2.297096 .995947 2.468995 1.379413 2.548336 1.895115 2.482221 2.397589 2.52189 1.987676 2.455773 1.564535 2.323544 1.260406 2.21776 1.022393 2.059082 .797605 1.834291 .652152 1.6095 .506699 1.318597 .467027 1.080583 .599257 .829346 .731487 .710339 1.022391 .617778 1.300074 .525217 1.604203 .459102 1.934779 .40621 2.252131 .366541 2.55626 .353318 2.886838 .485548 3.164521 .657447 3.508318 1.054134 3.719885 1.437601 3.680216 1.953298 3.614101 2.244205 3.25708 2.389658 2.807498 2.230983 3.349641 1.873961 3.786 1.25248 3.878561 .802898 3.944676 .326871 3.680216 .128526 3.270303-.03015 2.926505-.016925 2.529812 .035967 2.159568Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,42.082704,61.953126)" d="M.474183 .75704C.884096 .36035 1.47913 .175225 2.047719 .24134 2.563415 .294232 3.052665 .558692 3.356794 .968604 3.660923 1.39174 3.740263 1.973553 3.528695 2.436357 3.343574 2.819824 2.973327 3.097508 2.563415 3.242961 1.690697 3.54709 .394845 3.335522 .06427 2.330575-.107629 1.788432 .077494 1.153729 .474183 .75704ZM.579966 2.581811C.897317 3.322299 1.968382 3.414861 2.669201 3.137178 2.999776 3.004948 3.290679 2.780158 3.422909 2.476029 3.594808 2.105785 3.502248 1.669423 3.237788 1.365294 2.973328 1.061165 2.550193 .902492 2.127057 .876046 1.664253 .862823 1.175 1.021499 .844425 1.352074 .527074 1.695872 .408067 2.185121 .579966 2.581811Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,36.170778,60.808595)" d="M.025768 1.75442C.091883 1.384176 .303453 1.053601 .594359 .815587 1.17617 .339559 2.075333 .260224 2.696814 .670137 3.318294 1.093273 3.595976 1.979213 3.265401 2.653586 2.77615 3.671757 1.017494 3.737871 .316675 2.904822 .052215 2.600693-.05357 2.164333 .025768 1.75442ZM.594359 2.944489C1.17617 3.658531 2.683591 3.605638 3.093503 2.732921 3.371186 2.151109 3.133171 1.3974 2.604251 1.040379 2.075331 .683359 1.308398 .749473 .819147 1.159385 .581133 1.35773 .396014 1.635413 .329899 1.952765 .277007 2.296563 .369568 2.666807 .594359 2.944489Z" fill-opacity=".15"/>
<path transform="matrix(1,0,0,-1,37.04608,58.820314)" d="M1.028182 1.235514C1.358756 1.209068 1.68933 1.063614 1.927343 .8256 2.072796 .69337 2.17858 .481803 2.112464 .309904 2.033127 .124782 1.808336 .058667 1.596769 .045444 1.16041 .018998 .644712 .085113 .261246 .309904-.095775 .521472-.082551 .891715 .274469 1.09006 .49926 1.209067 .776945 1.26196 1.028182 1.235514Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,38.940065,66.60547)" d="M2.08283 4.207376C2.717534 4.009032 3.272902 3.625563 3.775376 3.189204 4.172066 2.832184 4.529086 2.409045 4.687762 1.893349 4.846437 1.377652 4.740652 .769397 4.330739 .4256 4.172063 .280147 3.947272 .187589 3.735704 .214035 3.484468 .240481 3.286124 .412374 3.087779 .571049 2.281176 1.245422 1.487795 1.933021 .681192 2.607394 .284503 2.951191-.297307 3.612343 .17872 4.154485 .588633 4.590844 1.620026 4.352829 2.08283 4.207376Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,42.97406,60.566408)" d="M1.949665 1.216953C2.095118 1.124392 2.240572 1.005383 2.293464 .846707 2.372802 .569024 2.148009 .278118 1.870327 .185557 1.592644 .106219 1.288516 .172335 1.024056 .264896 .759596 .344234-.430473 .926046 .164561 1.269844 .667035 1.573973 1.460415 1.507859 1.949665 1.216953Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,46.44449,65.765628)" d="M1.057839 1.779359C1.163623 1.329777 1.097509 .827305 .872718 .417392 .806603 .298386 .714041 .179384 .581811 .166161 .476027 .152937 .370246 .219049 .304131 .31161 .238016 .404171 .211567 .509953 .185121 .615737 .079337 1.065319 .013223 1.541347 0 2.004152 0 2.255388 .052893 2.916538 .476029 2.731416 .79338 2.585963 .991724 2.070265 1.057839 1.779359Z" fill="#ffffff" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,32.256135,54.757814)" d="M2.69749 2.499078C2.684267 2.578415 2.631374 2.631308 2.552036 2.644531H2.538812 .158676 .145452C.066114 2.644531 .013223 2.578415 0 2.499078V2.485858 1.785037C.198345 1.956936 .449581 2.049497 .740486 2.06272H.75371C.793379 2.06272 .833049 2.06272 .859495 2.06272 .846272 2.075943 .84627 2.075945 .833047 2.089168L.819825 2.102388C.806602 2.102388 .806603 2.115612 .79338 2.115612 .780157 2.115612 .780155 2.128835 .766932 2.128835 .753709 2.142058 .740488 2.14206 .727265 2.155283L.714041 2.168503C.714041 2.168503 .700817 2.168504 .700817 2.181726 .687594 2.181726 .674371 2.19495 .674371 2.19495 .647925 2.208173 .621481 2.221395 .595035 2.234618H.608256 .62148 .634702C.647925 2.234618 .661148 2.247841 .674371 2.247841H.687595 .700817 .714041 .727265C.740488 2.247841 .740487 2.247841 .75371 2.247841 .766933 2.247841 .766933 2.247841 .780156 2.247841 .806602 2.247841 .819825 2.261065 .846271 2.261065 .899163 2.274288 .965278 2.274289 1.01817 2.274289 1.123954 2.274289 1.216514 2.261065 1.309075 2.247841 1.361967 2.234618 1.401637 2.221397 1.454529 2.208174 1.494198 2.194951 1.520643 2.181726 1.560311 2.168503 1.573534 2.15528 1.586759 2.155281 1.586759 2.128835 1.586759 2.115613 1.586759 2.102391 1.586759 2.089168 1.586759 2.023053 1.599983 1.970158 1.599983 1.904043 1.599983 1.89082 1.613204 1.864375 1.63965 1.851152 1.718988 1.785037 1.811548 1.732146 1.877663 1.666031 1.904109 1.639585 1.917334 1.626362 1.917334 1.61314 1.917334 1.599916 1.917333 1.586694 1.90411 1.547024V1.533801C1.970225 1.494132 2.03634 1.441239 2.102455 1.388347 2.115678 1.375124 2.11568 1.375126 2.128903 1.361903 2.142126 1.34868 2.155347 1.335455 2.16857 1.322232 2.181793 1.309009 2.195014 1.295788 2.208237 1.282565 2.22146 1.269342 2.221462 1.26934 2.234685 1.256117 2.261131 1.229671 2.287577 1.203228 2.3008 1.163558H2.287576 2.274352 2.261132C2.234687 1.163558 2.20824 1.150334 2.181794 1.137111 2.181794 1.137111 2.16857 1.13711 2.16857 1.123887 2.155347 1.110664 2.142126 1.110667 2.128903 1.097444 2.128903 1.097444 2.128903 1.097443 2.128903 1.08422H2.142122C2.195014 1.070997 2.234686 1.018106 2.247909 .965214 2.247909 .938768 2.247908 .912321 2.234685 .885875 2.234685 .885875 2.234684 .872651 2.221461 .872651 2.181792 .819759 2.128903 .793311 2.062788 .780089 2.049565 .780089 2.049563 .780089 2.03634 .780089 1.996671 .780089 1.94378 .780089 1.917334 .793312 1.904111 .793312 1.890885 .806536 1.877663 .806536 1.890885 .806536 1.904111 .806536 1.917334 .806536 1.957003 .806536 1.99667 .819761 2.023116 .832984 2.036339 .832984 2.036341 .846203 2.049564 .846203 2.062787 .859426 2.076007 .859428 2.076007 .872651 2.062784 .872651 2.049563 .872651 2.03634 .872651 2.03634 .872651 2.036339 .872651 2.023116 .872651 1.983447 .872651 1.957003 .872652 1.917334 .885875 1.851219 .912321 1.798325 .96521 1.758656 1.044548L1.745433 1.057772C1.73221 1.057772 1.732212 1.057773 1.718989 1.070996 1.705766 1.070996 1.705764 1.070996 1.692541 1.070996 1.666095 1.070996 1.639649 1.070995 1.613203 1.057772 1.533865 1.044549 1.454529 1.018103 1.388414 .991657 1.361968 .978434 1.348744 .978437 1.335521 .965214 1.322298 .965214 1.309075 .95199 1.309075 .95199 1.203292 1.057774 1.11073 1.176782 1.031392 1.295788 1.031392 1.269342 1.031392 1.256119 1.031392 1.229674V1.21645C1.031392 1.203227 1.031392 1.190004 1.031392 1.163558 1.031392 1.150336 1.031392 1.150334 1.031392 1.137111 1.031392 1.123888 1.031392 1.097443 1.031392 1.08422V1.070996C1.031392 1.057773 1.031392 1.044548 1.031392 1.044548 1.031392 1.031325 1.031392 1.018104 1.031392 1.004881 1.031392 .991658 1.031392 .978437 1.031392 .965214 1.031392 .965214 1.031392 .965213 1.031392 .95199 1.031392 .938767 1.031393 .925541 1.044616 .912318 1.044616 .899095 1.044616 .885874 1.057839 .872651 1.057839 .859428 1.071061 .846207 1.071061 .832984 1.084284 .793315 1.097508 .753643 1.110731 .727197 1.123954 .700751 1.137177 .674305 1.1504 .647859 1.295853 .34373 1.573535 .105717 1.90411-.000067H2.525592C2.60493-.000067 2.684266 .066049 2.684266 .15861V2.446186C2.697489 2.485855 2.69749 2.485855 2.69749 2.499078ZM1.917334 .42307C1.771881 .475962 1.613204 .608193 1.50742 .700754 1.599981 .647862 1.785102 .594967 1.890886 .594967 1.970224 .594967 2.03634 .581745 2.102455 .608191H2.115679C2.07601 .528853 1.996672 .475962 1.917334 .42307Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,32.256105,53.56836)" d="M.502472 .410604C.489249 .410604 .489249 .410604 .476027 .410604H.462805C.449582 .410604 .436358 .410604 .423135 .410604 .409912 .410604 .396689 .410604 .383466 .397381 .370243 .397381 .35702 .39738 .343797 .384157 .330574 .384157 .31735 .38416 .304127 .370937 .290904 .370937 .277683 .357713 .26446 .357713 .251237 .357713 .251235 .357712 .238012 .344489 .171897 .318043 .119006 .291597 .052891 .265151 .039668 .265151 .026446 .251927 .026446 .251927 .013223 .251927 0 .238706 0 .225483V.000691C.039669 .04036 .066113 .093252 .105782 .132921 .119005 .146144 .132229 .159369 .145452 .172592 .277682 .291599 .423136 .370936 .595035 .423828H.581811C.555365 .423828 .528918 .410604 .502472 .410604Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,33.684206,53.44922)" d="M.290907 .238729 .304131 .251953C.304131 .251953 .290907 .251953 .277684 .251953 .251238 .251953 .22479 .251953 .185121 .251953 .119006 .251953 .052893 .238729 .013224 .238729H0C.013223 .238729 .013225 .225506 .026448 .225506 .145455 .172614 .238014 .093275 .330575 .013937L.343799 .000713C.343799 .000713 .343799 .000714 .343799 .013937 .343799 .02716 .343798 .040382 .330575 .066828 .317352 .093274 .30413 .119721 .290907 .146167 .277684 .15939 .277683 .159391 .26446 .172614L.290907 .238729Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,33.062715,54.73047)" d="M0 .594154C0 .382586 .039669 .184241 .105784-.000881H.35702C.171898 .263579 .052892 .594151 .039669 .951172 .013223 .845388 0 .71316 0 .594154Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,32.256105,54.70703)" d="M.581811 1.164062C.581811 1.164062 .58181 1.150839 .568587 1.150839 .555364 1.124393 .542141 1.11117 .542141 1.084724 .528919 1.071501 .528919 1.045056 .515696 1.031833 .515696 1.01861 .502472 1.018608 .502472 1.005385 .502472 .992162 .48925 .978941 .48925 .965718 .48925 .952495 .489249 .952493 .476027 .93927V.926046C.476027 .912823 .462805 .899602 .462805 .886379 .449582 .859933 .44958 .833486 .436357 .80704 .409911 .688033 .39669 .555804 .39669 .423574 .39669 .278121 .409912 .132667 .449581 .000437H.145452C.132229 .000437 .119006 .000437 .119006 .000437 .052891 .01366 0 .079775 0 .14589V.331012C.013223 .304566 .026445 .278123 .052891 .251677 .079337 .212008 .105783 .18556 .145452 .159114 .145452 .18556 .145452 .212007 .145452 .238453 .145452 .251676 .145452 .264897 .145452 .27812 .145452 .648364 .317351 .952495 .581811 1.164062Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,26.543824,50.59375)" d="M.39669-.000411C.615775-.000411 .79338 .177193 .79338 .396279 .79338 .615365 .615775 .792969 .39669 .792969 .177604 .792969 0 .615365 0 .396279 0 .177193 .177604-.000411 .39669-.000411Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,26.411622,52.177736)" d="M.39669-.000411C.615775-.000411 .79338 .177193 .79338 .396279 .79338 .615365 .615775 .792969 .39669 .792969 .177604 .792969 0 .615365 0 .396279 0 .177193 .177604-.000411 .39669-.000411Z" fill="#fdfcf5"/>
<path transform="matrix(1,0,0,-1,.163971,56.17383)" d="M61.804265 21.541207C63.086896 20.985844 64.05218 20.708157 64.05218 20.708157 62.478639 17.746205 60.4423 17.799099 58.908433 18.433804 58.881986 17.918107 58.802648 17.442078 58.710088 16.979275 60.680315 12.800808 59.371236 8.926472 59.371236 8.926472 59.318346 10.989258 57.559686 12.311558 56.660524 12.86692 56.594408 12.90659 56.541517 12.933037 56.488626 12.972706 56.277059 13.091713 56.131605 13.157829 56.131605 13.157829 56.21094 12.985931 56.277059 12.800808 56.34317 12.62891 56.3035 12.549572 56.263837 12.47023 56.224168 12.390892 56.224168 12.390892 56.277055 12.443787 56.38284 12.549572 58.392736 6.876907 54.227495 2.949677 54.227495 2.949677 55.536569 5.395931 54.3465 8.106644 54.3465 8.106644 53.222547 2.262081 48.36971 .926563 48.36971 .926563 50.28704 3.663719 48.303594 6.480217 48.303594 6.480217 47.920129 2.500099 45.976346 2.235638 45.976346 2.235638 45.976346 2.235638 47.245756 5.369488 44.072237 5.29015 40.978059 5.210812 40.528474 7.524834 40.50203 7.643841 40.396245 6.533108 41.506975 5.395931 41.506975 5.395931 37.394624 5.726505 36.5748 8.238876 36.5748 8.238876 35.56985 7.181034 36.733476 4.219086 36.733476 4.219086 33.599626 4.404209 33.255826 6.400879 33.255826 6.400879 32.264104 5.713284 31.563284 3.386036 31.563284 3.386036 31.563284 3.386036 31.10048 3.253803 29.262482 4.708336 27.411265 6.189308 26.77656 7.643841 26.77656 7.643841 25.67905 6.255424 23.72205 6.824013 23.72205 6.824013 24.145186 7.604168 23.788165 10.07687 23.788165 10.07687 22.412974 6.876907 17.53369 7.59095 17.53369 7.59095 17.53369 7.59095 18.155169 7.776073 19.252678 9.323162 20.363409 10.870251 19.384907 11.30661 19.384907 11.30661 17.784926 8.397552 15.047765 9.944641 15.047765 9.944641 16.541964 10.010754 17.348568 11.610737 17.348568 11.610737 17.348568 11.610737 15.404785 10.460339 13.593236 10.156208 11.768462 9.852081 11.027976 7.405827 11.027976 7.405827 8.555276 10.32811 11.107313 14.13633 11.107313 14.13633 8.899074 11.994205 9.018081 7.842186 9.018081 7.842186 3.318971 13.131382 6.611496 18.52636 6.611496 18.52636 4.7735 18.513138 3.332194 15.537968 3.332194 15.537968 .872718 21.554429 6.016461 25.40232 7.140416 26.15603 6.71728 25.904795 6.241252 25.785788 5.752002 25.759343 5.474319 25.322983 5.315642 25.005629 5.315642 25.005629 5.209858 25.27009 5.117298 25.52133 5.03796 25.78579 2.604929 26.037026 0 28.099812 0 28.099812 1.639651 27.213871 4.561933 28.152705 4.561933 28.152705 1.428083 27.729569 .766934 31.061762 .753711 31.154324 .780157 31.114655 1.599982 29.739462 3.609877 29.580786 3.980121 29.55434 4.390034 29.580786 4.826392 29.660124 4.892507 29.9907 4.985068 30.30805 5.090852 30.612179 4.707385 30.493172 4.469371 30.400612 4.469371 30.400612 4.469371 30.400612 4.429702 31.286553 6.201583 32.76753 7.285869 34.301397 8.634614 35.29312 9.137088 35.636919 6.426374 34.050157 2.009895 37.567476 2.009895 37.567476 3.649546 36.681535 6.571827 37.620367 6.571827 37.620367 3.437978 37.197229 2.776828 40.529424 2.763605 40.621988 2.790051 40.582315 3.609877 39.207124 5.619772 39.048448 7.642889 38.87655 11.252767 40.714548 11.252767 40.714548 9.494108 41.0319 6.492489 39.868276 6.492489 39.868276 6.492489 39.868276 6.439597 40.99223 9.004858 42.830228 10.300711 43.755838 11.940362 44.020296 13.236216 44.04674 12.178376 44.56244 11.530449 45.514493 11.530449 45.514493 11.530449 45.514493 14.598184 44.509546 16.09238 46.334318 16.568409 46.91613 17.256007 47.57728 17.970047 48.19876 17.692366 49.692956 16.356842 51.47806 16.356842 51.47806 16.92543 51.46484 17.467574 51.411947 17.983269 51.319387 18.895657 51.16071 19.728707 50.89625 20.442748 50.59212 20.54853 50.552453 20.654314 50.49956 20.760098 50.459894 22.571648 49.640066 23.642712 48.621896 23.68238 48.582227 23.51048 49.66651 21.275797 51.306165 21.275797 51.306165 21.275797 51.306165 22.941893 52.61524 27.014575 52.231775 30.83602 51.874753 30.75668 50.01031 30.743458 49.79874 31.2856 54.149106 35.2525 54.74414 35.2525 54.74414 32.700464 52.747468 34.37978 51.06815 34.37978 51.06815 37.791315 55.66975 42.921834 53.091268 42.921834 53.091268 40.17145 53.06482 38.611139 50.737577 38.558248 50.67146 45.619325 54.400346 49.308538 49.957418 49.308538 49.957418 45.39453 50.67146 43.477205 48.55578 43.34497 48.397104 43.463979 48.502889 44.733388 49.547506 48.435827 48.87313 52.323385 48.172315 53.44734 45.421934 53.44734 45.421934 52.812635 45.60705 52.601068 44.28475 52.601068 44.28475 54.544847 45.1178 56.356397 44.97235 57.625806 44.641778 58.776205 44.35087 59.490243 43.901288 59.490243 43.901288 59.490243 43.901288 57.057214 42.962457 56.50185 40.50298 56.475404 40.383974 56.435735 40.264966 56.409288 40.145959 57.427457 36.588975 62.901777 35.980714 62.901777 35.980714 60.429079 34.380735 57.784479 35.703035 57.70514 35.7427 59.278676 32.212163 66.075298 30.189045 66.075298 30.189045 63.311689 24.979186 59.053884 29.091536 58.762979 29.38244 59.596029 28.456831 60.217508 27.504778 60.680315 26.565945 60.706758 26.526276 60.719984 26.486607 60.746427 26.433716 61.55303 24.635388 61.791044 22.929623 61.804265 21.541207Z" fill="#dd1f37"/>
<path transform="matrix(1,0,0,-1,0,56.382814)" d="M48.507226 .923141C48.44111 .923141 48.388219 .949589 48.34855 1.002483 48.295656 1.068596 48.295656 1.161156 48.34855 1.227268 49.710515 3.184273 49.022924 5.154499 48.560117 6.053661 48.031198 2.523125 46.219648 2.232216 46.14031 2.218994 46.074197 2.205772 45.994859 2.23222 45.95519 2.298332 45.91552 2.364449 45.902296 2.430565 45.92874 2.496677 45.92874 2.509903 46.470884 3.858646 45.915517 4.665249 45.62461 5.088383 45.0428 5.299953 44.222978 5.273506 42.9139 5.233837 42.054407 5.604084 41.485818 6.080109 41.657716 5.85532 41.789949 5.723087 41.789949 5.709866 41.84284 5.656971 41.85606 5.564415 41.829614 5.485077 41.789945 5.405739 41.710607 5.36607 41.631269 5.36607 38.312299 5.630527 37.069337 7.283401 36.68587 8.010666 36.23629 7.071835 36.791654 5.180946 37.069337 4.466904 37.095785 4.400787 37.082559 4.334675 37.04289 4.281784 37.00322 4.228889 36.937108 4.202442 36.87099 4.202442 34.3454 4.347897 33.5388 5.643749 33.300786 6.238785 32.480958 5.432182 31.899146 3.528072 31.899146 3.501625 31.885924 3.435513 31.83303 3.382618 31.766917 3.369396 31.634686 3.329727 31.0661 3.316502 29.280995 4.731361 27.866135 5.868542 27.152092 6.979275 26.874409 7.468525 25.68434 6.278454 23.886013 6.794151 23.806675 6.807373 23.753783 6.820599 23.700892 6.860264 23.674445 6.92638 23.647999 6.992496 23.647999 7.045387 23.687668 7.098278 23.925683 7.561081 23.886013 8.764378 23.806675 9.597427 22.100909 6.952827 17.843108 7.547859 17.644762 7.574306 17.5522 7.587528 17.486086 7.666866 17.472863 7.759426 17.459639 7.85199 17.525753 7.931328 17.618315 7.957771 17.618315 7.957771 18.200127 8.182564 19.23152 9.610645 19.733994 10.311466 19.760439 10.734604 19.707547 10.972618 19.6811 11.065178 19.641434 11.144512 19.588541 11.197407 19.086068 10.377579 18.451363 9.861885 17.68443 9.637093 16.36213 9.253628 15.145616 9.927998 15.092724 9.954445 15.013386 9.994114 14.973716 10.086678 15.000162 10.179237 15.026608 10.271797 15.092723 10.324692 15.185284 10.324692 15.925772 10.351139 16.49436 10.813942 16.851382 11.223854 16.110894 10.853611 14.920825 10.337914 13.770424 10.15279 12.091105 9.875107 11.377062 7.57431 11.363839 7.547863 11.337393 7.481747 11.284501 7.428856 11.218387 7.415634 11.152271 7.402409 11.072933 7.428856 11.033264 7.481747 9.684519 9.08173 9.77708 10.932945 10.17377 12.334583 9.327498 10.337914 9.367167 8.076782 9.367167 8.050335 9.367167 7.970997 9.327498 7.891659 9.24816 7.865215 9.168822 7.838768 9.089484 7.85199 9.036592 7.904881 6.034973 10.681709 5.5325 13.511433 5.638284 15.389095 5.717622 16.85685 6.167204 17.967579 6.418441 18.496499 4.897797 18.139477 3.681281 15.693226 3.668058 15.666779 3.628389 15.600666 3.562274 15.547771 3.482936 15.560993 3.403598 15.560993 3.337483 15.613888 3.311038 15.680004 1.552379 19.9907 3.628389 23.23033 5.413493 25.028656 5.373824 25.055104 5.334156 25.08155 5.320932 25.134444 5.228372 25.359234 5.149033 25.584025 5.082918 25.795594 2.66311 26.099722 .177188 28.056723 .071404 28.136059-.007934 28.202176-.021157 28.321183 .031735 28.40052 .084627 28.479859 .203634 28.519526 .296195 28.466635 1.15569 28.00383 2.398651 28.069947 3.337483 28.228623 1.24825 28.79721 .745777 31.283133 .745777 31.309578 .732554 31.40214 .785446 31.494702 .878007 31.53437 .970568 31.57404 1.076352 31.534369 1.116021 31.441809 1.129244 31.428586 1.9094 30.119509 3.813511 29.960833 4.104417 29.934388 4.461438 29.960835 4.858128 30.013726 4.897797 30.172402 4.937465 30.317856 4.977134 30.47653 4.831681 30.42364 4.73912 30.397192 4.73912 30.383969 4.673006 30.357523 4.60689 30.370747 4.553998 30.397192 4.501106 30.436861 4.461438 30.489752 4.461438 30.555869 4.461438 30.661652 4.474661 31.57404 6.246542 33.055017 6.894468 33.98062 7.634956 34.694665 8.243214 35.21036 5.545723 34.84012 2.239975 37.431825 2.081299 37.564058 2.001961 37.63017 1.988738 37.749177 2.04163 37.828516 2.094522 37.907854 2.213529 37.94752 2.30609 37.89463 3.165585 37.431825 4.408545 37.49794 5.347378 37.656618 3.258145 38.225206 2.768895 40.71113 2.755672 40.73757 2.742449 40.83013 2.795341 40.92269 2.887902 40.962366 2.980463 41.00203 3.086246 40.962366 3.125915 40.8698 3.139138 40.85658 3.919295 39.5475 5.823406 39.38883 7.251489 39.26982 9.539066 40.19543 10.663021 40.71113 8.983701 40.671457 6.762239 39.83841 6.749015 39.825189 6.6829 39.79874 6.616786 39.811964 6.563893 39.83841 6.511002 39.878076 6.471332 39.93097 6.471332 39.997087 6.471332 40.049978 6.444886 41.25327 9.063038 43.130937 10.213438 43.95076 11.601853 44.25489 12.739031 44.34745 11.998543 44.863145 11.548962 45.49785 11.535738 45.537519 11.482846 45.603635 11.496069 45.696195 11.535738 45.76231 11.588631 45.828424 11.68119 45.85487 11.760529 45.828424 11.786975 45.8152 14.722479 44.88959 16.110894 46.582136 16.520806 47.08461 17.129063 47.692865 17.922444 48.393686 17.618315 49.808546 16.375353 51.46142 16.362132 51.474645 16.322463 51.540758 16.309239 51.620096 16.348908 51.68621 16.388577 51.752325 16.45469 51.791994 16.534029 51.791994 17.089394 51.77877 17.64476 51.72588 18.200127 51.633317 19.046399 51.487867 19.89267 51.23663 20.712496 50.89283 20.81828 50.83994 20.937285 50.80027 21.029847 50.747377 22.008349 50.31102 22.775282 49.808546 23.277756 49.42508 22.735613 50.152345 21.770335 50.9325 21.333975 51.24985 21.281085 51.28952 21.254637 51.34241 21.254637 51.408529 21.254637 51.474645 21.281085 51.527536 21.333975 51.567205 21.400092 51.620096 23.105856 52.915948 27.204983 52.532484 28.884305 52.373807 30.047928 51.924226 30.656186 51.170515 30.748747 51.051507 30.828083 50.94572 30.880976 50.83994 31.83303 54.47626 35.350347 55.04485 35.390016 55.04485 35.482576 55.05807 35.561918 55.00518 35.601587 54.925844 35.64125 54.846506 35.614809 54.740724 35.53547 54.687829 34.689199 54.02668 34.239614 53.339086 34.199949 52.65149 34.1735 52.12257 34.385068 51.712656 34.543745 51.487867 38.021394 55.89112 43.12547 53.391977 43.17836 53.36553 43.257699 53.32586 43.297369 53.2333 43.28414 53.14074 43.257699 53.04818 43.17836 52.982065 43.099023 52.982065 41.393259 52.96884 40.163515 52.030008 39.489145 51.34241 42.781667 52.83661 45.26759 52.532484 46.827905 51.977117 48.665899 51.315969 49.59151 50.205236 49.63118 50.165567 49.68407 50.09945 49.697298 50.00689 49.6444 49.940774 49.604734 49.87466 49.525396 49.83499 49.432836 49.848215 47.29071 50.23168 45.78329 49.768877 44.831235 49.253179 45.651063 49.42508 46.880796 49.47797 48.626234 49.160619 52.56668 48.446577 53.74353 45.696195 53.79642 45.59041 53.82287 45.524297 53.809648 45.444959 53.756757 45.378847 53.70386 45.32595 53.624525 45.299509 53.55841 45.32595 53.492296 45.339174 53.452627 45.339174 53.39973 45.299505 53.25428 45.206945 53.135274 44.955709 53.055936 44.704469 54.960046 45.41851 56.692258 45.23339 57.85588 44.92926 59.019506 44.638357 59.746767 44.188776 59.786436 44.17555 59.85255 44.13588 59.89222 44.069768 59.878999 43.99043 59.865777 43.91109 59.826108 43.844976 59.74677 43.818529 59.72032 43.805307 57.406297 42.879697 56.877378 40.565675 56.850934 40.45989 56.82449 40.354108 56.798044 40.24832 57.80299 36.902906 63.052518 36.281427 63.105409 36.281427 63.184747 36.2682 63.250864 36.21531 63.27731 36.13597 63.29053 36.056634 63.264085 35.964075 63.19797 35.924406 61.280637 34.681444 59.25752 35.14425 58.318689 35.4616 60.19635 32.340974 66.25248 30.502976 66.318599 30.489752 66.37149 30.47653 66.42438 30.436861 66.45083 30.370747 66.47727 30.317854 66.47727 30.25174 66.43761 30.198847 65.59133 28.598866 64.52027 27.699704 63.250864 27.501358 62.08724 27.329459 60.963285 27.792263 60.14346 28.307959 60.487257 27.805485 60.791387 27.289788 61.055845 26.747647 61.08229 26.707978 61.095514 26.655085 61.12196 26.615416 61.822778 25.121218 62.193025 23.508015 62.206247 21.815472 63.40954 21.312996 64.3087 21.035317 64.32192 21.035317 64.37482 21.02209 64.42771 20.982422 64.454158 20.916306 64.4806 20.863415 64.4806 20.797299 64.44093 20.744408 62.867395 17.78246 60.817834 17.729565 59.297189 18.284932 59.27074 17.901463 59.204626 17.517998 59.125288 17.134533 61.069067 12.956066 59.826108 9.147842 59.773218 8.98917 59.74677 8.896606 59.65421 8.843716 59.561647 8.856937 59.469087 8.870163 59.389749 8.949497 59.389749 9.042061 59.35008 10.972618 57.72365 12.228802 56.771596 12.810612L56.731927 12.837059C56.731927 12.823837 56.731927 12.823833 56.745149 12.810612 56.745149 12.79739 56.758375 12.784168 56.758375 12.757721 56.771596 12.744499 56.771596 12.731274 56.78482 12.718048 58.807939 6.992493 54.616247 2.946262 54.576578 2.906593 54.510465 2.840477 54.391458 2.840477 54.32534 2.893368 54.246004 2.946259 54.21956 3.065266 54.27245 3.144604 55.14517 4.77103 54.854265 6.556137 54.603025 7.481747 53.227834 2.166103 48.69235 .857029 48.639455 .843803 48.546895 .923141 48.533674 .923141 48.507226 .923141ZM48.44111 6.873489C48.507226 6.873489 48.56012 6.847042 48.59979 6.794151 48.61301 6.767704 50.411336 4.175999 48.983255 1.518177 50.252664 2.060322 53.412958 3.845421 54.285676 8.354462 54.298898 8.4338 54.378236 8.499916 54.457574 8.513142 54.550134 8.526363 54.62947 8.473473 54.65592 8.394135 54.695589 8.301571 55.55508 6.318123 54.986494 4.18922 56.03111 5.60408 57.64431 8.605701 56.427797 12.4007 56.361684 12.387474 56.295568 12.387478 56.242677 12.427147 56.163339 12.480038 56.136896 12.585823 56.189786 12.678383 56.21623 12.731274 56.242677 12.784168 56.269124 12.837059 56.216234 12.995735 56.150114 13.141186 56.09722 13.273418 56.070775 13.352757 56.084 13.432095 56.136896 13.498211 56.189786 13.551102 56.282346 13.564323 56.361684 13.537876 56.361684 13.537876 56.507139 13.471764 56.731927 13.339531 56.771596 13.313087 56.811265 13.28664 56.86416 13.260193L56.91705 13.233749C57.776544 12.718052 59.11207 11.686657 59.57487 10.15279 59.826108 11.541203 60.037675 14.265141 58.702154 17.09486 58.68893 17.13453 58.675706 17.174199 58.68893 17.227093 58.794713 17.689896 58.86083 18.165925 58.887277 18.641953 58.887277 18.708069 58.926946 18.760956 58.979837 18.800625 59.03273 18.840294 59.098844 18.840298 59.16496 18.81385 61.135187 17.9808 62.735166 18.655175 63.938459 20.784077 63.568216 20.903084 62.814504 21.167546 61.902116 21.55101 61.822778 21.577458 61.783109 21.656796 61.783109 21.736134 61.783109 23.428675 61.426088 25.028658 60.72527 26.509634 60.712049 26.549304 60.6856 26.58897 60.67238 26.62864 60.19635 27.620364 59.561647 28.545973 58.794713 29.405468 58.728597 29.484807 58.728597 29.603814 58.807935 29.683152 58.88727 29.76249 59.00628 29.76249 59.085618 29.683152 59.297189 29.471585 61.17485 27.660035 63.184747 27.964163 64.269039 28.122839 65.20786 28.889773 65.96158 30.238516 64.731838 30.635207 59.151735 32.59221 57.71043 35.83184 57.67076 35.91118 57.697206 36.003744 57.750097 36.056634 57.81621 36.109529 57.89555 36.12275 57.974889 36.096303 58.080675 36.04341 60.288915 34.972349 62.48393 36.056634 61.0294 36.32109 57.247625 37.299596 56.401353 40.28799 56.38813 40.32766 56.38813 40.36733 56.401353 40.406999 56.44102 40.526006 56.467466 40.63179 56.49391 40.750795 56.943494 42.707799 58.503808 43.739194 59.23107 44.122659 58.91372 44.268114 58.411249 44.492906 57.76332 44.65158 56.599699 44.955709 54.80137 45.127607 52.85759 44.294557 52.791475 44.268114 52.712137 44.281335 52.659246 44.321004 52.60635 44.360673 52.579908 44.44001 52.579908 44.506128 52.59313 44.598688 52.751806 45.45818 53.188165 45.735864 53.227834 45.76231 53.2675 45.77553 53.30717 45.788759 52.91048 46.47635 51.601408 48.314348 48.57334 48.869714 44.98991 49.51764 43.773397 48.55236 43.641168 48.433355 43.56183 48.367238 43.44282 48.367238 43.376707 48.433355 43.297369 48.51269 43.297369 48.618478 43.363485 48.697816 43.44282 48.790376 45.227926 50.83994 48.903917 50.4168 48.480779 50.78705 47.72707 51.32919 46.66923 51.712656 44.368429 52.532484 41.657716 52.16224 38.82799 50.668039 38.748655 50.628374 38.64287 50.641595 38.58998 50.707708 38.523866 50.773824 38.523866 50.87961 38.576757 50.945726 38.64287 51.038286 39.93873 52.955617 42.25275 53.378755 41.72383 53.55065 40.956895 53.735776 40.08418 53.775445 37.88916 53.854784 36.090837 52.96884 34.715646 51.117624 34.675977 51.077955 34.623086 51.038286 34.57019 51.038286 34.5173 51.038286 34.451185 51.051507 34.411516 51.091176 34.385068 51.117624 33.750368 51.752325 33.81648 52.730829 33.856149 53.339086 34.147054 53.93412 34.675977 54.50271 33.525575 54.092794 31.47601 52.955617 31.105767 49.940774 31.092543 49.83499 30.999983 49.755655 30.880976 49.768877 30.775193 49.782098 30.695855 49.87466 30.709076 49.980443 30.7223 50.05978 30.735524 50.50936 30.352057 50.972169 29.96859 51.434976 29.109097 52.016786 27.165316 52.188684 24.057913 52.479589 22.36537 51.76555 21.79678 51.448198 22.391816 50.98539 23.899237 49.715986 24.057913 48.763929 24.071137 48.68459 24.031466 48.59203 23.952128 48.55236 23.872792 48.51269 23.78023 48.525915 23.727339 48.578809 23.68767 48.618478 22.629828 49.62342 20.857947 50.430025 20.752163 50.482919 20.64638 50.522588 20.540596 50.562257 19.747218 50.89283 18.940613 51.130846 18.120789 51.2763 17.724099 51.34241 17.314187 51.395307 16.917498 51.408529 17.32741 50.81349 18.120789 49.53086 18.332358 48.380464 18.34558 48.314348 18.319134 48.24823 18.266242 48.19534 17.459639 47.481298 16.838158 46.859819 16.415022 46.357347 15.251399 44.942483 13.241504 45.127607 12.236556 45.32595 12.527462 45.02182 12.950598 44.638357 13.492741 44.373895 13.572079 44.334226 13.62497 44.241666 13.598525 44.149106 13.585302 44.05654 13.505963 43.99043 13.413403 43.99043 12.24978 43.96398 10.596906 43.73919 9.301052 42.813585 7.648178 41.636735 7.119259 40.76402 6.947361 40.32766 7.806855 40.61857 10.015094 41.319387 11.469624 41.054925 11.548962 41.041704 11.615076 40.975587 11.628299 40.883028 11.641521 40.803689 11.601853 40.71113 11.522515 40.671457 11.377063 40.592119 7.846524 38.820238 5.783737 38.992139 4.673006 39.084699 3.892849 39.53428 3.416821 39.94419 3.813511 39.018587 4.752343 37.69628 6.735792 37.960744 6.841576 37.97397 6.934137 37.907854 6.960583 37.80207 6.987029 37.69628 6.920914 37.60372 6.828353 37.564058 6.735792 37.537607 4.924242 36.955797 3.337483 37.154146 4.765566 36.241758 7.43661 34.879789 9.234938 35.924406 9.327498 35.977296 9.446506 35.95085 9.499397 35.858289 9.552289 35.765729 9.525843 35.64672 9.446506 35.593828 8.930808 35.25003 7.621733 34.28475 6.563893 32.77733 6.55067 32.764108 6.537447 32.750886 6.524224 32.737665 5.466385 31.85172 5.082918 31.203795 4.937465 30.846774 5.016803 30.873219 5.109364 30.899666 5.215148 30.939335 5.281263 30.965783 5.360601 30.939335 5.413493 30.886443 5.466385 30.833552 5.479608 30.754214 5.453162 30.688097 5.347378 30.383969 5.254817 30.07984 5.201925 29.762488 5.188702 29.68315 5.122587 29.617035 5.043249 29.603814 4.553998 29.524476 4.130863 29.484805 3.773842 29.524474 2.663111 29.617035 1.882954 30.066618 1.406926 30.47653 1.803616 29.550923 2.742449 28.215399 4.725898 28.493083 4.831682 28.506306 4.924242 28.44019 4.950688 28.334404 4.977134 28.22862 4.911019 28.13606 4.818458 28.096392 4.725897 28.069947 2.914347 27.488135 1.327588 27.68648 2.292867 27.07822 3.813511 26.245173 5.254817 26.09972 5.334156 26.086497 5.40027 26.033606 5.426716 25.96749 5.466385 25.848483 5.506054 25.729475 5.545723 25.610468 5.611838 25.716252 5.691176 25.848484 5.783737 25.993937 5.823406 26.04683 5.876297 26.086497 5.942412 26.086497 6.431664 26.112942 6.868022 26.245173 7.238266 26.45674 7.330827 26.509633 7.449834 26.483189 7.502726 26.390627 7.555618 26.298068 7.529172 26.17906 7.449834 26.126168 6.431664 25.438572 1.539156 21.802247 3.549051 16.156029 4.025079 16.989075 5.254817 18.85352 6.788684 18.866742 6.854799 18.866742 6.920914 18.827073 6.960583 18.76096 7.000252 18.694844 7.000252 18.615506 6.960583 18.562615 6.94736 18.549393 6.153981 17.213871 6.06142 15.270088 5.92919 12.79739 6.920914 10.49659 9.010147 8.4338 9.049816 9.597424 9.327498 12.638714 11.152271 14.410595 11.218387 14.476711 11.337393 14.489933 11.416731 14.42382 11.496069 14.357704 11.509293 14.251919 11.456401 14.159359 11.429955 14.11969 9.182046 10.668488 11.152271 7.944553 11.443177 8.658596 12.236557 10.232128 13.743978 10.483364 15.502636 10.774269 17.41997 11.898228 17.433194 11.911449 17.512532 11.951118 17.605092 11.951118 17.671208 11.885002 17.737322 11.81889 17.750546 11.72633 17.710877 11.646992 17.684432 11.594097 17.076172 10.417248 15.925772 10.007336 16.36213 9.875107 16.98361 9.782547 17.618315 9.954445 18.34558 10.166012 18.940613 10.694931 19.403418 11.527981 19.45631 11.620541 19.562096 11.66021 19.654656 11.607319 19.66788 11.607319 20.011675 11.448643 20.130682 11.012283 20.24969 10.54948 20.077792 9.980892 19.601763 9.319744 19.046397 8.552811 18.61004 8.103226 18.30591 7.85199 19.601765 7.785873 22.76206 7.865215 23.793454 10.258575 23.833123 10.337914 23.912459 10.390804 24.00502 10.377583 24.09758 10.364361 24.163697 10.298241 24.176918 10.205681 24.216588 9.967667 24.467825 8.023891 24.176918 7.071835 24.692616 6.979275 26.001692 6.860268 26.808295 7.865215 26.847964 7.918106 26.914078 7.944553 26.993416 7.944553 27.059533 7.931328 27.125647 7.891659 27.152092 7.825542 27.152092 7.812321 27.786797 6.397461 29.5719 4.969379 30.801638 3.990879 31.38345 3.752865 31.60824 3.69997 31.793362 4.242115 32.428068 6.04044 33.32723 6.675144 33.38012 6.714813 33.45946 6.714809 33.512353 6.701588 33.57847 6.67514 33.618135 6.622253 33.63136 6.556137 33.64458 6.490025 33.97516 4.823925 36.63298 4.559467 36.355298 5.36607 35.733816 7.561085 36.619756 8.499916 36.67265 8.552807 36.73876 8.566029 36.80488 8.552807 36.87099 8.539585 36.923887 8.486691 36.95033 8.420578 36.976778 8.328014 37.717264 6.212341 41.221357 5.76276 40.877557 6.212341 40.401529 6.992496 40.480867 7.785873 40.49409 7.878437 40.573434 7.957775 40.665994 7.970997 40.758554 7.970997 40.851114 7.904884 40.864339 7.812325 40.904008 7.57431 41.380037 5.537968 44.2362 5.617306 45.214704 5.643753 45.889074 5.379292 46.27254 4.837151 46.74857 4.162777 46.603116 3.223942 46.45766 2.708248 46.999807 2.999153 48.004755 3.911537 48.26921 6.635471 48.282438 6.714809 48.335325 6.78093 48.41466 6.807373 48.40144 6.873489 48.427888 6.873489 48.44111 6.873489ZM6.034974 25.769145C5.968859 25.66336 5.902744 25.557579 5.849852 25.465018 5.982082 25.584025 6.101089 25.689808 6.220096 25.782368 6.167204 25.782368 6.101089 25.782368 6.034974 25.769145Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,29.242554,14.353516)" d="M16.408495 5.729763C14.001911 3.01905 14.425047 .903371 14.425047 .903371L7.972229 2.926489 1.228502 3.561193C1.228502 3.561193 2.431793 5.346296 1.26817 8.771051 .104546 12.209029-.212804 12.288366 .130993 12.711502 .461568 13.134638 2.854929 14.999081 10.074683 13.557775L10.154022 13.544551C17.373776 12.076799 18.841526 9.432201 18.986979 8.903281 19.145655 8.400807 18.81508 8.453699 16.408495 5.729763Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,29.039216,14.691406)" d="M14.628392 1.055341C14.61517 1.055341 14.588724 1.055342 14.575501 1.068564L8.135901 3.091682 1.418623 3.726386C1.352509 3.726386 1.286394 3.779278 1.259948 3.845393 1.233502 3.911508 1.233501 3.977623 1.27317 4.043737 1.286393 4.056961 2.397125 5.802395 1.286394 9.081697 .863258 10.324658 .559128 11.131262 .34756 11.673405-.035907 12.651905-.115243 12.863474 .175663 13.220494 .255001 13.326279 2.278117 15.746086 10.317696 14.146104L10.397035 14.13288C18.423392 12.493229 19.348999 9.478387 19.388669 9.35938 19.507676 8.909798 19.362225 8.751122 18.63496 7.997412 18.23827 7.587499 17.643237 6.966019 16.77052 5.974295 14.469718 3.369365 14.826738 1.346247 14.839961 1.3198 14.853184 1.253686 14.826738 1.174348 14.773846 1.13468 14.734177 1.068564 14.681284 1.055341 14.628392 1.055341ZM1.762422 4.083407 8.202017 3.475148C8.21524 3.475148 8.228465 3.475149 8.241688 3.461926L14.416823 1.518146C14.390378 2.218964 14.549054 4.017292 16.47961 6.199086 17.35233 7.19081 17.960587 7.81229 18.357277 8.235426 19.084542 8.989136 19.071318 9.002359 19.018427 9.213926 19.005204 9.240374 18.119263 12.122986 10.33092 13.709744L10.251581 13.722968C2.450016 15.270058 .519461 12.969257 .506238 12.942811 .374008 12.770912 .360785 12.757689 .731028 11.779188 .942596 11.237045 1.246724 10.430443 1.66986 9.174259 2.595469 6.437099 2.053328 4.718111 1.762422 4.083407Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,33.946077,4.908203)" d="M.029041 1.466797 .002594 1.321344C-.010629 1.281675 .02904 1.228783 .068709 1.228783L.38606 1.162668 .174495 .091605C.174495 .078382 .187716 .051937 .200939 .051937L.438955-.000955 .663743 1.109776 .967875 1.043661C1.020766 1.030438 1.073657 1.070107 1.073657 1.122999L1.100104 1.255229 .029041 1.466797Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,35.0065,5.267578)" d="M1.084287 .277917C1.044618 .396924 1.004947 .476261 .978501 .529153 .952055 .582045 .912386 .621715 .859495 .674607 .978501 .674607 1.071064 .687829 1.150402 .740721 1.216517 .793613 1.269409 .872951 1.282632 .965512 1.295855 1.04485 1.295854 1.124188 1.269408 1.190303 1.242962 1.256418 1.203293 1.30931 1.137178 1.348979 1.084286 1.388648 .978504 1.415094 .833051 1.44154L.26446 1.560547 0 .225025 .171901 .185355C.224793 .172132 .277684 .211801 .277684 .264693L.370246 .727498 .423137 .714275C.489252 .701052 .528921 .68783 .555367 .674607 .581813 .661384 .608259 .634938 .621482 .608492 .621482 .595269 .634706 .595268 .634706 .582045 .687598 .476261 .727265 .357255 .740488 .225025L.753712 .15891C.753712 .106018 .793379 .066348 .846271 .066348L1.190069 .000233 1.084287 .277917ZM.859495 .872951C.819826 .872951 .740488 .872951 .608258 .899397L.409914 .939066 .476029 1.282864 .687597 1.243195C.793381 1.216749 .859496 1.203526 .885942 1.203526 .925611 1.190303 .952055 1.163857 .978501 1.137411 .991724 1.110965 1.004947 1.071296 .991724 1.018404 .978501 .978735 .965279 .952289 .938833 .925843 .92561 .899397 .899163 .886174 .859495 .872951Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,36.44284,5.484375)" d="M1.208295 1.116096C1.115734 1.261549 .970283 1.340887 .785161 1.380556 .679377 1.407002 .586815 1.407002 .494254 1.380556 .428139 1.367333 .362025 1.340887 .309133 1.301218 .243018 1.261549 .190124 1.208657 .150455 1.142542 .097563 1.063204 .044671 .957421 .018225 .825191-.021444 .613623 .005002 .441724 .08434 .309494 .176901 .164041 .309133 .084703 .507477 .045034 .692599 .005365 .864496 .031811 .996726 .124372 1.128956 .216933 1.221517 .375609 1.261186 .573954 1.327301 .798745 1.300856 .970643 1.208295 1.116096ZM.838052 .33594C.758714 .269825 .666153 .256602 .560369 .269825 .454585 .296271 .375248 .349163 .322356 .428501 .269464 .521062 .256239 .640069 .282685 .785521 .309131 .930974 .362025 1.036759 .454586 1.102873 .533924 1.168988 .626482 1.182211 .732266 1.155765 .83805 1.129319 .91739 1.076427 .970282 .997089 1.023174 .904528 1.036395 .798745 .996726 .640069 .983503 .494616 .930613 .388831 .838052 .33594Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,37.836214,5.8066408)" d="M1.071063 1.374976 .885942 .489036 .542144 1.427868C.528921 1.467537 .489254 1.493983 .449585 1.507206L.26446 1.546875 0 .211352 .25124 .158461 .423137 1.031179 .753712 .132015C.766935 .092346 .806603 .052677 .859495 .039454L1.057839-.000215 1.322299 1.335307 1.071063 1.374976Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,39.25769,6.0117189)" d="M.786766 .705735C.720652 .718958 .654536 .679288 .641313 .613173L.628089 .520613 .93222 .454498 .892549 .295822C.85288 .269376 .79999 .256153 .733875 .24293 .680983 .229707 .614866 .229707 .561974 .24293 .45619 .269376 .36363 .322267 .310738 .414828 .257846 .507389 .244625 .626396 .284294 .785072 .31074 .930525 .363631 1.023087 .456192 1.089202 .53553 1.142094 .641315 1.168539 .747099 1.142093 .826437 1.12887 .879327 1.102424 .918996 1.062755 .918996 1.062755 .945441 1.036309 .958664 .983417 .971887 .930525 1.011559 .90408 1.077674 .890857L1.236347 .86441C1.24957 .956971 1.249572 1.036309 1.19668 1.12887 1.117342 1.274323 .945442 1.340438 .786766 1.366884 .66776 1.39333 .561976 1.39333 .469415 1.366884 .350408 1.327215 .244623 1.274323 .178508 1.181762 .09917 1.089201 .04628 .970194 .019834 .837965-.006611 .718958-.006611 .59995 .019834 .480943 .04628 .361936 .112394 .269376 .191732 .190038 .284293 .1107 .390077 .057808 .522307 .031362 .628091 .004916 .733874 .004916 .852881 .031362 .958665 .044585 1.051226 .084254 1.117341 .123923L1.223124 .63962 .786766 .705735Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,34.66272,6.8789064)" d="M1.190069 .542085C1.1504 .6082 1.09751 .661092 1.018172 .700761 1.084287 .713984 1.137181 .74043 1.17685 .793322 1.216519 .832991 1.256185 .885882 1.269408 .951997 1.282631 1.004889 1.28263 1.071005 1.256184 1.110674 1.242961 1.163566 1.216519 1.203235 1.17685 1.242904 1.137181 1.282573 1.097512 1.309018 1.04462 1.322241 .991728 1.335464 .912387 1.36191 .806603 1.388356L.26446 1.494141 0 .158618 .462805 .066057C.634704 .026388 .753711 .013165 .79338 .013165 .872717-.000058 .938832 .013165 .991724 .039611 1.044616 .066057 1.097509 .105726 1.137178 .158618 1.176847 .21151 1.203294 .264402 1.216517 .330517 1.22974 .396632 1.229738 .47597 1.190069 .542085ZM.502476 1.229681 .66115 1.203234C.79338 1.176788 .87272 1.163565 .899166 1.150342 .938835 1.137119 .978501 1.110673 .991724 1.084227 1.004947 1.057781 1.018171 1.018113 1.004948 .978444 .991725 .938775 .978502 .899105 .938833 .885882 .912387 .859436 .872719 .859437 .819827 .859437 .793381 .859437 .727266 .87266 .621482 .899106L.436361 .938774 .502476 1.229681ZM.872718 .290848C.846272 .264402 .806605 .251179 .766936 .251179 .74049 .251179 .674376 .264402 .581815 .277625L.330575 .330517 .39669 .687537 .621482 .647869C.740489 .621423 .819826 .594977 .859495 .581754 .899163 .555308 .912387 .528862 .938833 .502416 .952056 .47597 .952057 .436301 .952057 .396632 .925611 .34374 .912387 .317294 .872718 .290848Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,36.11264,7.1621096)" d="M1.221144 1.116096C1.128583 1.261549 .983128 1.35411 .798007 1.380556 .692223 1.407002 .599664 1.407002 .507103 1.380556 .440988 1.367333 .374873 1.340886 .308758 1.301217 .242643 1.261548 .18975 1.208657 .150081 1.142542 .097189 1.063204 .044297 .95742 .017851 .82519-.021818 .613622 .004629 .441723 .09719 .309493 .189751 .16404 .335202 .084702 .533547 .045033 .731892 .005364 .89057 .031811 1.036023 .124372 1.181476 .216933 1.260815 .375608 1.313707 .573953 1.340153 .798744 1.313705 .983866 1.221144 1.116096ZM.850902 .33594C.771564 .269825 .679002 .256602 .573218 .269825 .467434 .296271 .388094 .349162 .335202 .441723 .28231 .534284 .269089 .653291 .295535 .798744 .321981 .944197 .374871 1.049981 .467432 1.116096 .54677 1.182211 .639332 1.195433 .758339 1.168988 .864123 1.142541 .94346 1.08965 .996352 1.010312 1.049243 .917751 1.062468 .811967 1.022799 .653292 .996353 .507839 .93024 .402055 .850902 .33594Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,37.492464,7.421875)" d="M1.295852 .884985C1.282629 .977546 1.256181 1.056884 1.203289 1.122999 1.16362 1.189114 1.097506 1.242006 1.018168 1.281675 .965276 1.308121 .872716 1.334567 .766932 1.361013L.26446 1.466797 0 .131275 .515696 .025491C.62148-.000955 .700817-.000955 .766932-.000955 .859493 .012268 .925609 .025491 .978501 .06516 1.057839 .118052 1.123954 .184167 1.176846 .276728 1.216514 .356066 1.256181 .448626 1.269404 .55441 1.29585 .68664 1.309075 .792424 1.295852 .884985ZM.912386 .395735C.88594 .342843 .846269 .303174 .819823 .276728 .780154 .263505 .740486 .250281 .700817 .237058 .661148 .237058 .608258 .237059 .52892 .263505L.317351 .303173 .502472 1.189114 .621478 1.162668C.727262 1.136222 .806602 1.122999 .846271 1.109776 .899163 1.083331 .938831 1.056884 .965277 1.017215 .991723 .977546 1.004945 .937877 1.018168 .871762 1.031391 .81887 1.018167 .726309 1.004944 .633748 .978498 .527964 .952055 .448627 .912386 .395735Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,38.999849,7.6992189)" d="M.965277 1.309018C.925608 1.322242 .872717 1.295796 .846271 1.26935L.727265 1.110674 .52892 .846214 .409914 1.348688C.396691 1.388357 .370243 1.428026 .317351 1.428026L0 1.494141 .343799 .621423 .251236 .132173C.238013 .092504 .264458 .052835 .304127 .039611L.502472-.000057 .621478 .555308 1.282628 1.229681 .965277 1.309018Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,32.543764,4.6484377)" d="M1.047863 .532817C1.021417 .598932 .981748 .651824 .928856 .691493 .875965 .731162 .783404 .784054 .664397 .836946 .54539 .889838 .466052 .94273 .439606 .969176 .41316 .995622 .413159 1.022068 .413159 1.048514 .426381 1.088183 .439606 1.101406 .466052 1.114629 .518944 1.141075 .571835 1.141075 .651173 1.127852 .730511 1.114629 .783402 1.088183 .809848 1.048514 .823071 1.035291 .836295 1.008845 .849518 .982399 .862741 .929507 .90241 .889838 .955302 .876615L1.113977 .850169C1.140423 .969176 1.113978 1.07496 1.047863 1.154298 .981748 1.246858 .862742 1.299751 .704066 1.33942 .611505 1.365866 .518944 1.365865 .439606 1.352642 .360268 1.339419 .307375 1.299751 .254483 1.260082 .201591 1.20719 .175145 1.154298 .161922 1.088183 .135476 .982399 .161922 .889838 .228037 .8105 .267706 .744385 .360267 .691493 .492497 .625378 .598281 .572486 .664396 .54604 .690842 .532817 .730511 .506371 .756958 .479925 .770181 .453479 .783404 .427033 .783403 .400587 .783403 .374141 .77018 .321249 .743734 .294804 .690842 .268358 .63795 .241912 .571835 .241911 .492497 .255134 .413159 .268357 .360267 .308026 .320598 .347695 .294152 .374141 .28093 .41381 .267707 .453479 .254484 .506371 .214814 .559263 .148699 .559263L.003247 .572486C-.009976 .427033 .01647 .308026 .095808 .215465 .161922 .122904 .280929 .056789 .452828 .030343 .558612 .003897 .664395 .003897 .743733 .01712 .823071 .030343 .889186 .070012 .942078 .122904 .99497 .175796 1.03464 .241911 1.047863 .321249 1.074309 .400587 1.061086 .466702 1.047863 .532817Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,40.95691,8.517578)" d="M3.279305 2.841934C3.292527 2.934495 3.239635 3.013833 3.147075 3.040279H3.133851L.370246 3.595645H.357022C.264461 3.608869 .185124 3.555977 .158678 3.463416V3.450192L0 2.630367C.26446 2.77582 .595034 2.841934 .912386 2.775819 .925609 2.775819 .92561 2.775819 .938833 2.775819 .978502 2.762596 1.018171 2.762597 1.071063 2.749374 1.05784 2.762597 1.057839 2.775819 1.044616 2.775819 1.044616 2.789042 1.031396 2.789043 1.031396 2.802266 1.018173 2.815489 1.018171 2.815489 1.004948 2.828712 .991725 2.841935 .991724 2.841934 .978501 2.855157 .965278 2.86838 .952056 2.881604 .938833 2.881604L.925609 2.894826 .912386 2.908049C.899163 2.921272 .885941 2.921273 .872718 2.934496 .846272 2.960942 .806603 2.987388 .79338 3.000611H.806603 .819827 .833051C.846274 3.000611 .859496 3.000611 .885942 3.000611 .899165 3.000611 .899163 3.000611 .912386 3.000611 .925609 3.000611 .92561 3.000611 .938833 3.000611H.952057C.96528 3.000611 .965278 3.000611 .978501 3.000611 .991724 3.000611 1.004948 3.000611 1.004948 3.000611 1.018171 3.000611 1.018173 3.000611 1.031396 3.000611 1.057842 3.000611 1.084285 3.000611 1.110731 3.000611 1.176846 3.000611 1.242961 2.987387 1.322299 2.974164 1.441306 2.947718 1.54709 2.921273 1.652874 2.868381 1.705766 2.841935 1.75866 2.815488 1.811552 2.789042 1.851221 2.762596 1.890888 2.749373 1.917334 2.722927 1.930557 2.709704 1.943781 2.696482 1.943781 2.670036 1.943781 2.656813 1.943781 2.64359 1.943781 2.630367V2.617144C1.930559 2.551029 1.930557 2.471691 1.917334 2.405576 1.917334 2.37913 1.930559 2.352684 1.943781 2.339461 2.023119 2.2469 2.102456 2.167562 2.181794 2.075001 2.20824 2.048555 2.221465 2.02211 2.221465 2.008886 2.221465 1.995663 2.20824 1.969217 2.181794 1.942771 2.181794 1.942771 2.181793 1.929548 2.16857 1.929548 2.234685 1.863433 2.3008 1.797318 2.366915 1.71798 2.380138 1.704757 2.380139 1.691534 2.393363 1.691534 2.406585 1.678311 2.419807 1.651865 2.43303 1.638642 2.446253 1.612196 2.459478 1.598973 2.472701 1.572527 2.485924 1.559304 2.485922 1.546081 2.499145 1.532858 2.525591 1.493189 2.538814 1.453521 2.56526 1.413852H2.55204C2.538817 1.413852 2.538815 1.413852 2.525592 1.413852 2.512369 1.413852 2.512368 1.413852 2.499145 1.413852 2.472699 1.413852 2.433032 1.413852 2.406586 1.413852 2.393363 1.413852 2.393362 1.413852 2.380139 1.413852 2.353693 1.413852 2.340471 1.400628 2.327247 1.387405V1.374182H2.340471C2.406586 1.334513 2.43303 1.268398 2.43303 1.202283 2.43303 1.175837 2.419808 1.136169 2.393363 1.109723L2.380139 1.0965C2.327247 1.043608 2.247908 1.030385 2.16857 1.030385 2.155347 1.030385 2.142126 1.030385 2.142126 1.030385 2.089234 1.030385 2.036342 1.056831 1.996673 1.083277 1.98345 1.0965 1.970228 1.0965 1.957005 1.109723 1.970228 1.109723 1.98345 1.0965 1.996673 1.0965 2.036342 1.083277 2.089234 1.083277 2.128903 1.0965 2.142126 1.0965 2.142127 1.0965 2.15535 1.109723 2.168573 1.122946 2.181795 1.122945 2.195018 1.136168 2.181795 1.136168 2.168572 1.149392 2.142126 1.149392H2.128903C2.089234 1.162615 2.049566 1.175838 2.009897 1.18906 1.930559 1.228729 1.877666 1.308068 1.864443 1.413852 1.864443 1.427075 1.864442 1.427074 1.851219 1.440297 1.837996 1.453521 1.837998 1.45352 1.824775 1.45352 1.811552 1.45352 1.798328 1.45352 1.798328 1.45352 1.771882 1.45352 1.732211 1.466743 1.705765 1.466743 1.613204 1.466743 1.507424 1.466743 1.428086 1.440297 1.40164 1.440297 1.375194 1.427075 1.361971 1.427075 1.348748 1.427075 1.335522 1.413852 1.322299 1.413852 1.229738 1.559305 1.150402 1.717981 1.084287 1.876657 1.084287 1.850211 1.071063 1.836987 1.071063 1.810542V1.797318C1.071063 1.784095 1.057839 1.75765 1.057839 1.744427 1.057839 1.731204 1.057839 1.71798 1.057839 1.71798 1.057839 1.691534 1.057839 1.678311 1.044616 1.651865V1.638642C1.044616 1.625419 1.044616 1.612196 1.044616 1.598973 1.044616 1.58575 1.044616 1.572528 1.044616 1.546082 1.044616 1.532859 1.044616 1.519635 1.044616 1.506412V1.49319C1.044616 1.479967 1.044616 1.466743 1.044616 1.45352 1.044616 1.440297 1.044616 1.427074 1.044616 1.400628 1.044616 1.387405 1.044616 1.36096 1.044616 1.347737 1.044616 1.308068 1.057839 1.255176 1.057839 1.215507 1.071062 1.175838 1.071064 1.149392 1.084287 1.109723 1.190071 .713033 1.454531 .382458 1.811552 .184113L2.525592 .038661C2.618153 .012214 2.723938 .078329 2.737161 .184113L3.266081 2.841934C3.266081 2.828711 3.266082 2.841934 3.279305 2.841934ZM1.89089 .620472C1.745437 .713033 1.573538 .911378 1.480977 1.043608 1.573538 .96427 1.77188 .858486 1.90411 .83204 1.996671 .805594 2.062789 .792371 2.15535 .805593H2.16857C2.089232 .726255 1.996674 .660141 1.89089 .620472Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,40.851106,6.5351564)" d="M.634702 .34375C.621479 .34375 .608258 .34375 .608258 .34375H.595035C.581812 .34375 .568586 .34375 .555363 .34375 .54214 .34375 .528919 .34375 .515696 .34375 .502473 .34375 .489252 .34375 .476029 .34375 .462806 .34375 .44958 .34375 .436357 .34375 .423134 .34375 .409913 .34375 .39669 .34375 .383467 .34375 .370241 .34375 .357018 .34375 .27768 .330527 .198345 .317305 .13223 .290859 .119007 .290859 .105782 .277635 .092559 .277635 .079336 .277635 .066114 .264412 .052891 .264412L0-.000048C.052892 .039621 .105782 .079291 .158673 .11896 .171896 .132183 .198346 .145406 .211569 .158629 .383468 .264413 .581811 .330527 .780156 .34375H.766932C.687594 .330527 .661148 .330527 .634702 .34375Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,42.530397,6.826172)" d="M.343799 .290858 .357022 .304081C.357022 .304081 .343798 .304081 .330575 .304081 .304129 .304081 .264461 .317304 .224792 .317304 .145455 .330527 .066116 .34375 .013224 .34375H0C.013223 .34375 .013225 .330527 .026448 .330527 .145455 .237966 .251237 .132182 .330575 .013175L.343799-.000048V.013175C.343799 .026398 .343799 .052844 .343799 .066067 .343799 .105736 .330578 .132182 .317355 .171851 .317355 .185074 .304131 .198297 .304131 .21152L.343799 .290858Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,41.563295,8.1484379)" d="M.041498 .754598C-.011394 .516584-.011391 .278571 .028278 .05378L.319181 .000888C.160506 .357909 .107613 .754599 .173728 1.177734 .120836 1.032281 .067944 .886828 .041498 .754598Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,40.55768,7.9882814)" d="M.914891 1.322266 .901672 1.309043C.888449 1.282597 .861999 1.269374 .848776 1.242928 .835553 1.229705 .822332 1.203259 .809109 1.190036 .795886 1.176813 .795885 1.16359 .795885 1.16359 .782662 1.150367 .782665 1.137144 .769442 1.123921 .769442 1.110698 .756218 1.110698 .756218 1.097475 .756218 1.084252 .742994 1.084252 .742994 1.071029 .729771 1.057806 .729769 1.044583 .716546 1.03136 .703323 1.004914 .690102 .965245 .676879 .938799 .610764 .806569 .571094 .647893 .531425 .502441 .491756 .330542 .491758 .171865 .491758-.000034L.147959 .066081C.134736 .066081 .121515 .066081 .108292 .079304 .028954 .118973-.010717 .198311 .002506 .277649L.042177 .489218C.0554 .449549 .055398 .423102 .081844 .383433 .10829 .343764 .134737 .304095 .161183 .264426 .161183 .290872 .174407 .330542 .174407 .356988 .174407 .370211 .187627 .396656 .187627 .409879 .306634 .833015 .571093 1.150367 .914891 1.322266Z" fill="#d72028"/>
<path transform="matrix(1,0,0,-1,27.267487,17.58789)" d="M18.039725 .750435C17.695927 .723989 17.35213 .816549 17.074448 1.04134 11.309223 5.801618 1.524209 5.114022 1.418425 5.1008 .69116 5.047907 .056455 5.590051 .003563 6.317316-.049329 7.04458 .492815 7.679283 1.22008 7.732175 1.669662 7.771844 12.155493 8.512332 18.766989 3.077682 19.335577 2.614878 19.414919 1.781828 18.952115 1.21324 18.700877 .935557 18.383522 .776881 18.039725 .750435Z" fill="#bc0625"/>
<path transform="matrix(1,0,0,-1,27.081208,17.78711)" d="M18.107008 .737991C17.749987 .737991 17.419413 .856997 17.141732 1.081788 11.416175 5.802396 1.723722 5.101577 1.63116 5.101577 .798112 5.035462 .057624 5.670166 .004732 6.503214-.061383 7.336263 .573322 8.063528 1.40637 8.129642 1.855952 8.169312 12.434342 8.923021 19.085509 3.422257 19.733435 2.893337 19.825997 1.928059 19.297078 1.280132 19.032618 .96278 18.675599 .77766 18.265685 .737991 18.19957 .737991 18.146677 .737991 18.107008 .737991ZM3.032796 5.537936C5.994747 5.537936 12.883926 5.101579 17.392968 1.39914 17.63098 1.214019 17.921889 1.121457 18.212794 1.147902 18.516922 1.174348 18.78138 1.319802 18.966502 1.557816 19.363192 2.033844 19.29708 2.747885 18.807829 3.144575 12.27567 8.526333 1.842729 7.785844 1.40637 7.759398 .784889 7.706507 .322084 7.177587 .374976 6.556107 .427868 5.934626 .97001 5.471822 1.578267 5.524714 1.617936 5.498268 2.146856 5.537936 3.032796 5.537936Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,46.312196,26.138672)" d="M0 7.862596C0 7.862596 3.530541 3.075872 3.530541 2.150263 3.530541 2.150263 11.120538 .999863 11.715572 7.003101 12.310607 13.00634 6.056133 14.831112 3.345419 13.336914 .634706 11.842716 0 7.862596 0 7.862596Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,46.087404,24.652344)" d="M5.209858 .359997C4.363587 .359997 3.781773 .452558 3.715658 .452558L3.54376 .479003V.650902C3.54376 1.272383 1.401636 4.419454 .052891 6.25745L0 6.323565 .01322 6.402903C.039666 6.574802 .700816 10.502031 3.464422 12.022676 5.051181 12.895392 7.656111 12.630933 9.494107 11.401195 11.437887 10.118565 12.363495 8.016109 12.112258 5.490518 11.953582 3.903758 11.305658 2.660798 10.181705 1.78808 8.674283 .598011 6.598272 .359997 5.209858 .359997ZM3.914002 .836023C4.628044 .756685 7.880902 .492227 9.956912 2.105432 10.988305 2.912034 11.583339 4.062432 11.728792 5.530185 11.966805 7.910324 11.094089 9.88055 9.282538 11.07062 7.550326 12.221021 5.130519 12.472258 3.662766 11.665655 1.282628 10.356579 .542141 6.971493 .436357 6.416127 .938831 5.715308 3.623097 2.01287 3.914002 .836023Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,47.40973,24.605469)" d="M0 5.339314C0 5.339314 2.393358 2.073233 2.393358 1.451753 2.393358 1.451753 7.550326 .671597 7.960239 4.757501 8.370152 8.843406 4.112348 10.073145 2.274352 9.068197 .423133 8.050027 0 5.339314 0 5.339314Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,16.18274,17.123047)" d="M5.402491 .685915C5.402491 .685915 9.990871 3.819764 10.850366 3.793318 10.850366 3.793318 12.172664 10.841173 6.579338 11.594884 .986012 12.348595-.904876 6.570147 .390977 3.991663 1.686831 1.413179 5.402491 .685915 5.402491 .685915Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,15.989716,16.898438)" d="M5.635172 .250181 5.555835 .263403C5.39716 .289848 1.747612 1.030337 .41209 3.674936-.354844 5.19558-.024268 7.615387 1.179024 9.30793 2.448431 11.079811 4.445103 11.886414 6.798796 11.569061 8.279771 11.370717 9.416947 10.72279 10.197104 9.651728 11.90287 7.284812 11.254944 3.688159 11.228498 3.542706L11.202052 3.370807H11.030153C11.030153 3.370807 11.030152 3.370807 11.016929 3.370807 10.408671 3.370807 7.4335 1.493141 5.688066 .303072L5.635172 .250181ZM5.820295 11.238486C4.021968 11.238486 2.514546 10.484777 1.509599 9.069917 .385645 7.496381 .068293 5.248472 .769111 3.846835 1.906289 1.585703 5.013693 .792323 5.555835 .673316 6.230208 1.136121 9.747523 3.503036 10.871477 3.741051 10.977262 4.428646 11.307837 7.430266 9.879753 9.400492 9.165711 10.378993 8.121096 10.974027 6.745905 11.159149 6.441776 11.212041 6.124424 11.238486 5.820295 11.238486Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,18.870514,15.904297)" d="M3.666796 .459738C3.666796 .459738 6.787421 2.58864 7.382456 2.575417 7.382456 2.575417 8.281619 7.375363 4.473398 7.877837 .665176 8.380311-.617455 4.453082 .268486 2.707646 1.141203 .962212 3.666796 .459738 3.666796 .459738Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,13.029602,51.058595)" d="M37.91061 13.424328C36.707319 8.055794 32.132169 2.290569 17.163739 4.935169-6.333518 9.100412-.793085 24.002724 5.276269 32.51833 11.358846 41.033937 22.585167 38.336446 22.585167 38.336446 28.892535 36.815805 32.581747 34.475336 34.750318 31.949742 35.33213 31.262146 35.808156 30.57455 36.191625 29.873732 36.204847 29.860508 36.21807 29.83406 36.21807 29.820839 36.588315 29.093575 36.985006 28.154744 37.342027 26.99112V26.977894C38.12218 24.333296 38.664327 20.498629 38.214746 15.090425 38.16185 14.429276 38.056066 13.887133 37.91061 13.424328Z" fill="#f44a66"/>
<path transform="matrix(1,0,0,-1,12.828644,47.378908)" d="M24.227448 .395683C22.217553 .395683 19.916752 .60725 17.325047 1.070053 8.386303 2.656815 2.832647 5.936117 .835975 10.828625-1.83507 17.374007 2.502072 24.990448 5.331792 28.944125 8.518534 33.400274 13.106911 34.76224 16.399437 35.132486 19.95642 35.51595 22.812588 34.8548 22.839034 34.84158 28.458806 33.47961 32.58438 31.311039 35.096754 28.401982L35.109975 28.388756C35.678564 27.727608 36.167814 27.02679 36.577726 26.2863 36.590948 26.273079 36.60417 26.246632 36.60417 26.220189 37.04053 25.373917 37.423997 24.408638 37.74135 23.350798V23.337574C38.719854 20.045048 39.010755 16.02526 38.62729 11.370768 38.574396 10.736065 38.468614 10.167475 38.309938 9.691446L38.29671 9.651779C37.26532 5.182407 33.906679 .395683 24.227448 .395683ZM18.660569 34.8548C17.95975 34.8548 17.20604 34.815134 16.41266 34.735795 13.199473 34.378774 8.730102 33.056474 5.635921 28.719332 2.859093 24.831772-1.411934 17.34756 1.192995 10.974079 3.136775 6.2138 8.584648 3.013836 17.391161 1.453526 24.081995 .263454 29.26541 .673363 32.795949 2.670036 35.440549 4.164234 37.15954 6.531153 37.886804 9.70467L37.84713 9.757561 37.92647 9.836901C38.071924 10.286482 38.164487 10.802177 38.217378 11.397211 38.600847 15.998814 38.30994 19.965712 37.357885 23.218567V23.231789C37.053754 24.263184 36.683507 25.202017 36.24715 26.035065 36.233926 26.048289 36.23393 26.074734 36.220708 26.087958 35.824018 26.801999 35.347986 27.476372 34.805845 28.111077L34.792619 28.13752C32.33314 30.993687 28.286908 33.12259 22.733252 34.45811 22.720028 34.45811 21.040707 34.8548 18.660569 34.8548Z" fill="#231f20"/>
<path transform="matrix(1,0,0,-1,20.21112,46.041017)" d="M16.752362 14.948987C18.60358 13.772141 20.07133 11.987038 20.864708 9.92425 21.420075 8.482944 21.644869 6.856517 21.182065 5.388764 20.454798 3.101188 18.16722 1.580543 15.800303 1.157407 13.433388 .734272 11.013579 1.249967 8.712779 1.924339 7.12602 2.387144 5.552485 2.929286 4.137625 3.762335 2.722764 4.595383 1.453356 5.759007 .686423 7.213536 .170726 8.20526-.106956 9.355662 .038497 10.466393 .276511 12.264721 1.612033 13.772143 3.079785 14.724198 7.007014 17.28946 12.759017 17.487803 16.752362 14.948987Z" fill="#ffeeee"/>
<path transform="matrix(1,0,0,-1,23.383393,42.583986)" d="M0 5.144827C.185122 4.721692 .423135 4.325002 .687595 3.954758 1.123954 3.320054 1.666097 2.764691 2.274354 2.27544 4.006567 .887026 6.294144 .12009 8.449492 .186205 8.687505 .186205 8.938742 .21265 9.176756 .239096 10.023027 .331657 10.869301 .556448 11.649457 .900246 12.204823 1.13826 12.733742 1.442392 13.222992 1.812636 13.381668 1.931643 13.540346 2.063872 13.712245 2.196102 11.094092 3.597739 8.542052 5.078712 5.474318 5.409287 3.609876 5.660524 1.771881 5.541517 0 5.144827Z" fill="#e22b2b"/>
<path transform="matrix(1,0,0,-1,22.827179,40.45703)" d="M.278614 3.72176C.357952 3.483746 .450514 3.245728 .556298 3.007715 2.328179 3.404405 4.166174 3.523412 6.030616 3.285398 9.085127 2.954823 11.650391 1.47385 14.268543 .072213 15.471835 1.090384 16.595788 2.624249 16.397442 4.211008 16.265212 5.215955 15.6834 5.758098 14.876798 5.982888 14.625561 6.049004 14.34788 6.101895 14.056974 6.115118 13.607392 6.141564 13.157809 6.062228 12.708228 5.943221 12.377653 5.863883 12.060303 5.771324 11.74295 5.705209 10.949572 5.53331 10.169414 5.559754 9.376034 5.678761 8.304972 5.837437 7.260355 6.194458 6.3083 6.736601 5.832272 7.00106 5.422359 7.331635 5.02567 7.66221 4.602534 8.019231 4.192621 8.389474 3.729816 8.706825 3.57114 8.825832 3.399241 8.918394 3.227342 8.984509 2.671976 9.222523 2.063719 9.222524 1.508353 8.865503 1.085217 8.58782 .767866 8.164683 .529852 7.715101-.104851 6.485363-.144521 5.044059 .278614 3.72176Z" fill="#d30707"/>
<path transform="matrix(1,0,0,-1,35.707368,36.61133)" d="M.171897 .604551C.119005 .842565 .052892 1.093801 0 1.331815 .52892 1.358261 1.07106 1.384706 1.613203 1.371483 1.441304 1.027685 1.256184 .697109 1.057839 .366534 .965278 .207859 .846272 .035959 .66115 .022736 .383467 .009513 .238012 .340091 .171897 .604551Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,35.522218,35.30078)" d="M1.348747 .97342C1.639653 .960197 1.917337 .920528 2.168574 .84119 2.049567 .57673 1.930558 .312271 1.798328 .061034 1.256185 .074257 .727268 .047813 .185125 .021367 .11901 .285826 .066115 .550285 0 .801522 .449582 .920529 .899165 .999866 1.348747 .97342Z" fill="#e5e5e5"/>
<path transform="matrix(1,0,0,-1,26.054535,33.52539)" d="M.066115 1.058C.039669 1.401798 .026446 1.732374 0 2.076172 .171899 2.010057 .343798 1.904272 .502474 1.798488 .965279 1.467913 1.375192 1.09767 1.798328 .753873 1.613206 .489413 1.414861 .238174 1.203293 .00016 .833049 .343958 .462805 .727425 .066115 1.058Z" fill="#e5e5e5"/>
<path transform="matrix(1,0,0,-1,26.120697,34.128908)" d="M1.137178 .581729C1.05784 .475945 .978501 .383386 .88594 .290825 .766933 .158595 .621481-.000081 .436359 .026365 .171899 .052811 .079338 .396608 .066115 .674291 .039669 1.004866 .026446 1.335441 0 1.666016 .39669 1.335441 .766934 .951972 1.137178 .581729Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,32.56018,42.328126)" d="M2.472701 .662039C1.692545 .318241 .859495 .09345 0 .000889 .224791 .238903 .436359 .476919 .66115 .70171 1.295853 .781048 1.904112 .926498 2.472701 1.177734 2.472701 1.005836 2.472701 .833937 2.472701 .662039Z" fill="#e5e5e5"/>
<path transform="matrix(1,0,0,-1,33.221376,41.728517)" d="M1.63965 1.346749C1.785103 1.240965 1.811552 1.029396 1.811552 .857497 1.811552 .764936 1.811552 .672374 1.811552 .579813 1.242963 .341799 .634704 .183126 0 .103788 .317352 .434363 .621481 .764938 .938833 1.095513 1.123955 1.293858 1.414859 1.505425 1.63965 1.346749Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,24.348725,39.640626)" d="M0 1.438995C.039669 1.50511 .092561 1.571224 .13223 1.637339 .277683 1.86213 .528919 2.126589 .780156 2.007582 .938832 1.928244 1.004948 1.729899 1.031394 1.544777 1.123955 1.068749 1.190068 .579499 1.24296 .090248 .766933 .486938 .343798 .923298 0 1.438995Z" fill="#ffffff"/>
<path transform="matrix(1,0,0,-1,24.071076,40.304689)" d="M1.520644 .766039C1.54709 .514803 1.573536 .263567 1.586759-.000892 .978501 .488358 .436359 1.043721 0 1.678425 .092561 1.823878 .185123 1.95611 .277684 2.101562 .621481 1.585866 1.044616 1.149506 1.520644 .766039Z" fill="#e5e5e5"/>
<path transform="matrix(1,0,0,-1,22.82608,42.46875)" d="M.279679 5.731973C.359017 5.493959 .451579 5.255942 .557363 5.017929 .742485 4.594793 .980498 4.198103 1.244958 3.827859 1.681317 3.193156 2.22346 2.637793 2.831717 2.148541 3.030062 1.989865 3.24163 1.83119 3.453197 1.69896 3.307745 1.791522 3.175516 1.897304 3.043286 2.003088 2.48792 2.439446 1.985446 2.955143 1.575533 3.523732 1.337519 3.86753 1.112728 4.224552 .940829 4.608019 .848268 4.819587 .755708 5.031154 .689593 5.255945 .292903 6.459237 .332573 7.768313 .927607 8.892268 1.139175 9.302181 1.430081 9.685647 1.826771 9.936884 2.329245 10.254235 2.897833 10.254233 3.400306 10.042665 3.558982 9.976551 3.717658 9.897214 3.863111 9.79143 4.286247 9.500525 4.656491 9.156729 5.053181 8.839376 5.423424 8.535248 5.80689 8.231115 6.230026 7.993102 7.102744 7.517075 8.054799 7.186502 9.046523 7.041049 9.773788 6.935265 10.501055 6.908817 11.228319 7.067492 11.519225 7.133607 11.823352 7.212947 12.114258 7.292285 12.52417 7.384846 12.934082 7.464185 13.357218 7.437739 13.621677 7.424516 13.872916 7.384848 14.11093 7.318733 14.86464 7.107165 15.393561 6.617912 15.512568 5.705526 15.69769 4.250997 14.666294 2.875806 13.555563 1.950196 13.41011 1.83119 13.264658 1.712182 13.105982 1.606398 12.6564 1.275824 12.16715 .998138 11.664677 .786571 10.937412 .469219 10.170479 .270878 9.390322 .178316 9.165531 .151871 8.94074 .138644 8.715949 .125422 8.107691 .112198 7.486212 .151869 6.877954 .284099 7.578773 .138646 8.292813 .072531 8.993632 .085754 9.231647 .085754 9.482883 .112199 9.720897 .138645 10.567168 .231206 11.41344 .455997 12.193596 .799795 12.748961 1.037808 13.277881 1.341937 13.767132 1.71218 13.925807 1.831187 14.084485 1.963421 14.256384 2.095651 15.459676 3.113821 16.583632 4.647686 16.385287 6.234446 16.253056 7.239393 15.671245 7.781535 14.864642 8.006326 14.613405 8.072441 14.335721 8.125333 14.044815 8.138556 13.595233 8.165002 13.14565 8.085666 12.696068 7.966659 12.365494 7.887321 12.048143 7.794757 11.730791 7.728642 10.937412 7.556744 10.157256 7.583192 9.363876 7.702199 8.292814 7.860875 7.248197 8.217896 6.296141 8.760038 5.820114 9.024498 5.410201 9.355073 5.013511 9.685648 4.590375 10.042668 4.180462 10.412911 3.717657 10.730263 3.558981 10.84927 3.387084 10.941832 3.215185 11.007947 2.65982 11.24596 2.051562 11.245961 1.496196 10.888941 1.073061 10.611258 .755708 10.188121 .517694 9.738539-.103787 8.495577-.143456 7.054273 .279679 5.731973Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,26.305787,42.183595)" d="M0 1.402344C.991724 .727971 2.142125 .23872 3.305748 .000706 2.142125 .265166 1.004947 .741194 0 1.402344Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,27.402588,40.08008)" d="M2.618839 2.287043C3.438664 2.101922 4.232045 1.771346 4.853526 1.21598 5.078317 1.017635 5.289885 .72673 5.184101 .435824 5.078317 .131696 4.681626 .052354 4.351052 .052354 2.975861 .052354 1.627114 .501939 .50316 1.282095 .10647 1.559778-.329889 2.23415 .37093 2.458941 .688282 2.564725 1.084972 2.485386 1.402324 2.445717 1.812237 2.419271 2.222149 2.366381 2.618839 2.287043Z" fill="#ffffff" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,28.143769,45.583986)" d="M7.748674 1.627483C9.917245 2.169626 11.279211 4.205966 11.543671 6.374537 11.821354 9.217481 10.591618 12.615791 7.748674 13.012481 4.971846 13.475286 1.41486 13.647182 0 16.119883 3.041288 16.622357 6.280923 16.106659 8.819737 14.493454 10.670957 13.316607 12.138705 11.531504 12.932085 9.468717 13.487451 8.027411 13.712245 6.400984 13.24944 4.933231 12.522176 2.645655 10.234596 1.12501 7.867681 .701874 5.685887 .318407 3.4512 .728321 1.309075 1.310133 3.424754 .966335 5.632996 1.032449 7.748674 1.627483Z" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,29.515686,33.56836)" d="M5.107373 4.106097C5.84786 3.841637 6.442894 3.431725 5.94042 2.770576 4.552005 .93258 .849569 1.950749 .109081 3.947421-.657853 6.023431 2.84624 4.674685 3.547059 4.502787 3.837965 4.423449 4.512338 4.317665 5.107373 4.106097Z" fill="#b70000"/>
<path transform="matrix(1,0,0,-1,29.515717,32.61914)" d="M.003247 3.609375C.518944 2.498644 1.378439 1.546587 2.700739 1.282128 3.9437 1.110229 5.239553 1.625924 6.032932 2.577979 6.151939 2.379634 6.151941 2.128401 5.92715 1.837495 4.538736-.000501 .836297 1.017668 .095809 3.01434 .016471 3.252354-.009976 3.450699 .003247 3.609375Z" fill="#b70000" fill-opacity=".3"/>
<path transform="matrix(1,0,0,-1,29.876099,31.111329)" d="M2.353691 1.255219C3.213186 .964313 4.191689 1.083321 4.892507 1.599018 5.104075 1.51968 5.289198 1.427119 5.434651 1.308112 4.218135 .250273 2.419808-.000964 .991724 1.109767 .489251 1.466788 .185122 1.982483 0 2.537849 .119007 2.590741 .251237 2.617188 .39669 2.617188 .846272 1.969261 1.547089 1.493233 2.353691 1.255219Z" fill="#b70000" fill-opacity=".1"/>
<path transform="matrix(1,0,0,-1,29.519013,31.515625)" d="M.357022 2.949219C.542144 2.393853 .846273 1.878158 1.348747 1.521137 2.77683 .397183 4.575158 .661641 5.791673 1.71948 5.897457 1.640142 5.976793 1.560803 6.029685 1.468242 5.236306 .516186 3.940453 .000491 2.697492 .17239 1.375192 .43685 .515697 1.388906 0 2.499638 .026446 2.724429 .158678 2.869881 .357022 2.949219Z" fill="#b70000" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,31.176667,29.810547)" d="M.206783 .583078C.140668 .63597 .061331 .688861 .021662 .768199-.018007 .847537-.004785 .953322 .074553 1.006214 .140668 1.045883 .220005 1.03266 .299343 1.006214 .68281 .90043 1.0795 .794647 1.462967 .688863 1.833211 .583079 2.26957 .530186 2.626591 .37151 2.904274 .252503 2.428246 .067382 2.296016 .054159 1.952218 .01449 1.595197 .040936 1.264622 .120274 .894378 .199612 .524135 .358287 .206783 .583078Z" fill="#b70000" fill-opacity=".2"/>
<path transform="matrix(1,0,0,-1,42.712678,38.70703)" d="M3.414374 5.527324C4.882126 5.077742 5.741621 3.662879 5.344932 2.314134 4.882127 .740598 3.467267 .304243 1.986292 .64804 .505317 .991838-.327732 2.525705 .121849 3.887674 .544985 5.209973 2.012736 5.950459 3.414374 5.527324Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,18.657715,30.998047)" d="M5.14894 2.182772C4.739027 .516675 3.178714 .318329 2.041537 .648904 .534116 1.098485-.338602 2.486899 .124203 3.954652 .573785 5.395958 1.829969 6.017438 3.363836 5.541411 4.858034 5.078606 5.611744 3.663747 5.14894 2.182772Z" fill="#ed6e83"/>
<path transform="matrix(1,0,0,-1,44.546327,27.050782)" d="M.813808 1.965025C1.39562 1.713788 1.898091 .827846 1.699746 .457602 1.514624 .100582 1.184049 .074136 .840251 .206366 .311332 .417934-.151473 1.277428 .046872 1.687341 .231994 2.070808 .536125 2.123701 .813808 1.965025Z" fill="#b20623"/>
<path transform="matrix(1,0,0,-1,25.118958,20.634766)" d="M.45962 .202411C.221606 .294972-.148638 .427202 .06293 .797445 .406728 1.418926 1.054653 1.564378 1.689356 1.511486 2.033154 1.48504 2.297615 1.233805 2.165385 .810669 2.046378 .400756 1.067877 .043735 .45962 .202411Z" fill="#b20623"/>
<path transform="matrix(1,0,0,-1,35.61728,18.751954)" d="M.949577 .608065C.870239 .290714 .685117 .026254 .341319 .0527-.108263 .079146 .010745 .462611 .023968 .753517 .063637 1.229545 .129748 1.705573 .169417 2.181601 .195863 2.446061 .275201 2.670853 .566107 2.697299 .883459 2.723745 .936353 2.472507 .989245 2.22127 1.068583 1.930364 1.042138 .965086 .949577 .608065Z" fill="#dd1f37"/>
<path transform="matrix(1,0,0,-1,37.53076,19.501954)" d="M1.693932 1.847299C1.482365 1.239042 1.032783 .789461 .636093 .30021 .503863 .141534 .331964 .088642 .173288 .207649 .014612 .326656-.038276 .511778 .027839 .696899 .265853 1.31838 .636094 1.860522 1.046007 2.376219 1.165014 2.534895 1.40303 2.667125 1.614598 2.508449 1.839389 2.33655 1.77327 2.085313 1.693932 1.847299Z" fill="#dd1f37"/>
<path transform="matrix(.2922,-.9564,-.9564,-.2922,29.54367,27.721478)" d="M2.049564 1.767305C3.181507 1.767305 4.099128 2.448121 4.099128 3.28795 4.099128 4.127778 3.181507 4.808594 2.049564 4.808594 .917621 4.808594 0 4.127778 0 3.28795 0 2.448121 .917621 1.767305 2.049564 1.767305Z" fill="#040500"/>
<path transform="matrix(.2922,-.9564,-.9564,-.2922,27.652865,26.311707)" d="M.991724 .849103C1.539439 .849103 1.983449 1.18063 1.983449 1.58959 1.983449 1.99855 1.539439 2.330078 .991724 2.330078 .44401 2.330078 0 1.99855 0 1.58959 0 1.18063 .44401 .849103 .991724 .849103Z" fill="#fdfcf5"/>
<path transform="matrix(.2922,-.9564,-.9564,-.2922,43.990907,31.571087)" d="M2.049564 1.767303C3.181507 1.767303 4.099128 2.448121 4.099128 3.28795 4.099128 4.127778 3.181507 4.808594 2.049564 4.808594 .917621 4.808594 0 4.127778 0 3.28795 0 2.448121 .917621 1.767303 2.049564 1.767303Z" fill="#040500"/>
<path transform="matrix(.2922,-.9564,-.9564,-.2922,42.087495,30.161316)" d="M.991724 .849103C1.539439 .849103 1.983449 1.180632 1.983449 1.589592 1.983449 1.998552 1.539439 2.330078 .991724 2.330078 .44401 2.330078 0 1.998552 0 1.589592 0 1.180632 .44401 .849103 .991724 .849103Z" fill="#fdfcf5"/>
</svg>
