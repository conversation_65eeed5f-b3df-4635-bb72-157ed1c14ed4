<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Payment</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .form-container {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .form-row {
            margin-bottom: 16px;
        }
        
        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }
        
        .StripeElement {
            background: white;
            padding: 12px 16px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .StripeElement--focus {
            border-color: #6772e5;
            box-shadow: 0 0 0 1px #6772e5;
        }
        
        .StripeElement--invalid {
            border-color: #e25950;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #6772e5;
            box-shadow: 0 0 0 1px #6772e5;
        }
        
        .form-input.error {
            border-color: #e25950;
        }
        
        .error-message {
            color: #e25950;
            font-size: 13px;
            margin-top: 4px;
            min-height: 16px;
        }
        
        .pay-button {
            width: 100%;
            background: #6772e5;
            color: white;
            border: none;
            padding: 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
        }
        
        .pay-button:disabled {
            background: #aab7c4;
            cursor: not-allowed;
        }
        
        .pay-button:hover:not(:disabled) {
            background: #5469d4;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <form id="payment-form">
            <div class="form-row">
                <label for="cardholder-name">Cardholder Name</label>
                <input 
                    type="text" 
                    id="cardholder-name" 
                    class="form-input"
                    placeholder="John Doe"
                    required
                />
                <div class="error-message" id="cardholder-name-errors"></div>
            </div>
            
            <div class="form-row">
                <label for="card-number-element">Card Number</label>
                <div id="card-number-element"></div>
                <div class="error-message" id="card-number-errors"></div>
                <!-- 
                TEST CARDS:
                ✅ Success: ****************
                ❌ Declined: ****************
                ❌ Insufficient funds: ****************
                ❌ Incorrect CVC: ****************
                ❌ Incorrect ZIP: ****************
                ❌ Processing error: ****************
                -->
            </div>
            
            <div style="display: flex; gap: 16px;">
                <div class="form-row" style="flex: 1;">
                    <label for="card-expiry-element">MM / YY</label>
                    <div id="card-expiry-element"></div>
                    <div class="error-message" id="card-expiry-errors"></div>
                </div>
                
                <div class="form-row" style="flex: 1;">
                    <label for="card-cvc-element">CVC</label>
                    <div id="card-cvc-element"></div>
                    <div class="error-message" id="card-cvc-errors"></div>
                </div>
            </div>
            
            <button type="submit" id="submit-button" class="pay-button">
                Pay Now
            </button>
            
            <div class="loading" id="loading">
                Processing payment...
            </div>
            
            <div class="error-message" id="card-errors"></div>
        </form>
    </div>

    <script>
        // Stripe instance sẽ được khởi tạo từ Flutter
        let stripe;
        let elements;
        let cardNumber, cardExpiry, cardCvc;
        let clientSecret;

        // Function để initialize Stripe (được gọi từ Flutter)
        function initializeStripe(publishableKey, clientSecretParam) {
            console.log('🔧 Initializing Stripe with:', {
                publishableKey: publishableKey ? '***' + publishableKey.slice(-4) : 'null',
                clientSecret: clientSecretParam ? '***' + clientSecretParam.slice(-4) : 'null'
            });
            
            stripe = Stripe(publishableKey);
            elements = stripe.elements();
            clientSecret = clientSecretParam;
            
            // Tạo card elements
            const style = {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
            };
            
            cardNumber = elements.create('cardNumber', {style});
            cardExpiry = elements.create('cardExpiry', {style});
            cardCvc = elements.create('cardCvc', {style});
            
            cardNumber.mount('#card-number-element');
            cardExpiry.mount('#card-expiry-element');
            cardCvc.mount('#card-cvc-element');
            
            // Error handling
            cardNumber.on('change', ({error}) => {
                showError('card-number-errors', error);
            });
            
            cardExpiry.on('change', ({error}) => {
                showError('card-expiry-errors', error);
            });
            
            cardCvc.on('change', ({error}) => {
                showError('card-cvc-errors', error);
            });
            
            // Cardholder name validation
            const cardholderNameInput = document.getElementById('cardholder-name');
            cardholderNameInput.addEventListener('blur', () => {
                const name = cardholderNameInput.value.trim();
                if (!name) {
                    cardholderNameInput.classList.add('error');
                    showError('cardholder-name-errors', {message: 'Cardholder name is required'});
                } else if (name.length < 2) {
                    cardholderNameInput.classList.add('error');
                    showError('cardholder-name-errors', {message: 'Please enter a valid name'});
                } else {
                    cardholderNameInput.classList.remove('error');
                    showError('cardholder-name-errors', null);
                }
            });
            
            cardholderNameInput.addEventListener('input', () => {
                if (cardholderNameInput.value.trim()) {
                    cardholderNameInput.classList.remove('error');
                    showError('cardholder-name-errors', null);
                }
            });
            
            // Form submission
            document.getElementById('payment-form').addEventListener('submit', handleSubmit);
        }

        function showError(elementId, error) {
            const errorElement = document.getElementById(elementId);
            if (error) {
                errorElement.textContent = error.message;
            } else {
                errorElement.textContent = '';
            }
        }

        async function handleSubmit(event) {
            event.preventDefault();
            
            // Validate cardholder name
            const cardholderName = document.getElementById('cardholder-name').value.trim();
            if (!cardholderName) {
                showError('cardholder-name-errors', {message: 'Cardholder name is required'});
                return;
            }
            if (cardholderName.length < 2) {
                showError('cardholder-name-errors', {message: 'Please enter a valid name'});
                return;
            }
            
            // Validate card elements
            const cardNumberComplete = cardNumber._complete;
            const cardExpiryComplete = cardExpiry._complete;
            const cardCvcComplete = cardCvc._complete;
            
            if (!cardNumberComplete || !cardExpiryComplete || !cardCvcComplete) {
                showError('card-errors', {message: 'Please complete all card details'});
                return;
            }
            
            const submitButton = document.getElementById('submit-button');
            const loading = document.getElementById('loading');
            
            submitButton.disabled = true;
            loading.style.display = 'block';
            
            // Clear previous errors
            showError('card-errors', null);
            
            try {
                console.log('🚀 Starting Stripe payment confirmation...');
                
                const {error, paymentIntent} = await stripe.confirmCardPayment(clientSecret, {
                    payment_method: {
                        card: cardNumber,
                        billing_details: {
                            name: cardholderName,
                        }
                    }
                });
                
                // Detailed logging for debugging
                console.log('📊 Stripe Response:', {
                    error: error,
                    paymentIntent: paymentIntent,
                    hasError: !!error,
                    paymentIntentStatus: paymentIntent?.status
                });
                
                if (error) {
                    console.log('❌ Stripe Error Details:', {
                        code: error.code,
                        type: error.type,
                        message: error.message,
                        decline_code: error.decline_code,
                        param: error.param
                    });
                    
                    // Show error in UI
                    showError('card-errors', error);
                    
                    // Phân loại lỗi chi tiết hơn
                    let errorCategory = 'unknown';
                    let userFriendlyMessage = error.message;
                    
                    // Phân loại theo decline_code
                    if (error.decline_code) {
                        switch (error.decline_code) {
                            case 'card_declined':
                                errorCategory = 'card_declined';
                                userFriendlyMessage = 'Thẻ bị từ chối. Vui lòng thử thẻ khác.';
                                break;
                            case 'insufficient_funds':
                                errorCategory = 'insufficient_funds';
                                userFriendlyMessage = 'Thẻ không đủ tiền. Vui lòng kiểm tra số dư.';
                                break;
                            case 'incorrect_cvc':
                                errorCategory = 'incorrect_cvc';
                                userFriendlyMessage = 'Mã CVC không đúng. Vui lòng kiểm tra lại.';
                                break;
                            case 'incorrect_zip':
                                errorCategory = 'incorrect_zip';
                                userFriendlyMessage = 'Mã ZIP không đúng. Vui lòng kiểm tra lại.';
                                break;
                            case 'processing_error':
                                errorCategory = 'processing_error';
                                userFriendlyMessage = 'Lỗi xử lý thanh toán. Vui lòng thử lại sau.';
                                break;
                            case 'expired_card':
                                errorCategory = 'expired_card';
                                userFriendlyMessage = 'Thẻ đã hết hạn. Vui lòng sử dụng thẻ khác.';
                                break;
                            case 'incorrect_number':
                                errorCategory = 'incorrect_number';
                                userFriendlyMessage = 'Số thẻ không đúng. Vui lòng kiểm tra lại.';
                                break;
                            default:
                                errorCategory = 'card_declined';
                                userFriendlyMessage = 'Thẻ bị từ chối. Vui lòng thử thẻ khác.';
                        }
                    } else if (error.code) {
                        // Phân loại theo error code
                        switch (error.code) {
                            case 'card_declined':
                                errorCategory = 'card_declined';
                                userFriendlyMessage = 'Thẻ bị từ chối. Vui lòng thử thẻ khác.';
                                break;
                            case 'expired_card':
                                errorCategory = 'expired_card';
                                userFriendlyMessage = 'Thẻ đã hết hạn. Vui lòng sử dụng thẻ khác.';
                                break;
                            case 'incorrect_cvc':
                                errorCategory = 'incorrect_cvc';
                                userFriendlyMessage = 'Mã CVC không đúng. Vui lòng kiểm tra lại.';
                                break;
                            case 'incorrect_number':
                                errorCategory = 'incorrect_number';
                                userFriendlyMessage = 'Số thẻ không đúng. Vui lòng kiểm tra lại.';
                                break;
                            case 'invalid_expiry_month':
                            case 'invalid_expiry_year':
                                errorCategory = 'invalid_expiry';
                                userFriendlyMessage = 'Ngày hết hạn không đúng. Vui lòng kiểm tra lại.';
                                break;
                            case 'invalid_cvc':
                                errorCategory = 'invalid_cvc';
                                userFriendlyMessage = 'Mã CVC không hợp lệ. Vui lòng kiểm tra lại.';
                                break;
                            default:
                                errorCategory = 'validation_error';
                                userFriendlyMessage = error.message;
                        }
                    }
                    
                    // Send detailed error info to Flutter
                    sendMessageToFlutter('error', {
                        errorCode: error.code,
                        errorMessage: error.message,
                        errorType: error.type,
                        declineCode: error.decline_code || null,
                        param: error.param || null,
                        paymentIntentId: paymentIntent?.id || null,
                        status: paymentIntent?.status || 'validation_error',
                        errorCategory: errorCategory,
                        userFriendlyMessage: userFriendlyMessage
                    });
                    
                } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                    console.log('✅ Payment succeeded:', paymentIntent.id);
                    
                    // Payment thành công
                    sendMessageToFlutter('success', {
                        paymentIntentId: paymentIntent.id,
                        status: paymentIntent.status,
                        created: paymentIntent.created,
                        amount: paymentIntent.amount,
                        currency: paymentIntent.currency,
                        payment_method: paymentIntent.payment_method,
                        cardholderName: cardholderName
                    });
                    
                } else if (paymentIntent) {
                    console.log('⚠️ Payment Intent with non-succeeded status:', paymentIntent.status);
                    
                    // Payment Intent tồn tại nhưng status không phải succeeded
                    sendMessageToFlutter('error', {
                        errorCode: 'payment_intent_not_succeeded',
                        errorMessage: `Payment status: ${paymentIntent.status}`,
                        errorType: 'payment_intent_error',
                        paymentIntentId: paymentIntent.id,
                        status: paymentIntent.status
                    });
                } else {
                    console.log('❌ No payment intent returned');
                    
                    // Không có payment intent
                    sendMessageToFlutter('error', {
                        errorCode: 'no_payment_intent',
                        errorMessage: 'No payment intent returned',
                        errorType: 'unknown_error',
                        paymentIntentId: null,
                        status: 'unknown'
                    });
                }
                
            } catch (jsError) {
                console.error('💥 JavaScript Error:', jsError);
                
                // JavaScript error hoặc network error
                sendMessageToFlutter('error', {
                    errorCode: 'javascript_error',
                    errorMessage: jsError.message || 'Unknown error occurred',
                    errorType: 'exception',
                    paymentIntentId: null,
                    status: 'exception'
                });
            } finally {
                submitButton.disabled = false;
                loading.style.display = 'none';
            }
        }

        function sendMessageToFlutter(type, data) {
            // Send message to Flutter WebView
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler('paymentResult', {
                    type: type,
                    data: data
                });
            } else {
                console.error('flutter_inappwebview not available');
            }
        }

        // Test function
        function testConnection() {
            sendMessageToFlutter('test', 'WebView connection working');
        }
    </script>
</body>
</html>
