{"hello": "Hello", "language": "Language", "home": "Home", "setting": "Setting", "note": "Note", "featured": "Featured", "seeMore": "See more", "order": "Order", "message": "Message", "cancel": "Cancel", "notInterested": "Not Interested?", "swipeLeftNoInterest": "Swiping left means you're not interested.", "askMore": "Ask More", "swipeRightNoInterest": "Swiping right means you want to chat with them.", "business": "Business", "request": "Request", "readMore": "Read More", "readLess": "Read Less", "whatDoYouWantToDo": "What do you want to sell", "postBusinessOpportunity": "Post business opportunity", "offer": "Offer", "thisIsAn": "This is an", "title": "Title", "graphicDesigner": "Graphic Designer, 2D Animation, ....", "businessNeeds": "Business Needs", "description": "Description", "shortDescription": "Eg: Short description, Main tasks I perform, Skills and experience, Work environment, etc.", "stateYourNeeds": "Eg: State your needs or offer, Industry/sector, Required skills/experience, Describe what you are looking for in a potential candidate or partner", "budgetInvestmentUSD": "Budget/Investment (USD)", "uploadAvatar": "Upload Avatar", "optional": "(Optional)", "uploadAPhoto": "Upload a job photo. This photo will appear in the list for others to see.", "addMore": "Add More", "postDisplayDuration": "Post Display Duration", "dayAgo": "day ago", "age": "Age", "distance": "Distance", "skip": "<PERSON><PERSON>", "post": "Post", "maleText": "Male", "preferredGender": "Preferred Gender", "femaleText": "Female", "otherText": "Other", "save": "Save", "dating": "Dating", "likeYou": "Like You", "matches": "Matches", "editBio": "Edit <PERSON>io", "editSeeking": "Edit Seeking", "camera": "Camera", "gallery": "Gallery", "aboutYou": "About You", "displayName": "Display Name", "name": "Name", "yearOfBirth": "Year of Birth", "youYearOfBirth": "Your Year of Birth", "yourOccupation": "Your Occupation", "shortBio": "Short Bio", "yourBio": "Your Bio", "addYourPhotos": "Add Your Photos", "addAtLeast2": "Add at least 2 photos, you can change them later", "continueButton": "Continue", "likedYou": "Liked You", "youLikedThem": "You liked them", "theyLikedYou": "They liked you", "reviewThePeople": "Review the people you liked", "today": "Today", "reviewThePeople2": "Review the people who liked you", "reviewThePeopleYouHaveSwipedLeftOn": "Review the people you have swiped left on.", "yesterday": "Yesterday", "itsAMatch": "It's a match!", "startAConversationNow": "Start a conversation now.", "startChatting": "Start chatting", "keepSwiping": "Keep swiping", "recent": "Recent", "whoAreYouSeeking": "Who are you seeking?", "recentlyActive": "Recently Active", "welcomeDatingMode": "Welcome Dating Mode", "ruleBeYourselfTitle": "Be Yourself.", "ruleBeYourselfDescription": "Make sure your photos, age, and bio are true to who you are", "ruleStaySafeTitle": "Stay Safe.", "ruleStaySafeDescription": "Don't be too quick to give out personal information.", "ruleStaySafeLinkText": "Date Safely", "rulePlayItCoolTitle": "Play It Cool", "rulePlayItCoolDescription": "Respect others and treat them as you would like to be treated.", "ruleBeProactiveTitle": "Be Proactive.", "ruleBeProactiveDescription": "Always report bad behavior.", "iAgreeButtonText": "I Agree", "followHouseRulesText": "Please follow these House Rules", "addNewCardTitle": "Add New Card", "cardDetailsTitle": "Card details", "cardNumberLabel": "Card number", "cardNumberHint": "0000-0000-0000-0000", "expiryDateLabel": "Expiry date", "expiryDateHint": "MM/YY", "cvvLabel": "CVV", "cvvHint": "CVV", "cardholderNameLabel": "Cardholder's name", "cardholderNameHint": "Name on card", "verificationTransaction": "Verification transaction", "verificationTransactionText": " (It will be refunded to your card immediately after the verification)", "confirmButtonText": "Confirm", "checkoutTitle": "Checkout", "warningMessage": "Before making an order, make sure the address is correct and matches your current address.", "orderDetailsTitle": "Order Details", "orderDetailsHint": "Your order", "paymentOptionsTitle": "Payment Options", "paymentDetailsTitle": "Payment Details", "totalCost": "Total cost", "paypal": "<PERSON><PERSON>", "visaCard": "Visa card", "paymentFailed": "Payment Failed", "paymentFailedMessage": "There was an error processing your payment. Please try again later.", "locationLabel": "Location", "addNewCard": "Add New Card", "orderID": "Order ID", "orderDate": "Order date", "productPrice": "Product price", "voucher": "Voucher", "serviceFee": "Service fee", "termsAndConditions": "By pressing Pay, you agree to the ", "termsAndConditionsLink": "terms and conditions of Multi.Me.", "confirmOrder": "Confirm Order", "dateText": "dd/MM/yyyy", "personalInformation": "Personal Information", "fullName": "Full Name", "phone": "Phone", "addressLabel": "Address", "addressHint": "Your address", "specificAddressLabel": "Specific address", "specificAddressHint": "Specific address", "mapPlaceholder": "Map Placeholder", "setDefaultCheckboxLabel": "Set as default address", "saveChangeButton": "Save Change", "liked": "Liked", "marketplace": "Marketplace", "newText": "News", "mostView": "MostView", "mostViewed": "Most Viewed", "likeNew": "Like New", "adsPlaceholder": "Ads Placeholder", "productName": "Product Name", "shopAddress": "Shop Address", "postDate": "Post Date", "price": "Price", "orderPage": "Order Page", "messagePage": "Message Page", "cancelOrder": "Cancel order", "payment": "Payment", "totalPayment": "Total payment", "subtotal": "Subtotal", "orderShipped": "Your order had started shipping", "orderPlaced": "You placed the order", "deliveredTo": "Delivered to", "sentFrom": "<PERSON>t from", "awaitingApproval": "Awaiting approval", "productDetails": "Product Details", "quantity": "Quantity", "sellerInfo": "<PERSON><PERSON>", "contactSeller": "Contact seller", "shippingInfo": "Shipping Info", "orderFailed": "Order failed", "orderFailureMessage": "There is something wrong with your order.", "orderCheckMessage": "Please go back and check your order again thoroughly.", "returnOrder": "Return my order", "orderConfirmed": "Order Confirmed", "thankYouMessage": "Thank you for your order. You will receive email confirmation shortly.", "checkOrderStatus": "Check the status of your order in Your Cart.", "continueShopping": "Continue shopping", "addNewAddress": "Add New Address", "postProduct": "Post Product", "productHintText": "Product name (Minimum 50 and maximum 150 words)", "statusText": "Status", "productFeatures": "Outstanding features of the product, what it includes, how long it has been used...", "statusProduct": "Choose your product’s status", "backText": "Back", "buyNow": "Buy Now", "reportSeller": "Report is Seller", "noRecommendSeller": "Don't recommend this seller", "noMoreProfile": "No more profiles!", "selectPostsFrom": "Select posts from", "offersFrom": "Offers from multi.me", "payFee": "Pay fee", "apply": "Apply", "freePosting": "Free posting", "adType": "Type: Regular ad\nApplies to first personal ads/month", "upgradeToPro": "Upgrade to pro", "postFreeAds": "Post free ads", "adTypeAds": "Type: Regular - Permanent Ads", "appliesToAllPosts": "Applies to all posts", "multiMe": "Multi.Me ", "doesNot": "DOES NOT", "allowDepositPrivate": "allow: Depositing/Private transfers/Off-platform transactions/Recruiting collaborators/Giving free gifts/Providing contact information or Canceling orders at the seller's request.", "pleaseOnlyBuySell": "Please only Buy/Sell on Multi.Me to avoid being scammed. Multi.Me will collect and use chat history according to Multi.Me's Privacy Policy.", "gotUnusedItem": "Got unused items?", "turnThemIntoCash": "Turn them into cash!", "postForSale": "Post for Sale", "itsTotally": "It's totally", "safe": "safe", "toPurchaseAtMultiMe": "to purchase at Multi.Me. Your money will only be sent to the seller after you confirm receiving the product; otherwise, it will be returned to you.", "aboutThisItem": "About this item", "condition": "Condition", "type": "Type", "brand": "Brand", "show": "Show", "hide": "<PERSON>de", "estDelivery": "Est. Delivery: ", "safePurchase": "It’s totally safe to purchase at Multi.Me. Your money will only be sent to the seller after your confirmation on receiving the product, else it will return to you.", "chargeRateInfo": "There will be a 3% charge rate for purchases > $1000\nThere will be a 5% charge rate for purchases < $999", "thereWill": "There will be a", "chargeRate": "charge rate for purchases", "defaultText": "<PERSON><PERSON><PERSON>", "createDate": "Create Date", "shippingPrice": "Includes shipping price", "contactText": "Please contact me if you have any question, want to discuss the price or you want a better offer.", "contactMe": "Contact Me", "moreLike": "More like this", "viewAll": "View All", "postMe": "Post Me", "postNews": "Post News", "newsTitle": "News Title", "category": "Category", "country": "Country", "articleSource": "Article Source", "content": "Content", "copyContent": "Copy content from Article here", "addCoverPhoto": "+ \nAdd Cover Photo", "selectCategory": "Select Category", "reviewPost": "Review your posts", "tabAll": "All", "tabSports": "Sports", "tabPolitics": "Politics", "tabBusiness": "Business", "tabHealth": "Health", "tabTravel": "Travel", "tabScience": "Science", "readAndShare": "Read and share news", "createCommunity": "🙌 We don't create news, we create a community where people share and discuss news, helping each other develop critical thinking and multi-faceted understanding", "hotNew": "Hot News", "history": "History", "createPost": "Create Post", "latest": "Latest", "about": "About", "skill": "Skills", "education": "Education", "experience": "Experience", "notPost": "Not Post Yet", "subNewPost": "Try subscribing to a new post of yours...", "notReview": "Not Reviews Yet", "thanksStrongBody": "Your order means a lot to the StrongBody community.Order now to write the first review here.", "productAndService": "Explore products and services", "newService": "New Service", "notService": "Not Services Yet", "sellerCreateService": "<PERSON><PERSON> hasn't created any services yet. If she sold something it would be here.", "noProduct": "Not Product Yet", "noProductDescription": "<PERSON><PERSON> not sold any products yet. If she does in the future, the products will appear here.", "activeShop": "Active StrongBodyShop here →", "add": "Add", "fullTime": "Full Time", "toYear": "To Year", "companyBusiness": "Company / business name", "fromYear": "From year", "positionCompany": "Position in the company", "addressCountry": "Address / Country", "addSkill": "<PERSON><PERSON> (e.g. Voice Talent)", "year": "Year", "uploadImage": "Upload 430x190 px images for more impressions and sales opportunities.", "displayNameLabel": "Display Name", "displayNameHint": "Please name your account", "yourProfession": "Your Profession", "yourProfessionHint": "Example 1: Board-certified Dermatologist specializing in cosmetic procedures and skin cancer treatments. Example 2: Certified Personal Trainer focusing on weight loss and strength training for all fitness levels.", "professionalLabel": "Your Professional Title", "professionalHint": "Ex: Personal Trainer / Therapist / Dentist", "contentProfile": "Please introduce yourself. Consider including:\n- Your experience in the field\n- Key achievements or certifications\n- Your approach to patient care or work methodology\n- Any specializations or areas of expertise", "addLanguage": "Add Language", "levelLanguage": "Level Language", "englishText": "English", "frenchText": "French", "basic": "Basic", "conversational": "Conversational", "completionRate": "Completion Rate", "settingAccount": "Setting Account", "myAccount": "My account", "password": "Password", "verifyAccount": "Verify account", "notification": "Notification", "privacy": "Privacy", "syncSetting": "Sync Setting", "deleteAccount": "Delete account", "turnOnNotificationsForAllModes": "Turn on notifications for all modes", "socialMode": "Social mode", "manageYourConnection": "Manage your connections", "businessMode": "Business Mode", "connectToPartnerChances": "Connect to partner, chances", "newsMode": "News Mode", "readNews": "Read news", "marketPlace": "Marketplace", "shoppingProductsAndService": "Shopping products and service", "datingMode": "Dating Mode", "findRealPartnerForYou": "Find real partner for you", "strongBodyAi": "StrongBody.ai", "darkMode": "Dark Mode", "selectMode": "Select eye protection mode", "yourInvisible": "Your activity will be invisible", "settingMode": "Setting Mode", "policy": "Policy", "yourActivityInvisible": "Your activity will be invisible", "helpCenterTitle": "Help center", "helpCenterDescription": "Everything you need to know", "shoppingCartTitle": "Shopping Cart", "shoppingCartDescription": "Manage your saved orders at MarketPlace, Network.", "shakeToExploreTitle": "Shake to explore nearby", "shakeToExploreDescription": "Switch to shake to explore", "balanceTitle": "Balance", "balanceDescription": "You can withdraw money to your own account", "sellerModeTitle": "<PERSON><PERSON>", "sellerModeDescription": "Switch to service selling", "logOut": "Log out", "textAndCall": "Text and Call", "showViewedStatus": "Show 'Viewed' status", "allowMessaging": "Allow messaging", "allowCall": "Allow call", "diary": "Diary", "allowViewingAndCommenting": "Allow viewing and commenting", "friendSource": "Friend Source", "suggestFriendsFromContacts": "Suggest friends from contacts", "addContactsToMultiMe": "Add contacts to MultiMe when both have each other's numbers saved on their devices", "on": "On", "everybody": "Everybody", "friend": "Friend", "phoneNumberTitle": "What is Your \nPhone Number ?", "phoneNumberDescription": "This information helps us verify your account to \nmake it more secure.", "mobileNumber": "Mobile Number", "enterMobileNumber": "Enter your mobile number", "forgotPassword": "Forgot Password ?", "emailLabel": "Email", "emailHint": "<PERSON><PERSON>", "alreadyMember": "Already a member?", "signUp": "Sign Up", "pickFriendsTitle": "Pick 10 best friends & clients", "inviteFriendMessage": "Invite a friend to continue", "searchContactsHint": "Search your contacts", "importContactsTitle": "Import your contacts", "importContactsMessage": "Strong<PERSON><PERSON> never texts friends on your behalf", "importButton": "Import", "minPasswordCriteria": "Min 8 characters, 1 uppercase, 1 lowercase, 1 number", "confirmPasswordHint": "Confirm password", "signInTitle": "Sign in", "rememberMe": "Remember me", "notAMember": "Not a member yet?", "userName": "User Name", "publicName": "Public Username", "occupation": "Occupation", "enterOccupation": "Enter Occupation", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy policy", "toData": " to learn how we use your personal data.", "andOur": " and to occasionally receive emails from us. Please read our ", "byJoinStrongBody": "By joining, you agree to the StrongBody ", "verifyAccountTitle": "Check your email to Verify account", "verifyAccountMessage": "We have sent you a link to confirm. Please check your email to complete your registration.", "backToSignIn": "Back to Sign in", "emailVerifiedTitle": "Email identified successfully", "emailVerifiedMessage": "You have successfully verified the phone number for this account.", "phoneVerifiedTitle": "Phone number identified successfully", "phoneVerifiedMessage": "You have successfully verified the phone number for this account.", "blockUser": "Block", "from": "From", "imageText": "Image", "createPostTitle": "Create Post", "postHintText": "Create a moment... What's on your mind?", "postFirstTimeMessage": "👋 Welcome to MultiMe! Introduce yourself and share something interesting about you or what brings you here today. Share your knowledge and stories to build trust and expand the community.", "postFirstTimeSubTitle": "Post your first article", "strongBody": "StrongBody", "social": "Social", "record": "Record", "backgroundColor": "Background Color", "checkIn": "Check In", "postDetail": "Post Detail", "getStarted": "Get Started", "send": "Send", "resonate": "Resonate", "nameProduct": "Name Product", "descriptionOption": "Description (Optional)", "shareIssue": "Share more details about this issue", "ifImmediately": "If you know someone is in physical danger, please contact your local law enforcement agency immediately.", "postRequest": "Post Request", "supportImage": "Supports image formats .jpg, .jpeg with a size under", "fiveMB": "5MB", "uploadPhotoBrowse": "Upload a photo of the job. This photo will appear on listings for people to browse", "uploadPhoto": "Upload Photo", "egPartner": "Eg. State your Needs or Offerings, Industry/Field,Skills/Experience Required, Describe what you're looking for in a potential candidate or partner", "graphic": "Graphic Designer, 2D Animation, ....", "headerLogin": "Login to your account", "headerLoginText1": "If you have", "headerLoginText2": "StrongBody", "headerLoginText3": "account, use it on multi.me app", "invalidateEmail": "This email is not registered.", "invalidatePassword": "This password is invalid.", "cantEmpty": "{label} cannot be empty", "report": "Report", "login": "Log In", "orLoginWith": "Or", "dontHaveAnAccount": "Don't have an account?", "already": "Already a member?", "resetPassword": "Reset password", "resetPasswordText": "Enter the email associated with your account and we'll send an email with instructions to reset your password.", "emailAddress": "Email Address", "sendEmail": "Send Instructions", "checkEmail": "Check your email", "checkEmailText": "We have sent a password recover instructions to your email.", "openEmail": "Open email app", "createPassword": "Create new password", "createPasswordText": "Your new password must be different from previous used passwords.", "confirmPassword": "Confirm New Password", "confirmPasswordText": "Both passwords must match", "spamEmail": "Did not receive the email? Check your spam filter, or ", "spamEmailLink": "try another email address.", "passwordNotMatch": "Password does not match", "uploadImageBussines": "Upload Image", "Whatisyouroccupation": "What is your occupation?", "JobProfile": "Job Profile", "dayago": "day ago", "weekago": "week ago", "SelectaTimeRange": "Select a time range", "SeekingMarketingExpertForTechStartup": "Eg: Seeking Marketing Expert for Tech Startup", "Field_Industry": "Field/Industry", "General": "General", "AvatarImage": "Avatar image", "Display_name": "Display name", "Occupation": "Occupation", "YouAreAllowedToChange": "You are allowed to change only once.", "AccountDeactivation": "Account Deactivation", "WhatHappensWhenYou": "What happens when you deactivate your account?", "yourProfileGigsHidden": "Your profile and Gigs won't be shown on Strongbody anymore.", "activeOrdersCancelled": "Active orders will be cancelled.", "cantReactivateGigs": "You won't be able to re-activate your Gigs.", "SettingBusinessMode": "Setting Business Mode", "Postoffer": "Post offer", "ShareOpportunities": "Share Opportunities", "RequestSupport": "Request Support", "GetNotifiedWhenYouMatch": "Get notified when you match", "GetNotifiedWhenIHaveSomething": "Get notified when i have something news", "SettingDatingMode": "Setting Dating Mode", "EditprofileDatingmode": "Edit profile Dating mode", "Selecteyeprotectionmode": "Select eye protection mode", "Managewhoicanview": "Manage who i can view", "Commentanonymous": "Comment anonymous", "Onlypeopleyoulikecanfindyou": "Only people you like can find you", "Notification": "Notification", "SettingMarketplance": "Setting Marketplace", "ShareYourProducts": "Share Your Product", "SettingNewMode": "Setting News Mode", "Customarticle": "Custom article", "Changestyle": "Change style, color, text", "Reviewlikedarticles": "Review liked articles", "Reviewyourposts": "Review your post", "SettingSocialMode": "Setting Social Mode", "PostRequest": "Post Request", "Editprofile": "Edit profile", "Previewprofile": "Preview profile", "ReferralProgram": "Referral Program", "Friends": "Friends", "morepeopletoget": "more people to get free\nvoice translations for one month.", "Add": "Add", "Messages": "Messages", "SocialMode": "Social Mode", "BusinessMode": "Business Mode", "Shoppingproducts": "Shopping products", "Serviceforyouglobally": "Service for you globally", "MyOrder": "My Order", "ToPay": "To Pay", "ToShip": "To Ship", "ToReceive": "To Receive", "ToRate": "To Rate", "VoiceTranslatePlan": "Voice Translate Plan", "benefitsOfSubscribing": "Benefits of subscribing:", "Unlimitedaccesstovoice": "Unlimited access to voice translation tools.", "Instanttranslationformessaging": "Instant translation for messaging and calls.", "Cancelyoursubscriptionanytime": "Cancel your subscription anytime.", "Subscribe": "Subscribe", "ViewallourUpgrades": "View all our Upgrades", "Support": "Support", "HelpCenter": "Help Center", "ChatwithMultime": "Chat with <PERSON>me", "SwitchtoSeller": "Switch to <PERSON><PERSON>", "Activefor6Months": "Active for 6 Months", "MyProfile": "My Profile", "Home": "Home", "Order": "Order", "Message": "Message", "Me": "Me", "Request": "Request", "Readmore": "Read more", "Readless": "Read less", "Clickheretouploadimage": "Click here to upload image", "Supportsimageformats": "Supports image formats .jpg, .jpeg with a size under 5MB", "Balance": "Balance", "Availablefunds": "Available funds", "ActiveService": "Active Service", "ActiveProduct": "Active Product", "Showingresults15of": "Showing results 1–5 of", "Showingresults1": "Showing results 1", "Viewall": "View all", "Addacreditcard": "Add a credit card", "Addanewcreditcard": "Add a new credit card", "AddanewPayPalaccount": "Add a new PayPal account", "AddanewPayoneeraccount": "Add a new Payoneer account", "Addanewpayoutmethod": "Add a new payout method", "Cardtype": "Card type", "Verificationtransaction": "Verification transaction", "Verifyfeedialogue": "(It will be refunded to your card immediately after the verification)", "Paymentmethod": "Payment method", "Showing": "Showing", "Results": "results", "Waitingtransaction": "Waiting transaction", "Withdrawalsuccessful": "<PERSON><PERSON><PERSON> successful", "Withdrawalfailed": "<PERSON><PERSON><PERSON> failed", "Withdrawwarningdialogue": "You can withdraw once every 24 hours, with up to 48-hour waiting period before money goes through to your account.", "Subscriptionsummary": "Subscription summary", "Linkcopiedtoclipboard": "Link copied to clipboard", "Returntobalance": "Return to balance", "Saveimage": "Save image", "Tryagain": "Try again", "Waitingtransactiondialogue": "Wait a moment, the system is verifying.", "Youhavesuccessfullywithdrawn": "You have successfully withdrawn", "Toyourvisadebitaccount": "to your visa debit account.", "Somethingwentwrong": "Something went wrong.", "Transactiondate": "Transaction date", "TransactionID": "Transaction ID", "Method": "Method", "Managepayoutmethod": "Manage payout method", "Carddetails": "Card details", "Cardnumber": "Card number", "Expirydate": "Expiry date", "Cardholdername": "Cardholder’s name", "Deletepayoutmethod": "Delete payout method", "Creditcard": "Credit card", "Withdrawntodate": "Withdrawn to date:", "Withdraw": "Withdraw", "AsaSeller": "As a Seller", "AsaBuyer": "As a Buyer", "PaymentBeingCleared": "Payment Being Cleared", "OrdersisActiveNow": "Orders is Active Now", "ServicesareActiveNow": "Services are Active Now", "ProductsareActiveNow": "Products are Active Now", "Payment": "Payment", "Chooseawithdrawmethod": "Choose a withdraw method", "Withdrawamount": "Withdraw amount", "Maximumwithdrawamountis": "Maximum withdraw amount is", "Permissiondenied": "Permission denied. Storage access is required.", "Unabletosaveimage": "Unable to save image. Please try again.", "ImagesavedtoDownloads": "Image saved to Downloads:", "Imagesavedat": "Image saved at:", "Therewasanerror": "There was an error occured while saving the image.", "Pleasenotethatyou": "Please note that you need to be over 18 years old.", "Reviewthepeople": "Review the people have liked you", "Yes": "Yes", "Dontinterested": "Don't interested", "Block": "Block", "Theywillnotbe": "They will not be able to see your profile and cannot message you in multi.me", "ReportThisDatingProfile": "Report this dating profile", "YouCanReportThisDating": "You can report this Dating profile after selecting a reason", "InTheDatingSection": "in the dating section", "View": "View", "ThisAppWillHaveAccess": "This app will have access to the photos you select", "Done": "Done", "ShowSelected": "Show Selected", "Interests": "Interests", "More": "More", "SubmitASupportRequest": "Submit a Support Request", "Connected": "Connected", "Views": "Views", "TotalPostsAcrossAllPlatforms": "Total posts across all platforms", "MyAddresses": "My Addresses", "CreditDebitCard": "Credit / Debit Card", "NotificationSetting": "Notification Setting", "ConnectWithSuggestedAccounts": "Connect with suggested accounts", "InviteAfriendToContinue": "Invite a friend to continue", "SearchYourContacts": "Search your contacts", "ContactsUsingPhoneNumber": "Contacts using phone number", "AutoAddFirend": "Auto add friend", "AutomaticallyAddContactsAsFriends": "Automatically add contacts as friends", "OK": "OK", "AddYourContact": "Add your contact", "CoppyLink": "Copy Link", "CopyLinkToShare": "Copy link to share", "or": "or", "PickBestFriendsClients": "Pick 10 best friends & clients", "ImportYourContacts": "Import your contacts", "StrongbodyNeverTexts": "Strong<PERSON><PERSON> never texts friends on your behalf", "Import": "Import", "SKIP": "SKIP", "CreateNewService": "Create New Service", "CreatedServiceCompleted": "Created Service Completed", "ShareYourServices": "Share your services with people, they will thank you for it", "OrCopyLink": "Or Copy Link", "CreateYourService": "Create Your Service", "Medical": "Medical", "Nutrition": "Nutrition", "Physical": "Physical", "Mental": "Mental", "Pharmacy": "Pharmacy", "Beauty": "Beauty", "Child": "Child", "Family": "Family", "Elderly": "Elderly", "Medical Travel": "Medical Travel", "MedSupport": "MedSupport", "Freelance Digital Work": "Freelance Digital Work", "Freelance Physical Work": "Freelance Physical Work", "Freelance Creative Work": "Freelance Creative Work", "Freelance Administrative Work": "Freelance Administrative Work", "Freelance Technical Work": "Freelance Technical Work", "Massage": "Massage", "CategoryServices": "Category", "certificate": "Certificate", "field_optional": "(Field Optional)", "certificate_description": "We recommend uploading your certificates and qualifications to build customer trust and provide a basis for regulatory authorities. You can upload these documents later. Please note that you will only be able to receive customer payments once you have uploaded all necessary certificates and qualifications.", "supported_file_types": "Supported file types: JPEG, PNG, PDF, and PNG", "add_file": "Add file", "self_declared_info": "This information will be displayed as \"Self-declared\" on your profile.", "ServiceDescription": "Service description", "service_image": "Upload files ", "upload_images_for_impressions": "Upload images for more impressions and sales opportunities.", "square_product_photo": "Square product photo size.", "include_at_least_two_images": "You should include at least 2 images to fully represent your services.", "service_name": "Service name", "starting_price": "Starting Price", "service_description": "Service description", "time_to_complete_order": "Time to complete the order", "related_questions": "Related questions", "help_center": "Help Center", "account": "Account", "privacy_and_security": "Privacy And Security", "business_mode": "Business Mode", "dating_mode": "Dating Mode", "news": "News", "support_request": "Support Request", "help_center_request_description": "We're here to help with any issues you may have! Submit a support request! As long as your request doesn't get lost in the portal, we'll get back to you shortly.", "submit": "Submit", "help_center_search_prompt": "Need help? Type in the keyword you're searching for.", "no_search_results": "Sorry, we couldn’t find any matching results :(", "help_topics": "Help topics", "order_support": "Order Support", "help_center_greeting": "Hello, how can we assist you?", "help_center_description": "We’re here to help with answers to your questions and popular topics.", "your_photo": "Your photo", "order_id": "Order ID", "my_account_from_being_hacked": "How do I protect my account from being hacked?", "get_notified_about_suspicious_activity": "How do I get notified about suspicious activity on my account?", "report_or_block_posts_i_dont_like": "How do I report or block posts I don't like?", "how_to_like_and_react_to_posts": "How to 'Like' and 'React' to posts?", "how_to_send_a_message": "How to send a message?", "how_to_block_or_report_a_post": "How to block or report a post?", "how_to_protect_my_account_from_being_hacked": "To protect your account, use a strong password, don't share your password with others, and enable two-factor authentication.", "how_to_get_notified_about_suspicious_activity": "You can set up notifications about suspicious activity in the “Security” section of your account settings.", "how_to_report_or_block_posts_i_dont_like": "You can block or report content you don't like. This lets us know you don't want to see more posts like this.", "how_to_block_or_report_a_post_1": "1. <PERSON>lick on the icon on the right side of the post.", "how_to_block_or_report_a_post_2": "2. Then click block or report and confirm again.", "follow_these_steps": " To block or report the post, follow these steps:", "When_you_click_Like": "When you click Like below a post, you tell people you like it without commenting. Like comments, anyone who can see the post can see that you liked it.", "When_you_like_something": "When you like something, it tells us what other things you want to see.", "How_to_like_something": "How to like something?", "Go_to_the_post": "1. Go to the post or photo.", "ClickLike": "2. <PERSON><PERSON>.", "How_to_unlike_something": "How to unlike something? You can only unlike posts, photos, and comments that you previously liked.", "post_or_photo": "1. Go to the post or photo.", "ClickUnlike": "2. <PERSON><PERSON>.", "only_send_messages": "You can only send messages when both members use the right swipe feature to show interest in each other. It’s a match!.", "RecommendedArticles": "Recommended articles", "SUCCESS": "SUCCESS", "Thank_you_for_your_request": "Thank you for your request. \nWe are working hard to find the best service and deals for you. ", "Continue": "Continue", "Click_here_to_upload_file": "Click here to upload file", "Supports_image_formats": "Supports image formats .png .jpg .jpeg with a size under ", "orderDetails": "Order Details", "reviewOrder": "Review Order", "serviceDetail": "Service Detail", "selectPaymentMethod": "Select payment method", "orderSummary": "Order summary", "acceptAndPay": "Accept & Pay", "details": "Details", "viewInChat": "View in chat", "orderStatusTimeline": "Order Status Timeline", "editMore": "Edit more", "orderReceive": "Order receive", "notifyMe": "Notify me", "addToCalendar": "Add to calendar", "deliveryDate": "Delivery date", "itTotally": "It's totally", "toPurchaseAt": "comprar en", "yourMoneyWill": "Your money will only be sent to the seller after your confirmation on receiving the product, else it will return to you.", "review": "Review", "resolutionRegarding": "Resolution Regarding the Buyer", "theMoneyWill": "The money will be automatically refunded to the buyer's wallet after 3 days", "method": "Method", "transactionDate": "Transaction Date", "transactionCode": "Transaction Code", "error_required": "{field} cannot be left blank", "error_maxLength": "{field} cannot exceed {max} characters", "error_emoji": "{field} cannot contain emoji characters", "error_email_invalid": "Your email address is not valid. Please re-enter.", "error_password_invalid": "Min 8 characters, 1 uppercase, 1 lowercase, 1 number", "error_password_mismatch": "Password do not match", "error_phone_invalid": "Phone number is not valid", "error_special_characters": "Invalid characters in {field}. Please remove special symbols.", "passwordChanged": "Password changed", "search": "Search", "fieldOptional": "(Field Optional)", "supportedFileTypes": "Supported file types: JPEG, PNG, PDF, and PNG", "addFile": "Add file", "selfDeclaredInfo": "This information will be displayed as \"Self-declared\" on your profile.", "certificateDescription": "We recommend uploading your certificates and qualifications to build customer trust and provide a basis for regulatory authorities. You can upload these documents later. Please note that you will only be able to receive customer payments once you have uploaded all necessary certificates and qualifications.", "whoCanTextingYou": "Who can texting you?", "describeTheReason": "Describe the reason", "dataSynchronization": "Data synchronization", "backupOptions": "Backup options", "everyday": "Everyday", "TitleOfRequest": "Title of request", "DescriptionOfRequest": "Please describe your needs and issues in detail, along with your bugget"}