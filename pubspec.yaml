name: multime_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+4

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  dots_indicator: ^2.1.0
  flutter_card_swiper: ^7.0.2
  flutter_screenutil: ^5.9.3
  auto_size_text: ^3.0.0
  bloc: ^9.0.0
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.17
  flutter_bloc: ^9.0.0
  equatable: ^2.0.7
  shared_preferences: ^2.3.3
  dio: ^5.7.0
  flutter_cache_manager: ^3.4.1
  image_picker: ^1.1.2
  json_annotation: ^4.9.0
  intl: ^0.19.0
  readmore: ^3.0.0
  dotted_border: ^2.0.0+1
  flutter_dotenv: ^5.1.0
  flutter_quill: ^10.8.5
  smooth_page_indicator: ^1.2.0+3
  lottie: ^2.4.0
  easy_localization: ^3.0.7
  gap: ^3.0.1
  pinput: ^5.0.0
  flutter_launcher_icons: ^0.11.0
  carousel_slider: ^5.0.0
  cupertino_onboarding: ^1.3.0
  flutter_sound: ^9.28.0
  permission_handler: ^11.3.1
  get_it: ^8.0.3
  cached_network_image: ^3.3.1
  flutter_contacts: ^1.1.9+2
  go_router: ^14.6.3
  country_code_picker: ^3.1.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_rating_bar: ^4.0.1
  supabase_flutter: ^2.8.3
  google_sign_in: ^6.2.2
  screenshot: ^3.0.0
  path_provider: ^2.1.5
  image_cropper: ^9.0.0
  speech_to_text: ^7.0.0
  webview_flutter: ^4.10.0
  flutter_inappwebview: ^6.0.0
  google_maps_flutter: ^2.10.1
  geocoding: ^3.0.0
  timeago: ^3.7.0
  rename: ^3.0.2
  local_auth: ^2.3.0
  url_launcher: ^6.3.1
  share_plus: ^11.0.0
  shimmer: ^3.0.0
  onesignal_flutter: ^5.0.0
  device_info_plus: ^11.3.0
  flutter_stripe: 11.4.0
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.1
  cloud_firestore: ^5.6.5
  flutter_facebook_auth: ^7.1.2
  pubnub: ^5.2.0
  flutter_image_compress: ^2.4.0
  # twitter_login: ^4.4.2
  sign_in_with_apple: ^7.0.1
  crypto: ^3.0.6
  file_picker: ^10.2.0
  record: ^6.0.0
  audio_waveforms: ^1.3.0
  bcrypt: ^1.1.3
  html: ^0.15.6
  scroll_to_index: ^3.0.1
  flutter_widget_from_html: ^0.16.0
  connectivity_plus: ^6.1.4
  pusher_channels_flutter: ^2.4.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.13
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/image/
    - assets/image/social/
    - assets/svg/marketplace_icon/
    - assets/profile/
    - assets/sign_img/
    - assets/svg/
    - assets/svg/social/
    - assets/svg/dating_icon/
    - assets/svg/new_icon/
    - assets/svg/bottom_bar/
    - assets/svg/setting_icon/
    - assets/svg/busines_icon/
    - assets/aimation/
    - assets/translations/
    - assets/logoMultime/
    - .env.dev
    - assets/svg/onboarding_start_icon/
    - assets/svg/profile_buyer_icon/
    - assets/svg/help_center_icon/
    - assets/svg/admin_user_icon/
    - assets/svg/strongBodyAi/
    - assets/svg/shopping_cart/
    - assets/svg/over_view_icon/
    - assets/svg/message/
    - assets/settings/
    - assets/svg/create_service_icon/
    - assets/svg/balance_icon/
    - assets/svg/my_order_mode/
    - assets/image/order_mode/
    - assets/image/message/
    - assets/image/image.png
    - assets/image/strongBodyAi/
    - assets/svg/language_svg/
    - assets/language/
    - assets/sticker/
    - assets/image/SBPromoSlider/
    - assets/stripe_card_form.html

  fonts:
    - family: Bold
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Bold.ttf
    - family: BoldItalic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-BoldItalic.ttf
    - family: ExtraBold
      fonts:
        - asset: assets/fonts/PlusJakartaSans-ExtraBold.ttf
    - family: ExtraBoldItalic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-ExtraBoldItalic.ttf
    - family: ExtraLight
      fonts:
        - asset: assets/fonts/PlusJakartaSans-ExtraLight.ttf
    - family: ExtraLightItalic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-ExtraLightItalic.ttf
    - family: Italic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Italic.ttf
    - family: Light
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Light.ttf
    - family: LightItalic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-LightItalic.ttf
    - family: MediumItalic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-MediumItalic.ttf
    - family: SemiBoldItalic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-SemiBoldItalic.ttf
    - family: Medium
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Medium.ttf
    - family: Regular
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Regular.ttf
    - family: SemiBold
      fonts:
        - asset: assets/fonts/PlusJakartaSans-SemiBold.ttf

flutter_icons:
  android: true
  ios: true
  image_path: "assets/logoMultime/logoMultime.png" # Đường dẫn tới file icon của bạn
  min_sdk_android: 21
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
