.PHONY: help setup dev prod build-dev build-prod clean info

# Default target
help:
	@echo "🔧 Multime Environment Manager"
	@echo "=============================="
	@echo ""
	@echo "Available commands:"
	@echo "  make setup      - Initial environment setup"
	@echo "  make dev        - Run development environment"
	@echo "  make prod       - Run production environment"
	@echo "  make build-dev  - Build development APK"
	@echo "  make build-prod - Build production APK"
	@echo "  make clean      - Clean project"
	@echo "  make info       - Show environment info"
	@echo ""

# Initial setup
setup:
	@echo "🔧 Setting up environment..."
	./scripts/setup_env.sh

# Development environment
dev:
	@echo "🔧 Running development environment..."
	./scripts/env.sh dev

# Production environment  
prod:
	@echo "🚀 Running production environment..."
	./scripts/env.sh prod

# Build development
build-dev:
	@echo "🔨 Building development APK..."
	./scripts/env.sh build-dev

# Build production
build-prod:
	@echo "🔨 Building production APK..."
	./scripts/env.sh build-prod

# Clean project
clean:
	@echo "🧹 Cleaning project..."
	./scripts/env.sh clean

# Show environment info
info:
	@echo "ℹ️  Environment information..."
	./scripts/env.sh info

# Quick build for both platforms
build-all-dev:
	@echo "🔨 Building development for all platforms..."
	./scripts/build.sh dev both debug

build-all-prod:
	@echo "🔨 Building production for all platforms..."
	./scripts/build.sh prod both release

# iOS specific builds
ios-dev:
	@echo "🍎 Building iOS development..."
	./scripts/build.sh dev ios debug

ios-prod:
	@echo "🍎 Building iOS production..."
	./scripts/build.sh prod ios release

# Android specific builds
android-dev:
	@echo "🤖 Building Android development..."
	./scripts/build.sh dev android debug

android-prod:
	@echo "🤖 Building Android production..."
	./scripts/build.sh prod android release
