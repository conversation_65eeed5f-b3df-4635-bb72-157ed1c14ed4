#!/bin/bash

# Environment setup script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

echo "🔧 Environment Setup for Multime App"
echo "====================================="

# Check if .env files exist
print_info "Checking environment files..."

if [ ! -f ".env.dev" ]; then
    print_error ".env.dev not found!"
    exit 1
else
    print_status ".env.dev ✅"
fi

if [ ! -f ".env.prod" ]; then
    print_warning ".env.prod not found"
    print_info "Creating .env.prod from template..."
    
    if [ -f ".env.prod.example" ]; then
        cp .env.prod.example .env.prod
        print_status "Created .env.prod from template"
        print_warning "Please edit .env.prod and add your production keys!"
        echo ""
        echo "Required production keys to update:"
        echo "- SUPABASE_URL"
        echo "- SUPABASE_ANON_KEY" 
        echo "- STRIPE_PUBLISHABLE_KEY"
        echo "- GOOGLE_MAPS_API_KEY"
        echo "- FIREBASE_PROJECT_ID"
        echo ""
    else
        print_error ".env.prod.example template not found!"
        exit 1
    fi
else
    print_status ".env.prod ✅"
fi

# Validate environment files
print_info "Validating environment files..."

validate_env_file() {
    local file=$1
    local env_name=$2
    
    if [ ! -f "$file" ]; then
        print_error "$file not found"
        return 1
    fi
    
    # Check required keys
    required_keys=("APP_NAME" "API_BASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY" "STRIPE_PUBLISHABLE_KEY")
    missing_keys=()
    
    for key in "${required_keys[@]}"; do
        if ! grep -q "^$key=" "$file" || grep -q "^$key=$" "$file" || grep -q "^$key=your_" "$file"; then
            missing_keys+=("$key")
        fi
    done
    
    if [ ${#missing_keys[@]} -eq 0 ]; then
        print_status "$env_name environment: All required keys present ✅"
    else
        print_warning "$env_name environment: Missing or incomplete keys: ${missing_keys[*]}"
        if [ "$env_name" = "Production" ]; then
            print_info "Please update .env.prod with your actual production values"
        fi
    fi
}

validate_env_file ".env.dev" "Development"
validate_env_file ".env.prod" "Production"

# Install dependencies
print_info "Installing Flutter dependencies..."
flutter pub get

if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully!"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Setup gitignore for environment files
print_info "Setting up git ignore for environment files..."

# Check if .gitignore exists and add environment rules
if [ -f ".gitignore" ]; then
    if ! grep -q ".env.prod" .gitignore; then
        echo "" >> .gitignore
        echo "# Environment files" >> .gitignore
        echo ".env.prod" >> .gitignore
        echo ".env.*.backup" >> .gitignore
        echo ".env.local" >> .gitignore
        print_status "Added environment files to .gitignore"
    else
        print_status ".gitignore already configured for environment files"
    fi
fi

echo ""
print_status "🎉 Environment setup completed!"
echo ""
echo "Next steps:"
echo "1. Update .env.prod with your production keys"
echo "2. Run development: ./scripts/run.sh dev"
echo "3. Run production: ./scripts/run.sh prod"
echo ""
echo "Available commands:"
echo "- ./scripts/run.sh [dev|prod]"
echo "- ./scripts/build.sh [dev|prod] [android|ios|both] [debug|release]"
