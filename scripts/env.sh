#!/bin/bash

# Quick environment switch script

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[ENV]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Show current environment info
show_env_info() {
    local env_file=$1
    if [ -f "$env_file" ]; then
        print_info "Environment file: $env_file"
        echo "📱 App Name: $(grep "^APP_NAME=" $env_file | cut -d'=' -f2)"
        echo "🌐 API URL: $(grep "^API_BASE_URL=" $env_file | cut -d'=' -f2)"
        echo "🔐 Debug Mode: $(grep "^DEBUG_MODE=" $env_file | cut -d'=' -f2)"
        echo ""
    fi
}

# Quick commands
case "$1" in
    "dev")
        print_status "Switching to Development environment..."
        show_env_info ".env.dev"
        flutter run --target lib/main_dev.dart --flavor dev
        ;;
    "prod")
        print_status "Switching to Production environment..."
        show_env_info ".env.prod"
        flutter run --target lib/main_prod.dart --flavor prod
        ;;
    "info")
        print_info "Environment Information:"
        echo ""
        echo "🔧 Development Environment:"
        show_env_info ".env.dev"
        echo "🚀 Production Environment:"
        show_env_info ".env.prod"
        ;;
    "build-dev")
        print_status "Building Development APK..."
        flutter build apk --target lib/main_dev.dart --flavor dev --debug
        ;;
    "build-prod")
        print_status "Building Production APK..."
        flutter build apk --target lib/main_prod.dart --flavor prod --release
        ;;
    "clean")
        print_status "Cleaning project..."
        flutter clean
        flutter pub get
        ;;
    *)
        echo "🔧 Multime Environment Manager"
        echo "=============================="
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  dev        - Run in development environment"
        echo "  prod       - Run in production environment"
        echo "  build-dev  - Build development APK"
        echo "  build-prod - Build production APK"
        echo "  info       - Show environment information"
        echo "  clean      - Clean and reinstall dependencies"
        echo ""
        echo "Examples:"
        echo "  $0 dev              # Run development"
        echo "  $0 prod             # Run production"
        echo "  $0 info             # Show env info"
        ;;
esac
