#!/bin/bash

# Run script for different environments

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

# Check if environment is provided
if [ -z "$1" ]; then
    echo -e "${YELLOW}Usage:${NC} ./scripts/run.sh [dev|prod] [device_id]"
    echo "Example: ./scripts/run.sh dev"
    echo "Example: ./scripts/run.sh prod iPhone"
    exit 1
fi

ENVIRONMENT=$1
DEVICE=${2:-}

print_status "Running app in $ENVIRONMENT environment..."

if [ -n "$DEVICE" ]; then
    flutter run --target lib/main_$ENVIRONMENT.dart --flavor $ENVIRONMENT -d $DEVICE
else
    flutter run --target lib/main_$ENVIRONMENT.dart --flavor $ENVIRONMENT
fi
