#!/bin/bash

# Build script for different environments

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment is provided
if [ -z "$1" ]; then
    print_error "Please specify environment: dev or prod"
    echo "Usage: ./scripts/build.sh [dev|prod] [android|ios|both] [debug|release]"
    echo "Example: ./scripts/build.sh dev android debug"
    exit 1
fi

ENVIRONMENT=$1
PLATFORM=${2:-both}
BUILD_MODE=${3:-debug}

# Validate environment
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    print_error "Invalid environment. Use 'dev' or 'prod'"
    exit 1
fi

# Check if environment file exists
ENV_FILE=".env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    print_error "Environment file $ENV_FILE not found!"
    if [ "$ENVIRONMENT" = "prod" ]; then
        print_warning "Copy .env.prod.example to .env.prod and fill in your production values"
    fi
    exit 1
fi

# Validate platform
if [[ "$PLATFORM" != "android" && "$PLATFORM" != "ios" && "$PLATFORM" != "both" ]]; then
    print_error "Invalid platform. Use 'android', 'ios', or 'both'"
    exit 1
fi

# Validate build mode
if [[ "$BUILD_MODE" != "debug" && "$BUILD_MODE" != "release" ]]; then
    print_error "Invalid build mode. Use 'debug' or 'release'"
    exit 1
fi

print_status "Building for environment: $ENVIRONMENT"
print_status "Platform: $PLATFORM"
print_status "Build mode: $BUILD_MODE"

# Clean previous builds
print_status "Cleaning previous builds..."
flutter clean
flutter pub get

# Build Android
if [[ "$PLATFORM" == "android" || "$PLATFORM" == "both" ]]; then
    print_status "Building Android ($ENVIRONMENT)..."
    
    if [ "$BUILD_MODE" == "release" ]; then
        flutter build apk --target lib/main_$ENVIRONMENT.dart --flavor $ENVIRONMENT --release
        flutter build appbundle --target lib/main_$ENVIRONMENT.dart --flavor $ENVIRONMENT --release
    else
        flutter build apk --target lib/main_$ENVIRONMENT.dart --flavor $ENVIRONMENT --debug
    fi
    
    if [ $? -eq 0 ]; then
        print_status "Android build completed successfully!"
    else
        print_error "Android build failed!"
        exit 1
    fi
fi

# Build iOS
if [[ "$PLATFORM" == "ios" || "$PLATFORM" == "both" ]]; then
    print_status "Building iOS ($ENVIRONMENT)..."
    
    if [ "$BUILD_MODE" == "release" ]; then
        flutter build ios --target lib/main_$ENVIRONMENT.dart --flavor $ENVIRONMENT --release
    else
        flutter build ios --target lib/main_$ENVIRONMENT.dart --flavor $ENVIRONMENT --debug
    fi
    
    if [ $? -eq 0 ]; then
        print_status "iOS build completed successfully!"
    else
        print_error "iOS build failed!"
        exit 1
    fi
fi

print_status "Build process completed!"
