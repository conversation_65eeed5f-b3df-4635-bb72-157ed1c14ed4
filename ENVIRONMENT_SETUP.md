# Environment Configuration Guide

Dự án này đã được cấu hình để hỗ trợ 2 môi trường: **Development (dev)** và **Production (prod)** sử dụng file `.env` để quản lý keys và configuration.

## Cấu trúc môi trường

### Development Environment (`.env.dev`)
- **Package Name**: `com.multime.app.dev`
- **App Name**: `Multime Dev`
- **Configuration**: Sử dụng test/development keys
- **Debug Mode**: Enabled

### Production Environment (`.env.prod`)
- **Package Name**: `com.multime.app`
- **App Name**: `Multime`
- **Configuration**: Sử dụng live/production keys
- **Debug Mode**: Disabled

## Thiết lập ban đầu

### 1. Chạy script setup
```bash
./scripts/setup_env.sh
```

### 2. Cấu hình production environment
```bash
# Copy template và chỉnh sửa
cp .env.prod.example .env.prod
# Sau đó edit .env.prod với production keys thật
```

### 3. Cấu trúc file .env

**.env.dev** (Development):
```bash
APP_NAME=Multime Dev
API_BASE_URL=https://dev-api.multime.com
SUPABASE_URL=https://your-dev-supabase.supabase.co
SUPABASE_ANON_KEY=your_dev_supabase_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_test_key
DEBUG_MODE=true
```

**.env.prod** (Production):
```bash
APP_NAME=Multime
API_BASE_URL=https://api.multime.com
SUPABASE_URL=https://your-prod-supabase.supabase.co
SUPABASE_ANON_KEY=your_prod_supabase_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_key
DEBUG_MODE=false
```

## Cách sử dụng

### 1. Setup ban đầu
```bash
# Sử dụng script setup
./scripts/setup_env.sh

# Hoặc dùng Makefile
make setup
```

### 2. Chạy ứng dụng

```bash
# Development environment
./scripts/env.sh dev
make dev

# Production environment
./scripts/env.sh prod
make prod

# Xem thông tin environment
./scripts/env.sh info
make info
```

### 3. Build ứng dụng

```bash
# Build development
./scripts/env.sh build-dev
make build-dev

# Build production
./scripts/env.sh build-prod
make build-prod

# Build tất cả platform
make build-all-dev
make build-all-prod
```

### 4. Platform specific builds

```bash
# iOS only
make ios-dev
make ios-prod

# Android only
make android-dev
make android-prod
```

## Cấu hình môi trường

### Environment Files
Tất cả cấu hình môi trường được quản lý trong các file `.env`:

- `.env.dev` - Development environment
- `.env.prod` - Production environment (cần tạo từ template)
- `.env.prod.example` - Template cho production

### Sử dụng trong code

```dart
import 'package:multime_app/core/config/env.dart';

// Truy cập environment variables
String apiUrl = Env.apiBaseUrl;
String appName = Env.appName;
bool isDebug = Env.isDebug;

// Log environment info
Env.logEnvironmentInfo();
```

### Thêm cấu hình mới

1. Thêm vào file `.env.dev` và `.env.prod`:
```bash
NEW_API_KEY=your_new_api_key
```

2. Cập nhật `AppConfig` trong `environment.dart`:
```dart
static String get newApiKey => _getEnvVar('NEW_API_KEY');
```

3. Thêm vào `Env` helper class:
```dart
static String get newApiKey => AppConfig.newApiKey;
```

## Tiện ích phát triển

### Environment Widgets
Sử dụng các widget để hiển thị thông tin environment:

```dart
import 'package:multime_app/core/widgets/environment_widgets.dart';

// Wrap app với banner (chỉ hiện ở dev)
EnvironmentBanner(
  child: MyApp(),
)

// Hiển thị thông tin environment
EnvironmentInfo()

// Debug panel với thông tin chi tiết
DebugEnvironmentPanel()
```

### Quick Commands

```bash
# Environment manager
./scripts/env.sh [command]

# Available commands:
./scripts/env.sh dev        # Run development
./scripts/env.sh prod       # Run production  
./scripts/env.sh info       # Show env info
./scripts/env.sh build-dev  # Build dev APK
./scripts/env.sh build-prod # Build prod APK
./scripts/env.sh clean      # Clean project

# Makefile shortcuts
make dev                    # Run development
make prod                   # Run production
make info                   # Show environment info
make build-dev              # Build development
make build-prod             # Build production
make clean                  # Clean project
```

## File Structure

```
├── .env.dev                    # Development environment
├── .env.prod                   # Production environment
├── .env.prod.example           # Production template
├── Makefile                    # Make commands
├── lib/
│   ├── main_dev.dart          # Development entry point
│   ├── main_prod.dart         # Production entry point
│   └── core/
│       ├── config/
│       │   ├── environment.dart    # Environment configuration
│       │   ├── env.dart           # Environment helper
│       │   └── env_validator.dart # Environment validation
│       └── widgets/
│           └── environment_widgets.dart # Environment UI widgets
└── scripts/
    ├── setup_env.sh           # Environment setup
    ├── env.sh                 # Environment manager
    ├── build.sh               # Build script
    └── run.sh                 # Run script
```

## Lưu ý quan trọng

1. **Cập nhật production keys**: Nhớ cập nhật Supabase URL, Stripe keys và API URLs cho production trong `environment.dart`
2. **iOS Schemes**: Cần tạo schemes trong Xcode để build iOS
3. **Signing**: Cấu hình signing riêng cho từng environment
4. **App Icons**: Có thể tạo icons khác nhau cho dev và prod để dễ phân biệt

## Troubleshooting

### Android build issues
- Xóa folder `build/` và chạy `flutter clean`
- Kiểm tra Android SDK và build tools

### iOS build issues
- Mở Xcode và kiểm tra signing
- Chạy `pod install` trong folder ios/
- Kiểm tra iOS deployment target

### Environment không đúng
- Kiểm tra file main_dev.dart và main_prod.dart
- Xác nhận AppConfig.setEnvironment() được gọi đúng
