#!/usr/bin/env ruby

require 'xcodeproj'

project_path = 'Runner.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Get the main target
target = project.targets.find { |t| t.name == 'Runner' }
test_target = project.targets.find { |t| t.name == 'RunnerTests' }

# Create new build configurations for dev and prod flavors
debug_config = project.build_configurations.find { |c| c.name == 'Debug' }
release_config = project.build_configurations.find { |c| c.name == 'Release' }

# Create Debug-dev configuration
debug_dev_config = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
debug_dev_config.name = 'Debug-dev'
debug_dev_config.build_settings = debug_config.build_settings.dup
project.build_configurations << debug_dev_config

# Create Debug-prod configuration  
debug_prod_config = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
debug_prod_config.name = 'Debug-prod'
debug_prod_config.build_settings = debug_config.build_settings.dup
project.build_configurations << debug_prod_config

# Create Release-dev configuration
release_dev_config = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
release_dev_config.name = 'Release-dev'
release_dev_config.build_settings = release_config.build_settings.dup
project.build_configurations << release_dev_config

# Create Release-prod configuration
release_prod_config = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
release_prod_config.name = 'Release-prod'
release_prod_config.build_settings = release_config.build_settings.dup
project.build_configurations << release_prod_config

# Update build configuration lists for main target
target_debug_dev = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
target_debug_dev.name = 'Debug-dev'
target_debug_dev.base_configuration_reference = project.files.find { |f| f.path == 'Flutter/Debug-dev.xcconfig' }
target_debug_dev.build_settings = target.build_configurations.find { |c| c.name == 'Debug' }.build_settings.dup

target_debug_prod = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
target_debug_prod.name = 'Debug-prod'
target_debug_prod.base_configuration_reference = project.files.find { |f| f.path == 'Flutter/Debug-prod.xcconfig' }
target_debug_prod.build_settings = target.build_configurations.find { |c| c.name == 'Debug' }.build_settings.dup

target_release_dev = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
target_release_dev.name = 'Release-dev'
target_release_dev.base_configuration_reference = project.files.find { |f| f.path == 'Flutter/Release-dev.xcconfig' }
target_release_dev.build_settings = target.build_configurations.find { |c| c.name == 'Release' }.build_settings.dup

target_release_prod = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
target_release_prod.name = 'Release-prod'
target_release_prod.base_configuration_reference = project.files.find { |f| f.path == 'Flutter/Release-prod.xcconfig' }
target_release_prod.build_settings = target.build_configurations.find { |c| c.name == 'Release' }.build_settings.dup

# Add configurations to target
target.build_configurations << target_debug_dev
target.build_configurations << target_debug_prod  
target.build_configurations << target_release_dev
target.build_configurations << target_release_prod

# Add configurations to test target if it exists
if test_target
  test_debug_dev = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
  test_debug_dev.name = 'Debug-dev'
  test_debug_dev.build_settings = test_target.build_configurations.find { |c| c.name == 'Debug' }.build_settings.dup
  
  test_debug_prod = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
  test_debug_prod.name = 'Debug-prod'
  test_debug_prod.build_settings = test_target.build_configurations.find { |c| c.name == 'Debug' }.build_settings.dup
  
  test_release_dev = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
  test_release_dev.name = 'Release-dev'
  test_release_dev.build_settings = test_target.build_configurations.find { |c| c.name == 'Release' }.build_settings.dup
  
  test_release_prod = project.new(Xcodeproj::Project::Object::XCBuildConfiguration)
  test_release_prod.name = 'Release-prod'
  test_release_prod.build_settings = test_target.build_configurations.find { |c| c.name == 'Release' }.build_settings.dup
  
  test_target.build_configurations << test_debug_dev
  test_target.build_configurations << test_debug_prod
  test_target.build_configurations << test_release_dev  
  test_target.build_configurations << test_release_prod
end

project.save

puts "✅ Successfully added flavor configurations to iOS project!"
