<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>382263610736-or1kcvne97gdnb7vl7eve8f0gbljaibb.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.382263610736-or1kcvne97gdnb7vl7eve8f0gbljaibb</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>382263610736-c1p103b9t6j0ffsa0spf18i5qklmkdnl.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCsP8CHuyEMICkDk1TUXFu1s2bGTmsGJ24</string>
	<key>GCM_SENDER_ID</key>
	<string>382263610736</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.multime.app.prod</string>
	<key>PROJECT_ID</key>
	<string>multime23102024</string>
	<key>STORAGE_BUCKET</key>
	<string>multime23102024.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:382263610736:ios:88377e5115ffa5dcf213b5</string>
</dict>
</plist>