import Flutter
import UIKit
import GoogleMaps
import MessageUI

@main
@objc class AppDelegate: FlutterAppDelegate, MFMessageComposeViewControllerDelegate {
  private var messengerChannel: FlutterMethodChannel?

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {

    let controller = window?.rootViewController as! FlutterViewController
    messengerChannel = FlutterMethodChannel(name: "com.multime.app/share",
                                            binaryMessenger: controller.binaryMessenger)

    messengerChannel?.setMethodCallHandler { [weak self] (call, result) in
      switch call.method {
      case "shareToMessenger":
        self?.shareToMessenger(call: call, result: result)
      case "shareToInstagram":
        self?.shareToInstagram(call: call, result: result)
      case "shareToSnapchat":
        self?.shareToSnapchat(call: call, result: result)
      case "shareToSMS":
        self?.shareToSMS(call: call, result: result)
      case "shareToSMSWithPhone":
        self?.shareToSMSWithPhone(call: call, result: result)
      case "shareToSMSWithPhoneFallback":
        self?.shareToSMSWithPhoneFallback(call: call, result: result)
      case "testSMS":
        self?.testSMS(call: call, result: result)
      default:
        result(FlutterMethodNotImplemented)
      }
    }

    GMSServices.provideAPIKey("AIzaSyDgBrAqc1hW7NwA6tLR9P6gcek7cCKlX0o")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Share to Messenger
private func shareToMessenger(call: FlutterMethodCall, result: @escaping FlutterResult) {
    guard let args = call.arguments as? [String: String],
          let text = args["text"] else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing text parameter", details: nil))
        return
    }

    // Format message as a URL (only plain text sharing works with this scheme)
    let urlString = "fb-messenger://share?text=\(text.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")"

    if let url = URL(string: urlString), UIApplication.shared.canOpenURL(url) {
        UIApplication.shared.open(url, options: [:], completionHandler: { success in
            result(success)
        })
    } else {
        // Fallback to App Store if Messenger not installed
        let appStoreURL = "https://apps.apple.com/app/id454638411"
        UIApplication.shared.open(URL(string: appStoreURL)!, options: [:], completionHandler: nil)
        result(false)
    }
}


  // Share to Instagram
  private func shareToInstagram(call: FlutterMethodCall, result: @escaping FlutterResult) {
      guard let text = call.arguments as? [String: String],
            let shareText = text["text"] else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing text parameter", details: nil))
          return
      }

      if let url = URL(string: "instagram://") {
          if UIApplication.shared.canOpenURL(url) {
              let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
              if let rootVC = UIApplication.shared.keyWindow?.rootViewController {
                  rootVC.present(activityVC, animated: true, completion: nil)
                  result(true)
              } else {
                  result(FlutterError(code: "NO_ROOT_VC", message: "Cannot find root view controller", details: nil))
              }
          } else {
              let appStoreURL = "https://apps.apple.com/app/id389801252" // Instagram
              UIApplication.shared.open(URL(string: appStoreURL)!, options: [:], completionHandler: nil)
              result(false)
          }
      }
  }

  // Share to Snapchat
  private func shareToSnapchat(call: FlutterMethodCall, result: @escaping FlutterResult) {
      guard let text = call.arguments as? [String: String],
            let shareText = text["text"] else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing text parameter", details: nil))
          return
      }

      if let url = URL(string: "snapchat://") {
          if UIApplication.shared.canOpenURL(url) {
              let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
              if let rootVC = UIApplication.shared.keyWindow?.rootViewController {
                  rootVC.present(activityVC, animated: true, completion: nil)
                  result(true)
              } else {
                  result(FlutterError(code: "NO_ROOT_VC", message: "Cannot find root view controller", details: nil))
              }
          } else {
              let appStoreURL = "https://apps.apple.com/app/id447188370" // Snapchat
              UIApplication.shared.open(URL(string: appStoreURL)!, options: [:], completionHandler: nil)
              result(false)
          }
      }
  }

  // Share to SMS
  private func shareToSMS(call: FlutterMethodCall, result: @escaping FlutterResult) {
      guard let text = call.arguments as? [String: String],
            let shareText = text["text"] else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing text parameter", details: nil))
          return
      }

      let smsURL = "sms:?body=" + (shareText.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")
      if let url = URL(string: smsURL) {
          if UIApplication.shared.canOpenURL(url) {
              UIApplication.shared.open(url, options: [:], completionHandler: nil)
              result(true)
          } else {
              result(FlutterError(code: "ERROR", message: "Cannot open SMS app", details: nil))
          }
      }
  }

  // Share to SMS with specific phone number
  private func shareToSMSWithPhone(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("shareToSMSWithPhone called")
      
      guard let args = call.arguments as? [String: String],
            let shareText = args["text"],
            let phoneNumber = args["phoneNumber"] else {
          print("Invalid arguments")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing text or phoneNumber parameter", details: nil))
          return
      }

      print("Arguments received - Text: \(shareText), Phone: \(phoneNumber)")

      // Kiểm tra xem thiết bị có thể gửi SMS không
      guard MFMessageComposeViewController.canSendText() else {
          print("SMS not available on this device")
          result(FlutterError(code: "SMS_NOT_AVAILABLE", message: "SMS is not available on this device", details: nil))
          return
      }

      print("SMS is available, proceeding...")

      // Loại bỏ các ký tự không cần thiết từ số điện thoại, nhưng giữ lại dấu +
      let cleanedPhoneNumber = phoneNumber.replacingOccurrences(of: " ", with: "")
                                         .replacingOccurrences(of: "-", with: "")
                                         .replacingOccurrences(of: "(", with: "")
                                         .replacingOccurrences(of: ")", with: "")
                                         .replacingOccurrences(of: ".", with: "")

      print("Original phone number: \(phoneNumber)")
      print("Cleaned phone number: \(cleanedPhoneNumber)")
      print("SMS text: \(shareText)")

      let messageController = MFMessageComposeViewController()
      messageController.messageComposeDelegate = self
      messageController.recipients = [cleanedPhoneNumber]
      messageController.body = shareText

      print("Message controller created")

      // Lấy root view controller để present SMS composer
      var rootViewController: UIViewController?
      
      if #available(iOS 13.0, *) {
          // iOS 13+ scene-based approach
          if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
             let window = windowScene.windows.first {
              rootViewController = window.rootViewController
          }
      } else {
          // iOS 12 và cũ hơn
          rootViewController = UIApplication.shared.keyWindow?.rootViewController
      }
      
      guard let rootVC = rootViewController else {
          print("Cannot find root view controller")
          result(FlutterError(code: "NO_ROOT_VC", message: "Cannot find root view controller", details: nil))
          return
      }
      
      print("Found root view controller")
      
      var topController = rootVC
      while let presentedViewController = topController.presentedViewController {
          topController = presentedViewController
      }
      
      print("Presenting message controller...")
      topController.present(messageController, animated: true, completion: {
          print("Message controller presented successfully")
          result(true)
      })
  }

  // Delegate method cho MessageUI
  func messageComposeViewController(_ controller: MFMessageComposeViewController, didFinishWith result: MessageComposeResult) {
      controller.dismiss(animated: true, completion: nil)
  }

  // Fallback method sử dụng URL scheme
  private func shareToSMSWithPhoneFallback(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("shareToSMSWithPhoneFallback called")
      
      guard let args = call.arguments as? [String: String],
            let shareText = args["text"],
            let phoneNumber = args["phoneNumber"] else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing text or phoneNumber parameter", details: nil))
          return
      }

      // Loại bỏ các ký tự không cần thiết từ số điện thoại, nhưng giữ lại dấu +
      let cleanedPhoneNumber = phoneNumber.replacingOccurrences(of: " ", with: "")
                                         .replacingOccurrences(of: "-", with: "")
                                         .replacingOccurrences(of: "(", with: "")
                                         .replacingOccurrences(of: ")", with: "")
                                         .replacingOccurrences(of: ".", with: "")

      print("Fallback - Original phone: \(phoneNumber)")
      print("Fallback - Cleaned phone: \(cleanedPhoneNumber)")

      let smsURL = "sms:\(cleanedPhoneNumber)?body=" + (shareText.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")
      
      print("SMS URL: \(smsURL)")
      
      if let url = URL(string: smsURL) {
          if UIApplication.shared.canOpenURL(url) {
              UIApplication.shared.open(url, options: [:], completionHandler: { success in
                  print("URL opened with success: \(success)")
                  result(success)
              })
          } else {
              print("Cannot open SMS URL")
              result(FlutterError(code: "ERROR", message: "Cannot open SMS app", details: nil))
          }
      } else {
          print("Invalid SMS URL")
          result(FlutterError(code: "INVALID_URL", message: "Invalid SMS URL: \(smsURL)", details: nil))
      }
  }

  // Test method để kiểm tra SMS functionality
  private func testSMS(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("Testing SMS functionality...")
      
      if MFMessageComposeViewController.canSendText() {
          print("✅ Device can send SMS")
          
          let messageController = MFMessageComposeViewController()
          messageController.messageComposeDelegate = self
          messageController.recipients = ["1234567890"] // Test number
          messageController.body = "Test message"
          
          // Tìm root view controller
          var rootViewController: UIViewController?
          
          if #available(iOS 13.0, *) {
              if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                 let window = windowScene.windows.first {
                  rootViewController = window.rootViewController
              }
          } else {
              rootViewController = UIApplication.shared.keyWindow?.rootViewController
          }
          
          if let rootVC = rootViewController {
              print("✅ Found root view controller")
              var topController = rootVC
              while let presentedViewController = topController.presentedViewController {
                  topController = presentedViewController
              }
              
              topController.present(messageController, animated: true, completion: {
                  print("✅ SMS composer presented successfully")
                  result(true)
              })
          } else {
              print("❌ Cannot find root view controller")
              result(FlutterError(code: "NO_ROOT_VC", message: "Cannot find root view controller", details: nil))
          }
      } else {
          print("❌ Device cannot send SMS")
          result(FlutterError(code: "SMS_NOT_AVAILABLE", message: "SMS is not available on this device", details: nil))
      }
  }

}
