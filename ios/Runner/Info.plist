<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(BUNDLE_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(BUNDLE_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.multime.app</string>
				<string>com.googleusercontent.apps.382263610736-86b2gkrviep4q9n09o4d6bkbaq49fa4s</string>
				<string>fb1666588447358359</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>twitter</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>O1uRmurGJxs2y3WNLBoHMXTDc</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>apple</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>GW6B397VD6</string>
			</array>
		</dict>
	</array>

	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>GIDClientID</key>
	<string>382263610736-86b2gkrviep4q9n09o4d6bkbaq49fa4s.apps.googleusercontent.com</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fb-messenger</string>
		<string>instagram</string>
		<string>snapchat</string>
		<string>sms</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAllowsArbitraryLoads</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>We need access to your camera to take photos/videos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photo library to select images.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>This app uses Face ID to authenticate the user</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>FacebookAppID</key>
	<string>29559386307009064</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>multime</string>

	<key>UISupportedDocumentBrowser</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>NSMicrophoneUsageDescription</key>
    <string>We need access to your microphone to record voice messages</string>
	<key>NSContactsUsageDescription</key>
	<string>We need access to your contacts to help you connect with friends.</string>

	<key>NSSpeechRecognitionUsageDescription</key>
	<string>We use speech recognition to enable voice-based interaction in the app.</string>

	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to suggest relevant content and enhance your experience.</string>

</dict>
</plist>