{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984a94b2c7da66a5a8e1d9f557be214b44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989afe546e87647ad3f07b853e347f6748", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b17fa33d26643dfd67fed21b394b99f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b720a332fcc5a65b1429ed8829a0a560", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b17fa33d26643dfd67fed21b394b99f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984d1f48592d2e3ebf22972788879fde24", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1435095dd833dfad7d1a858f22b132e", "guid": "bfdfe7dc352907fc980b868725387e9846fe39df704d05a97b8ab3427f4f0c6f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9c8bccb720fa221cfa4170fe5003561", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9877b79edc9d74acfd04bd505df3d13ad2", "guid": "bfdfe7dc352907fc980b868725387e98a6f45dd09dfa7b39832d59a0112e688a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d69f2afc35f884ebb2a1e8ed1eeeb7ba", "guid": "bfdfe7dc352907fc980b868725387e98c608915c1dba7f10d3a607b60782b894"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98592bf9e87a7d232159170f68c491787f", "guid": "bfdfe7dc352907fc980b868725387e9803aede7b612128d5ab7c5a5caf5b5ea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba93f4e236a7607b2455a560165b99e", "guid": "bfdfe7dc352907fc980b868725387e98f43a52301bc0cf72938459d7c6c87a13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def35f487c681908399df1988335eb7e", "guid": "bfdfe7dc352907fc980b868725387e98612a9409ffc468f088b52bebffd7e20d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98713d0896ea0bb34ef215b97627af129f", "guid": "bfdfe7dc352907fc980b868725387e9887a3d3ccc97ddc8eba540abca7ea8753"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820681ace8e3bdda01aaeb710026b40b4", "guid": "bfdfe7dc352907fc980b868725387e980323c18fb339ccb7f01637309337dbec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817da24465ace8555b0d753604df9779a", "guid": "bfdfe7dc352907fc980b868725387e987dc99ccae99b30b182d8f5f796691037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860574f3cb20dbf35e33335a3ab49439c", "guid": "bfdfe7dc352907fc980b868725387e980880ae0fff2d515b21bcb21280b5f9d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987711c0efb50fad6efe2379e55e65bc2a", "guid": "bfdfe7dc352907fc980b868725387e980e110cf2882b5f3834e1eb89871f48fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba9f4f5765b57a5ffbb4c7e1c8378209", "guid": "bfdfe7dc352907fc980b868725387e980d88bfabda285cf9f2f708c9e1e950c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be7931526e5bb1a64470467aee124020", "guid": "bfdfe7dc352907fc980b868725387e986a899592cae7cf8a7ec1eea67c323532"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813281f76007b6e68b7c782f9a0e4c8f9", "guid": "bfdfe7dc352907fc980b868725387e98883172f9040c2c60dc4084fa3918a323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3e177f3da0883514be9e0557aa22ce", "guid": "bfdfe7dc352907fc980b868725387e98cb185311f0ef63d54b6bf8a543e43e20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b79250985f13e1a413d96b322b73a21", "guid": "bfdfe7dc352907fc980b868725387e984ffc506da4fbf8fa70e093e218144fbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98837260062da84e4a9885f0a576937c42", "guid": "bfdfe7dc352907fc980b868725387e986d7f139d82d7ef0e0ebaafc86ec87de6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847a19f7a125da5d003704bd2ebb3c6a3", "guid": "bfdfe7dc352907fc980b868725387e98cee19a77f9de0bcffd815d7a5c1a6076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f31d62e8d231d4c20f4599e3ef98b53", "guid": "bfdfe7dc352907fc980b868725387e98796401763eb4fb2b5d997409103d6d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e1af4495aece1d54cd6202b8b3c6bf2", "guid": "bfdfe7dc352907fc980b868725387e989dd56dc19179a183bd04312df833574e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de7a2fedfaec008e6835723ee54e48e", "guid": "bfdfe7dc352907fc980b868725387e98d5253c7b8b84a2d030b3c69973964831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ad9c059fd52e9eb18e2acc89d9275f", "guid": "bfdfe7dc352907fc980b868725387e98518fb51767efad9a69c214d1301cedce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ce2105faa5ee4aaa75c89d96e9a9f15", "guid": "bfdfe7dc352907fc980b868725387e986023a966bf501f11d09fe3ced37dffb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98992f045973353172e6eaf59c1f7d3790", "guid": "bfdfe7dc352907fc980b868725387e9853fb2e4a1fdc703cb6ad03dbc6991048"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fc352a8d236125277862deb58ef28b6", "guid": "bfdfe7dc352907fc980b868725387e983c2fe9fafd914f84b458df905448d839"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea58d3d821757fbd440b74e8d200af7f", "guid": "bfdfe7dc352907fc980b868725387e983a4aee6ae7432d130c5069c64d302919"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874d73924e4da637615bbd7aba8596d59", "guid": "bfdfe7dc352907fc980b868725387e98e350e35b40e8d5381fbb89ed2f8b4b68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ff47bf5efd09f807f222b35acb02feb", "guid": "bfdfe7dc352907fc980b868725387e985d1f4597ca105b3aed88da9f0ae3b8e7"}], "guid": "bfdfe7dc352907fc980b868725387e98518005c5253c688bcc0333185bdb8714", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9862332afa49cc0bbfa1829f8c87f0fc66", "guid": "bfdfe7dc352907fc980b868725387e98be98dff26b6fc0897bb3188ace6dea05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a6c40dcb666cbddcd0412b863a3fb17", "guid": "bfdfe7dc352907fc980b868725387e9815e803d45c65758befb3c134bfdd36ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e989a38f29c1e04153da9a02d37bb5e658a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98915a61675084778c081421e2c0125ee9", "guid": "bfdfe7dc352907fc980b868725387e98c81d3dc825af5944c43a2521758e6f71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f4f7baf44f16f6e9a8550eefb5bf75", "guid": "bfdfe7dc352907fc980b868725387e987de3797a9f941c9ddbe7a4841f6b4278"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d293b216a9d019387da097c08d64a92f", "guid": "bfdfe7dc352907fc980b868725387e98fa23be5a97159509203539499851f135"}], "guid": "bfdfe7dc352907fc980b868725387e98cf4c3db1caf34520dbf9c8a50c0e0c8b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98383e350cfdd8f8e2c95e6973ed6c4f95", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "StripeApplePay.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}