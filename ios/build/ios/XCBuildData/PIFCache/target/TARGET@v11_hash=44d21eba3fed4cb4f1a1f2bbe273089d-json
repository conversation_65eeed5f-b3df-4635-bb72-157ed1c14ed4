{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98459e55d7b0665a2a4da2879827f23152", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripePaymentsUI/StripePaymentsUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripePaymentsUI", "PRODUCT_NAME": "StripePaymentsUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b10d5d5b966e6526d9aa64448e0cf42", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2f265c6422e35dc091baa9cec0138cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripePaymentsUI/StripePaymentsUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI.modulemap", "PRODUCT_MODULE_NAME": "StripePaymentsUI", "PRODUCT_NAME": "StripePaymentsUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986060a333aef13619bcc85e8b7dfda571", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2f265c6422e35dc091baa9cec0138cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripePaymentsUI/StripePaymentsUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripePaymentsUI/StripePaymentsUI.modulemap", "PRODUCT_MODULE_NAME": "StripePaymentsUI", "PRODUCT_NAME": "StripePaymentsUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9868c612c08bd23abe7ccb41e65716ec6e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f741894d21b82cfaa69a44304a1505cf", "guid": "bfdfe7dc352907fc980b868725387e98324ce10bd27c2fdb442e90753b7c1c66", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c6c79b66080df4434a53eb5d1e4b75d3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c31e0b526d8ee5a5aff33b72258ed37b", "guid": "bfdfe7dc352907fc980b868725387e98141d4ea3901bdddb7e86bdf21c10981e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcfab10821de1ea87bae12b170c9c26a", "guid": "bfdfe7dc352907fc980b868725387e98d05e3b6fb306382be4e9637fd4a5800c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e86d706793022b294df87ec1e7bb0b", "guid": "bfdfe7dc352907fc980b868725387e9870374330eeb551ce6bd61d32df823b7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd0b855e000c6e9c3ab37a9c50ed17b", "guid": "bfdfe7dc352907fc980b868725387e98df9320d8fb5dd2be00a1c1a76cc24395"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ee41661122668dfe02a62881a61f9c", "guid": "bfdfe7dc352907fc980b868725387e985603faee77e2f0c2b405ffcbd1a18721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c15105c710be7be4d83de859af4c22", "guid": "bfdfe7dc352907fc980b868725387e98336aacab96b2d6a82244ea2ccd6cad51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb6fe73f2256aa2d404892aa6388df5d", "guid": "bfdfe7dc352907fc980b868725387e98fdd4cb05040dea0d9e35e27aa5f26e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae0904fce84ae1e71b799bc9dbd31c92", "guid": "bfdfe7dc352907fc980b868725387e98704224fb22cb6dc41735a3bff5ecee2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066368f51b0807e37581ba219faf56a6", "guid": "bfdfe7dc352907fc980b868725387e982b19ae781507621346300a3984409c1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2af90d060f6c69ad216ddd8b30acf7", "guid": "bfdfe7dc352907fc980b868725387e98185199b8e702eba58ad2526889e8ba7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981417bff2ca95550b55d34c577dbf9a51", "guid": "bfdfe7dc352907fc980b868725387e98ccf697124d9e0b3d3b17d0370f08ca87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a4450231cbfb1d04361731c2c634806", "guid": "bfdfe7dc352907fc980b868725387e982027fc1497fb763b58df3c8d2a8f57aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982930158aa269bcf5e006c498477d92af", "guid": "bfdfe7dc352907fc980b868725387e989017518139e1fb39e733516c3aea388c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98594933b577b45ecad39dae60b6653706", "guid": "bfdfe7dc352907fc980b868725387e98e53928a0f1f019bcb6ce3877b3b45e12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ec56ccad8bf1fba2dfc778f87e1bee3", "guid": "bfdfe7dc352907fc980b868725387e98f7785538cde5abd4bc4ecc93899a96f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6d583176cfb7686fe7e27230af0041f", "guid": "bfdfe7dc352907fc980b868725387e98ca70f3f5659c33538a61f1e48beeb9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c63083edccd0812d79181eeccfeac3c1", "guid": "bfdfe7dc352907fc980b868725387e98cfd2f6ca917148124ea0fed4bf3536e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f09e6b4c04f6e7e26a5b382b2b25df7e", "guid": "bfdfe7dc352907fc980b868725387e988deeda1d8fb8e6943e4de642bd99ac9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a68e9cfba86e07c595c9a45dd6559a7d", "guid": "bfdfe7dc352907fc980b868725387e988eae687493ca0e00fe055a9f59384e07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f10dd88468d3de91770e1084c790d1a", "guid": "bfdfe7dc352907fc980b868725387e98462c69a40b9f76a791fdbd6946f1af93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e555e445f2ca45a36eacbf8a52f7fe3", "guid": "bfdfe7dc352907fc980b868725387e98b075de257777adb91e5daa5dc8785103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855f255e600efdb5fddd6cab4c36f980d", "guid": "bfdfe7dc352907fc980b868725387e98e7d5da2aa2767ae6574f096cc0d2d68f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a576332c5c9f30571e21a2584e3a97", "guid": "bfdfe7dc352907fc980b868725387e98b9822d79f1720a069b7674d18a5da5da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f70a99bff60b5c2bf8b7bf5ee19422e", "guid": "bfdfe7dc352907fc980b868725387e98be320d9556aff225c616c5b67648fd1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98210648adaff9a6ec96959fea7b6601c0", "guid": "bfdfe7dc352907fc980b868725387e9830942aaf47da6590630aaf9523999ba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffa19ee041312f7f8c50202ec6725d96", "guid": "bfdfe7dc352907fc980b868725387e980a38a12659f40c408baee131230c14d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd6bb3b7baf5d071070034b1e80106fc", "guid": "bfdfe7dc352907fc980b868725387e98e6c6a79565342b4acd2196adf0463291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9251f856ea74e43a2ade5f8af39180d", "guid": "bfdfe7dc352907fc980b868725387e98b02f61f55c8a720eb685f9b5d574d1c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988844cb88ca9edf184902b4febcea1d22", "guid": "bfdfe7dc352907fc980b868725387e98e464dff550d8df1dcd4ebb9a8f0a05d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815711f05d0498a807fed0b9078552ba5", "guid": "bfdfe7dc352907fc980b868725387e98a5db821deb9a44188bfc2ad272299301"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5296d1a8c17a881428480f70d714fd2", "guid": "bfdfe7dc352907fc980b868725387e9875d2405637bf79958364c520eefb4832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fc684ebefd6f5bb941a92d0e971b0f2", "guid": "bfdfe7dc352907fc980b868725387e98eda86016cd264f2f877731e4b60e0b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fbc105705e583207a5caca3db52c9aa", "guid": "bfdfe7dc352907fc980b868725387e9898c2fb504ffdc48d1ce657be94b4709d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9269a508ac0a70a5e48df1c07afabbd", "guid": "bfdfe7dc352907fc980b868725387e98a1ee152b1cb2bf18b3cc4fadb0c0610a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045abf07ad5ceee4007c9203c035a039", "guid": "bfdfe7dc352907fc980b868725387e98d0ed27ecf188a751ed66b8fe287872de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e37ab2101c0b7ee3e6f8357f0adb10a9", "guid": "bfdfe7dc352907fc980b868725387e9878b2edb8a58edfda3b4d1613fd9e95ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872370c02a8f48cd9b5cc3eccb4fa6afd", "guid": "bfdfe7dc352907fc980b868725387e98cdbc5ad5df57439d8f7bfce20c2d1f1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877879145ee4da1c977500784660e4f90", "guid": "bfdfe7dc352907fc980b868725387e987877892a2994b99e851c06893f6ced06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c115a25e17d791a486023b7e1514a7a", "guid": "bfdfe7dc352907fc980b868725387e980e51547aaec31435e126b0ba435260f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f7263ad04eb5f46e824e97566a3ccf2", "guid": "bfdfe7dc352907fc980b868725387e98730679dd44d777308833a39de7d134c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a400c7161e4aa579a3e00f54bb73b76b", "guid": "bfdfe7dc352907fc980b868725387e9875958c68eaacc34fb40ca7db8c466545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa49517b2e3a27a03c710318aa4fb025", "guid": "bfdfe7dc352907fc980b868725387e9887900d8da01610ec72c5a072b3a3b1b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f434be5faa78aa8e07b0da9a547d21", "guid": "bfdfe7dc352907fc980b868725387e985313e2431ebeb9d40fed24d0eca1eef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d575fcd9e9b91d3ece488950a60fe1bb", "guid": "bfdfe7dc352907fc980b868725387e981ba918ed14c85825fb70c263b9216201"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805db03791ead699ed95118edb7827e63", "guid": "bfdfe7dc352907fc980b868725387e98cba90cb2d27cefb1a968a466f6a8b757"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b56d40d012f2a661b5b2cf8afe0e6902", "guid": "bfdfe7dc352907fc980b868725387e98c130b82fb6869086ba80cd868427935b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814587e39fc66058befcef4396686e0e2", "guid": "bfdfe7dc352907fc980b868725387e9835f7aec6cfde76cbc3d2b618bd76e6a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c429152062447d333bd8166cac3f540", "guid": "bfdfe7dc352907fc980b868725387e982151bef73e57730ed4697c4f8b309572"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809154f82f9a2f12e7e6f8bd8d68bb372", "guid": "bfdfe7dc352907fc980b868725387e983d143ad304a934767ad4ca0cac27e873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7fd3f87f9ab705d7af5a8645890f261", "guid": "bfdfe7dc352907fc980b868725387e98507496ba0af87a5bb9a6073b5562e4ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f49b66360be70e485b02e6e1fbfc5f1", "guid": "bfdfe7dc352907fc980b868725387e9848dad232ba1f4e09a141b5a43cbb76d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a796ee24eecacf60475a31df4ba3b9cb", "guid": "bfdfe7dc352907fc980b868725387e987241f9bc98e8a7158342c406fe204034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6403316e4d4b78da2d931c3c912c938", "guid": "bfdfe7dc352907fc980b868725387e98ca72ac0526545b25671c73bacd9bdca2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c45d14fdf47324d36485020cfdafe2a", "guid": "bfdfe7dc352907fc980b868725387e983c79d206d6cc771f749f763fd7526541"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989049bf38cb37ba92bc23ce9507f49d5f", "guid": "bfdfe7dc352907fc980b868725387e9862b5c42bdea63c566d57926bd074007b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98491e06f51a4b96e76223bbead67ab202", "guid": "bfdfe7dc352907fc980b868725387e9870e09673dc690b8f987ef3c0330846bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892f39af3cbc162d30930e69de78a8f20", "guid": "bfdfe7dc352907fc980b868725387e98fa3064a352f45a53a4f2a22f97fc733f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d4d3a56c46875ad6e6ec98c6acf0f1", "guid": "bfdfe7dc352907fc980b868725387e98c1ae042b2b90464be267a8492fb85781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c60f303530f728590705aee736f9dee5", "guid": "bfdfe7dc352907fc980b868725387e98aaab4429f153fdac38e0c9129b621834"}], "guid": "bfdfe7dc352907fc980b868725387e984936897d7199fb36379baadfc432238f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e98d654f7601541ec6b0be5a535c9c4fb1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a26dbb3a2e54b6730f9f9b38e29867b5", "guid": "bfdfe7dc352907fc980b868725387e98bfc5c135a1c45cda6cd51762ec2ae1af"}], "guid": "bfdfe7dc352907fc980b868725387e980ed0abe89c49b7cc970dd94c0ee064aa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d1ebba5ba9eb6ea8ac6c4414908e2961", "targetReference": "bfdfe7dc352907fc980b868725387e98821f5b3e5beb50f85c2c602e7f37448c"}], "guid": "bfdfe7dc352907fc980b868725387e986c8805db7358ec0e58446166d1aa7016", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98821f5b3e5beb50f85c2c602e7f37448c", "name": "StripePaymentsUI-StripePaymentsUIBundle"}, {"guid": "bfdfe7dc352907fc980b868725387e98f17b608a8faeb01ef6ec76d52489fd0b", "name": "StripeUICore"}], "guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e5ddfa1cdd414dbb19163eec79823d4", "name": "StripePaymentsUI.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}