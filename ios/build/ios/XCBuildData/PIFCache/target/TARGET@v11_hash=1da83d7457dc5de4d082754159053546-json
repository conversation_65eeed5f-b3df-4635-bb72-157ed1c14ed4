{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985021d8bde672f8b57a6dff9b19d1600b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9864dece58fe8898e7f0b46391fe2d091f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98688d1613c510cd94a6db350e72d5426d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b08ec94c515cc2b895c3ae9b4c03834", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98688d1613c510cd94a6db350e72d5426d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982b03ce4519745554a82e5cffe58f25e2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c9114483feb44e96f51f3922553b915", "guid": "bfdfe7dc352907fc980b868725387e986ea09e73b3e9c66564d9ca06c4292426", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffd354c66c0d24bb09729fb7b593673c", "guid": "bfdfe7dc352907fc980b868725387e98e83b1d5958993e4d1b5d9e68f57841fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981367dcb52824596d06814fca8e5f7237", "guid": "bfdfe7dc352907fc980b868725387e98dcc229aa14b3eab156f5e289d60cdc27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a05b4feb369fb15f4d6904251286a6", "guid": "bfdfe7dc352907fc980b868725387e9830cbe78cf7681c622e85fec2802ef7ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98110e92f984cf6a1b3d5e5e8ee1b79dba", "guid": "bfdfe7dc352907fc980b868725387e98fd83fb774dba3945d76c0b2f31ea0e3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988af01cb570d47fd9222f556f26f98637", "guid": "bfdfe7dc352907fc980b868725387e9801b388534ad86f78c68bc33ea48d2e9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d262876ef7f3b687bd288bd8a51fe07", "guid": "bfdfe7dc352907fc980b868725387e98b39d20f9d4776c8fa8f6c7aa880c2266", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98530857fe2564e1398e4771890ea7a91d", "guid": "bfdfe7dc352907fc980b868725387e9862792b3ce7258ae0fe3ccd8f31012526", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987c543487347cac13b6462fae62598e7f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98536d8393f07194ff1d7d9e3a0e4d016b", "guid": "bfdfe7dc352907fc980b868725387e98fbfa07d75c6c288cd4cf81d33a65d853"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820740af5ebd18bea808a8162defef841", "guid": "bfdfe7dc352907fc980b868725387e9827dff9f42ec85bb360716c6ad58453f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875394d81e7df5e93b74e5763a3106b97", "guid": "bfdfe7dc352907fc980b868725387e98b46a6feb27824dd565e2cdec7076642a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea45bd4fb2537000a7af7c6712a207e", "guid": "bfdfe7dc352907fc980b868725387e98258689b735f4bc767b2e455a0c4fe0d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844453f7df0fff402d12f9a96d0085ce0", "guid": "bfdfe7dc352907fc980b868725387e98053b7e0939465150db9c50a3f89d6de3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da6110817348ec4ff46beb5ec58745b", "guid": "bfdfe7dc352907fc980b868725387e98e0ab939fbafbd88342244f2b4a6d7f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869c9f67c90cc3eb6281cd6b473496f83", "guid": "bfdfe7dc352907fc980b868725387e98ae56f215c5ee302bfe22c2a68c3c5982"}], "guid": "bfdfe7dc352907fc980b868725387e98ce67561b68c83c2e24888c94da999914", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e98cf1fddec899afc6c9825c5eb5ec44493"}], "guid": "bfdfe7dc352907fc980b868725387e98434353ef3b38c3699582ee30b73fe6a8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9872ef11792920b3966776ea469c5092df", "targetReference": "bfdfe7dc352907fc980b868725387e98082dc85da1fc941e5234c7cc1f11b27d"}], "guid": "bfdfe7dc352907fc980b868725387e9811e9e8a5f23273fd9234f4740a75ccb9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98082dc85da1fc941e5234c7cc1f11b27d", "name": "image_picker_ios-image_picker_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981f000f066404b97b12e9c4ca84d38d0f", "name": "image_picker_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988e06e8c3685b7c12032d8059f412f4cb", "name": "image_picker_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}