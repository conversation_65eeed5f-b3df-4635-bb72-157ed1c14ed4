{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98637099b02baf67d7666bea8fa2bf7bf7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985150f8e48e626e00b7037d7040d73c14", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985150f8e48e626e00b7037d7040d73c14", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982394f4408145ff64ab9d6be3ad7706fb", "guid": "bfdfe7dc352907fc980b868725387e982ea0ca19c0771fd6d266f2b66446b37d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98071ccc91eb7d1eb16733498b8550234a", "guid": "bfdfe7dc352907fc980b868725387e98f1887403300e04b839877af836998497", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d851ad5cf4dd15a2b2fbacd31939a4a2", "guid": "bfdfe7dc352907fc980b868725387e9899c4b45e473fef94c750c44c19300e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830dbc33d33c2b1e45adc0d176d852988", "guid": "bfdfe7dc352907fc980b868725387e9801e957e9f2cdc171382139f7af1a5681", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98516c51b2523bd15a293fc659bbd1030a", "guid": "bfdfe7dc352907fc980b868725387e98f9dcd7bfc2b70fc0334d5a2b029d2be4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eac4a5bb43c64e5be0e9a16134549bb", "guid": "bfdfe7dc352907fc980b868725387e983c0142618f0c80e86891f682ede039d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb8873429716b8127249a5b26a10740", "guid": "bfdfe7dc352907fc980b868725387e98a01fc3746b75fa29083dde20e4e057e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cf6c25266210aee792bc28fe3638637", "guid": "bfdfe7dc352907fc980b868725387e98cd73b22058278a9b4f651422cde8e6cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874812c7d12d5e55003edb40991184375", "guid": "bfdfe7dc352907fc980b868725387e9858da164c6f05b97dc4f70eeaf0ab7f88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d74495088879c7001e3bdbd69268bfe4", "guid": "bfdfe7dc352907fc980b868725387e9810ee0ac07ad1614d2a0f4f4692012057", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6e1d15e52a95c7b4c3670605b814b3a", "guid": "bfdfe7dc352907fc980b868725387e9898f6f1d0225d5da11e073619b459ecbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a8f3da1a8477c7fae357d6615e9c6a", "guid": "bfdfe7dc352907fc980b868725387e982567c545f94782ef792faee70a9f28e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894d4747ada4b1a72a54a9cd65eac63fa", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8eb973d150406003c1d7cf6c66d8557", "guid": "bfdfe7dc352907fc980b868725387e98616c37b3aed90db7f9fdd15a498d9b27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802880378a0ebe345cef89cf6f663f003", "guid": "bfdfe7dc352907fc980b868725387e989ec464f79f509c315509332bb727e800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee30bd280b5d62c07298275a89faa00d", "guid": "bfdfe7dc352907fc980b868725387e983249d575db8b5cbd53c0564d7472fd4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980258a8578dd22a5ebd5e83d56b5539ca", "guid": "bfdfe7dc352907fc980b868725387e980d76b669df9576a70807ec449fb952b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988792723079748aa61014a93611416231", "guid": "bfdfe7dc352907fc980b868725387e98bc7c56555dd219b78ffdd88c372b232b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4f7537d3bbe452c27b13dc33b010070", "guid": "bfdfe7dc352907fc980b868725387e988e7018dc0c9e1f0c9f93b5994bcbc130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2698426d6ec07818fa076294aa5d764", "guid": "bfdfe7dc352907fc980b868725387e98dcef7cb18e4d73ea8eef002919db5632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892786b46ab13bd4e29de0ae1e6ae7fa2", "guid": "bfdfe7dc352907fc980b868725387e98955da4966904666f6adbe480ce93a36f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984abf214054948020e6c897a8345b9f78", "guid": "bfdfe7dc352907fc980b868725387e98232d0a9b2be5b70279bedd97f191826e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8133d859d2e1837a48705c942311290", "guid": "bfdfe7dc352907fc980b868725387e987211db59fabb7296cf1568fb71a68255", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819052a3b53599a9b50c86dd8fda40fd1", "guid": "bfdfe7dc352907fc980b868725387e98c8af4857ac32d849c3b447bcff03aa13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983729a16525c69cd218abc6062695e5c1", "guid": "bfdfe7dc352907fc980b868725387e98dc950c62ee46ede361cd98ae90dd27a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb3a9943cb113160526e5a9d2219644e", "guid": "bfdfe7dc352907fc980b868725387e983f0af89b0b8bda8241febbe4723f8c88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baaa5ad43374e07a34b78ede35944e78", "guid": "bfdfe7dc352907fc980b868725387e981a333127dad81374abf6fe8c83600eee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98130011f073c4fe45f11906e601ca7435", "guid": "bfdfe7dc352907fc980b868725387e988f8e2ac76c48795c8610f17a754a59c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a03a84e6c885f62c64363cb4da6fdfd9", "guid": "bfdfe7dc352907fc980b868725387e984b1e4946f1823465931d4e8492eb19a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ee7dea57c67aa8e3304555061738478", "guid": "bfdfe7dc352907fc980b868725387e98486c17b42526ba81b0e47a838401edd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d18bd9d2be6417c5f89f94b17ffe0fe", "guid": "bfdfe7dc352907fc980b868725387e98e24ce56a008cd8fb5a8cdae1e9811e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf7c6c2f9414b9bdbc949601d2eee7a", "guid": "bfdfe7dc352907fc980b868725387e98243ca78b2974c64cd01083596d8fe9a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98621d7f22e5c7d27fafd9438134e42a8a", "guid": "bfdfe7dc352907fc980b868725387e98c41a340685df4d1ce3783021dcf54fc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e43594d5228099e163a87bbfcfc8348", "guid": "bfdfe7dc352907fc980b868725387e98506a4bb5c9b087dedda179e379e974b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982179f7fa9ba75c5fe1fa362942a0b82e", "guid": "bfdfe7dc352907fc980b868725387e984463890dd80178e8bcdc0b520010feee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982df976bd025303b0f9bd5e166ed04ccf", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc5e33108dc4ef481823f3d0a917ebd", "guid": "bfdfe7dc352907fc980b868725387e9859e3b579695a572ae5f0cad67ec2876d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862cbedb219db3d1c2dd460edd75a34f0", "guid": "bfdfe7dc352907fc980b868725387e9885aba67d2d135f9806c9a05487a1a98c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e87ca330e22e839d1fb227687decb6e6", "guid": "bfdfe7dc352907fc980b868725387e985805bc10d6057738af5938ead2b69fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff3af82d766aa96b7ac41cdb49e66bb0", "guid": "bfdfe7dc352907fc980b868725387e985658d819a1ceda9c7580d1657159b11a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baf933d277fcf09ada14ee2ee4b876ae", "guid": "bfdfe7dc352907fc980b868725387e98c8c90bc417199af9668b9601526e858b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ace06126ed501060eff4a4980fc42d11", "guid": "bfdfe7dc352907fc980b868725387e98a79aa5b656ffdfb7930448cc9a744fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982635d92447da620b994a15db650faf4a", "guid": "bfdfe7dc352907fc980b868725387e9822c0d109ebdbc53ca874ef63b18251dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897459cb49ea058ed0dce08ea303ffaee", "guid": "bfdfe7dc352907fc980b868725387e983c3ab34f30d3bcea13309294f772a70c"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}