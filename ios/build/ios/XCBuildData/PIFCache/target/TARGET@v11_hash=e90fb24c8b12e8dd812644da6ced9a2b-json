{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989d4434d6a6b7e6a75f66ebb47eabc8d6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8ffff8c4608b48a02b8e6dff7150e62", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da91f71280255f5db1b6be28e997d14b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ef1fc6d25699796b1f97b56600d50813", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da91f71280255f5db1b6be28e997d14b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_contacts/flutter_contacts-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_contacts/flutter_contacts-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_contacts/flutter_contacts.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_contacts", "PRODUCT_NAME": "flutter_contacts", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5afe3ea1143ea5558fd7a311b1ef47e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf8d35ed1a1fca721ebd360cf31df824", "guid": "bfdfe7dc352907fc980b868725387e98d6a9faf101b28e0cb6b0f6b28fd8ed44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede9a611f60de66dcf4e4af07d0c8692", "guid": "bfdfe7dc352907fc980b868725387e981f92bf70f178b27d9a3efa8ed140403f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982f000430adb083ec7cc5dc23620bf9cb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98acfc9507657cf0ad622ab918fd1c9038", "guid": "bfdfe7dc352907fc980b868725387e98793caf8167e7011ce094e87f31b30b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a243a03ecd353d4f59b4e1e69f56d7e8", "guid": "bfdfe7dc352907fc980b868725387e98263009124ff76943b278466d51a2be88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1bea209bb1fec48f78ee22fe20bf50d", "guid": "bfdfe7dc352907fc980b868725387e98d9ad7a1fd83ab08a997b7463fbfa9a98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98856b343becaf82d1d7b89cc8e4abd5cd", "guid": "bfdfe7dc352907fc980b868725387e98f4add278f5b9add1fb3a1d53f911946f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98035b642b625a54b823f41c67ac37fcd0", "guid": "bfdfe7dc352907fc980b868725387e98a6ee27c2d92bb828c22d4d5428d618c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98363043f0bd30ce6095f943eca8927b4a", "guid": "bfdfe7dc352907fc980b868725387e9830c714ca63519cd2b6fd4c3889cce027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bd8a1e97334778ed45ab7cd18b60d6e", "guid": "bfdfe7dc352907fc980b868725387e985ccb9cdf975d0a3aae85f5ed4a2331d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985799c67b999f076d471ade8f780b830a", "guid": "bfdfe7dc352907fc980b868725387e98e3e174dea831267a68d2d3fb9317e890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98501b4fd2c76472723d918f989e8d9f67", "guid": "bfdfe7dc352907fc980b868725387e981edb9baa65b84c3d649b2eb6d67ecc52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e44d93a35616e31900c0b7635a12b199", "guid": "bfdfe7dc352907fc980b868725387e989c9d8fd3ffbfaf3eea40189dc2343b9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a66376b92693fe7490b2db7a80a05cf1", "guid": "bfdfe7dc352907fc980b868725387e98e06f8ad587c8c6787c1a696d75887b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751bb6d865d53705343634bf476226a2", "guid": "bfdfe7dc352907fc980b868725387e98020f73c02cd7305c22ba22735f6269f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d9866297528640854cba4b95912419", "guid": "bfdfe7dc352907fc980b868725387e98aab974d220566b743a1495cfadc2e83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9e34a46168885211eba5b2eda6f1a53", "guid": "bfdfe7dc352907fc980b868725387e9830d2019e22015de92bc026367a34b33a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d005b3b845808329e2161002ec0dfc0a", "guid": "bfdfe7dc352907fc980b868725387e98065b2048d2e476a0abfdbd7d52ed8f3a"}], "guid": "bfdfe7dc352907fc980b868725387e987c966a3b129a240cdc79c574c521c055", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e98660c51cf828305bc3a5ec4c52aa8b13a"}], "guid": "bfdfe7dc352907fc980b868725387e98fa71aa902bf434f3999bc057159ce99f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988a175a6448755246720f963908648c4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e983dbb0d5d79b94fc9af349ed668188ddd", "name": "flutter_contacts", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9844de8c75da5c0dc8b59260788428245e", "name": "flutter_contacts.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}