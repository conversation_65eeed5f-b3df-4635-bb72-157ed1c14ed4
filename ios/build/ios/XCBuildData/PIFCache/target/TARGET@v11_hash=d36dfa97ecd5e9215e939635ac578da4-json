{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f68f8eadd8ce150d799f39ae53b230d0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b8d92148f0450187622fe48b9a95cec", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98355cade5846a1e0ff13060acaaf13332", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d899cdd5da9a94ba2acd886e09d8dfc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98355cade5846a1e0ff13060acaaf13332", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b01478975ba1faaf106de72be06cf780", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f9ef7533b91e23bb4ce8c57a81a1525", "guid": "bfdfe7dc352907fc980b868725387e9820f29024ea49f43723c4b5ccce8b840b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd6a09716d404bbd2cce2f43ec5ff25", "guid": "bfdfe7dc352907fc980b868725387e98a850f4c50a0419fc935d6fe60849a3cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef76b9e81c4bc8a2cd295b9fdeda464b", "guid": "bfdfe7dc352907fc980b868725387e98e1be4fbea96ac9409efce9a5db8bfeac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7101d0a4c5c9b781262938c18f09a74", "guid": "bfdfe7dc352907fc980b868725387e98c43299612805119dc30764335382fbf1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98107e79ae4f33231f0a5893203225477c", "guid": "bfdfe7dc352907fc980b868725387e9896d3ef0e56a16180f8bfb6d881e273dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d596095c77834a3e0c1a5560cee6f6", "guid": "bfdfe7dc352907fc980b868725387e982d911b856a0255d7147c5541bd7ff21a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98153e25bfbd9f863b60a057fcd4ab6e59", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d7b04f878736239fe27e24e957217033", "guid": "bfdfe7dc352907fc980b868725387e98d4bb246d1b9b6059c6821c2191b2b14f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed304222d74e309bc5d0d7d4d4866239", "guid": "bfdfe7dc352907fc980b868725387e98ec0dabc8487d7035aec961ac2dfe4751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fea4ef9bc0dfadbf8a95c6b50ee21b8", "guid": "bfdfe7dc352907fc980b868725387e98e88012559bcf20a13c143202a713fb7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd53e6b17f7947f9476c5e0395ca55ec", "guid": "bfdfe7dc352907fc980b868725387e983ecf4aaecb0474d9f634309e131a6fce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857de9266da069d696ea94038f48bbdbd", "guid": "bfdfe7dc352907fc980b868725387e985ac5b59579b7374af723fdc51709c884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828da3b29b71fad25425530272dddc2f3", "guid": "bfdfe7dc352907fc980b868725387e980a1e741e0b240d4ed9970053a4eb7f39"}], "guid": "bfdfe7dc352907fc980b868725387e98a4ae53b748c3152989d37aad6a05a27e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eac51829982960cffeadd4607d672032", "guid": "bfdfe7dc352907fc980b868725387e98d48de3f06d69fa3487c131a75032fbeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e98a63f1c3e0f465764530021f7683ec260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ee89734500173021219fc40541a5b3", "guid": "bfdfe7dc352907fc980b868725387e9890ca76add55e99541eded3c0f66ddfbd"}], "guid": "bfdfe7dc352907fc980b868725387e98ac8e382a22981eccca2a16eecfb83047", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d1e0175d8107e2f6525da71222bc9669", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9817d41af66eee3a4c1e145e17163b22b8", "name": "flutter_sound_core", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ed846bc5edbcc85d935ace19b53742e0", "name": "flutter_sound_core.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}