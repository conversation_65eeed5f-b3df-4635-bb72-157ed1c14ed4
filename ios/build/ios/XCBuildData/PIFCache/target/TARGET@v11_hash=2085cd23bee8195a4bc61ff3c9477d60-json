{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d46f8ddfb19bf36b9654a7276b9638c6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802dfba1040b9826bcc620b6e29f5452e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f1184969c7641115a58406b287b577e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98353c3596a6874e21a82e5cebb22618a5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f1184969c7641115a58406b287b577e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a1caeb009dfd3c4f8a9eac8f99a0b6ec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9827c1a9f738da778ef5129e71c73b626d", "guid": "bfdfe7dc352907fc980b868725387e987a865173bbd33c964289c298110d0113", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815b756faff4119717933656ba8182a89", "guid": "bfdfe7dc352907fc980b868725387e9889018e9986950050023d79b3eb3b8d0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840cced8a0152eda3cb3d3ed201adbfde", "guid": "bfdfe7dc352907fc980b868725387e989d1e80df0caa541ffb9a2751ccc5763d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98aa7647f5f17c2b31e264585bf9535b3b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8c481c1ada9187b845280666435b5dd", "guid": "bfdfe7dc352907fc980b868725387e988d5321c34a005bc3d3eafabdcbbd195b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983715db6a9275b28e9fb36c9c6f9d01e4", "guid": "bfdfe7dc352907fc980b868725387e98134963bcfea375703bad8fa6db68aee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e6546ca5aba0b0bf2f599ce4917cc8", "guid": "bfdfe7dc352907fc980b868725387e985d76fe350679af2392b09be1589a9301"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fe8ef32ed9619fe9b7561117183975b", "guid": "bfdfe7dc352907fc980b868725387e9887b417888cd18749bb988162ddf0a461"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2670896e31de214d270d28c4b9a47fc", "guid": "bfdfe7dc352907fc980b868725387e982e0c077d40950af114d2c3de10c2ad37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98981ddd7c1c8ea1e56caf6f49e2281cea", "guid": "bfdfe7dc352907fc980b868725387e985def90ff43f5fb72613b2a2d20460005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a73c6ac01e074fd46f5199b9c39093", "guid": "bfdfe7dc352907fc980b868725387e9814d82469694213f1a3ab1c5a70efcd2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98649c713c88c21d35d004779d3f3d45dc", "guid": "bfdfe7dc352907fc980b868725387e98d7a0f6526b06ee2a40fc18658a4e0013"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c5ff3941fb6975ca455eae23be1da06", "guid": "bfdfe7dc352907fc980b868725387e98cef0b492ef776659d5a68de17637f855"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2015f8be25c41deaca83f2ded62fd3", "guid": "bfdfe7dc352907fc980b868725387e98b92befe7bbd0bd358f4442ffbeb8d012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5827ad92e8c4f9ce3c8de8eecd6462f", "guid": "bfdfe7dc352907fc980b868725387e98828ba2727dd134b1ea673095e6a76060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98174571592a601331f3f62f782c22bc4e", "guid": "bfdfe7dc352907fc980b868725387e9842e5fc93ab21f0d9a93b48c24abb560f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee0cd9474a1bb2ad12da878e7323a124", "guid": "bfdfe7dc352907fc980b868725387e98b38737da9d118a395269f3c2a4baafef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbecbb8f47b99b43d1553d394081dd6e", "guid": "bfdfe7dc352907fc980b868725387e98bf56a49c34d16918350bc655091e2b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8dcab228f8f1fc3f4160858a5eba850", "guid": "bfdfe7dc352907fc980b868725387e9867092df475d1ab7fe018ced320e5a9fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6863a8f8593e0c2ec4f5cd4382ff07", "guid": "bfdfe7dc352907fc980b868725387e98184e38255988c3f2d1cc446a43ab0fa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f6928db2a341c0c9d6f0890194f3c07", "guid": "bfdfe7dc352907fc980b868725387e9822633ce38f935ce3e72b8bd4264e5169"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e797f80c658caadbb2c734a9847889", "guid": "bfdfe7dc352907fc980b868725387e980b685fc8e8b79636980d7b575fe36c61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb02a9f17c568b947ccc5224348f3f9f", "guid": "bfdfe7dc352907fc980b868725387e9887892a066a9aa1272aa249d7397795f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477df2cb9d48b8e8627731b0592a7226", "guid": "bfdfe7dc352907fc980b868725387e98a863828ff5da9e7bd3caeb7ac9e3b577"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f1de62b4de3b4384084785c6828625", "guid": "bfdfe7dc352907fc980b868725387e985823d453998c2396e1afdd0ca37b9aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbbd9f77e5ff48caa76a11dfb658e799", "guid": "bfdfe7dc352907fc980b868725387e98e1a53772a033629d2b859755ecbe4e23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981754a500790abf47586d8a7fa3453b60", "guid": "bfdfe7dc352907fc980b868725387e9824756b63ed618aed6ff956fbef16cdad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819cb0c6a4b443e5412d20b8f636d1555", "guid": "bfdfe7dc352907fc980b868725387e98f1f554a100f412b07ee21dd2b9f9ef01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985906e7d9ec5e57ca10b71f47afb07553", "guid": "bfdfe7dc352907fc980b868725387e9838c750581b1f0eaa24de351d1785ae2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa57b8749e26c739e95191d5e52676b8", "guid": "bfdfe7dc352907fc980b868725387e98425761264ca94cd96945659b57bcfd28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98708c0a2f9aaeb0d362acd73d7724b6cb", "guid": "bfdfe7dc352907fc980b868725387e98ccbec85d0247dd1c9cb5cf0424c13034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e47f6aee31d528e22618dd30f4afccfa", "guid": "bfdfe7dc352907fc980b868725387e980170396d9cbdda9b8efb15f452cea8b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b3ee0d8b4bee29e74acdd57399020f", "guid": "bfdfe7dc352907fc980b868725387e981399130cec9df8bb71d774e668c80ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ad8078da15145fc955df3a428009f76", "guid": "bfdfe7dc352907fc980b868725387e9859df423874e635c815a983e402654afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dc63df96a20f1042190c8e9cd6512ea", "guid": "bfdfe7dc352907fc980b868725387e9868fca7656dffb3daddbac984d7a6994f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c5f8304a3b2f8df431db79a9ba539b", "guid": "bfdfe7dc352907fc980b868725387e98ce26361b81fa44db55aee2e028cffdea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895615cf80044171688ffe52f01401c72", "guid": "bfdfe7dc352907fc980b868725387e9892714b4ae2aa1ed07e621f7d10eb65bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981486101db05f635d5089aacc297bbbce", "guid": "bfdfe7dc352907fc980b868725387e98520752f28d9bf57b0f679546f7bec74a"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5a09b9a4ad65c720d2be023caf286", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e98f63f763ddeca3aede9b5b5c6e2e3c953"}], "guid": "bfdfe7dc352907fc980b868725387e9868974109819156cae0001cab795a8d13", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c69589cf5eaaadee22bdd0a46f74f90c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "stripe_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}