{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d41d1fc6fab0782d586b935913266774", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987c9d2b2cefb464f086838e4baf91d63c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986c63744ee42456e8aea29bdbf866cf14", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c4e898c39d422548ab2fa785aecd0cf8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986c63744ee42456e8aea29bdbf866cf14", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d0760e9570104ffa88b3a8b9e7b00055", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d7d49235b2f6f78eb9ea9962f113bb28", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b5cc3b41a4324c84a3bfabdc841a081a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984b27920ba2de5f5c67251c63204d04d3", "guid": "bfdfe7dc352907fc980b868725387e985f1de2a6a30889d341e20de841eabf8f"}], "guid": "bfdfe7dc352907fc980b868725387e9849b7369b375684261d624824ac41b947", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9815c568b692670a257cffee120a27e3f5", "name": "quill_native_bridge_ios-quill_native_bridge_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986b20fa06afd49bd8eb14532479b5f0ed", "name": "quill_native_bridge_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}