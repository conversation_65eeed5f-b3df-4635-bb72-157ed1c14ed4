{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c9f8427c26343e44f0a1e0393b1cbda", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee49f65d26d8a0c930cc03d16e0ffcc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ff30e2aa7cd35d2a97ccb97e31b573e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d3fcd01a34fff9e18dec0764bcc371e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ff30e2aa7cd35d2a97ccb97e31b573e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984def481d54810d0de6d3335b228b2a5d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f5acbbbd0172f40c08a2ca2d01e38614", "guid": "bfdfe7dc352907fc980b868725387e98f74bfe561cdc142d140be02f934f1dd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d7026920b170fb525ea14aeb3f86f4d", "guid": "bfdfe7dc352907fc980b868725387e98ee11ff009cad5d58df14f6726361e05e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987edfe5001c1ee5772f006cc2ef47d7b6", "guid": "bfdfe7dc352907fc980b868725387e985b56be77fd90c95178875fbdc9759ded"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac25d024aa2da06d365231a39f105c2", "guid": "bfdfe7dc352907fc980b868725387e989fb03240d74c5b16d4df2090487dd870"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fe773ca3c0ed64b1aaecb50a9972ab", "guid": "bfdfe7dc352907fc980b868725387e98b0a98399ea089bec6015924fca214fd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861e8454d0df2ea62dc1be7d58efbe02a", "guid": "bfdfe7dc352907fc980b868725387e98a561fea4089e8e9aaa7d41e004a74551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c7caa9e41339da197483f75a0be9ea", "guid": "bfdfe7dc352907fc980b868725387e98e58eb8cea732f8da9eabee06f4760c04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98615a49563cf2cb1291d6c2ba3de234fd", "guid": "bfdfe7dc352907fc980b868725387e989f7af140018ee6c1e6b210b20014ceb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870b694f506d3a4756045ed667d65ae5e", "guid": "bfdfe7dc352907fc980b868725387e98f23c703fb2f8f96105b7661f651d76b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe20616ca04db97f0a549e7afb1ff657", "guid": "bfdfe7dc352907fc980b868725387e98b842866f43a58c7f2cd4903a46020f66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c5a9ea768661ee053e702ccda8a8069", "guid": "bfdfe7dc352907fc980b868725387e986769c43d062e13d97bdb26435517a0a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78432063a27c4ea21b29bf0a7572ace", "guid": "bfdfe7dc352907fc980b868725387e98dcd1d6833c3ba22cff192e9c729f402d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811292e1bb6508c4edba9b2de914097a3", "guid": "bfdfe7dc352907fc980b868725387e98a8b10f256eab576dca45253c4e5eb01a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792b9a5fdffee0a91691ff5c2fb0d5e8", "guid": "bfdfe7dc352907fc980b868725387e984db51d20d4b10739c36207080362307d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cc465b98567e5be1dff8b7284a07e4e3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98728a764444d0d3fed5cad33fae665591", "guid": "bfdfe7dc352907fc980b868725387e985adaa6c4e40f33315ac0cec984200988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98259a8ba702f228815953207f975f9b5d", "guid": "bfdfe7dc352907fc980b868725387e9887c26a4ce919cd8e1da7ba74bc37f5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985436ae1322903cfc490ac4716ca9d3a1", "guid": "bfdfe7dc352907fc980b868725387e98d14ae3cc83358b6ee88a1f9a4d5c6d3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c124fd1859bacb671d3eedd018c6e13", "guid": "bfdfe7dc352907fc980b868725387e983dbbed738f1be0937cd9105c2a700a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4087f36bc71164252300d8897ab827d", "guid": "bfdfe7dc352907fc980b868725387e98ea0fba00024403dbd3e85cac557df8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d320086e8c0fe704f66e68d4d8ca14ef", "guid": "bfdfe7dc352907fc980b868725387e9823662ee0e62201af4b56a8af77ae26e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b747f427d37f1125137a8806c7becc2c", "guid": "bfdfe7dc352907fc980b868725387e98fed9191d2666c9cd3507583cd694c1a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa32a4c57fffd234e97cc57072038db9", "guid": "bfdfe7dc352907fc980b868725387e98af1eb69ffb28b93e0b654f9fa9aeec11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b2ad20b1b5a95e0586a3030919aeb2a", "guid": "bfdfe7dc352907fc980b868725387e98e6b9ebef247a9cb276932d07fdf18195"}], "guid": "bfdfe7dc352907fc980b868725387e9837c2f0a37c50e959478519168227e455", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e982f461d51c284a55b8f869fc9092ae5dc"}], "guid": "bfdfe7dc352907fc980b868725387e98dd47f73652ff7b522b7942f6a87afd23", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d17544c34b81de618417de5f9c91b4ec", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e98e60a652c76bfee084293e97b00176921", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}