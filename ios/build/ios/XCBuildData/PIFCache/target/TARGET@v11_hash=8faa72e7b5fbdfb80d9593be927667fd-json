{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fd2023c01283162fb6f5e5c578da556", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b0491debad13020a165ab8d8fb8e7a6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98468f6ee074617f13ee861c91333b6c4c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5d17fb1d91f0a4f64158124b05fa286", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98468f6ee074617f13ee861c91333b6c4c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.0/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d48975f249f96592ebac62cabeb71693", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984bb5e13944816c24d69efa8b58d1ea9a", "guid": "bfdfe7dc352907fc980b868725387e9883c10ad6045e2edb3b45c1c6c0413b92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a4fdc38bf986b19d561fd705b949d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9889c9d5976d88ed18f708f5d615f69acd", "guid": "bfdfe7dc352907fc980b868725387e98969408efbabd6c244fb31026a70b0e67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98642259f9fa191092cbccbab3383febf1", "guid": "bfdfe7dc352907fc980b868725387e98ccc662fd71b0284b67383206314978f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce10c351a9f3b7c3554e16501c51467b", "guid": "bfdfe7dc352907fc980b868725387e98a1d9f2375a69945874d713b2e9d10914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982341ec96c7c344a466d749901c76698d", "guid": "bfdfe7dc352907fc980b868725387e9831c4597a13c2a83e1eb4b6365e79c8a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892f597d7ddecaec9d1f6e87ef26788d0", "guid": "bfdfe7dc352907fc980b868725387e98a38a8909fbb968ee8962d0eb3a64c90a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980571a24f59d502facf30bac443d54a5a", "guid": "bfdfe7dc352907fc980b868725387e98c0986a0665428367160e1e55ec465718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984154ce4e923137e7df2ca16d7c3b7705", "guid": "bfdfe7dc352907fc980b868725387e98897de58191c27aa4fe939008a06c809d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873fe5b89d494b97d368c8600facad1dd", "guid": "bfdfe7dc352907fc980b868725387e9854c5968b776e8718b35f17e0dff8eb98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872c61d81920d903d3fc96c2abd3d062d", "guid": "bfdfe7dc352907fc980b868725387e98ab96995fc8e4a45a40f3c884fe968273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d44b765e4578fc1ffad948ab9da5993", "guid": "bfdfe7dc352907fc980b868725387e9899af2dda6527bf8fb092669ac6408cf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b965699eb149b89c2da5aa1cab8394ab", "guid": "bfdfe7dc352907fc980b868725387e9876349763c5b1cf0979ef72fba08af750"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809e87314b12798194eba15f30be85a41", "guid": "bfdfe7dc352907fc980b868725387e98807a98cc2e15ab353cc80cd5a2911dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e126807d16006a7c10aa5133226627d", "guid": "bfdfe7dc352907fc980b868725387e98f4fd326e0ca504595dc36191fefcf041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3ba5990bc5762cb771d0dc0ed43f301", "guid": "bfdfe7dc352907fc980b868725387e98614e7b84060dfca1532c9261932bedba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98130d32fe23696263c8a7666942210148", "guid": "bfdfe7dc352907fc980b868725387e986f8ab02e63746154e009cf16b40b5ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cd9bf852af6d63891d4faae088854f1", "guid": "bfdfe7dc352907fc980b868725387e984722d323bbb9bbc17b36d513ba5ca692"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98439b871fc16302ae7413116c7bee82d2", "guid": "bfdfe7dc352907fc980b868725387e98023a186b4da6cb3c95409fe9cabdc07f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb3e14877d54d4a302d4862ec0de197e", "guid": "bfdfe7dc352907fc980b868725387e9829f9bddb91fc2ae033d4ff0390bd4077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6d8866ea83c106db85acff07d1c2574", "guid": "bfdfe7dc352907fc980b868725387e9883231659dc0afd4499e758a9f0ac4bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881791db78aa5c060ac416f13145640d8", "guid": "bfdfe7dc352907fc980b868725387e980578bc68fa4d6001c19f15cea2cf0916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddb1d357728d2f5918ad8700f487dbfc", "guid": "bfdfe7dc352907fc980b868725387e98127007af8599b62b2d07e49be3a3a435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838e81869732396885e557f8e5b979693", "guid": "bfdfe7dc352907fc980b868725387e984780857419970627e136821200fd6ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fca328cc7f8b35c62eafc9dcd29f520", "guid": "bfdfe7dc352907fc980b868725387e9801dc05cf4d659d6c5ffddde739f677ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0c7feb1cadbe0e414d1cb864f97f9dc", "guid": "bfdfe7dc352907fc980b868725387e98f8e623a8064d5de453d3f5b25a482784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f557ae40fde1d336929ed8c7b7b857", "guid": "bfdfe7dc352907fc980b868725387e9896432c7de9753ce65d6c09472c6f77d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bfa18582847694b473cf1e559661f75", "guid": "bfdfe7dc352907fc980b868725387e9860763840ab26c983d8fa8905475b90b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98056bd471d915510ec4cd3f80010b2377", "guid": "bfdfe7dc352907fc980b868725387e9896909fd38aa76b2683f8efc4d8ccc7a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984068935bcb736197daf9931e4bf1ecb4", "guid": "bfdfe7dc352907fc980b868725387e98062843da7211295493f6e6943e3833cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bddc5857d42d0584fd0318f09392350f", "guid": "bfdfe7dc352907fc980b868725387e983424fea5e879691c8f8c58e5ec913d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fab663bd66cc29e38544d13d6a812ba7", "guid": "bfdfe7dc352907fc980b868725387e985f2d59c25bbf2bc9057916c51b751229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f0307714c9ec60155e358c3bcc7599", "guid": "bfdfe7dc352907fc980b868725387e98e039bbe167efa69be6380f8e93017b9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1740ea42754e2df30888ab0d799f4da", "guid": "bfdfe7dc352907fc980b868725387e98ebbe03d7b48c2a6be2ce2affa2b6ae48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885f9d7eb45888ad7374726e7b2f9a389", "guid": "bfdfe7dc352907fc980b868725387e98810205a4c17760bb9cdceb27780dc2fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846dd0367a395e007cdf9e8eddd1282e4", "guid": "bfdfe7dc352907fc980b868725387e981c4bd8d279e2891906d36301438a9802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8e33b0cf7744d409f9e6d3585fac54f", "guid": "bfdfe7dc352907fc980b868725387e9848c8613314694b7b2103a9a1feff2255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860c8bac0eede8587db7b4f8e74b4639d", "guid": "bfdfe7dc352907fc980b868725387e98b081cf1e1109fd8daeefbf5d083c8d20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98444f3588fb7f3c8ebb550c947b3d6103", "guid": "bfdfe7dc352907fc980b868725387e9892f7d93ec0427d7daa11e33b8c3504a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ce22309511e04d038094b051484c212", "guid": "bfdfe7dc352907fc980b868725387e98ec2088e131689a01ad1b2b5d940a7622"}], "guid": "bfdfe7dc352907fc980b868725387e98579fbecb47bc990731107675682637c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e9817874d657b3f6cd3a4f0acf89676b8a6"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e0578284590adbf8b73f59c73cb7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acb6d21f8b6048502a790d59f4fc84cb", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98be611f6cbf3440ae2e558dafe4f7c459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}