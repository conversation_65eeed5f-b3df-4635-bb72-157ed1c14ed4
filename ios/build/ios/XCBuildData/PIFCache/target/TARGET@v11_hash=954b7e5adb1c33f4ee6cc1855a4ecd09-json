{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c799077a76a1b586faa0afb657d6cef", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98259fd92bd305ccb8db6e6ef073eba6c2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9830759a14d1bfc02be249e411499c9406", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d906abd9d33aa28494af73aadc6cd96", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9830759a14d1bfc02be249e411499c9406", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c5c1ff6c519181e13612ced6a7c63691", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982ae7447e5b2916a19f1901d7d817ccd2", "guid": "bfdfe7dc352907fc980b868725387e98da3e2f74538d5f5b605b35daac527452", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988435a8e2d0786c6ab5056b531c7bb2a1", "guid": "bfdfe7dc352907fc980b868725387e98831267ed3ed1fce6a5364db7dc6136a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6f6be043bc9c7f2fa496292b9e217fe", "guid": "bfdfe7dc352907fc980b868725387e98c8f5f43b2f615326cbe210ed9bcdd848", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af1029829f986410075afac491d8843f", "guid": "bfdfe7dc352907fc980b868725387e98330432fb2ec290eb1f2bef0ef64b0249", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e37d90263666cffaa04f253e7c27f32f", "guid": "bfdfe7dc352907fc980b868725387e981789658866ee337fabd8b075f2ecd0b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987765b891d45a580c0d01de33dda019d6", "guid": "bfdfe7dc352907fc980b868725387e98460584d037a1561444edb08c91662833", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f76613d7bc90d191cdccb3f8b12321", "guid": "bfdfe7dc352907fc980b868725387e98618a8e633cd8b46bb98f703cc8067afc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fc526ab06a9b317d6e239e0c49e53ce", "guid": "bfdfe7dc352907fc980b868725387e981610abfcc3cc1f94c8f2b736600dcc9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae254ec2ebdde671bda92ad59676b3f", "guid": "bfdfe7dc352907fc980b868725387e9884d4f0a47b805c99ea53caeee086879e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98afdb505188727d683a5a7377e10cc42d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ef43ed391a24f810d2c1cff0c3c8dc6f", "guid": "bfdfe7dc352907fc980b868725387e98c2d0507d47b939b11e0e31d97336c536"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad56e599aa7a4eaea70d21dcf14a55bb", "guid": "bfdfe7dc352907fc980b868725387e982b948401dd212e8aba9bf79e3eb4077c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0561fbe73d53bd8d8d77537dfb78eb7", "guid": "bfdfe7dc352907fc980b868725387e98f017ef2d473bafee49262be8f96972ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdc452374d090c116c95d9c9093edc1b", "guid": "bfdfe7dc352907fc980b868725387e98a460083a6152857d8a68b77efaa85e1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981af1d3ac8f82ea5dd7eefdf31672d4dd", "guid": "bfdfe7dc352907fc980b868725387e98b4938ccd23b90c83f146386792e23321"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8242ec571fb117ec4de42995db6a5e4", "guid": "bfdfe7dc352907fc980b868725387e98940aea04bdf429d3656d4a385ae26bdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c70d379badc61b0b82ed7c6d598ac1c2", "guid": "bfdfe7dc352907fc980b868725387e98af856176801955cec738413e11da37fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883b572caf0cb8a8014c25fc9e58035b2", "guid": "bfdfe7dc352907fc980b868725387e98c9c9557084bc7b3a5eb2beff59a12f54"}], "guid": "bfdfe7dc352907fc980b868725387e9874c9eca2e6501a80c8215b118e2873fa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d305e1e4d9243361d7bdb28100c0789e", "guid": "bfdfe7dc352907fc980b868725387e984ad88aac6d453cd945dccee7d29f6c60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f4f7baf44f16f6e9a8550eefb5bf75", "guid": "bfdfe7dc352907fc980b868725387e9869a4b44732f4ac5b12ef2d7194e6b2e8"}], "guid": "bfdfe7dc352907fc980b868725387e98b2020bdebc602390e683e88da3428fa8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cfc04aaa91689d40ddff2e59961fd6a7", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98dc0e11f2e91dd3aca4ff00131db06687", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98b55a2f7ee4549e6f208d97d5060221a9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}