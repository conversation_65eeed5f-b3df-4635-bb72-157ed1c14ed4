<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!-- <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission
        android:name="com.google.android.gms.permission.AD_ID"
        tools:node="remove" />

    <!-- Queries for package visibility (Android 11+) -->
    <queries>
        <!-- Social apps -->
        <package android:name="com.facebook.katana" />
        <package android:name="com.facebook.orca" />
        <package android:name="com.instagram.android" />
        <package android:name="com.snapchat.android" />
        <package android:name="com.whatsapp" />
        <package android:name="com.zing.zalo" />
        <!-- Google Play Services -->
        <package android:name="com.google.android.gms" />
        <package android:name="com.android.vending" />

        <!-- SMS, phone, process text, custom tabs -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="sms" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="tel" />
        </intent>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
        <!-- Facebook provider -->
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>

    <application
        android:requestLegacyExternalStorage="true"
        android:enableOnBackInvokedCallback="true"
        android:label="${appLabel}"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">

        <!-- Facebook SDK config -->
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <!-- Google Play Services version -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
                android:name="com.google.android.geo.API_KEY"
                android:value="AIzaSyDgBrAqc1hW7NwA6tLR9P6gcek7cCKlX0o" />

        <!-- Flutter MainActivity -->
        <activity
            android:name=".MainActivity"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">

            <!-- Theme for Flutter after launch -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <meta-data
                android:name="flutterEmbedding"
                android:value="2" />

            <!-- Main launcher intent -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Deep link for custom scheme -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="com.multime.app"
                    android:host="gizmos" />
            </intent-filter>

            <!-- Deep link for login callback -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="com.multime.app"
                    android:host="login-callback" />
            </intent-filter>
        </activity>

        <!-- Facebook SDK Activity -->
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
        android:name="com.facebook.CustomTabActivity"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data android:scheme="@string/fb_login_protocol_scheme" />
        </intent-filter>
        </activity>

        <!-- Google Sign-in Activity -->
        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- UCrop Activity for image cropping -->
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/NormalTheme" />

        <!-- Facebook provider -->
        <provider
            android:name="com.facebook.internal.FacebookInitProvider"
            android:authorities="${applicationId}.facebookinitprovider"
            android:exported="false"
            tools:replace="android:authorities" />

        <!-- FileProvider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Flutter plugin support -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>