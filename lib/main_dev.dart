import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/config/environment.dart';
import 'package:multime_app/firebase_options_dev.dart';
import 'package:multime_app/main.dart' as app;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  // Load environment configuration
  AppConfig.setEnvironment(Environment.dev);
  app.main();
}
