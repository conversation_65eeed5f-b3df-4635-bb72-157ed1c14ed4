import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/components/section_icon.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/shared/widgets/app_loader/horizontal_product_shimmer.dart';
import 'package:multime_app/shared/widgets/layout/listview_layout.dart';

class ListViewGeneric<T> extends StatelessWidget {
  const ListViewGeneric({
    super.key,
    this.height = 270,
    required this.items,
    required this.itemBuilder,
    this.title = 'Trending',
    this.showMore = false,
    this.onItemTap,
    this.onShowMoreTap,
    this.emptyMessage = 'No items available',
    this.isListHome = false,
    required this.isShowButton,
    this.onPressed,
    this.filterType,
    this.isLoading = false,
    this.isSuccess = false,
    this.isItemAvailable = false,
  });
  final int height;
  final bool? isLoading;
  final bool? isSuccess;
  final bool? isItemAvailable;
  final List<T> items;
  final String title;
  final bool showMore;
  final String emptyMessage;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final void Function(T item)? onItemTap;
  final VoidCallback? onShowMoreTap;
  final bool isListHome;
  final Function()? onPressed;
  final bool isShowButton;
  final FilterType? filterType;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (filterType != null) {
          context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                filterType: filterType!,
                typeReset: 'reset',
              ));
        }
        return true;
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
            child: SectionIcon(
              title: title,
              titleStyle: isListHome
                  ? Theme.of(context).textTheme.lightBodyXLargeBold
                  : Theme.of(context).textTheme.lightBodyLargeBold,
              showActionButton: isShowButton,
              onPressed: isListHome ? onPressed : null,
            ),
          ),
          Gap(16.h),
          (isLoading! && items.isEmpty)
              ? Padding(
                  padding: EdgeInsets.only(left: AppSpacing.padding16),
                  child: THorizontalProductShimmer(),
                )
              : (isSuccess! && items.isNotEmpty)
                  ? SizedBox(
                      height: height.h,
                      child: ListviewLayout(
                        itemCount: items.length > 5 ? 5 : items.length,
                        itemBuilder: (context, i) {
                          final item = items[i];
                          return Padding(
                            padding: EdgeInsets.only(left: 10.w, right: 6),
                            child: GestureDetector(
                              onTap: () => onItemTap?.call(item),
                              child: itemBuilder(context, item, i),
                            ),
                          );
                        },
                      ),
                    )
                  : Center(
                      child: Text('Data not available',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodySmallRegular),
                    ),
          if (showMore) ...[
            Gap(16.h),
            Center(
              child: GestureDetector(
                onTap: onShowMoreTap,
                child: Text(
                  'Show more',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallRegular
                      .copyWith(
                        color: Theme.of(context).informationBase(context),
                        decoration: TextDecoration.underline,
                        decorationColor:
                            Theme.of(context).informationBase(context),
                      ),
                ),
              ),
            )
          ]
        ],
      ),
    );
  }
}
