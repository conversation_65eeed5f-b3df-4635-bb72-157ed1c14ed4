import 'package:flutter/material.dart';
import 'package:multime_app/core/components/shimmer_effect.dart';
import 'package:multime_app/core/constants/app_spacings.dart';

class LoadingCardShimmer extends StatelessWidget {
  const LoadingCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.padding16, vertical: AppSpacing.padding8h),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        children: [
          // Avatar shimmer
          const SBShimmerEffect(
            width: 48,
            height: 48,
          ),
          const SizedBox(width: 12),
          // Text shimmer
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                SBShimmerEffect(width: 100, height: 16),
                <PERSON><PERSON><PERSON><PERSON>(height: 8),
                SBShimmerEffect(width: 140, height: 14),
              ],
            ),
          ),
          const SizedBox(width: 12),
          // Button shimmer
          const SBShimmerEffect(
            width: 60,
            height: 32,
          ),
        ],
      ),
    );
  }
}
