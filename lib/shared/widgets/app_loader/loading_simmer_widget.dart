// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

// ignore: must_be_immutable
class LoadingSimmerSB extends StatelessWidget {
  double width;
  double height;
   LoadingSimmerSB({
    Key? key,
    required this.width,
    required this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: const Color.fromARGB(31, 209, 209, 209),
      highlightColor: Colors.white,
      child: Container(
        margin: EdgeInsets.only(top: 10.h),
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          color: Colors.grey.shade300,
        ),
      ),
    );
  }
}
