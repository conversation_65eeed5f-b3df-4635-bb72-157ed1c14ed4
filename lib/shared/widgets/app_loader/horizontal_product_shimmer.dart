import 'package:flutter/material.dart';
import 'package:multime_app/core/components/shimmer_effect.dart';

class THorizontalProductShimmer extends StatelessWidget {
  const THorizontalProductShimmer({super.key, this.itemCount = 4});
  final int itemCount;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      height: 120,
      child: ListView.separated(
        itemCount: itemCount,
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        separatorBuilder: (context, index) => const SizedBox(
          width: 10,
        ),
        itemBuilder: (_, __) => const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ///Image
            SBShimmerEffect(width: 120, height: 120),
            SizedBox(
              width: 10,
            ),

            ///Text
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 10 / 2,
                ),
                SBShimmerEffect(width: 160, height: 15),
                Sized<PERSON>ox(
                  height: 10 / 2,
                ),
                SBShimmerEffect(width: 110, height: 15),
                <PERSON><PERSON><PERSON><PERSON>(
                  height: 10 / 2,
                ),
                <PERSON>himmerEffect(width: 80, height: 15),
                Spacer(),
              ],
            )
          ],
        ),
      ),
    );
  }
}
