import 'package:flutter/material.dart';
import 'package:multime_app/core/components/shimmer_effect.dart';

class TVerticalProductShimmer extends StatelessWidget {
  const TVerticalProductShimmer({super.key, this.itemCount = 4});
  final int itemCount;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 180,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///Image
          SBShimmerEffect(width: 180, height: 180),
          <PERSON>zed<PERSON>ox(
            height: 12,
          ),

          ///Text
          SBShimmerEffect(width: 160, height: 15),
          SizedBox(
            height: 8,
          ),
          SBShimmerEffect(width: 110, height: 15),
        ],
      ),
    );
  }
}
