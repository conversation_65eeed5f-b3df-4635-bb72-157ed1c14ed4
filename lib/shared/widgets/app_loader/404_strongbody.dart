import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class PageNotFound extends StatelessWidget {
  const PageNotFound({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '404',
            style: Theme.of(context).textTheme.lightHeadingMediumLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Text(
            'Page not found!',
            style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 20.0.h),
            child: SvgPicture.asset(
              AppAssets.logoMultime404,
              width: 200.h,
              height: 200.h,
            ),
          ),
          Text(
            'We’re sorry, the page you requested could not be\n found. Please try again later!',
            style: Theme.of(context).textTheme.lightBodyMediumRegular,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class NoDataStrongBody extends StatelessWidget {
  const NoDataStrongBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 20.0.h),
            child: SvgPicture.asset(
              AppAssets.logoMultime404,
              width: 200.h,
              height: 200.h,
            ),
          ),
          Text(
            'Oops! Something Went Wrong.',
            style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
    );
  }
}
