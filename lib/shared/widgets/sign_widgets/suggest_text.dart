import 'package:flutter/material.dart';

// ignore: must_be_immutable
class SuggestText extends StatelessWidget {
  String text;
  Color color;
  final double horizontalPadding;

  SuggestText({
    super.key,
    required this.text,
    required this.color,
    this.horizontalPadding = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            Icons.error,
            color: color,
          ),
          Expanded(
            child: Text(
              maxLines: 2,
              "   $text",
              style: TextStyle(
                color: color,
                fontSize: 12,
              ),
            ),
          )
        ],
      ),
    );
  }
}
