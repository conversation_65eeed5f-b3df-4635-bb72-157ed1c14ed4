import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../app_loader/loading_strongbody.dart';

enum ImageType {
  svg,
  network,
  assetImage,
}

class CustomImage extends StatelessWidget {
  const CustomImage({
    super.key,
    required this.path,
    this.width,
    this.height,
    this.size,
    this.fit = BoxFit.contain,
    this.imageType = ImageType.svg,
  });

  final String path;
  final double? width;
  final double? height;
  final double? size;
  final BoxFit fit;
  final ImageType imageType;

  Widget _buildErrorWidget() {
    print('error in image widget');
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Icon(
        Icons.broken_image,
        color: Colors.grey,
        size: size,
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: LoadingStrongBody(),
    );
  }

  @override
  Widget build(BuildContext context) {
    switch (imageType) {
      case ImageType.svg:
        return SvgPicture.asset(
          path,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
        );

      case ImageType.network:
        if (path.toLowerCase().endsWith('.svg')) {
          return SvgPicture.network(
            path,
            width: width,
            height: height,
            fit: fit,
            placeholderBuilder: (context) => _buildLoadingWidget(),
            errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
          );
        }
        return CachedNetworkImage(
          imageUrl: path,
          width: width,
          height: height,
          fit: fit,
          placeholder: (context, url) => _buildLoadingWidget(),
          errorWidget: (context, url, error) => _buildErrorWidget(),
        );

      case ImageType.assetImage:
        return Image.asset(
          path,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
        );
    }
  }
}
