import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

// ignore: must_be_immutable
class CustomeButtonDating extends StatelessWidget {
  Function() onTap;
  final String text;
  final Color colorBackground;
  final Color colorText;
  final double height;
  final double width;

  CustomeButtonDating({
    super.key,
    required this.onTap,
    required this.text,
    required this.colorBackground,
    required this.colorText,
    required this.height,
    required this.width,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: colorBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          minimumSize: Size(width, height),
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: Theme.of(context)
              .textTheme
              .lightBodyLargeBold
              .copyWith(color: colorText),
        ));
  }
}
