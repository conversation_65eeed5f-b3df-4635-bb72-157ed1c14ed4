import 'package:flutter/material.dart';

class WrapButton extends StatelessWidget {
  String text;
  VoidCallback callback;
  Color color;
  Radius radius;
  bool isOutlined;
  IconData? icon;

  WrapButton(
      {super.key, required this.text,
      required this.callback,
      required this.color,
      required this.radius,
      required this.isOutlined,
      this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(10),
      width: double.infinity,
      height: 50,
      decoration: isOutlined
          ? null
          : BoxDecoration(color: color, borderRadius: BorderRadius.all(radius)),
      child: isOutlined
          ? OutlinedButton(
              onPressed: callback,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                side: BorderSide(color: color),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(radius),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(icon, color: color),
                  const SizedBox(width: 8),
                  Text(
                    text,
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color),
                  ),
                ],
              ),
            )
          : ElevatedButton(
              onPressed: callback,
              style: ElevatedButton.styleFrom(backgroundColor: color),
              child: Text(
                text,
                style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white),
              ),
            ),
    );
  }
}
