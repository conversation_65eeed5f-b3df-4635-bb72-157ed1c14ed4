import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../core/l10n/locale_keys.g.dart';

// ignore: must_be_immutable
class BuildBottomTotal extends StatelessWidget {
  String total;
  void Function()? onPressed;
  BuildBottomTotal({
    super.key,
    required this.total,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Đường kẻ ngang
        const Divider(
          color: Colors.grey, // Màu của đường kẻ
          thickness: 1, // Độ dày của đường kẻ
          height: 1, // Chiều cao của Divider
        ),
        Center(
          child: Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 20, right: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      LocaleKeys.totalCost.tr(),
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    Gap(5.h),
                    Text(
                      '\$ $total',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).backgroundRed(context),
                      ),
                    ),
                  ],
                ),
                Gap(20.w),
                // Nút Continue
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).backgroundRed(context), // Màu nền đỏ
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8), // Bo góc
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12), // Padding
                  ),
                  onPressed: onPressed,
                  child: Text(
                    LocaleKeys.continueButton.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
