import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CustomDropDownButton extends StatefulWidget {
  const CustomDropDownButton({
    super.key,
    required this.arr,
    required this.hintText,
    this.isCheck = true,
    this.hintTextColor = Colors.grey,
    this.iconColor = Colors.black,
    this.onChanged,
  });

  final List<String> arr;
  final String hintText;
  final bool isCheck;
  final Color hintTextColor;
  final Color iconColor;
  final ValueChanged<String>? onChanged; // Function to handle selection change

  @override
  State<CustomDropDownButton> createState() => _CustomDropDownButtonState();
}

class _CustomDropDownButtonState extends State<CustomDropDownButton> {
  late String _selectedText;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isShow = false;

  @override
  void initState() {
    super.initState();
    _selectedText = widget.hintText;
  }

  @override
  void deactivate() {
    // Remove overlay when widget becomes inactive (e.g., when navigating away)
    _removeOverlay();
    _isShow = false;
    super.deactivate();
  }

  void _toggleDropdown() {
    setState(() {
      if (_isShow) {
        _removeOverlay();
      } else {
        _addOverlay();
      }
      _isShow = !_isShow;
    });
  }

  void _addOverlay() {
    _overlayEntry = OverlayEntry(
      builder: (context) {
        // Tính toán chiều cao động
        double itemHeight = 50.0; // Chiều cao mỗi mục
        double listHeight = (widget.arr.length * itemHeight).toDouble();
        double maxHeight = 300.0; // Chiều cao tối đa của danh sách

        return Positioned(
          width: MediaQuery.of(context).size.width -
              32, // Cùng kích thước với dropdown
          child: CompositedTransformFollower(
            link: _layerLink,
            offset: const Offset(0, 60), // Vị trí xuất hiện của dropdown
            child: Material(
              elevation: 2,
              borderRadius: BorderRadius.circular(10),
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: listHeight > maxHeight
                      ? maxHeight
                      : listHeight, // Điều chỉnh chiều cao động
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.grey, width: 1.w),
                ),
                child: ListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero, // Xóa khoảng trắng ở đầu danh sách
                  physics: listHeight > maxHeight
                      ? const AlwaysScrollableScrollPhysics()
                      : const NeverScrollableScrollPhysics(),
                  itemCount: widget.arr.length,
                  itemBuilder: (context, index) {
                    final item = widget.arr[index];
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedText = item;
                          widget.onChanged?.call(item); // Notify parent
                          _removeOverlay();
                          _isShow = false;
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: itemHeight,
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        decoration: BoxDecoration(
                          color: _selectedText == item
                              ? Theme.of(context).textSecondary200(context)
                              : Colors.transparent,
                        ),
                        child: ListTile(
                          contentPadding: EdgeInsets
                              .zero, // Xóa padding mặc định của ListTile
                          title: Text(
                            item,
                            style: Theme.of(context)
                                .textTheme
                                .bodyLarge!
                                .copyWith(
                                    color: _selectedText == item
                                        ? Theme.of(context)
                                            .informationBase(context)
                                        : Theme.of(context)
                                            .textPrimary(context)),
                          ),
                          trailing: _selectedText == item && widget.isCheck
                              ? const Icon(Icons.check)
                              : null,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }

  @override
  void dispose() {
    // Ensure the overlay is removed when the widget is disposed
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: _toggleDropdown,
            child: Container(
              height: 50,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.grey, width: 1),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(_selectedText,
                          style: _selectedText == widget.hintText
                              ? Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumRegular
                                  .copyWith(color: widget.hintTextColor)
                              : Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumRegular),
                    ),
                  ),
                  Icon(
                    _isShow
                        ? Icons.keyboard_arrow_up_sharp
                        : Icons.keyboard_arrow_down_sharp,
                    color: widget.iconColor,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
