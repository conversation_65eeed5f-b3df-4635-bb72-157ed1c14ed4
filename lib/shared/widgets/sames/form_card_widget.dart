import 'package:flutter/material.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';

class FormCardMTM extends StatefulWidget {
  final List<String> images;
  final double width;
  final double height;

  const FormCardMTM({
    super.key,
    required this.images,
    required this.width,
    required this.height,
  });

  @override
  _FormCardMTMState createState() => _FormCardMTMState();
}

class _FormCardMTMState extends State<FormCardMTM> {
  late double _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = 0; // Khởi tạo với giá trị 0
  }

  @override
  void didUpdateWidget(FormCardMTM oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Đặt lại _currentIndex về 0 khi widget được cập nhật
    if (oldWidget.images != widget.images) {
      _currentIndex = 0;
    }
  }

  void _nextImage() {
    setState(() {
      _currentIndex = (_currentIndex + 1) % widget.images.length;
    });
  }

  void _prevourImage() {
    setState(() {
      _currentIndex = (_currentIndex - 1) % widget.images.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTapUp: (details) {
            double dx = details.localPosition.dx;
            if (dx < widget.width / 2) {
              _prevourImage();
            } else {
              _nextImage();
            }
          },
          child: SizedBox(
            width: widget.width,
            height: widget.height,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: Image.network(
                widget.images[_currentIndex.toInt()],
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
        Positioned(
          top: 25.h,
          left: 0,
          right: 0,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: DotsIndicator(
              dotsCount: widget.images.length,
              position: _currentIndex,
              decorator: DotsDecorator(
                size: const Size(75, 5.0),
                activeSize: const Size(75, 5.0),
                color: Theme.of(context).greyScale700(context),
                activeColor: AppColors.white,
                activeShape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(3.0),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(3.0),
                ),
                spacing: const EdgeInsets.symmetric(horizontal: 2.5),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
