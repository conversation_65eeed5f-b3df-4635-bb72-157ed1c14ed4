import 'package:flutter/material.dart';

class Shadows extends StatelessWidget {
  Widget child;

  Shadows({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
              color: Colors.grey.shade900,
              blurRadius: 15,
              offset: const Offset(4, 4)),
          const BoxShadow(
              color: Colors.white, blurRadius: 15, offset: Offset(-4, -4))
        ],
      ),
      child: child,
    );
  }
}
