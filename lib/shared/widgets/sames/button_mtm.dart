// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

// ignore: must_be_immutable
class ButtonSB extends StatelessWidget {
  VoidCallback onTap;
  String text;
  Color color;

  ButtonSB({
    Key? key,
    required this.onTap,
    required this.text,
    this.color =  Colors.red,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 50,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Center(
          child: Text(
            text,
            style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w400,
                ),
          ),
        ),
      ),
    );
  }
}
