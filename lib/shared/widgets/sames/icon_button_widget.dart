// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// ignore: must_be_immutable
class IconButtonMTM extends StatelessWidget {
  double sized;
  double sizedIcon;
  Color colorBackgroud;
  IconData icon;
  Color colorIcon;

  IconButtonMTM(
      {super.key,
      this.sized = 70,
      this.colorBackgroud = Colors.white,
      required this.icon,
      this.sizedIcon = 50,
      required this.colorIcon});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: sized.w,
      height: sized.h,
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: colorBackgroud,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.25),
              blurRadius: 8.0,
              offset: const Offset(0, 4),
            ),
          ]),
      child: Center(
          child: Icon(
        icon,
        size: sizedIcon.sp,
        color: colorIcon,
      )),
    );
  }
}
