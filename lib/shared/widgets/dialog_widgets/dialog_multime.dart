import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/busines_mode/presentation/widgets/true_faild_button_widget.dart';

import '../../../modules/auth_mode/presentation/widgets/strong_body_button.dart';

// ignore: non_constant_identifier_names
void AlertMultimeMin(
 { required BuildContext context,
  required String titleAlert,
  required String contentAlert,
  dynamic Function()? onTapButton2,
   String? textButton2,
   Color? colorButton2,
   Color? colorButton1,
    Color? colorText1,
   Color? colorText2,

 }
) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AlertDialog(
            backgroundColor: Colors.white,
            title: Text(
              titleAlert,
              style: TextStyle(
                fontWeight: FontWeight.w800,
                fontSize: 16.sp,
                fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            content: Text(
              contentAlert,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 14.sp,
                fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            actions: [
              TrueFaildButton(
                textButton1: 'Cancel',
                textButton2: textButton2 ?? 'Yes',
                colorButton1: colorButton1 ?? Theme.of(context).textPrimary(context),
                colorButton2: colorButton2 ?? Theme.of(context).primary(context),
                colorText1: colorText1 ?? Theme.of(context).textPrimary(context),
                onTapButton1: () => context.pop(),
                onTapButton2: onTapButton2,
                colorText2: colorText2 ??  Theme.of(context).whitePrimary(context),
              )
            ],
          ),
        ],
      );
    },
  );
}

// ignore: non_constant_identifier_names
void AlertMultimeMedium(
  {required BuildContext context,
  required String titleAlert,
  required String contentAlert,
  dynamic Function()? onTapButton,
  required String textButton,}
) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AlertDialog(
            backgroundColor: Colors.white,
            title: Text(
              titleAlert,
              style: TextStyle(
                fontWeight: FontWeight.w800,
                fontSize: 16.sp,
                fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            content: Text(
              contentAlert,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 14.sp,
                fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            actions: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: onTapButton,
                    child: Container(
                      height: 50.h,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primary(context),
                        borderRadius: BorderRadius.circular(8),
                        // color: colorButton1,
                      ),
                      child: Center(
                        child: Text(
                          textButton,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 15.h),
                  GestureDetector(
                    onTap: () => context.pop(),
                    child: Container(
                      height: 50.h,
                      decoration: BoxDecoration(
                        color:Theme.of(context).textSecondary100(context),
                        borderRadius: BorderRadius.circular(8),
                        // color: colorButton1,
                      ),
                      child: Center(
                        child: Text(
                          "Cancel",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ],
      );
    },
  );
}

// ignore: non_constant_identifier_names
void AlertMultimeBackTo(
  {required BuildContext context,
  required String titleAlert,
  required String contentAlert,
  dynamic Function()? onTapButton,
  required String textButton,}
) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AlertDialog(
            backgroundColor: Colors.white,
            title: Text(
              titleAlert,
              style: TextStyle(
                fontWeight: FontWeight.w800,
                fontSize: 16.sp,
                fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            content: Text(
              contentAlert,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 14.sp,
                fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            actions: [
              GestureDetector(
                onTap: onTapButton,
                child: Container(
                  height: 50.h,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primary(context),
                    borderRadius: BorderRadius.circular(8),
                    // color: colorButton1,
                  ),
                  child: Center(
                    child: Text(
                      textButton,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      );
    },
  );
}

void CheckboxMultiMeDialog({
  required BuildContext context,
  required List<String> value,
  required String groupValue,
  required int length,
  required VoidCallback onCanceled,
  required VoidCallback onConfirmed,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (context, setState) {
          String selectedValue = groupValue;
          return AlertDialog(
            backgroundColor: Colors.white,
            title: Text(
              'Select cancellation Reason',
              style: Theme.of(context).textTheme.labelLarge,
              textAlign: TextAlign.center,
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(
                  length,
                      (index) => ListTile(
                    leading: ClipRRect(
                      borderRadius: BorderRadius.circular(5.r),
                      child: Radio<String>(
                        value: value[index],
                        groupValue: selectedValue,
                        activeColor: Theme.of(context).informationBase(context),
                        onChanged: (value){
                          debugPrint('valuess: $value');
                          setState(() {
                            selectedValue = value!;
                          });
                        },
                      ),
                    ),
                    title: Text(
                      value[index],
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: Theme.of(context).secondary(context),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: StrongBodyButton(
                      label: 'Cancel',
                      onPressed: onCanceled,
                      textColor: Theme.of(context).whitePrimary(context),
                      backgroundColor: Theme.of(context).secondary(context),
                    ),
                  ),
                  Gap(14.w),
                  Expanded(

                    child: StrongBodyButton(
                      label: 'Confirm',
                      onPressed: onConfirmed,
                      textColor: Theme.of(context).whitePrimary(context),
                      backgroundColor: Theme.of(context).errorBase(context),
                    ),
                  ),
                ],
              )
            ],
          );
        },
      );
    },
  );
}
