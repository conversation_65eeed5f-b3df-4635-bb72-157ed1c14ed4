import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class ListviewLayout<T> extends StatelessWidget {
  const ListviewLayout({
    super.key,
    this.itemCount = 0,
    this.scrollDirection = Axis.horizontal,
    this.shrinkWrap = false,
    required this.itemBuilder,
    this.physics,
    this.controller,
    this.padding,
  });

  final int itemCount;
  final Axis scrollDirection;
  final EdgeInsets? padding;
  final NeverScrollableScrollPhysics? physics;
  final bool shrinkWrap;
  final Widget Function(BuildContext, int) itemBuilder;
  final ScrollController? controller;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        controller: controller,
        physics: physics,
        separatorBuilder: (context, index) => Gap(0),
        shrinkWrap: shrinkWrap,
        padding: padding,
        scrollDirection: scrollDirection,
        itemCount: itemCount,
        itemBuilder: itemBuilder);
  }
}
