import 'package:flutter/material.dart';
import 'package:multime_app/shared/widgets/sames/shadow.dart';

class OrderCard extends StatelessWidget {
  String imageUrl;
  String description;
  String status;
  String price;

  OrderCard(
      {super.key,
      required this.imageUrl,
      required this.price,
      required this.description,
      required this.status});

  @override
  Widget build(BuildContext context) {
    return Shadows(
        child: ListTile(
      leading: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          imageUrl,
          width: 94,
          height: 94,
          fit: BoxFit.cover,
        ),
      ),
      title: Text(
        description,
        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
      ),
      subtitle: Row(
        children: [
          Text(
            status,
            style: const TextStyle(color: Colors.green),
          ),
          const Spacer(),
          Text(price, style: const TextStyle(color: Colors.blue))
        ],
      ),
    ));
  }
}
