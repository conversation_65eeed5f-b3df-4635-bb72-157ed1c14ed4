import 'package:flutter/material.dart';
import 'package:multime_app/shared/widgets/sames/wrap_button.dart';

class MarketConfirmation extends StatelessWidget {
  bool confirm;

  MarketConfirmation({super.key, required this.confirm});

  @override
  Widget build(BuildContext context) {
    return confirm
        ? Column(
            children: [
              Center(
                child: Image.asset(
                  "assets/image/tick-circle.png",
                  width: 100,
                  height: 100,
                ),
              ),
              const Text(
                "Order confirmed",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
              ),
              const Text(
                "Thank you for your order. You will receive email confirmation shortly.",
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const Text(
                "Check the status of your order in Your Cart.",
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              WrapButton(
                  text: "Continue shopping",
                  callback: () {},
                  color: Colors.red,
                  radius: const Radius.circular(10),
                  isOutlined: false),
              WrapButton(
                  text: "Return to your cart",
                  callback: () {},
                  color: Colors.grey,
                  radius: const Radius.circular(10),
                  isOutlined: true),
            ],
          )
        : Column(
            children: [
              Center(
                child: Image.asset(
                  "assets/image/close-circle.png",
                  width: 100,
                  height: 100,
                ),
              ),
              const Text(
                "Order failed",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
              ),
              const Text(
                "There is something wrong with your order.",
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const Text(
                "Please go back and check your order again throughly.",
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              WrapButton(
                  text: "Return to your order",
                  callback: () {},
                  color: Colors.red,
                  radius: const Radius.circular(10),
                  isOutlined: false),
            ],
          );
  }
}
