import 'package:flutter/material.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/manage_post/model/product_model.dart';

class ProductCard extends StatelessWidget {
  String imageUrl;
  bool isLike;
  String name;
  String location;
  String price;

  ProductCard(
      {super.key,
      required this.imageUrl,
      required this.price,
      required this.name,
      required this.location,
      required this.isLike, required Product product});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      width: 190,
      child: Column(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  imageUrl,
                  height: 190,
                  width: 190,
                  fit: BoxFit.fill,
                ),
              ),
              Positioned(
                  top: 8,
                  left: 146,
                  child: Container(
                      height: 32,
                      width: 32,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isLike ? Colors.white : Colors.grey,
                          boxShadow: const [
                            BoxShadow(
                              color: Colors.black12,
                              blurRadius: 4.0,
                              spreadRadius: 2.0,
                              offset: Offset(2, 2),
                            )
                          ]),
                      child: Center(
                        child: IconButton(
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            onPressed: () {},
                            icon: isLike
                                ? const Icon(
                                    Icons.favorite,
                                    color: Colors.blue,
                                    size: 24,
                                  )
                                : const Icon(
                                    Icons.favorite_border,
                                    color: Colors.white,
                                    size: 24,
                                  )),
                      )))
            ],
          ),
          Text(
            name,
            maxLines: 2,
            style: const TextStyle(
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Row(
            children: [
              const Icon(Icons.location_on),
              Text(
                location,
              ),
              const Spacer(),
              Text(
                price,
                style: const TextStyle(
                    color: Colors.red, fontFamily: "Plus Jakarta Sans"),
              )
            ],
          )
        ],
      ),
    );
  }
}
