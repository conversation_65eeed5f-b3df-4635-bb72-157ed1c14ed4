import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/theme.dart';

class ScrollImage extends StatefulWidget {
  List<String> imageList;

  ScrollImage({super.key, required this.imageList});

  @override
  State<ScrollImage> createState() => _ScrollImageState();
}

class _ScrollImageState extends State<ScrollImage> {
  double _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    return SizedBox(
      width: screenWidth,
      height: screenWidth,
      child: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index.floorToDouble();
          });
        },
        children: [
          for (var i in widget.imageList)
            Stack(
              children: [
                Image.network(
                  i,
                  width: screenWidth,
                  height: screenWidth,
                  fit: BoxFit.fitWidth,
                ),
                Positioned(
                  bottom: 10,
                  left: 100,
                  right: 100,
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        color: Theme.of(context).greyScale500(context),
                        borderRadius: BorderRadius.circular(35)),
                    width: 500,
                    child: DotsIndicator(
                      decorator: DotsDecorator(
                        color: Colors.white.withOpacity(0.5),
                        activeColor: Colors.white,
                        size: const Size.square(9.0),
                        activeSize: const Size(18.0, 9.0),
                        activeShape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5.0),
                        ),
                        spacing: const EdgeInsets.symmetric(horizontal: 3),
                      ),
                      dotsCount: widget.imageList.length,
                      position: _currentIndex,
                    ),
                  ),
                )
              ],
            ),
        ],
      ),
    );
  }
}
