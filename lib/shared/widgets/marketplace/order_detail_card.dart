import 'package:flutter/material.dart';

class OrderDetailCard extends StatelessWidget {
  String imageUrl;
  String description;
  String price;

  OrderDetailCard(
      {super.key,
      required this.imageUrl,
      required this.description,
      required this.price});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      child: ListTile(
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            imageUrl,
            width: 80,
            height: 80,
            fit: BoxFit.fill,
          ),
        ),
        title: Text(
          description,
          style: const TextStyle(fontSize: 14),
        ),
        trailing: Text(
          price,
          style: const TextStyle(fontSize: 14),
        ),
      ),
    );
  }
}
