// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class TextfieldUpdate extends StatelessWidget {
  String hint;
  int maxLines;
  Color? colorHint;
  TextEditingController? controller;
  TextInputType? keyboardType;
  bool? readOnly;
  TextfieldUpdate({
    super.key,
    required this.hint,
    this.colorHint = Colors.grey,
    this.maxLines = 1,
    this.controller,
    this.readOnly,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
   
        SizedBox(height: 8.h),
        TextField(
          controller: controller,
          style: TextStyle(
            fontSize: 13.sp,
            fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
          ),
          readOnly: readOnly ?? false,
          keyboardType: keyboardType,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              fontSize: 13.sp,
              color: colorHint,
              fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
            ),
            // border: OutlineInputBorder(
            //   borderRadius: BorderRadius.circular(8),
            // ),
            // focusColor: Colors.white,
            // focusedBorder: OutlineInputBorder(
            //   borderSide: BorderSide(),
            //   borderRadius: BorderRadius.circular(8),
            // ),
            // enabledBorder: OutlineInputBorder(
            //   borderSide: const BorderSide(color: Colors.grey),
            //   borderRadius: BorderRadius.circular(8),
            // ),
          ),
        ),
      ],
    );
  }
}
