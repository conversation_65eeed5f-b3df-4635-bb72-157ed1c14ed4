import 'package:multime_app/modules/marketplace_mode/data/models/product_model_fake.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/seller_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/user_model.dart';

List<Product> fakeDataProduct = List.generate(
  59,
  (index) => Product(
      productName: "NIVEA, Nivea Creme, 30ml (Small travel size)",
      price: 150 + index,
      about:
          "Hello \nWelcome to our medical consultation and healthcare services, where we are committed to providing professional and dedicated medical care. We are proud to be a leading provider in the healthcare sector, offering comprehensive health solutions ranging from medical examinations and diagnostics to treatment and rehabilitation.",
      condition: "New",
      type: "ok",
      brand: "Nike",
      postDate: DateTime.now(),
      seller: Seller(
        sellerID: '01',
        sellerName: 'SellerName',
        address: 'Gulberg Phase 4, Lah...  ',
        shopAddress: 'Gulberg Phase 4, Lah... ',
        activeTime: DateTime.now(),
      ),
      productID: '$index',
      isFav: false,
      shopName: 'ShopName',
      image: 'https://random.imagecdn.app/600/700'),
);

List<User> fakeDataUser = [
  User(
    userID: '01',
    userName: 'UserName',
    userEmail: '<EMAIL>',
    userAddress: 'Ha Noi',
    userPhone: '**********',
    dob: DateTime.now(),
  )
];
