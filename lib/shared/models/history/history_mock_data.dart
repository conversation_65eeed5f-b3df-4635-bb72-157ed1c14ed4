final List<Map<String, dynamic>> mockSellerHistoryData = [
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789013,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 160.50,
    'paymentID': 789014,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789013,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 160.50,
    'paymentID': 789014,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789013,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 160.50,
    'paymentID': 789014,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789013,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 160.50,
    'paymentID': 789014,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Withdrawal',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789013,
  },
  {
    'status': 'Earning',
    'date': '02/10/2024',
    'amount': 160.50,
    'paymentID': 789014,
  },
];

final List<Map<String, dynamic>> mockBuyerHistoryData = [
  {
    'status': 'Completed',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Waiting for rating',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789013,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789014,
  },
  {
    'status': 'Completed',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Waiting for rating',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789013,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789014,
  },
  {
    'status': 'Completed',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Waiting for rating',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789013,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789014,
  },
  {
    'status': 'Completed',
    'date': '02/10/2024',
    'amount': 320.00,
    'paymentID': 789012,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789012,
  },
  {
    'status': 'Waiting for rating',
    'date': '02/10/2024',
    'amount': 320.50,
    'paymentID': 789013,
  },
  {
    'status': 'Delivered',
    'date': '02/10/2024',
    'amount': 160.00,
    'paymentID': 789014,
  },
];
