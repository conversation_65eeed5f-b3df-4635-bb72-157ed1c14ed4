import 'package:multime_app/modules/country/model/country/country.dart';

class UserModel {
  final int? id;
  final String? createdAt;
  final String? updatedAt;
  final String? deletedAt;
  final int? createdBy;
  final int? updatedBy;
  final String? email;
  final String? mobile;
  final String? passwordHashed;
  final String? status;
  final String? firstName;
  final String? lastName;
  final String? birthDate;
  final String? language;
  final String? currency;
  final String? address;
  final String? city;
  final String? state;
  final int? countryId;
  final String? bio;
  final String? profilePicture;
  final String? backgroundPicture;
  final bool? isEmailVerified;
  final bool? isMobileVerified;
  final String? signupMethod;
  final String? signupPlatform;
  final List<dynamic>? userRoles;
  final List<dynamic>? posts;
  final List<dynamic>? ownedCampaigns;
  final List<dynamic>? products;
  final List<dynamic>? requests;
  final List<dynamic>? assignedRequests;
  final Country? country;

  UserModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.email,
    this.mobile,
    this.passwordHashed,
    this.status,
    this.firstName,
    this.lastName,
    this.birthDate,
    this.language,
    this.currency,
    this.address,
    this.city,
    this.state,
    this.countryId,
    this.bio,
    this.profilePicture,
    this.backgroundPicture,
    this.isEmailVerified,
    this.isMobileVerified,
    this.signupMethod,
    this.signupPlatform,
    this.userRoles,
    this.posts,
    this.ownedCampaigns,
    this.products,
    this.requests,
    this.assignedRequests,
    this.country,
  });

  factory UserModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return UserModel();

    return UserModel(
      id: _parseIntSafely(json['id']),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      deletedAt: json['deleted_at'] as String?,
      createdBy: _parseIntSafely(json['created_by']),
      updatedBy: _parseIntSafely(json['updated_by']),
      email: json['email'] as String?,
      mobile: json['mobile'] as String?,
      passwordHashed: json['password_hashed'] as String?,
      status: json['status'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      birthDate: json['birth_date'] as String?,
      language: json['language'] as String?,
      currency: json['currency'] as String?,
      address: json['address'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      countryId: _parseIntSafely(json['country_id']),
      bio: json['bio'] as String?,
      profilePicture: json['profile_picture'] as String? ?? null,
      backgroundPicture: json['background_picture'] as String?,
      isEmailVerified: json['is_email_verified'] as bool?,
      isMobileVerified: json['is_mobile_verified'] as bool?,
      signupMethod: json['signup_method'] as String?,
      signupPlatform: json['signup_platform'] as String?,
      userRoles: json['user_roles'] as List<dynamic>?,
      posts: json['posts'] as List<dynamic>?,
      ownedCampaigns: json['owned_campaigns'] as List<dynamic>?,
      products: json['products'] as List<dynamic>?,
      requests: json['requests'] as List<dynamic>?,
      assignedRequests: json['assigned_requests'] as List<dynamic>?,
      country: (json['Country'] is Map<String, dynamic>)
          ? Country.fromJson(json['Country'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'created_by': createdBy,
      'updated_by': updatedBy,
      'email': email,
      'mobile': mobile,
      'password_hashed': passwordHashed,
      'status': status,
      'first_name': firstName,
      'last_name': lastName,
      'birth_date': birthDate,
      'language': language,
      'currency': currency,
      'address': address,
      'city': city,
      'state': state,
      'country_id': countryId,
      'bio': bio,
      'profile_picture': profilePicture,
      'background_picture': backgroundPicture,
      'is_email_verified': isEmailVerified,
      'is_mobile_verified': isMobileVerified,
      'signup_method': signupMethod,
      'signup_platform': signupPlatform,
      'user_roles': userRoles,
      'posts': posts,
      'owned_campaigns': ownedCampaigns,
      'products': products,
      'requests': requests,
      'assigned_requests': assignedRequests,
      'country': country?.toJson(),
    };
  }

  UserModel copyWith({
    int? id,
    String? createdAt,
    String? updatedAt,
    String? deletedAt,
    int? createdBy,
    int? updatedBy,
    String? email,
    String? mobile,
    String? passwordHashed,
    String? status,
    String? firstName,
    String? lastName,
    String? birthDate,
    String? language,
    String? currency,
    String? address,
    String? city,
    String? state,
    int? countryId,
    String? bio,
    String? profilePicture,
    String? backgroundPicture,
    bool? isEmailVerified,
    bool? isMobileVerified,
    String? signupMethod,
    String? signupPlatform,
    List<dynamic>? userRoles,
    List<dynamic>? posts,
    List<dynamic>? ownedCampaigns,
    List<dynamic>? products,
    List<dynamic>? requests,
    List<dynamic>? assignedRequests,
    Country? country,
  }) {
    return UserModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      passwordHashed: passwordHashed ?? this.passwordHashed,
      status: status ?? this.status,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      birthDate: birthDate ?? this.birthDate,
      language: language ?? this.language,
      currency: currency ?? this.currency,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      countryId: countryId ?? this.countryId,
      bio: bio ?? this.bio,
      profilePicture: profilePicture ?? this.profilePicture,
      backgroundPicture: backgroundPicture ?? this.backgroundPicture,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isMobileVerified: isMobileVerified ?? this.isMobileVerified,
      signupMethod: signupMethod ?? this.signupMethod,
      signupPlatform: signupPlatform ?? this.signupPlatform,
      userRoles: userRoles ?? this.userRoles,
      posts: posts ?? this.posts,
      ownedCampaigns: ownedCampaigns ?? this.ownedCampaigns,
      products: products ?? this.products,
      requests: requests ?? this.requests,
      assignedRequests: assignedRequests ?? this.assignedRequests,
      country: country ?? this.country,
    );
  }

  // Helper method để parse int safely
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  // Getter helpers
  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();
  String get displayName =>
      fullName.isNotEmpty ? fullName : email ?? 'Unknown User';
  bool get hasProfilePicture =>
      profilePicture != null && profilePicture!.isNotEmpty;
  String get countryName => country?.displayName ?? '';

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, firstName: $firstName, lastName: $lastName, status: $status)';
  }
}
