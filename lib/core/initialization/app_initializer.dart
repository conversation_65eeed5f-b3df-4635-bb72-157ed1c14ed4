import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:multime_app/core/config/environment.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/service/pusher_service.dart';
import 'package:multime_app/core/services/network_service.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

final ServiceLocator dependencyInjector = ServiceLocator();

class AppInitializer {
  static bool _isInitialized = false;

  /// Initialize all app services and dependencies
  static Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    try {
      // 1. Flutter binding
      WidgetsFlutterBinding.ensureInitialized();

      // 2. Environment configuration
      await _initializeEnvironment();

      // 3. Storage (must be before core services)
      await _initializeStorage();

      // 4. Core services
      await _initializeCoreServices();

      // 5. External services
      await _initializeExternalServices();

      _isInitialized = true;
    } catch (e) {
      rethrow;
    }
  }

  /// Initialize environment configuration
  static Future<void> _initializeEnvironment() async {
    await AppConfig.loadEnv();
    AppConfig.printEnvironmentInfo();
  }

  /// Initialize core services
  static Future<void> _initializeCoreServices() async {
    // Dependency injection
    await dependencyInjector.servicesLocator();

    // Network service
    await NetworkService().initialize();

    // Pusher service
    await PusherService.initialize();
  }

  /// Initialize storage systems
  static Future<void> _initializeStorage() async {
    // Hive storage
    await Hive.initFlutter();
    await Hive.openBox(GlobalStorageKey.globalStorage);
  }

  /// Initialize external services
  static Future<void> _initializeExternalServices() async {
    // Stripe
    Stripe.publishableKey = AppConfig.stripePublishableKey;
    await Stripe.instance.applySettings();

    // OneSignal
    await OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
    OneSignal.initialize(AppConfig.onesignalAppId);
    OneSignal.Notifications.requestPermission(true);

    // Device orientation
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // Contacts configuration
    FlutterContacts.config.returnUnifiedContacts = false;
  }

  /// Check if app is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
  }
}
