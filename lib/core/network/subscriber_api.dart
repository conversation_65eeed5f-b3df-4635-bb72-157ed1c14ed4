import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';

class SubscriberApi {
  final ApiClient apiClient;

  SubscriberApi(this.apiClient);

  Future<dynamic> createSubscriber({
    required int userId,
    required String deviceId,
    required String os,
  }) async {
    final body = {
      'user_id': userId,
      'device_id': deviceId,
      'os': os,
    };
    return await apiClient.request(
      path: '/admin/subscriber',
      method: ApiType.post,
      data: body,
      requiresAuth: false,
    );
  }

  Future<dynamic> deleteSubscriber({
    required String deviceId,
  }) async {
    return await apiClient.request(
      path: '/admin/subscriber/device/$deviceId',
      method: ApiType.delete,
      requiresAuth: false,
    );
  }
}
