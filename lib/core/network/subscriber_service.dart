import 'dart:io';

import 'package:multime_app/core/network/subscriber_api.dart';
import 'package:get_it/get_it.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

class SubscriberService {
  final SubscriberApi _subscriberApi;

  SubscriberService(this._subscriberApi);

  Future<void> createSubscriber(int userId) async {
    String os = '';
    if (Platform.isAndroid) {
      os = 'android';
    } else if (Platform.isIOS) {
      os = 'ios';
    }
    final oneSignalSubscriptionId = await _getOneSignalSubscriptionId();
    await _subscriberApi.createSubscriber(
      userId: userId,
      deviceId: oneSignalSubscriptionId ?? "",
      os: os,
    );
  }

  Future<void> deleteSubscriber() async {
    final oneSignalSubscriptionId = await _getOneSignalSubscriptionId();
    if (oneSignalSubscriptionId != null) {
      await subscriberService._subscriberApi.deleteSubscriber(
        deviceId: oneSignalSubscriptionId,
      );
    }
  }

  Future<String?> _getOneSignalSubscriptionId() async {
    try {
      final subscription = await OneSignal.User.pushSubscription.id;
      return subscription;
    } catch (e) {
      print('Error getting OneSignal Subscription ID: $e');
      return null; // Return null instead of empty string for consistency
    }
  }
}

Future<void> sendSubscriptionId() async {
  final storage = getIt<GlobalStorage>();
  final user = storage.user;
  if (user != null && user.id != null) {
    final userId = user.id!;
    await subscriberService.createSubscriber(userId);
  }
}

Future<void> deleteSubscriptionId() async {
  final storage = getIt<GlobalStorage>();
  final user = storage.user;
  if (user != null && user.id != null) {
    await subscriberService.deleteSubscriber();
  }
}

final subscriberService = SubscriberService(GetIt.I<SubscriberApi>());
