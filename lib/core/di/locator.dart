import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/network/subscriber_api.dart';
import 'package:multime_app/modules/auth_mode/data/services/auth_services.dart';
import 'package:multime_app/modules/auth_mode/presentation/change_password/bloc/change_password_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/forget_password/bloc/forget_password_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_bloc.dart';
import 'package:multime_app/modules/chat/data/repository/chat_repository.dart';
import 'package:multime_app/modules/country/bloc/country_bloc.dart';
import 'package:multime_app/modules/country/service/country/country_service.dart';
import 'package:multime_app/modules/create_service_mode/data/repositories/google_places_repository.dart';
import 'package:multime_app/modules/dating_mode/presentation/info/bloc/info_dating_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/address_repository.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/category_repository.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/payment_repository.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';
import 'package:multime_app/modules/my_order_mode/data/repository/my_order_repository.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import 'package:multime_app/modules/notification_mode/data/repositories/notification_repository.dart';
import 'package:multime_app/modules/notification_mode/presentation/bloc/notification_bloc.dart';
import 'package:multime_app/modules/offer/data/repository/offer_repository.dart';
import 'package:multime_app/modules/profile_view_mode/data/repositories/profile_seller_repository.dart';
import 'package:multime_app/modules/profile_view_mode/data/repositories/search_seller_repository.dart';
import 'package:multime_app/modules/setting_mode/bloc/user_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/response/check_out_product_reponse.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/bloc/detail_blog_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/bloc/detail_service_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/buyer_request_repository.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';
import 'package:multime_app/modules/strongbody.ai_mode/services/home_strongbody_ai_services.dart';
import '../../modules/auth_mode/data/repositories/auth_repository_remote.dart';
import '../../modules/strongbody.ai_mode/presentation/checkout_service/response/checkout_service_respose.dart';
import '../network/api.dart';

// GetIt is a package used for service locator to manage dependency injection
GetIt getIt = GetIt.instance;

class ServiceLocator {
  Future<void> servicesLocator() async {
    final storage = GlobalStorageImpl();
    await storage.init();
    getIt.registerSingleton<GlobalStorage>(storage);

    // Dio and API Client
    final dio = Dio();

    getIt.registerSingleton<ApiClient>(ApiClient(dio, getIt<GlobalStorage>()));
    getIt.registerSingleton<AuthServices>(
        AuthServices(apiClient: getIt<ApiClient>()));
    getIt.registerSingleton<CountryService>(CountryService(
        apiClient: getIt<
            ApiClient>())); // ✅ Register AuthRepository interface with AuthRepositoryRemote implementation
    getIt.registerFactory<AuthRepository>(() => AuthRepositoryRemote(
        getIt<ApiClient>(),
        authServices: getIt<AuthServices>()));

    //Register blocs
    getIt.registerFactory<InfoDatingBloc>(() => InfoDatingBloc());
    getIt.registerFactory<UserBloc>(() => UserBloc(
          authRepository: getIt<AuthRepository>(),
          globalStorage: getIt<GlobalStorage>(),
        ));
    getIt.registerFactory<ChangePasswordBloc>(() => ChangePasswordBloc());

    getIt.registerFactory<ProductRepositoryRemote>(
        () => ProductRepositoryRemote(getIt<ApiClient>()));

    // ✅ Remove duplicate CategoryRepositoryRemote registration
    getIt.registerFactory<CategoryRepositoryRemote>(
        () => CategoryRepositoryRemote(getIt<ApiClient>()));
    getIt.registerFactory<CheckoutProductReponseRemote>(
        () => CheckoutProductReponseRemoteImp(getIt<ApiClient>()));

    getIt.registerFactory<AddressRepository>(() =>
        AddressRepositoryRemote(getIt<ApiClient>(), getIt<GlobalStorage>()));
    getIt.registerFactory<PaymentRepository>(() =>
        PaymentRepositoryRemote(getIt<ApiClient>(), getIt<GlobalStorage>()));

    // Service - Đăng ký HomeStrongbodyAiServices TRƯỚC ServicesRepository
    getIt.registerFactory<HomeStrongbodyAiServices>(
        () => HomeStrongbodyAiServices(apiClient: getIt<ApiClient>()));

    getIt.registerFactory<HomeStrongbodyAiRepository>(() =>
        HomeStrongbodyAiRepositoryRemote(
            apiClient: getIt<ApiClient>(),
            homeStrongbodyAiServices: getIt<HomeStrongbodyAiServices>()));

    getIt.registerFactory<BuyerRequestRepository>(
        () => BuyerRequestRepositoryRemote());

    getIt.registerFactory<MyOrderRepository>(
        () => MyOrderRepositoryRemote(getIt<ApiClient>()));

    getIt.registerFactory<NotificationRepository>(
        () => NotificationRepositoryRemote(getIt<ApiClient>()));

    getIt.registerFactory<GooglePlacesRepository>(
        () => GooglePlacesRepositoryRemote(getIt<ApiClient>()));

    // RegisterBloc
    getIt.registerFactory<RegisterBloc>(
        () => RegisterBloc(authRepository: getIt<AuthRepository>()));
    getIt.registerFactory<LoginBloc>(
      () => LoginBloc(
        storage: getIt<GlobalStorage>(),
        authRepository: getIt<AuthRepository>(),
        chatRepository: getIt<ChatRepository>(),
      ),
    );
    getIt.registerFactory<OtpBloc>(() => OtpBloc(
          authRepository: getIt<AuthRepository>(),
          flowType: OtpFlowType.signup,
          email: '',
          storage: getIt<GlobalStorage>(),
        ));
    getIt.registerFactory<CountryBloc>(() => CountryBloc(
        countryService: getIt<CountryService>(),
        globalStorage: getIt<GlobalStorage>()));
    getIt.registerFactory<HomeStrongbodyAiBloc>(() => HomeStrongbodyAiBloc(
          servicesRepository: getIt<HomeStrongbodyAiRepository>(),
          globalStorage: getIt<GlobalStorage>(),
        ));
    getIt.registerFactory<DetailServiceBloc>(() => DetailServiceBloc(
          servicesRepository: getIt<HomeStrongbodyAiRepository>(),
        ));
    getIt.registerFactory<DetailBlogBloc>(() => DetailBlogBloc(
          homeStrongbodyAiRepository: getIt<HomeStrongbodyAiRepository>(),
        ));
    getIt.registerFactory<ForgetPasswordBloc>(() => ForgetPasswordBloc(
          authRepository: getIt<AuthRepository>(),
        ));

    // ✅ Register NotificationBloc
    getIt.registerFactory<NotificationBloc>(() => NotificationBloc(
        notificationRepository: getIt<NotificationRepository>()));

    getIt.registerFactory<SubscriberApi>(
        () => SubscriberApi(getIt<ApiClient>()));

    getIt.registerFactory<ChatRepository>(
      () => ChatRepositoryImpl(),
    );

    getIt.registerFactory<OfferRepository>(
      () => OfferRepositoryImpl(),
    );
    getIt.registerLazySingleton<CheckoutServiceReponseRemote>(
      () => CheckoutServiceReponseRemoteImp(getIt<ApiClient>()),
    );
    getIt.registerFactory<CheckoutServiceReponseRemoteImp>(
        () => CheckoutServiceReponseRemoteImp(getIt<ApiClient>()));

    getIt.registerFactory<ProfileSellerRepoRemote>(
        () => ProfileSellerRepoRemoteImp(getIt<ApiClient>()));

    getIt.registerFactory<SearchSellerRepoRemote>(
        () => SearchSellerRepoRemoteImp(getIt<ApiClient>()));
    getIt.registerFactory<ServiceHomePageBloc>(() =>
        ServiceHomePageBloc(myOrderRepository: getIt<MyOrderRepository>()));
  }
}
