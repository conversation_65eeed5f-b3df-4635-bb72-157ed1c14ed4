import 'package:flutter/material.dart';
import 'package:multime_app/core/config/environment.dart';
import 'package:multime_app/core/config/env.dart';

class EnvironmentBanner extends StatelessWidget {
  final Widget child;

  const EnvironmentBanner({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Chỉ hiển thị banner khi ở development mode
    if (AppConfig.isProd) {
      return child;
    }

    return Banner(
      message: 'DEV',
      location: BannerLocation.topEnd,
      color: Colors.red,
      child: child,
    );
  }
}

class EnvironmentInfo extends StatelessWidget {
  const EnvironmentInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (AppConfig.isProd) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        border: Border.all(color: Colors.orange, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '🔧 ${Env.environmentName} Environment',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Package: ${Env.packageName}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          Text(
            'API: ${Env.apiBaseUrl}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}

class DebugEnvironmentPanel extends StatelessWidget {
  const DebugEnvironmentPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (AppConfig.isProd) {
      return const SizedBox.shrink();
    }

    return ExpansionTile(
      title: Text('🔧 Environment Info'),
      children: [
        ListTile(
          title: Text('Environment'),
          subtitle: Text(Env.environmentName),
          trailing: Icon(
            AppConfig.isDev ? Icons.bug_report : Icons.rocket_launch,
            color: AppConfig.isDev ? Colors.orange : Colors.green,
          ),
        ),
        ListTile(
          title: Text('Package Name'),
          subtitle: Text(Env.packageName),
        ),
        ListTile(
          title: Text('API Base URL'),
          subtitle: Text(Env.apiBaseUrl),
        ),
        ListTile(
          title: Text('Debug Mode'),
          subtitle: Text(Env.isDebug ? 'Enabled' : 'Disabled'),
          trailing: Icon(
            Env.isDebug ? Icons.check_circle : Icons.cancel,
            color: Env.isDebug ? Colors.green : Colors.red,
          ),
        ),
        ListTile(
          title: Text('Logging'),
          subtitle: Text(Env.enableLogging ? 'Enabled' : 'Disabled'),
          trailing: Icon(
            Env.enableLogging ? Icons.check_circle : Icons.cancel,
            color: Env.enableLogging ? Colors.green : Colors.red,
          ),
        ),
      ],
    );
  }
}
