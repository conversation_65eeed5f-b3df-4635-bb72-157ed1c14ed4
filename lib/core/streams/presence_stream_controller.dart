import 'dart:async';

// ✅ Events
abstract class PresenceEvent {}

class PresenceSubscribed extends PresenceEvent {
  final String authKey;
  final int userId;

  PresenceSubscribed({required this.authKey, required this.userId});
}

class PresenceUnsubscribed extends PresenceEvent {}

// ✅ StreamController
class PresenceStreamController {
  static final StreamController<PresenceEvent> _controller =
      StreamController<PresenceEvent>.broadcast();
  static Stream<PresenceEvent> get stream => _controller.stream;
  static void emit(PresenceEvent event) => _controller.add(event);
  static void dispose() => _controller.close();
}
