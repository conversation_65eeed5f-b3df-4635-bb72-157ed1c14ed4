import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppSpacing {
  static double horizontalEdge = 20.w;
  static double verticalAvatar = 42.w;
  static double padding20 = 20.w;
  static double padding16 = 16.w;
  static double padding4 = 4.w;
  static double padding0 = 0.w;
  static double padding8 = 8.w;
  static double padding28 = 28.w;
  static double padding44 = 44.w;
  static double padding48 = 48.w;
  static double margin32 = 32.w;
  static double margin36 = 36.w;
  static double padding12 = 12.w;
  static double padding32 = 32.w;
  static double padding24 = 24.w;
  static double padding2 = 2.w;
  static double padding14 = 14.w;
  static double padding6 = 6.w;
  static double padding10 = 10.w;

  static double horizontalEdgeh = 20.h;
  static double verticalAvatarh = 42.h;
  static double padding20h = 20.h;
  static double padding16h = 16.h;
  static double padding4h = 4.h;
  static double padding0h = 0.h;
  static double padding8h = 8.h;
  static double padding10h = 10.h;
  static double padding28h = 28.h;
  static double padding44h = 44.h;
  static double padding48h = 48.h;
  static double margin32h = 32.h;
  static double margin36h = 36.h;
  static double padding12h = 12.h;
  static double padding32h = 32.h;
  static double padding24h = 24.h;
  static double padding2h = 2.h;
  static double padding14h = 14.h;
  static double padding6h = 6.h;

  // IMAGE
  static double avatarSize40 = 40.w;
  static double logoTradeMarkSize = 32.w;
  static double heightTextFromField = 51.w;
  static double iconBookingRestaurantW = 32.w;
  static double iconBookingRestaurantH = 32.h;
}
