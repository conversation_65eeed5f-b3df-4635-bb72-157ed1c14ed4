class ApiConst {
  static const String user = "user/api/v1/users";

  // User
  static const String updateProfile = "/buyer/auth/profile";
  static const String changePasswordUser = "/public/auth/change-password";
  static const String updateAvatar = '/buyer/auth/avatar';
  static const String updateBackground = '/buyer/auth/background';

  static const String getCoutries = "/countries";

  //LoginService
  static const String login = "/public/auth/login";
  static const String loginGoogle = "/public/auth/login-google";
  static const String loginFacebook = "/public/auth/login-facebook";
  static const String loginApple = "/public/auth/login-apple";

  //ForgotPassword
  static const String forgotPassword = "/forgot-password";
  static const String verifyForgotPassword = "/forgot-password/verify";
  static const String resetPassword = "/forgot-password/reset";

  //Logout
  static const String logout = "/public/auth/logout";

  //RegisterService
  static const String register = "/public/auth/multi-me/sign-up";
  static const String verifyOtp = "/auth/verify-email";
  static const String resendOtp = "/auth/resend-verify-email";

  /// MarketplaceService
  static const String marketplace = "marketplace/api/v1";
  static const String product = "$marketplace/products";
  static const String getCategoryList = "/admin/category/list";

  // static const String postProduct = "$marketplace/products";
  static const String uploadProductImage = "$marketplace/products/";
  static const String productLiked = "$product/liked";

  //SearchService part of marketplace
  static const String search = "search/api/v1";
  static const String getHomePageData = "$search/homepage/products";
  static const String searchProduct = "$search/search/products";
  static const String similarproducts = "$search/products/";
  static const String searchService = "$search/search/services";
  static const String getHomeService = "/user/strongbodyai";
  static const String similarServices = "$search/services/";

  //Strong body part of marketplace
  static const String services = "$marketplace/services";
  static const String adminServices = "/admin/service";
  static const String buyerRequests = "$marketplace/buyer-requests";
  static const String userRequest = "/user/request";

  //PaymentService
  static const String payment = "payment/api/v1";
  static const String calculateFee = "$payment/calculate-fees";
  static const String createPaymentIntents = "$payment/payment-intents";

  //CheckoutService
  static const String createOrderService = "/buyer/service/checkout";

  ///Order service
  static const String order = "order/api/v1/orders";
  static const String notificationHistory = '/admin/notification/history/user';
  static const String createOrderProduct = "/buyer/order";
  static const String checkoutOrderProduct = "/buyer/order/checkout";

  // ===== Chat/Messaging (PubNub) Service =====
  static const String conversation = '/user/conversation';
  static const String createChannel = '$conversation/create';
  static const String getConversations = '$conversation/conversations';
  static const String translateMessage = '/user/translate-file-v3';
  static const String getHeaderMessage = '/user/conversation/header';
  static const String personalUpdateToken =
      '/user/conversation/personal-update-token';
  static const String markReadAndStatus = "/user/mark-read-and-status";
  static const String presenceToken = '/user/conversation/presence-token';

  // ===== Offer ======
  static const String offer = "/offer";
  static const String sellerOffer = '/seller/offer';
  static const String userOffer = '/user/offer';

  //homeStrongService
  static const String requestBuyers = "/v1/admin/user/";
  static const String searchHome = "/search";

  static const String getServices = "/public/services";
  static const String getService = "/seller/service/";
  static const String getProduct = "/seller/product/";

  ///Seller Profile
  static const String getProfile = "/public/seller/profile/";
  static const String getProductSeller = "/seller/product";
  static const String getBlogSeller = "/admin/post/list";

  // Payment Method
  static const String addPaymentMethod = "/buyer/payment-method";

  //Shipping Address
  static const String getShippingAddress = "/buyer/shipping-addresses";
  static const String shippingAddress = "/buyer/shipping-address";

  // Google Places API
  static const String googlePlacesAutocomplete =
      "/admin/google-places/autocomplete";
  static const String googlePlacesDetails = "/admin/google-places/details";

  // My Order
  static const String orderProduct = "/buyer/order/my-orders";

  static const String orderService = "/buyer/offer/sent-offers-by-my-requests";
  static const String updateOrderProductDetails = "/buyer/order";

  static const String paymentServiceBuyer = "/buyer/offer";
  // Message
  static const String markMessageAsRead = "/user/mark-read";
  static const String getLastReadMessage = "/user/status/";

  //Seller
  static const String getSeller = "/public/seller/profile/{id}";
}
