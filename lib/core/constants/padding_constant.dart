import 'package:flutter/material.dart';

class PaddingConstants {
  const PaddingConstants._();

  // Padding All
  static const padAll02 = EdgeInsets.all(2.0);
  static const padAll04 = EdgeInsets.all(4.0);
  static const padAll06 = EdgeInsets.all(6.0);
  static const padAll08 = EdgeInsets.all(8.0);
  static const padAll10 = EdgeInsets.all(10.0);
  static const padAll12 = EdgeInsets.all(12.0);
  static const padAll14 = EdgeInsets.all(14.0);
  static const padAll16 = EdgeInsets.all(16.0);
  static const padAll18 = EdgeInsets.all(18.0);
  static const padAll20 = EdgeInsets.all(20.0);
  static const padAll31 = EdgeInsets.all(31.0);
  static const padAll32 = EdgeInsets.all(32.0);

  // Padding horizontal
  static const padSymH2 = EdgeInsets.symmetric(horizontal: 2.0);
  static const padSymH4 = EdgeInsets.symmetric(horizontal: 4.0);
  static const padSymH6 = EdgeInsets.symmetric(horizontal: 6.0);
  static const padSymH8 = EdgeInsets.symmetric(horizontal: 8.0);
  static const padSymH10 = EdgeInsets.symmetric(horizontal: 10.0);
  static const padSymH12 = EdgeInsets.symmetric(horizontal: 12.0);
  static const padSymH14 = EdgeInsets.symmetric(horizontal: 14.0);
  static const padSymH16 = EdgeInsets.symmetric(horizontal: 16.0);
  static const padSymH18 = EdgeInsets.symmetric(horizontal: 18.0);
  static const padSymH20 = EdgeInsets.symmetric(horizontal: 20.0);

  // Padding vertical
  static const padSymV2 = EdgeInsets.symmetric(vertical: 2.0);
  static const padSymV4 = EdgeInsets.symmetric(vertical: 4.0);
  static const padSymV6 = EdgeInsets.symmetric(vertical: 6.0);
  static const padSymV8 = EdgeInsets.symmetric(vertical: 8.0);
  static const padSymV10 = EdgeInsets.symmetric(vertical: 10.0);
  static const padSymV12 = EdgeInsets.symmetric(vertical: 12.0);
  static const padSymV14 = EdgeInsets.symmetric(vertical: 14.0);
  static const padSymV16 = EdgeInsets.symmetric(vertical: 16.0);
  static const padSymV18 = EdgeInsets.symmetric(vertical: 18.0);
  static const padSymV20 = EdgeInsets.symmetric(vertical: 20.0);

//
  //padding vertical and horizontal
  static const padSymHV2 = EdgeInsets.symmetric(horizontal: 2.0, vertical: 2.0);
  static const padSymHV4 = EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0);
  static const padSymHV6 = EdgeInsets.symmetric(horizontal: 6.0, vertical: 6.0);
  static const padSymHV8 = EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0);
  static const padSymHV10 =
      EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0);
  static const padSymHV12 =
      EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0);
  static const padSymHV14 =
      EdgeInsets.symmetric(horizontal: 14.0, vertical: 14.0);
  static const padSymHV16 =
      EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0);
  static const padSymHV18 =
      EdgeInsets.symmetric(horizontal: 18.0, vertical: 18.0);
  static const padSymHV20 =
      EdgeInsets.symmetric(horizontal: 20.0, vertical: 20.0);
}
