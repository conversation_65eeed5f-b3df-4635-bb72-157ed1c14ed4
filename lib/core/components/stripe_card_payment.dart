import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:flutter_stripe/flutter_stripe.dart';

class StripeCardPayment extends StatefulWidget {
  final String clientSecret;
  final String publishableKey;
  final Function(Map<String, dynamic>) onPaymentResult;
  final double amount;
  final String currency;

  const StripeCardPayment({
    super.key,
    required this.clientSecret,
    required this.publishableKey,
    required this.onPaymentResult,
    required this.amount,
    this.currency = 'USD',
  });

  @override
  State<StripeCardPayment> createState() => _StripeCardPaymentState();
}

class _StripeCardPaymentState extends State<StripeCardPayment> {
  final CardFormEditController controller = CardFormEditController();
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeStripe();
  }

  Future<void> _initializeStripe() async {
    try {
      Stripe.publishableKey = widget.publishableKey;
      await Stripe.instance.applySettings();
    } catch (e) {
      print('Error initializing Stripe: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Payment Details',
                  style: GoogleFonts.plusJakartaSans(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Payment Amount
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Amount:',
                  style: GoogleFonts.plusJakartaSans(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '\$${widget.amount.toStringAsFixed(2)}',
                  style: GoogleFonts.plusJakartaSans(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ),

          // Card Form
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Card Form
                  CardFormField(
                    key: UniqueKey(),
                    controller: controller,
                    style: CardFormStyle(
                      borderColor: Colors.grey[300]!,
                      borderRadius: 8,
                      fontSize: 16,
                      placeholderColor: Colors.grey[400]!,
                      textColor: Colors.black87,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Error Message
                  if (errorMessage != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red[600], size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              errorMessage!,
                              style: TextStyle(
                                color: Colors.red[700],
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  const Spacer(),

                  // Pay Button
                  SizedBox(
                    height: 50,
                    child: ElevatedButton(
                      onPressed: isLoading ? null : _handlePayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Pay \$${widget.amount.toStringAsFixed(2)}',
                              style: GoogleFonts.plusJakartaSans(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handlePayment() async {
    if (!controller.details.complete) {
      setState(() {
        errorMessage = 'Please complete all card details';
      });
      return;
    }

    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      // Confirm payment
      final paymentIntent = await Stripe.instance.confirmPayment(
        paymentIntentClientSecret: widget.clientSecret,
        data: PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(
            billingDetails: BillingDetails(
              name: 'Test User', // Có thể thêm field để nhập tên
            ),
          ),
        ),
      );

      if (paymentIntent.status == PaymentIntentsStatus.Succeeded) {
        // Payment successful
        widget.onPaymentResult({
          'success': true,
          'paymentIntentId': paymentIntent.id,
          'status': 'succeeded',
          'amount': widget.amount,
          'currency': widget.currency,
        });
      } else {
        // Payment failed
        widget.onPaymentResult({
          'success': false,
          'error': 'Payment failed',
          'errorCode': 'payment_failed',
          'status': paymentIntent.status.toString(),
        });
      }
    } catch (e) {
      print('Payment error: $e');

      String errorMsg = 'Payment failed. Please try again.';
      String errorCode = 'unknown_error';

      if (e.toString().contains('card_declined')) {
        errorMsg = 'Card was declined. Please try another card.';
        errorCode = 'card_declined';
      } else if (e.toString().contains('insufficient_funds')) {
        errorMsg = 'Insufficient funds. Please check your balance.';
        errorCode = 'insufficient_funds';
      } else if (e.toString().contains('incorrect_cvc')) {
        errorMsg = 'Incorrect CVC. Please check your card details.';
        errorCode = 'incorrect_cvc';
      } else if (e.toString().contains('expired_card')) {
        errorMsg = 'Card has expired. Please use another card.';
        errorCode = 'expired_card';
      }

      setState(() {
        errorMessage = errorMsg;
      });

      widget.onPaymentResult({
        'success': false,
        'error': errorMsg,
        'errorCode': errorCode,
        'errorCategory': errorCode,
        'userFriendlyMessage': errorMsg,
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }
}
