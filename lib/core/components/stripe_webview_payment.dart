import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StripeWebViewPayment extends StatefulWidget {
  final String clientSecret;
  final String publishableKey;
  final Function(Map<String, dynamic>) onPaymentResult;

  const StripeWebViewPayment({
    super.key,
    required this.clientSecret,
    required this.publishableKey,
    required this.onPaymentResult,
  });

  @override
  State<StripeWebViewPayment> createState() => _StripeWebViewPaymentState();
}

class _StripeWebViewPaymentState extends State<StripeWebViewPayment> {
  InAppWebViewController? webViewController;
  bool isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Payment Details',
                  style: GoogleFonts.plusJakartaSans(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // WebView
          Expanded(
            child: Stack(
              children: [
                FutureBuilder<String>(
                  future: _loadHtmlFromAssets(),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    return InAppWebView(
                      initialData: InAppWebViewInitialData(
                        data: snapshot.data!,
                        baseUrl: WebUri("https://multime.app"),
                      ),
                      initialSettings: InAppWebViewSettings(
                        javaScriptEnabled: true,
                        domStorageEnabled: true,
                        allowsInlineMediaPlayback: true,
                        mediaPlaybackRequiresUserGesture: false,
                      ),
                      onWebViewCreated: (controller) {
                        webViewController = controller;

                        // Handle result from JS
                        controller.addJavaScriptHandler(
                          handlerName: 'paymentResult',
                          callback: (args) {
                            final result = args[0] as Map<String, dynamic>;
                            final type = result['type'] as String;
                            final data =
                                result['data'] as Map<String, dynamic>?;

                            final success = type == 'success';
                            final response = {
                              'success': success,
                              'paymentIntentId': data?['paymentIntentId'],
                              'status': data?['status'],
                              'error': data?['errorMessage'],
                              'errorCode': data?['errorCode'],
                              'errorType': data?['errorType'],
                              'errorCategory': data?['errorCategory'],
                              'userFriendlyMessage':
                                  data?['userFriendlyMessage'],
                              'declineCode': data?['declineCode'],
                            };

                            // Gọi callback bên ngoài
                            widget.onPaymentResult(response);

                            // // Đóng WebView sau khi xử lý
                            // Navigator.of(context).pop();
                          },
                        );
                      },
                      onLoadStop: (controller, url) async {
                        setState(() {
                          isLoading = false;
                        });

                        // Gọi JS để khởi tạo Stripe
                        await controller.evaluateJavascript(source: """
                          initializeStripe('${widget.publishableKey}', '${widget.clientSecret}');
                        """);
                      },
                      onConsoleMessage: (controller, consoleMessage) {
                        // Debug thông điệp từ console WebView nếu cần
                        debugPrint("Console: ${consoleMessage.message}");
                      },
                    );
                  },
                ),

                // Loading indicator
                if (isLoading) const Center(child: CircularProgressIndicator()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<String> _loadHtmlFromAssets() async {
    try {
      return await rootBundle.loadString('assets/stripe_card_form.html');
    } catch (e) {
      return '''
        <!DOCTYPE html>
        <html>
        <head><title>Error</title></head>
        <body>
          <div style="text-align: center; padding: 50px;">
            <h2>Error loading payment form</h2>
            <p>Please try again later</p>
          </div>
        </body>
        </html>
      ''';
    }
  }
}
