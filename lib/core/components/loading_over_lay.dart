import 'package:flutter/material.dart';
import 'package:multime_app/shared/widgets/app_loader/loading_strongbody.dart';

class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withOpacity(0.3),
            child: const Center(
              child: LoadingStrongBody(),
            ),
          ),
      ],
    );
  }
}
