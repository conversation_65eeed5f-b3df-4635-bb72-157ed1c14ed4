// import 'package:flutter/material.dart';
// import 'package:flutter_stripe/flutter_stripe.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// class StripeNativePayment extends StatefulWidget {
//   final String clientSecret;
//   final Function(Map<String, dynamic>) onPaymentResult;
//   final String? customerEmail;
//   final double? total;

//   const StripeNativePayment({
//     super.key,
//     required this.clientSecret,
//     required this.onPaymentResult,
//     required this.total,
//     this.customerEmail,
//   });

//   @override
//   State<StripeNativePayment> createState() => _StripeNativePaymentState();
// }

// class _StripeNativePaymentState extends State<StripeNativePayment> {
//   CardFormEditController controller = CardFormEditController();
//   bool isLoading = false;

//   @override
//   void dispose() {
//     controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: MediaQuery.of(context).size.height * 0.8,
//       decoration: const BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
//       ),
//       child: Column(
//         children: [
//           // Header
//           Container(
//             padding: const EdgeInsets.all(16),
//             decoration: const BoxDecoration(
//               border:
//                   Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
//             ),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   'Payment Details',
//                   style: GoogleFonts.plusJakartaSans(
//                     fontSize: 18.sp,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 IconButton(
//                   onPressed: () => Navigator.of(context).pop(),
//                   icon: const Icon(Icons.close),
//                 ),
//               ],
//             ),
//           ),

//           // Card Form
//           Expanded(
//             child: Padding(
//               padding: const EdgeInsets.all(16),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.stretch,
//                 children: [
//                   Text(
//                     'Card Information',
//                     style: GoogleFonts.plusJakartaSans(
//                       fontSize: 16.sp,
//                       fontWeight: FontWeight.w600,
//                       color: Colors.grey[700],
//                     ),
//                   ),
//                   const SizedBox(height: 16),

//                   // Stripe Card Form
//                   CardFormField(
//                     controller: controller,
//                     style: CardFormStyle(
//                       borderColor: Colors.grey[300],
//                       borderRadius: 8,
//                       borderWidth: 1,
//                       backgroundColor: Colors.white,
//                       textColor: Colors.black,
//                       fontSize: 16,
//                       placeholderColor: Colors.grey[500],
//                     ),
//                   ),

//                   const Spacer(),

//                   // Pay Button
//                   SizedBox(
//                     height: 50,
//                     child: ElevatedButton(
//                       onPressed: isLoading ? null : _handlePayment,
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: Colors.blue,
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                         disabledBackgroundColor: Colors.grey[300],
//                       ),
//                       child: isLoading
//                           ? const SizedBox(
//                               height: 20,
//                               width: 20,
//                               child: CircularProgressIndicator(
//                                 strokeWidth: 2,
//                                 valueColor:
//                                     AlwaysStoppedAnimation<Color>(Colors.white),
//                               ),
//                             )
//                           : Text(
//                               'Pay Now (\$${widget.total?.toStringAsFixed(2) ?? '0.00'})',
//                               style: GoogleFonts.plusJakartaSans(
//                                 fontSize: 16.sp,
//                                 fontWeight: FontWeight.w600,
//                                 color: Colors.white,
//                               ),
//                             ),
//                     ),
//                   ),

//                   const SizedBox(height: 16),

//                   // Powered by Stripe
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Icon(
//                         Icons.security,
//                         size: 16,
//                         color: Colors.grey[600],
//                       ),
//                       const SizedBox(width: 8),
//                       Text(
//                         'Powered by Stripe',
//                         style: GoogleFonts.plusJakartaSans(
//                           fontSize: 12.sp,
//                           color: Colors.grey[600],
//                         ),
//                       ),
//                     ],
//                   ),

//                   const SizedBox(height: 16),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<void> _handlePayment() async {
//     if (!controller.details.complete) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(
//           content: Text('Please complete all card details'),
//           backgroundColor: Colors.red,
//         ),
//       );
//       return;
//     }

//     setState(() {
//       isLoading = true;
//     });

//     try {
//       // Confirm payment
//       final paymentIntent = await Stripe.instance.confirmPayment(
//         paymentIntentClientSecret: widget.clientSecret,
//         data: PaymentMethodParams.card(
//           paymentMethodData: PaymentMethodData(
//             billingDetails: BillingDetails(
//               email: widget.customerEmail,
//             ),
//           ),
//         ),
//       );

//       // Payment successful
//       widget.onPaymentResult({
//         'type': 'success',
//         'data': {
//           'paymentIntentId': paymentIntent.id,
//           'status': paymentIntent.status.name,
//         }
//       });
//     } catch (error) {
//       // Payment failed
//       String errorMessage = 'Payment failed';

//       if (error is StripeException) {
//         errorMessage = error.error.localizedMessage ?? 'Payment failed';
//       } else {
//         errorMessage = error.toString();
//       }

//       widget.onPaymentResult({
//         'type': 'error',
//         'data': errorMessage,
//       });
//     } finally {
//       if (mounted) {
//         setState(() {
//           isLoading = false;
//         });
//       }
//     }
//   }
// }
