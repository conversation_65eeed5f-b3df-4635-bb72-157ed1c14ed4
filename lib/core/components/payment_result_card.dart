import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/model/payment_result_data.dart';
import 'package:multime_app/core/themes/theme.dart';

class PaymentResultCard extends StatelessWidget {
  const PaymentResultCard({
    super.key,
    required this.data,
    this.onPrimaryAction,
    this.onSecondaryAction,
  });

  final PaymentResultData data;
  final VoidCallback? onPrimaryAction;
  final VoidCallback? onSecondaryAction;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Payment Status
          _buildPaymentStatus(),

          Gap(24.h),

          // Order Info (if provided)
          if (data.orderStatus != null) _buildOrderInfo(context),

          if (data.orderStatus != null) Gap(24.h),
          _buildAddress(
            data.recipientName ?? 'Recipient Name',
            data.phone ?? 'Phone Number',
            data.city ?? 'City',
            data.state ?? 'State',
            data.street ?? 'Street Address',
          ),
          Gap(24.h),
          _buildOrderDetail(
              context,
              data.orderCode ?? 'Order Code',
              data.itemName,
              data.quantity,
              data.itemPrice,
              data.imageUrl ?? [],
              data.shopName ?? 'Shop Name',
              data.userId!),
          Gap(24.h),

          // Payment Summary
          _buildPaymentSummary(),

          Gap(32.h),
        ],
      ),
    );
  }

  Widget _buildPaymentStatus() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16.r)),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            data.isSuccess ? Icons.check_circle : Icons.error,
            size: 64.sp,
            color: data.isSuccess ? Colors.green : Colors.red,
          ),
          Gap(16.h),
          Text(
            data.isSuccess ? 'Payment Successful' : 'Payment Failed',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: data.isSuccess ? Colors.green : Colors.red,
            ),
          ),
          Gap(8.h),
          Text(
            '\$${data.amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.w700,
              color: Colors.black87,
            ),
          ),
          if (data.paymentMethod != null)
            _buildInfoRow('Method', data.paymentMethod!),
          if (data.paymentMethod != null && data.transactionDate != null)
            Gap(8.h),
          if (data.transactionDate != null)
            _buildInfoRow('Transaction Date',
                '${data.transactionDate!.day}/${data.transactionDate!.month}/${data.transactionDate!.year}'),
          if ((data.paymentMethod != null || data.transactionDate != null) &&
              data.transactionId != null)
            Gap(8.h),
          if (data.transactionId != null)
            _buildInfoRow('Transaction ID', data.transactionId!),
        ],
      ),
    );
  }

  Widget _buildOrderInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          if (data.orderStatus != null)
            _buildOrderStatus('Order Status', data.orderStatus!, context),
          if (data.orderStatus != null && data.orderTime != null) Gap(12.h),
          if (data.orderTime != null)
            _buildInfoRow('Order Time',
                '${data.orderTime!.day}/${data.orderTime!.month}/${data.orderTime!.year}'),
        ],
      ),
    );
  }

  Widget _buildPaymentSummary() {
    final double calculatedSubtotal =
        data.subtotal ?? (data.itemPrice * data.quantity);
    final double total = calculatedSubtotal - data.discount + data.platformFee;

    return Container(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Information',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          Gap(16.h),
          _buildPaymentRow(
            'Subtotal (${data.quantity} ${data.unit ?? 'items'})',
            '\$${calculatedSubtotal.toStringAsFixed(2)}',
          ),
          if (data.discount > 0) ...[
            Gap(8.h),
            _buildPaymentRow(
              'You saved',
              '-\$${data.discount.toStringAsFixed(2)}',
              isDiscount: true,
            ),
          ],
          if (data.platformFee > 0) ...[
            Gap(8.h),
            _buildPaymentRow(
              'Platform Fee',
              '\$${data.platformFee.toStringAsFixed(2)}',
            ),
          ],
          Gap(8.h),
          _buildPaymentRow(
            'Order Total',
            '\$${total.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatus(String label, String value, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: _getStatusColor(value, context),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            textAlign: TextAlign.center,
         
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w700,
              color: _getStatusColorText(value, context),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Theme.of(context).successLight(context);
      case 'awaiting payment':
        return Theme.of(context).errorLight(context);
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColorText(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Theme.of(context).successBase(context);
      case 'awaiting payment':
        return Theme.of(context).errorBase(context);
      default:
        return Colors.grey;
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildAddress(String name, String phone, String city, String state, String street) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Address',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          Gap(8.h),
          Row(
            children: [
              Text(
                name,
                style: TextStyle(fontSize: 14.sp, color: Colors.black87),
              ),
              Gap(8.w),
              Text(
                phone,
                style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
              ),
            ],
          ),
          Gap(4.h),
          Text(
            "$street, $city, $state",
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetail(
      BuildContext context,
      String orderCode,
      final String? title,
      final int quantity,
      final double? price,
      final List<String> mediaUrls,
      final String shopName,
      final int userId) {
    return Container(
      padding:
          EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).lightGrey(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            " Order code : $orderCode",
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: Theme.of(context).greyScale700(context),
                  fontSize: 12.sp,
                ),
          ),
          _buildShopName(context, shopName),
          Gap(12.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              mediaUrls.isNotEmpty
                  ? Container(
                      height: 56.h,
                      width: 56.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: NetworkImage(mediaUrls.first),
                          fit: BoxFit.cover,
                        ),
                      ),
                    )
                  : Container(
                      height: 80.h,
                      width: 80.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[200],
                      ),
                      child: Icon(
                        Icons.image,
                        size: 25.sp,
                        color: Theme.of(context).greyScale700(context),
                      ),
                    ),
              Gap(12.w),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Product title with ellipsis
                    Expanded(
                      child: Text(
                        title ?? 'Product Title',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: GoogleFonts.plusJakartaSans(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Gap(8.w),
                    Text(
                      "x$quantity",
                      style: GoogleFonts.plusJakartaSans(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Gap(12.w),
                    Text(
                      "\$${price?.toStringAsFixed(2) ?? '0.00'}",
                      style: GoogleFonts.plusJakartaSans(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildShopName(BuildContext context, String shopName) {
    return Row(
      children: [
        Text(
          shopName,
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w700,
              ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: () {},
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Theme.of(context).errorBase(context),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Chat',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).errorBase(context),
                      ),
                ),
                Gap(4.w),
                SvgPicture.asset(
                  AppAssets.iconChatSvg,
                  colorFilter: ColorFilter.mode(
                    Theme.of(context).errorBase(context),
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _buildPaymentRow(
    String label,
    String value, {
    bool isDiscount = false,
    bool isVoucher = false,
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            color: isTotal ? Colors.black87 : Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            color: isDiscount
                ? Colors.green
                : isVoucher
                    ? Colors.blue
                    : isTotal
                        ? Colors.red
                        : Colors.black87,
          ),
        ),
      ],
    );
  }
}
