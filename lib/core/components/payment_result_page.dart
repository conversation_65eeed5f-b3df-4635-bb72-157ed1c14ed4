import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/components/payment_result_card.dart';
import 'package:multime_app/core/model/payment_result_data.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

class PaymentResultPage extends StatelessWidget {
  const PaymentResultPage(
      {super.key,
      required this.data,
      this.onPrimaryAction,
      this.onSecondaryAction});

  final PaymentResultData data;
  final VoidCallback? onPrimaryAction;
  final VoidCallback? onSecondaryAction;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(data.isSuccess ? 'Payment Successful' : 'Payment Failed'),
        backgroundColor: data.isSuccess ? Colors.green : Colors.red,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: PaymentResultCard(
            data: data,
            onPrimaryAction: onPrimaryAction,
            onSecondaryAction: onSecondaryAction,
          ),
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (onPrimaryAction != null)
              Expanded(
                child: StrongBodyButton(
                  label: data.primaryActionText!,
                  onPressed: onPrimaryAction,
                  textColor: Theme.of(context).secondaryBase(context),
                  backgroundColor: Colors.white,
                  borderColor: Theme.of(context).secondaryBase(context),
                  fontSize: 13,
                ),
              ),
            Gap(8.w),
            if (onSecondaryAction != null)
              Expanded(
                child: StrongBodyButton(
                  label: data.secondaryActionText!,
                  onPressed: onSecondaryAction,
                  textColor: Colors.white,
                  backgroundColor: Theme.of(context).secondaryBase(context),
                  fontSize: 13,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
