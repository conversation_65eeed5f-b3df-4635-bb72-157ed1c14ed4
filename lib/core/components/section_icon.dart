import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../constants/app_assets.dart';

class SectionIcon extends StatelessWidget {
  const SectionIcon(
      {super.key,
      this.textColor,
      this.showActionButton = true,
      required this.title,
      this.onPressed,
      this.iconColor,
      this.titleColor,
      this.icon,
      this.titleStyle});

  final Color? textColor;
  final bool showActionButton;
  final String title;
  final void Function()? onPressed;
  final Color? titleColor, iconColor;
  final Widget? icon;
  final TextStyle? titleStyle;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: titleStyle,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (showActionButton)
          GestureDetector(
            onTap: onPressed,
            child: SizedBox(
              height: 24.h,
              width: 24.w,
              child: SvgPicture.asset(
                AppAssets.arrowRightSvg,
                height: 24.h,
                width: 24.w,
                fit: BoxFit.cover,
              ),
            ),
          )
        // IconButton(
        //   onPressed: onPressed,
        //   icon: icon ??
        //       Icon(
        //         Icons.arrow_forward_ios,
        //         size: 16.w,
        //         color: iconColor ?? Theme.of(context).textSecondary(context),
        //       ),
        // ),
      ],
    );
  }
}
