class Contacts {
  final String? channelID;
  final int? userID;
  final String? avatar;
  final String? name;
  final bool isOnline;

  const Contacts({
    this.channelID,
    this.avatar,
    this.name,
    required this.isOnline,
    this.userID,
  });

  factory Contacts.fromJson(Map<String, dynamic> json) {
    return Contacts(
      channelID: json['channel_id'] ?? '',
      userID: json['user_id'],
      avatar: json['avatar'] ?? '',
      name: json['name'] ?? '',
      isOnline: json['is_online'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'channel_id': channelID,
      'user_id': userID,
      'avatar': avatar,
      'name': name,
      'is_online': isOnline,
    };
  }
}
