class Conversations {
  final String channelName;
  final int? userID;
  final String? userName;
  final String? avatar;
  final String? message;
  final String? messageType;
  final String? timetoken;
  final int? senderID;
  final bool isOnline;
  final int unreadCount;

  Conversations({
    required this.channelName,
    this.userID,
    this.userName,
    this.avatar,
    this.message,
    this.messageType,
    this.timetoken,
    this.senderID,
    required this.isOnline,
    required this.unreadCount,
  });

  factory Conversations.fromJson(Map<String, dynamic> json) {
    final message = json['last_message'] as Map<String, dynamic>?;

    return Conversations(
      channelName: json['channel_id'] as String? ?? '',
      userID: (message?['user_info']?['user_id'] is int)
          ? message!['user_info']['user_id'] as int
          : int.tryParse(message?['user_info']?['user_id']?.toString() ?? '') ??
              0,
      userName: message?['user_info']?['name'] as String? ?? '',
      avatar: message?['user_info']?['avatar'] as String? ?? '',
      message: message?['message']?['content'] as String? ?? '',
      messageType: message?['message']?['type'] as String? ?? '',
      timetoken: message?['timetoken'] as String? ?? '',
      senderID: (message?['sender_id'] is int)
          ? message!['sender_id'] as int
          : int.tryParse(message?['sender_id']?.toString() ?? '') ?? 0,
      isOnline: json['is_online'] as bool? ?? false,
      unreadCount: json['unread_count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'channel_id': channelName,
      'user_id': userID,
      'name': userName,
      'avatar': avatar,
      'last_message': message,
      'message_type': messageType,
      'timetoken': timetoken,
      'sender_id': senderID,
      'is_online': isOnline,
      'unread_count': unreadCount,
    };
  }
}
