class PersonalUpdateChannelToken {
  final String token;
  final String channelID;
  final int ttl;

  PersonalUpdateChannelToken({
    required this.token,
    required this.channelID,
    required this.ttl,
  });

  factory PersonalUpdateChannelToken.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;

    return PersonalUpdateChannelToken(
      token: data?['token'] as String? ?? '',
      channelID: data?['channel_id'] as String? ?? '',
      ttl: data?['ttl'] is int
          ? data!['ttl'] as int
          : int.tryParse(data?['ttl']?.toString() ?? '') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'channelID': channelID,
      'ttl': ttl,
    };
  }
}
