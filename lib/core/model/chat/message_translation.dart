class MessageTranslation {
  final int? statusCode;
  final String? originalText;
  final String? translatedText;

  MessageTranslation({
    this.statusCode,
    this.originalText,
    this.translatedText,
  });

  factory MessageTranslation.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;

    return MessageTranslation(
      statusCode: json['code'] is int
          ? json['code'] as int
          : int.tryParse(json['code']?.toString() ?? '') ?? 0,
      originalText: data?['original_text'] as String? ?? '',
      translatedText: data?['translated_text'] as String? ?? '',
    );
  }
}
