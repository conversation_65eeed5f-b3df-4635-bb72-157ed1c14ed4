class MockMessages {
  final int uid;
  final String avatar;
  final String name;
  final bool isOnline;
  final int unreadCount;

  MockMessages({
    required this.uid,
    required this.avatar,
    required this.name,
    required this.isOnline,
    required this.unreadCount,
  });
}

final List<MockMessages> mockMessages = [
  MockMessages(
    uid: 26862,
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    isOnline: true,
    unreadCount: 2,
  ),
  MockMessages(
    uid: 2,
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    isOnline: false,
    unreadCount: 0,
  ),
  MockMessages(
    uid: 3,
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    name: 'Đỗ Viết <PERSON>',
    isOnline: true,
    unreadCount: 1,
  ),
  MockMessages(
    uid: 4,
    avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
    name: '<PERSON>r<PERSON><PERSON><PERSON>',
    isOnline: false,
    unreadCount: 0,
  ),
  MockMessages(
    uid: 5,
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
    name: 'Hoàng Phú Trọng',
    isOnline: true,
    unreadCount: 3,
  ),
  MockMessages(
    uid: 6,
    avatar: 'https://randomuser.me/api/portraits/men/6.jpg',
    name: 'Nguyễn Đại Nghĩa',
    isOnline: false,
    unreadCount: 1,
  ),
  MockMessages(
    uid: 7,
    avatar: 'https://randomuser.me/api/portraits/men/7.jpg',
    name: 'Trần Đăng Trung',
    isOnline: true,
    unreadCount: 5,
  ),
  MockMessages(
    uid: 8,
    avatar: 'https://randomuser.me/api/portraits/men/8.jpg',
    name: 'Ngô Hồng Phong',
    isOnline: false,
    unreadCount: 0,
  ),
  MockMessages(
    uid: 9,
    avatar: 'https://randomuser.me/api/portraits/men/9.jpg',
    name: 'Ngô Văn Quân',
    isOnline: true,
    unreadCount: 0,
  ),
];
