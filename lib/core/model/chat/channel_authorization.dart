class ChannelAuthorization {
  final String channelName;
  final int? uid;
  final String token;

  ChannelAuthorization({
    required this.channelName,
    required this.uid,
    required this.token,
  });

  factory ChannelAuthorization.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;

    return ChannelAuthorization(
      channelName: data?['channel_id'] as String? ?? '',
      uid: data?['user_id'] is int
          ? data!['user_id'] as int
          : int.tryParse(data?['user_id']?.toString() ?? '') ?? 0,
      token: data?['token'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'channel_id': channelName,
      'user_id': uid,
      'token': token,
    };
  }
}
