class ReadByUser {
  final int userID;
  final String username;
  final String avatar;
  final String readAt;

  ReadByUser({
    required this.userID,
    required this.username,
    required this.avatar,
    required this.readAt,
  });

  factory ReadByUser.fromJson(Map<String, dynamic> json) {
    return ReadByUser(
      userID: json['user_id'] is int
          ? json['user_id'] as int
          : int.tryParse(json['user_id'].toString()) ?? 0,
      username: json['user_name'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      readAt: json['read_at'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userID,
      'user_name': username,
      'avatar': avatar,
      'read_at': readAt,
    };
  }
}

class MarkReadResponse {
  final String channelID;
  final int senderID;
  final String timetoken;
  final List<ReadByUser> readBy;

  MarkReadResponse({
    required this.channelID,
    required this.senderID,
    required this.timetoken,
    required this.readBy,
  });

  factory MarkReadResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;

    return MarkReadResponse(
      channelID: data?['channel_id'] as String? ?? '',
      senderID: data?['sender_id'] is int
          ? data!['sender_id'] as int
          : int.tryParse(data!['sender_id'].toString()) ?? 0,
      timetoken: data['timetoken'] as String? ?? '',
      readBy: (data['read_by'] as List<dynamic>?)
              ?.map((item) => ReadByUser.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'channel_id': channelID,
      'sender_id': senderID,
      'timetoken': timetoken,
      'read_by': readBy.map((user) => user.toJson()).toList(),
    };
  }

  bool isReadByUser(int userID) {
    return readBy.any(
      (user) => user.userID == userID,
    );
  }

  factory MarkReadResponse.empty() {
    return MarkReadResponse(
      channelID: '',
      senderID: 0,
      timetoken: '',
      readBy: const [],
    );
  }
}
