import 'package:multime_app/core/model/chat/mark_read_response.dart';
import 'package:multime_app/core/model/offer/create_offer_response.dart';
import 'package:pubnub/pubnub.dart';

sealed class ChatItem {}

class Message extends ChatItem {
  final ChatMessage message;

  Message(this.message);
}

class Timestamp extends ChatItem {
  final DateTime timestamp;

  Timestamp(this.timestamp);
}

class ReadReceipt extends ChatItem {
  final MarkReadResponse markReadResponse;

  ReadReceipt(this.markReadResponse);
}

class ChatMessage {
  // Basic message properties
  final String timetoken;
  final String channel;
  final int uuid;
  final String message;

  // Replying properties
  final ChatMessage? parentMessage;

  // Sticker properties
  final String? sticker;

  // Voice Translation properties
  final String? translated;

  // Image properties
  final String? image;
  final String? imageID;
  final String? imageName;
  final String? pendingImage;

  // File properties
  final String? file;
  final String? fileID;
  final String? fileName;
  final String? pendingFile;
  final int? fileSize;

  // Indicates if the message is pending
  final bool isPending;

  // Offer message properties
  final int? offerID;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? orderNo;
  final String? title;
  final String? description;
  final double? price;
  final String? status;
  final String? type;
  final int? sellerID;
  final int? customerID;
  final int? requestID;
  final int? serviceID;
  final List<OfferFile>? files;
  final String? paymentStatus;
  final String? paymentMethod;
  final String? transactionID;

  ChatMessage({
    required this.timetoken,
    required this.channel,
    required this.uuid,
    required this.message,
    this.parentMessage,
    this.sticker,
    this.translated,
    this.image,
    this.imageID,
    this.imageName,
    this.pendingImage,
    this.file,
    this.fileID,
    this.fileName,
    this.pendingFile,
    this.fileSize,
    this.isPending = false,
    this.offerID = 0,
    this.createdAt,
    this.updatedAt,
    this.orderNo,
    this.title,
    this.description,
    this.price,
    this.status,
    this.type,
    this.sellerID,
    this.customerID,
    this.requestID,
    this.serviceID,
    this.files,
    this.paymentStatus,
    this.paymentMethod,
    this.transactionID,
  });

  factory ChatMessage.fromHistory(
    BaseMessage message,
    String channel,
    PubNub instance,
  ) {
    final content = message.content;

    if (content is Map &&
        content['type'] == 'offer' &&
        content['offer'] is Map) {
      final offer = content['offer'] as Map;

      return ChatMessage(
        timetoken: message.publishedAt.value.toString(),
        channel: channel,
        uuid: int.tryParse(content['uuid']?.toString() ?? '') ?? 0,
        message: content['content']?.toString() ?? '',
        offerID: int.tryParse(offer['id']?.toString() ?? '0') ?? 0,
        createdAt: offer['created_at'] != null
            ? DateTime.tryParse(offer['created_at'].toString())
            : null,
        updatedAt: offer['updated_at'] != null
            ? DateTime.tryParse(offer['updated_at'].toString())
            : null,
        orderNo: offer['order_no']?.toString() ?? '',
        title: offer['title']?.toString() ?? '',
        description: offer['description']?.toString() ?? '',
        price: offer['price'] is num
            ? (offer['price'] as num).toDouble()
            : double.tryParse(offer['price']?.toString() ?? '0') ?? 0.0,
        status: offer['status']?.toString() ?? '',
        type: offer['type']?.toString() ?? '',
        sellerID: int.tryParse(offer['seller_id']?.toString() ?? '0') ?? 0,
        customerID: int.tryParse(offer['customer_id']?.toString() ?? '0') ?? 0,
        requestID: int.tryParse(offer['request_id']?.toString() ?? '0') ?? 0,
        serviceID: int.tryParse(offer['service_id']?.toString() ?? '0') ?? 0,
        files: (offer['files'] as List<dynamic>?)
            ?.where((file) => file != null)
            .map((file) => OfferFile.fromJson(file as Map<String, dynamic>))
            .toList(),
        paymentStatus: offer['payment_status']?.toString() ?? '',
        paymentMethod: offer['payment_method']?.toString() ?? '',
        transactionID: offer['transaction_id']?.toString() ?? '',
      );
    }

    if (content is Map &&
        content['message'] is Map &&
        content['file'] is Map &&
        content['message']['type'] == 'image') {
      return ChatMessage(
        timetoken: message.publishedAt.value.toString(),
        channel: channel,
        uuid: int.tryParse(content['message']['uuid']?.toString() ?? '') ?? 0,
        message: content['message']['content']?.toString() ?? '',
        parentMessage: content['parent'] != null
            ? _parseParentMessage(content['parent'])
            : null,
        image: (content['message']['type']?.toString() == 'image' &&
                content['file']?['id'] != null &&
                content['file']?['name'] != null)
            ? instance.files
                .getFileUrl(
                  channel,
                  content['file']!['id'],
                  content['file']!['name'],
                )
                .toString()
            : null,
        imageID: content['file']?['id']?.toString() ?? '',
        imageName: content['file']?['name']?.toString() ?? '',
      );
    }

    if (content is Map &&
        content['message'] is Map &&
        content['file'] is Map &&
        content['message']['type'] == 'file') {
      return ChatMessage(
        timetoken: message.publishedAt.value.toString(),
        channel: channel,
        uuid: int.tryParse(content['message']['uuid']?.toString() ?? '') ?? 0,
        message: content['message']['content']?.toString() ?? '',
        parentMessage: content['parent'] != null
            ? _parseParentMessage(content['parent'])
            : null,
        file: (content['message']['type']?.toString() == 'file' &&
                content['file']?['id'] != null &&
                content['file']?['name'] != null)
            ? instance.files
                .getFileUrl(
                  channel,
                  content['file']!['id'],
                  content['file']!['name'],
                )
                .toString()
            : null,
        fileID: content['file']?['id']?.toString() ?? '',
        fileName: content['file']?['name']?.toString() ?? '',
        fileSize: content['message']?['size']?.toInt() ?? null,
      );
    }

    return ChatMessage(
      timetoken: message.publishedAt.value.toString(),
      channel: channel,
      uuid: int.tryParse(content['uuid']?.toString() ?? '') ?? 0,
      message: content is Map && content['content'] != null
          ? content['content'].toString()
          : content.toString(),
      parentMessage: content is Map && content['parent'] != null
          ? _parseParentMessage(content['parent'])
          : null,
      sticker: content is Map && content['sticker'] != null
          ? content['sticker'].toString()
          : null,
      translated: content is Map && content['translated'] != null
          ? content['translated'].toString()
          : null,
      isPending: false,
    );
  }

  factory ChatMessage.fromEnvelope(
    BaseMessage message,
    PubNub instance,
  ) {
    final original = message.originalMessage;

    if (original?['d'] is Map &&
        original['d']['type'] == 'offer' &&
        original['d']['offer'] is Map) {
      final offer = original['d']['offer'] as Map;

      return ChatMessage(
        timetoken: original['p']?['t']?.toString() ??
            message.publishedAt.value.toString(),
        channel: original['c']?.toString() ?? '',
        uuid: int.tryParse(original['d']?['uuid']?.toString() ?? '') ?? 0,
        message: original['d']?['content']?.toString() ?? '',
        offerID: int.tryParse(offer['id']?.toString() ?? '0') ?? 0,
        createdAt: offer['created_at'] != null
            ? DateTime.tryParse(offer['created_at'].toString())
            : null,
        updatedAt: offer['updated_at'] != null
            ? DateTime.tryParse(offer['updated_at'].toString())
            : null,
        orderNo: offer['order_no']?.toString() ?? '',
        title: offer['title']?.toString() ?? '',
        description: offer['description']?.toString() ?? '',
        price: offer['price'] is num
            ? (offer['price'] as num).toDouble()
            : double.tryParse(offer['price']?.toString() ?? '0') ?? 0.0,
        status: offer['status']?.toString() ?? '',
        type: offer['type']?.toString() ?? '',
        sellerID: int.tryParse(offer['seller_id']?.toString() ?? '0') ?? 0,
        customerID: int.tryParse(offer['customer_id']?.toString() ?? '0') ?? 0,
        requestID: int.tryParse(offer['request_id']?.toString() ?? '0') ?? 0,
        serviceID: int.tryParse(offer['service_id']?.toString() ?? '0') ?? 0,
        files: (offer['files'] as List<dynamic>?)
            ?.where((file) => file != null)
            .map((file) => OfferFile.fromJson(file as Map<String, dynamic>))
            .toList(),
        paymentStatus: offer['payment_status']?.toString() ?? '',
        paymentMethod: offer['payment_method']?.toString() ?? '',
        transactionID: offer['transaction_id']?.toString() ?? '',
      );
    }

    if (original?['d'] is Map &&
        original!['d']['message'] is Map &&
        original['d']['file'] is Map &&
        original['d']['message']['type'] == 'image') {
      return ChatMessage(
        timetoken: original['p']?['t']?.toString() ??
            message.publishedAt.value.toString(),
        channel: original['c']?.toString() ?? '',
        uuid:
            int.tryParse(original['d']['message']?['uuid']?.toString() ?? '') ??
                0,
        message: original['d']['message']?['content']?.toString() ?? '',
        parentMessage: original['d']['parent'] != null
            ? _parseParentMessage(original['d']['parent'])
            : null,
        image: (original['d']['message']?['type']?.toString() == 'image' &&
                original['d']['file']?['id'] != null &&
                original['d']['file']?['name'] != null)
            ? instance.files
                .getFileUrl(
                  original['c']?.toString() ?? '',
                  original['d']['file']['id'],
                  original['d']['file']['name'],
                )
                .toString()
            : null,
        imageID: original['d']['file']?['id']?.toString() ?? '',
        imageName: original['d']['file']?['name']?.toString() ?? '',
      );
    }

    if (original?['d'] is Map &&
        original!['d']['message'] is Map &&
        original['d']['file'] is Map &&
        original['d']['message']['type'] == 'file') {
      return ChatMessage(
        timetoken: original['p']?['t']?.toString() ??
            message.publishedAt.value.toString(),
        channel: original['c']?.toString() ?? '',
        uuid:
            int.tryParse(original['d']['message']?['uuid']?.toString() ?? '') ??
                0,
        message: original['d']['message']?['content']?.toString() ?? '',
        parentMessage: original['d']['parent'] != null
            ? _parseParentMessage(original['d']['parent'])
            : null,
        file: (original['d']['message']?['type']?.toString() == 'file' &&
                original['d']['file']?['id'] != null &&
                original['d']['file']?['name'] != null)
            ? instance.files
                .getFileUrl(
                  original['c']?.toString() ?? '',
                  original['d']['file']['id'],
                  original['d']['file']['name'],
                )
                .toString()
            : null,
        fileID: original['d']['file']?['id']?.toString() ?? '',
        fileName: original['d']['file']?['name']?.toString() ?? '',
        fileSize: original['d']['message']?['size']?.toInt() ?? null,
      );
    }

    return ChatMessage(
      timetoken: original?['p']?['t']?.toString() ??
          message.publishedAt.value.toString(),
      channel: original?['c']?.toString() ?? '',
      uuid: int.tryParse(original?['i']?.toString() ?? '') ?? 0,
      message: (original?['d'] is Map && original?['d']['content'] != null)
          ? original!['d']['content'].toString()
          : original?['d']?.toString() ?? '',
      parentMessage: original?['d'] is Map && original?['d']['parent'] != null
          ? _parseParentMessage(original['d']['parent'])
          : null,
      sticker: (original?['d'] is Map && original?['d']['sticker'] != null)
          ? original['d']['sticker'].toString()
          : null,
      translated:
          (original?['d'] is Map && original?['d']['translated'] != null)
              ? original['d']['translated'].toString()
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timetoken': timetoken,
      'channel': channel,
      'uuid': uuid,
      'message': message,
      'sticker': sticker,
      'translated': translated,
      'image': image,
      'imageID': imageID,
      'imageName': imageName,
      'pendingImage': pendingImage,
      'file': file,
      'fileID': fileID,
      'fileName': fileName,
      'pendingFile': pendingFile,
      'fileSize': fileSize,
      'isPending': isPending,
      'offerID': offerID,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'orderNo': orderNo,
      'title': title,
      'description': description,
      'price': price,
      'status': status,
      'type': type,
      'sellerID': sellerID,
      'customerID': customerID,
      'requestID': requestID,
      'serviceID': serviceID,
      'files': files?.map((file) => file.toJson()).toList() ?? [],
      'paymentStatus': paymentStatus,
      'paymentMethod': paymentMethod,
      'transactionID': transactionID
    };
  }

  static ChatMessage? _parseParentMessage(dynamic data) {
    if (data == null || data is! Map) return null;

    try {
      return ChatMessage(
        timetoken: data['timetoken']?.toString() ?? '',
        channel: data['channel']?.toString() ?? '',
        uuid: int.tryParse(data['uuid']?.toString() ?? '0') ?? 0,
        message: data['message']?.toString() ?? '',
        sticker: data['sticker']?.toString(),
        translated: data['translated']?.toString(),
        image: data['image']?.toString(),
        imageID: data['imageID']?.toString(),
        imageName: data['imageName']?.toString(),
        pendingImage: data['pendingImage']?.toString(),
        file: data['file']?.toString(),
        fileID: data['fileID']?.toString(),
        fileName: data['fileName']?.toString(),
        pendingFile: data['pendingFile']?.toString(),
        fileSize: int.tryParse(data['fileSize']?.toString() ?? '0'),
        isPending: data['isPending'] == true,
        offerID: int.tryParse(data['offerID']?.toString() ?? '0'),
        createdAt: data['createdAt'] != null
            ? DateTime.tryParse(data['createdAt'].toString())
            : null,
        updatedAt: data['updatedAt'] != null
            ? DateTime.tryParse(data['updatedAt'].toString())
            : null,
        orderNo: data['orderNo']?.toString(),
        title: data['title']?.toString(),
        description: data['description']?.toString(),
        price: data['price'] != null
            ? double.tryParse(data['price'].toString())
            : null,
        status: data['status']?.toString(),
        type: data['type']?.toString(),
        sellerID: int.tryParse(data['sellerID']?.toString() ?? '0'),
        customerID: int.tryParse(data['customerID']?.toString() ?? '0'),
        requestID: int.tryParse(data['requestID']?.toString() ?? '0'),
        serviceID: int.tryParse(data['serviceID']?.toString() ?? '0'),
        files: data['files'] is List
            ? (data['files'] as List)
                .where((file) => file != null)
                .map((file) => OfferFile.fromJson(file as Map<String, dynamic>))
                .toList()
            : null,
        paymentStatus: data['paymentStatus']?.toString(),
        paymentMethod: data['paymentMethod']?.toString(),
        transactionID: data['transactionID']?.toString(),
      );
    } catch (error) {
      return null;
    }
  }
}
