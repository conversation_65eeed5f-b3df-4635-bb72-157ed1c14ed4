class PresenceTokenModel {
  final String token;
  final int ttl;
  final List<String> channels;

  PresenceTokenModel({
    required this.token,
    required this.ttl,
    required this.channels,
  });

  factory PresenceTokenModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? json;

    return PresenceTokenModel(
      token: data['token'] as String,
      ttl: data['ttl'] as int,
      channels: List<String>.from(data['channels'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'ttl': ttl,
      'channels': channels,
    };
  }
}
