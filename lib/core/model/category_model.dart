class CategoryModel {
  final int id;
  final String name;
  final String description;
  final String slug;
  final String type;
  final List<String> coverImage;
  final List<String> icon;
  final String path;
  final int depth;
  final int parentId;
  final String externalId;
  final dynamic products;
  final dynamic requests;
  final dynamic sellerProfiles;

  CategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.slug,
    required this.type,
    required this.coverImage,
    required this.icon,
    required this.path,
    required this.depth,
    required this.parentId,
    required this.externalId,
    this.products,
    this.requests,
    this.sellerProfiles,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      slug: json['slug'] ?? '',
      type: json['type'] ?? '',
      coverImage: json['cover_image'] != null
          ? (json['cover_image'] is List
              ? List<String>.from(json['cover_image'])
              : [json['cover_image'].toString()])
          : [],
      icon: json['icon'] != null
          ? (json['icon'] is List
              ? List<String>.from(json['icon'])
              : [json['icon'].toString()])
          : [],
      path: json['path'] ?? '',
      depth: json['depth'] ?? 0,
      parentId: json['parent_id'] ?? 0,
      externalId: json['ExternalID'] ?? '',
      products: json['products'],
      requests: json['requests'],
      sellerProfiles: json['SellerProfiles'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'slug': slug,
      'type': type,
      'cover_image': coverImage,
      'icon': icon,
      'path': path,
      'depth': depth,
      'parent_id': parentId,
      'ExternalID': externalId,
      'products': products,
      'requests': requests,
      'SellerProfiles': sellerProfiles,
    };
  }

  CategoryModel copyWith({
    int? id,
    String? name,
    String? description,
    String? slug,
    String? type,
    List<String>? coverImage,
    List<String>? icon,
    String? path,
    int? depth,
    int? parentId,
    String? externalId,
    dynamic products,
    dynamic requests,
    dynamic sellerProfiles,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      slug: slug ?? this.slug,
      type: type ?? this.type,
      coverImage: coverImage ?? this.coverImage,
      icon: icon ?? this.icon,
      path: path ?? this.path,
      depth: depth ?? this.depth,
      parentId: parentId ?? this.parentId,
      externalId: externalId ?? this.externalId,
      products: products ?? this.products,
      requests: requests ?? this.requests,
      sellerProfiles: sellerProfiles ?? this.sellerProfiles,
    );
  }

  @override
  String toString() {
    return 'Category(id: $id, name: $name, type: $type, slug: $slug, depth: $depth, parentId: $parentId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  bool get hasIcon => icon.isNotEmpty;
  bool get hasCoverImage => coverImage.isNotEmpty;
  String get firstCoverImage => coverImage.isNotEmpty ? coverImage.first : '';
  String get firstIcon => icon.isNotEmpty ? icon.first : '';
  bool get isSubcategory => type == 'subcategory';
  bool get isParentCategory => parentId == 0;
}
