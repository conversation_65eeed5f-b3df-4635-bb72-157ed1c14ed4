class ExperienceModel {
  final String id;
  final int fromYear;
  final int toYear;
  final String companyName;
  final String jobPosition;
  final String companyAddress;

  ExperienceModel({
    required this.id,
    required this.fromYear,
    required this.toYear,
    required this.companyName,
    required this.jobPosition,
    required this.companyAddress,
  });

  factory ExperienceModel.fromJson(Map<String, dynamic> json) {
    return ExperienceModel(
      id: json['id'] ?? '',
      fromYear: json['fromYear'] ?? 0,
      toYear: json['toYear'] ?? 0,
      companyName: json['company_name'] ?? '',
      jobPosition: json['job_position'] ?? '',
      companyAddress: json['company_address'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromYear': fromYear,
      'toYear': toYear,
      'company_name': companyName,
      'job_position': jobPosition,
      'company_address': companyAddress,
    };
  }
}
