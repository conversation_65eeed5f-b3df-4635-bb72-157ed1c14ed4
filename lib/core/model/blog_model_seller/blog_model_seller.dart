class BlogModelSeller {
  final int id;
  final String title;
  final String slug;
  final String content;
  final String excerpt;
  final String status;
  final int authorId;
  final String authorName;
  final String authorProfilePicture;
  final int categoryId;
  final String categoryName;
  final String categorySlug;
  final bool featured;
  final List<String> image;
  final String imageAlt;
  final dynamic tags;
  final dynamic params;

  BlogModelSeller ({
    required this.id,
    required this.title,
    required this.slug,
    required this.content,
    required this.excerpt,
    required this.status,
    required this.authorId,
    required this.authorName,
    required this.authorProfilePicture,
    required this.categoryId,
    required this.categoryName,
    required this.categorySlug,
    required this.featured,
    required this.image,
    required this.imageAlt,
    this.tags,
    this.params,
  });
  /// Parse from JSON
  factory BlogModelSeller.fromJson(Map<String, dynamic> json) {
    return BlogModelSeller(
      id: json['id']?.toInt() ?? 0,
      title: json['title']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      content: json['content']?.toString() ?? '',
      excerpt: json['excerpt']?.toString() ?? '',
      status: json['status']?.toString() ?? 'draft',
      authorId: json['author_id']?.toInt() ?? 0,
      authorName: json['author_name']?.toString() ?? '',
      authorProfilePicture: json['author_profile_picture']?.toString() ?? '',
      categoryId: json['category_id']?.toInt() ?? 0,
      categoryName: json['category_name']?.toString() ?? '',
      categorySlug: json['category_slug']?.toString() ?? '',
      featured: json['featured'] == true,
      image: List<String>.from(json['image'] ?? []),
      imageAlt: json['image_alt']?.toString() ?? '',
      tags: json['tags'],
      params: json['params'],
    );
  }


  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'slug': slug,
      'content': content,
      'excerpt': excerpt,
      'status': status,
      'author_id': authorId,
      'author_name': authorName,
      'author_profile_picture': authorProfilePicture,
      'category_id': categoryId,
      'category_name': categoryName,
      'category_slug': categorySlug,
      'featured': featured,
      'image': image,
      'image_alt': imageAlt,
      'tags': tags,
      'params': params,
    };
  }
}


class BlogModelSellerList {
  final List<BlogModelSeller> blogs;
  final int total;
  final int totalPage;
  final int currentPage;
  final int limit;

  BlogModelSellerList({
    required this.blogs,
    required this.total,
    required this.totalPage,
    required this.currentPage,
    required this.limit,
  });

  factory BlogModelSellerList.fromJson(Map<String, dynamic> json) {
    var jsonlist = json['data']['list'] as List<dynamic>;
    List<BlogModelSeller> blogs = jsonlist
        .map((item) => BlogModelSeller.fromJson(item as Map<String, dynamic>))
        .toList();

    return BlogModelSellerList(
      blogs: blogs,
      total: json['data']['total'] ?? 0,
      totalPage: json['data']['total_page'] ?? 0,
      currentPage: json['data']['current_page'] ?? 0,
      limit: json['data']['limit'] ?? 0,
    );

  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'list': blogs.map((blog) => blog.toJson()).toList(),
        'total': total,
        'total_page': totalPage,
        'current_page': currentPage,
        'limit': limit,
      },
    };
  }


}