import 'package:multime_app/core/model/category_model.dart';
import 'package:multime_app/shared/models/user/user_model.dart';

class BlogModel {
  final int id;
  final String title;
  final String content;
  final String excerpt;
  final List<String> images;
  final int categoryId;
  final CategoryModel? category;
  final int authorId;
  final UserModel? author;
  final String status;
  final bool featured;
  final List<String>? tags;
  final DateTime? createdAt;

  BlogModel({
    required this.id,
    required this.title,
    required this.content,
    required this.excerpt,
    required this.images,
    required this.categoryId,
    this.category,
    required this.authorId,
    this.author,
    required this.status,
    required this.featured,
    this.tags,
    this.createdAt,
  });

  /// Parse from JSON
  factory BlogModel.fromJson(Map<String, dynamic> json) {
    List<String>? tagsList;
    if (json['tags'] is List) {
      tagsList =
          (json['tags'] as List).map((e) => e?.toString() ?? '').toList();
    }

    return BlogModel(
      id: json['id']?.toInt() ?? 0,
      title: json['title']?.toString() ?? '',
      content: json['content']?.toString() ?? '',
      excerpt: json['excerpt']?.toString() ?? '',
      images: json['image'] != null ? List<String>.from(json['image']) : [],
      categoryId: json['category_id']?.toInt() ?? 0,
      category: json['category'] != null
          ? CategoryModel.fromJson(json['category'])
          : null,
      authorId: json['author_id']?.toInt() ?? 0,
      author:
          json['author'] != null ? UserModel.fromJson(json['author']) : null,
      status: json['status']?.toString() ?? 'draft',
      featured: json['featured'] == true,
      tags: tagsList,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'excerpt': excerpt,
      'image': images,
      'category_id': categoryId,
      'category': category?.toJson(),
      'author_id': authorId,
      'author': author?.toJson(),
      'status': status,
      'featured': featured,
      'tags': tags,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Helper getters
  String get firstImage => images.isNotEmpty ? images.first : '';
  bool get hasImages => images.isNotEmpty;
  String get authorName => author?.fullName ?? 'Unknown Author';
  String get categoryName => category?.name ?? 'Uncategorized';
  String get authorAvatar => author?.profilePicture ?? '';
  bool get hasAuthorAvatar => (author?.profilePicture ?? '').isNotEmpty;

  /// Truncate excerpt for display
  String getTruncatedExcerpt({int maxLength = 150}) {
    if (excerpt.length <= maxLength) return excerpt;
    return '${excerpt.substring(0, maxLength)}...';
  }

  @override
  String toString() =>
      'BlogModel(id: $id, title: $title, status: $status, featured: $featured)';
}
