import 'package:multime_app/core/model/category_model.dart';
import 'package:multime_app/core/model/certificates/certificates_model.dart';
import 'package:multime_app/core/model/edutcation/education_model.dart';
import 'package:multime_app/core/model/experience/experience_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/shared/models/user/user_model.dart';

class ShopModel {
  final int id;
  final int userId;
  final UserModel? user;
  final int? salesUserId;
  final UserModel? salesUser;
  final CategoryModel? category;
  final String? profession;
  final String? shopName;
  final String? bannerPicture;
  final String? about;
  final List<String>? skills;
  final int? organizationId;
  final bool? isOrgOwner;
  final List<ExperienceModel>? experiences;
  final List<dynamic>? bankAccounts;
  final List<Address>? addresses;
  final List<EducationModel>? educations;
  final List<CertificateModel>? certificates;

  ShopModel({
    required this.id,
    required this.userId,
    this.user,
    this.salesUserId,
    this.salesUser,
    this.category,
    this.profession,
    this.shopName,
    this.bannerPicture,
    this.about,
    this.skills,
    this.organizationId,
    this.isOrgOwner,
    this.experiences,
    this.bankAccounts,
    this.addresses,
    this.educations,
    this.certificates,
  });

  factory ShopModel.fromJson(Map<String, dynamic> json) {
    return ShopModel(
      id: json['id'] as int,
      userId: json['user_id'] as int,
      user: (json['user'] is Map<String, dynamic>)
          ? UserModel.fromJson(json['user'])
          : null,
      salesUserId: json['sales_user_id'] as int?,
      salesUser: (json['sales_user'] is Map<String, dynamic>)
          ? UserModel.fromJson(json['sales_user'])
          : null,
      category:
          json['category'] != null && json['category'] is Map<String, dynamic>
              ? CategoryModel.fromJson(json['category'])
              : null,
      profession: json['profession'] as String?,
      shopName: json['shop_name'] as String?,
      bannerPicture: json['banner_picture'] as String?,
      about: json['about'] as String?,
      skills: json['skills'] != null ? json['skills'] as List<String> : null,
      organizationId: json['organization_id'] as int?,
      isOrgOwner: json['is_org_owner'] as bool?,
      experiences: (json['experiences'] as List<dynamic>?)
          ?.map((e) => ExperienceModel.fromJson(e))
          .toList(),
      bankAccounts: json['bank_accounts'] as List<dynamic>?,
      addresses: (json['addresses'] as List<dynamic>?)
          ?.map((e) => Address.fromJson(e))
          .toList(),
      educations: (json['educations'] as List<dynamic>?)
          ?.map((e) => EducationModel.fromJson(e))
          .toList(),
      certificates: (json['certificates'] as List<dynamic>?)
          ?.map((e) => CertificateModel.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user': user?.toJson(),
      'sales_user_id': salesUserId,
      'sales_user': salesUser?.toJson(),
      'category': category?.toJson(),
      'profession': profession,
      'shop_name': shopName,
      'banner_picture': bannerPicture,
      'about': about,
      'skills': skills,
      'organization_id': organizationId,
      'is_org_owner': isOrgOwner,
      'experiences': experiences?.map((e) => e.toJson()).toList(),
      'bank_accounts': bankAccounts,
      'addresses': addresses?.map((e) => e.toJson()).toList(),
      'educations': educations?.map((e) => e.toJson()).toList(),
      'certificates': certificates?.map((e) => e.toJson()).toList(),
    };
  }
}
