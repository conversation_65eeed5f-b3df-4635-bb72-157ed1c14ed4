/// Model chứa tất cả thông tin cần thiết cho Payment Result Card
/// Sử dụng để truyền data qua GoRoute một cách gọn gàng
class PaymentResultData {
  // Required fields
  final bool isSuccess;
  final double amount;
  final String itemName;
  final int quantity;
  final double itemPrice;

  // Optional Item Details
  final String? itemDescription;
  final String? unit;
  final List<String>? imageUrl;
  final String? shopName;
  final String? iconName; // Lưu tên icon để serialize được

  // Optional Transaction Info
  final String? paymentMethod;
  final String? transactionId;
  final DateTime? transactionDate;

  // Optional Order Info
  final String? orderCode;
  final String? orderStatus;
  final DateTime? orderTime;

  // Address
  final String? phone;
  final String? recipientName;
  final String? street;
  final String? city;
  final String? state;

  // Optional Payment Summary
  final double? subtotal;
  final double discount;
  final double platformFee;
  final String? voucherCode;

  // Optional Action Texts
  final String? primaryActionText;
  final String? secondaryActionText;
  final int? userId; // Thêm userId để xác định người dùng

  const PaymentResultData({
    // Required
    required this.isSuccess,
    required this.amount,
    required this.itemName,
    required this.quantity,
    required this.itemPrice,

    // Optional
    this.itemDescription,
    this.unit,
    this.imageUrl,
    this.shopName,
    this.iconName,
    this.paymentMethod,
    this.transactionId,
    this.transactionDate,
    this.orderCode,
    this.orderStatus,
    this.orderTime,
    this.subtotal,
    this.discount = 0.0,
    this.platformFee = 0.0,
    this.voucherCode,
    this.primaryActionText,
    this.secondaryActionText,
    this.userId,
    this.phone,
    this.recipientName,
    this.street,
    this.city,
    this.state,
  });

  /// Factory method để tạo từ Map (cho GoRoute extra)
  factory PaymentResultData.fromMap(Map<String, dynamic> map) {
    return PaymentResultData(
      isSuccess: map['isSuccess'] ?? false,
      amount: (map['amount'] ?? 0.0).toDouble(),
      itemName: map['itemName'] ?? '',
      quantity: map['quantity'] ?? 1,
      itemPrice: (map['itemPrice'] ?? 0.0).toDouble(),
      itemDescription: map['itemDescription'],
      unit: map['unit'],
      imageUrl:
          map['imageUrl'] != null ? List<String>.from(map['imageUrl']) : null,
      shopName: map['shopName'],
      iconName: map['iconName'],
      paymentMethod: map['paymentMethod'],
      transactionId: map['transactionId'],
      transactionDate: map['transactionDate'] != null
          ? DateTime.parse(map['transactionDate'])
          : null,
      orderCode: map['orderCode'],
      orderStatus: map['orderStatus'],
      orderTime:
          map['orderTime'] != null ? DateTime.parse(map['orderTime']) : null,
      subtotal: map['subtotal']?.toDouble(),
      discount: (map['discount'] ?? 0.0).toDouble(),
      platformFee: (map['platformFee'] ?? 0.0).toDouble(),
      voucherCode: map['voucherCode'],
      primaryActionText: map['primaryActionText'],
      secondaryActionText: map['secondaryActionText'],
      userId: map['userId'],
      phone: map['phone'],
      recipientName: map['recipientName'],
      street: map['street'],
      city: map['city'],
      state: map['state'],
    );
  }

  /// Convert to Map (cho GoRoute extra)
  Map<String, dynamic> toMap() {
    return {
      'isSuccess': isSuccess,
      'amount': amount,
      'itemName': itemName,
      'quantity': quantity,
      'itemPrice': itemPrice,
      if (itemDescription != null) 'itemDescription': itemDescription,
      if (unit != null) 'unit': unit,
      if (imageUrl != null) 'imageUrl': imageUrl,
      if (shopName != null) 'shopName': shopName,
      if (iconName != null) 'iconName': iconName,
      if (paymentMethod != null) 'paymentMethod': paymentMethod,
      if (transactionId != null) 'transactionId': transactionId,
      if (transactionDate != null)
        'transactionDate': transactionDate!.toIso8601String(),
      if (orderCode != null) 'orderCode': orderCode,
      if (orderStatus != null) 'orderStatus': orderStatus,
      if (orderTime != null) 'orderTime': orderTime!.toIso8601String(),
      if (subtotal != null) 'subtotal': subtotal,
      'discount': discount,
      'platformFee': platformFee,
      if (voucherCode != null) 'voucherCode': voucherCode,
      if (primaryActionText != null) 'primaryActionText': primaryActionText,
      if (secondaryActionText != null)
        'secondaryActionText': secondaryActionText,
      if (userId != null) 'userId': userId,
      if (phone != null) 'phone': phone,
      if (recipientName != null) 'recipientName': recipientName,
      if (street != null) 'street': street,
      if (city != null) 'city': city,
      if (state != null) 'state': state,
    };
  }

  /// Copy with method để dễ dàng tạo bản sao với một số thay đổi
  PaymentResultData copyWith({
    bool? isSuccess,
    double? amount,
    String? itemName,
    int? quantity,
    double? itemPrice,
    String? itemDescription,
    String? unit,
    List<String>? imageUrl,
    String? shopName,
    String? iconName,
    String? paymentMethod,
    String? transactionId,
    DateTime? transactionDate,
    String? orderCode,
    String? orderStatus,
    DateTime? orderTime,
    double? subtotal,
    double? discount,
    double? platformFee,
    String? voucherCode,
    String? primaryActionText,
    String? secondaryActionText,
    int? userId,
    String? phone,
    String? recipientName,
    String? street,
    String? city,
    String? state,
  }) {
    return PaymentResultData(
      isSuccess: isSuccess ?? this.isSuccess,
      amount: amount ?? this.amount,
      itemName: itemName ?? this.itemName,
      quantity: quantity ?? this.quantity,
      itemPrice: itemPrice ?? this.itemPrice,
      itemDescription: itemDescription ?? this.itemDescription,
      unit: unit ?? this.unit,
      imageUrl: imageUrl ?? this.imageUrl,
      shopName: shopName ?? this.shopName,
      iconName: iconName ?? this.iconName,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionId: transactionId ?? this.transactionId,
      transactionDate: transactionDate ?? this.transactionDate,
      orderCode: orderCode ?? this.orderCode,
      orderStatus: orderStatus ?? this.orderStatus,
      orderTime: orderTime ?? this.orderTime,
      subtotal: subtotal ?? this.subtotal,
      discount: discount ?? this.discount,
      platformFee: platformFee ?? this.platformFee,
      voucherCode: voucherCode ?? this.voucherCode,
      primaryActionText: primaryActionText ?? this.primaryActionText,
      secondaryActionText: secondaryActionText ?? this.secondaryActionText,
      userId: userId ?? this.userId,
      phone: phone ?? this.phone,
      recipientName: recipientName ?? this.recipientName,
      street: street ?? this.street,
      city: city ?? this.city,
      state: state ?? this.state,
    );
  }

  /// Factory methods để tạo nhanh cho các trường hợp thường gặp

  /// Tạo cho thanh toán thành công đơn giản
  factory PaymentResultData.success(
      {required double amount,
      required String itemName,
      required int quantity,
      required double itemPrice,
      String? unit,
      List<String>? imageUrl,
      String? shopName,
      int? userId,
      String? orderStatus}) {
    return PaymentResultData(
      isSuccess: true,
      amount: amount,
      itemName: itemName,
      quantity: quantity,
      itemPrice: itemPrice,
      unit: unit,
      imageUrl: imageUrl,
      shopName: shopName,
      orderStatus: orderStatus,
      primaryActionText: 'Continue Shopping',
      secondaryActionText: 'View Order',
      userId: userId,
    );
  }

  /// Tạo cho thanh toán thất bại
  factory PaymentResultData.failed(
      {required double amount,
      required String itemName,
      required int quantity,
      required double itemPrice,
      String? unit,
      List<String>? imageUrl,
      String? shopName,
      int? userId,
      String? orderStatus}) {
    return PaymentResultData(
      isSuccess: false,
      amount: amount,
      itemName: itemName,
      quantity: quantity,
      itemPrice: itemPrice,
      unit: unit,
      imageUrl: imageUrl,
      shopName: shopName,
      orderStatus: orderStatus,
      primaryActionText: 'Change Payment',
      secondaryActionText: 'Try Again',
      userId: userId,
    );
  }
}
