class StripePaymentResponse {
  final String transactionID;
  final String clientSecret;
  final double amount;
  final String currency;

  StripePaymentResponse({
    required this.transactionID,
    required this.clientSecret,
    required this.amount,
    required this.currency,
  });

  factory StripePaymentResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;

    return StripePaymentResponse(
      transactionID: data?['transaction_id'] as String? ?? '',
      clientSecret: data?['client_secret'] as String? ?? '',
      amount: (data?['amount'] as num?)?.toDouble() ?? 0,
      currency: data?['currency'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionID,
      'client_secret': clientSecret,
      'amount': amount,
      'currency': currency,
    };
  }
}
