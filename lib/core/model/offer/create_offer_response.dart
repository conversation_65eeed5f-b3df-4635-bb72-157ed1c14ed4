class OfferFile {
  final String name;
  final String size;
  final String type;
  final String url;

  OfferFile({
    required this.name,
    required this.size,
    required this.type,
    required this.url,
  });

  factory OfferFile.fromJson(Map<String, dynamic> json) {
    return OfferFile(
      name: json['name'] as String? ?? '',
      size: json['size'] as String? ?? '',
      type: json['type'] as String? ?? '',
      url: json['url'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'size': size,
      'type': type,
      'url': url,
    };
  }
}

class CreateOfferResponse {
  final int offerID;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String orderNo;
  final String title;
  final String description;
  final double price;
  final String status;
  final String type;
  final int sellerID;
  final int? customerID;
  final int? requestID;
  final int? serviceID;
  final List<OfferFile>? files;
  final String? paymentStatus;
  final String? paymentMethod;
  final String? transactionID;

  CreateOfferResponse({
    required this.offerID,
    required this.createdAt,
    required this.updatedAt,
    required this.orderNo,
    required this.title,
    required this.description,
    required this.price,
    required this.status,
    required this.type,
    required this.sellerID,
    this.customerID,
    this.requestID,
    this.serviceID,
    this.files,
    this.paymentStatus,
    this.paymentMethod,
    this.transactionID,
  });

  factory CreateOfferResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;

    return CreateOfferResponse(
      offerID: data?['id'] is int
          ? data!['id'] as int
          : int.tryParse(data?['id']?.toString() ?? '0') ?? 0,
      createdAt: data != null
          ? DateTime.parse(data['created_at'] as String)
          : DateTime.now(),
      updatedAt: data != null
          ? DateTime.parse(data['updated_at'] as String)
          : DateTime.now(),
      orderNo: data?['order_no'] as String? ?? '',
      title: data?['title'] as String? ?? '',
      description: data?['description'] as String? ?? '',
      price: data?['price'] is num
          ? (data!['price'] as num).toDouble()
          : double.tryParse(data?['price']?.toString() ?? '0') ?? 0.0,
      status: data?['status'] as String? ?? '',
      type: data?['type'] as String? ?? '',
      sellerID: data?['seller_id'] is int
          ? data!['seller_id'] as int
          : int.tryParse(data?['seller_id']?.toString() ?? '0') ?? 0,
      customerID: data?['customer_id'] is int
          ? data!['customer_id'] as int
          : int.tryParse(data?['customer_id']?.toString() ?? '0') ?? 0,
      requestID: data?['request_id'] is int
          ? data!['request_id'] as int
          : int.tryParse(data?['request_id']?.toString() ?? '0') ?? 0,
      serviceID: data?['service_id'] is int
          ? data!['service_id'] as int
          : int.tryParse(data?['service_id']?.toString() ?? '0') ?? 0,
      files: (data?['files'] as List<dynamic>?)
              ?.map((file) => OfferFile.fromJson(file as Map<String, dynamic>))
              .toList() ??
          [],
      paymentStatus: data?['payment_status'] as String? ?? '',
      paymentMethod: data?['payment_method'] as String? ?? '',
      transactionID: data?['transaction_id'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offerID': offerID,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'orderNo': orderNo,
      'title': title,
      'description': description,
      'price': price,
      'status': status,
      'type': type,
      'sellerID': sellerID,
      'customerID': customerID,
      'requestID': requestID,
      'serviceID': serviceID,
      'files': files?.map((file) => file.toJson()).toList(),
      'paymentStatus': paymentStatus,
      'paymentMethod': paymentMethod,
      'transactionID': transactionID,
    };
  }

  factory CreateOfferResponse.empty() {
    final now = DateTime.now();
    return CreateOfferResponse(
      offerID: 0,
      createdAt: now,
      updatedAt: now,
      orderNo: '',
      title: '',
      description: '',
      price: 0.0,
      status: '',
      type: '',
      sellerID: 0,
      customerID: 0,
      requestID: 0,
      serviceID: 0,
      files: [],
      paymentStatus: '',
      paymentMethod: '',
      transactionID: '',
    );
  }

  List<String> get fileURL {
    return files?.map((file) => file.url).toList() ?? [];
  }
}
