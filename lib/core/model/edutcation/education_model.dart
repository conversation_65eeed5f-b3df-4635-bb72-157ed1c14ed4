class EducationModel {
  final String id;
  final String major;
  final int fromYear;
  final int toYear;
  final String schoolName;
  final String address;

  EducationModel({
    required this.id,
    required this.major,
    required this.fromYear,
    required this.toYear,
    required this.schoolName,
    required this.address,
  });

  factory EducationModel.fromJson(Map<String, dynamic> json) {
    return EducationModel(
      id: json['id'] ?? '',
      major: json['major'] ?? '',
      fromYear: json['fromYear'] ?? 0,
      toYear: json['toYear'] ?? 0,
      schoolName: json['school_name'] ?? '',
      address: json['address'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'major': major,
      'fromYear': fromYear,
      'toYear': toYear,
      'school_name': schoolName,
      'address': address,
    };
  }
}
