class CreateOrderProductModel {
  final int id;
  final String orderNo;
  final double total;
  final String serviceFee;
  final String commissionFee;
  final String orderStatus;
  final String payStatus;
  final String type;
  final String method;
  final int productId;
  final int addressId;
  final String unit;
  final int quantity;
  final int customerId;
  final int shopId;
  final ProductSnapshot productSnapshot;

  CreateOrderProductModel({
    required this.id,
    required this.orderNo,
    required this.total,
    required this.serviceFee,
    required this.commissionFee,
    required this.orderStatus,
    required this.payStatus,
    required this.type,
    required this.method,
    required this.productId,
    required this.addressId,
    required this.unit,
    required this.quantity,
    required this.customerId,
    required this.shopId,
    required this.productSnapshot,
  });


  factory CreateOrderProductModel.fromJson(Map<String, dynamic> json) {
    return CreateOrderProductModel(
      id: json['id'] as int,
      orderNo: json['order_no'] as String,
      total: (json['total'] as num).toDouble(),
      serviceFee: json['service_fee'] as String,
      commissionFee: json['commission_fee'] as String,
      orderStatus: json['order_status'] as String,
      payStatus: json['pay_status'] as String,
      type: json['type'] as String,
      method: json['method'] as String,
      productId: json['product_id'] as int,
      addressId: json['address_id'] as int,
      unit: json['unit'] as String,
      quantity: json['quantity'] as int,
      customerId: json['customer_id'] as int,
      shopId: json['shop_id'] as int,
      productSnapshot: ProductSnapshot.fromJson(json['product_snapshot'] as Map<String, dynamic>),
    );
  }


  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_no': orderNo,
      'total': total,
      'service_fee': serviceFee,
      'commission_fee': commissionFee,
      'order_status': orderStatus,
      'pay_status': payStatus,
      'type': type,
      'method': method,
      'product_id': productId,
      'address_id': addressId,
      'unit': unit,
      'quantity': quantity,
      'customer_id': customerId,
      'shop_id': shopId,
      'product_snapshot': productSnapshot.toJson(),
    };
  }

}



class ProductSnapshot {
  final String snapshotName;
  final String snapshotDescription;
  final String snapshotSku;
  final String snapshotBrandText;
  final int snapshotCategoryId;
  final String snapshotCategoryName;
  final int snapshotPrice;
  final String snapshotUnit;
  final List<String> snapshotImages;
  final String snapshotCountryOfOrigin;
  final String snapshotLicenseNo;
  final String snapshotLicenseFile;


  ProductSnapshot({
    required this.snapshotName,
    required this.snapshotDescription,
    required this.snapshotSku,
    required this.snapshotBrandText,
    required this.snapshotCategoryId,
    required this.snapshotCategoryName,
    required this.snapshotPrice,
    required this.snapshotUnit,
    required this.snapshotImages,
    required this.snapshotCountryOfOrigin,
    required this.snapshotLicenseNo,
    required this.snapshotLicenseFile,
  });

  factory ProductSnapshot.fromJson(Map<String, dynamic> json) {
    return ProductSnapshot(
      snapshotName: json['snapshotName'] as String,
      snapshotDescription: json['snapshotDescription'] as String,
      snapshotSku: json['snapshotSku'] as String,
      snapshotBrandText: json['snapshotBrandText'] as String,
      snapshotCategoryId: json['snapshotCategoryId'] as int,
      snapshotCategoryName: json['snapshotCategoryName'] as String,
      snapshotPrice: json['snapshotPrice'] as int,
      snapshotUnit: json['snapshotUnit'] as String,
      snapshotImages: List<String>.from(json['snapshotImages']),
      snapshotCountryOfOrigin: json['snapshotCountryOfOrigin'] as String,
      snapshotLicenseNo: json['snapshotLicenseNo'] as String,
      snapshotLicenseFile: json['snapshotLicenseFile'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'snapshotName': snapshotName,
      'snapshotDescription': snapshotDescription,
      'snapshotSku': snapshotSku,
      'snapshotBrandText': snapshotBrandText,
      'snapshotCategoryId': snapshotCategoryId,
      'snapshotCategoryName': snapshotCategoryName,
      'snapshotPrice': snapshotPrice,
      'snapshotUnit': snapshotUnit,
      'snapshotImages': snapshotImages,
      'snapshotCountryOfOrigin': snapshotCountryOfOrigin,
      'snapshotLicenseNo': snapshotLicenseNo,
      'snapshotLicenseFile': snapshotLicenseFile,
    };
  }
}