class OrderServiceModel {
  final int offerId;
  final String orderNo;
  final String status;
  final String paymentStatus;
  final String clientSecret;
  final String createdAt;

  OrderServiceModel({
    required this.offerId,
    required this.orderNo,
    required this.status,
    required this.paymentStatus,
    required this.clientSecret,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'offer_id': offerId,
      'order_no': orderNo,
      'status': status,
      'payment_status': paymentStatus,
      'client_secret': clientSecret,
      'created_at': createdAt,
    };
  }

  factory OrderServiceModel.fromJson(Map<String, dynamic> json) {
    return OrderServiceModel(
      offerId: json['offer_id'] ?? '',
      orderNo: json['order_no'] ?? '',
      status: json['status'] ?? '',
      paymentStatus: json['payment_status'] ?? '',
      clientSecret: json['client_secret'] ?? '',
      createdAt: json['created_at'] ?? '',
    );
  }
}
