class CreateOrderServiceModel {
  final String serviceId;
  final String sellerId;
  final String customerId;
  final String title;
  final String description;
  final double price;
  final List<String> files;
  final String notes;

  CreateOrderServiceModel({
    required this.serviceId,
    required this.sellerId,
    required this.customerId,
    required this.title,
    required this.description,
    required this.price,
    required this.files,
    required this.notes,
  });

  factory CreateOrderServiceModel.fromJson(Map<String, dynamic> json) {
    return CreateOrderServiceModel(
      serviceId: json['service_id'],
      sellerId: json['seller_id'],
      customerId: json['customer_id'],
      title: json['title'],
      description: json['description'],
      price: json['price'],
      files: List<String>.from(json['files']),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'service_id': serviceId,
      'seller_id': sellerId,
      'customer_id': customerId,
      'title': title,
      'description': description,
      'price': price,
      'files': files,
      'notes': notes,
    };
  }
}
