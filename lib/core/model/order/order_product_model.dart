class OrderProductModel {
  final int order_id;
  final String order_no;
  final double total;
  final int discount;
  final double final_total;
  final String pay_status;
  final String order_status;
  final String client_secret;
  final String created_at;

  OrderProductModel({
    required this.order_id,
    required this.order_no,
    required this.total,
    required this.discount,
    required this.final_total,
    required this.pay_status,
    required this.order_status,
    required this.client_secret,
    required this.created_at,
  });

  factory OrderProductModel.fromJson(Map<String, dynamic> json) {
    return OrderProductModel(
      order_id: json['order_id'] as int,
      order_no: json['order_no'] as String,
      total: (json['total'] as num).toDouble(),
      discount: json['discount'] as int,
      final_total: (json['final_total'] as num).toDouble(),
      pay_status: json['pay_status'] as String,
      order_status: json['order_status'] as String,
      client_secret: json['client_secret'] as String,
      created_at: json['created_at'] as String,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'order_id': order_id,
      'order_no': order_no,
      'total': total,
      'discount': discount,
      'final_total': final_total,
      'pay_status': pay_status,
      'order_status': order_status,
      'client_secret': client_secret,
      'created_at': created_at,
    };
  }

  
}
