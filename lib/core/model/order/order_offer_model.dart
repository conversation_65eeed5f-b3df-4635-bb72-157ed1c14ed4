class OrderOfferModel {
  final String transactionId;
  final String stripeCustomerId;
  final String clientSecret;
  final double amount;
  final String currency;

  OrderOfferModel({
    required this.transactionId,
    required this.stripeCustomerId,
    required this.clientSecret,
    required this.amount,
    required this.currency,
  });

  // Factory constructor từ JSON
  factory OrderOfferModel.fromJson(Map<String, dynamic> json) {
    return OrderOfferModel(
      transactionId: json['transaction_id']?.toString() ?? '',
      stripeCustomerId: json['stripe_customer_id']?.toString() ?? '',
      clientSecret: json['client_secret']?.toString() ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency']?.toString() ?? 'USD',
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionId,
      'stripe_customer_id': stripeCustomerId,
      'client_secret': clientSecret,
      'amount': amount,
      'currency': currency,
    };
  }

  // Copy with method cho immutable updates
  OrderOfferModel copyWith({
    String? transactionId,
    String? stripeCustomerId,
    String? clientSecret,
    double? amount,
    String? currency,
  }) {
    return OrderOfferModel(
      transactionId: transactionId ?? this.transactionId,
      stripeCustomerId: stripeCustomerId ?? this.stripeCustomerId,
      clientSecret: clientSecret ?? this.clientSecret,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
    );
  }

  // toString method for debugging
  @override
  String toString() {
    return 'OrderOfferModel(transactionId: $transactionId, stripeCustomerId: $stripeCustomerId, clientSecret: $clientSecret, amount: $amount, currency: $currency)';
  }
}
