class CertificateModel {
  final String? image;
  final String location;
  final String certificateName;
  final String linkCertificate;
  final String yearOfCertification;

  CertificateModel({
    this.image,
    required this.location,
    required this.certificateName,
    required this.linkCertificate,
    required this.yearOfCertification,
  });

  factory CertificateModel.fromJson(Map<String, dynamic> json) {
    return CertificateModel(
      image: json['image'],
      location: json['location'] ?? '',
      certificateName: json['certificate_name'] ?? '',
      linkCertificate: json['link_certificate'] ?? '',
      yearOfCertification: json['year_of_certification'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'image': image,
      'location': location,
      'certificate_name': certificateName,
      'link_certificate': linkCertificate,
      'year_of_certification': yearOfCertification,
    };
  }
}