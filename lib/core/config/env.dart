import 'package:multime_app/core/config/environment.dart';

/// Helper class để truy cập environment variables một cách thuận tiện
class Env {
  // App Info
  static String get appName => AppConfig.appName;
  static String get packageName => AppConfig.packageName;
  static bool get isDebug => AppConfig.debugMode;
  static bool get isProd => AppConfig.isProd;
  static bool get isDev => AppConfig.isDev;

  // API Configuration
  static String get apiBaseUrl => AppConfig.baseUrl;

  // Supabase
  static String get supabaseUrl => AppConfig.supabaseUrl;
  static String get supabaseKey => AppConfig.supabaseAnonKey;

  // Stripe
  static String get stripeKey => AppConfig.stripePublishableKey;

  // Pusher Configuration
  static String get pusherAppId => AppConfig.pusherAppId;
  static String get pusherKey => AppConfig.pusherKey;
  static String get pusherSecret => AppConfig.pusherSecret;
  static String get pusherCluster => AppConfig.pusherCluster;

  // External APIs
  static String get googleMapsKey => AppConfig.googleMapsApiKey;
  static String get firebaseProjectId => AppConfig.firebaseProjectId;

  // Logging
  static bool get enableLogging => AppConfig.enableLogging;

  // Environment name for display
  static String get environmentName => isProd ? 'Production' : 'Development';

  // Environment suffix for titles/names
  static String get envSuffix => isProd ? '' : ' (Dev)';

  // Helper methods
  static void logEnvironmentInfo() {
    if (enableLogging) {
      print('📱 Running in $environmentName environment');
      print('🔧 Package: $packageName');
      print('🌐 API: $apiBaseUrl');
    }
  }
}
