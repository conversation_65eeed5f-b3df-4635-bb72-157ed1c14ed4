import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:multime_app/core/config/env_validator.dart';

enum Environment { dev, prod }

class AppConfig {
  static Environment _environment = Environment.dev;

  static void setEnvironment(Environment env) {
    _environment = env;
  }

  static Environment get environment => _environment;

  static bool get isDev => _environment == Environment.dev;
  static bool get isProd => _environment == Environment.prod;

  // Load environment file based on current environment
  static Future<void> loadEnv() async {
    String envFile = isDev ? '.env.dev' : '.env.prod';
    try {
      await dotenv.load(fileName: envFile);

      // Validate required environment variables
      EnvValidator.validateEnvironment();

      if (debugMode) {
        EnvValidator.printEnvironmentStatus();
      }
    } catch (e) {
      throw Exception('Failed to load environment file ($envFile): $e');
    }
  }

  // Helper method to get environment variable with fallback
  static String _getEnvVar(String key, {String fallback = ''}) {
    return dotenv.env[key] ?? fallback;
  }

  // App Configuration
  static String get appName => _getEnvVar('APP_NAME');

  // API Configuration
  static String get baseUrl => _getEnvVar('API_BASE_URL');

  // Supabase Configuration
  static String get supabaseUrl => _getEnvVar('SUPABASE_URL');
  static String get supabaseAnonKey => _getEnvVar('SUPABASE_ANON_KEY');

  // Stripe Configuration
  static String get stripePublishableKey =>
      _getEnvVar('STRIPE_PUBLISHABLE_KEY');
  static String get stripeSecretKey => _getEnvVar('STRIPE_SECRET_KEY');

  // Other API Keys
  static String get googleMapsApiKey => _getEnvVar('GOOGLE_MAPS_API_KEY');
  static String get firebaseProjectId => _getEnvVar('FIREBASE_PROJECT_ID');
  static String get onesignalAppId => _getEnvVar('ONESIGNAL_APP_ID');

  // Debug Configuration
  static bool get debugMode => _getEnvVar('DEBUG_MODE') == 'true';
  static bool get enableLogging => _getEnvVar('ENABLE_LOGGING') == 'true';

  // Pusher Configuration
  static String get pusherAppId => _getEnvVar('PUSHER_APP_ID');
  static String get pusherKey => _getEnvVar('PUSHER_KEY');
  static String get pusherSecret => _getEnvVar('PUSHER_SECRET');
  static String get pusherCluster => _getEnvVar('PUSHER_CLUSTER');

  // Package Names for different environments
  static String get packageName {
    switch (_environment) {
      case Environment.dev:
        return 'com.multime.app.dev';
      case Environment.prod:
        return 'com.multime.app';
    }
  }

  // Display current environment info (for debugging)
  static void printEnvironmentInfo() {
    if (debugMode) {
      print('=== Environment Configuration ===');
      print('Environment: ${_environment.name}');
      print('App Name: $appName');
      print('Base URL: $baseUrl');
      print('Package Name: $packageName');
      print('Debug Mode: $debugMode');
      print('==================================');
    }
  }
}
