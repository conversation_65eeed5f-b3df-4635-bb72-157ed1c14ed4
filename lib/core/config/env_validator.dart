import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvValidator {
  static const List<String> _requiredKeys = [
    'APP_NAME',
    'API_BASE_URL',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'STRIPE_PUBLISHABLE_KEY',
  ];

  static bool validateEnvironment() {
    final missingKeys = <String>[];

    for (final key in _requiredKeys) {
      if (!dotenv.env.containsKey(key) || dotenv.env[key]?.isEmpty == true) {
        missingKeys.add(key);
      }
    }

    if (missingKeys.isNotEmpty) {
      throw Exception(
          'Missing required environment variables: ${missingKeys.join(', ')}\n'
          'Please check your .env file configuration.');
    }

    return true;
  }

  static void printEnvironmentStatus() {
    print('\n=== Environment Validation ===');
    for (final key in _requiredKeys) {
      final value = dotenv.env[key];
      final status = value?.isNotEmpty == true ? '✅' : '❌';
      final maskedValue = _maskSensitiveValue(key, value);
      print('$status $key: $maskedValue');
    }
    print('==============================\n');
  }

  static String _maskSensitiveValue(String key, String? value) {
    if (value == null || value.isEmpty) return 'NOT SET';

    // Mask sensitive keys
    if (key.contains('KEY') ||
        key.contains('SECRET') ||
        key.contains('TOKEN')) {
      if (value.length <= 10) return '*' * value.length;
      return '${value.substring(0, 6)}${'*' * (value.length - 10)}${value.substring(value.length - 4)}';
    }

    return value;
  }
}
