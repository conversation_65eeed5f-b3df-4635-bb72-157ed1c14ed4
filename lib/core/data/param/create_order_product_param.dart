class CreateOrderProductParam {
  final int productId;
  final int customerId;
  final int shopId;
  final String unit;
  final int quantity;
  final double? total;
  final String? serviceFee;
  final String? commissionFee;
  final String? type;
  final String method;
  final int? addressId;
  final String? orderStatus;
  final String? payStatus;

  CreateOrderProductParam({
    required this.productId,
    required this.customerId,
    required this.shopId,
    required this.unit,
    required this.quantity,
     this.total,
    this.serviceFee,
    this.commissionFee,
    this.type,
    required this.method,
    this.addressId,
    this.orderStatus,
    this.payStatus,
  });

  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'customer_id': customerId,
      'shop_id': shopId,
      'unit': unit,
      'quantity': quantity,
      'total': total,
      'service_fee': serviceFee,
      'commission_fee': commissionFee,
      'type': type,
      'method': method,
      'address_id': addressId,
      'order_status': orderStatus,
      'pay_status': payStatus,
    };
  }
}
