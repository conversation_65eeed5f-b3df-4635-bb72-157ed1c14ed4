class CreateOrderServiceParam {
  final int offerId;
  final String orderNo;
  final String status;
  final String paymentStatus;
  final String clientSecret;
  final String createdAt;
  CreateOrderServiceParam({
    required this.offerId,
    required this.orderNo,
    required this.status,
    required this.paymentStatus,
    required this.clientSecret,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'offer_id': offerId,
      'order_no': orderNo,
      'status': status,
      'payment_status': paymentStatus,
      'client_secret': clientSecret,
      'created_at': createdAt,
    };
  }

  factory CreateOrderServiceParam.fromJson(Map<String, dynamic> json) {
    return CreateOrderServiceParam(
      offerId: json['offer_id'],
      orderNo: json['order_no'],
      status: json['status'],
      paymentStatus: json['payment_status'],
      clientSecret: json['client_secret'],
      createdAt: json['created_at'],
    );
  }
}

class OrderServiceParam {
  final String orderId;

  OrderServiceParam({required this.orderId});

  Map<String, dynamic> toJson() {
    return {
      'offer_id': orderId,
    };
  }
}
