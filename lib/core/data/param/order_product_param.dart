class OrderProductParam {
  final int? customer_id;
  final int address_id;
  final int? shop_id;
  final List<OrderProduct> products;
  final String? method;
  final String? message;
  final String? voucher_code;
  final int? order_id;


  OrderProductParam({
    this.customer_id,
    required this.address_id,
    this.shop_id,
    required this.products,
    this.method,
    this.message,
    this.voucher_code,
    this.order_id,
  });
  Map<String, dynamic> toJson() {
    return {
      'customer_id': customer_id,
      'address_id': address_id,
      'shop_id': shop_id,
      'products': products.map((product) => product.toJson()).toList(),
      'method': method,
      'message': message,
      'voucher_code': voucher_code,
      'order_id': order_id,
    };
  }

}

class OrderProduct {
  final int productId;
  final String unit;
  final int quantity;

  OrderProduct({
    required this.productId,
    required this.unit,
    required this.quantity,
  });

  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'unit': unit,
      'quantity': quantity,
    };
  }
}
