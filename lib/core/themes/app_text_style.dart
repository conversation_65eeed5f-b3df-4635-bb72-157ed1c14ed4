import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_font_family.dart';

class AppTextStyle {
  // Heading
  static TextStyle lightHeadingXLarge = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 48.sp,
      fontFamily: AppFontFamily.bold);

  static TextStyle lightHeadingLarge = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 40.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightHeadingMediumLarge = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 32.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightHeadingMedium = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 24.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightHeadingSmall = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 20.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightHeadingXSmall = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.bold);

  // Body

  // X Large
  static TextStyle lightBodyXLargeBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.bold);

  static TextStyle lightBodyXLargeSemiBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.semiBold);

  static TextStyle lightBodyXLargeMedium = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle lightBodyXLargeRegular = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.regular);

  // Large
  static TextStyle lightBodyLargeBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightBodyLargeSemiBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle lightBodyLargeMedium = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle lightBodyLargeRegular = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.regular);

  //Medium

  static TextStyle lightBodyMediumBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightBodyMediumSemiBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle lightBodyMediumMedium = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle lightBodyMediumRegular = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.regular);

  //Small

  static TextStyle lightBodySmallBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightBodySmallSemiBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle lightBodySmallMedium = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle lightBodySmallRegular = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.regular);

  //X Small

  static TextStyle lightBodyXSmallBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle lightBodyXSmallSemiBold = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle lightBodyXSmallMedium = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle lightBodyXSmallRegular = TextStyle(
      color: AppColors.textPrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.regular);

  // Heading
  static TextStyle darkHeadingXLarge = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 48.sp,
      fontFamily: AppFontFamily.bold);

  static TextStyle darkHeadingLarge = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 40.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkHeadingMediumLarge = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 32.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkHeadingMedium = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 24.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkHeadingSmall = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 20.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkHeadingXSmall = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.bold);

  // Body

  // X Large
  static TextStyle darkBodyXLargeBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.bold);

  static TextStyle darkBodyXLargeSemiBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.semiBold);

  static TextStyle darkBodyXLargeMedium = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle darkBodyXLargeRegular = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 18.sp,
      fontFamily: AppFontFamily.regular);

  // Large
  static TextStyle darkBodyLargeBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkBodyLargeSemiBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle darkBodyLargeMedium = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle darkBodyLargeRegular = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 16.sp,
      fontFamily: AppFontFamily.regular);

  //Medium

  static TextStyle darkBodyMediumBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkBodyMediumSemiBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle darkBodyMediumMedium = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle darkBodyMediumRegular = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 14.sp,
      fontFamily: AppFontFamily.regular);

  //Small

  static TextStyle darkBodySmallBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkBodySmallSemiBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle darkBodySmallMedium = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle darkBodySmallRegular = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 12.sp,
      fontFamily: AppFontFamily.regular);

  //X Small

  static TextStyle darkBodyXSmallBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.bold);
  static TextStyle darkBodyXSmallSemiBold = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.semiBold);
  static TextStyle darkBodyXSmallMedium = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.medium);
  static TextStyle darkBodyXSmallRegular = TextStyle(
      color: AppColors.whitePrimary,
      fontSize: 10.sp,
      fontFamily: AppFontFamily.regular);
}

extension CustomTextStyles on TextTheme {
  TextStyle get lightHeadingXLarge => AppTextStyle.lightHeadingXLarge;
  TextStyle get lightHeadingLarge => AppTextStyle.lightHeadingLarge;
  TextStyle get lightHeadingMediumLarge => AppTextStyle.lightHeadingMediumLarge;
  TextStyle get lightHeadingMedium => AppTextStyle.lightHeadingMedium;
  TextStyle get lightHeadingSmall => AppTextStyle.lightHeadingSmall;
  TextStyle get lightHeadingXSmall => AppTextStyle.lightHeadingXSmall;

  TextStyle get lightBodyXLargeBold => AppTextStyle.lightBodyXLargeBold;
  TextStyle get lightBodyXLargeSemiBold => AppTextStyle.lightBodyXLargeSemiBold;
  TextStyle get lightBodyXLargeMedium => AppTextStyle.lightBodyXLargeMedium;
  TextStyle get lightBodyXLargeRegular => AppTextStyle.lightBodyXLargeRegular;

  TextStyle get lightBodyLargeBold => AppTextStyle.lightBodyLargeBold;
  TextStyle get lightBodyLargeSemiBold => AppTextStyle.lightBodyLargeSemiBold;
  TextStyle get lightBodyLargeMedium => AppTextStyle.lightBodyLargeMedium;
  TextStyle get lightBodyLargeRegular => AppTextStyle.lightBodyLargeRegular;

  TextStyle get lightBodyMediumBold => AppTextStyle.lightBodyMediumBold;
  TextStyle get lightBodyMediumSemiBold => AppTextStyle.lightBodyMediumSemiBold;
  TextStyle get lightBodyMediumMedium => AppTextStyle.lightBodyMediumMedium;
  TextStyle get lightBodyMediumRegular => AppTextStyle.lightBodyMediumRegular;

  TextStyle get lightBodySmallBold => AppTextStyle.lightBodySmallBold;
  TextStyle get lightBodySmallSemiBold => AppTextStyle.lightBodySmallSemiBold;
  TextStyle get lightBodySmallMedium => AppTextStyle.lightBodySmallMedium;
  TextStyle get lightBodySmallRegular => AppTextStyle.lightBodySmallRegular;

  TextStyle get lightBodyXSmallBold => AppTextStyle.lightBodyXSmallBold;
  TextStyle get lightBodyXSmallSemiBold => AppTextStyle.lightBodyXSmallSemiBold;
  TextStyle get lightBodyXSmallMedium => AppTextStyle.lightBodyXSmallMedium;
  TextStyle get lightBodyXSmallRegular => AppTextStyle.lightBodyXSmallRegular;

  TextStyle get darkHeadingXLarge => AppTextStyle.darkHeadingXLarge;
  TextStyle get darkHeadingLarge => AppTextStyle.darkHeadingLarge;
  TextStyle get darkHeadingMediumLarge => AppTextStyle.darkHeadingMediumLarge;
  TextStyle get darkHeadingMedium => AppTextStyle.darkHeadingMedium;
  TextStyle get darkHeadingSmall => AppTextStyle.darkHeadingSmall;
  TextStyle get darkHeadingXSmall => AppTextStyle.darkHeadingXSmall;

  TextStyle get darkBodyXLargeBold => AppTextStyle.darkBodyXLargeBold;
  TextStyle get darkBodyXLargeSemiBold => AppTextStyle.darkBodyXLargeSemiBold;
  TextStyle get darkBodyXLargeMedium => AppTextStyle.darkBodyXLargeMedium;
  TextStyle get darkBodyXLargeRegular => AppTextStyle.darkBodyXLargeRegular;

  TextStyle get darkBodyLargeBold => AppTextStyle.darkBodyLargeBold;
  TextStyle get darkBodyLargeSemiBold => AppTextStyle.darkBodyLargeSemiBold;
  TextStyle get darkBodyLargeMedium => AppTextStyle.darkBodyLargeMedium;
  TextStyle get darkBodyLargeRegular => AppTextStyle.darkBodyLargeRegular;

  TextStyle get darkBodyMediumBold => AppTextStyle.darkBodyMediumBold;
  TextStyle get darkBodyMediumSemiBold => AppTextStyle.darkBodyMediumSemiBold;
  TextStyle get darkBodyMediumMedium => AppTextStyle.darkBodyMediumMedium;
  TextStyle get darkBodyMediumRegular => AppTextStyle.darkBodyMediumRegular;

  TextStyle get darkBodySmallBold => AppTextStyle.darkBodySmallBold;
  TextStyle get darkBodySmallSemiBold => AppTextStyle.darkBodySmallSemiBold;
  TextStyle get darkBodySmallMedium => AppTextStyle.darkBodySmallMedium;
  TextStyle get darkBodySmallRegular => AppTextStyle.darkBodySmallRegular;

  TextStyle get darkBodyXSmallBold => AppTextStyle.darkBodyXSmallBold;
  TextStyle get darkBodyXSmallSemiBold => AppTextStyle.darkBodyXSmallSemiBold;
  TextStyle get darkBodyXSmallMedium => AppTextStyle.darkBodyXSmallMedium;
  TextStyle get darkBodyXSmallRegular => AppTextStyle.lightBodyXSmallRegular;
}
