import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../modules/theme/bloc/theme_bloc.dart';

class AppThemeClass {
  static ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    dividerColor: Colors.transparent,
    primaryColor: lightColorThemeData.primary,
    primaryColorDark: lightColorThemeData.primary,
    highlightColor: Colors.transparent,
    colorScheme: ColorScheme(
      primary: lightColorThemeData.primary,
      secondary: lightColorThemeData.secondary,
      surface: lightColorThemeData.textSecondary100,
      error: lightColorThemeData.errorBase,
      onPrimary: lightColorThemeData.primary,
      onSecondary: lightColorThemeData.secondary,
      onSurface: lightColorThemeData.textSecondary100,
      onError: lightColorThemeData.errorBase,
      brightness: Brightness.light,
    ),
    scaffoldBackgroundColor: lightColorThemeData.whitePrimary,
    hintColor: lightColorThemeData.textSecondary100,
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: lightColorThemeData.primary,
      selectionColor: lightColorThemeData.primary,
      selectionHandleColor: lightColorThemeData.primary,
    ),
    splashColor: Colors.transparent,
    inputDecorationTheme: InputDecorationTheme(
      errorMaxLines: 2,
      floatingLabelStyle:
          WidgetStateTextStyle.resolveWith((Set<WidgetState> states) {
        if (states.contains(WidgetState.error)) {
          return AppTextStyle.lightBodySmallMedium.copyWith(
              color: lightColorThemeData.errorBase,
              fontWeight: FontWeight.w400);
        }
        return AppTextStyle.lightBodySmallMedium.copyWith(
            color: lightColorThemeData.greyScale500,
            fontWeight: FontWeight.w400);
      }),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Radius 8px
        borderSide: BorderSide(
          color: lightColorThemeData.greyScale300,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        borderSide: BorderSide(color: lightColorThemeData.greyScale900),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Radius 8px
        borderSide: BorderSide(
          color: lightColorThemeData.greyScale300,
          width: 1,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Radius 8px
        borderSide: BorderSide(
          color: lightColorThemeData.errorBase,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Radius 8px
        borderSide: BorderSide(
          color: lightColorThemeData.errorBase,
          width: 1,
        ),
      ),
      hintStyle: AppTextStyle.lightBodySmallMedium.copyWith(
          color: lightColorThemeData.greyScale500, fontWeight: FontWeight.w400),
      contentPadding: const EdgeInsets.all(10),
    ),
    appBarTheme: AppBarTheme(
      surfaceTintColor: Colors.transparent,
      systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light),
      elevation: 0.2,
      // color: lightColorThemeData.whitePrimary,
      iconTheme: IconThemeData(
        color: lightColorThemeData.greyScale900,
      ),
      actionsIconTheme: IconThemeData(
        color: lightColorThemeData.greyScale900,
      ),
      backgroundColor: lightColorThemeData.whitePrimary,
      foregroundColor: lightColorThemeData.whitePrimary,
      toolbarHeight: 56,
      titleTextStyle: AppTextStyle.lightBodyLargeBold,
      centerTitle: true,
    ),
    textTheme: TextTheme(
      headlineLarge: AppTextStyle.lightHeadingLarge,
      headlineMedium: AppTextStyle.lightHeadingMedium,
      headlineSmall: AppTextStyle.lightHeadingSmall,
      titleLarge: AppTextStyle.lightBodyLargeBold,
      titleMedium: AppTextStyle.lightBodyMediumBold,
      titleSmall: AppTextStyle.lightBodySmallBold,
      bodyLarge: AppTextStyle.lightBodyLargeMedium,
      bodyMedium: AppTextStyle.lightBodyMediumMedium,
      bodySmall: AppTextStyle.lightBodySmallMedium,
      displayLarge: AppTextStyle.lightBodyXLargeMedium,
      displayMedium: AppTextStyle.lightBodyLargeMedium,
      displaySmall: AppTextStyle.lightBodyMediumMedium,
      labelLarge: AppTextStyle.lightBodyLargeMedium,
      labelMedium: AppTextStyle.lightBodyMediumMedium,
      labelSmall: AppTextStyle.lightBodySmallMedium,
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: lightColorThemeData.primary,
      foregroundColor: lightColorThemeData.whitePrimary,
    ),
    cupertinoOverrideTheme: CupertinoThemeData(
      primaryColor: lightColorThemeData.primary,
    ),
    bottomAppBarTheme:
        BottomAppBarTheme(color: lightColorThemeData.whitePrimary),
    snackBarTheme: const SnackBarThemeData().copyWith(
      // backgroundColor: lightColorThemeData.greyScale700,
      contentTextStyle: AppTextStyle.lightBodyMediumRegular,
    ),
  );
  static ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: darkColorThemeData.primary,
    primaryColorLight: darkColorThemeData.primary,
    scaffoldBackgroundColor: darkColorThemeData.blackPrimary,
    hintColor: darkColorThemeData.greyScale50,
    dividerColor: darkColorThemeData.greyScale700,
    colorScheme: ColorScheme(
      primary: lightColorThemeData.primary,
      secondary: lightColorThemeData.secondary,
      surface: lightColorThemeData.textSecondary100,
      error: lightColorThemeData.errorBase,
      onPrimary: lightColorThemeData.primary,
      onSecondary: lightColorThemeData.secondary,
      onSurface: lightColorThemeData.textSecondary100,
      onError: lightColorThemeData.errorBase,
      brightness: Brightness.dark,
    ),
    textTheme: TextTheme(
      headlineLarge: AppTextStyle.lightHeadingLarge,
      headlineMedium: AppTextStyle.lightHeadingMedium,
      headlineSmall: AppTextStyle.lightHeadingSmall,
      titleLarge: AppTextStyle.lightBodyLargeBold,
      titleMedium: AppTextStyle.lightBodyMediumBold,
      titleSmall: AppTextStyle.lightBodySmallBold,
      bodyLarge: AppTextStyle.lightBodyLargeMedium,
      bodyMedium: AppTextStyle.lightBodyMediumMedium,
      bodySmall: AppTextStyle.lightBodySmallMedium,
      displayLarge: AppTextStyle.lightBodyXLargeMedium,
      displayMedium: AppTextStyle.lightBodyLargeMedium,
      displaySmall: AppTextStyle.lightBodyMediumMedium,
      labelLarge: AppTextStyle.lightBodyLargeMedium,
      labelMedium: AppTextStyle.lightBodyMediumMedium,
      labelSmall: AppTextStyle.lightBodySmallMedium,
    ),
    splashColor: Colors.transparent,
    inputDecorationTheme: InputDecorationTheme(
      errorMaxLines: 2,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Radius 8px
        borderSide: BorderSide(
          color: darkColorThemeData.greyScale300,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        borderSide: BorderSide(color: darkColorThemeData.whitePrimary),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Radius 8px
        borderSide: BorderSide(
          color: darkColorThemeData.greyScale300,
          width: 1,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8), // Radius 8px
        borderSide: BorderSide(
          color: darkColorThemeData.errorBase,
          width: 1,
        ),
      ),
      hintStyle: AppTextStyle.lightBodySmallMedium.copyWith(
          color: darkColorThemeData.greyScale50, fontWeight: FontWeight.w400),
      contentPadding: const EdgeInsets.all(10),
    ),
    appBarTheme: AppBarTheme(
      surfaceTintColor: Colors.transparent,
      systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light),
      elevation: 0.2,
      // color: darkColorThemeData.blackPrimary,
      iconTheme: IconThemeData(
        color: darkColorThemeData.whitePrimary,
      ),
      actionsIconTheme: IconThemeData(
        color: darkColorThemeData.whitePrimary,
      ),
      backgroundColor: darkColorThemeData.textPrimary,
      foregroundColor: darkColorThemeData.textPrimary,
      toolbarHeight: 56,
      titleTextStyle: AppTextStyle.lightBodyLargeBold,
      centerTitle: true,
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: darkColorThemeData.primary,
      foregroundColor: darkColorThemeData.greyScale900,
    ),
    cupertinoOverrideTheme: CupertinoThemeData(
      primaryColor: darkColorThemeData.primary,
    ),
    bottomAppBarTheme:
        BottomAppBarTheme(color: darkColorThemeData.greyScale900),
    snackBarTheme: const SnackBarThemeData().copyWith(
      backgroundColor: lightColorThemeData.greyScale700,
      contentTextStyle: AppTextStyle.darkBodyMediumRegular,
    ),
  );
}

extension LightTheme on ThemeData {
  AppColorThemeData appThemeData(BuildContext context) {
    final isDark = this.isDark(context);
    return isDark ? darkColorThemeData : lightColorThemeData;
  }

  bool isDark(BuildContext context) {
    final themeState =
        context.read<ThemeBloc>().state.appTheme == AppThemeClass.darkTheme;
    return themeState;
  }

  Color alertWhite(BuildContext context) => appThemeData(context).alertWhite;
  Color hyperlink(BuildContext context) => appThemeData(context).hyperLink;

  Color whitePrimary(BuildContext context) =>
      appThemeData(context).whitePrimary;

  Color blackPrimary(BuildContext context) =>
      appThemeData(context).blackPrimary;

  Color transparent(BuildContext context) => Colors.transparent;

  Color primary(BuildContext context) => appThemeData(context).primary;

  Color secondary(BuildContext context) => appThemeData(context).secondary;

  Color secondaryDark(BuildContext context) =>
      appThemeData(context).secondaryDark;

  Color secondaryBase(BuildContext context) =>
      appThemeData(context).secondaryBase;

  Color successDark(BuildContext context) => appThemeData(context).successDark;

  Color successBase(BuildContext context) => appThemeData(context).successBase;

  Color successLight(BuildContext context) =>
      appThemeData(context).successLight;

  Color warningDark(BuildContext context) => appThemeData(context).warningDark;

  Color warningBase(BuildContext context) => appThemeData(context).warningBase;

  Color warningLight(BuildContext context) =>
      appThemeData(context).warningLight;

  Color errorDark(BuildContext context) => appThemeData(context).errorDark;

  Color errorBase(BuildContext context) => appThemeData(context).errorBase;

  Color errorLight(BuildContext context) => appThemeData(context).errorLight;

  Color attentionDark(BuildContext context) =>
      appThemeData(context).attentionDark;

  Color attentionBase(BuildContext context) =>
      appThemeData(context).attentionBase;
  Color alertAttentionBase(BuildContext context) =>
      appThemeData(context).alertAttentionBase;

  Color attentionLight(BuildContext context) =>
      appThemeData(context).attentionLight;

  Color informationDark(BuildContext context) =>
      appThemeData(context).informationDark;

  Color informationBase(BuildContext context) =>
      appThemeData(context).informationBase;

  Color informationLight(BuildContext context) =>
      appThemeData(context).informationLight;

  Color focusHighlightBase(BuildContext context) =>
      appThemeData(context).focusHighlightBase;

  Color focusHighlightDark(BuildContext context) =>
      appThemeData(context).focusHighlightDark;

  Color focusHighlightLight(BuildContext context) =>
      appThemeData(context).focusHighlightLight;

  Color disabledDark(BuildContext context) =>
      appThemeData(context).disabledDark;

  Color disabledBase(BuildContext context) =>
      appThemeData(context).disabledBase;

  Color disabledLight(BuildContext context) =>
      appThemeData(context).disabledLight;

  Color greyScale50(BuildContext context) => appThemeData(context).greyScale50;

  Color greyScale100(BuildContext context) =>
      appThemeData(context).greyScale100;

  Color greyScale200(BuildContext context) =>
      appThemeData(context).greyScale200;

  Color greyScale300(BuildContext context) =>
      appThemeData(context).greyScale300;

  Color greyScale400(BuildContext context) =>
      appThemeData(context).greyScale400;

  Color greyScale500(BuildContext context) =>
      appThemeData(context).greyScale500;

  Color greyScale600(BuildContext context) =>
      appThemeData(context).greyScale600;

  Color greyScale700(BuildContext context) =>
      appThemeData(context).greyScale700;

  Color greyScale800(BuildContext context) =>
      appThemeData(context).greyScale800;

  Color greyScale900(BuildContext context) =>
      appThemeData(context).greyScale900;
  Color pinkScale50(BuildContext context) => appThemeData(context).pinkScale50;

  Color textPrimary(BuildContext context) => appThemeData(context).textPrimary;

  Color textSecondary(BuildContext context) =>
      appThemeData(context).textSecondary;

  Color textSecondary100(BuildContext context) =>
      appThemeData(context).textSecondary100;

  Color textBlack(BuildContext context) => appThemeData(context).textBlack;

  Color backgroundRed(BuildContext context) =>
      appThemeData(context).backgroundRed;
  Color lightGrey(BuildContext context) => appThemeData(context).lightGrey;
  Color errorText(BuildContext context) => appThemeData(context).errorText;
  Color textSecondary200(BuildContext context) =>
      appThemeData(context).textSecondary200;
  Color alertInformationBase(BuildContext context) =>
      appThemeData(context).alertInformationBase;
  Color modeLightText(BuildContext context) =>
      appThemeData(context).modeLightText;
}
