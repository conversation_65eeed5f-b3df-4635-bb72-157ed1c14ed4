import 'package:flutter/material.dart';

class AppColors {
  static const Color white = Color(0xFFFFFFFF);
  static const Color backgroundColor = Color(0xFFF5F5F5);

  static const Color hyperLink = Color(0xff007AFF);
  static const Color forgotPass = Color.fromRGBO(0, 122, 255, 1);

  static const Color btnDisable = Color(0xffee5757);
  static const Color btnSubmit = Color(0xffe52020);

  static const Color purple = Color(0xFF542ACF);

  // Main Colors
  static const Color primary = Color(0xFFDA1F27);
  static const Color secondary = Color(0xFF1F2D44);
  static const Color secondaryDark = Color(0xFF454156);
  static const Color secondaryBase = Color(0xFF354156);

  // Alert Colors
  static const Color successBase = Color(0xFF0CAF60);
  static const Color successDark = Color(0xFF0BA259);
  static const Color successLight = Color(0xFF73CDA2);

  static const Color warningBase = Color(0xFFFFCD16);
  static const Color warningDark = Color(0xFFD8AF1C);
  static const Color warningLight = Color(0xFFFFF6D6);

  static const Color errorBase = Color(0xFFF75355);
  static const Color errorDark = Color(0xFFB9383D);
  static const Color errorLight = Color(0xFFFFEDEC);
  static const Color errorText = Color(0xFFEF3E3E);

  static const Color attentionBase = Color(0xFFFA9114);
  static const Color attentionDark = Color(0xFFCD6E29);
  static const Color attentionLight = Color(0xFFFFEDD3);

  static const Color informationBase = Color(0xFF2F8CF9);
  static const Color informationDark = Color(0xFF2083C6);
  static const Color informationLight = Color(0xFFE4F4FF);

  static const Color focusHighlightBase = Color(0xFF8C2FFF);
  static const Color focusHighlightDark = Color(0xFF5F32D8);
  static const Color focusHighlightLight = Color(0xFFE5DBFF);

  static const Color disabledBase = Color(0xFF979797);
  static const Color disabledDark = Color(0xFF707378);
  static const Color disabledLight = Color(0xFFE8E8E8);

  // Greyscale
  static const Color greyScale50 = Color(0xFFF9FAFA);
  static const Color greyScale100 = Color(0xFFF1F3F8);

  static const Color greyScale200 = Color(0xFFE7EDF4);

  static const Color greyScale300 = Color(0xFFE5EAEC);
  static const Color greyScale400 = Color(0xFFCBD5DC);
  static const Color greyScale500 = Color(0xFFA0ACC0);
  static const Color greyScale600 = Color(0xFF68738B);
  static const Color greyScale700 = Color(0xFF323849);
  static const Color greyScale800 = Color(0xFF1F2327);
  static const Color greyScale900 = Color(0xFF0F171A);
  static const Color pinkScale50 = Color(0xFFFAFAFA);
  static const Color lightGrey = Color(0xFFE9EAEC);

  // Text Colors
  static const Color textPrimary = Color(0xFF1F2D44);
  static const Color textSecondary = Color(0xFF738093);
  static const Color textSecondary100 = Color(0xFFC6C8CC);
  static const Color textSecondary200 = Color(0xFFEBF1FA);

  // Additional Colors
  static const Color whitePrimary = Color(0xFFFFFFFF);
  static const Color blackPrimary = Color(0xFF000000);
  static const textBlack = Color(0xFF111827);

  static const Color modeLightText = Color(0xFFC6C6C6);

  // Alert Colors
  static const Color alertWhite = Color(0xFFE8E8E8);
  static const Color alertInformationBase = Color(0xFF2F8CF9);
  static const Color alertAttentionBase = Color(0xFFFF9141);

  //background
  static const Color backgroundRed = Color(0xFFDA2126);
}

class AppColorThemeData {
  final Color primary;
  final Color secondary;
  final Color secondaryDark;
  final Color secondaryBase;
  final Color successBase;
  final Color successDark;
  final Color successLight;
  final Color modeLightText;

  final Color warningBase;
  final Color warningDark;
  final Color warningLight;

  final Color errorBase;
  final Color errorDark;
  final Color errorLight;
  final Color errorText;

  final Color attentionBase;
  final Color attentionDark;
  final Color attentionLight;

  final Color informationBase;
  final Color informationDark;
  final Color informationLight;

  final Color focusHighlightBase;
  final Color focusHighlightDark;
  final Color focusHighlightLight;

  final Color disabledBase;
  final Color disabledDark;
  final Color disabledLight;

  // Greyscale
  final Color greyScale50;
  final Color greyScale100;
  final Color greyScale200;
  final Color greyScale300;
  final Color greyScale400;
  final Color greyScale500;
  final Color greyScale600;
  final Color greyScale700;
  final Color greyScale800;
  final Color greyScale900;
  final Color lightGrey;

  // pinkScale

  final Color pinkScale50;

  // Text Colors
  final Color textPrimary;
  final Color textSecondary;
  final Color textSecondary100;
  final Color textSecondary200;

  // Alert Colors
  final Color alertWhite;
  final Color alertInformationBase;
  final Color alertAttentionBase;

  // Additional Colors
  final Color whitePrimary;
  final Color blackPrimary;
  final Color textBlack;

  final Color backgroundRed;
  final Color hyperLink;

  const AppColorThemeData({
    required this.primary,
    required this.secondary,
    required this.secondaryDark,
    required this.secondaryBase,
    required this.successBase,
    required this.successDark,
    required this.successLight,
    required this.warningBase,
    required this.warningDark,
    required this.warningLight,
    required this.errorBase,
    required this.errorDark,
    required this.errorLight,
    required this.attentionBase,
    required this.attentionDark,
    required this.attentionLight,
    required this.informationBase,
    required this.informationDark,
    required this.informationLight,
    required this.focusHighlightBase,
    required this.focusHighlightDark,
    required this.focusHighlightLight,
    required this.disabledBase,
    required this.disabledDark,
    required this.disabledLight,
    required this.greyScale50,
    required this.greyScale100,
    required this.greyScale200,
    required this.greyScale300,
    required this.greyScale400,
    required this.greyScale500,
    required this.greyScale600,
    required this.greyScale700,
    required this.greyScale800,
    required this.greyScale900,
    required this.textPrimary,
    required this.textSecondary,
    required this.textSecondary100,
    required this.whitePrimary,
    required this.textBlack,
    required this.blackPrimary,
    required this.backgroundRed,
    required this.lightGrey,
    required this.errorText,
    required this.textSecondary200,
    required this.alertWhite,
    required this.alertInformationBase,
    required this.modeLightText,
    required this.hyperLink,
    required this.pinkScale50,
    required this.alertAttentionBase,
  });
}

const lightColorThemeData = AppColorThemeData(
  primary: AppColors.primary,
  secondary: AppColors.secondary,
  secondaryDark: AppColors.secondaryDark,
  secondaryBase: AppColors.secondaryBase,
  attentionBase: AppColors.attentionBase,
  attentionDark: AppColors.attentionDark,
  attentionLight: AppColors.attentionLight,
  disabledBase: AppColors.disabledBase,
  disabledDark: AppColors.disabledDark,
  disabledLight: AppColors.disabledLight,
  errorBase: AppColors.errorBase,
  errorDark: AppColors.errorDark,
  errorLight: AppColors.errorLight,
  focusHighlightBase: AppColors.focusHighlightBase,
  focusHighlightDark: AppColors.focusHighlightBase,
  focusHighlightLight: AppColors.focusHighlightLight,
  greyScale100: AppColors.greyScale100,
  greyScale200: AppColors.greyScale200,
  greyScale300: AppColors.greyScale300,
  greyScale400: AppColors.greyScale400,
  greyScale500: AppColors.greyScale500,
  greyScale600: AppColors.greyScale600,
  greyScale50: AppColors.greyScale50,
  greyScale700: AppColors.greyScale700,
  greyScale800: AppColors.greyScale800,
  greyScale900: AppColors.greyScale900,
  informationBase: AppColors.informationBase,
  informationDark: AppColors.informationDark,
  informationLight: AppColors.informationLight,
  successBase: AppColors.successBase,
  successDark: AppColors.successDark,
  successLight: AppColors.successLight,
  textSecondary100: AppColors.textSecondary100,
  textPrimary: AppColors.textPrimary,
  textSecondary: AppColors.textSecondary,
  warningBase: AppColors.warningBase,
  warningDark: AppColors.warningDark,
  warningLight: AppColors.warningLight,
  whitePrimary: AppColors.whitePrimary,
  textBlack: AppColors.textBlack,
  blackPrimary: AppColors.blackPrimary,
  backgroundRed: AppColors.backgroundRed,
  lightGrey: AppColors.lightGrey,
  errorText: AppColors.errorText,
  textSecondary200: AppColors.textSecondary200,
  alertWhite: AppColors.alertWhite,
  alertInformationBase: AppColors.alertInformationBase,
  modeLightText: AppColors.modeLightText,
  hyperLink: AppColors.hyperLink,
  pinkScale50: AppColors.pinkScale50,
  alertAttentionBase: AppColors.alertAttentionBase,
);
const darkColorThemeData = AppColorThemeData(
  primary: AppColors.secondary,
  secondary: AppColors.primary,
  secondaryDark: AppColors.secondaryDark,
  secondaryBase: AppColors.secondaryBase,
  attentionBase: AppColors.attentionBase,
  attentionDark: AppColors.attentionDark,
  attentionLight: AppColors.attentionLight,
  disabledBase: AppColors.disabledBase,
  disabledDark: AppColors.disabledDark,
  disabledLight: AppColors.disabledLight,
  errorBase: AppColors.errorBase,
  errorDark: AppColors.errorDark,
  errorLight: AppColors.errorLight,
  focusHighlightBase: AppColors.focusHighlightBase,
  focusHighlightDark: AppColors.focusHighlightBase,
  focusHighlightLight: AppColors.focusHighlightLight,
  greyScale100: AppColors.greyScale900,
  greyScale200: AppColors.greyScale800,
  greyScale300: AppColors.greyScale700,
  greyScale400: AppColors.greyScale600,
  greyScale500: AppColors.greyScale500,
  greyScale600: AppColors.greyScale400,
  greyScale50: AppColors.greyScale50,
  greyScale700: AppColors.greyScale300,
  greyScale800: AppColors.greyScale200,
  greyScale900: AppColors.greyScale100,
  informationBase: AppColors.informationBase,
  informationDark: AppColors.informationDark,
  informationLight: AppColors.informationLight,
  successBase: AppColors.successBase,
  successDark: AppColors.successDark,
  successLight: AppColors.successLight,
  textSecondary100: AppColors.textPrimary,
  textPrimary: AppColors.whitePrimary,
  textSecondary: AppColors.textSecondary100,
  warningBase: AppColors.warningBase,
  warningDark: AppColors.warningDark,
  warningLight: AppColors.warningLight,
  whitePrimary: AppColors.whitePrimary,
  blackPrimary: AppColors.blackPrimary,
  textBlack: AppColors.textBlack,
  backgroundRed: AppColors.backgroundRed,
  lightGrey: AppColors.lightGrey,
  errorText: AppColors.errorText,
  textSecondary200: AppColors.textSecondary200,
  alertWhite: AppColors.alertWhite,
  alertInformationBase: AppColors.alertInformationBase,
  modeLightText: AppColors.modeLightText,
  hyperLink: AppColors.hyperLink,
  pinkScale50: AppColors.pinkScale50,
  alertAttentionBase: AppColors.alertAttentionBase,
);
