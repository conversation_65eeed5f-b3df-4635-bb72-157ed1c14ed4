import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class NetworkService extends ChangeNotifier {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  bool _isConnected = true;
  bool _isManuallyDisabled = false;
  bool _networkWarningDisabled = false;
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  final Connectivity _connectivity = Connectivity();

  bool get isConnected =>
      (_isConnected && !_isManuallyDisabled) || _networkWarningDisabled;
  void disableNetworkWarning() {
    _networkWarningDisabled = true;
    notifyListeners();
  }

  void enableNetworkWarning() {
    _networkWarningDisabled = false;
    notifyListeners();
  }

  Future<void> initialize() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on PlatformException catch (e) {
      debugPrint('Couldn\'t check connectivity status: $e');
    }

    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
    );
  }

  void _updateConnectionStatus(List<ConnectivityResult> result) {
    final wasConnected = _isConnected;
    _isConnected = result.isNotEmpty &&
        !result.every((element) => element == ConnectivityResult.none);

    if (!_isConnected && wasConnected) {
      notifyListeners();
      debugPrint('Network status changed: Disconnected');
    } else if (_isConnected && !wasConnected) {
      enableNetworkWarning();
      notifyListeners();
      debugPrint('Network status changed: Connected');
    }
  }

  Future<bool> checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      return result.isNotEmpty &&
          !result.every((element) => element == ConnectivityResult.none);
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    super.dispose();
  }
}
