import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

class StripePaymentService {
  static bool _isProcessing = false;

  /// Gọi PaymentSheet với hỗ trợ:
  /// - Retry: luôn re-init trư<PERSON><PERSON> mỗi lần present (nếu client secret đổi sẽ tự áp dụng)
  /// - Customer mode: truyền `customerId` và `customerEphemeralKeySecret` để hiển thị PM đã lưu
  /// - Hỗ trợ cả PaymentIntent và SetupIntent (truyền 1 trong 2 secret)
  static Future<Map<String, dynamic>> processPayment({
    String? paymentIntentClientSecret,
    String? setupIntentClientSecret,
    String? customerId,
    String? customerEphemeralKeySecret,
    required double amount,
    String currency = 'USD',
    bool allowsDelayedPaymentMethods = false,
  }) async {
    if (_isProcessing) {
      return {
        'success': false,
        'error': 'Payment is already in progress',
        'errorCode': 'already_processing',
      };
    }
    _isProcessing = true;

    try {
      // <PERSON><PERSON><PERSON> bảo chỉ truyền 1 loại secret
      if ((paymentIntentClientSecret == null &&
              setupIntentClientSecret == null) ||
          (paymentIntentClientSecret != null &&
              setupIntentClientSecret != null)) {
        return {
          'success': false,
          'error':
              'Provide exactly one of paymentIntentClientSecret or setupIntentClientSecret',
          'errorCode': 'invalid_parameters',
        };
      }

      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentIntentClientSecret,
          setupIntentClientSecret: setupIntentClientSecret,
          merchantDisplayName: 'Multime App',
          style: ThemeMode.system,
          allowsDelayedPaymentMethods: allowsDelayedPaymentMethods,
          customerId: customerId,
          customerEphemeralKeySecret: customerEphemeralKeySecret,
          billingDetailsCollectionConfiguration:
              const BillingDetailsCollectionConfiguration(
            name: CollectionMode.never,
            email: CollectionMode.never,
            phone: CollectionMode.never,
            address: AddressCollectionMode.never,
          ),
        ),
      );

      await Stripe.instance.presentPaymentSheet();

      return {
        'success': true,
        'status': 'succeeded',
        'amount': amount,
        'currency': currency,
      };
    } on StripeException catch (e) {
      print('❌ StripeException caught: ${e.error.code}');
      if (e.error.code == FailureCode.Canceled) {
        print('👤 User cancelled payment');
        return {
          'success': false,
          'error': 'Payment was cancelled',
          'errorCode': 'user_cancelled',
        };
      }
      print('💳 Stripe error: ${e.error.localizedMessage}');
      return {
        'success': false,
        'error': e.error.localizedMessage ?? 'Payment failed',
        'errorCode': e.error.code,
      };
    } catch (e) {
      print('💥 Unexpected error: $e');
      return {
        'success': false,
        'error': e.toString(),
        'errorCode': 'unknown_error',
      };
    } finally {
      _isProcessing = false;
    }
  }
}
