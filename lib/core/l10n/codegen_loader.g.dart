// // DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// // ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters

// import 'dart:ui';

// import 'package:easy_localization/easy_localization.dart' show AssetLoader;

// class CodegenLoader extends AssetLoader{
//   const CodegenLoader();

//   @override
//   Future<Map<String, dynamic>?> load(String path, Locale locale) {
//     return Future.value(mapLocales[locale.toString()]);
//   }

//   static const Map<String,dynamic> zh = {
//   "hello": "你好",
//   "language": "语言",
//   "home": "首页",
//   "setting": "设置",
//   "note": "笔记",
//   "featured": "特色",
//   "seeMore": "查看更多",
//   "order": "订单",
//   "message": "消息",
//   "cancel": "取消",
//   "notInterested": "不感兴趣？",
//   "swipeLeftNoInterest": "向左滑动表示你不感兴趣。",
//   "askMore": "询问更多",
//   "swipeRightNoInterest": "向右滑动表示你想与他们聊天。",
//   "business": "商务",
//   "request": "请求",
//   "readMore": "阅读更多",
//   "readLess": "收起",
//   "whatDoYouWantToDo": "你想做什么？",
//   "postBusinessOpportunity": "发布商业机会",
//   "offer": "提供",
//   "thisIsAn": "这是一个",
//   "title": "标题",
//   "graphicDesigner": "平面设计师，2D 动画，....",
//   "businessNeeds": "业务需求",
//   "description": "描述",
//   "shortDescription": "例如：简短描述，主要任务，我的技能和经验，工作环境等。",
//   "stateYourNeeds": "例如：陈述您的需求或提供的服务，行业/部门，所需技能/经验，描述您在潜在候选人或合作伙伴身上寻找的东西",
//   "budgetInvestmentUSD": "预算/投资（美元）",
//   "uploadAvatar": "上传头像",
//   "optional": "(可选)",
//   "uploadAPhoto": "上传一张工作照片。此照片将显示在列表中，供其他人查看。",
//   "addMore": "添加更多",
//   "postDisplayDuration": "发布展示时长",
//   "dayAgo": "天前",
//   "age": "年龄",
//   "distance": "距离",
//   "skip": "跳过",
//   "post": "发布",
//   "maleText": "男",
//   "preferredGender": "偏好性别",
//   "femaleText": "女",
//   "otherText": "其他",
//   "save": "保存",
//   "dating": "约会",
//   "likeYou": "喜欢你",
//   "matches": "匹配",
//   "editBio": "编辑个人简介",
//   "editSeeking": "编辑寻找",
//   "camera": "相机",
//   "gallery": "图库",
//   "aboutYou": "关于你",
//   "displayName": "显示名称",
//   "name": "姓名",
//   "yearOfBirth": "出生年份",
//   "youYearOfBirth": "你的出生年份",
//   "yourOccupation": "你的职业",
//   "shortBio": "简短简介",
//   "yourBio": "你的个人简介",
//   "addYourPhotos": "添加你的照片",
//   "addAtLeast2": "至少添加2张照片，你可以稍后更改",
//   "continueButton": "继续",
//   "likedYou": "喜欢你",
//   "youLikedThem": "你喜欢了他们",
//   "theyLikedYou": "他们喜欢了你",
//   "reviewThePeople": "查看你喜欢的人",
//   "today": "今天",
//   "reviewThePeople2": "查看喜欢你的人",
//   "reviewThePeopleYouHaveSwipedLeftOn": "查看你左滑的人。",
//   "yesterday": "昨天",
//   "itsAMatch": "配对成功！",
//   "startAConversationNow": "现在开始对话。",
//   "startChatting": "开始聊天",
//   "keepSwiping": "继续滑动",
//   "recent": "最近",
//   "whoAreYouSeeking": "你在寻找谁？",
//   "recentlyActive": "最近活跃",
//   "welcomeDatingMode": "欢迎来到约会模式",
//   "ruleBeYourselfTitle": "做自己。",
//   "ruleBeYourselfDescription": "确保你的照片、年龄和简介反映你真实的自己",
//   "ruleStaySafeTitle": "保持安全。",
//   "ruleStaySafeDescription": "不要过早地透露个人信息。",
//   "ruleStaySafeLinkText": "安全约会",
//   "rulePlayItCoolTitle": "保持冷静",
//   "rulePlayItCoolDescription": "尊重他人，并像你希望他人对待你一样对待他人。",
//   "ruleBeProactiveTitle": "积极主动。",
//   "ruleBeProactiveDescription": "始终报告不良行为。",
//   "iAgreeButtonText": "我同意",
//   "followHouseRulesText": "请遵守这些房屋规则",
//   "addNewCardTitle": "添加新卡片",
//   "cardDetailsTitle": "卡片详情",
//   "cardNumberLabel": "卡号",
//   "cardNumberHint": "0000-0000-0000-0000",
//   "expiryDateLabel": "过期日期",
//   "expiryDateHint": "MM/YY",
//   "cvvLabel": "CVV",
//   "cvvHint": "CVV",
//   "cardholderNameLabel": "卡持有人姓名",
//   "cardholderNameHint": "卡上的姓名",
//   "verificationTransaction": "验证交易",
//   "verificationTransactionText": "（验证后款项将立即返还至您的卡）",
//   "confirmButtonText": "确认",
//   "checkoutTitle": "结账",
//   "warningMessage": "下单前，请确保地址正确并与您当前地址匹配。",
//   "orderDetailsTitle": "订单详情",
//   "orderDetailsHint": "您的订单",
//   "paymentOptionsTitle": "支付选项",
//   "paymentDetailsTitle": "支付详情",
//   "totalCost": "总成本",
//   "paypal": "Paypal",
//   "visaCard": "Visa 卡",
//   "paymentFailed": "支付失败",
//   "paymentFailedMessage": "处理支付时出错，请稍后再试。",
//   "locationLabel": "位置",
//   "addNewCard": "添加新卡",
//   "orderID": "订单ID",
//   "orderDate": "订单日期",
//   "productPrice": "产品价格",
//   "voucher": "优惠券",
//   "serviceFee": "服务费",
//   "termsAndConditions": "按下支付按钮，即表示您同意",
//   "termsAndConditionsLink": "Multi.Me 的条款和条件。",
//   "confirmOrder": "确认订单",
//   "dateText": "dd/MM/yyyy",
//   "personalInformation": "个人信息",
//   "fullName": "全名",
//   "phone": "电话",
//   "addressLabel": "地址",
//   "addressHint": "您的地址",
//   "specificAddressLabel": "具体地址",
//   "specificAddressHint": "具体地址",
//   "mapPlaceholder": "地图占位符",
//   "setDefaultCheckboxLabel": "设为默认地址",
//   "saveChangeButton": "保存更改",
//   "liked": "喜欢",
//   "marketplace": "市场",
//   "newText": "新",
//   "mostView": "最受欢迎",
//   "mostViewed": "最多观看",
//   "likeNew": "像新的一样",
//   "adsPlaceholder": "广告占位符",
//   "productName": "产品名称",
//   "shopAddress": "商店地址",
//   "postDate": "发布日期",
//   "price": "价格",
//   "orderPage": "订单页面",
//   "messagePage": "消息页面",
//   "cancelOrder": "取消订单",
//   "payment": "支付",
//   "totalPayment": "总支付",
//   "subtotal": "小计",
//   "orderShipped": "您的订单已开始发货",
//   "orderPlaced": "您已下单",
//   "deliveredTo": "送达",
//   "sentFrom": "发货自",
//   "awaitingApproval": "等待批准",
//   "productDetails": "产品详情",
//   "quantity": "数量",
//   "sellerInfo": "卖家信息",
//   "contactSeller": "联系卖家",
//   "shippingInfo": "运输信息",
//   "orderFailed": "订单失败",
//   "orderFailureMessage": "您的订单出现问题。",
//   "orderCheckMessage": "请返回并仔细检查您的订单。",
//   "returnOrder": "退货",
//   "orderConfirmed": "订单确认",
//   "thankYouMessage": "感谢您的订单。您将很快收到电子邮件确认。",
//   "checkOrderStatus": "在您的购物车中查看订单状态。",
//   "continueShopping": "继续购物",
//   "addNewAddress": "添加新地址",
//   "postProduct": "发布产品",
//   "productHintText": "产品名称（最少50字，最多150字）",
//   "statusText": "状态",
//   "productFeatures": "产品的突出特点，包含的内容，使用多久了…",
//   "statusProduct": "选择产品的状态",
//   "backText": "返回",
//   "buyNow": "立即购买",
//   "reportSeller": "举报卖家",
//   "noRecommendSeller": "不推荐此卖家",
//   "noMoreProfile": "没有更多资料！",
//   "selectPostsFrom": "选择帖子来源",
//   "offersFrom": "来自 Multi.Me 的报价",
//   "payFee": "支付费用",
//   "apply": "申请",
//   "freePosting": "免费发布",
//   "adType": "类型：常规广告\n适用于每月首次个人广告",
//   "upgradeToPro": "升级为专业版",
//   "postFreeAds": "发布免费广告",
//   "adTypeAds": "类型：常规 - 永久广告",
//   "appliesToAllPosts": "适用于所有帖子",
//   "multiMe": "Multi.Me",
//   "doesNot": "不允许",
//   "allowDepositPrivate": "允许：存款/私人转账/平台外交易/招聘合作者/赠送免费礼物/提供联系信息或按卖家的要求取消订单。",
//   "pleaseOnlyBuySell": "请仅在 Multi.Me 上购买/出售，以避免被骗。Multi.Me 将根据 Multi.Me 的隐私政策收集和使用聊天记录。",
//   "gotUnusedItem": "有闲置物品吗？",
//   "turnThemIntoCash": "把它们变成现金！",
//   "postForSale": "发布待售",
//   "itsTotally": "它完全是",
//   "safe": "安全的",
//   "toPurchaseAtMultiMe": "在 Multi.Me 上购买是安全的。您的钱将只有在确认收到产品后才会转给卖家，否则会退还给您。",
//   "aboutThisItem": "关于此物品",
//   "condition": "状况",
//   "type": "类型",
//   "brand": "品牌",
//   "show": "显示",
//   "hide": "隐藏",
//   "estDelivery": "预计交付：",
//   "safePurchase": "在 Multi.Me 上购买是完全安全的。只有在确认后，您的钱才会转给卖家。",
//   "chargeRateInfo": "购买金额 > $1000 将收取 3% 的手续费\n购买金额 < $999 将收取 5% 的手续费",
//   "thereWill": "将会有",
//   "chargeRate": "手续费",
//   "defaultText": "默认",
//   "createDate": "创建日期",
//   "shippingPrice": "包括运费",
//   "contactText": "如有任何问题，想讨论价格或希望获得更优惠的报价，请联系我。",
//   "contactMe": "联系我",
//   "moreLike": "更多类似的",
//   "viewAll": "查看全部",
//   "postMe": "发布我",
//   "postNews": "发布新闻",
//   "newsTitle": "新闻标题",
//   "category": "类别",
//   "country": "国家",
//   "articleSource": "文章来源",
//   "content": "内容",
//   "copyContent": "从文章复制内容到这里",
//   "addCoverPhoto": "+\n添加封面照片",
//   "selectCategory": "选择类别",
//   "reviewPost": "审核您的帖子",
//   "tabAll": "全部",
//   "tabSports": "体育",
//   "tabPolitics": "政治",
//   "tabBusiness": "商业",
//   "tabHealth": "健康",
//   "tabTravel": "旅游",
//   "tabScience": "科学",
//   "readAndShare": "阅读并分享全球新闻",
//   "createCommunity": "🙌 我们不是创造新闻，我们创建一个社区，让人们分享和讨论新闻，帮助彼此发展批判性思维和多方面理解",
//   "hotNew": "热门新闻",
//   "history": "历史",
//   "createPost": "创建帖子",
//   "latest": "最新",
//   "about": "关于",
//   "skill": "技能",
//   "education": "教育",
//   "experience": "经验",
//   "notPost": "还没有发布",
//   "subNewPost": "尝试订阅您新的帖子...",
//   "notReview": "尚未评价",
//   "thanksStrongBody": "您的订单对 StrongBody 社区意义重大。现在下单来写下您的第一篇评论。",
//   "productAndService": "探索产品和服务",
//   "newService": "新服务",
//   "notService": "还没有服务",
//   "sellerCreateService": "卖家尚未创建任何服务。如果她卖了什么东西，它会出现在这里。",
//   "noProduct": "还没有产品",
//   "noProductDescription": "卖家尚未销售任何产品。如果她将来销售，产品会出现在这里。",
//   "activeShop": "活跃的 StrongBody 店铺在这里 →",
//   "add": "添加",
//   "fullTime": "全职",
//   "toYear": "到年份",
//   "companyBusiness": "公司/商号名称",
//   "fromYear": "起始年份",
//   "positionCompany": "公司职位",
//   "addressCountry": "地址/国家",
//   "addSkill": "添加技能（例如：声音演员）",
//   "year": "年份",
//   "uploadImage": "上传 430x190 px 的图片以获得更多的展示和销售机会。",
//   "displayNameLabel": "显示名称",
//   "displayNameHint": "请为您的账户命名",
//   "yourProfession": "您的职业",
//   "yourProfessionHint": "示例 1：经过认证的皮肤科医生，专注于美容手术和皮肤癌治疗。示例 2：认证个人教练，专注于各个健身水平的减肥和力量训练。",
//   "professionalLabel": "您的专业头衔",
//   "professionalHint": "例如：私人教练 / 治疗师 / 牙医",
//   "contentProfile": "请介绍自己。考虑包括：\n- 在该领域的经验\n- 主要成就或证书\n- 您对患者护理或工作方法的看法\n- 任何专业化或领域专长",
//   "addLanguage": "添加语言",
//   "levelLanguage": "语言水平",
//   "englishText": "英语",
//   "frenchText": "法语",
//   "basic": "基础",
//   "conversational": "会话",
//   "completionRate": "完成率",
//   "settingAccount": "设置账户",
//   "myAccount": "我的账户",
//   "password": "密码",
//   "verifyAccount": "验证账户",
//   "notification": "通知",
//   "privacy": "隐私",
//   "syncSetting": "同步设置",
//   "deleteAccount": "删除账户",
//   "turnOnNotificationsForAllModes": "开启所有模式的通知",
//   "socialMode": "社交模式",
//   "manageYourConnection": "管理您的连接",
//   "businessMode": "商业模式",
//   "connectToPartnerChances": "与合作伙伴连接，获取机会",
//   "newsMode": "新闻模式",
//   "readNews": "阅读新闻",
//   "marketPlace": "市场",
//   "shoppingProductsAndService": "购物产品和服务",
//   "datingMode": "约会模式",
//   "findRealPartnerForYou": "为你找到真正的伴侣",
//   "strongBodyAi": "StrongBody.ai",
//   "darkMode": "暗模式",
//   "selectMode": "选择护眼模式",
//   "yourInvisible": "您的活动将是不可见的",
//   "settingMode": "设置模式",
//   "policy": "政策",
//   "yourActivityInvisible": "您的活动将不可见",
//   "helpCenterTitle": "帮助中心",
//   "helpCenterDescription": "您需要知道的一切",
//   "shoppingCartTitle": "购物车",
//   "shoppingCartDescription": "管理您在 MarketPlace 和网络上的保存订单。",
//   "shakeToExploreTitle": "摇一摇探索附近",
//   "shakeToExploreDescription": "切换到摇一摇探索",
//   "balanceTitle": "余额",
//   "balanceDescription": "您可以将钱提取到自己的账户",
//   "sellerModeTitle": "卖家模式",
//   "sellerModeDescription": "切换到服务销售",
//   "logOut": "登出",
//   "textAndCall": "短信和通话",
//   "showViewedStatus": "显示 '已查看' 状态",
//   "allowMessaging": "允许消息",
//   "allowCall": "允许通话",
//   "diary": "日记",
//   "allowViewingAndCommenting": "允许查看和评论",
//   "friendSource": "好友来源",
//   "suggestFriendsFromContacts": "从联系人中推荐朋友",
//   "addContactsToMultiMe": "当双方在设备上保存彼此的号码时，添加联系人到 MultiMe",
//   "on": "开启",
//   "everybody": "每个人",
//   "friend": "朋友",
//   "phoneNumberTitle": "您的手机号码是什么？",
//   "phoneNumberDescription": "此信息帮助我们验证您的账户，\n确保账户更安全。",
//   "mobileNumber": "手机号码",
//   "enterMobileNumber": "输入手机号码",
//   "forgotPassword": "忘记密码",
//   "emailLabel": "电子邮件",
//   "emailHint": "输入电子邮件",
//   "alreadyMember": "已经是会员？",
//   "signUp": "注册",
//   "pickFriendsTitle": "选择 10 个最佳朋友和客户",
//   "inviteFriendMessage": "邀请朋友继续",
//   "searchContactsHint": "搜索您的联系人",
//   "importContactsTitle": "导入您的联系人",
//   "importContactsMessage": "StrongBody 永远不会代表您向朋友发送短信",
//   "importButton": "导入",
//   "minPasswordCriteria": "最少 8 个字符，1 个大写字母，1 个小写字母，1 个数字",
//   "confirmPasswordHint": "确认密码",
//   "signInTitle": "登录",
//   "rememberMe": "记住我",
//   "notAMember": "还不是会员？",
//   "userName": "用户名",
//   "publicName": "公开用户名",
//   "occupation": "职业",
//   "enterOccupation": "输入职业",
//   "termsOfService": "服务条款",
//   "privacyPolicy": "隐私政策",
//   "toData": "了解我们如何使用您的个人数据。",
//   "andOur": "并偶尔接收我们的电子邮件。请阅读我们的 ",
//   "byJoinStrongBody": "加入即表示您同意 StrongBody 的 ",
//   "verifyAccountTitle": "检查您的电子邮件以验证账户",
//   "verifyAccountMessage": "我们已向您发送了确认链接，请检查您的电子邮件完成注册。",
//   "backToSignIn": "返回登录",
//   "emailVerifiedTitle": "电子邮件成功验证",
//   "emailVerifiedMessage": "您已成功验证该账户的电子邮件。",
//   "phoneVerifiedTitle": "手机号码成功验证",
//   "phoneVerifiedMessage": "您已成功验证该账户的手机号码。",
//   "blockUser": "屏蔽",
//   "from": "来自",
//   "imageText": "图片",
//   "createPostTitle": "创建帖子",
//   "postHintText": "创建一个时刻... 您的想法是什么？",
//   "postFirstTimeMessage": "👋 欢迎来到 MultiMe！介绍一下自己，分享一些关于您或您今天来到这里的故事。分享您的知识和故事，建立信任并扩展社区。",
//   "postFirstTimeSubTitle": "发布您的第一篇文章",
//   "strongBody": "StrongBody",
//   "social": "社交",
//   "record": "记录",
//   "backgroundColor": "背景颜色",
//   "checkIn": "签到",
//   "postDetail": "帖子详情",
//   "getStarted": "开始",
//   "send": "发送",
//   "resonate": "共鸣",
//   "nameProduct": "命名产品",
//   "descriptionOption": "描述（可选）",
//   "shareIssue": "分享更多关于此问题的细节",
//   "ifImmediately": "如果您知道有人处于身体危险中，请立即联系当地执法机构。",
//   "postRequest": "发布请求",
//   "supportImage": "支持 .jpg，.jpeg 图片格式，大小不超过",
//   "fiveMB": "5MB",
//   "uploadPhotoBrowse": "上传工作照片。此照片将在人们浏览时出现在列表中",
//   "uploadPhoto": "上传照片",
//   "egPartner": "例如：说明您的需求或提供的内容，行业/领域、所需的技能/经验，描述您对潜在候选人或合作伙伴的要求",
//   "graphic": "平面设计师、2D 动画、...",
//   "headerLogin": "登录到您的帐户",
//   "headerLoginText": "请输入您的电子邮件和密码进行登录",
//   "invalidateEmail": "此电子邮件未注册。",
//   "invalidatePassword": "此密码无效。",
//   "cantEmpty": "{label} 不能为空",
//   "report": "报告",
//   "login": "登录",
//   "orLoginWith": "或",
//   "dontHaveAnAccount": "没有账户？",
//   "already": "已经是会员了吗？",
//   "resetPassword": "重置密码",
//   "resetPasswordText": "请输入与您的帐户关联的电子邮件地址，我们将发送一封电子邮件，提供重置密码的说明。",
//   "emailAddress": "电子邮件地址",
//   "sendEmail": "发送说明",
//   "checkEmail": "检查您的电子邮件",
//   "checkEmailText": "我们已将密码恢复说明发送到您的电子邮件。",
//   "openEmail": "打开电子邮件应用",
//   "createPassword": "创建新密码",
//   "createPasswordText": "您的新密码必须与之前使用的密码不同。",
//   "confirmPassword": "确认新密码",
//   "confirmPasswordText": "两个密码必须匹配",
//   "spamEmail": "没有收到电子邮件？检查您的垃圾邮件过滤器，或者",
//   "spamEmailLink": "尝试另一个电子邮件地址。",
//   "passwordChanged": "密码已更改",
//   "uploadImageBussines": "上传图片",
//   "Whatisyouroccupation?": "你的职业是什么？",
//   "JobProfile": "职位简介",
//   "dayago": "天前",
//   "weekago": "一周前",
//   "SelectaTimeRange": "选择时间范围",
//   "Eg:SeekingMarketingExpertForTechStartup": "例如：为科技初创公司寻找营销专家",
//   "Field/Industry": "领域/行业",
//   "General": "常规",
//   "AvatarImage": "头像图片",
//   "Display_name": "显示名称",
//   "Occupation": "职业",
//   "YouAreAllowedToChange": "您只能更改一次。",
//   "AccountDeactivation": "账户停用",
//   "WhatHappensWhenYou": "当您停用账户时会发生什么？",
//   "YourProfileAndGigs": "您的个人资料和Gigs将不再显示在Strongbody上。\n活动订单将被取消。\n您将无法重新激活您的Gigs。"
// };
// static const Map<String,dynamic> en = {
//   "hello": "Hello",
//   "language": "Language",
//   "home": "Home",
//   "setting": "Settings",
//   "note": "Note",
//   "featured": "Featured",
//   "seeMore": "See More",
//   "order": "Order",
//   "message": "Message",
//   "cancel": "Cancel",
//   "notInterested": "Not Interested?",
//   "swipeLeftNoInterest": "Swiping left means you're not interested.",
//   "askMore": "Ask More",
//   "swipeRightNoInterest": "Swiping right means you want to chat with them.",
//   "business": "Business",
//   "request": "Request",
//   "readMore": "Read More",
//   "readLess": "Read Less",
//   "whatDoYouWantToDo": "What do you want to do?",
//   "postBusinessOpportunity": "Post a business opportunity",
//   "offer": "Offer",
//   "thisIsAn": "This is an",
//   "title": "Title",
//   "graphicDesigner": "Graphic Designer, 2D Animation, ....",
//   "businessNeeds": "Business Needs",
//   "description": "Description",
//   "shortDescription": "Eg: Short description, Main tasks I perform, Skills and experience, Work environment, etc.",
//   "stateYourNeeds": "Eg: State your needs or offer, Industry/sector, Required skills/experience, Describe what you are looking for in a potential candidate or partner",
//   "budgetInvestmentUSD": "Budget/Investment (USD)",
//   "uploadAvatar": "Upload Avatar",
//   "optional": "(Optional)",
//   "uploadAPhoto": "Upload a job photo. This photo will appear in the list for others to see.",
//   "addMore": "Add More",
//   "postDisplayDuration": "Post Display Duration",
//   "dayAgo": "day ago",
//   "age": "Age",
//   "distance": "Distance",
//   "skip": "Skip",
//   "post": "Post",
//   "maleText": "Male",
//   "preferredGender": "Preferred Gender",
//   "femaleText": "Female",
//   "otherText": "Other",
//   "save": "Save",
//   "dating": "Dating",
//   "likeYou": "Like You",
//   "matches": "Matches",
//   "editBio": "Edit Bio",
//   "editSeeking": "Edit Seeking",
//   "camera": "Camera",
//   "gallery": "Gallery",
//   "aboutYou": "About You",
//   "displayName": "Display Name",
//   "name": "Name",
//   "yearOfBirth": "Year of Birth",
//   "youYearOfBirth": "Your Year of Birth",
//   "yourOccupation": "Your Occupation",
//   "shortBio": "Short Bio",
//   "yourBio": "Your Bio",
//   "addYourPhotos": "Add Your Photos",
//   "addAtLeast2": "Add at least 2 photos, you can change them later",
//   "continueButton": "Continue",
//   "likedYou": "Liked You",
//   "youLikedThem": "You liked them",
//   "theyLikedYou": "They liked you",
//   "reviewThePeople": "Review the people you liked",
//   "today": "Today",
//   "reviewThePeople2": "Review the people who liked you",
//   "reviewThePeopleYouHaveSwipedLeftOn": "Review the people you have swiped left on.",
//   "yesterday": "Yesterday",
//   "itsAMatch": "It's a match!",
//   "startAConversationNow": "Start a conversation now.",
//   "startChatting": "Start chatting",
//   "keepSwiping": "Keep swiping",
//   "recent": "Recent",
//   "whoAreYouSeeking": "Who are you seeking?",
//   "recentlyActive": "Recently Active",
//   "welcomeDatingMode": "Welcome Dating Mode",
//   "ruleBeYourselfTitle": "Be Yourself.",
//   "ruleBeYourselfDescription": "Make sure your photos, age, and bio are true to who you are",
//   "ruleStaySafeTitle": "Stay Safe.",
//   "ruleStaySafeDescription": "Don't be too quick to give out personal information.",
//   "ruleStaySafeLinkText": "Date Safely",
//   "rulePlayItCoolTitle": "Play It Cool",
//   "rulePlayItCoolDescription": "Respect others and treat them as you would like to be treated.",
//   "ruleBeProactiveTitle": "Be Proactive.",
//   "ruleBeProactiveDescription": "Always report bad behavior.",
//   "iAgreeButtonText": "I Agree",
//   "followHouseRulesText": "Please follow these House Rules",
//   "addNewCardTitle": "Add New Card",
//   "cardDetailsTitle": "Card details",
//   "cardNumberLabel": "Card number",
//   "cardNumberHint": "0000-0000-0000-0000",
//   "expiryDateLabel": "Expiry date",
//   "expiryDateHint": "MM/YY",
//   "cvvLabel": "CVV",
//   "cvvHint": "CVV",
//   "cardholderNameLabel": "Cardholder's name",
//   "cardholderNameHint": "Name on card",
//   "verificationTransaction": "Verification transaction",
//   "verificationTransactionText": " (It will be refunded to your card immediately after the verification)",
//   "confirmButtonText": "Confirm",
//   "checkoutTitle": "Checkout",
//   "warningMessage": "Before making an order, make sure the address is correct and matches your current address.",
//   "orderDetailsTitle": "Order Details",
//   "orderDetailsHint": "Your order",
//   "paymentOptionsTitle": "Payment Options",
//   "paymentDetailsTitle": "Payment Details",
//   "totalCost": "Total cost",
//   "paypal": "Paypal",
//   "visaCard": "Visa card",
//   "paymentFailed": "Payment Failed",
//   "paymentFailedMessage": "There was an error processing your payment. Please try again later.",
//   "locationLabel": "Location",
//   "addNewCard": "Add New Card",
//   "orderID": "Order ID",
//   "orderDate": "Order date",
//   "productPrice": "Product price",
//   "voucher": "Voucher",
//   "serviceFee": "Service fee",
//   "termsAndConditions": "By pressing Pay, you agree to the ",
//   "termsAndConditionsLink": "terms and conditions of Multi.Me.",
//   "confirmOrder": "Confirm Order",
//   "dateText": "dd/MM/yyyy",
//   "personalInformation": "Personal Information",
//   "fullName": "Full Name",
//   "phone": "Phone",
//   "addressLabel": "Address",
//   "addressHint": "Your address",
//   "specificAddressLabel": "Specific address",
//   "specificAddressHint": "Specific address",
//   "mapPlaceholder": "Map Placeholder",
//   "setDefaultCheckboxLabel": "Set as default address",
//   "saveChangeButton": "Save Change",
//   "liked": "Liked",
//   "marketplace": "MarketPlace",
//   "newText": "News",
//   "mostView": "MostView",
//   "mostViewed": "Most Viewed",
//   "likeNew": "Like New",
//   "adsPlaceholder": "Ads Placeholder",
//   "productName": "Product Name",
//   "shopAddress": "Shop Address",
//   "postDate": "Post Date",
//   "price": "Price",
//   "orderPage": "Order Page",
//   "messagePage": "Message Page",
//   "cancelOrder": "Cancel my order",
//   "payment": "Payment",
//   "totalPayment": "Total payment",
//   "subtotal": "Subtotal",
//   "orderShipped": "Your order had started shipping",
//   "orderPlaced": "You placed the order",
//   "deliveredTo": "Delivered to",
//   "sentFrom": "Sent from",
//   "awaitingApproval": "Awaiting approval",
//   "productDetails": "Product Details",
//   "quantity": "Quantity",
//   "sellerInfo": "Seller Info",
//   "contactSeller": "Contact seller",
//   "shippingInfo": "Shipping Info",
//   "orderFailed": "Order failed",
//   "orderFailureMessage": "There is something wrong with your order.",
//   "orderCheckMessage": "Please go back and check your order again thoroughly.",
//   "returnOrder": "Return my order",
//   "orderConfirmed": "Order Confirmed",
//   "thankYouMessage": "Thank you for your order. You will receive email confirmation shortly.",
//   "checkOrderStatus": "Check the status of your order in Your Cart.",
//   "continueShopping": "Continue shopping",
//   "addNewAddress": "Add New Address",
//   "postProduct": "Post Product",
//   "productHintText": "Product name (Minimum 50 and maximum 150 words)",
//   "statusText": "Status",
//   "productFeatures": "Outstanding features of the product, what it includes, how long it has been used...",
//   "statusProduct": "Choose your product’s status",
//   "backText": "Back",
//   "buyNow": "Buy Now",
//   "reportSeller": "Report is Seller",
//   "noRecommendSeller": "Don't recommend this seller",
//   "noMoreProfile": "No more profiles!",
//   "selectPostsFrom": "Select posts from",
//   "offersFrom": "Offers from multi.me",
//   "payFee": "Pay fee",
//   "apply": "Apply",
//   "freePosting": "Free posting",
//   "adType": "Type: Regular ad\nApplies to first personal ads/month",
//   "upgradeToPro": "Upgrade to pro",
//   "postFreeAds": "Post free ads",
//   "adTypeAds": "Type: Regular - Permanent Ads",
//   "appliesToAllPosts": "Applies to all posts",
//   "multiMe": "Multi.Me ",
//   "doesNot": "DOES NOT",
//   "allowDepositPrivate": "allow: Depositing/Private transfers/Off-platform transactions/Recruiting collaborators/Giving free gifts/Providing contact information or Canceling orders at the seller's request.",
//   "pleaseOnlyBuySell": "Please only Buy/Sell on Multi.Me to avoid being scammed. Multi.Me will collect and use chat history according to Multi.Me's Privacy Policy.",
//   "gotUnusedItem": "Got unused items?",
//   "turnThemIntoCash": "Turn them into cash!",
//   "postForSale": "Post for Sale",
//   "itsTotally": "It's totally",
//   "safe": "safe",
//   "toPurchaseAtMultiMe": "to purchase at Multi.Me. Your money will only be sent to the seller after you confirm receiving the product; otherwise, it will be returned to you.",
//   "aboutThisItem": "About this item",
//   "condition": "Condition",
//   "type": "Type",
//   "brand": "Brand",
//   "show": "Show",
//   "hide": "Hide",
//   "estDelivery": "Est. Delivery: ",
//   "safePurchase": "It’s totally safe to purchase at Multi.Me. Your money will only be sent to the seller after your confirmation on receiving the product, else it will return to you.",
//   "chargeRateInfo": "There will be a 3% charge rate for purchases > $1000\nThere will be a 5% charge rate for purchases < $999",
//   "thereWill": "There will be a",
//   "chargeRate": "charge rate for purchases",
//   "defaultText": "Default",
//   "createDate": "Create Date",
//   "shippingPrice": "Includes shipping price",
//   "contactText": "Please contact me if you have any question, want to discuss the price or you want a better offer.",
//   "contactMe": "Contact Me",
//   "moreLike": "More like this",
//   "viewAll": "View All",
//   "postMe": "Post Me",
//   "postNews": "Post News",
//   "newsTitle": "News Title",
//   "category": "Category",
//   "country": "Country",
//   "articleSource": "Article Source",
//   "content": "Content",
//   "copyContent": "Copy content from Article here",
//   "addCoverPhoto": "+ \nAdd Cover Photo",
//   "selectCategory": "Select Category",
//   "reviewPost": "Review your posts",
//   "tabAll": "All",
//   "tabSports": "Sports",
//   "tabPolitics": "Politics",
//   "tabBusiness": "Business",
//   "tabHealth": "Health",
//   "tabTravel": "Travel",
//   "tabScience": "Science",
//   "readAndShare": "Read and share news around the world",
//   "createCommunity": "🙌 We don't create news, we create a community where people share and discuss news, helping each other develop critical thinking and multi-faceted understanding",
//   "hotNew": "Hot News",
//   "history": "History",
//   "createPost": "Create Post",
//   "latest": "Latest",
//   "about": "About",
//   "skill": "Skills",
//   "education": "Education",
//   "experience": "Experience",
//   "notPost": "Not Post Yet",
//   "subNewPost": "Try subscribing to a new post of yours...",
//   "notReview": "Not Reviews Yet",
//   "thanksStrongBody": "Your order means a lot to the StrongBody community.Order now to write the first review here.",
//   "productAndService": "Explore products and services",
//   "newService": "New Service",
//   "notService": "Not Services Yet",
//   "sellerCreateService": "Seller hasn't created any services yet. If she sold something it would be here.",
//   "noProduct": "Not Product Yet",
//   "noProductDescription": "Seller not sold any products yet. If she does in the future, the products will appear here.",
//   "activeShop": "Active StrongBodyShop here →",
//   "add": "Add",
//   "fullTime": "Full Time",
//   "toYear": "To Year",
//   "companyBusiness": "Company / business name",
//   "fromYear": "From year",
//   "positionCompany": "Position in the company",
//   "addressCountry": "Address / Country",
//   "addSkill": "Add Skill (e.g. Voice Talent)",
//   "year": "Year",
//   "uploadImage": "Upload 430x190 px images for more impressions and sales opportunities.",
//   "displayNameLabel": "Display Name",
//   "displayNameHint": "Please name your account",
//   "yourProfession": "Your Profession",
//   "yourProfessionHint": "Example 1: Board-certified Dermatologist specializing in cosmetic procedures and skin cancer treatments. Example 2: Certified Personal Trainer focusing on weight loss and strength training for all fitness levels.",
//   "professionalLabel": "Your Professional Title",
//   "professionalHint": "Ex: Personal Trainer / Therapist / Dentist",
//   "contentProfile": "Please introduce yourself. Consider including:\n- Your experience in the field\n- Key achievements or certifications\n- Your approach to patient care or work methodology\n- Any specializations or areas of expertise",
//   "addLanguage": "Add Language",
//   "levelLanguage": "Level Language",
//   "englishText": "English",
//   "frenchText": "French",
//   "basic": "Basic",
//   "conversational": "Conversational",
//   "completionRate": "Completion Rate",
//   "settingAccount": "Setting Account",
//   "myAccount": "My account",
//   "password": "Password",
//   "verifyAccount": "Verify account",
//   "notification": "Notification",
//   "privacy": "Privacy",
//   "syncSetting": "Sync Setting",
//   "deleteAccount": "Delete account",
//   "turnOnNotificationsForAllModes": "Turn on notifications for all modes",
//   "socialMode": "Social Mode",
//   "manageYourConnection": "Manage your connection",
//   "businessMode": "Business Mode",
//   "connectToPartnerChances": "Connect to partner, chances",
//   "newsMode": "News Mode",
//   "readNews": "Read news",
//   "marketPlace": "Market Place",
//   "shoppingProductsAndService": "Shopping products and service",
//   "datingMode": "Dating Mode",
//   "findRealPartnerForYou": "Find real partner for you",
//   "strongBodyAi": "StrongBody.ai",
//   "darkMode": "Dark Mode",
//   "selectMode": "Select eye protection mode",
//   "yourInvisible": "Your activity will be invisible",
//   "settingMode": "Setting Mode",
//   "policy": "Policy",
//   "yourActivityInvisible": "Your activity will be invisible",
//   "helpCenterTitle": "Help Center",
//   "helpCenterDescription": "Everything you need to know",
//   "shoppingCartTitle": "Shopping Cart",
//   "shoppingCartDescription": "Manage your saved orders at MarketPlace, Network.",
//   "shakeToExploreTitle": "Shake to explore nearby",
//   "shakeToExploreDescription": "Switch to shake to explore",
//   "balanceTitle": "Balance",
//   "balanceDescription": "You can withdraw money to your own account",
//   "sellerModeTitle": "Seller Mode",
//   "sellerModeDescription": "Switch to service selling",
//   "logOut": "Log Out",
//   "textAndCall": "Text and Call",
//   "showViewedStatus": "Show 'Viewed' status",
//   "allowMessaging": "Allow messaging",
//   "allowCall": "Allow call",
//   "diary": "Diary",
//   "allowViewingAndCommenting": "Allow viewing and commenting",
//   "friendSource": "Friend Source",
//   "suggestFriendsFromContacts": "Suggest friends from contacts",
//   "addContactsToMultiMe": "Add contacts to MultiMe when both have each other's numbers saved on their devices",
//   "on": "On",
//   "everybody": "Everybody",
//   "friend": "Friend",
//   "phoneNumberTitle": "What is Your \nPhone Number ?",
//   "phoneNumberDescription": "This information helps us verify your account to \nmake it more secure.",
//   "mobileNumber": "Mobile Number",
//   "enterMobileNumber": "Enter your mobile number",
//   "forgotPassword": "Forgot password",
//   "emailLabel": "Email",
//   "emailHint": "Enter Email",
//   "alreadyMember": "Already a member?",
//   "signUp": "Sign up",
//   "pickFriendsTitle": "Pick 10 best friends & clients",
//   "inviteFriendMessage": "Invite a friend to continue",
//   "searchContactsHint": "Search your contacts",
//   "importContactsTitle": "Import your contacts",
//   "importContactsMessage": "StrongBody never texts friends on your behalf",
//   "importButton": "Import",
//   "minPasswordCriteria": "Min 8 characters, 1 uppercase, 1 lowercase, 1 number",
//   "confirmPasswordHint": "Confirm Password",
//   "signInTitle": "Sign in",
//   "rememberMe": "Remember me",
//   "notAMember": "Not a member yet?",
//   "userName": "User Name",
//   "publicName": "Public Username",
//   "occupation": "Occupation",
//   "enterOccupation": "Enter Occupation",
//   "termsOfService": "Terms of Service",
//   "privacyPolicy": "Privacy policy",
//   "toData": " to learn how we use your personal data.",
//   "andOur": " and to occasionally receive emails from us. Please read our ",
//   "byJoinStrongBody": "By joining, you agree to the StrongBody ",
//   "verifyAccountTitle": "Check your email to Verify account",
//   "verifyAccountMessage": "We have sent you a link to confirm. Please check your email to complete your registration.",
//   "backToSignIn": "Back to Sign in",
//   "emailVerifiedTitle": "Email identified successfully",
//   "emailVerifiedMessage": "You have successfully verified the phone number for this account.",
//   "phoneVerifiedTitle": "Phone number identified successfully",
//   "phoneVerifiedMessage": "You have successfully verified the phone number for this account.",
//   "blockUser": "Block",
//   "from": "From",
//   "imageText": "Image",
//   "createPostTitle": "Create Post",
//   "postHintText": "Create a moment... What's on your mind?",
//   "postFirstTimeMessage": "👋 Welcome to MultiMe! Introduce yourself and share something interesting about you or what brings you here today. Share your knowledge and stories to build trust and expand the community.",
//   "postFirstTimeSubTitle": "Post your first article",
//   "strongBody": "StrongBody",
//   "social": "Social",
//   "record": "Record",
//   "backgroundColor": "Background Color",
//   "checkIn": "Check In",
//   "postDetail": "Post Detail",
//   "getStarted": "Get Started",
//   "send": "Send",
//   "resonate": "Resonate",
//   "nameProduct": "Name Product",
//   "descriptionOption": "Description (Optional)",
//   "shareIssue": "Share more details about this issue",
//   "ifImmediately": "If you know someone is in physical danger, please contact your local law enforcement agency immediately.",
//   "postRequest": "Post Request",
//   "supportImage": "Supports image formats .jpg, .jpeg with a size under",
//   "fiveMB": "5MB",
//   "uploadPhotoBrowse": "Upload a photo of the job. This photo will appear on listings for people to browse",
//   "uploadPhoto": "Upload Photo",
//   "egPartner": "Eg. State your Needs or Offerings, Industry/Field,Skills/Experience Required, Describe what you're looking for in a potential candidate or partner",
//   "graphic": "Graphic Designer, 2D Animation, ....",
//   "headerLogin": "Sign in to your Account",
//   "headerLoginText": "Enter your email and password to log in",
//   "invalidateEmail": "This email is not registered.",
//   "invalidatePassword": "This password is invalid.",
//   "cantEmpty": "{label} cannot be empty",
//   "report": "Report",
//   "login": "Login",
//   "orLoginWith": "Or",
//   "dontHaveAnAccount": "Don't have an account?",
//   "already": "Already a member?",
//   "resetPassword": "Reset password",
//   "resetPasswordText": "Enter the email associated with your account and we'll send an email with instructions to reset your password.",
//   "emailAddress": "Email Address",
//   "sendEmail": "Send Instructions",
//   "checkEmail": "Check your email",
//   "checkEmailText": "We have sent a password recover instructions to your email.",
//   "openEmail": "Open email app",
//   "createPassword": "Create New Password",
//   "createPasswordText": "Your new password must be different from previous used passwords.",
//   "confirmPassword": "Confirm New Password",
//   "confirmPasswordText": "Both passwords must match",
//   "spamEmail": "Did not receive the email? Check your spam filter, or ",
//   "spamEmailLink": "try another email address.",
//   "passwordNotMatch": "Password does not match",
//   "uploadImageBussines": "Upload Image",
//   "Whatisyouroccupation?": "What is your occupation?",
//   "JobProfile": "Job Profile",
//   "dayago": "day ago",
//   "weekago": "week ago",
//   "SelectaTimeRange": "Select a time range",
//   "Eg:SeekingMarketingExpertForTechStartup": "Eg: Seeking Marketing Expert for Tech Startup",
//   "Field/Industry": "Field/Industry",
//   "General": "General",
//   "AvatarImage": "Avatar image",
//   "Display_name": "Display name",
//   "Occupation": "Occupation",
//   "YouAreAllowedToChange": "You are allowed to change only once.",
//   "AccountDeactivation": "Account Deactivation",
//   "WhatHappensWhenYou": "What happens when you deactivate your account?",
//   "YourProfileAndGigs": "Your profile and Gigs won't be shown on Strongbody anymore.\nActive orders will be cancelled.\nYou won't be able to re-activate your Gigs.",
//   "SettingBusinessMode": "Setting Business Mode",
//   "Postoffer": "Post offer",
//   "ShareOpportunities": "Share Opportunities",
//   "RequestSupport": "Request Support",
//   "Getnotifiedwhenyoumatch": "Get notified when you match",
//   "SettingDatingMode": "Setting Dating Mode",
//   "EditprofileDatingmode": "Edit profile Dating mode",
//   "Selecteyeprotectionmode": "Select eye protection mode",
//   "Managewhoicanview": "Manage who i can view",
//   "Commentanonymous": "Comment anonymous",
//   "Onlypeopleyoulikecanfindyou": "Only people you like can find you",
//   "Notification": "Notification",
//   "SettingMarketplance": "Setting Marketplance",
//   "ShareYourProducts": "Share Your Products",
//   "SettingNewMode": "Setting New Mode",
//   "Customarticle": "Custom article",
//   "Changestyle": "Change style, color, text",
//   "Reviewlikedarticles": "Review liked articles",
//   "Reviewyourposts": "Review your posts",
//   "SettingSocialMode": "Setting Social Mode",
//   "PostRequest": "Post Request",
//   "Editprofile": "Edit profile",
//   "Previewprofile": "Preview profile",
//   "ReferralProgram": "Referral Program",
//   "Friends": "Friends",
//   "morepeopletoget": "more people to get free\nvoice translations for one month.",
//   "Add": "Add",
//   "Messages": "Messages",
//   "SocialMode": "Social Mode",
//   "BusinessMode": "Business Mode",
//   "Shoppingproducts": "Shopping products",
//   "Serviceforyouglobally": "Service for you globally",
//   "MyOrder": "My Order",
//   "ToPay": "To Pay",
//   "ToShip": "To Ship",
//   "ToReceive": "To Receive",
//   "ToRate": "To Rate",
//   "VoiceTranslatePlan": "Voice Translate Plan",
//   "Benefitsofsubscribing:": "Benefits of subscribing:",
//   "Unlimitedaccesstovoice": "Unlimited access to voice translation tools",
//   "Instanttranslationformessaging": "Instant translation for messaging and calls.",
//   "Cancelyoursubscriptionanytime.": "Cancel your subscription anytime.",
//   "Subscribe": "Subscribe",
//   "ViewallourUpgrades": "View all our Upgrades",
//   "Support": "Support",
//   "HelpCenter": "Help Center",
//   "ChatwithMultime": "Chat with Multime",
//   "SwitchtoSeller": "Switch to Seller",
//   "Activefor6Months": "Active for 6 Months",
//   "MyProfile": "My Profile",
//   "Home": "Home",
//   "Order": "Order",
//   "Message": "Message",
//   "Me": "Me",
//   "Request": "Request",
//   "Readmore": "Read more",
//   "Readless": "Read less",
//   "Clickheretouploadimage": "Click here to upload image",
//   "Supportsimageformats": "Supports image formats .jpg, .jpeg with a size under 5MB",
//   "Balance": "Balance",
//   "Availablefunds": "Available funds",
//   "ActiveService": "Active Service",
//   "ActiveProduct": "Active Product",
//   "Showingresults15of": "Showing results 1–5 of",
//   "Showingresults1": "Showing results 1",
//   "Managepayoutmethod": "Manage payout method",
//   "Carddetails": "Card details",
//   "Cardnumber": "Card number",
//   "Expirydate": "Expiry date",
//   "Cardholdername": "Cardholder’s name",
//   "Deletepayoutmethod": "Delete payout method",
//   "Creditcard": "Credit card",
//   "Withdrawntodate": "Withdrawn to date:",
//   "Withdraw": "Withdraw",
//   "AsaSeller": "As a Seller",
//   "AsaBuyer": "As a Buyer",
//   "PaymentBeingCleared": "Payment Being Cleared",
//   "OrdersisActiveNow": "Orders is Active Now",
//   "ServicesareActiveNow": "Services are Active Now",
//   "ProductsareActiveNow": "Products are Active Now",
//   "Payment": "Payment",
//   "Chooseawithdrawmethod": "Choose a withdraw method",
//   "Withdrawamount": "Withdraw amount",
//   "Maximumwithdrawamountis": "Maximum withdraw amount is",
//   "Permissiondenied": "Permission denied. Storage access is required.",
//   "Unabletosaveimage": "Unable to save image. Please try again.",
//   "ImagesavedtoDownloads": "Image saved to Downloads:",
//   "Imagesavedat": "Image saved at:",
//   "Therewasanerror": "There was an error occured while saving the image.",
//   "Pleasenotethatyou": "Please note that you need to be over 18 years old.",
//   "Reviewthepeople": "Review the people have liked you",
//   "Yes": "Yes",
//   "Dontinterested": "Don't interested",
//   "Block": "Block",
//   "Theywillnotbe": "They will not be able to see your profile and cannot message you in multi.me",
//   "ReportThisDatingProfile": "Report this dating profile",
//   "YouCanReportThisDating": "You can report this Dating profile after selecting a reason",
//   "InTheDatingSection": "in the dating section",
//   "View": "View",
//   "ThisAppWillHaveAccess": "This app will have access to the photos you select",
//   "Done": "Done",
//   "ShowSelected": "Show Selected",
//   "Interests": "Interests",
//   "More": "More",
//   "SubmitASupportRequest": "Submit a Support Request",
//   "Connected": "Connected",
//   "Views": "Views",
//   "TotalPostsAcrossAllPlatforms": "Total posts across all platforms",
//   "MyAddresses": "My Addresses",
//   "CreditDebitCard": "Credit / Debit Card",
//   "NotificationSetting": "Notification Setting",
//   "ConnectWithSuggestedAccounts": "Connect with suggested accounts",
//   "InviteAfriendToContinue": "Invite a friend to continue",
//   "SearchForContacts": "Search for contacts",
//   "ContactsUsingPhoneNumber": "Contacts using phone number",
//   "AutoAddFirend": "Auto add friend",
//   "AutomaticallyAddContactsAsFriends": "Automatically add contacts as friends",
//   "OK": "OK",
//   "AddYourContact": "Add your contact",
//   "CoppyLink": "Coppy Link",
//   "CopyLinkToShare": "Copy link to share",
//   "or": "or",
//   "PickBestFriendsClients": "Pick 10 best friends & clients",
//   "ImportYourContacts": "Import your contacts",
//   "StrongbodyNeverTexts": "Strongbody never texts friends on your behalf",
//   "Import": "Import",
//   "SKIP": "SKIP",
//   "CreateNewService": "Create New Service",
//   "CreatedServiceCompleted": "Created Service Completed",
//   "ShareYourServices": "Share your services with people, they will thank you for it",
//   "OrCopyLink": "Or Copy Link",
//   "CreateYourService": "Create Your Service",
//   "Medical": "Medical",
//   "Nutrition": "Nutrition",
//   "Physical": "Physical",
//   "Mental": "Mental",
//   "Pharmacy": "Pharmacy",
//   "Beauty": "Beauty",
//   "Child": "Child",
//   "Family": "Family",
//   "Elderly": "Elderly",
//   "Medical Travel": "Medical Travel",
//   "MedSupport": "MedSupport",
//   "Freelance Digital Work": "Freelance Digital Work",
//   "Freelance Physical Work": "Freelance Physical Work",
//   "Freelance Creative Work": "Freelance Creative Work",
//   "Freelance Administrative Work": "Freelance Administrative Work",
//   "Freelance Technical Work": "Freelance Technical Work",
//   "Massage": "Massage",
//   "CategoryServices": "Category Services",
//   "certificate": "Certificate",
//   "field_optional": "(Field Optional)",
//   "certificate_description": "We recommend uploading your certificates and qualifications to build customer trust and provide a basis for regulatory authorities. You can upload these documents later. Please note that you will only be able to receive customer payments once you have uploaded all necessary certificates and qualifications.",
//   "supported_file_types": "Supported file types: JPEG, PNG, PDF, and PNG",
//   "add_file": "Add file",
//   "self_declared_info": "This information will be displayed as \"Self-declared\" on your profile.",
//   "ServiceDescription": "Service description",
//   "service_image": "Service Image",
//   "upload_images_for_impressions": "Upload images for more impressions and sales opportunities.",
//   "square_product_photo": "Square product photo size.",
//   "include_at_least_two_images": "You should include at least 2 images to fully represent your services.",
//   "service_name": "Service name",
//   "starting_price": "Starting Price",
//   "service_description": "Service description",
//   "time_to_complete_order": "Time to complete the order",
//   "related_questions": "Related questions",
//   "help_center": "Help Center",
//   "account": "Account",
//   "privacy_and_security": "Privacy and security",
//   "business_mode": "Business Mode",
//   "dating_mode": "Dating Mode",
//   "news": "News",
//   "support_request": "Support Request",
//   "help_center_request_description": "We're here to help with any issues you may have! Submit a support request! As long as your request doesn't get lost in the portal, we'll get back to you shortly.",
//   "submit": "Submit",
//   "help_center_search_prompt": "Need help? Type in the keyword you're searching for.",
//   "no_search_results": "Sorry, we couldn’t find any matching results :(",
//   "help_topics": "Help topics",
//   "order_support": "Order Support",
//   "help_center_greeting": "Hello, how can we assist you?",
//   "help_center_description": "We’re here to help with answers to your questions and popular topics.",
//   "your_photo": "Your photo",
//   "order_id": "Order ID",
//   "helpCenter": {
//     "recommendedArticles": "Recommended articles",
//     "protectAccount": {
//       "title": "How to protect your account?",
//       "description": "To protect your account, make sure to use a strong password and enable two-factor authentication"
//     },
//     "suspiciousActivity": {
//       "title": "Detecting suspicious activity",
//       "description": "If you notice any suspicious activity on your account, change your password immediately and check your login history"
//     },
//     "reportBlock": {
//       "title": "How to report and block users",
//       "intro": "You can report and block users who violate our guidelines",
//       "stepsIntro": "Follow these steps:",
//       "step1": "1. Tap the three dots icon in the top right",
//       "step2": "2. Select 'Report' or 'Block' user"
//     },
//     "likeReact": {
//       "title": "How to like and react",
//       "description1": "You can express your feelings about posts by liking or reacting to them",
//       "description2": "Here's how to do it:",
//       "howToLike": {
//         "title": "How to like a post:",
//         "step1": "1. Tap the heart icon below the post",
//         "step2": "2. The heart will turn red to show you've liked it"
//       },
//       "howToUnlike": {
//         "intro": "To unlike a post:",
//         "step1": "1. Tap the red heart icon again",
//         "step2": "2. The heart will return to its original state"
//       }
//     },
//     "sendMessage": {
//       "title": "How to send messages",
//       "description": "Learn how to send messages and communicate with other users in the app"
//     }
//   }
// };
// static const Map<String,dynamic> hi = {
//   "hello": "नमस्ते",
//   "language": "भाषा",
//   "home": "मुख पृष्ठ",
//   "setting": "सेटिंग्स",
//   "note": "नोट",
//   "featured": "विशेष रूप से प्रदर्शित",
//   "seeMore": "और देखें",
//   "order": "आदेश",
//   "message": "संदेश",
//   "cancel": "रद्द करें",
//   "notInterested": "रुचि नहीं है?",
//   "swipeLeftNoInterest": "बाएँ स्वाइप करना मतलब आप रुचि नहीं रखते हैं।",
//   "askMore": "और पूछें",
//   "swipeRightNoInterest": "दाएँ स्वाइप करना मतलब आप उनके साथ चैट करना चाहते हैं।",
//   "business": "व्यवसाय",
//   "request": "अनुरोध",
//   "readMore": "और पढ़ें",
//   "readLess": "कम पढ़ें",
//   "whatDoYouWantToDo": "आप क्या करना चाहते हैं?",
//   "postBusinessOpportunity": "व्यवसाय का अवसर पोस्ट करें",
//   "offer": "प्रस्ताव",
//   "thisIsAn": "यह एक",
//   "title": "शीर्षक",
//   "graphicDesigner": "ग्राफिक डिजाइनर, 2D एनीमेशन, ....",
//   "businessNeeds": "व्यवसाय की आवश्यकताएँ",
//   "description": "विवरण",
//   "shortDescription": "उदाहरण: संक्षिप्त विवरण, मुख्य कार्य, कौशल और अनुभव, कार्य वातावरण, आदि",
//   "stateYourNeeds": "उदाहरण: अपनी आवश्यकताएँ या प्रस्ताव, उद्योग/क्षेत्र, आवश्यक कौशल/अनुभव, संभावित उम्मीदवार या साझेदार में आप क्या खोज रहे हैं, यह बताएं",
//   "budgetInvestmentUSD": "बजट/निवेश (USD)",
//   "uploadAvatar": "अवतार अपलोड करें",
//   "optional": "(वैकल्पिक)",
//   "uploadAPhoto": "एक नौकरी की फोटो अपलोड करें। यह फोटो दूसरों को देखने के लिए सूची में दिखाई देगी।",
//   "addMore": "अधिक जोड़ें",
//   "postDisplayDuration": "पोस्ट डिस्प्ले अवधि",
//   "dayAgo": "दिन पहले",
//   "age": "उम्र",
//   "distance": "दूरी",
//   "skip": "स्किप करें",
//   "post": "पोस्ट करें",
//   "maleText": "पुरुष",
//   "preferredGender": "पसंदीदा लिंग",
//   "femaleText": "महिला",
//   "otherText": "अन्य",
//   "save": "सहेजें",
//   "dating": "डेटिंग",
//   "likeYou": "आपको पसंद किया",
//   "matches": "मेल खाता है",
//   "editBio": "जैव संशोधित करें",
//   "editSeeking": "क्या ढूंढ रहे हैं इसे संशोधित करें",
//   "camera": "कैमरा",
//   "gallery": "गैलरी",
//   "aboutYou": "आपके बारे में",
//   "displayName": "प्रदर्शन नाम",
//   "name": "नाम",
//   "yearOfBirth": "जन्म का वर्ष",
//   "youYearOfBirth": "आपका जन्म वर्ष",
//   "yourOccupation": "आपका व्यवसाय",
//   "shortBio": "संक्षिप्त जैव",
//   "yourBio": "आपका जैव",
//   "addYourPhotos": "अपनी फोटो जोड़ें",
//   "addAtLeast2": "कम से कम 2 फोटो जोड़ें, आप इन्हें बाद में बदल सकते हैं",
//   "continueButton": "जारी रखें",
//   "likedYou": "आपको पसंद किया",
//   "youLikedThem": "आपने उन्हें पसंद किया",
//   "theyLikedYou": "उन्होंने आपको पसंद किया",
//   "reviewThePeople": "उन लोगों की समीक्षा करें जिन्हें आपने पसंद किया",
//   "today": "आज",
//   "reviewThePeople2": "उन लोगों की समीक्षा करें जिन्होंने आपको पसंद किया",
//   "reviewThePeopleYouHaveSwipedLeftOn": "उन लोगों की समीक्षा करें जिन्हें आपने बाएँ स्वाइप किया है।",
//   "yesterday": "कल",
//   "itsAMatch": "यह मैच है!",
//   "startAConversationNow": "अब बातचीत शुरू करें।",
//   "startChatting": "चैटिंग शुरू करें",
//   "keepSwiping": "स्वाइप करते रहें",
//   "recent": "हाल ही में",
//   "whoAreYouSeeking": "आप किसे ढूंढ रहे हैं?",
//   "recentlyActive": "हाल ही में सक्रिय",
//   "welcomeDatingMode": "डेटिंग मोड में स्वागत है",
//   "ruleBeYourselfTitle": "स्वयं बनें।",
//   "ruleBeYourselfDescription": "यह सुनिश्चित करें कि आपकी फोटो, उम्र, और जैव आपके असली व्यक्तित्व के अनुरूप हैं",
//   "ruleStaySafeTitle": "सुरक्षित रहें।",
//   "ruleStaySafeDescription": "व्यक्तिगत जानकारी देने में जल्दबाजी न करें।",
//   "ruleStaySafeLinkText": "सुरक्षित डेटिंग करें",
//   "rulePlayItCoolTitle": "ठंडा खेलें",
//   "rulePlayItCoolDescription": "दूसरों का सम्मान करें और उन्हें वैसे ही व्यवहार करें जैसे आप चाहते हैं कि वे आपके साथ व्यवहार करें।",
//   "ruleBeProactiveTitle": "सक्रिय रहें।",
//   "ruleBeProactiveDescription": "हमेशा बुरी हरकतों की रिपोर्ट करें।",
//   "iAgreeButtonText": "मैं सहमत हूँ",
//   "followHouseRulesText": "कृपया इन हाउस नियमों का पालन करें",
//   "addNewCardTitle": "नई कार्ड जोड़ें",
//   "cardDetailsTitle": "कार्ड विवरण",
//   "cardNumberLabel": "कार्ड नंबर",
//   "cardNumberHint": "0000-0000-0000-0000",
//   "expiryDateLabel": "समाप्ति तिथि",
//   "expiryDateHint": "MM/YY",
//   "cvvLabel": "CVV",
//   "cvvHint": "CVV",
//   "cardholderNameLabel": "कार्डधारक का नाम",
//   "cardholderNameHint": "कार्ड पर नाम",
//   "verificationTransaction": "सत्यापन लेन-देन",
//   "verificationTransactionText": " (यह सत्यापन के बाद तुरंत आपके कार्ड में वापस हो जाएगा)",
//   "confirmButtonText": "पुष्टि करें",
//   "checkoutTitle": "चेकआउट",
//   "warningMessage": "आदेश देने से पहले, सुनिश्चित करें कि पता सही है और आपके वर्तमान पते से मेल खाता है।",
//   "orderDetailsTitle": "आदेश विवरण",
//   "orderDetailsHint": "आपका आदेश",
//   "paymentOptionsTitle": "भुगतान विकल्प",
//   "paymentDetailsTitle": "भुगतान विवरण",
//   "totalCost": "कुल लागत",
//   "paypal": "पेपल",
//   "visaCard": "वीजा कार्ड",
//   "paymentFailed": "भुगतान विफल",
//   "paymentFailedMessage": "आपका भुगतान संसाधित करने में एक त्रुटि हुई। कृपया बाद में पुनः प्रयास करें।",
//   "locationLabel": "स्थान",
//   "addNewCard": "नई कार्ड जोड़ें",
//   "orderID": "आदेश ID",
//   "orderDate": "आदेश तिथि",
//   "productPrice": "उत्पाद मूल्य",
//   "voucher": "वाउचर",
//   "serviceFee": "सेवा शुल्क",
//   "termsAndConditions": "पे करने पर, आप Multi.Me के ",
//   "termsAndConditionsLink": "नियम और शर्तों से सहमत होते हैं।",
//   "confirmOrder": "आदेश की पुष्टि करें",
//   "dateText": "dd/MM/yyyy",
//   "personalInformation": "व्यक्तिगत जानकारी",
//   "fullName": "पूरा नाम",
//   "phone": "फ़ोन",
//   "addressLabel": "पता",
//   "addressHint": "आपका पता",
//   "specificAddressLabel": "विशेष पता",
//   "specificAddressHint": "विशेष पता",
//   "mapPlaceholder": "नक्शा प्लेसहोल्डर",
//   "setDefaultCheckboxLabel": "डिफ़ॉल्ट पता के रूप में सेट करें",
//   "saveChangeButton": "बदलाव सहेजें",
//   "liked": "पसंद किया",
//   "marketplace": "बाज़ार",
//   "newText": "नया",
//   "mostView": "सबसे अधिक देखा गया",
//   "mostViewed": "सबसे अधिक देखा गया",
//   "likeNew": "नए जैसा",
//   "adsPlaceholder": "विज्ञापन प्लेसहोल्डर",
//   "productName": "उत्पाद का नाम",
//   "shopAddress": "दुकान का पता",
//   "postDate": "पोस्ट तिथि",
//   "price": "कीमत",
//   "orderPage": "आदेश पृष्ठ",
//   "messagePage": "संदेश पृष्ठ",
//   "cancelOrder": "मेरा आदेश रद्द करें",
//   "payment": "भुगतान",
//   "totalPayment": "कुल भुगतान",
//   "subtotal": "उपकुल",
//   "orderShipped": "आपका आदेश शिपिंग के लिए भेजा गया",
//   "orderPlaced": "आपने आदेश दिया है",
//   "deliveredTo": "प्राप्तकर्ता को भेजा गया",
//   "sentFrom": "भेजा गया",
//   "awaitingApproval": "स्वीकृति का इंतजार है",
//   "productDetails": "उत्पाद विवरण",
//   "quantity": "मात्रा",
//   "sellerInfo": "विक्रेता जानकारी",
//   "contactSeller": "विक्रेता से संपर्क करें",
//   "shippingInfo": "शिपिंग जानकारी",
//   "orderFailed": "आदेश विफल",
//   "orderFailureMessage": "आपके आदेश में कुछ गड़बड़ है।",
//   "orderCheckMessage": "कृपया वापस जाएं और अपना आदेश फिर से ध्यान से जांचें।",
//   "returnOrder": "मेरा आदेश वापस करें",
//   "orderConfirmed": "आदेश पुष्टि हुआ",
//   "thankYouMessage": "आपके आदेश के लिए धन्यवाद। आपको जल्द ही ईमेल द्वारा पुष्टि प्राप्त होगी।",
//   "checkOrderStatus": "अपने आदेश की स्थिति अपने कार्ट में देखें।",
//   "continueShopping": "खरीदारी जारी रखें",
//   "addNewAddress": "नया पता जोड़ें",
//   "postProduct": "उत्पाद पोस्ट करें",
//   "productHintText": "उत्पाद का नाम (कम से कम 50 और अधिकतम 150 शब्द)",
//   "statusText": "स्थिति",
//   "productFeatures": "उत्पाद की प्रमुख विशेषताएँ, इसमें क्या शामिल है, इसका उपयोग कितने समय से हो रहा है...",
//   "statusProduct": "अपने उत्पाद की स्थिति चुनें",
//   "backText": "वापस",
//   "buyNow": "अब खरीदें",
//   "reportSeller": "विक्रेता की रिपोर्ट करें",
//   "noRecommendSeller": "इस विक्रेता की सिफारिश न करें",
//   "noMoreProfile": "कोई और प्रोफ़ाइल नहीं!",
//   "selectPostsFrom": "पोस्ट चुनें",
//   "offersFrom": "Multi.Me से ऑफ़र",
//   "payFee": "शुल्क का भुगतान करें",
//   "apply": "आवेदन करें",
//   "freePosting": "मुफ्त पोस्टिंग",
//   "adType": "प्रकार: सामान्य विज्ञापन\nपहले व्यक्तिगत विज्ञापनों/महीने के लिए लागू होता है",
//   "upgradeToPro": "प्रो में अपग्रेड करें",
//   "postFreeAds": "मुफ्त विज्ञापन पोस्ट करें",
//   "adTypeAds": "प्रकार: सामान्य - स्थायी विज्ञापन",
//   "appliesToAllPosts": "सभी पोस्ट पर लागू होता है",
//   "multiMe": "Multi.Me",
//   "doesNot": "यह अनुमति नहीं देता है",
//   "allowDepositPrivate": "सुरक्षित नहीं है: जमा/निजी ट्रांसफर/ऑफ-फ्लैटफॉर्म लेन-देन/साझेदारों की भर्ती/मुफ्त उपहार देना/संपर्क जानकारी प्रदान करना या विक्रेता के अनुरोध पर आदेश रद्द करना।",
//   "pleaseOnlyBuySell": "कृपया केवल Multi.Me पर खरीदें/बेचें, ताकि आप धोखाधड़ी से बच सकें। Multi.Me आपकी चैट हिस्ट्री का संग्रहण और उपयोग करेगा Multi.Me की गोपनीयता नीति के अनुसार।",
//   "gotUnusedItem": "क्या आपके पास अप्रयुक्त वस्तुएं हैं?",
//   "turnThemIntoCash": "उन्हें नकद में बदलें!",
//   "postForSale": "बिक्री के लिए पोस्ट करें",
//   "itsTotally": "यह पूरी तरह से",
//   "safe": "सुरक्षित है",
//   "toPurchaseAtMultiMe": "Multi.Me पर खरीदने के लिए। आपका पैसा केवल विक्रेता को भेजा जाएगा जब आप उत्पाद प्राप्त करने की पुष्टि करेंगे; अन्यथा, यह आपको वापस कर दिया जाएगा।",
//   "aboutThisItem": "इस आइटम के बारे में",
//   "condition": "स्थिति",
//   "type": "प्रकार",
//   "brand": "ब्रांड",
//   "show": "दिखाएं",
//   "hide": "छिपाएं",
//   "estDelivery": "अनुमानित डिलीवरी: ",
//   "safePurchase": "Multi.Me पर खरीदारी करना पूरी तरह से सुरक्षित है। आपका पैसा विक्रेता को केवल पुष्टि के बाद भेजा जाएगा।",
//   "chargeRateInfo": "खरीदारी > $1000 पर 3% चार्ज रेट होगा\nखरीदारी < $999 पर 5% चार्ज रेट होगा",
//   "thereWill": "यह होगा",
//   "chargeRate": "चार्ज दर खरीदारी के लिए",
//   "defaultText": "डिफ़ॉल्ट",
//   "createDate": "निर्माण तिथि",
//   "shippingPrice": "शिपिंग मूल्य शामिल है",
//   "contactText": "कृपया मुझसे संपर्क करें यदि आपको कोई सवाल है, मूल्य पर चर्चा करना चाहते हैं या आप एक बेहतर प्रस्ताव चाहते हैं।",
//   "contactMe": "मुझसे संपर्क करें",
//   "moreLike": "इसके जैसे और",
//   "viewAll": "सभी देखें",
//   "postMe": "पोस्ट करें मुझे",
//   "postNews": "समाचार पोस्ट करें",
//   "newsTitle": "समाचार का शीर्षक",
//   "category": "श्रेणी",
//   "country": "देश",
//   "articleSource": "लेख का स्रोत",
//   "content": "सामग्री",
//   "copyContent": "यहां से लेख की सामग्री कॉपी करें",
//   "addCoverPhoto": "+ \nकवर फोटो जोड़ें",
//   "selectCategory": "श्रेणी चुनें",
//   "reviewPost": "अपनी पोस्ट की समीक्षा करें",
//   "tabAll": "सभी",
//   "tabSports": "खेल",
//   "tabPolitics": "राजनीति",
//   "tabBusiness": "व्यवसाय",
//   "tabHealth": "स्वास्थ्य",
//   "tabTravel": "यात्रा",
//   "tabScience": "विज्ञान",
//   "readAndShare": "दुनिया भर के समाचार पढ़ें और साझा करें",
//   "createCommunity": "🙌 हम समाचार नहीं बनाते, हम एक समुदाय बनाते हैं जहां लोग समाचार साझा करते हैं और चर्चा करते हैं, एक-दूसरे की मदद करते हैं, सोचने की क्षमता और बहुआयामी समझ को विकसित करते हैं",
//   "hotNew": "गरम समाचार",
//   "history": "इतिहास",
//   "createPost": "पोस्ट बनाएं",
//   "latest": "नवीनतम",
//   "about": "के बारे में",
//   "skill": "कौशल",
//   "education": "शिक्षा",
//   "experience": "अनुभव",
//   "notPost": "अभी तक पोस्ट नहीं",
//   "subNewPost": "कोई नया पोस्ट करने के लिए प्रयास करें...",
//   "notReview": "अभी तक कोई समीक्षा नहीं",
//   "thanksStrongBody": "आपका आदेश StrongBody समुदाय के लिए बहुत मायने रखता है। यहां पहली समीक्षा लिखने के लिए अब आदेश दें।",
//   "productAndService": "उत्पाद और सेवाओं का अन्वेषण करें",
//   "newService": "नई सेवा",
//   "notService": "अभी तक कोई सेवा नहीं",
//   "sellerCreateService": "विक्रेता ने अभी तक कोई सेवा नहीं बनाई है। यदि उन्होंने कुछ बेचा होता, तो यह यहां दिखाई देता।",
//   "noProduct": "अभी तक कोई उत्पाद नहीं",
//   "noProductDescription": "विक्रेता ने अभी तक कोई उत्पाद नहीं बेचा है। यदि वे भविष्य में ऐसा करते हैं, तो उत्पाद यहां दिखाई देंगे।",
//   "activeShop": "सक्रिय StrongBodyShop यहां →",
//   "add": "जोड़ें",
//   "fullTime": "पूर्णकालिक",
//   "toYear": "साल तक",
//   "companyBusiness": "कंपनी/व्यवसाय का नाम",
//   "fromYear": "साल से",
//   "positionCompany": "कंपनी में पद",
//   "addressCountry": "पता/देश",
//   "addSkill": "कौशल जोड़ें (जैसे: वॉयस टैलेंट)",
//   "year": "साल",
//   "uploadImage": "अधिक प्रभाव और बिक्री के अवसरों के लिए 430x190 px छवियाँ अपलोड करें।",
//   "displayNameLabel": "प्रदर्शन नाम",
//   "displayNameHint": "कृपया अपने खाते का नाम दर्ज करें",
//   "yourProfession": "आपका पेशा",
//   "yourProfessionHint": "उदाहरण 1: बोर्ड-प्रमाणित डर्मेटोलॉजिस्ट जो कॉस्मेटिक प्रक्रियाओं और त्वचा कैंसर उपचार में विशेषज्ञ हैं। उदाहरण 2: प्रमाणित पर्सनल ट्रेनर जो सभी फिटनेस स्तरों के लिए वजन घटाने और ताकत प्रशिक्षण पर केंद्रित हैं।",
//   "professionalLabel": "आपका पेशेवर शीर्षक",
//   "professionalHint": "जैसे: पर्सनल ट्रेनर / थेरेपिस्ट / डेंटिस्ट",
//   "contentProfile": "कृपया अपना परिचय दें। इसमें शामिल करने पर विचार करें:\n- इस क्षेत्र में आपका अनुभव\n- प्रमुख उपलब्धियां या प्रमाणपत्र\n- रोगी देखभाल या कार्य विधि के लिए आपकी दृष्टिकोण\n- कोई भी विशेषज्ञताएँ या क्षेत्र",
//   "addLanguage": "भाषा जोड़ें",
//   "levelLanguage": "भाषा स्तर",
//   "englishText": "अंग्रेज़ी",
//   "frenchText": "फ्रांसीसी",
//   "basic": "बुनियादी",
//   "conversational": "वार्तालाप",
//   "completionRate": "पूरण दर",
//   "settingAccount": "खाता सेटिंग",
//   "myAccount": "मेरा खाता",
//   "password": "पासवर्ड",
//   "verifyAccount": "खाता सत्यापित करें",
//   "notification": "सूचना",
//   "privacy": "गोपनीयता",
//   "syncSetting": "सिंक सेटिंग",
//   "deleteAccount": "खाता हटाएं",
//   "turnOnNotificationsForAllModes": "सभी मोड के लिए सूचनाएं चालू करें",
//   "socialMode": "सोशल मोड",
//   "manageYourConnection": "अपना कनेक्शन प्रबंधित करें",
//   "businessMode": "व्यवसाय मोड",
//   "connectToPartnerChances": "साझेदार, अवसरों से जुड़ें",
//   "newsMode": "समाचार मोड",
//   "readNews": "समाचार पढ़ें",
//   "marketPlace": "बाजार",
//   "shoppingProductsAndService": "उत्पादों और सेवाओं की खरीदारी",
//   "datingMode": "डेटिंग मोड",
//   "findRealPartnerForYou": "आपके लिए वास्तविक साथी ढूंढें",
//   "strongBodyAi": "StrongBody.ai",
//   "darkMode": "अंधेरे मोड",
//   "selectMode": "आंखों की सुरक्षा मोड का चयन करें",
//   "yourInvisible": "आपकी गतिविधि अदृश्य होगी",
//   "settingMode": "मोड सेटिंग",
//   "policy": "नीति",
//   "yourActivityInvisible": "आपकी गतिविधि अदृश्य होगी",
//   "helpCenterTitle": "सहायता केंद्र",
//   "helpCenterDescription": "जो कुछ भी आपको जानने की आवश्यकता है",
//   "shoppingCartTitle": "शॉपिंग कार्ट",
//   "shoppingCartDescription": "मार्केटप्लेस, नेटवर्क में अपने बचाए गए आदेशों को प्रबंधित करें।",
//   "shakeToExploreTitle": "निकटवर्ती का अन्वेषण करने के लिए हिलाएं",
//   "shakeToExploreDescription": "निकटवर्ती का अन्वेषण करने के लिए स्विच करें",
//   "balanceTitle": "संतुलन",
//   "balanceDescription": "आप अपने खाते में पैसे निकाल सकते हैं",
//   "sellerModeTitle": "विक्रेता मोड",
//   "sellerModeDescription": "सेवा बेचने के लिए स्विच करें",
//   "logOut": "लॉग आउट",
//   "textAndCall": "संदेश और कॉल",
//   "showViewedStatus": "'देखा' स्थिति दिखाएं",
//   "allowMessaging": "संदेश भेजने की अनुमति दें",
//   "allowCall": "कॉल करने की अनुमति दें",
//   "diary": "डायरी",
//   "allowViewingAndCommenting": "देखने और टिप्पणी करने की अनुमति दें",
//   "friendSource": "दोस्त का स्रोत",
//   "suggestFriendsFromContacts": "संपर्कों से मित्र सुझाव दें",
//   "addContactsToMultiMe": "जब दोनों ने एक-दूसरे के नंबर अपने उपकरणों में सहेजे हों, तब MultiMe में संपर्क जोड़ें",
//   "on": "चालू",
//   "everybody": "सभी",
//   "friend": "मित्र",
//   "phoneNumberTitle": "आपका फोन नंबर क्या है?",
//   "phoneNumberDescription": "यह जानकारी हमें आपके खाते को सत्यापित करने में मदद करती है ताकि यह और अधिक सुरक्षित हो सके।",
//   "mobileNumber": "मोबाइल नंबर",
//   "enterMobileNumber": "अपना मोबाइल नंबर दर्ज करें",
//   "forgotPassword": "पासवर्ड भूल गए",
//   "emailLabel": "ईमेल",
//   "emailHint": "ईमेल दर्ज करें",
//   "alreadyMember": "पहले से सदस्य हैं?",
//   "signUp": "साइन अप करें",
//   "pickFriendsTitle": "10 बेहतरीन दोस्तों और ग्राहकों का चयन करें",
//   "inviteFriendMessage": "जारी रखने के लिए एक दोस्त को आमंत्रित करें",
//   "searchContactsHint": "अपने संपर्कों की खोज करें",
//   "importContactsTitle": "अपने संपर्कों को आयात करें",
//   "importContactsMessage": "StrongBody आपके पक्ष से मित्रों को कभी भी संदेश नहीं भेजता है",
//   "importButton": "आयात करें",
//   "minPasswordCriteria": "न्यूनतम 8 अक्षर, 1 अपरकेस, 1 लोअरकेस, 1 संख्या",
//   "confirmPasswordHint": "पासवर्ड की पुष्टि करें",
//   "signInTitle": "साइन इन करें",
//   "rememberMe": "मुझे याद रखें",
//   "notAMember": "अभी तक सदस्य नहीं हैं?",
//   "userName": "यूज़र नाम",
//   "publicName": "सार्वजनिक उपयोगकर्ता नाम",
//   "occupation": "पेशे",
//   "enterOccupation": "पेशा दर्ज करें",
//   "termsOfService": "सेवा की शर्तें",
//   "privacyPolicy": "गोपनीयता नीति",
//   "toData": "यह जानने के लिए कि हम आपके व्यक्तिगत डेटा का उपयोग कैसे करते हैं।",
//   "andOur": "और हमें कभी-कभी ईमेल प्राप्त करने के लिए। कृपया हमारी ",
//   "byJoinStrongBody": "StrongBody से जुड़कर, आप StrongBody की ",
//   "verifyAccountTitle": "खाता सत्यापित करने के लिए अपना ईमेल जांचें",
//   "verifyAccountMessage": "हमने आपको पुष्टि करने के लिए एक लिंक भेजा है। कृपया अपना ईमेल जांचें ताकि आप अपनी पंजीकरण प्रक्रिया पूरी कर सकें।",
//   "backToSignIn": "साइन इन पर वापस जाएं",
//   "emailVerifiedTitle": "ईमेल सफलतापूर्वक पहचाना गया",
//   "emailVerifiedMessage": "आपने इस खाते के लिए फोन नंबर सफलतापूर्वक सत्यापित कर लिया है।",
//   "phoneVerifiedTitle": "फोन नंबर सफलतापूर्वक पहचाना गया",
//   "phoneVerifiedMessage": "आपने इस खाते के लिए फोन नंबर सफलतापूर्वक सत्यापित कर लिया है।",
//   "blockUser": "ब्लॉक करें",
//   "from": "से",
//   "imageText": "छवि",
//   "createPostTitle": "पोस्ट बनाएं",
//   "postHintText": "एक पल बनाएं... आपके दिमाग में क्या है?",
//   "postFirstTimeMessage": "👋 MultiMe में आपका स्वागत है! खुद को प्रस्तुत करें और आपके बारे में कुछ दिलचस्प साझा करें या आज आपको यहां क्या लाया है। विश्वास बनाने और समुदाय का विस्तार करने के लिए अपना ज्ञान और कहानियाँ साझा करें।",
//   "postFirstTimeSubTitle": "अपना पहला लेख पोस्ट करें",
//   "strongBody": "StrongBody",
//   "social": "सामाजिक",
//   "record": "रिकॉर्ड",
//   "backgroundColor": "पृष्ठभूमि रंग",
//   "checkIn": "चेक-इन करें",
//   "postDetail": "पोस्ट विवरण",
//   "getStarted": "शुरू करें",
//   "send": "भेजें",
//   "resonate": "गूंजना",
//   "nameProduct": "उत्पाद का नाम",
//   "descriptionOption": "विवरण (वैकल्पिक)",
//   "shareIssue": "इस मुद्दे के बारे में अधिक विवरण साझा करें",
//   "ifImmediately": "अगर आपको पता है कि कोई शारीरिक खतरे में है, तो कृपया तुरंत अपने स्थानीय कानून प्रवर्तन एजेंसी से संपर्क करें।",
//   "postRequest": "पोस्ट अनुरोध",
//   "supportImage": "छवि प्रारूपों को .jpg, .jpeg का समर्थन करता है जिनका आकार ",
//   "fiveMB": "5MB",
//   "uploadPhotoBrowse": "कार्य की एक फोटो अपलोड करें। यह फोटो लिस्टिंग पर दिखाई देगी जिसे लोग ब्राउज़ करेंगे",
//   "uploadPhoto": "फोटो अपलोड करें",
//   "egPartner": "जैसे: अपनी आवश्यकताओं या प्रस्तावों, उद्योग/क्षेत्र, कौशल/अनुभव आवश्यकताओं को बताएं, जो आप एक संभावित उम्मीदवार या साझीदार में देख रहे हैं उसे विवरणित करें",
//   "graphic": "ग्राफिक डिज़ाइनर, 2D एनीमेशन, ....",
//   "headerLogin": "अपने खाते में साइन इन करें",
//   "headerLoginText": "लॉग इन करने के लिए अपना ईमेल और पासवर्ड दर्ज करें",
//   "invalidateEmail": "यह ईमेल पंजीकृत नहीं है।",
//   "invalidatePassword": "यह पासवर्ड अमान्य है।",
//   "cantEmpty": "{label} खाली नहीं हो सकता",
//   "report": "रिपोर्ट",
//   "login": "लॉगिन",
//   "orLoginWith": "या",
//   "dontHaveAnAccount": "क्या आपका खाता नहीं है?",
//   "already": "क्या आप पहले से सदस्य हैं?",
//   "resetPassword": "पासवर्ड रीसेट करें",
//   "resetPasswordText": "अपने खाते से जुड़ी ईमेल दर्ज करें और हम आपको पासवर्ड रीसेट करने के लिए निर्देशों के साथ एक ईमेल भेजेंगे।",
//   "emailAddress": "ईमेल पता",
//   "sendEmail": "निर्देश भेजें",
//   "checkEmail": "अपना ईमेल जांचें",
//   "checkEmailText": "हमने आपके ईमेल पर पासवर्ड रिकवरी निर्देश भेजे हैं।",
//   "openEmail": "ईमेल ऐप खोलें",
//   "createPassword": "नया पासवर्ड बनाएं",
//   "createPasswordText": "आपका नया पासवर्ड पहले उपयोग किए गए पासवर्ड से अलग होना चाहिए।",
//   "confirmPassword": "नया पासवर्ड पुष्टि करें",
//   "confirmPasswordText": "दोनों पासवर्ड मेल खाने चाहिए",
//   "spamEmail": "ईमेल प्राप्त नहीं हुआ? अपने स्पैम फ़िल्टर की जांच करें, या",
//   "spamEmailLink": "कोई अन्य ईमेल पता आजमाएं।",
//   "passwordNotMatch": "पासवर्ड मेल नहीं खाता",
//   "uploadImageBussines": "छवि अपलोड करें",
//   "Whatisyouroccupation?": "आपका व्यवसाय क्या है?",
//   "JobProfile": "कार्य प्रोफ़ाइल",
//   "dayago": "दिन पहले",
//   "weekago": "सप्ताह पहले",
//   "SelectaTimeRange": "एक समय सीमा चुनें",
//   "Eg:SeekingMarketingExpertForTechStartup": "उदाहरण: टेक स्टार्टअप के लिए मार्केटिंग विशेषज्ञ की तलाश",
//   "Field/Industry": "क्षेत्र/उद्योग",
//   "General": "सामान्य",
//   "AvatarImage": "अवतार छवि",
//   "Display_name": "प्रदर्शित नाम",
//   "Occupation": "व्यवसाय",
//   "YouAreAllowedToChange": "आपको केवल एक बार बदलने की अनुमति है।",
//   "AccountDeactivation": "खाता निष्क्रिय करना",
//   "WhatHappensWhenYou": "आपका खाता निष्क्रिय करने पर क्या होता है?",
//   "YourProfileAndGigs": "आपकी प्रोफ़ाइल और गिग्स Strongbody पर अब नहीं दिखेंगे।\nसक्रिय आदेश रद्द कर दिए जाएंगे।\nआप गिग्स को फिर से सक्रिय नहीं कर पाएंगे।"
// };
// static const Map<String,dynamic> es = {
//   "hello": "Hola",
//   "language": "Idioma",
//   "home": "Inicio",
//   "setting": "Ajustes",
//   "note": "Nota",
//   "featured": "Destacado",
//   "seeMore": "Ver Más",
//   "order": "Pedido",
//   "message": "Mensaje",
//   "cancel": "Cancelar",
//   "notInterested": "¿No Interesado?",
//   "swipeLeftNoInterest": "Deslizar a la izquierda significa que no estás interesado.",
//   "askMore": "Preguntar Más",
//   "swipeRightNoInterest": "Deslizar a la derecha significa que quieres chatear con ellos.",
//   "business": "Negocios",
//   "request": "Solicitud",
//   "readMore": "Leer Más",
//   "readLess": "Leer Menos",
//   "whatDoYouWantToDo": "¿Qué quieres hacer?",
//   "postBusinessOpportunity": "Publicar una oportunidad de negocio",
//   "offer": "Oferta",
//   "thisIsAn": "Esto es un",
//   "title": "Título",
//   "graphicDesigner": "Diseñador gráfico, Animación 2D, ....",
//   "businessNeeds": "Necesidades del negocio",
//   "description": "Descripción",
//   "shortDescription": "Ej.: Descripción breve, Principales tareas que realizo, Habilidades y experiencia, Entorno laboral, etc.",
//   "stateYourNeeds": "Ej.: Declara tus necesidades u oferta, Industria/sector, Habilidades/experiencia requerida, Describe lo que buscas en un candidato o socio potencial",
//   "budgetInvestmentUSD": "Presupuesto/Inversión (USD)",
//   "uploadAvatar": "Subir Avatar",
//   "optional": "(Opcional)",
//   "uploadAPhoto": "Sube una foto de trabajo. Esta foto aparecerá en la lista para que otros la vean.",
//   "addMore": "Agregar Más",
//   "postDisplayDuration": "Duración de la publicación",
//   "dayAgo": "día atrás",
//   "age": "Edad",
//   "distance": "Distancia",
//   "skip": "Saltar",
//   "post": "Publicar",
//   "maleText": "Masculino",
//   "preferredGender": "Género Preferido",
//   "femaleText": "Femenino",
//   "otherText": "Otro",
//   "save": "Guardar",
//   "dating": "Citas",
//   "likeYou": "Te Gustó",
//   "matches": "Coincidencias",
//   "editBio": "Editar Biografía",
//   "editSeeking": "Editar Búsqueda",
//   "camera": "Cámara",
//   "gallery": "Galería",
//   "aboutYou": "Sobre Ti",
//   "displayName": "Nombre Visible",
//   "name": "Nombre",
//   "yearOfBirth": "Año de Nacimiento",
//   "youYearOfBirth": "Tu Año de Nacimiento",
//   "yourOccupation": "Tu Ocupación",
//   "shortBio": "Biografía Breve",
//   "yourBio": "Tu Biografía",
//   "addYourPhotos": "Añade tus Fotos",
//   "addAtLeast2": "Añade al menos 2 fotos, puedes cambiarlas más tarde",
//   "continueButton": "Continuar",
//   "likedYou": "Te Han Gustado",
//   "youLikedThem": "Te Gustaron",
//   "theyLikedYou": "Les Gustaste",
//   "reviewThePeople": "Revisa las personas que te gustaron",
//   "today": "Hoy",
//   "reviewThePeople2": "Revisa las personas que les gustaste",
//   "reviewThePeopleYouHaveSwipedLeftOn": "Revisa las personas que deslizaste a la izquierda.",
//   "yesterday": "Ayer",
//   "itsAMatch": "¡Es una coincidencia!",
//   "startAConversationNow": "Comienza una conversación ahora.",
//   "startChatting": "Comienza a chatear",
//   "keepSwiping": "Sigue deslizando",
//   "recent": "Reciente",
//   "whoAreYouSeeking": "¿A quién estás buscando?",
//   "recentlyActive": "Recientemente Activo",
//   "welcomeDatingMode": "Bienvenido al Modo de Citas",
//   "ruleBeYourselfTitle": "Sé Tú Mismo.",
//   "ruleBeYourselfDescription": "Asegúrate de que tus fotos, edad y biografía sean fieles a quien eres",
//   "ruleStaySafeTitle": "Mantente Seguro.",
//   "ruleStaySafeDescription": "No te apresures a compartir información personal.",
//   "ruleStaySafeLinkText": "Cita Segura",
//   "rulePlayItCoolTitle": "Relájate",
//   "rulePlayItCoolDescription": "Respeta a los demás y trátalos como te gustaría ser tratado.",
//   "ruleBeProactiveTitle": "Sé Proactivo.",
//   "ruleBeProactiveDescription": "Siempre reporta un mal comportamiento.",
//   "iAgreeButtonText": "Estoy de Acuerdo",
//   "followHouseRulesText": "Por favor, sigue estas Reglas de la Casa",
//   "addNewCardTitle": "Agregar Nueva Tarjeta",
//   "cardDetailsTitle": "Detalles de la Tarjeta",
//   "cardNumberLabel": "Número de Tarjeta",
//   "cardNumberHint": "0000-0000-0000-0000",
//   "expiryDateLabel": "Fecha de Expiración",
//   "expiryDateHint": "MM/AA",
//   "cvvLabel": "CVV",
//   "cvvHint": "CVV",
//   "cardholderNameLabel": "Nombre del Titular de la Tarjeta",
//   "cardholderNameHint": "Nombre en la tarjeta",
//   "verificationTransaction": "Transacción de Verificación",
//   "verificationTransactionText": " (Se reembolsará a su tarjeta inmediatamente después de la verificación)",
//   "confirmButtonText": "Confirmar",
//   "checkoutTitle": "Finalizar Compra",
//   "warningMessage": "Antes de realizar un pedido, asegúrese de que la dirección sea correcta y coincida con su dirección actual.",
//   "orderDetailsTitle": "Detalles del Pedido",
//   "orderDetailsHint": "Su pedido",
//   "paymentOptionsTitle": "Opciones de Pago",
//   "paymentDetailsTitle": "Detalles de Pago",
//   "totalCost": "Costo Total",
//   "paypal": "Paypal",
//   "visaCard": "Tarjeta Visa",
//   "paymentFailed": "Pago Fallido",
//   "paymentFailedMessage": "Hubo un error al procesar su pago. Por favor, inténtelo de nuevo más tarde.",
//   "locationLabel": "Ubicación",
//   "addNewCard": "Agregar Nueva Tarjeta",
//   "orderID": "ID del Pedido",
//   "orderDate": "Fecha del Pedido",
//   "productPrice": "Precio del Producto",
//   "voucher": "Cupón",
//   "serviceFee": "Costo del Servicio",
//   "termsAndConditions": "Al presionar Pagar, usted acepta los ",
//   "termsAndConditionsLink": "términos y condiciones de Multi.Me.",
//   "confirmOrder": "Confirmar Pedido",
//   "dateText": "dd/MM/aaaa",
//   "personalInformation": "Información Personal",
//   "fullName": "Nombre Completo",
//   "phone": "Teléfono",
//   "addressLabel": "Dirección",
//   "addressHint": "Su dirección",
//   "specificAddressLabel": "Dirección Específica",
//   "specificAddressHint": "Dirección específica",
//   "mapPlaceholder": "Marcador del Mapa",
//   "setDefaultCheckboxLabel": "Establecer como dirección predeterminada",
//   "saveChangeButton": "Guardar Cambios",
//   "liked": "Me Gustó",
//   "marketplace": "Mercado",
//   "newText": "Nuevo",
//   "mostView": "Más Visto",
//   "mostViewed": "Más Vistos",
//   "likeNew": "Como Nuevo",
//   "adsPlaceholder": "Marcador de Anuncios",
//   "productName": "Nombre del Producto",
//   "shopAddress": "Dirección de la Tienda",
//   "postDate": "Fecha de Publicación",
//   "price": "Precio",
//   "orderPage": "Página de Pedido",
//   "messagePage": "Página de Mensajes",
//   "cancelOrder": "Cancelar mi pedido",
//   "payment": "Pago",
//   "totalPayment": "Pago Total",
//   "subtotal": "Subtotal",
//   "orderShipped": "Su pedido ha comenzado a ser enviado",
//   "orderPlaced": "Usted realizó el pedido",
//   "deliveredTo": "Entregado a",
//   "sentFrom": "Enviado desde",
//   "awaitingApproval": "Esperando aprobación",
//   "productDetails": "Detalles del Producto",
//   "quantity": "Cantidad",
//   "sellerInfo": "Información del Vendedor",
//   "contactSeller": "Contactar al Vendedor",
//   "shippingInfo": "Información de Envío",
//   "orderFailed": "Pedido Fallido",
//   "orderFailureMessage": "Algo salió mal con su pedido.",
//   "orderCheckMessage": "Por favor, regrese y revise su pedido nuevamente.",
//   "returnOrder": "Devolver mi pedido",
//   "orderConfirmed": "Pedido Confirmado",
//   "thankYouMessage": "Gracias por su pedido. Recibirá una confirmación por correo electrónico en breve.",
//   "checkOrderStatus": "Revise el estado de su pedido en Su Carrito.",
//   "continueShopping": "Seguir Comprando",
//   "addNewAddress": "Agregar Nueva Dirección",
//   "postProduct": "Publicar Producto",
//   "productHintText": "Nombre del producto (mínimo 50 y máximo 150 palabras)",
//   "statusText": "Estado",
//   "productFeatures": "Características destacadas del producto, qué incluye, cuánto tiempo se ha usado...",
//   "statusProduct": "Elija el estado de su producto",
//   "backText": "Atrás",
//   "buyNow": "Comprar Ahora",
//   "reportSeller": "Denunciar al Vendedor",
//   "noRecommendSeller": "No recomendar a este vendedor",
//   "noMoreProfile": "¡No hay más perfiles!",
//   "selectPostsFrom": "Seleccionar publicaciones de",
//   "offersFrom": "Ofertas de Multi.Me",
//   "payFee": "Pagar tarifa",
//   "apply": "Aplicar",
//   "freePosting": "Publicación gratuita",
//   "adType": "Tipo: Anuncio regular\nSe aplica a los primeros anuncios personales/mes",
//   "upgradeToPro": "Actualizar a Pro",
//   "postFreeAds": "Publicar anuncios gratis",
//   "adTypeAds": "Tipo: Regular - Anuncios permanentes",
//   "appliesToAllPosts": "Se aplica a todas las publicaciones",
//   "multiMe": "Multi.Me",
//   "doesNot": "NO",
//   "allowDepositPrivate": "permite: Depósitos/Transferencias privadas/Transacciones fuera de la plataforma/Contratar colaboradores/Regalar obsequios/Proporcionar información de contacto o cancelar pedidos a solicitud del vendedor.",
//   "pleaseOnlyBuySell": "Por favor, solo compre/venda en Multi.Me para evitar estafas. Multi.Me recopilará y utilizará el historial de chat según la Política de Privacidad de Multi.Me.",
//   "gotUnusedItem": "¿Tienes artículos sin usar?",
//   "turnThemIntoCash": "¡Conviértelos en efectivo!",
//   "postForSale": "Publicar en venta",
//   "itsTotally": "Es completamente",
//   "safe": "seguro",
//   "toPurchaseAtMultiMe": "comprar en Multi.Me. Su dinero solo se enviará al vendedor después de confirmar la recepción del producto; de lo contrario, se le devolverá.",
//   "aboutThisItem": "Sobre este artículo",
//   "condition": "Condición",
//   "type": "Tipo",
//   "brand": "Marca",
//   "show": "Mostrar",
//   "hide": "Ocultar",
//   "estDelivery": "Entrega Estimada: ",
//   "safePurchase": "Es completamente seguro comprar en Multi.Me. Su dinero se enviará al vendedor solo después de la confirmación.",
//   "chargeRateInfo": "Habrá una tasa del 3% para compras > $1000\nHabrá una tasa del 5% para compras < $999",
//   "thereWill": "Habrá una",
//   "chargeRate": "tasa de cargo para compras",
//   "defaultText": "Predeterminado",
//   "createDate": "Fecha de Creación",
//   "shippingPrice": "Incluye precio de envío",
//   "contactText": "Por favor contácteme si tiene alguna pregunta, desea discutir el precio o busca una mejor oferta.",
//   "contactMe": "Contáctame",
//   "moreLike": "Más como esto",
//   "viewAll": "Ver todo",
//   "postMe": "Publícame",
//   "postNews": "Publicar Noticias",
//   "newsTitle": "Título de la Noticia",
//   "category": "Categoría",
//   "country": "País",
//   "articleSource": "Fuente del Artículo",
//   "content": "Contenido",
//   "copyContent": "Copiar contenido del artículo aquí",
//   "addCoverPhoto": "+ \nAgregar Foto de Portada",
//   "selectCategory": "Seleccionar Categoría",
//   "reviewPost": "Revisar tus publicaciones",
//   "tabAll": "Todo",
//   "tabSports": "Deportes",
//   "tabPolitics": "Política",
//   "tabBusiness": "Negocios",
//   "tabHealth": "Salud",
//   "tabTravel": "Viajes",
//   "tabScience": "Ciencia",
//   "readAndShare": "Leer y compartir noticias alrededor del mundo",
//   "createCommunity": "🙌 No creamos noticias, creamos una comunidad donde las personas comparten y discuten noticias, ayudándose mutuamente a desarrollar pensamiento crítico y comprensión multifacética.",
//   "hotNew": "Noticias Destacadas",
//   "history": "Historial",
//   "createPost": "Crear Publicación",
//   "latest": "Últimas",
//   "about": "Acerca de",
//   "skill": "Habilidades",
//   "education": "Educación",
//   "experience": "Experiencia",
//   "notPost": "Aún no hay publicaciones",
//   "subNewPost": "Prueba suscribiéndote a una nueva publicación tuya...",
//   "notReview": "Aún no hay reseñas",
//   "thanksStrongBody": "Tu pedido significa mucho para la comunidad de StrongBody. Haz tu pedido ahora para escribir la primera reseña aquí.",
//   "productAndService": "Explorar productos y servicios",
//   "newService": "Nuevo Servicio",
//   "notService": "Aún no hay servicios",
//   "sellerCreateService": "El vendedor aún no ha creado ningún servicio. Si vende algo, aparecerá aquí.",
//   "noProduct": "Aún no hay productos",
//   "noProductDescription": "El vendedor aún no ha vendido ningún producto. Si lo hace en el futuro, los productos aparecerán aquí.",
//   "activeShop": "Activar StrongBodyShop aquí →",
//   "add": "Agregar",
//   "fullTime": "Tiempo Completo",
//   "toYear": "Hasta el año",
//   "companyBusiness": "Nombre de la empresa / negocio",
//   "fromYear": "Desde el año",
//   "positionCompany": "Posición en la empresa",
//   "addressCountry": "Dirección / País",
//   "addSkill": "Agregar habilidad (por ejemplo: Talento vocal)",
//   "year": "Año",
//   "uploadImage": "Sube imágenes de 430x190 px para más impresiones y oportunidades de ventas.",
//   "displayNameLabel": "Nombre para Mostrar",
//   "displayNameHint": "Por favor, nombra tu cuenta",
//   "yourProfession": "Tu Profesión",
//   "yourProfessionHint": "Ejemplo 1: Dermatólogo certificado especializado en procedimientos cosméticos y tratamientos contra el cáncer de piel. Ejemplo 2: Entrenador personal certificado enfocado en pérdida de peso y entrenamiento de fuerza para todos los niveles de condición física.",
//   "professionalLabel": "Tu Título Profesional",
//   "professionalHint": "Ej: Entrenador Personal / Terapeuta / Dentista",
//   "contentProfile": "Por favor, preséntate. Considera incluir:\n- Tu experiencia en el campo\n- Logros o certificaciones clave\n- Tu enfoque en la atención al cliente o metodología de trabajo\n- Cualquier especialización o área de experiencia",
//   "addLanguage": "Agregar Idioma",
//   "levelLanguage": "Nivel de Idioma",
//   "englishText": "Inglés",
//   "frenchText": "Francés",
//   "basic": "Básico",
//   "conversational": "Conversacional",
//   "completionRate": "Tasa de Finalización",
//   "settingAccount": "Configurar Cuenta",
//   "myAccount": "Mi cuenta",
//   "password": "Contraseña",
//   "verifyAccount": "Verificar cuenta",
//   "notification": "Notificaciones",
//   "privacy": "Privacidad",
//   "syncSetting": "Configurar Sincronización",
//   "deleteAccount": "Eliminar cuenta",
//   "turnOnNotificationsForAllModes": "Activar notificaciones para todos los modos",
//   "socialMode": "Modo Social",
//   "manageYourConnection": "Gestiona tu conexión",
//   "businessMode": "Modo Negocios",
//   "connectToPartnerChances": "Conéctate con socios, oportunidades",
//   "newsMode": "Modo Noticias",
//   "readNews": "Leer noticias",
//   "marketPlace": "Mercado",
//   "shoppingProductsAndService": "Comprar productos y servicios",
//   "datingMode": "Modo Citas",
//   "findRealPartnerForYou": "Encuentra la pareja ideal para ti",
//   "strongBodyAi": "StrongBody.ai",
//   "darkMode": "Modo oscuro",
//   "selectMode": "Seleccionar modo de protección ocular",
//   "yourInvisible": "Tu actividad será invisible",
//   "settingMode": "Modo Configuración",
//   "policy": "Política",
//   "yourActivityInvisible": "Tu actividad será invisible",
//   "helpCenterTitle": "Centro de Ayuda",
//   "helpCenterDescription": "Todo lo que necesitas saber",
//   "shoppingCartTitle": "Carrito de Compras",
//   "shoppingCartDescription": "Gestiona tus pedidos guardados en Mercado, Red.",
//   "shakeToExploreTitle": "Agita para explorar cerca",
//   "shakeToExploreDescription": "Cambiar a agitar para explorar",
//   "balanceTitle": "Saldo",
//   "balanceDescription": "Puedes retirar dinero a tu propia cuenta",
//   "sellerModeTitle": "Modo Vendedor",
//   "sellerModeDescription": "Cambiar a venta de servicios",
//   "logOut": "Cerrar Sesión",
//   "textAndCall": "Texto y Llamada",
//   "showViewedStatus": "Mostrar el estado 'Visto'",
//   "allowMessaging": "Permitir mensajería",
//   "allowCall": "Permitir llamadas",
//   "diary": "Diario",
//   "allowViewingAndCommenting": "Permitir visualización y comentarios",
//   "friendSource": "Fuente de Amigos",
//   "suggestFriendsFromContacts": "Sugerir amigos de los contactos",
//   "addContactsToMultiMe": "Agregar contactos a MultiMe cuando ambos tengan los números guardados en sus dispositivos",
//   "on": "Activado",
//   "everybody": "Todos",
//   "friend": "Amigo",
//   "phoneNumberTitle": "¿Cuál es tu \nnúmero de teléfono?",
//   "phoneNumberDescription": "Esta información nos ayuda a verificar tu cuenta para \nhacerla más segura.",
//   "mobileNumber": "Número de móvil",
//   "enterMobileNumber": "Ingresa tu número de móvil",
//   "forgotPassword": "¿Olvidaste tu contraseña?",
//   "emailLabel": "Correo electrónico",
//   "emailHint": "Ingresa correo electrónico",
//   "alreadyMember": "¿Ya eres miembro?",
//   "signUp": "Regístrate",
//   "pickFriendsTitle": "Elige a tus 10 mejores amigos y clientes",
//   "inviteFriendMessage": "Invita a un amigo para continuar",
//   "searchContactsHint": "Busca en tus contactos",
//   "importContactsTitle": "Importa tus contactos",
//   "importContactsMessage": "StrongBody nunca envía mensajes a tus amigos en tu nombre",
//   "importButton": "Importar",
//   "minPasswordCriteria": "Mínimo 8 caracteres, 1 mayúscula, 1 minúscula, 1 número",
//   "confirmPasswordHint": "Confirmar contraseña",
//   "signInTitle": "Iniciar sesión",
//   "rememberMe": "Recuérdame",
//   "notAMember": "¿No eres miembro aún?",
//   "userName": "Nombre de usuario",
//   "publicName": "Nombre público",
//   "occupation": "Ocupación",
//   "enterOccupation": "Ingresa tu ocupación",
//   "termsOfService": "Términos de Servicio",
//   "privacyPolicy": "Política de Privacidad",
//   "toData": " para aprender cómo usamos tus datos personales.",
//   "andOur": " y para recibir ocasionalmente correos electrónicos de nosotros. Por favor lee nuestro ",
//   "byJoinStrongBody": "Al unirte, aceptas los ",
//   "verifyAccountTitle": "Revisa tu correo electrónico para verificar tu cuenta",
//   "verifyAccountMessage": "Te hemos enviado un enlace para confirmar. Por favor revisa tu correo electrónico para completar tu registro.",
//   "backToSignIn": "Volver a Iniciar Sesión",
//   "emailVerifiedTitle": "Correo electrónico verificado con éxito",
//   "emailVerifiedMessage": "Has verificado con éxito el correo electrónico para esta cuenta.",
//   "phoneVerifiedTitle": "Número de teléfono verificado con éxito",
//   "phoneVerifiedMessage": "Has verificado con éxito el número de teléfono para esta cuenta.",
//   "blockUser": "Bloquear",
//   "from": "De",
//   "imageText": "Imagen",
//   "createPostTitle": "Crear Publicación",
//   "postHintText": "Crea un momento... ¿Qué tienes en mente?",
//   "postFirstTimeMessage": "👋 ¡Bienvenido a MultiMe! Preséntate y comparte algo interesante sobre ti o por qué estás aquí hoy. Comparte tu conocimiento e historias para generar confianza y ampliar la comunidad.",
//   "postFirstTimeSubTitle": "Publica tu primer artículo",
//   "strongBody": "StrongBody",
//   "social": "Social",
//   "record": "Grabar",
//   "backgroundColor": "Color de Fondo",
//   "checkIn": "Registrarse",
//   "postDetail": "Detalle de la Publicación",
//   "getStarted": "Comenzar",
//   "send": "Enviar",
//   "resonate": "Resonar",
//   "nameProduct": "Nombre del Producto",
//   "descriptionOption": "Descripción (Opcional)",
//   "shareIssue": "Comparte más detalles sobre este problema",
//   "ifImmediately": "Si sabes que alguien está en peligro físico, por favor contacta a las autoridades locales de inmediato.",
//   "postRequest": "Solicitud de Publicación",
//   "supportImage": "Admite formatos de imagen .jpg, .jpeg con un tamaño inferior a",
//   "fiveMB": "5 MB",
//   "uploadPhotoBrowse": "Sube una foto del trabajo. Esta foto aparecerá en los listados para que las personas puedan explorarlos",
//   "uploadPhoto": "Subir Foto",
//   "egPartner": "Por ejemplo: Indica tus necesidades u ofertas, industria/campo, habilidades/experiencia requerida, describe qué buscas en un candidato o socio potencial",
//   "graphic": "Diseñador Gráfico, Animación 2D, ....",
//   "headerLogin": "Inicia sesión en tu cuenta",
//   "headerLoginText": "Ingresa tu correo electrónico y contraseña para iniciar sesión",
//   "invalidateEmail": "Este correo electrónico no está registrado.",
//   "invalidatePassword": "Esta contraseña no es válida.",
//   "cantEmpty": "{label} no puede estar vacío",
//   "report": "Informe",
//   "login": "Iniciar Sesión",
//   "orLoginWith": "O",
//   "dontHaveAnAccount": "¿No tienes una cuenta?",
//   "already": "¿Ya eres miembro?",
//   "resetPassword": "Restablecer contraseña",
//   "resetPasswordText": "Introduce el correo electrónico asociado a tu cuenta y te enviaremos un correo con las instrucciones para restablecer tu contraseña.",
//   "emailAddress": "Dirección de correo electrónico",
//   "sendEmail": "Enviar instrucciones",
//   "checkEmail": "Revisa tu correo electrónico",
//   "checkEmailText": "Hemos enviado las instrucciones para recuperar tu contraseña a tu correo electrónico.",
//   "openEmail": "Abrir la aplicación de correo",
//   "createPassword": "Crear nueva contraseña",
//   "createPasswordText": "Tu nueva contraseña debe ser diferente de las contraseñas anteriores.",
//   "confirmPassword": "Confirmar nueva contraseña",
//   "confirmPasswordText": "Ambas contraseñas deben coincidir",
//   "spamEmail": "¿No recibiste el correo electrónico? Revisa tu filtro de spam, o",
//   "spamEmailLink": "intenta con otra dirección de correo electrónico.",
//   "passwordNotMatch": "La contraseña no coincide",
//   "uploadImageBussines": "Subir imagen",
//   "Whatisyouroccupation?": "¿Cuál es tu ocupación?",
//   "JobProfile": "Perfil laboral",
//   "dayago": "hace un día",
//   "weekago": "hace una semana",
//   "SelectaTimeRange": "Selecciona un rango de tiempo",
//   "Eg:SeekingMarketingExpertForTechStartup": "Ejemplo: Buscando experto en marketing para una startup tecnológica",
//   "Field/Industry": "Campo/Industria",
//   "General": "General",
//   "AvatarImage": "Imagen del avatar",
//   "Display_name": "Nombre para mostrar",
//   "Occupation": "Ocupación",
//   "YouAreAllowedToChange": "Se te permite cambiarlo solo una vez.",
//   "AccountDeactivation": "Desactivación de cuenta",
//   "WhatHappensWhenYou": "¿Qué sucede cuando desactivas tu cuenta?",
//   "YourProfileAndGigs": "Tu perfil y tus gigs ya no se mostrarán en Strongbody.\nLos pedidos activos serán cancelados.\nNo podrás reactivar tus gigs."
// };
// static const Map<String,dynamic> ar = {
//   "hello": "مرحبا",
//   "language": "اللغة",
//   "home": "الصفحة الرئيسية",
//   "setting": "الإعدادات",
//   "note": "ملاحظة",
//   "featured": "مميز",
//   "seeMore": "رؤية المزيد",
//   "order": "طلب",
//   "message": "رسالة",
//   "cancel": "إلغاء",
//   "notInterested": "غير مهتم؟",
//   "swipeLeftNoInterest": "السحب لليسار يعني أنك غير مهتم.",
//   "askMore": "اطلب المزيد",
//   "swipeRightNoInterest": "السحب لليمين يعني أنك ترغب في الدردشة معهم.",
//   "business": "الأعمال",
//   "request": "طلب",
//   "readMore": "اقرأ المزيد",
//   "readLess": "اقرأ أقل",
//   "whatDoYouWantToDo": "ماذا تريد أن تفعل؟",
//   "postBusinessOpportunity": "انشر فرصة عمل",
//   "offer": "عرض",
//   "thisIsAn": "هذا هو",
//   "title": "العنوان",
//   "graphicDesigner": "مصمم جرافيك، الرسوم المتحركة 2D، ....",
//   "businessNeeds": "احتياجات العمل",
//   "description": "الوصف",
//   "shortDescription": "مثال: وصف قصير، المهام الرئيسية التي أقوم بها، المهارات والخبرات، بيئة العمل، إلخ.",
//   "stateYourNeeds": "مثال: اذكر احتياجاتك أو عرضك، القطاع الصناعي/التخصص، المهارات/الخبرة المطلوبة، وصف ما تبحث عنه في شريك أو مرشح محتمل",
//   "budgetInvestmentUSD": "الميزانية/الاستثمار (USD)",
//   "uploadAvatar": "رفع صورة الملف الشخصي",
//   "optional": "(اختياري)",
//   "uploadAPhoto": "ارفع صورة للعمل. ستظهر هذه الصورة في القائمة ليطلع عليها الآخرون.",
//   "addMore": "أضف المزيد",
//   "postDisplayDuration": "مدة عرض المنشور",
//   "dayAgo": "قبل يوم",
//   "age": "العمر",
//   "distance": "المسافة",
//   "skip": "تخطي",
//   "post": "نشر",
//   "maleText": "ذكر",
//   "preferredGender": "الجنس المفضل",
//   "femaleText": "أنثى",
//   "otherText": "آخر",
//   "save": "حفظ",
//   "dating": "مواعدة",
//   "likeYou": "يعجبون بك",
//   "matches": "التطابقات",
//   "editBio": "تعديل السيرة الذاتية",
//   "editSeeking": "تعديل الهدف",
//   "camera": "الكاميرا",
//   "gallery": "المعرض",
//   "aboutYou": "حولك",
//   "displayName": "اسم العرض",
//   "name": "الاسم",
//   "yearOfBirth": "سنة الميلاد",
//   "youYearOfBirth": "سنة ميلادك",
//   "yourOccupation": "وظيفتك",
//   "shortBio": "سيرة ذاتية قصيرة",
//   "yourBio": "سيرتك الذاتية",
//   "addYourPhotos": "أضف صورك",
//   "addAtLeast2": "أضف صورتين على الأقل، يمكنك تغييرها لاحقًا",
//   "continueButton": "استمر",
//   "likedYou": "أعجبوا بك",
//   "youLikedThem": "أعجبوك",
//   "theyLikedYou": "أعجبوا بك",
//   "reviewThePeople": "راجع الأشخاص الذين أعجبت بهم",
//   "today": "اليوم",
//   "reviewThePeople2": "راجع الأشخاص الذين أعجبوا بك",
//   "reviewThePeopleYouHaveSwipedLeftOn": "راجع الأشخاص الذين قمت بالسحب لليسار عليهم.",
//   "yesterday": "الأمس",
//   "itsAMatch": "إنها مطابقة!",
//   "startAConversationNow": "ابدأ محادثة الآن.",
//   "startChatting": "ابدأ الدردشة",
//   "keepSwiping": "استمر في السحب",
//   "recent": "الأحدث",
//   "whoAreYouSeeking": "من تبحث عنه؟",
//   "recentlyActive": "نشط مؤخرًا",
//   "welcomeDatingMode": "مرحبًا بوضع المواعدة",
//   "ruleBeYourselfTitle": "كن على طبيعتك.",
//   "ruleBeYourselfDescription": "تأكد من أن صورك، عمرك، وسيرتك الذاتية تعبر عن حقيقتك.",
//   "ruleStaySafeTitle": "ابق آمنًا.",
//   "ruleStaySafeDescription": "لا تكن متسرعًا في تقديم معلومات شخصية.",
//   "ruleStaySafeLinkText": "واعد بأمان",
//   "rulePlayItCoolTitle": "تصرف بهدوء",
//   "rulePlayItCoolDescription": "احترم الآخرين وعاملهم كما تريد أن يعاملوك.",
//   "ruleBeProactiveTitle": "كن استباقيًا.",
//   "ruleBeProactiveDescription": "أبلغ دائمًا عن السلوك السيئ.",
//   "iAgreeButtonText": "أوافق",
//   "followHouseRulesText": "يرجى اتباع قواعد المنزل",
//   "addNewCardTitle": "إضافة بطاقة جديدة",
//   "cardDetailsTitle": "تفاصيل البطاقة",
//   "cardNumberLabel": "رقم البطاقة",
//   "cardNumberHint": "0000-0000-0000-0000",
//   "expiryDateLabel": "تاريخ الانتهاء",
//   "expiryDateHint": "MM/YY",
//   "cvvLabel": "CVV",
//   "cvvHint": "CVV",
//   "cardholderNameLabel": "اسم صاحب البطاقة",
//   "cardholderNameHint": "الاسم على البطاقة",
//   "verificationTransaction": "معاملة التحقق",
//   "verificationTransactionText": "(سيتم استردادها إلى بطاقتك فورًا بعد التحقق)",
//   "confirmButtonText": "تأكيد",
//   "checkoutTitle": "الدفع",
//   "warningMessage": "قبل تقديم الطلب، تأكد من أن العنوان صحيح ويتطابق مع عنوانك الحالي.",
//   "orderDetailsTitle": "تفاصيل الطلب",
//   "orderDetailsHint": "طلبك",
//   "paymentOptionsTitle": "خيارات الدفع",
//   "paymentDetailsTitle": "تفاصيل الدفع",
//   "totalCost": "التكلفة الإجمالية",
//   "paypal": "باي بال",
//   "visaCard": "بطاقة فيزا",
//   "paymentFailed": "فشل الدفع",
//   "paymentFailedMessage": "حدث خطأ أثناء معالجة الدفع الخاص بك. يرجى المحاولة مرة أخرى لاحقًا.",
//   "locationLabel": "الموقع",
//   "addNewCard": "إضافة بطاقة جديدة",
//   "orderID": "رقم الطلب",
//   "orderDate": "تاريخ الطلب",
//   "productPrice": "سعر المنتج",
//   "voucher": "قسيمة",
//   "serviceFee": "رسوم الخدمة",
//   "termsAndConditions": "بالضغط على الدفع، فإنك توافق على ",
//   "termsAndConditionsLink": "الشروط والأحكام الخاصة بـ Multi.Me.",
//   "confirmOrder": "تأكيد الطلب",
//   "dateText": "dd/MM/yyyy",
//   "personalInformation": "المعلومات الشخصية",
//   "fullName": "الاسم الكامل",
//   "phone": "الهاتف",
//   "addressLabel": "العنوان",
//   "addressHint": "عنوانك",
//   "specificAddressLabel": "عنوان محدد",
//   "specificAddressHint": "عنوان محدد",
//   "mapPlaceholder": "خريطة المكان",
//   "setDefaultCheckboxLabel": "تعيين كعنوان افتراضي",
//   "saveChangeButton": "حفظ التغيير",
//   "liked": "أعجب",
//   "marketplace": "السوق",
//   "newText": "جديد",
//   "mostView": "الأكثر مشاهدة",
//   "mostViewed": "الأكثر مشاهدة",
//   "likeNew": "جديد مثل الجديد",
//   "adsPlaceholder": "إعلانات مكانية",
//   "productName": "اسم المنتج",
//   "shopAddress": "عنوان المتجر",
//   "postDate": "تاريخ النشر",
//   "price": "السعر",
//   "orderPage": "صفحة الطلب",
//   "messagePage": "صفحة الرسائل",
//   "cancelOrder": "إلغاء طلبي",
//   "payment": "الدفع",
//   "totalPayment": "إجمالي الدفع",
//   "subtotal": "المجموع الفرعي",
//   "orderShipped": "تم شحن طلبك",
//   "orderPlaced": "لقد قمت بوضع الطلب",
//   "deliveredTo": "تم التوصيل إلى",
//   "sentFrom": "تم الإرسال من",
//   "awaitingApproval": "في انتظار الموافقة",
//   "productDetails": "تفاصيل المنتج",
//   "quantity": "الكمية",
//   "sellerInfo": "معلومات البائع",
//   "contactSeller": "اتصل بالبائع",
//   "shippingInfo": "معلومات الشحن",
//   "orderFailed": "فشل الطلب",
//   "orderFailureMessage": "هناك خطأ ما في طلبك.",
//   "orderCheckMessage": "يرجى العودة والتحقق من طلبك مرة أخرى بعناية.",
//   "returnOrder": "إرجاع طلبي",
//   "orderConfirmed": "تم تأكيد الطلب",
//   "thankYouMessage": "شكرًا لطلبك. ستتلقى تأكيدًا عبر البريد الإلكتروني قريبًا.",
//   "checkOrderStatus": "تحقق من حالة طلبك في سلة التسوق الخاصة بك.",
//   "continueShopping": "متابعة التسوق",
//   "addNewAddress": "إضافة عنوان جديد",
//   "postProduct": "نشر المنتج",
//   "productHintText": "اسم المنتج (50 كلمة على الأقل و 150 كلمة كحد أقصى)",
//   "statusText": "الحالة",
//   "productFeatures": "الميزات البارزة للمنتج، ما يتضمنه، المدة التي تم استخدامها...",
//   "statusProduct": "اختر حالة منتجك",
//   "backText": "عودة",
//   "buyNow": "اشترِ الآن",
//   "reportSeller": "الإبلاغ عن البائع",
//   "noRecommendSeller": "لا أوصي بهذا البائع",
//   "noMoreProfile": "لا توجد المزيد من الملفات الشخصية!",
//   "selectPostsFrom": "حدد المنشورات من",
//   "offersFrom": "عروض من multi.me",
//   "payFee": "دفع الرسوم",
//   "apply": "تقديم",
//   "freePosting": "نشر مجاني",
//   "adType": "النوع: إعلان عادي\nينطبق على أول إعلانات شخصية/شهر",
//   "upgradeToPro": "ترقية إلى النسخة الاحترافية",
//   "postFreeAds": "نشر إعلانات مجانية",
//   "adTypeAds": "النوع: إعلانات دائمة - إعلانات عادية",
//   "appliesToAllPosts": "ينطبق على جميع المنشورات",
//   "multiMe": "Multi.Me",
//   "doesNot": "لا",
//   "allowDepositPrivate": "يسمح: الإيداع/التحويلات الخاصة/المعاملات خارج المنصة/توظيف المتعاونين/تقديم الهدايا المجانية/تقديم المعلومات الشخصية أو إلغاء الطلبات بناءً على طلب البائع.",
//   "pleaseOnlyBuySell": "يرجى شراء/بيع فقط على Multi.Me لتجنب التعرض للاحتيال. ستقوم Multi.Me بجمع واستخدام سجل الدردشة وفقًا لسياسة الخصوصية الخاصة بـ Multi.Me.",
//   "gotUnusedItem": "هل لديك أشياء غير مستخدمة؟",
//   "turnThemIntoCash": "حولها إلى نقود!",
//   "postForSale": "نشر للبيع",
//   "itsTotally": "إنه تمامًا",
//   "safe": "آمن",
//   "toPurchaseAtMultiMe": "للشراء في Multi.Me. سيتم إرسال أموالك إلى البائع فقط بعد تأكيدك لاستلام المنتج؛ وإلا سيتم إعادتها إليك.",
//   "aboutThisItem": "عن هذا العنصر",
//   "condition": "الحالة",
//   "type": "النوع",
//   "brand": "العلامة التجارية",
//   "show": "عرض",
//   "hide": "إخفاء",
//   "estDelivery": "التوصيل المتوقع: ",
//   "safePurchase": "من الآمن تمامًا الشراء في Multi.Me. سيتم إرسال أموالك إلى البائع فقط بعد التأكيد.",
//   "chargeRateInfo": "سيتم فرض رسوم بنسبة 3% على المشتريات التي تزيد عن 1000 دولار\nسيتم فرض رسوم بنسبة 5% على المشتريات التي تقل عن 999 دولار",
//   "thereWill": "سيكون هناك",
//   "chargeRate": "رسوم للشراء",
//   "defaultText": "افتراضي",
//   "createDate": "تاريخ الإنشاء",
//   "shippingPrice": "يشمل سعر الشحن",
//   "contactText": "يرجى الاتصال بي إذا كان لديك أي سؤال أو ترغب في مناقشة السعر أو الحصول على عرض أفضل.",
//   "contactMe": "اتصل بي",
//   "moreLike": "المزيد مثل هذا",
//   "viewAll": "عرض الكل",
//   "postMe": "نشر لي",
//   "postNews": "نشر الأخبار",
//   "newsTitle": "عنوان الخبر",
//   "category": "الفئة",
//   "country": "الدولة",
//   "articleSource": "مصدر المقال",
//   "content": "المحتوى",
//   "copyContent": "انسخ المحتوى من المقال هنا",
//   "addCoverPhoto": "+ \nإضافة صورة غلاف",
//   "selectCategory": "اختر الفئة",
//   "reviewPost": "مراجعة منشوراتك",
//   "tabAll": "الكل",
//   "tabSports": "الرياضة",
//   "tabPolitics": "السياسة",
//   "tabBusiness": "الأعمال",
//   "tabHealth": "الصحة",
//   "tabTravel": "السفر",
//   "tabScience": "العلوم",
//   "readAndShare": "اقرأ وشارك الأخبار من جميع أنحاء العالم",
//   "createCommunity": "🙌 نحن لا نخلق الأخبار، نحن نخلق مجتمعًا حيث يشارك الناس ويناقشون الأخبار، ويساعدون بعضهم البعض على تطوير التفكير النقدي والفهم متعدد الأبعاد",
//   "hotNew": "أخبار ساخنة",
//   "history": "التاريخ",
//   "createPost": "إنشاء منشور",
//   "latest": "الأحدث",
//   "about": "عن",
//   "skill": "المهارات",
//   "education": "التعليم",
//   "experience": "الخبرة",
//   "notPost": "لم يتم النشر بعد",
//   "subNewPost": "جرب الاشتراك في منشور جديد لك...",
//   "notReview": "لم يتم المراجعة بعد",
//   "thanksStrongBody": "طلبك مهم جدًا لمجتمع StrongBody. اطلب الآن لكتابة أول مراجعة هنا.",
//   "productAndService": "استكشاف المنتجات والخدمات",
//   "newService": "خدمة جديدة",
//   "notService": "لا توجد خدمات بعد",
//   "sellerCreateService": "لم يقم البائع بإنشاء أي خدمات بعد. إذا باع شيئًا، فسيتم عرضه هنا.",
//   "noProduct": "لا توجد منتجات بعد",
//   "noProductDescription": "لم يبيع البائع أي منتجات بعد. إذا فعل ذلك في المستقبل، ستظهر المنتجات هنا.",
//   "activeShop": "متجر StrongBody نشط هنا →",
//   "add": "إضافة",
//   "fullTime": "دوام كامل",
//   "toYear": "إلى السنة",
//   "companyBusiness": "اسم الشركة / العمل",
//   "fromYear": "من السنة",
//   "positionCompany": "المنصب في الشركة",
//   "addressCountry": "العنوان / الدولة",
//   "addSkill": "إضافة مهارة (مثل موهبة الصوت)",
//   "year": "السنة",
//   "uploadImage": "قم بتحميل صور بحجم 430x190 بكسل للحصول على انطباعات وفرص بيع أفضل.",
//   "displayNameLabel": "اسم العرض",
//   "displayNameHint": "يرجى تسمية حسابك",
//   "yourProfession": "مهنتك",
//   "yourProfessionHint": "مثال 1: طبيب أمراض جلدية معتمد متخصص في الإجراءات التجميلية وعلاج سرطان الجلد. مثال 2: مدرب شخصي معتمد متخصص في فقدان الوزن والتدريب على القوة لجميع مستويات اللياقة البدنية.",
//   "professionalLabel": "لقبك المهني",
//   "professionalHint": "مثال: مدرب شخصي / معالج / طبيب أسنان",
//   "contentProfile": "يرجى تقديم نفسك. يمكنك تضمين:\n- تجربتك في المجال\n- الإنجازات الرئيسية أو الشهادات\n- نهجك في رعاية المرضى أو منهجية العمل\n- أي تخصصات أو مجالات خبرة",
//   "addLanguage": "إضافة لغة",
//   "levelLanguage": "مستوى اللغة",
//   "englishText": "الإنجليزية",
//   "frenchText": "الفرنسية",
//   "basic": "أساسي",
//   "conversational": "محادثة",
//   "completionRate": "معدل الإنجاز",
//   "settingAccount": "إعداد الحساب",
//   "myAccount": "حسابي",
//   "password": "كلمة المرور",
//   "verifyAccount": "التحقق من الحساب",
//   "notification": "الإشعار",
//   "privacy": "الخصوصية",
//   "syncSetting": "إعداد المزامنة",
//   "deleteAccount": "حذف الحساب",
//   "turnOnNotificationsForAllModes": "تشغيل الإشعارات لجميع الأوضاع",
//   "socialMode": "وضع اجتماعي",
//   "manageYourConnection": "إدارة اتصالك",
//   "businessMode": "وضع الأعمال",
//   "connectToPartnerChances": "الاتصال بالشريك، الفرص",
//   "newsMode": "وضع الأخبار",
//   "readNews": "قراءة الأخبار",
//   "marketPlace": "سوق",
//   "shoppingProductsAndService": "التسوق للمنتجات والخدمات",
//   "datingMode": "وضع المواعدة",
//   "findRealPartnerForYou": "البحث عن شريك حقيقي لك",
//   "strongBodyAi": "StrongBody.ai",
//   "darkMode": "الوضع المظلم",
//   "selectMode": "اختر وضع حماية العين",
//   "yourInvisible": "نشاطك سيكون غير مرئي",
//   "settingMode": "إعداد الوضع",
//   "policy": "السياسة",
//   "yourActivityInvisible": "نشاطك سيكون غير مرئي",
//   "helpCenterTitle": "مركز المساعدة",
//   "helpCenterDescription": "كل ما تحتاج معرفته",
//   "shoppingCartTitle": "عربة التسوق",
//   "shoppingCartDescription": "إدارة الطلبات المحفوظة في سوق، شبكة.",
//   "shakeToExploreTitle": "اهتز للاستكشاف بالقرب",
//   "shakeToExploreDescription": "التبديل إلى اهتز للاستكشاف",
//   "balanceTitle": "الرصيد",
//   "balanceDescription": "يمكنك سحب المال إلى حسابك الخاص",
//   "sellerModeTitle": "وضع البائع",
//   "sellerModeDescription": "التبديل إلى بيع الخدمات",
//   "logOut": "تسجيل الخروج",
//   "textAndCall": "نص واتصال",
//   "showViewedStatus": "عرض حالة 'تم المشاهدة'",
//   "allowMessaging": "السماح بالرسائل",
//   "allowCall": "السماح بالاتصال",
//   "diary": "المذكرات",
//   "allowViewingAndCommenting": "السماح بالمشاهدة والتعليق",
//   "friendSource": "مصدر الأصدقاء",
//   "suggestFriendsFromContacts": "اقتراح الأصدقاء من جهات الاتصال",
//   "addContactsToMultiMe": "إضافة جهات الاتصال إلى MultiMe عندما يكون لدى كل منكما أرقام هواتف بعضكما في أجهزتهم",
//   "on": "تشغيل",
//   "everybody": "الجميع",
//   "friend": "الصديق",
//   "phoneNumberTitle": "ما هو رقم هاتفك؟",
//   "phoneNumberDescription": "تساعدنا هذه المعلومات في التحقق من حسابك لجعله أكثر أمانًا.",
//   "mobileNumber": "رقم الهاتف المحمول",
//   "enterMobileNumber": "أدخل رقم هاتفك المحمول",
//   "forgotPassword": "هل نسيت كلمة المرور؟",
//   "emailLabel": "البريد الإلكتروني",
//   "emailHint": "أدخل البريد الإلكتروني",
//   "alreadyMember": "هل أنت عضو بالفعل؟",
//   "signUp": "سجل الآن",
//   "pickFriendsTitle": "اختر 10 أصدقاء وعملاء مفضلين",
//   "inviteFriendMessage": "ادعُ صديقًا للاستمرار",
//   "searchContactsHint": "ابحث في جهات الاتصال الخاصة بك",
//   "importContactsTitle": "استيراد جهات الاتصال الخاصة بك",
//   "importContactsMessage": "لن ترسل StrongBody رسائل نصية إلى الأصدقاء نيابة عنك",
//   "importButton": "استيراد",
//   "minPasswordCriteria": "حد أدنى 8 أحرف، 1 حرف كبير، 1 حرف صغير، 1 رقم",
//   "confirmPasswordHint": "تأكيد كلمة المرور",
//   "signInTitle": "تسجيل الدخول",
//   "rememberMe": "تذكرني",
//   "notAMember": "لست عضوًا بعد؟",
//   "userName": "اسم المستخدم",
//   "publicName": "اسم المستخدم العام",
//   "occupation": "المهنة",
//   "enterOccupation": "أدخل المهنة",
//   "termsOfService": "شروط الخدمة",
//   "privacyPolicy": "سياسة الخصوصية",
//   "toData": "لتعلم كيفية استخدام بياناتك الشخصية.",
//   "andOur": "ولتلقي رسائل بريد إلكتروني منا بين الحين والآخر. يرجى قراءة",
//   "byJoinStrongBody": "من خلال الانضمام، فإنك توافق على StrongBody",
//   "verifyAccountTitle": "تحقق من بريدك الإلكتروني للتحقق من الحساب",
//   "verifyAccountMessage": "لقد أرسلنا لك رابطًا للتأكيد. يرجى التحقق من بريدك الإلكتروني لإتمام التسجيل.",
//   "backToSignIn": "الرجوع إلى تسجيل الدخول",
//   "emailVerifiedTitle": "تم التحقق من البريد الإلكتروني بنجاح",
//   "emailVerifiedMessage": "لقد قمت بالتحقق بنجاح من رقم الهاتف لهذا الحساب.",
//   "phoneVerifiedTitle": "تم التحقق من رقم الهاتف بنجاح",
//   "phoneVerifiedMessage": "لقد قمت بالتحقق بنجاح من رقم الهاتف لهذا الحساب.",
//   "blockUser": "حظر",
//   "from": "من",
//   "imageText": "صورة",
//   "createPostTitle": "إنشاء منشور",
//   "postHintText": "أنشئ لحظة... ما الذي يدور في ذهنك؟",
//   "postFirstTimeMessage": "👋 مرحبًا بك في MultiMe! قدم نفسك وشارك شيئًا مثيرًا عنك أو ما الذي جاء بك إلى هنا اليوم. شارك معرفتك وقصصك لبناء الثقة وتوسيع المجتمع.",
//   "postFirstTimeSubTitle": "انشر مقالك الأول",
//   "strongBody": "StrongBody",
//   "social": "اجتماعي",
//   "record": "سجل",
//   "backgroundColor": "لون الخلفية",
//   "checkIn": "تسجيل الوصول",
//   "postDetail": "تفاصيل المنشور",
//   "getStarted": "ابدأ الآن",
//   "send": "إرسال",
//   "resonate": "تأثير",
//   "nameProduct": "اسم المنتج",
//   "descriptionOption": "الوصف (اختياري)",
//   "shareIssue": "شارك مزيد من التفاصيل حول هذه المشكلة",
//   "ifImmediately": "إذا كنت تعلم أن شخصًا ما في خطر جسدي، يرجى الاتصال بوكالة إنفاذ القانون المحلية على الفور.",
//   "postRequest": "طلب منشور",
//   "supportImage": "يدعم تنسيقات الصور .jpg، .jpeg بحجم أقل من",
//   "fiveMB": "5 ميجابايت",
//   "uploadPhotoBrowse": "قم بتحميل صورة للوظيفة. ستظهر هذه الصورة في القوائم ليتمكن الناس من تصفحها",
//   "uploadPhoto": "تحميل الصورة",
//   "egPartner": "على سبيل المثال. حدد احتياجاتك أو عروضك، الصناعة/المجال، المهارات/الخبرات المطلوبة، وصف ما تبحث عنه في مرشح أو شريك محتمل",
//   "graphic": "مصمم جرافيك، الرسوم المتحركة ثنائية الأبعاد، ....",
//   "headerLogin": "تسجيل الدخول إلى حسابك",
//   "headerLoginText": "أدخل بريدك الإلكتروني وكلمة المرور لتسجيل الدخول",
//   "invalidateEmail": "هذا البريد الإلكتروني غير مسجل.",
//   "invalidatePassword": "كلمة المرور هذه غير صالحة.",
//   "cantEmpty": "لا يمكن أن يكون {label} فارغًا",
//   "report": "تقرير",
//   "login": "تسجيل الدخول",
//   "orLoginWith": "أو",
//   "dontHaveAnAccount": "ليس لديك حساب؟",
//   "already": "هل أنت عضو بالفعل؟",
//   "resetPassword": "إعادة تعيين كلمة المرور",
//   "resetPasswordText": "أدخل البريد الإلكتروني المرتبط بحسابك وسنرسل لك رسالة بريد إلكتروني تحتوي على التعليمات لإعادة تعيين كلمة المرور.",
//   "emailAddress": "عنوان البريد الإلكتروني",
//   "sendEmail": "إرسال التعليمات",
//   "checkEmail": "افتح بريدك الإلكتروني",
//   "checkEmailText": "لقد أرسلنا تعليمات استرداد كلمة المرور إلى بريدك الإلكتروني.",
//   "openEmail": "افتح تطبيق البريد الإلكتروني",
//   "createPassword": "إنشاء كلمة مرور جديدة",
//   "createPasswordText": "يجب أن تكون كلمة مرورك الجديدة مختلفة عن كلمات المرور السابقة.",
//   "confirmPassword": "تأكيد كلمة المرور الجديدة",
//   "confirmPasswordText": "يجب أن تتطابق كلا الكلمتين",
//   "spamEmail": "لم تستلم البريد الإلكتروني؟ تحقق من فلتر البريد العشوائي، أو",
//   "spamEmailLink": "جرب عنوان بريد إلكتروني آخر.",
//   "passwordNotMatch": "كلمة المرور غير مطابقة",
//   "uploadImageBussines": "تحميل الصورة",
//   "Whatisyouroccupation?": "ما هي وظيفتك؟",
//   "JobProfile": "ملف الوظيفة",
//   "dayago": "منذ يوم",
//   "weekago": "منذ أسبوع",
//   "SelectaTimeRange": "اختر نطاقًا زمنيًا",
//   "Eg:SeekingMarketingExpertForTechStartup": "مثال: البحث عن خبير تسويق لشركة تقنية ناشئة",
//   "Field/Industry": "المجال/الصناعة",
//   "General": "عام",
//   "AvatarImage": "صورة الرمزية",
//   "Display_name": "اسم العرض",
//   "Occupation": "الوظيفة",
//   "YouAreAllowedToChange": "يُسمح لك بالتغيير مرة واحدة فقط.",
//   "AccountDeactivation": "تعطيل الحساب",
//   "WhatHappensWhenYou": "ماذا يحدث عندما تقوم بتعطيل حسابك؟",
//   "YourProfileAndGigs": "لن يتم عرض ملفك الشخصي وجيجز على Strongbody بعد الآن.\nسيتم إلغاء الطلبات النشطة.\nلن تتمكن من إعادة تنشيط الجيجز."
// };
// static const Map<String, Map<String,dynamic>> mapLocales = {"zh": zh, "en": en, "hi": hi, "es": es, "ar": ar};
// }
