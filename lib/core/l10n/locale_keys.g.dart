// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class  LocaleKeys {
  static const hello = 'hello';
  static const language = 'language';
  static const home = 'home';
  static const setting = 'setting';
  static const note = 'note';
  static const featured = 'featured';
  static const seeMore = 'seeMore';
  static const order = 'order';
  static const message = 'message';
  static const cancel = 'cancel';
  static const notInterested = 'notInterested';
  static const swipeLeftNoInterest = 'swipeLeftNoInterest';
  static const askMore = 'askMore';
  static const swipeRightNoInterest = 'swipeRightNoInterest';
  static const business = 'business';
  static const request = 'request';
  static const readMore = 'readMore';
  static const readLess = 'readLess';
  static const whatDoYouWantToDo = 'whatDoYouWantToDo';
  static const postBusinessOpportunity = 'postBusinessOpportunity';
  static const offer = 'offer';
  static const thisIsAn = 'thisIsAn';
  static const title = 'title';
  static const graphicDesigner = 'graphicDesigner';
  static const businessNeeds = 'businessNeeds';
  static const description = 'description';
  static const shortDescription = 'shortDescription';
  static const stateYourNeeds = 'stateYourNeeds';
  static const budgetInvestmentUSD = 'budgetInvestmentUSD';
  static const uploadAvatar = 'uploadAvatar';
  static const optional = 'optional';
  static const uploadAPhoto = 'uploadAPhoto';
  static const addMore = 'addMore';
  static const postDisplayDuration = 'postDisplayDuration';
  static const dayAgo = 'dayAgo';
  static const age = 'age';
  static const distance = 'distance';
  static const skip = 'skip';
  static const post = 'post';
  static const maleText = 'maleText';
  static const preferredGender = 'preferredGender';
  static const femaleText = 'femaleText';
  static const otherText = 'otherText';
  static const save = 'save';
  static const dating = 'dating';
  static const likeYou = 'likeYou';
  static const matches = 'matches';
  static const editBio = 'editBio';
  static const editSeeking = 'editSeeking';
  static const camera = 'camera';
  static const gallery = 'gallery';
  static const aboutYou = 'aboutYou';
  static const displayName = 'displayName';
  static const name = 'name';
  static const yearOfBirth = 'yearOfBirth';
  static const youYearOfBirth = 'youYearOfBirth';
  static const yourOccupation = 'yourOccupation';
  static const shortBio = 'shortBio';
  static const yourBio = 'yourBio';
  static const addYourPhotos = 'addYourPhotos';
  static const addAtLeast2 = 'addAtLeast2';
  static const continueButton = 'continueButton';
  static const likedYou = 'likedYou';
  static const youLikedThem = 'youLikedThem';
  static const theyLikedYou = 'theyLikedYou';
  static const reviewThePeople = 'reviewThePeople';
  static const today = 'today';
  static const reviewThePeople2 = 'reviewThePeople2';
  static const reviewThePeopleYouHaveSwipedLeftOn = 'reviewThePeopleYouHaveSwipedLeftOn';
  static const yesterday = 'yesterday';
  static const itsAMatch = 'itsAMatch';
  static const startAConversationNow = 'startAConversationNow';
  static const startChatting = 'startChatting';
  static const keepSwiping = 'keepSwiping';
  static const recent = 'recent';
  static const whoAreYouSeeking = 'whoAreYouSeeking';
  static const recentlyActive = 'recentlyActive';
  static const welcomeDatingMode = 'welcomeDatingMode';
  static const ruleBeYourselfTitle = 'ruleBeYourselfTitle';
  static const ruleBeYourselfDescription = 'ruleBeYourselfDescription';
  static const ruleStaySafeTitle = 'ruleStaySafeTitle';
  static const ruleStaySafeDescription = 'ruleStaySafeDescription';
  static const ruleStaySafeLinkText = 'ruleStaySafeLinkText';
  static const rulePlayItCoolTitle = 'rulePlayItCoolTitle';
  static const rulePlayItCoolDescription = 'rulePlayItCoolDescription';
  static const ruleBeProactiveTitle = 'ruleBeProactiveTitle';
  static const ruleBeProactiveDescription = 'ruleBeProactiveDescription';
  static const iAgreeButtonText = 'iAgreeButtonText';
  static const followHouseRulesText = 'followHouseRulesText';
  static const addNewCardTitle = 'addNewCardTitle';
  static const cardDetailsTitle = 'cardDetailsTitle';
  static const cardNumberLabel = 'cardNumberLabel';
  static const cardNumberHint = 'cardNumberHint';
  static const expiryDateLabel = 'expiryDateLabel';
  static const expiryDateHint = 'expiryDateHint';
  static const cvvLabel = 'cvvLabel';
  static const cvvHint = 'cvvHint';
  static const cardholderNameLabel = 'cardholderNameLabel';
  static const cardholderNameHint = 'cardholderNameHint';
  static const verificationTransaction = 'verificationTransaction';
  static const verificationTransactionText = 'verificationTransactionText';
  static const confirmButtonText = 'confirmButtonText';
  static const checkoutTitle = 'checkoutTitle';
  static const warningMessage = 'warningMessage';
  static const orderDetailsTitle = 'orderDetailsTitle';
  static const orderDetailsHint = 'orderDetailsHint';
  static const paymentOptionsTitle = 'paymentOptionsTitle';
  static const paymentDetailsTitle = 'paymentDetailsTitle';
  static const totalCost = 'totalCost';
  static const paypal = 'paypal';
  static const visaCard = 'visaCard';
  static const paymentFailed = 'paymentFailed';
  static const paymentFailedMessage = 'paymentFailedMessage';
  static const locationLabel = 'locationLabel';
  static const addNewCard = 'addNewCard';
  static const orderID = 'orderID';
  static const orderDate = 'orderDate';
  static const productPrice = 'productPrice';
  static const voucher = 'voucher';
  static const serviceFee = 'serviceFee';
  static const termsAndConditions = 'termsAndConditions';
  static const termsAndConditionsLink = 'termsAndConditionsLink';
  static const confirmOrder = 'confirmOrder';
  static const dateText = 'dateText';
  static const personalInformation = 'personalInformation';
  static const fullName = 'fullName';
  static const phone = 'phone';
  static const addressLabel = 'addressLabel';
  static const addressHint = 'addressHint';
  static const specificAddressLabel = 'specificAddressLabel';
  static const specificAddressHint = 'specificAddressHint';
  static const mapPlaceholder = 'mapPlaceholder';
  static const setDefaultCheckboxLabel = 'setDefaultCheckboxLabel';
  static const saveChangeButton = 'saveChangeButton';
  static const liked = 'liked';
  static const marketplace = 'marketplace';
  static const newText = 'newText';
  static const mostView = 'mostView';
  static const mostViewed = 'mostViewed';
  static const likeNew = 'likeNew';
  static const adsPlaceholder = 'adsPlaceholder';
  static const productName = 'productName';
  static const shopAddress = 'shopAddress';
  static const postDate = 'postDate';
  static const price = 'price';
  static const orderPage = 'orderPage';
  static const messagePage = 'messagePage';
  static const cancelOrder = 'cancelOrder';
  static const payment = 'payment';
  static const totalPayment = 'totalPayment';
  static const subtotal = 'subtotal';
  static const orderShipped = 'orderShipped';
  static const orderPlaced = 'orderPlaced';
  static const deliveredTo = 'deliveredTo';
  static const sentFrom = 'sentFrom';
  static const awaitingApproval = 'awaitingApproval';
  static const productDetails = 'productDetails';
  static const quantity = 'quantity';
  static const sellerInfo = 'sellerInfo';
  static const contactSeller = 'contactSeller';
  static const shippingInfo = 'shippingInfo';
  static const orderFailed = 'orderFailed';
  static const orderFailureMessage = 'orderFailureMessage';
  static const orderCheckMessage = 'orderCheckMessage';
  static const returnOrder = 'returnOrder';
  static const orderConfirmed = 'orderConfirmed';
  static const thankYouMessage = 'thankYouMessage';
  static const checkOrderStatus = 'checkOrderStatus';
  static const continueShopping = 'continueShopping';
  static const addNewAddress = 'addNewAddress';
  static const postProduct = 'postProduct';
  static const productHintText = 'productHintText';
  static const statusText = 'statusText';
  static const productFeatures = 'productFeatures';
  static const statusProduct = 'statusProduct';
  static const backText = 'backText';
  static const buyNow = 'buyNow';
  static const reportSeller = 'reportSeller';
  static const noRecommendSeller = 'noRecommendSeller';
  static const noMoreProfile = 'noMoreProfile';
  static const selectPostsFrom = 'selectPostsFrom';
  static const offersFrom = 'offersFrom';
  static const payFee = 'payFee';
  static const apply = 'apply';
  static const freePosting = 'freePosting';
  static const adType = 'adType';
  static const upgradeToPro = 'upgradeToPro';
  static const postFreeAds = 'postFreeAds';
  static const adTypeAds = 'adTypeAds';
  static const appliesToAllPosts = 'appliesToAllPosts';
  static const multiMe = 'multiMe';
  static const doesNot = 'doesNot';
  static const allowDepositPrivate = 'allowDepositPrivate';
  static const pleaseOnlyBuySell = 'pleaseOnlyBuySell';
  static const gotUnusedItem = 'gotUnusedItem';
  static const turnThemIntoCash = 'turnThemIntoCash';
  static const postForSale = 'postForSale';
  static const itsTotally = 'itsTotally';
  static const safe = 'safe';
  static const toPurchaseAtMultiMe = 'toPurchaseAtMultiMe';
  static const aboutThisItem = 'aboutThisItem';
  static const condition = 'condition';
  static const type = 'type';
  static const brand = 'brand';
  static const show = 'show';
  static const hide = 'hide';
  static const estDelivery = 'estDelivery';
  static const safePurchase = 'safePurchase';
  static const chargeRateInfo = 'chargeRateInfo';
  static const thereWill = 'thereWill';
  static const chargeRate = 'chargeRate';
  static const defaultText = 'defaultText';
  static const createDate = 'createDate';
  static const shippingPrice = 'shippingPrice';
  static const contactText = 'contactText';
  static const contactMe = 'contactMe';
  static const moreLike = 'moreLike';
  static const viewAll = 'viewAll';
  static const postMe = 'postMe';
  static const postNews = 'postNews';
  static const newsTitle = 'newsTitle';
  static const category = 'category';
  static const country = 'country';
  static const articleSource = 'articleSource';
  static const content = 'content';
  static const copyContent = 'copyContent';
  static const addCoverPhoto = 'addCoverPhoto';
  static const selectCategory = 'selectCategory';
  static const reviewPost = 'reviewPost';
  static const tabAll = 'tabAll';
  static const tabSports = 'tabSports';
  static const tabPolitics = 'tabPolitics';
  static const tabBusiness = 'tabBusiness';
  static const tabHealth = 'tabHealth';
  static const tabTravel = 'tabTravel';
  static const tabScience = 'tabScience';
  static const readAndShare = 'readAndShare';
  static const createCommunity = 'createCommunity';
  static const hotNew = 'hotNew';
  static const history = 'history';
  static const createPost = 'createPost';
  static const latest = 'latest';
  static const about = 'about';
  static const skill = 'skill';
  static const education = 'education';
  static const experience = 'experience';
  static const notPost = 'notPost';
  static const subNewPost = 'subNewPost';
  static const notReview = 'notReview';
  static const thanksStrongBody = 'thanksStrongBody';
  static const productAndService = 'productAndService';
  static const newService = 'newService';
  static const notService = 'notService';
  static const sellerCreateService = 'sellerCreateService';
  static const noProduct = 'noProduct';
  static const noProductDescription = 'noProductDescription';
  static const activeShop = 'activeShop';
  static const add = 'add';
  static const fullTime = 'fullTime';
  static const toYear = 'toYear';
  static const companyBusiness = 'companyBusiness';
  static const fromYear = 'fromYear';
  static const positionCompany = 'positionCompany';
  static const addressCountry = 'addressCountry';
  static const addSkill = 'addSkill';
  static const year = 'year';
  static const uploadImage = 'uploadImage';
  static const displayNameLabel = 'displayNameLabel';
  static const displayNameHint = 'displayNameHint';
  static const yourProfession = 'yourProfession';
  static const yourProfessionHint = 'yourProfessionHint';
  static const professionalLabel = 'professionalLabel';
  static const professionalHint = 'professionalHint';
  static const contentProfile = 'contentProfile';
  static const addLanguage = 'addLanguage';
  static const levelLanguage = 'levelLanguage';
  static const englishText = 'englishText';
  static const frenchText = 'frenchText';
  static const basic = 'basic';
  static const conversational = 'conversational';
  static const completionRate = 'completionRate';
  static const settingAccount = 'settingAccount';
  static const myAccount = 'myAccount';
  static const password = 'password';
  static const verifyAccount = 'verifyAccount';
  static const notification = 'notification';
  static const privacy = 'privacy';
  static const syncSetting = 'syncSetting';
  static const deleteAccount = 'deleteAccount';
  static const turnOnNotificationsForAllModes = 'turnOnNotificationsForAllModes';
  static const socialMode = 'socialMode';
  static const manageYourConnection = 'manageYourConnection';
  static const businessMode = 'businessMode';
  static const connectToPartnerChances = 'connectToPartnerChances';
  static const newsMode = 'newsMode';
  static const readNews = 'readNews';
  static const marketPlace = 'marketPlace';
  static const shoppingProductsAndService = 'shoppingProductsAndService';
  static const datingMode = 'datingMode';
  static const findRealPartnerForYou = 'findRealPartnerForYou';
  static const strongBodyAi = 'strongBodyAi';
  static const darkMode = 'darkMode';
  static const selectMode = 'selectMode';
  static const yourInvisible = 'yourInvisible';
  static const settingMode = 'settingMode';
  static const policy = 'policy';
  static const yourActivityInvisible = 'yourActivityInvisible';
  static const helpCenterTitle = 'helpCenterTitle';
  static const helpCenterDescription = 'helpCenterDescription';
  static const shoppingCartTitle = 'shoppingCartTitle';
  static const shoppingCartDescription = 'shoppingCartDescription';
  static const shakeToExploreTitle = 'shakeToExploreTitle';
  static const shakeToExploreDescription = 'shakeToExploreDescription';
  static const balanceTitle = 'balanceTitle';
  static const balanceDescription = 'balanceDescription';
  static const sellerModeTitle = 'sellerModeTitle';
  static const sellerModeDescription = 'sellerModeDescription';
  static const logOut = 'logOut';
  static const textAndCall = 'textAndCall';
  static const showViewedStatus = 'showViewedStatus';
  static const allowMessaging = 'allowMessaging';
  static const allowCall = 'allowCall';
  static const diary = 'diary';
  static const allowViewingAndCommenting = 'allowViewingAndCommenting';
  static const friendSource = 'friendSource';
  static const suggestFriendsFromContacts = 'suggestFriendsFromContacts';
  static const addContactsToMultiMe = 'addContactsToMultiMe';
  static const on = 'on';
  static const everybody = 'everybody';
  static const friend = 'friend';
  static const phoneNumberTitle = 'phoneNumberTitle';
  static const phoneNumberDescription = 'phoneNumberDescription';
  static const mobileNumber = 'mobileNumber';
  static const enterMobileNumber = 'enterMobileNumber';
  static const forgotPassword = 'forgotPassword';
  static const emailLabel = 'emailLabel';
  static const emailHint = 'emailHint';
  static const alreadyMember = 'alreadyMember';
  static const signUp = 'signUp';
  static const pickFriendsTitle = 'pickFriendsTitle';
  static const inviteFriendMessage = 'inviteFriendMessage';
  static const searchContactsHint = 'searchContactsHint';
  static const importContactsTitle = 'importContactsTitle';
  static const importContactsMessage = 'importContactsMessage';
  static const importButton = 'importButton';
  static const minPasswordCriteria = 'minPasswordCriteria';
  static const confirmPasswordHint = 'confirmPasswordHint';
  static const signInTitle = 'signInTitle';
  static const rememberMe = 'rememberMe';
  static const notAMember = 'notAMember';
  static const userName = 'userName';
  static const publicName = 'publicName';
  static const occupation = 'occupation';
  static const enterOccupation = 'enterOccupation';
  static const termsOfService = 'termsOfService';
  static const privacyPolicy = 'privacyPolicy';
  static const toData = 'toData';
  static const andOur = 'andOur';
  static const byJoinStrongBody = 'byJoinStrongBody';
  static const verifyAccountTitle = 'verifyAccountTitle';
  static const verifyAccountMessage = 'verifyAccountMessage';
  static const backToSignIn = 'backToSignIn';
  static const emailVerifiedTitle = 'emailVerifiedTitle';
  static const emailVerifiedMessage = 'emailVerifiedMessage';
  static const phoneVerifiedTitle = 'phoneVerifiedTitle';
  static const phoneVerifiedMessage = 'phoneVerifiedMessage';
  static const blockUser = 'blockUser';
  static const from = 'from';
  static const imageText = 'imageText';
  static const createPostTitle = 'createPostTitle';
  static const postHintText = 'postHintText';
  static const postFirstTimeMessage = 'postFirstTimeMessage';
  static const postFirstTimeSubTitle = 'postFirstTimeSubTitle';
  static const strongBody = 'strongBody';
  static const social = 'social';
  static const record = 'record';
  static const backgroundColor = 'backgroundColor';
  static const checkIn = 'checkIn';
  static const postDetail = 'postDetail';
  static const getStarted = 'getStarted';
  static const send = 'send';
  static const resonate = 'resonate';
  static const nameProduct = 'nameProduct';
  static const descriptionOption = 'descriptionOption';
  static const shareIssue = 'shareIssue';
  static const ifImmediately = 'ifImmediately';
  static const postRequest = 'postRequest';
  static const supportImage = 'supportImage';
  static const fiveMB = 'fiveMB';
  static const uploadPhotoBrowse = 'uploadPhotoBrowse';
  static const uploadPhoto = 'uploadPhoto';
  static const egPartner = 'egPartner';
  static const graphic = 'graphic';
  static const headerLogin = 'headerLogin';
  static const headerLoginText1 = 'headerLoginText1';
  static const headerLoginText2 = 'headerLoginText2';
  static const headerLoginText3 = 'headerLoginText3';
  static const invalidateEmail = 'invalidateEmail';
  static const invalidatePassword = 'invalidatePassword';
  static const cantEmpty = 'cantEmpty';
  static const report = 'report';
  static const login = 'login';
  static const orLoginWith = 'orLoginWith';
  static const dontHaveAnAccount = 'dontHaveAnAccount';
  static const already = 'already';
  static const resetPassword = 'resetPassword';
  static const resetPasswordText = 'resetPasswordText';
  static const emailAddress = 'emailAddress';
  static const sendEmail = 'sendEmail';
  static const checkEmail = 'checkEmail';
  static const checkEmailText = 'checkEmailText';
  static const openEmail = 'openEmail';
  static const createPassword = 'createPassword';
  static const createPasswordText = 'createPasswordText';
  static const confirmPassword = 'confirmPassword';
  static const confirmPasswordText = 'confirmPasswordText';
  static const spamEmail = 'spamEmail';
  static const spamEmailLink = 'spamEmailLink';
  static const passwordChanged = 'passwordChanged';
  static const uploadImageBussines = 'uploadImageBussines';
  static const Whatisyouroccupation = 'Whatisyouroccupation';
  static const JobProfile = 'JobProfile';
  static const dayago = 'dayago';
  static const weekago = 'weekago';
  static const SelectaTimeRange = 'SelectaTimeRange';
  static const SeekingMarketingExpertForTechStartup = 'SeekingMarketingExpertForTechStartup';
  static const Field_Industry = 'Field_Industry';
  static const General = 'General';
  static const AvatarImage = 'AvatarImage';
  static const Display_name = 'Display_name';
  static const Occupation = 'Occupation';
  static const YouAreAllowedToChange = 'YouAreAllowedToChange';
  static const AccountDeactivation = 'AccountDeactivation';
  static const WhatHappensWhenYou = 'WhatHappensWhenYou';
  static const YourProfileAndGigs = 'YourProfileAndGigs';
  static const SettingBusinessMode = 'SettingBusinessMode';
  static const Postoffer = 'Postoffer';
  static const ShareOpportunities = 'ShareOpportunities';
  static const RequestSupport = 'RequestSupport';
  static const Getnotifiedwhenyoumatch = 'Getnotifiedwhenyoumatch';
  static const SettingDatingMode = 'SettingDatingMode';
  static const EditprofileDatingmode = 'EditprofileDatingmode';
  static const Selecteyeprotectionmode = 'Selecteyeprotectionmode';
  static const Managewhoicanview = 'Managewhoicanview';
  static const Commentanonymous = 'Commentanonymous';
  static const Onlypeopleyoulikecanfindyou = 'Onlypeopleyoulikecanfindyou';
  static const Notification = 'Notification';
  static const SettingMarketplance = 'SettingMarketplance';
  static const ShareYourProducts = 'ShareYourProducts';
  static const SettingNewMode = 'SettingNewMode';
  static const Customarticle = 'Customarticle';
  static const Changestyle = 'Changestyle';
  static const Reviewlikedarticles = 'Reviewlikedarticles';
  static const Reviewyourposts = 'Reviewyourposts';
  static const SettingSocialMode = 'SettingSocialMode';
  static const PostRequest = 'PostRequest';
  static const Editprofile = 'Editprofile';
  static const Previewprofile = 'Previewprofile';
  static const ReferralProgram = 'ReferralProgram';
  static const Friends = 'Friends';
  static const morepeopletoget = 'morepeopletoget';
  static const Add = 'Add';
  static const Messages = 'Messages';
  static const SocialMode = 'SocialMode';
  static const BusinessMode = 'BusinessMode';
  static const Shoppingproducts = 'Shoppingproducts';
  static const Serviceforyouglobally = 'Serviceforyouglobally';
  static const MyOrder = 'MyOrder';
  static const ToPay = 'ToPay';
  static const ToShip = 'ToShip';
  static const ToReceive = 'ToReceive';
  static const ToRate = 'ToRate';
  static const VoiceTranslatePlan = 'VoiceTranslatePlan';
  static const benefitsOfSubscribing = 'benefitsOfSubscribing';
  static const Unlimitedaccesstovoice = 'Unlimitedaccesstovoice';
  static const Instanttranslationformessaging = 'Instanttranslationformessaging';
  static const Cancelyoursubscriptionanytime = 'Cancelyoursubscriptionanytime';
  static const Subscribe = 'Subscribe';
  static const ViewallourUpgrades = 'ViewallourUpgrades';
  static const Support = 'Support';
  static const HelpCenter = 'HelpCenter';
  static const ChatwithMultime = 'ChatwithMultime';
  static const SwitchtoSeller = 'SwitchtoSeller';
  static const Activefor6Months = 'Activefor6Months';
  static const MyProfile = 'MyProfile';
  static const Home = 'Home';
  static const Order = 'Order';
  static const Message = 'Message';
  static const Me = 'Me';
  static const Request = 'Request';
  static const Readmore = 'Readmore';
  static const Readless = 'Readless';
  static const Clickheretouploadimage = 'Clickheretouploadimage';
  static const Supportsimageformats = 'Supportsimageformats';
  static const Balance = 'Balance';
  static const Availablefunds = 'Availablefunds';
  static const ActiveService = 'ActiveService';
  static const ActiveProduct = 'ActiveProduct';
  static const Showingresults15of = 'Showingresults15of';
  static const Showingresults1 = 'Showingresults1';
  static const Viewall = 'Viewall';
  static const Addacreditcard = 'Addacreditcard';
  static const Addanewcreditcard = 'Addanewcreditcard';
  static const AddanewPayPalaccount = 'AddanewPayPalaccount';
  static const AddanewPayoneeraccount = 'AddanewPayoneeraccount';
  static const Addanewpayoutmethod = 'Addanewpayoutmethod';
  static const Cardtype = 'Cardtype';
  static const Verificationtransaction = 'Verificationtransaction';
  static const Verifyfeedialogue = 'Verifyfeedialogue';
  static const Paymentmethod = 'Paymentmethod';
  static const Showing = 'Showing';
  static const Results = 'Results';
  static const Waitingtransaction = 'Waitingtransaction';
  static const Withdrawalsuccessful = 'Withdrawalsuccessful';
  static const Withdrawalfailed = 'Withdrawalfailed';
  static const Withdrawwarningdialogue = 'Withdrawwarningdialogue';
  static const Subscriptionsummary = 'Subscriptionsummary';
  static const Linkcopiedtoclipboard = 'Linkcopiedtoclipboard';
  static const Returntobalance = 'Returntobalance';
  static const Saveimage = 'Saveimage';
  static const Tryagain = 'Tryagain';
  static const Waitingtransactiondialogue = 'Waitingtransactiondialogue';
  static const Youhavesuccessfullywithdrawn = 'Youhavesuccessfullywithdrawn';
  static const Toyourvisadebitaccount = 'Toyourvisadebitaccount';
  static const Somethingwentwrong = 'Somethingwentwrong';
  static const Transactiondate = 'Transactiondate';
  static const TransactionID = 'TransactionID';
  static const Method = 'Method';
  static const Managepayoutmethod = 'Managepayoutmethod';
  static const Carddetails = 'Carddetails';
  static const Cardnumber = 'Cardnumber';
  static const Expirydate = 'Expirydate';
  static const Cardholdername = 'Cardholdername';
  static const Deletepayoutmethod = 'Deletepayoutmethod';
  static const Creditcard = 'Creditcard';
  static const Withdrawntodate = 'Withdrawntodate';
  static const Withdraw = 'Withdraw';
  static const AsaSeller = 'AsaSeller';
  static const AsaBuyer = 'AsaBuyer';
  static const PaymentBeingCleared = 'PaymentBeingCleared';
  static const OrdersisActiveNow = 'OrdersisActiveNow';
  static const ServicesareActiveNow = 'ServicesareActiveNow';
  static const ProductsareActiveNow = 'ProductsareActiveNow';
  static const Payment = 'Payment';
  static const Chooseawithdrawmethod = 'Chooseawithdrawmethod';
  static const Withdrawamount = 'Withdrawamount';
  static const Maximumwithdrawamountis = 'Maximumwithdrawamountis';
  static const Permissiondenied = 'Permissiondenied';
  static const Unabletosaveimage = 'Unabletosaveimage';
  static const ImagesavedtoDownloads = 'ImagesavedtoDownloads';
  static const Imagesavedat = 'Imagesavedat';
  static const Therewasanerror = 'Therewasanerror';
  static const Pleasenotethatyou = 'Pleasenotethatyou';
  static const Reviewthepeople = 'Reviewthepeople';
  static const Yes = 'Yes';
  static const Dontinterested = 'Dontinterested';
  static const Block = 'Block';
  static const Theywillnotbe = 'Theywillnotbe';
  static const ReportThisDatingProfile = 'ReportThisDatingProfile';
  static const YouCanReportThisDating = 'YouCanReportThisDating';
  static const InTheDatingSection = 'InTheDatingSection';
  static const View = 'View';
  static const ThisAppWillHaveAccess = 'ThisAppWillHaveAccess';
  static const Done = 'Done';
  static const ShowSelected = 'ShowSelected';
  static const Interests = 'Interests';
  static const More = 'More';
  static const SubmitASupportRequest = 'SubmitASupportRequest';
  static const Connected = 'Connected';
  static const Views = 'Views';
  static const TotalPostsAcrossAllPlatforms = 'TotalPostsAcrossAllPlatforms';
  static const MyAddresses = 'MyAddresses';
  static const CreditDebitCard = 'CreditDebitCard';
  static const NotificationSetting = 'NotificationSetting';
  static const ConnectWithSuggestedAccounts = 'ConnectWithSuggestedAccounts';
  static const InviteAfriendToContinue = 'InviteAfriendToContinue';
  static const SearchYourContacts = 'SearchYourContacts';
  static const ContactsUsingPhoneNumber = 'ContactsUsingPhoneNumber';
  static const AutoAddFirend = 'AutoAddFirend';
  static const AutomaticallyAddContactsAsFriends = 'AutomaticallyAddContactsAsFriends';
  static const OK = 'OK';
  static const AddYourContact = 'AddYourContact';
  static const CoppyLink = 'CoppyLink';
  static const CopyLinkToShare = 'CopyLinkToShare';
  static const or = 'or';
  static const PickBestFriendsClients = 'PickBestFriendsClients';
  static const ImportYourContacts = 'ImportYourContacts';
  static const StrongbodyNeverTexts = 'StrongbodyNeverTexts';
  static const Import = 'Import';
  static const SKIP = 'SKIP';
  static const CreateNewService = 'CreateNewService';
  static const CreatedServiceCompleted = 'CreatedServiceCompleted';
  static const ShareYourServices = 'ShareYourServices';
  static const OrCopyLink = 'OrCopyLink';
  static const CreateYourService = 'CreateYourService';
  static const Medical = 'Medical';
  static const Nutrition = 'Nutrition';
  static const Physical = 'Physical';
  static const Mental = 'Mental';
  static const Pharmacy = 'Pharmacy';
  static const Beauty = 'Beauty';
  static const Child = 'Child';
  static const Family = 'Family';
  static const Elderly = 'Elderly';
  static const MedicalTravel = 'Medical Travel';
  static const MedSupport = 'MedSupport';
  static const FreelanceDigitalWork = 'Freelance Digital Work';
  static const FreelancePhysicalWork = 'Freelance Physical Work';
  static const FreelanceCreativeWork = 'Freelance Creative Work';
  static const FreelanceAdministrativeWork = 'Freelance Administrative Work';
  static const FreelanceTechnicalWork = 'Freelance Technical Work';
  static const Massage = 'Massage';
  static const CategoryServices = 'CategoryServices';
  static const certificate = 'certificate';
  static const field_optional = 'field_optional';
  static const certificate_description = 'certificate_description';
  static const supported_file_types = 'supported_file_types';
  static const add_file = 'add_file';
  static const self_declared_info = 'self_declared_info';
  static const ServiceDescription = 'ServiceDescription';
  static const service_image = 'service_image';
  static const upload_images_for_impressions = 'upload_images_for_impressions';
  static const square_product_photo = 'square_product_photo';
  static const include_at_least_two_images = 'include_at_least_two_images';
  static const service_name = 'service_name';
  static const starting_price = 'starting_price';
  static const service_description = 'service_description';
  static const time_to_complete_order = 'time_to_complete_order';
  static const related_questions = 'related_questions';
  static const help_center = 'help_center';
  static const account = 'account';
  static const privacy_and_security = 'privacy_and_security';
  static const business_mode = 'business_mode';
  static const dating_mode = 'dating_mode';
  static const news = 'news';
  static const support_request = 'support_request';
  static const help_center_request_description = 'help_center_request_description';
  static const submit = 'submit';
  static const help_center_search_prompt = 'help_center_search_prompt';
  static const no_search_results = 'no_search_results';
  static const help_topics = 'help_topics';
  static const order_support = 'order_support';
  static const help_center_greeting = 'help_center_greeting';
  static const help_center_description = 'help_center_description';
  static const your_photo = 'your_photo';
  static const order_id = 'order_id';
  static const my_account_from_being_hacked = 'my_account_from_being_hacked';
  static const get_notified_about_suspicious_activity = 'get_notified_about_suspicious_activity';
  static const report_or_block_posts_i_dont_like = 'report_or_block_posts_i_dont_like';
  static const how_to_like_and_react_to_posts = 'how_to_like_and_react_to_posts';
  static const how_to_send_a_message = 'how_to_send_a_message';
  static const how_to_block_or_report_a_post = 'how_to_block_or_report_a_post';
  static const how_to_protect_my_account_from_being_hacked = 'how_to_protect_my_account_from_being_hacked';
  static const how_to_get_notified_about_suspicious_activity = 'how_to_get_notified_about_suspicious_activity';
  static const how_to_report_or_block_posts_i_dont_like = 'how_to_report_or_block_posts_i_dont_like';
  static const how_to_block_or_report_a_post_1 = 'how_to_block_or_report_a_post_1';
  static const how_to_block_or_report_a_post_2 = 'how_to_block_or_report_a_post_2';
  static const follow_these_steps = 'follow_these_steps';
  static const When_you_click_Like = 'When_you_click_Like';
  static const When_you_like_something = 'When_you_like_something';
  static const How_to_like_something = 'How_to_like_something';
  static const Go_to_the_post = 'Go_to_the_post';
  static const ClickLike = 'ClickLike';
  static const How_to_unlike_something = 'How_to_unlike_something';
  static const post_or_photo = 'post_or_photo';
  static const ClickUnlike = 'ClickUnlike.';
  static const only_send_messages = 'only_send_messages';
  static const RecommendedArticles = 'RecommendedArticles';
  static const SUCCESS = 'SUCCESS';
  static const Thank_you_for_your_request = 'Thank_you_for_your_request';
  static const Continue = 'Continue';
  static const Click_here_to_upload_file = 'Click_here_to_upload_file';
  static const Supports_image_formats = 'Supports_image_formats';
  static const orderDetails = 'orderDetails';
  static const reviewOrder = 'reviewOrder';
  static const serviceDetail = 'serviceDetail';
  static const selectPaymentMethod = 'selectPaymentMethod';
  static const orderSummary = 'orderSummary';
  static const acceptAndPay = 'acceptAndPay';
  static const details = 'details';
  static const viewInChat = 'viewInChat';
  static const orderStatusTimeline = 'orderStatusTimeline';
  static const editMore = 'editMore';
  static const orderReceive = 'orderReceive';
  static const notifyMe = 'notifyMe';
  static const addToCalendar = 'addToCalendar';
  static const deliveryDate = 'deliveryDate';
  static const itTotally = 'itTotally';
  static const toPurchaseAt = 'toPurchaseAt';
  static const yourMoneyWill = 'yourMoneyWill';
  static const review = 'review';
  static const resolutionRegarding = 'resolutionRegarding';
  static const method = 'method';
  static const transactionDate = 'transactionDate';
  static const transactionCode = 'transactionCode';
  static const error_required = 'error_required';
  static const error_maxLength = 'error_maxLength';
  static const error_emoji = 'error_emoji';
  static const error_email_invalid = 'error_email_invalid';
  static const error_password_invalid = 'error_password_invalid';
  static const error_password_mismatch = 'error_password_mismatch';
  static const error_phone_invalid = 'error_phone_invalid';
  static const fieldOptional = 'fieldOptional';
  static const supportedFileTypes = 'supportedFileTypes';
  static const addFile = 'addFile';
  static const selfDeclaredInfo = 'selfDeclaredInfo';
  static const certificateDescription = 'certificateDescription';
  static const error_special_characters = 'error_special_characters';
  static const describeTheReason = 'describeTheReason';
  static const whoCanTextingYou = 'whoCanTextingYou';
  static const dataSynchronization = 'dataSynchronization';
  static const backupOptions = 'backupOptions';
  static const everyday = 'everyday';
  static const GetNotifiedWhenYouMatch = 'GetNotifiedWhenYouMatch';
  static const GetNotifiedWhenIHaveSomething = 'GetNotifiedWhenIHaveSomething';
  static const yourProfileGigsHidden = 'yourProfileGigsHidden';
  static const activeOrdersCancelled = 'activeOrdersCancelled';
  static const cantReactivateGigs = 'cantReactivateGigs';
  static const TitleOfRequest = 'TitleOfRequest';
  static const DescriptionOfRequest = 'DescriptionOfRequest';

}
