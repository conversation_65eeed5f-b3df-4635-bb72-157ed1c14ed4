import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:html/dom.dart' as dom;
import 'package:html/parser.dart' as html_parser;
import 'package:multime_app/core/model/offer/offers.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class TFormatter {
  static List<String> parseStringList(String text) {
    // Loại bỏ các ký tự đầu cuối nếu cần (nếu text bị bọc bởi dấu nháy kép thừa)
    final cleaned = text.trim();
    // Dùng RegExp để tách các phần tử trong mảng chuỗi
    final matches = RegExp(r'"(.*?)"').allMatches(cleaned);
    return matches
        .map((m) => m.group(1) ?? '')
        .where((e) => e.isNotEmpty)
        .toList();
  }

  static String formatDate(DateTime? date) {
    if (date == null) return '';
    final months = [
      '',
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[date.month]} ${date.day}, ${date.year}';
  }

  static String htmlToFormattedText(String htmlString) {
    final document = html_parser.parse(htmlString);
    final buffer = StringBuffer();

    void parseNode(dom.Node node) {
      if (node is dom.Element) {
        switch (node.localName) {
          case 'h1':
          case 'h2':
          case 'h3':
          case 'h4':
            buffer.writeln('\n${node.text.toUpperCase()}\n');
            break;
          case 'b':
          case 'strong':
            buffer.write('**');
            node.nodes.forEach(parseNode);
            buffer.write('**');
            break;
          case 'i':
          case 'em':
            buffer.write('_');
            node.nodes.forEach(parseNode);
            buffer.write('_');
            break;
          case 'u':
            buffer.write('<u>');
            node.nodes.forEach(parseNode);
            buffer.write('</u>');
            break;
          case 'ul':
          case 'ol':
            for (var child in node.children) {
              if (child.localName == 'li') {
                buffer.writeln('- ${child.text.trim()}');
              }
            }
            buffer.writeln();
            break;
          case 'li':
            buffer.writeln('- ${node.text.trim()}');
            break;
          case 'br':
            buffer.writeln();
            break;
          case 'p':
            node.nodes.forEach(parseNode);
            buffer.writeln('\n');
            break;
          case 'a':
            final href = node.attributes['href'] ?? '';
            buffer.write('[${node.text}]($href)');
            break;
          case 'hr':
            buffer.writeln('\n---------------------\n');
            break;
          case 'blockquote':
            buffer.write('\n> ');
            node.nodes.forEach(parseNode);
            buffer.writeln();
            break;
          case 'code':
            buffer.write('`');
            node.nodes.forEach(parseNode);
            buffer.write('`');
            break;
          case 'pre':
            buffer.write('\n```\n');
            node.nodes.forEach(parseNode);
            buffer.write('\n```\n');
            break;
          case 'span':
          case 'div':
            // Đối với span/div, chỉ parse nội dung bên trong (giữ format inline)
            node.nodes.forEach(parseNode);
            break;
          default:
            // Đối với mọi thẻ khác, luôn parse child node đệ quy
            node.nodes.forEach(parseNode);
        }
      } else if (node is dom.Text) {
        buffer.write(node.text);
      }
    }

    final body = document.body;
    if (body != null) {
      for (var node in body.nodes) {
        parseNode(node);
      }
    }
    // Loại bỏ nhiều dòng trống liên tiếp
    return buffer.toString().replaceAll(RegExp(r'\n{3,}'), '\n\n').trim();
  }

  static String formatPrice(double price) {
    return price % 1 == 0 ? price.toInt().toString() : price.toStringAsFixed(2);
  }

  static String formatDateTime(DateTime dateTime) {
    return DateFormat('HH:mm dd-MM-yyyy').format(dateTime);
  }

  static Future<void> downloadOfferAttachment(
    BuildContext context,
    OfferFile file,
  ) async {
    try {
      if (await Permission.manageExternalStorage.request().isGranted) {
        final dio = Dio();
        final directory = await getApplicationDocumentsDirectory();
        final path = '${directory.path}/${file.name}';

        await dio.download(file.url, path);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Attachment \'${file.name}\' downloaded successfully.'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Permission denied. Please allow storage access to download files.'),
          ),
        );
      }
    } catch (error) {
      debugPrint('Failed to download attachment: $error');
    }
  }
}
