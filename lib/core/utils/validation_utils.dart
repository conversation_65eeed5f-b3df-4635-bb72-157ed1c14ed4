import 'package:easy_localization/easy_localization.dart';

import '../l10n/locale_keys.g.dart';

class ValidationUtils {
  const ValidationUtils._();

  static bool isEmpty(String value) => value.trim().isNotEmpty;

  static bool isValidPassword(String password) {
    return RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\s]{8,}$')
        .hasMatch(password);
  }

  static bool isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return true;
    return RegExp(r'^[0-9]{10}\$').hasMatch(phoneNumber);
  }

  static bool isValidEmail(String email) {
    final regex = RegExp(r'^[a-zA-Z0-9._+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return regex.hasMatch(email.trim());
  }

  static bool containsEmoji(String text) {
    return RegExp(
      r'[😀-🙏'
      r'🌀-🗿'
      r'🚀-🛿'
      r'🜀-🝿'
      r'🞀-🟿'
      r'🠀-🣿'
      r'🤀-🧿'
      r'🨀-🩯'
      r'🩰-🫿'
      r'☀-⛿'
      r'✀-➿]',
      unicode: true,
    ).hasMatch(text);
  }

  static bool hasOnlyValidCharacters(String text) {
    // Cho phép: chữ cái (có dấu), số, khoảng trắng, dấu nháy đơn, dấu gạch nối, dấu chấm.
    return RegExp(r"^[\p{L}0-9'.-]+$", unicode: true).hasMatch(text);
  }
}

class ValidateForm {
  static const int maxLength = 150;

  static String? validateOrderId(String? orderId) {
    final value = orderId?.trim() ?? '';

    if (value.isEmpty) {
      return 'error_required'.tr(namedArgs: {'field': 'Order ID'});
    }

    // Không cho phép bất kỳ khoảng trắng nào
    if (value.contains(RegExp(r'\s'))) {
      return 'Order ID cannot contain spaces';
    }

    if (!ValidationUtils.hasOnlyValidCharacters(value)) {
      return 'error_special_characters'.tr(namedArgs: {'field': 'Order ID'});
    }

    return _validateLengthAndEmoji(value, 'Order ID');
  }

  static String? _validateLengthAndEmoji(String value, String field) {
    if (value.length > maxLength) {
      return 'error_maxLength'
          .tr(namedArgs: {'field': field, 'max': '$maxLength'});
    }
    if (ValidationUtils.containsEmoji(value)) {
      return 'error_emoji'.tr(namedArgs: {'field': field});
    }
    return null;
  }

  static String? validateFullName(String? name) {
    final value = name?.trim() ?? '';
    final field = LocaleKeys.publicName.tr();

    if (value.isEmpty) return 'error_required'.tr(namedArgs: {'field': field});
    if (!ValidationUtils.hasOnlyValidCharacters(value)) {
      return 'error_special_characters'.tr(namedArgs: {'field': field});
    }
    return _validateLengthAndEmoji(value, field);
  }

  static String? validateEmail(String? email) {
    final value = email?.trim() ?? '';
    final field = LocaleKeys.emailLabel.tr();

    if (value.isEmpty) {
      return 'error_required'.tr(namedArgs: {'field': field});
    }

    if (!ValidationUtils.isValidEmail(value)) {
      return 'error_email_invalid'.tr();
    }

    return _validateLengthAndEmoji(value, field);
  }

  static String? validatePassword(String? password) {
    final value = password?.trim() ?? '';
    if (value.isEmpty)
      return 'error_required'.tr(namedArgs: {'field': 'Password'});
    if (!ValidationUtils.isValidPassword(value)) {
      return 'error_password_invalid'.tr();
    }
    return _validateLengthAndEmoji(value, 'Password');
  }

  static String? matchPassword(String? password, String? confirmPassword) {
    if ((password ?? '').trim() != (confirmPassword ?? '').trim()) {
      return 'error_password_mismatch'.tr();
    }
    return null;
  }

  static String? validateGenericField(String? value, String field) {
    final trimmed = value?.trim() ?? '';
    if (trimmed.isEmpty)
      return 'error_required'.tr(namedArgs: {'field': field});
    return _validateLengthAndEmoji(trimmed, field);
  }

  static String? validateCountry(String? country) =>
      validateGenericField(country, 'Country');

  static String? validateCountrySelection(int? countryId) {
    if (countryId == null || countryId == 0) {
      return 'error_required'.tr(namedArgs: {'field': 'Country'});
    }
    return null;
  }

  static String? validateOccupation(String? occupation) {
    final value = occupation?.trim() ?? '';
    final field = LocaleKeys.occupation.tr();

    if (value.isEmpty) return 'error_required'.tr(namedArgs: {'field': field});
    if (!ValidationUtils.hasOnlyValidCharacters(value)) {
      return 'error_special_characters'.tr(namedArgs: {'field': field});
    }
    return _validateLengthAndEmoji(value, field);
  }

  static String? validatePhone(String? phone) {
    final value = phone?.trim() ?? '';
    if (value.isEmpty)
      return 'error_required'.tr(namedArgs: {'field': 'Phone'});
    if (!ValidationUtils.isValidPhoneNumber(value)) {
      return 'error_phone_invalid'.tr();
    }
    return null;
  }

  static String? validateConfirmPassword(
      String? confirmPassword, String? originalPassword) {
    final value = confirmPassword?.trim() ?? '';
    if (value.isEmpty) {
      return 'error_required'.tr(namedArgs: {'field': 'Confirm password'});
    }
    if (value != (originalPassword ?? '').trim()) {
      return 'error_password_mismatch'.tr();
    }
    return null;
  }
}
