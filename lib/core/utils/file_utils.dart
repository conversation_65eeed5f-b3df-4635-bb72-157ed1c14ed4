import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';

Future<MultipartFile> convertMultipartFile(String imageSelectPath) async {
  XFile imageSelect;
  if (imageSelectPath.startsWith('http')) {
    imageSelect = await getImageXFileByUrl(imageSelectPath);
  } else {
    imageSelect = XFile(imageSelectPath);
  }
  return MultipartFile.fromFileSync(imageSelect.path,
      filename: imageSelect.name,
      contentType: DioMediaType(
          lookupMimeType(imageSelect.path) ?? 'image/jpeg',
          Headers.multipartFormDataContentType)
  );
}

Future<XFile> getImageXFileByUrl(String url) async {
  var file = await DefaultCacheManager().getSingleFile(url);
  XFile result = XFile(file.path);
  return result;
}
