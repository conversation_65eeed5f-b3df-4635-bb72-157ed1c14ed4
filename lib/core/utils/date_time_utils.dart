import 'dart:ui';

import 'package:intl/intl.dart';
import 'package:pubnub/pubnub.dart';

String getTimeAgo(DateTime created, Locale locale) {
  final Duration diff = DateTime.now().difference(created);

  if (diff.inMinutes < 1) {
    return "Just now";
  } else if (diff.inHours < 1) {
    return "${diff.inMinutes} minutes ago";
  } else if (diff.inHours < 24) {
    return "${diff.inHours} hours ago";
  } else if (diff.inDays < 2) {
    return "${diff.inDays} day ago";
  } else {
    return "${diff.inDays} days ago";
  }
}

String formatDuration(int seconds) {
  final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
  final secs = (seconds % 60).toString().padLeft(2, '0');
  return '$minutes:$secs';
}

String formatTimetoken(String timetoken) {
  if (BigInt.tryParse(timetoken) == null) {
    return '';
  }
  return DateFormat('hh:mm a', 'en_US').format(
    Timetoken(
      BigInt.parse(timetoken),
    ).toDateTime(),
  );
}

String calculateTimetokenDifference(String timetoken) {
  if (BigInt.tryParse(timetoken) == null) {
    return '';
  }

  final time = Timetoken(BigInt.parse(timetoken)).toDateTime();
  final now = DateTime.now();
  final difference = now.difference(time);

  if (difference.inMinutes < 1) {
    return 'Just now';
  } else if (difference.inMinutes < 60) {
    final minutes = difference.inMinutes;
    return minutes == 1 ? 'A minute ago' : '$minutes minutes ago';
  } else if (difference.inHours < 24) {
    final hours = difference.inHours;
    return hours == 1 ? 'An hour ago' : '$hours hours ago';
  } else if (difference.inDays < 7) {
    final days = difference.inDays;
    return days == 1 ? 'A day ago' : '$days days ago';
  } else if (difference.inDays < 30) {
    final weeks = (difference.inDays / 7).floor();
    return weeks == 1 ? 'A week ago' : '$weeks weeks ago';
  } else if (difference.inDays < 365) {
    final months = (difference.inDays / 30).floor();
    return months == 1 ? 'A month ago' : '$months months ago';
  } else {
    final years = (difference.inDays / 365).floor();
    return years == 1 ? 'A year ago' : '$years years ago';
  }
}

String formatOfferDateTime(DateTime dateTime) {
  return DateFormat('HH:mm:ss, MMMM dd, yyyy', 'en_US').format(
    dateTime.isUtc ? dateTime.toLocal() : dateTime,
  );
}
