import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:multime_app/core/themes/theme.dart';

Future<String> showImageChoices({required BuildContext context}) async {
  String? imgPath;

  imgPath = await showModalBottomSheet<String?>(
    backgroundColor: Colors.white,
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(10.0),
    ),
    builder: (BuildContext context) {
      return FractionallySizedBox(
        heightFactor: .6,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(20.h),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 10),
              child: Text(
                'Select Image',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(),
            ListTile(
              leading: Icon(Icons.camera_alt_outlined,
                  color: Theme.of(context).textPrimary(context)),
              title: Text('Camera',
                  style:
                      TextStyle(color: Theme.of(context).textPrimary(context))),
              onTap: () async {
                final pickedPath =
                    await pickImage(camera: true, context: context);
                context.pop(pickedPath); // Always pop, even if empty
              },
            ),
            ListTile(
              leading: Icon(Icons.image,
                  color: Theme.of(context).textPrimary(context)),
              title: Text('Gallery',
                  style:
                      TextStyle(color: Theme.of(context).textPrimary(context))),
              onTap: () async {
                final pickedPath =
                    await pickImage(camera: false, context: context);
                context.pop(pickedPath); // Always pop, even if empty
              },
            ),
          ],
        ),
      );
    },
  );
  return imgPath ?? '';
}

Future<String> pickImage(
    {bool camera = false, required BuildContext context}) async {
  try {
    final ImagePicker picker = ImagePicker();
    final XFile? pickedFile = await picker.pickImage(
      source: camera ? ImageSource.camera : ImageSource.gallery,
      maxWidth: 1800,
      maxHeight: 1800,
      imageQuality: 80, // Tăng chất lượng ảnh một chút
    );

    if (pickedFile == null) return ''; // Người dùng hủy chọn ảnh

    // Cắt ảnh ngay sau khi chọn
    final String? croppedPath = await cropImage(pickedFile.path);

    // Nếu crop thành công thì trả về ảnh đã crop, không thì trả về ảnh gốc
    return croppedPath ?? pickedFile.path;
  } catch (e) {
    print('Error picking image: $e');
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to pick image: $e')),
      );
    }
    return '';
  }
}

Future<String?> cropImage(String imagePath) async {
  try {
    // Check if source file exists before attempting to crop
    if (!File(imagePath).existsSync()) {
      print('Source image file does not exist: $imagePath');
      return null;
    }

    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imagePath,
      maxWidth: 1080,
      maxHeight: 1080,
      compressFormat: ImageCompressFormat.jpg,
      compressQuality: 90,
      aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop Image',
          toolbarColor: Colors.blue,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true, // Khóa tỷ lệ hình vuông
          hideBottomControls: false,
          showCropGrid: true,
        ),
        IOSUiSettings(
          title: 'Crop Image',
          aspectRatioLockEnabled: true,
          resetAspectRatioEnabled: false,
          rectX: 1,
          rectY: 1,
        ),
      ],
    );

    // Verify the cropped file exists before returning
    if (croppedFile?.path != null && File(croppedFile!.path).existsSync()) {
      return croppedFile.path;
    } else {
      print('Cropped file was not created or does not exist');
      return null;
    }
  } catch (e) {
    print('Error cropping image: $e');
    return null;
  }
}
