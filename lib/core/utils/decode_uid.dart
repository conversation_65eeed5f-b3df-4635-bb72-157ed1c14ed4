import 'dart:convert';

class DecodeUID {
  static int? decodeUID(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64.normalize(payload);
      final decoded = utf8.decode(
        base64Url.decode(normalized),
      );
      final map = json.decode(decoded);

      return map['id'];
    } catch (e) {
      print('Error decoding payload in JWT token: $e');
      return null;
    }
  }
}
