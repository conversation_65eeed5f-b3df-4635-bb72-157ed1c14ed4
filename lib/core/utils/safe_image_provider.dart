import 'dart:io';
import 'package:flutter/material.dart';

/// Utility class for safely creating ImageProvider instances
/// Handles missing files and provides fallbacks
class SafeImageProvider {
  /// Creates a safe ImageProvider from a path
  /// Returns a fallback ImageProvider if the file doesn't exist
  static ImageProvider fromPath(
    String? imagePath, {
    ImageProvider? fallback,
  }) {
    if (imagePath == null || imagePath.isEmpty) {
      return fallback ?? const AssetImage('assets/image/logo.png');
    }

    // Network image
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return NetworkImage(imagePath);
    }

    // Local file
    if (imagePath.startsWith('/')) {
      final file = File(imagePath);
      if (file.existsSync()) {
        return FileImage(file);
      } else {
        // File doesn't exist, use fallback
        return fallback ?? const AssetImage('assets/image/logo.png');
      }
    }

    // Asset path or other format, try as NetworkImage
    return NetworkImage(imagePath);
  }

  /// Creates a safe ImageProvider specifically for avatars
  static ImageProvider forAvatar(String? imagePath) {
    return fromPath(
      imagePath,
      fallback: const AssetImage('assets/image/logo.png'),
    );
  }

  /// Creates a safe ImageProvider specifically for cover images
  static ImageProvider forCover(String? imagePath) {
    return fromPath(
      imagePath,
      fallback: const AssetImage('assets/profile/BG.png'),
    );
  }

  /// Validates if a file path exists and is readable
  static bool isValidFilePath(String? path) {
    if (path == null || path.isEmpty) return false;
    if (path.startsWith('http')) return true; // Assume network paths are valid

    try {
      return File(path).existsSync();
    } catch (e) {
      return false;
    }
  }
}
