import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

/// Utility class for safely handling CachedNetworkImage operations
class SafeCachedImage {
  /// Validates if a URL is likely to be a valid image URL
  static bool isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;

    // Basic URL validation
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return false;
    }

    // Assume all valid HTTP/HTTPS URLs can potentially be images
    return true;
  }

  /// Creates a safe CachedNetworkImageProvider with validation
  static CachedNetworkImageProvider? safeProvider(String? imageUrl) {
    if (!isValidImageUrl(imageUrl)) {
      return null;
    }

    try {
      return CachedNetworkImageProvider(imageUrl!);
    } catch (e) {
      print('Error creating CachedNetworkImageProvider: $e');
      return null;
    }
  }

  /// Creates a safe CachedNetworkImage widget with proper error handling
  static Widget safeImage({
    required String? imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    if (!isValidImageUrl(imageUrl)) {
      return errorWidget ?? _buildDefaultErrorWidget(width, height);
    }

    return CachedNetworkImage(
      imageUrl: imageUrl!,
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder != null ? (context, url) => placeholder : null,
      errorWidget: errorWidget != null
          ? (context, url, error) => errorWidget
          : (context, url, error) => _buildDefaultErrorWidget(width, height),
    );
  }

  /// Default error widget for failed image loads
  static Widget _buildDefaultErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.broken_image,
        color: Colors.grey[400],
        size: (width != null && height != null)
            ? (width < height ? width : height) * 0.3
            : 24,
      ),
    );
  }
}
