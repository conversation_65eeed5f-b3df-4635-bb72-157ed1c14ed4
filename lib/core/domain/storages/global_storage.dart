import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/shared/models/user/user_model.dart';

class GlobalStorageKey {
  const GlobalStorageKey._();

  static const globalStorage = 'globalStorage';
  static const isDarkMode = 'isDarkMode';
  static const languageCode = 'languageCode';
  static const accessToken = 'access_token';
  static const isLoggedIn = 'is_logged_in';
  static const user = 'user';
  static const hobby = 'hobby';
  static const searchHistory = 'search_history';
  static const searchServiceHistory = 'search_service_history';
  static const currentAppMode = 'current_app_mode';
  static const countries = 'countries';
  static const uid = 'uid';
  static const channelTokens = 'channel_tokens';
  static const userRole = 'user_role';
}

abstract class GlobalStorage {
  Future<void> init();

  UserModel? get user;

  Future<void> saveUser(UserModel user, String? userRole);

  Future<void> clearUser();

  Future<void> saveToken(String token);

  Future<void> saveUID(int uid);

  List<Country>? get countries;

  Future<void> setCountries(List<Country> countries);

  bool get darkMode;

  set darkMode(bool isDarkMode);

  Locale get locale;

  set locale(Locale locale);

  // New authentication methods
  String? get accessToken;

  int? get uid;

  // String? get userId;
  bool get isLoggedIn;

  String? get userRole;

  String get currentAppMode;

  Future<void> setCurrentAppMode(String mode);

  ValueNotifier<String> get currentAppModeNotifier;

  // Future<void> updateAuthenticationState({
  //   required String? token,
  //   required String? userId,
  // });

  // Future<void> clearAuthenticationState();

  // Search History methods
  List<String> getSearchHistories(String key);

  Future<void> addSearchHistory(String query, String key);

  List<Map<String, dynamic>> getSearchHistoryItems(String key);

  Future<void> addSearchHistoryItem(Map<String, dynamic> item, String key);

  Future<void> removeSearchHistoryItem(int index, {required String key});

  /// Removes a search history item at the specified index.
  Future<void> clearSearchHistoriesItems({required String key});

  Future<void> removeSearchHistory(int index, {required String key});

  Future<void> clearSearchHistories({required String key});

  // PubNub's channel tokens
  Map<String, String> get channelTokens;

  Future<void> saveChannelToken(String channelName, String token);

  Future<void> removeChannelTokens();
}

class GlobalStorageImpl implements GlobalStorage {
  late Box _box;
  final ValueNotifier<String> _currentAppModeNotifier =
      ValueNotifier<String>('social');

  @override
  Future<void> init() async {
    _box = await Hive.openBox('globalStorage');

    _currentAppModeNotifier.value =
        _box.get(GlobalStorageKey.currentAppMode, defaultValue: 'social');
  }

  @override
  bool get darkMode {
    return _box.get(GlobalStorageKey.isDarkMode, defaultValue: false);
  }

  @override
  set darkMode(bool isDarkMode) {
    _box.put(GlobalStorageKey.isDarkMode, isDarkMode);
  }

  @override
  Locale get locale {
    String languageCode =
        _box.get(GlobalStorageKey.languageCode, defaultValue: 'en');
    return Locale(languageCode);
  }

  @override
  set locale(Locale locale) {
    _box.put(GlobalStorageKey.languageCode, locale.languageCode);
  }

  @override
  String? get accessToken {
    return _box.get(GlobalStorageKey.accessToken);
  }

  @override
  int? get uid {
    return _box.get(GlobalStorageKey.uid);
  }

  // @override
  // String? get userId {
  //   return _box.get(GlobalStorageKey.userId);
  // }
  @override
  UserModel? get user {
    try {
      final data = _box.get(GlobalStorageKey.user);

      if (data == null) {
        return null;
      }

      if (data is Map) {
        final userMap = Map<String, dynamic>.from(data);

        final user = UserModel.fromJson(userMap);

        return user;
      }

      return null;
    } catch (e) {
      _box.delete(GlobalStorageKey.user);
      return null;
    }
  }

  @override
  Future<void> saveUser(UserModel user, String? userRole) async {
    try {
      final userJson = user.toJson();

      await _box.put(GlobalStorageKey.user, userJson);
      await _box.put(GlobalStorageKey.isLoggedIn, true);
      await _box.put(GlobalStorageKey.userRole, userRole);
    } catch (e) {
      debugPrint("❌ Error saving user: $e");
      rethrow;
    }
  }

  @override
  Future<void> clearUser() async {
    try {
      await Future.wait([
        _box.delete(GlobalStorageKey.user),
        _box.delete(GlobalStorageKey.isLoggedIn),
        _box.delete(GlobalStorageKey.accessToken),
      ]);

      debugPrint("✅ User data cleared successfully");
    } catch (e) {
      debugPrint("❌ Error clearing user data: $e");
      rethrow;
    }
  }

  @override
  bool get isLoggedIn {
    return _box.get(GlobalStorageKey.isLoggedIn, defaultValue: false);
  }

  @override
  String? get userRole {
    return _box.get(GlobalStorageKey.userRole);
  }

  @override
  Future<void> saveToken(String? token) async {
    await _box.put(GlobalStorageKey.accessToken, token);
  }

  @override
  Future<void> saveUID(int uid) async {
    await _box.put(GlobalStorageKey.uid, uid);
  }

  @override
  Future<void> addSearchHistory(String query, String key) async {
    if (query.trim().isEmpty) return;

    final history = getSearchHistories(key);

    history.remove(query);
    history.insert(0, query);

    const maxSearchHistories = 10;
    if (history.length > maxSearchHistories) {
      history.removeLast();
    }

    await _box.put(key, history);
  }

  @override
  Future<void> clearSearchHistories({required String key}) async {
    await _box.put(key, <String>[]);
  }

  @override
  List<String> getSearchHistories(key) {
    final history = _box.get(
      key,
      defaultValue: <String>[],
    );
    return List<String>.from(history ?? []);
  }

  @override
  Future<void> removeSearchHistory(int index, {required String key}) async {
    final history = getSearchHistories(key);

    if (index >= 0 && index < history.length) {
      history.removeAt(index);
      await _box.put(key, history);
    }
  }

  // App Mode methods
  @override
  String get currentAppMode {
    return _currentAppModeNotifier.value;
  }

  @override
  ValueNotifier<String> get currentAppModeNotifier => _currentAppModeNotifier;

  @override
  Future<void> setCurrentAppMode(String mode) async {
    _currentAppModeNotifier.value = mode;
    await _box.put(GlobalStorageKey.currentAppMode, mode);
    debugPrint('Current app mode set to: $mode');
  }

  @override
  List<Country>? get countries {
    final jsonList = _box.get(GlobalStorageKey.countries, defaultValue: []);
    if (jsonList is List) {
      return jsonList
          .map((json) => Country.fromJson(Map<String, dynamic>.from(json)))
          .toList();
    }
    return null;
  }

  @override
  Future<void> setCountries(List<Country> countries) {
    final jsonList = countries.map((country) => country.toJson()).toList();
    return _box.put(GlobalStorageKey.countries, jsonList);
  }

  @override
  Map<String, String> get channelTokens {
    final map = _box.get(
      GlobalStorageKey.channelTokens,
      defaultValue: <String, String>{},
    );
    return Map<String, String>.from(map);
  }

  @override
  Future<void> saveChannelToken(String channelName, String token) async {
    final map = channelTokens;
    map[channelName] = token;
    await _box.put(GlobalStorageKey.channelTokens, map);
  }

  @override
  Future<void> removeChannelTokens() async {
    await _box.delete(GlobalStorageKey.channelTokens);
    debugPrint('❌ PubNub\'s channel tokens removed!');
  }

  @override
  List<Map<String, dynamic>> getSearchHistoryItems(String key) {
    final history = _box.get(
      key,
      defaultValue: <Map<String, dynamic>>[],
    );

    if (history is List) {
      return history.map((item) {
        if (item is Map) {
          // Force cast to Map<String, dynamic>
          return Map<String, dynamic>.from(item);
        }
        return <String, dynamic>{};
      }).toList();
    }

    return <Map<String, dynamic>>[];
  }

  @override
  Future<void> addSearchHistoryItem(
      Map<String, dynamic> item, String key) async {
    try {
      final history = getSearchHistoryItems(key);

      // Remove existing item with same id if exists
      history.removeWhere((existingItem) => existingItem['id'] == item['id']);

      // Deep copy to ensure proper Map<String, dynamic> type
      final itemToStore = Map<String, dynamic>.from(item);

      // Add new item at the beginning
      history.insert(0, itemToStore);

      const maxSearchHistories = 10;
      if (history.length > maxSearchHistories) {
        history.removeLast();
      }

      await _box.put(key, history);
    } catch (e) {
      print('Error adding to search history: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearSearchHistoriesItems({required String key}) {
    return _box.put(key, <Map<String, dynamic>>[]);
  }

  @override
  Future<void> removeSearchHistoryItem(int index, {required String key}) {
    final history = getSearchHistoryItems(key);

    if (index >= 0 && index < history.length) {
      history.removeAt(index);
      return _box.put(key, history);
    }
    return Future.value();
  }
}
