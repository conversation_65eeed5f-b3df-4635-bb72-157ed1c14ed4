import 'package:equatable/equatable.dart';

class GlobalState extends Equatable {
  final Map<int, bool> globalPresence; // key: userId, value: isOnline
  final bool isPresenceSubscribed;
  
  const GlobalState({
    this.globalPresence = const {},
    this.isPresenceSubscribed = false,
  });
  
  GlobalState copyWith({
    Map<int, bool>? globalPresence,
    bool? isPresenceSubscribed,
  }) {
    return GlobalState(
      globalPresence: globalPresence ?? this.globalPresence,
      isPresenceSubscribed: isPresenceSubscribed ?? this.isPresenceSubscribed,
    );
  }
  
  @override
  List<Object?> get props => [globalPresence, isPresenceSubscribed];
}