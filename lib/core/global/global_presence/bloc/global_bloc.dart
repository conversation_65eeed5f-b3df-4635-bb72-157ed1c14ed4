import 'dart:async';
import 'dart:math';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/global/global_presence/bloc/global_event.dart';
import 'package:multime_app/core/model/chat/presence_update_model.dart';
import 'package:multime_app/core/streams/presence_stream_controller.dart'
    as presence_ctrl;
import 'package:multime_app/modules/chat/data/repository/chat_repository.dart';
import 'package:pubnub/pubnub.dart';

import '../../../../modules/chat/data/services/pubnub_service_presence.dart';
import 'global_state.dart';

class GlobalBloc extends Bloc<GlobalEvent, GlobalState> {
  final ChatRepository chatRepository;
  PubNubServicesPresence? _presenceService;
  StreamSubscription<presence_ctrl.PresenceEvent>? _eventSub;
  StreamSubscription<PresenceUpdate>? _presenceSub;

  // ✅ Lưu thông tin để resubscribe khi cần
  String? _lastAuthKey;
  int? _lastUserId;

  // ✅ Retry management
  final RetryManager _retryManager = RetryManager();
  final CircuitBreaker _circuitBreaker = CircuitBreaker();

  GlobalBloc({required this.chatRepository}) : super(const GlobalState()) {
    on<AppInitialized>(_onAppInitialized);
    on<GlobalPresenceUpdated>(_onGlobalPresenceUpdated);
    on<PresenceSubscriptionSuccess>(_onPresenceSubscriptionSuccess);
    on<PresenceSubscriptionCleaned>(_onPresenceSubscriptionCleaned);
    on<UserLoggedOut>(_onUserLoggedOut);
    on<AppResumed>(_onAppResumed);
    on<NetworkReconnected>(_onNetworkReconnected);
    on<NetworkLost>(_onNetworkLost);

    // ✅ Lắng nghe presence events
    _eventSub = presence_ctrl.PresenceStreamController.stream.listen(
      (event) {
        if (event is presence_ctrl.PresenceSubscribed) {
          _handlePresenceSubscribed(event);
        } else if (event is presence_ctrl.PresenceUnsubscribed) {
          _handlePresenceUnsubscribed();
        }
      },
      onError: (error) {
        print('❌ GlobalBloc: Error in presence events stream: $error');
      },
    );
  }

  // ✅ Xử lý subscribe presence
  Future<void> _handlePresenceSubscribed(
      presence_ctrl.PresenceSubscribed event) async {
    try {
      _lastAuthKey = event.authKey;
      _lastUserId = event.userId;

      // 1. Khởi tạo PubNub instance với authKey từ API
      final pubNubInstance = PubNub(
        defaultKeyset: Keyset(
          subscribeKey: '******************************************',
          publishKey: '******************************************',
          userId: UserId(event.userId.toString()),
          authKey: event.authKey,
        ),
      );

      // 2. Tạo PubNubServicesPresence với PubNub instance
      _presenceService = PubNubServicesPresence(pubNubInstance);

      // 3. Subscribe presence_global để nhận presence events real-time
      await _presenceService!.subscribePresenceGlobal();

      // 4. Lắng nghe presence updates từ presence events
      _presenceSub = _presenceService!.presenceUpdates().listen(
        (update) {
          // ✅ Cập nhật presence cho user cụ thể
          add(GlobalPresenceUpdated(
            userId: update.userId,
            isOnline: update.isOnline,
          ));
        },
        onError: (error) {
          print('❌ GlobalBloc: Error in presence updates stream: $error');

          // ✅ Sử dụng Circuit Breaker và Retry Manager
          if (_circuitBreaker.shouldAllowRequest() && _retryManager.canRetry) {
            final delay = _retryManager.getRetryDelay();

            _retryManager.incrementRetry();
            Future.delayed(delay, () {
              if (_presenceService != null &&
                  _lastAuthKey != null &&
                  _lastUserId != null) {
                _resubscribePresence(_lastAuthKey!, _lastUserId!);
              }
            });
          } else {
            print(
              '🚫 GlobalBloc: Retry blocked - Circuit: ${_circuitBreaker.isOpen ? "OPEN" : "CLOSED"}, Retry: ${_retryManager.retryCount}/${RetryManager.maxRetries}',
            );
          }
        },
        onDone: () {
          // ✅ Sử dụng Circuit Breaker và Retry Manager
          if (_circuitBreaker.shouldAllowRequest() && _retryManager.canRetry) {
            final delay = _retryManager.getRetryDelay();

            _retryManager.incrementRetry();
            Future.delayed(delay, () {
              if (_presenceService != null &&
                  _lastAuthKey != null &&
                  _lastUserId != null) {
                _resubscribePresence(_lastAuthKey!, _lastUserId!);
              }
            });
          } else {
            print(
              '🚫 GlobalBloc: Reconnect blocked - Circuit: ${_circuitBreaker.isOpen ? "OPEN" : "CLOSED"}, Retry: ${_retryManager.retryCount}/${RetryManager.maxRetries}',
            );
          }
        },
      );

      // 5. Fetch initial presence (who's online)
      final onlineUsers = await _presenceService!.fetchHereNow();

      // ✅ Cập nhật initial presence cho tất cả users
      for (final user in onlineUsers) {
        add(GlobalPresenceUpdated(
          userId: user.userId,
          isOnline: user.isOnline,
        ));
      }

      // Trigger success event
      add(PresenceSubscriptionSuccess());
    } catch (e) {
      print('❌ GlobalBloc: Failed to subscribe presence: $e');
    }
  }

  // ✅ Xử lý user logout
  Future<void> handleUserLogout(int userId) async {
    // 1. Cập nhật presence state thành offline
    add(GlobalPresenceUpdated(
      userId: userId,
      isOnline: false,
    ));

    // 2. Unsubscribe để phát LEAVE ngay
    if (_presenceService != null) {
      await _presenceService!.leavePresence();
    }

    // 3. Cleanup
    _presenceSub?.cancel();
    _presenceSub = null;
    await _presenceService?.dispose();
    _presenceService = null;

    // 4. Emit cleanup event
    add(PresenceSubscriptionCleaned());
  }

  // ✅ Xử lý unsubscribe presence
  Future<void> _handlePresenceUnsubscribed() async {
    // ✅ Yêu cầu PubNub phát LEAVE ngay lập tức
    if (_presenceService != null) {
      await _presenceService!.leavePresence();
    }

    _presenceSub?.cancel();
    _presenceSub = null;

    // ✅ Dispose PubNubServicesPresence
    await _presenceService?.dispose();
    _presenceService = null;

    // Trigger cleanup event
    add(PresenceSubscriptionCleaned());
  }

  // ✅ Xử lý global presence update
  void _onGlobalPresenceUpdated(
      GlobalPresenceUpdated event, Emitter<GlobalState> emit) {
    final newPresence = Map<int, bool>.from(state.globalPresence);
    newPresence[event.userId] = event.isOnline;

    emit(state.copyWith(globalPresence: newPresence));
  }

  // ✅ Khởi tạo app (có thể dùng để setup ban đầu)
  void _onAppInitialized(AppInitialized event, Emitter<GlobalState> emit) {
    // Có thể dùng để setup ban đầu nếu cần
  }

  // ✅ Xử lý presence subscription success
  void _onPresenceSubscriptionSuccess(
      PresenceSubscriptionSuccess event, Emitter<GlobalState> emit) {
    emit(state.copyWith(isPresenceSubscribed: true));
  }

  // ✅ Xử lý presence subscription cleaned
  void _onPresenceSubscriptionCleaned(
      PresenceSubscriptionCleaned event, Emitter<GlobalState> emit) {
    emit(state.copyWith(
      globalPresence: {},
      isPresenceSubscribed: false,
    ));
  }

  // ✅ Xử lý user logged out
  void _onUserLoggedOut(UserLoggedOut event, Emitter<GlobalState> emit) {
    // 1. Đảm bảo presence state thành offline cho tất cả users
    emit(state.copyWith(globalPresence: {}));

    // 2. Nếu đang theo dõi presence, cleanup
    if (_presenceService != null) {
      _presenceSub?.cancel();
      _presenceSub = null;
      _presenceService?.dispose();
      _presenceService = null;
    }

    // 3. Emit cleanup event
    add(PresenceSubscriptionCleaned());
  }

  // ✅ Xử lý app resumed
  void _onAppResumed(AppResumed event, Emitter<GlobalState> emit) async {
    // ✅ Luôn resubscribe khi có thông tin đăng nhập
    if (event.userId != null && _lastAuthKey != null) {
      await _resubscribePresence(_lastAuthKey!, event.userId!);
    } else {
      print(
        'ℹ️ GlobalBloc: No authKey or userId for resubscribe after app resume',
      );
    }
  }

  // ✅ Xử lý mạng reconnect
  void _onNetworkReconnected(
      NetworkReconnected event, Emitter<GlobalState> emit) async {
    // ✅ Luôn resubscribe khi có thông tin đăng nhập
    if (_lastAuthKey != null && _lastUserId != null) {
      await _resubscribePresence(_lastAuthKey!, _lastUserId!);
    } else {
      print(
        'ℹ️ GlobalBloc: No authKey or userId for resubscribe after network reconnect',
      );
    }
  }

  // ✅ Xử lý mất mạng
  void _onNetworkLost(NetworkLost event, Emitter<GlobalState> emit) {
    // Không làm gì, để PubNub TIMEOUT xử lý theo cấu hình
  }

  // ✅ Helper method để resubscribe presence
  Future<void> _resubscribePresence(String authKey, int userId) async {
    try {
      // 1. Cleanup subscription cũ nếu có
      if (_presenceSub != null) {
        _presenceSub!.cancel();
        _presenceSub = null;
      }

      if (_presenceService != null) {
        await _presenceService!.dispose();
        _presenceService = null;
      }

      // 2. Khởi tạo PubNub instance mới với authKey
      final pubNubInstance = PubNub(
        defaultKeyset: Keyset(
          subscribeKey: '******************************************',
          publishKey: '******************************************',
          userId: UserId(userId.toString()),
          authKey: authKey,
        ),
      );

      // 3. Tạo PubNubServicesPresence mới
      _presenceService = PubNubServicesPresence(pubNubInstance);

      // 4. Subscribe presence_global
      await _presenceService!.subscribePresenceGlobal();

      // 5. Lắng nghe presence updates
      _presenceSub = _presenceService!.presenceUpdates().listen(
        (update) {
          // ✅ Cập nhật presence cho user cụ thể
          add(GlobalPresenceUpdated(
            userId: update.userId,
            isOnline: update.isOnline,
          ));
        },
        onError: (error) {
          print('❌ GlobalBloc: Error in presence updates stream: $error');
        },
        onDone: () {
          print('🔚 GlobalBloc: Presence updates stream closed');
        },
      );

      // 6. Fetch initial presence để đồng bộ ngay
      final onlineUsers = await _presenceService!.fetchHereNow();

      // 7. Cập nhật initial presence
      for (final user in onlineUsers) {
        add(GlobalPresenceUpdated(
          userId: user.userId,
          isOnline: user.isOnline,
        ));
      }

      // ✅ Reset retry count và circuit breaker khi thành công
      _retryManager.resetRetry();
      _circuitBreaker.recordSuccess();
    } catch (e) {
      print('❌ GlobalBloc: Failed to resubscribe presence: $e');
      print('❌ GlobalBloc: Stack trace: ${StackTrace.current}');

      // ✅ Sử dụng Circuit Breaker và Retry Manager
      _circuitBreaker.recordFailure();

      if (_circuitBreaker.shouldAllowRequest() && _retryManager.canRetry) {
        final delay = _retryManager.getRetryDelay();

        _retryManager.incrementRetry();
        Future.delayed(delay, () {
          if (_lastAuthKey != null && _lastUserId != null) {
            _resubscribePresence(_lastAuthKey!, _lastUserId!);
          }
        });
      } else {
        print(
          '🚫 GlobalBloc: Resubscribe retry blocked - Circuit: ${_circuitBreaker.isOpen ? "OPEN" : "CLOSED"}, Retry: ${_retryManager.retryCount}/${RetryManager.maxRetries}',
        );
      }
    }
  }

  @override
  Future<void> close() async {
    await _eventSub?.cancel();
    await _presenceSub?.cancel();
    return super.close();
  }
}

// ✅ Retry Manager với Exponential Backoff
class RetryManager {
  int _retryCount = 0;
  static const int maxRetries = 5;

  Duration getRetryDelay() {
    if (_retryCount >= maxRetries) return Duration(seconds: 60);
    return Duration(seconds: pow(2, _retryCount).toInt());
  }

  void incrementRetry() => _retryCount++;

  void resetRetry() => _retryCount = 0;

  bool get canRetry => _retryCount < maxRetries;

  int get retryCount => _retryCount;
}

// ✅ Circuit Breaker để tránh retry vô hạn
class CircuitBreaker {
  bool _isOpen = false;
  int _failureCount = 0;
  static const int _threshold = 3;
  DateTime? _lastFailureTime;

  bool get isOpen => _isOpen;

  bool get isClosed => !_isOpen;

  void recordFailure() {
    _failureCount++;
    _lastFailureTime = DateTime.now();

    if (_failureCount >= _threshold) {
      _isOpen = true;

      // Auto-close sau 1 phút
      Timer(Duration(minutes: 1), () {
        _isOpen = false;
        _failureCount = 0;
      });
    }
  }

  void recordSuccess() {
    _failureCount = 0;
    _isOpen = false;
    _lastFailureTime = null;
  }

  bool shouldAllowRequest() {
    if (_isOpen) {
      return false;
    }
    return true;
  }

  Duration? get timeSinceLastFailure {
    if (_lastFailureTime == null) return null;
    return DateTime.now().difference(_lastFailureTime!);
  }
}
