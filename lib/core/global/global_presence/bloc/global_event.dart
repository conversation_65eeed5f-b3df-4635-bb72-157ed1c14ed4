import 'package:equatable/equatable.dart';

abstract class GlobalEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class AppInitialized extends GlobalEvent {
  final bool isAuthenticated;
  final int? userId;

  AppInitialized({required this.isAuthenticated, this.userId});
}

class GlobalPresenceUpdated extends GlobalEvent {
  final int userId;
  final bool isOnline;

  GlobalPresenceUpdated({required this.userId, required this.isOnline});
}

class PresenceSubscriptionSuccess extends GlobalEvent {}

class PresenceSubscriptionCleaned extends GlobalEvent {}

class UserLoggedOut extends GlobalEvent {
  final int userId;

  UserLoggedOut({required this.userId});
}

class AppResumed extends GlobalEvent {
  final int? userId;
  AppResumed({this.userId});
}

class NetworkReconnected extends GlobalEvent {}

class NetworkLost extends GlobalEvent {}