import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../app/routers/routers_name.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../../../application/app_bar/app_bar.dart';

class SocialCheckIn extends StatefulWidget {
  const SocialCheckIn({super.key});

  @override
  State<SocialCheckIn> createState() => _SocialCheckInState();
}

class _SocialCheckInState extends State<SocialCheckIn> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.social.tr(),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                      onTap: () {
                        context.pop();
                      },
                      child: SvgPicture.asset(AppAssets.arrowLeftSvg)),

                  const Spacer(),
                  Text(
                    'Check in',
                    style: Theme.of(context).textTheme.lightHeadingSmall,
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () {
                      (context).push(RouteName.homeSocialPost);
                    },
                    child: SvgPicture.asset(
                      AppAssets.send2,
                      height: 24,
                      colorFilter: ColorFilter.mode(
                          Theme.of(context).primary(context), BlendMode.srcIn),
                    ),
                  ) // Pha trộn màu với icon),
                ],
              ),
            ),
            SizedBox(
              width: double.infinity,
              height: 52,
              child: TextField(
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Theme.of(context).greyScale100(context),
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 20,
                  ),
                  border: const OutlineInputBorder(
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Theme.of(context)
                          .greyScale500(context)
                          .withOpacity(0.3),
                      width: 0,
                    ),
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: SvgPicture.asset(
                      AppAssets.searchSvg,
                      height: 20,
                      width: 20,
                      fit: BoxFit.contain,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Theme.of(context)
                          .greyScale500(context)
                          .withOpacity(0.3),
                      width: 0,
                    ),
                  ),
                ),
                onChanged: (value) {
                  debugPrint('Từ khóa tìm kiếm: $value');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
