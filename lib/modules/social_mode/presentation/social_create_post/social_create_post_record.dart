import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_quill/extensions.dart';
import 'package:flutter_sound/public/flutter_sound_player.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../app/routers/routers_name.dart';
import '../../../application/app_bar/app_bar.dart';
import '../../../application/bottom_bar/bloc/application_bloc.dart';
import '../../../application/bottom_bar/bloc/application_event.dart';
import '../widgets/create_post/background_color_text_field.dart';
import '../widgets/create_post/bottom_action_bar.dart';
import '../widgets/create_post/color_picker.dart';
import '../widgets/create_post/custom_text_filed.dart';
import '../widgets/create_post/disable_incognito_mode.dart';
import '../widgets/create_post/recording_widget.dart';

class CreatePostRecord extends StatefulWidget {
  final bool record;
  final int totalDuration;
  final String recordedFilePath;
  const CreatePostRecord(
      {super.key,
      required this.totalDuration,
      required this.recordedFilePath,
      required this.record});

  @override
  _CreatePostRecordState createState() => _CreatePostRecordState();
}

class _CreatePostRecordState extends State<CreatePostRecord> {
  bool _postIsEmpty = true;
  bool switchValue = false;
  bool selectIcon = false;
  bool selectRecord = false;
  final TextEditingController _contentController = TextEditingController();
  Color selectedColor = Colors.orange;
  List<List<Color>> getColorsList(BuildContext context) {
    return [
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).errorBase(context)
      ],
      [
        Theme.of(context).informationBase(context),
        Theme.of(context).successBase(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).attentionBase(context)
      ],
      [
        Theme.of(context).focusHighlightBase(context),
        Theme.of(context).errorLight(context)
      ],
      [
        Theme.of(context).warningBase(context),
        Theme.of(context).informationBase(context)
      ],
      [
        Theme.of(context).focusHighlightBase(context),
        Theme.of(context).informationBase(context)
      ],
      [
        Theme.of(context).successLight(context),
        Theme.of(context).errorDark(context)
      ],
      [
        Theme.of(context).errorLight(context),
        Theme.of(context).errorLight(context).withOpacity(0.7)
      ],
      [
        Theme.of(context).whitePrimary(context).withOpacity(0.9),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).informationLight(context),
        Theme.of(context).successLight(context)
      ],
      [
        Theme.of(context).informationLight(context),
        Theme.of(context).greyScale700(context)
      ],
      [
        Theme.of(context).successDark(context),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).attentionBase(context),
        Theme.of(context).successDark(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).successLight(context),
        Theme.of(context).informationLight(context)
      ],
      [
        Theme.of(context).greyScale500(context),
        Theme.of(context).greyScale50(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).informationLight(context)
      ],
      [
        Theme.of(context).successBase(context),
        Theme.of(context).errorLight(context)
      ],
      [
        Theme.of(context).greyScale100(context),
        Theme.of(context).focusHighlightLight(context)
      ],
      [
        Theme.of(context).greyScale100(context),
        Theme.of(context).warningDark(context)
      ],
      [
        Theme.of(context).informationBase(context),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).focusHighlightDark(context),
        Theme.of(context).errorLight(context)
      ],
      [
        Theme.of(context).focusHighlightBase(context),
        Theme.of(context).successLight(context)
      ],
      [
        Theme.of(context).greyScale700(context),
        Theme.of(context).informationBase(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).errorDark(context)
      ],
    ];
  }

  FlutterSoundPlayer? _player;
  final PlayerController _playerController = PlayerController();
  bool _isPlaying = false;
  double _currentPosition = 0.0;
  late Duration _totalDuration;

  @override
  void initState() {
    super.initState();
    selectRecord = widget.record;
    _player = FlutterSoundPlayer();
    _player!.openPlayer();
    _totalDuration = Duration(seconds: widget.totalDuration);

    _loadAudio();

    _playerController.onCurrentDurationChanged.listen((duration) {
      setState(() {
        _currentPosition = duration.toDouble() / _totalDuration.inMilliseconds;
      });
    });

    _playerController.onCompletion.listen((_) async {
      await _replayRecording();
    });
  }

  Future<void> _loadAudio() async {
    await _playerController.preparePlayer(
      path: widget.recordedFilePath,
      shouldExtractWaveform: true,
    );
  }

  Future<void> _replayRecording() async {
    await _loadAudio();
    await _playerController.seekTo(0);
    await _playerController.startPlayer();
    setState(() {
      _isPlaying = true;
      _currentPosition = 0.0;
    });
  }

  Future<void> _playRecording() async {
    if (!_isPlaying) {
      await _playerController.startPlayer();
      setState(() {
        _isPlaying = true;
      });
    } else {
      await _playerController.pausePlayer();
      setState(() {
        _isPlaying = false;
      });
    }
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  bool isImage = false;
  bool isImageRecording = false;

  XFile? _image;

  final ImagePicker _picker = ImagePicker();
  final ImagePicker _pickerRecord = ImagePicker();

  Future<void> _pickImage() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _image = pickedFile;
      });
    }
  }

  Future<void> _pickImageRecord() async {
    final XFile? pickedFile =
        await _pickerRecord.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _image = pickedFile;
      });
    }
  }

  void _setSelectIcon(bool value) {
    setState(() {
      selectIcon = value;
    });
  }

  @override
  void dispose() {
    _contentController.dispose();
    _player?.stopPlayer();
    _player?.closePlayer();
    _playerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // bool hasText = _contentController.text.isNotEmpty;

    // if (!_contentController.hasListeners) {
    //   _contentController.addListener(() {
    //     if (context.mounted) {
    //       setState(() {
    //         hasText = _contentController.text.isNotEmpty;
    //       });
    //     }
    //   });
    // }
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        moden: LocaleKeys.social.tr(),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                            onTap: () {
                              context.pop();
                            },
                            child: SvgPicture.asset(AppAssets.arrowLeftSvg)),
                        const Spacer(),
                        Text(
                          LocaleKeys.createPost.tr(),
                          style: Theme.of(context).textTheme.lightHeadingSmall,
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: _postIsEmpty
                              ? null
                              : () {
                                  setState(() {
                                    _postIsEmpty = !_postIsEmpty;
                                  });
                                },
                          child: GestureDetector(
                            onTap: () {
                              context.push(RouteName.socialPage);
                              context
                                  .read<ApplicationBloc>()
                                  .add(ApplicationSelectIndex(0));
                            },
                            child: Container(
                              height: 40,
                              width: 84,
                              decoration: BoxDecoration(
                                color: Theme.of(context).errorBase(context),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  LocaleKeys.post.tr(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeMedium
                                      .copyWith(color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(8.h),
                  // if (hasText == true)
                  IncognitoSwitch(
                    switchValue: switchValue,
                    onChanged: (value) {
                      setState(() {
                        switchValue = value;
                      });
                    },
                  ),
                  Gap(16.h),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ClipOval(
                          child: Image.network(
                            switchValue == false
                                ? 'https://wallpapers.com/images/hd/doctor-pictures-l5y1qs2998u7rf0x.jpg'
                                : 'https://hacom.vn/media/lib/15-06-2021/che-do-an-danh.jpg',
                            width: 43.w,
                            height: 43.w,
                            fit: BoxFit.cover,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          LocaleKeys.strongBody.tr(),
                          style:
                              Theme.of(context).textTheme.lightBodyXLargeBold,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 20.h),
                  selectIcon == false
                      ? Column(
                          children: [
                            SocialCustomTextField(
                              controller: _contentController,
                              onChanged: (value) {
                                setState(() {
                                  _postIsEmpty = value.trim().isEmpty;
                                });
                              },
                            ),
                            RecordingWidget(
                              pickImageRecord: _pickImageRecord,
                              playRecording: _playRecording,
                              isPlaying: _isPlaying,
                              selectIcon: selectIcon,
                              selectRecord: selectRecord,
                              playerController: _playerController,
                              totalDuration: _totalDuration,
                            ),
                          ],
                        )
                      : _buildBackgroundColorTextField(),
                ],
              ),
            ),
            if (selectIcon) _buildColorPicker(),
            _buildBottomActionBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundColorTextField() {
    return BackgroundColorTextField(
      controller: _contentController,
      selectedColor: selectedColor,
      onChanged: (value) {
        setState(() {
          _postIsEmpty = value.trim().isEmpty;
        });
      },
    );
  }

  Widget _buildColorPicker() {
    double bottomPadding = MediaQuery.of(context).viewInsets.bottom;
    return Positioned(
      bottom: (isAndroid ? 70 : 100) + bottomPadding,
      left: 0,
      right: 0,
      child: ColorPicker(
        colors: getColorsList(context),
        selectedColor: selectedColor,
        onColorSelected: (Color color) {
          setState(() {
            selectedColor = color;
          });
        },
      ),
    );
  }

  Widget _buildBottomActionBar() {
    double bottomPadding = MediaQuery.of(context).viewInsets.bottom;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOut,
      padding: EdgeInsets.only(bottom: bottomPadding),
      alignment: Alignment.bottomCenter,
      child: BottomActionBar(
        height: 60,
        onPickImage: _pickImage,
        onSelectBackground: () {
          setState(() {
            selectIcon = true;
          });
        },
        onSelectIcon: (value) {
          setState(() {
            selectIcon = value;
          });
        },
      ),
    );
  }
}
