import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/extensions.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../../../application/app_bar/app_bar.dart';
import '../widgets/create_post/background_color_text_field.dart';
import '../widgets/create_post/bottom_action_bar.dart';
import '../widgets/create_post/color_picker.dart';
import '../widgets/create_post/custom_text_filed.dart';
import '../widgets/create_post/disable_incognito_mode.dart';

class SocialCreatePostAI extends StatefulWidget {
  final String? initialText;
  final List<File>? initialImages;
  final VoidCallback onPost;

  const SocialCreatePostAI({
    super.key,
    this.initialText,
    this.initialImages,
    required this.onPost,
  });

  @override
  State<SocialCreatePostAI> createState() => _SocialCreatePostAIState();
}

class _SocialCreatePostAIState extends State<SocialCreatePostAI> {
  bool switchValue = false;
  bool selectIcon = false;
  bool hasImage = false;
  bool canPost = false;
  late TextEditingController _contentController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  final List<File> _images = [];
  Color selectedColor = Colors.orange;
  List<List<Color>> getColorsList(BuildContext context) {
    return [
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).errorBase(context)
      ],
      [
        Theme.of(context).informationBase(context),
        Theme.of(context).successBase(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).attentionBase(context)
      ],
      [
        Theme.of(context).focusHighlightBase(context),
        Theme.of(context).errorLight(context)
      ],
      [
        Theme.of(context).warningBase(context),
        Theme.of(context).informationBase(context)
      ],
      [
        Theme.of(context).focusHighlightBase(context),
        Theme.of(context).informationBase(context)
      ],
      [
        Theme.of(context).successLight(context),
        Theme.of(context).errorDark(context)
      ],
      [
        Theme.of(context).errorLight(context),
        Theme.of(context).errorLight(context).withOpacity(0.7)
      ],
      [
        Theme.of(context).whitePrimary(context).withOpacity(0.9),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).informationLight(context),
        Theme.of(context).successLight(context)
      ],
      [
        Theme.of(context).informationLight(context),
        Theme.of(context).greyScale700(context)
      ],
      [
        Theme.of(context).successDark(context),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).attentionBase(context),
        Theme.of(context).successDark(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).successLight(context),
        Theme.of(context).informationLight(context)
      ],
      [
        Theme.of(context).greyScale500(context),
        Theme.of(context).greyScale50(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).informationLight(context)
      ],
      [
        Theme.of(context).successBase(context),
        Theme.of(context).errorLight(context)
      ],
      [
        Theme.of(context).greyScale100(context),
        Theme.of(context).focusHighlightLight(context)
      ],
      [
        Theme.of(context).greyScale100(context),
        Theme.of(context).warningDark(context)
      ],
      [
        Theme.of(context).informationBase(context),
        Theme.of(context).whitePrimary(context)
      ],
      [
        Theme.of(context).focusHighlightDark(context),
        Theme.of(context).errorLight(context)
      ],
      [
        Theme.of(context).focusHighlightBase(context),
        Theme.of(context).successLight(context)
      ],
      [
        Theme.of(context).greyScale700(context),
        Theme.of(context).informationBase(context)
      ],
      [
        Theme.of(context).errorBase(context),
        Theme.of(context).errorDark(context)
      ],
    ];
  }

  @override
  void initState() {
    super.initState();
    _contentController = TextEditingController(text: widget.initialText ?? '');
    _contentController.addListener(_updatePostState);
    canPost = _contentController.text.isNotEmpty ||
        (widget.initialImages?.isNotEmpty ?? false);
  }

  void _updatePostState() {
    setState(() {
      canPost = _contentController.text.isNotEmpty || _images.isNotEmpty;
    });
  }

  Future<void> _pickImage() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _images.add(File(pickedFile.path));
        canPost = true;
      });
    }
  }

  void _postContent() {
    widget.onPost();
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _contentController.removeListener(_updatePostState);
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        moden: LocaleKeys.social.tr(),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(context),
                  Gap(8.h),
                  if (canPost) _buildIncognitoSwitch(),
                  Gap(16.h),
                  _buildUserInfo(),
                  SizedBox(height: 20.h),
                  selectIcon
                      ? _buildBackgroundColorTextField()
                      : _buildPostContent(),
                  Gap(60.h),
                ],
              ),
            ),
            if (selectIcon) _buildColorPicker(),
            _buildBottomActionBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              context.pop();
            },
            child: SvgPicture.asset(AppAssets.arrowLeftSvg),
          ),
          const Spacer(),
          Text(
            LocaleKeys.createPost.tr(),
            style: Theme.of(context).textTheme.lightHeadingSmall,
          ),
          const Spacer(),
          GestureDetector(
            onTap: canPost ? _postContent : null,
            child: Container(
              height: 40,
              width: 84,
              decoration: BoxDecoration(
                color: canPost
                    ? Theme.of(context).errorBase(context)
                    : Theme.of(context).modeLightText(context),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  LocaleKeys.post.tr(),
                  style:
                      Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                            color: canPost
                                ? Colors.white
                                : Theme.of(context).greyScale600(context),
                          ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncognitoSwitch() {
    return IncognitoSwitch(
      switchValue: switchValue,
      onChanged: (value) {
        setState(() {
          switchValue = value;
        });
      },
    );
  }

  Widget _buildUserInfo() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipOval(
            child: Image.network(
              switchValue == false
                  ? 'https://wallpapers.com/images/hd/doctor-pictures-l5y1qs2998u7rf0x.jpg'
                  : 'https://hacom.vn/media/lib/15-06-2021/che-do-an-danh.jpg',
              width: 43.w,
              height: 43.w,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            LocaleKeys.strongBody.tr(),
            style: Theme.of(context).textTheme.lightBodyXLargeBold,
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SocialCustomTextField(
          controller: _contentController,
          onChanged: (value) {
            setState(() {});
          },
        ),
        SizedBox(height: 10.h),
        Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children: _images.map((image) {
            return ClipRRect(
              child: Image.file(
                image,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBackgroundColorTextField() {
    return BackgroundColorTextField(
      controller: _contentController,
      selectedColor: selectedColor,
      onChanged: (value) {
        setState(() {});
      },
    );
  }

  Widget _buildColorPicker() {
    double bottomPadding = MediaQuery.of(context).viewInsets.bottom;
    return Positioned(
      bottom: (isAndroid ? 70 : 100) + bottomPadding,
      left: 0,
      right: 0,
      child: ColorPicker(
        colors: getColorsList(context),
        selectedColor: selectedColor,
        onColorSelected: (Color color) {
          setState(() {
            selectedColor = color;
          });
        },
      ),
    );
  }

  Widget _buildBottomActionBar() {
    double bottomPadding = MediaQuery.of(context).viewInsets.bottom;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOut,
      padding: EdgeInsets.only(bottom: bottomPadding),
      alignment: Alignment.bottomCenter,
      child: BottomActionBar(
        height: 60,
        onPickImage: _pickImage,
        onSelectBackground: () {
          setState(() {
            selectIcon = true;
          });
        },
        onSelectIcon: (value) {
          setState(() {
            selectIcon = value;
          });
        },
      ),
    );
  }
}
