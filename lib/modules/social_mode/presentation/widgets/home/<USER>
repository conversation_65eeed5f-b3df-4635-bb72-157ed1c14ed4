import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/constants/app_assets.dart';

class Customize extends StatelessWidget {
  const Customize({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        height: 234,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Theme.of(context).informationBase(context),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          child: Column(
            children: [
              Text(
                'Hold on just a sec! 🎉\nWe’re about to show you what you’re most excited about! Just pick one option for now — no stress, you can totally switch it up later!',
                style: Theme.of(context).textTheme.lightBodyLargeRegular,
                textAlign: TextAlign.start,
              ),
              const SizedBox(height: 16,),
              GestureDetector(
                onTap: () {
                  Scaffold.of(context).openEndDrawer();
                },
                child: Container(
                  height: 44,
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: Theme.of(context).secondary(context),
                      borderRadius: BorderRadius.circular(8)
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(AppAssets.customWhite),
                      const SizedBox(width: 8,),
                      Text('Customize your feed',
                        style: Theme.of(context).textTheme.darkBodyLargeSemiBold,
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
