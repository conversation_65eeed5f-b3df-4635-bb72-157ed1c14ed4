import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/home/<USER>';
import 'package:multime_app/modules/social_mode/presentation/widgets/home/<USER>';


class ListTitleCustomize extends StatefulWidget {
  final String selectedItem;
  final Function(String) onItemSelected;

  const ListTitleCustomize({
    Key? key,
    required this.selectedItem,
    required this.onItemSelected,
  }) : super(key: key);

  @override
  _ListTitleCustomizeState createState() => _ListTitleCustomizeState();
}

class _ListTitleCustomizeState extends State<ListTitleCustomize> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white, // Nền trắng
      ),
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          Gap(56.h),
          ListTile(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Customize your feed',
                  style: Theme.of(context)
                      .textTheme
                      .lightHeadingMedium
                      .copyWith(fontSize: 20.sp),
                ),
                GestureDetector(
                  onTap: () {
                    context.pop();
                  },
                  child:  Icon(
                    Icons.close,
                    size: 26,
                    color: Theme.of(context).disabledDark(context),
                  ),
                ),
              ],
            ),
          ),
          ..._buildListTiles(),
        ],
      ),
    );
  }

  List<Widget> _buildListTiles() {
    final options = [
      'Random',
      'Previous message',
      'Your country',
      'Global',
      'Near you',
      'Your occupation',
      'Image',
      'Audio',
    ];

    return options.map((option) {
      return ListTile(
        title: widget.selectedItem == option
            ? SelectCustomize(tittle: option)
            : UnSelectCustomize(tittle: option),
        onTap: () {
          widget.onItemSelected(option);
          context.pop();
        },
      );
    }).toList();
  }
}
