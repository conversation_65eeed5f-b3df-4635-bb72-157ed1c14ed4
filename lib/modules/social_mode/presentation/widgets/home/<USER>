import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../data/models/social_invite_contact_model.dart';

class AddContact extends StatelessWidget {
  final List<SocialInviteContactModel> contact;

  const AddContact({super.key, required this.contact});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: contact.length,
      itemBuilder: (context, index) {
        final item = contact[index];
        return Column(
          children: [
            Container(
              height: 108.h,
              width: double.infinity,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                      width: 1, color: Theme.of(context).light<PERSON>rey(context))),
              child: Padding(
                padding: EdgeInsets.all(10.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Row(
                        children: [
                          Container(
                            height: 92.h,
                            width: 93.9.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: Image.network(
                                item.avatarUrl,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                item.name,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeSemiBold,
                              ),
                              Gap(4.h),
                              Text(
                                item.professional,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodySmallRegular
                                    .copyWith(
                                        fontWeight: FontWeight.w400,
                                        color: Theme.of(context).greyScale600(context)),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Nút Connect
                    Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          height: 36.h,
                          width: 96.w,
                          decoration: BoxDecoration(
                            color: Theme.of(context).backgroundRed(context),
                            borderRadius: BorderRadius.circular(10.r),
                            border: Border.all(
                              color: Theme.of(context).backgroundRed(context),
                              width: 2.0,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              'Connect',
                              style: Theme.of(context)
                                  .textTheme
                                  .darkBodySmallRegular
                                  .copyWith(
                                    fontWeight: FontWeight.w400,
                                  ),
                            ),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
            Gap(10.h),
          ],
        );
      },
    );
  }
}
