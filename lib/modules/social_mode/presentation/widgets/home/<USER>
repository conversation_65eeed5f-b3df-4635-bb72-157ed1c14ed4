import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';

class PostInputWidget extends StatelessWidget {
  const PostInputWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            (context).push(RouteName.homeSocialPost);
          },
          child: Container(
            height: 60.h,
            width: double.infinity,
            color: Colors.white, // Đặt màu nền nếu cần
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Ảnh đại diện người dùng
                  ClipOval(
                    child: Image.network(
                      'https://wallpapers.com/images/hd/doctor-pictures-l5y1qs2998u7rf0x.jpg',
                      width: 32
                          .w, // Đảm bảo width và height giống nhau
                      height: 32.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  // Hộp nhập nội dung
                  Expanded(
                    child: Text(
                      LocaleKeys.postHintText.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyMediumRegular,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 4.w),
                  SvgPicture.asset(AppAssets.micOutLine),
                ],
              ),
            ),
          ),
        ),
        Container(
          height: 15,
          width: double.infinity,
          color: Colors.white,
        ),
      ],
    );
  }
}
