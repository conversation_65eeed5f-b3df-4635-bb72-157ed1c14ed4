import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class UnSelectCustomize extends StatelessWidget {
  final String tittle;
  const UnSelectCustomize({super.key, required this.tittle});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      width: double.infinity,

      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text( tittle,
              style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
            ),
          ),
        ],
      ),
    );
  }
}
