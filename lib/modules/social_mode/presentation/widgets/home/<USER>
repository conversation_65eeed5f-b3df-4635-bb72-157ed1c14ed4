import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class SelectCustomize extends StatelessWidget {
  final String tittle;
  const SelectCustomize({super.key, required this.tittle});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: double.infinity,
      decoration: BoxDecoration(
          color: Theme.of(context).secondary(context),
          borderRadius: BorderRadius.circular(4)
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text( tittle,
              style: Theme.of(context).textTheme.darkBodyLargeSemiBold,
            ),
          ),
        ],
      ),
    );
  }
}
