// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/themes/theme.dart';

class BasicAppButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String title;
  final FontWeight? fontW;
  final Color? colorButton;
  final Color? colorTitle;
  final double? height;
  final double? radius;
  final double sizeTitle;
  final Color? borderColor;
  final double? borderWidth;

  const BasicAppButton({
    super.key,
    required this.onPressed,
    required this.title,
    this.colorButton,
    this.radius,
    this.fontW,
    this.height,
    this.borderColor,
    this.borderWidth,
    required this.sizeTitle,
    this.colorTitle,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: colorButton ??Theme.of(context).primary(context),
        foregroundColor: colorTitle ?? Theme.of(context).whitePrimary(context),
        minimumSize: Size.fromHeight(height ?? 80),
        shadowColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius ?? 100),
          side: BorderSide.none,
        ),
      ).copyWith(
        shape: MaterialStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius ?? 100), // Bo góc
            side: BorderSide(
              color: borderColor ?? Colors.transparent, // Màu viền
              width: borderWidth ?? 1.0, // Độ dày viền
            ),
          ),
        ),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: sizeTitle,
          fontWeight: fontW ?? FontWeight.bold,
          fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
        ),
      ),
    );
  }
}
