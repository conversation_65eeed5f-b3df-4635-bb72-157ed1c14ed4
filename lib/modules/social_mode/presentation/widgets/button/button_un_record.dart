import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/theme.dart';

class ButtonUnRecord extends StatefulWidget {
  @override
  _ButtonUnRecordState createState() => _ButtonUnRecordState();
}

class _ButtonUnRecordState extends State<ButtonUnRecord> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _borderColorAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    // Animation thay đổi màu sắc của border
    _borderColorAnimation = ColorTween(
      begin: Colors.red.withOpacity(0.5),
      end: Colors.red.withOpacity(1),
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Container(
            width: 58,
            height: 58,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _borderColorAnimation.value!,
                width: 2,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(10),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color:Theme.of(context).backgroundRed(context),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
