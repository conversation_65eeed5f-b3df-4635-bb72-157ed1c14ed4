import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/theme.dart';

class ButtonRecord extends StatefulWidget {
  @override
  _ButtonRecordState createState() => _ButtonRecordState();
}

class _ButtonRecordState extends State<ButtonRecord> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _borderColorAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _borderColorAnimation = ColorTween(
      begin: Colors.red.withOpacity(0.5),
      end: Colors.red.withOpacity(1),
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Container(
            width: 58,
            height: 58,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _borderColorAnimation.value!,
                width: 2,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(4),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration:  BoxDecoration(
                  color: Theme.of(context).backgroundRed(context),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
