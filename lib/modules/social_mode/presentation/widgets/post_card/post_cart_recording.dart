
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/post_card/post_action_widget.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/post_card/voice_translator_dialog.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_block.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_model.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_report_model.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_share.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_user_model.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/text/see_more_text.dart';
import '../../../../../core/constants/app_assets.dart';

import '../../../data/fake_data/social_fake_data.dart';
import 'comment_input_widget.dart';
import 'list_comment.dart';

class PostCartRecording extends StatefulWidget {
  String avatarUrl;
  String name;
  String recordingTitle;
  String nameId;
  String content;
  String contentTranlation;
  String country;
  List<String>? imageUrls; // Thay đổi từ một ảnh thành danh sách ảnh
  String? hasTag;

  PostCartRecording({
    super.key,
    this.imageUrls,
    required this.name,
    required this.recordingTitle,
    required this.nameId,
    required this.avatarUrl,
    required this.content,
    required this.contentTranlation,
    required this.country,
    this.hasTag,
  });

  @override
  State<PostCartRecording> createState() => _PostCardState();
}

class _PostCardState extends State<PostCartRecording> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool switchValue = false;
  late TextEditingController _commentController;
  XFile? _image;
  bool isImage = false;
  // Khởi tạo ImagePicker
  final ImagePicker _picker = ImagePicker();
  // Hàm chọn ảnh từ thư viện
  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _image = pickedFile;
      });
    }
  }

  void _onSwitchChanged() {
    setState(() {
      switchValue = !switchValue;
    });
  }

  @override
  void initState() {
    super.initState();
    _commentController = TextEditingController();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(10),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 21,
                        backgroundImage: NetworkImage(widget.avatarUrl, ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Text(
                        widget.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color:Theme.of(context).greyScale800(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                        ),
                      ),
                      SizedBox(
                        width: 12.w,
                      ),
                      SvgPicture.asset(AppAssets.locationTick),
                      SizedBox(
                        width: 8.w,
                      ),
                      Text(
                        widget.country,
                        style:Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  InkWell(
                    onTap: () {
                      if(widget.name == 'StrongBody') {
                        showBottomUserModal(context, widget.avatarUrl, widget.name);
                      }
                      if(widget.name != 'StrongBody') {
                        showBottomModal(context, widget.avatarUrl, widget.name);
                      }
                    },
                    child: SvgPicture.asset(AppAssets.more),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            SeeMoreTextWidget(
              content: widget.content,
              contentTranslation: '  ${widget.contentTranlation}',
            ),
            if (widget.hasTag != null && widget.hasTag!.isNotEmpty)
              Text(
                widget.hasTag ?? "",
                style: const TextStyle(fontSize: 14, color: Colors.blue),
              ),
            SizedBox(height: 16.h),
            // Hiển thị ảnh nếu có
            if (widget.imageUrls != null && widget.imageUrls!.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 222.h,
                    width: double.infinity,
                    decoration: BoxDecoration(
                        color: Theme.of(context).secondary(context),
                        borderRadius: BorderRadius.circular(9)
                    ),
                    child: PageView.builder(
                      controller: _pageController,
                      itemCount: widget.imageUrls?.length ?? 0,
                      onPageChanged: (index) {
                        setState(() {
                          _currentPage = index;
                        });
                      },
                      itemBuilder: (context, index) {
                        return Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 17, right: 37, top: 20),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        height: 49,
                                        width: 49,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(10), // Bo góc
                                          child: Image.network(
                                            widget.imageUrls?[index] ?? '',
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                      Gap(20.w),
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(widget.recordingTitle, style:TextStyle(
                                            fontSize: 18.sp,
                                            color: Colors.white,
                                            fontWeight: FontWeight.w700,
                                            fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                                          ),),
                                          Gap(10.h),
                                          Text(widget.nameId, style:TextStyle(
                                            fontSize: 14.sp,
                                            color: Colors.white,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                                          ),),
                                        ],
                                      )
                                    ],
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      VoiceTranslatorDialog.show(context);
                                    },
                                    child: SvgPicture.asset(AppAssets.translate),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 17, right: 17, top: 20, bottom: 15),
                              child: SvgPicture.asset(AppAssets.recording),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Spacer(),
                                GestureDetector(
                                  onTap: () {
                                    if (_currentPage > 0) {
                                      setState(() {
                                        _currentPage--;
                                      });
                                      _pageController.animateToPage(
                                        _currentPage,
                                        duration: const Duration(milliseconds: 300),
                                        curve: Curves.easeInOut,
                                      );
                                    }
                                  },
                                  child: SvgPicture.asset(AppAssets.arrowUndo),
                                ),
                                const Spacer(),
                                SvgPicture.asset(AppAssets.pause),
                                const Spacer(),
                                GestureDetector(
                                  onTap: () {
                                    if (_currentPage < (widget.imageUrls?.length ?? 1) - 1) {
                                      setState(() {
                                        _currentPage++;
                                      });
                                      _pageController.animateToPage(
                                        _currentPage,
                                        duration: const Duration(milliseconds: 300),
                                        curve: Curves.easeInOut,
                                      );
                                    }
                                  },
                                  child: SvgPicture.asset(AppAssets.arrowDo),
                                ),
                                const Spacer(),
                              ],
                            )
                          ],
                        );
                      },
                    ),
                  ),
                  // Chấm tròn nằm dưới ảnh
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SmoothPageIndicator(
                          controller: _pageController,
                          count: widget.imageUrls?.length ?? 0,
                          effect:  WormEffect(
                            dotHeight: 6.0,
                            dotWidth: 6.0,
                            activeDotColor: Theme.of(context).backgroundRed(context),
                            dotColor: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                  PostActionsWidget(
                    onComment: () {
                      showComment(context, switchValue, _commentController);
                    },
                    onShare: () {
                      showShareModal(context);
                    },),
                  SizedBox(height: 8.h),
                ],
              ),
          ],
        ),
      ),
    );
  }

  void showComment(BuildContext context, bool switchValue, TextEditingController comment) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)), // Bo góc trên
        side: BorderSide(color: Colors.white, width: 0), // Viền xanh dày 2px
      ),
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) {
        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          behavior: HitTestBehavior.opaque,
          child: StatefulBuilder(
            builder: (context, setState) {
              // Ensure 'hasText' reflects the initial state of 'comment.text'
              bool hasText = comment.text.isNotEmpty;

              // Add the listener only once
              if (!comment.hasListeners) {
                comment.addListener(() {
                  if (context.mounted) {
                    setState(() {
                      hasText = comment.text.isNotEmpty;
                    });
                  }
                });
              }

              return FractionallySizedBox(
                heightFactor: 0.72,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

                    return Padding(
                      padding: EdgeInsets.only(bottom: keyboardHeight),
                      child: Column(
                        children: [
                          Gap(16.h),
                          SvgPicture.asset(AppAssets.line126Svg),
                          Gap(12.h),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 14),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Spacer(),
                                const Spacer(),
                                Text('    Comment',
                                  style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(color: Theme.of(context).secondary(context)),
                                ),
                                const Spacer(),
                                const Spacer(),
                                SvgPicture.asset(AppAssets.send),
                              ],
                            ),
                          ),
                          CommentList(
                            comments: comments,
                            showReportModal: showReportModal,
                            showBlockModal: showBlockModal,
                          ),
                          SocialCommentInputWidget(
                            hasText: hasText,
                            switchValue: switchValue,
                            commentController: _commentController,
                            onSwitchChanged: _onSwitchChanged,
                            // onPickImage: _pickImage,
                            isImage: isImage,
                            imageUrl: '',
                          ),
                        ],),);},),);},),
        );},);
  }
}
