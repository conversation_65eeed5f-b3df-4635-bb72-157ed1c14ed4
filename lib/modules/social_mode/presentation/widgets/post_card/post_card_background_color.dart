import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/post_card/post_action_widget.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/text/see_more_text_background_color.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_block.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_model.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_report_model.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_share.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_user_model.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../data/fake_data/social_fake_data.dart';
import 'comment_input_widget.dart';
import 'list_comment.dart';


class PostCardBackgroundColor extends StatefulWidget {
  String avatarUrl;
  String name;
  String content;
  String contentTranlation;
  String country;
  String? hasTag;

  PostCardBackgroundColor({
    super.key,
    required this.name,
    required this.avatarUrl,
    required this.content,
    required this.contentTranlation,
    required this.country,
    this.hasTag,
  });

  @override
  State<PostCardBackgroundColor> createState() => _PostCardState();
}

class _PostCardState extends State<PostCardBackgroundColor> {
  final PageController _pageController = PageController();
  bool switchValue = false;
  late TextEditingController _commentController;
  XFile? _image;
  bool isImage = false;
  // Khởi tạo ImagePicker
  final ImagePicker _picker = ImagePicker();
  // Hàm chọn ảnh từ thư viện
  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _image = pickedFile;
      });
    }
  }
  void _onSwitchChanged() {
    setState(() {
      switchValue = !switchValue;
    });
  }

  @override
  void initState() {
    super.initState();
    _commentController = TextEditingController();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 21,
                        backgroundImage: NetworkImage(widget.avatarUrl, ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Text(
                        widget.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Theme.of(context).greyScale800(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                        ),
                      ),
                      SizedBox(
                        width: 12.w,
                      ),
                      SvgPicture.asset(AppAssets.locationTick),
                      SizedBox(
                        width: 8.w,
                      ),
                      Text(
                        widget.country,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ),
                  InkWell(
                    onTap: () {
                      if(widget.name == 'StrongBody')
                        showBottomUserModal(context, widget.avatarUrl, widget.name);
                      if(widget.name != 'StrongBody')
                        showBottomModal(context, widget.avatarUrl, widget.name);
                    },
                    child: SvgPicture.asset(AppAssets.more),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              decoration:  BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).attentionBase(context), // Màu bắt đầu
                    Theme.of(context).backgroundRed(context), // Màu kết thúc
                  ],
                  begin: Alignment.topLeft, // Điểm bắt đầu của gradient
                  end: Alignment.bottomRight, // Điểm kết thúc của gradient
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
                child: Column(
                  children: [
                    SeeMoreTextBackgroundColor(
                      content: widget.content,
                      contentTranslation: widget.contentTranlation,
                    ),
                    if (widget.hasTag != null && widget.hasTag!.isNotEmpty)
                      Text(
                        widget.hasTag ?? "",
                        style: const TextStyle(fontSize: 14, color: Colors.blue),
                      ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16.h),
            PostActionsWidget(
              onComment: () {
                showComment(context, switchValue, _commentController);
              },
              onShare: () {
                showShareModal(context);
              },),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  void showComment(BuildContext context, bool switchValue, TextEditingController comment) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)), // Bo góc trên
        side: BorderSide(color: Colors.white, width: 0), // Viền xanh dày 2px
      ),
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) {
        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          behavior: HitTestBehavior.opaque,
          child: StatefulBuilder(
            builder: (context, setState) {
              // Ensure 'hasText' reflects the initial state of 'comment.text'
              bool hasText = comment.text.isNotEmpty;

              // Add the listener only once
              if (!comment.hasListeners) {
                comment.addListener(() {
                  if (context.mounted) {
                    setState(() {
                      hasText = comment.text.isNotEmpty;
                    });
                  }
                });
              }

              return FractionallySizedBox(
                heightFactor: 0.72,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

                    return Padding(
                      padding: EdgeInsets.only(bottom: keyboardHeight),
                      child: Column(
                        children: [
                          Gap(16.h),
                          SvgPicture.asset(AppAssets.line126Svg),
                          Gap(12.h),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 14),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Spacer(),
                                const Spacer(),
                                Text('    Comment',
                                  style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(color: Theme.of(context).secondary(context)),
                                ),
                                const Spacer(),
                                const Spacer(),
                                SvgPicture.asset(AppAssets.send),
                              ],
                            ),
                          ),
                          CommentList(
                            comments: comments,
                            showReportModal: showReportModal,
                            showBlockModal: showBlockModal,
                          ),
                          SocialCommentInputWidget(
                            hasText: hasText,
                            switchValue: switchValue,
                            commentController: _commentController,
                            onSwitchChanged: _onSwitchChanged,
                            // onPickImage: _pickImage,
                            isImage: isImage,
                            imageUrl: '',
                          ),
                        ],),);},),);},),
        );},);
  }
}
