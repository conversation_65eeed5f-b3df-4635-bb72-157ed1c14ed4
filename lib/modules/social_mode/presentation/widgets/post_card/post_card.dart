import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/post_card/post_action_widget.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_block.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_model.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_report_model.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_share.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_user_model.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/text/see_more_text.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../data/fake_data/social_fake_data.dart';
import 'comment_input_widget.dart';
import 'list_comment.dart';


class PostCard extends StatefulWidget {
  String avatarUrl;
  String name;
  String content;
  String contentTranlation;
  String country;
  List<dynamic>? imageUrls;
  String? hasTag;

  PostCard({
    super.key,
    this.imageUrls,
    required this.name,
    required this.avatarUrl,
    required this.content,
    required this.contentTranlation,
    required this.country,
    this.hasTag,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool switchValue = false;
  late TextEditingController _commentController;
  XFile? _image;
  bool isImage = false;
  // Khởi tạo ImagePicker
  final ImagePicker _picker = ImagePicker();
  // Hàm chọn ảnh từ thư viện
  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _image = pickedFile;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _commentController = TextEditingController();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 21,
                        backgroundImage: NetworkImage(widget.avatarUrl, ),
                      ),
                      Gap(10.w),
                      Text(
                        widget.name,
                        style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                      ),
                      Gap(12.w),
                      SvgPicture.asset(AppAssets.locationTick),
                      Gap(8.w),
                      Text(
                        widget.country,
                        style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                      ),
                    ],
                  ),
                  InkWell(
                    onTap: () {
                      //o day
                      if(widget.name == 'StrongBody') {
                        showBottomUserModal(context, widget.avatarUrl, widget.name);
                      }
                      if(widget.name != 'StrongBody') {
                        showBottomModal(context, widget.avatarUrl, widget.name);
                      }
                    },
                    child: SvgPicture.asset(AppAssets.more),
                  ),
                ],
              ),
            ),
            Gap(18.h),
            SeeMoreTextWidget(
              content: widget.content,
              contentTranslation: widget.contentTranlation,
            ),
            Gap(18.h),
            // Hiển thị ảnh nếu có
            if (widget.imageUrls != null && widget.imageUrls!.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 203.h,
                    width: double.infinity,
                    child: PageView.builder(
                      controller: _pageController,
                      itemCount: widget.imageUrls?.length ?? 0,
                      onPageChanged: (index) {
                        setState(() {
                          _currentPage = index;
                        });
                      },
                      itemBuilder: (context, index) {
                        return Image.network(
                          widget.imageUrls?[index] ?? '',
                          fit: BoxFit.cover,
                        );
                      },
                    ),
                  ),
                  // Chấm tròn nằm dưới ảnh
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SmoothPageIndicator(
                          controller: _pageController,
                          count: widget.imageUrls?.length ?? 0,
                          effect:  WormEffect(
                            dotHeight: 6.0,
                            dotWidth: 6.0,
                            activeDotColor: Theme.of(context).backgroundRed(context),
                            dotColor: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Gap(8.h)
                ],
              ),
            PostActionsWidget(
              onComment: () {
                showComment(context, switchValue, _commentController);
              },
              onShare: () {
                showShareModal(context);
              },),
            Gap(8.h),
          ],
        ),
      ),
    );
  }

  void showComment(BuildContext context, bool switchValue, TextEditingController comment) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)), // Bo góc trên
        side: BorderSide(color: Colors.white, width: 0), // Viền xanh dày 2px
      ),
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) {
        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          behavior: HitTestBehavior.opaque,
          child: StatefulBuilder(
            builder: (context, setState) {
              bool hasText = comment.text.isNotEmpty;

              if (!comment.hasListeners) {
                comment.addListener(() {
                  if (context.mounted) {
                    setState(() {
                      hasText = comment.text.isNotEmpty;
                    });
                  }
                });
              }

              return FractionallySizedBox(
                heightFactor: 0.72,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

                    return Padding(
                      padding: EdgeInsets.only(bottom: keyboardHeight),
                      child: Column(
                        children: [
                          Gap(16.h),
                          SvgPicture.asset(AppAssets.line126Svg),
                          Gap(12.h),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 14),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Spacer(),
                                const Spacer(),
                                Text(
                                  '    Comment',
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightHeadingMedium
                                      .copyWith(color: Theme.of(context).secondary(context)),
                                ),
                                const Spacer(),
                                const Spacer(),
                                GestureDetector(
                                  onTap: () {
                                    FocusScope.of(context).unfocus(); // Ẩn bàn phím khi gửi bình luận
                                    // Thêm logic gửi bình luận ở đây...
                                  },
                                  child: SvgPicture.asset(AppAssets.send),
                                ),
                              ],
                            ),
                          ),
                          CommentList(
                            comments: comments,
                            showReportModal: showReportModal,
                            showBlockModal: showBlockModal,
                          ),
                          SocialCommentInputWidget(
                            hasText: hasText,
                            switchValue: switchValue,
                            commentController: _commentController,
                            onSwitchChanged: () {
                              setState(() {
                                switchValue = !switchValue;
                              });
                            },
                            // onPickImage: _pickImage,
                            isImage: isImage,
                            imageUrl: '',
                          ),
                        ],
                      ),
                    );
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }
}
