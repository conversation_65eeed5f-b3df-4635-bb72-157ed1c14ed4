import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/constants/app_assets.dart';

class SocialCommentInputWidget extends StatefulWidget {
  final bool hasText;
  final bool switchValue;
  final TextEditingController commentController;
  final VoidCallback onSwitchChanged;
  final bool isImage;
  final String imageUrl;

  const SocialCommentInputWidget({
    required this.hasText,
    required this.switchValue,
    required this.commentController,
    required this.onSwitchChanged,
    required this.isImage,
    required this.imageUrl,
    super.key,
  });

  @override
  _SocialCommentInputWidgetState createState() =>
      _SocialCommentInputWidgetState();
}

class _SocialCommentInputWidgetState extends State<SocialCommentInputWidget> {
  File? pickedImage;

  Future<void> onPickImage() async {
    final pickedFile =
    await ImagePicker().pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        pickedImage = File(pickedFile.path);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).lightGrey(context), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.hasText)
            Row(
              children: [
                Text(
                  'Anonymous',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Gap(10.w),
                SizedBox(
                  height: 26.h,
                  width: 44.w,
                  child: CupertinoSwitch(
                    value: widget.switchValue,
                    onChanged: (value) {
                      widget.onSwitchChanged();
                    },
                  ),
                ),
              ],
            ),
          Gap(10.h),
          Row(
            children: [
              ClipOval(
                child: Image.network(
                  widget.switchValue == false
                      ? 'https://wallpapers.com/images/hd/doctor-pictures-l5y1qs2998u7rf0x.jpg'
                      : 'https://hacom.vn/media/lib/15-06-2021/che-do-an-danh.jpg',
                  width: 40.w,
                  height: 40.w,
                  fit: BoxFit.cover,
                ),
              ),
              Gap(10.w),
              Expanded(
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    border:
                    Border.all(color: Theme.of(context).lightGrey(context), width: 1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: TextField(
                    controller: widget.commentController,
                    textAlignVertical: TextAlignVertical.center,
                    decoration: InputDecoration(
                      hintText: 'Respond Joe_evans',
                      hintStyle:
                      Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).greyScale400(context),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        vertical: 0,
                        horizontal: 16,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(32),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: onPickImage,
                child: Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: SvgPicture.asset(
                    AppAssets.profileCoverImage,
                    colorFilter:  ColorFilter.mode(
                      Theme.of(context).textSecondary(context),
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ],
          ),
          if (pickedImage != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12,horizontal: 56),
              child: Image.file(
                pickedImage!,
                width: 80.w,
                height: 80.h,
                fit: BoxFit.cover,
              ),
            ),
        ],
      ),
    );
  }
}
