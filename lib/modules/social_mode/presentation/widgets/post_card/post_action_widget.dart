import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/themes/app_colors.dart';

class PostActionsWidget extends StatefulWidget {
  final VoidCallback onComment;
  final VoidCallback onShare;

  const PostActionsWidget({
    super.key,
    required this.onComment,
    required this.onShare,
  });

  @override
  State<PostActionsWidget> createState() => _PostActionsWidgetState();
}

class _PostActionsWidgetState extends State<PostActionsWidget> {
  bool _isLiked = false; // Biến trạng thái Like

  void _toggleLike() {
    setState(() {
      _isLiked = !_isLiked;
    });
  }
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: _toggleLike,
            child: Row(
              children: [
                SvgPicture.asset(
                  _isLiked ?  AppAssets.favoriteSvg :AppAssets.heart_icon,
                  colorFilter: _isLiked ? const  ColorFilter.mode(Colors.red, BlendMode.srcIn) : null
                ),
                Gap(12.w),
                Text(
                  _isLiked ? 'Liked' : 'Like',
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: _isLiked ? Colors.red : AppColors.textSecondary),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: widget.onComment,
            child: Row(
              children: [
                SvgPicture.asset(AppAssets.comments),
                Gap(12.w),
                Text(
                  'Comment',
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: AppColors.textSecondary),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: widget.onShare,
            child: Row(
              children: [
                SvgPicture.asset(AppAssets.repeat),
                Gap(12.w),
                Text(
                  'Share',
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: AppColors.textSecondary),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
