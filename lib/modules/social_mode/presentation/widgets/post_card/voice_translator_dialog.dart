import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/constants/app_assets.dart';
import '../button/basic_button.dart';
import '../show_bottom/show_bottom_convert.dart';
import '../text/row_text_voice_trans.dart';

class VoiceTranslatorDialog {
  static void show(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Center(
          child: Container(
            width: MediaQuery.of(context).size.width - 28,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Welcome to\nVoice Translator',
                              style: Theme.of(context).textTheme.headlineSmall,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                         const RowTextVoiceTrans(
                          imageIcon: AppAssets.voiceInput,
                          title: 'Voice Input',
                          content: 'Hold the microphone button to record your voice',
                        ),
                         const RowTextVoiceTrans(
                          imageIcon: AppAssets.autoTrans,
                          title: 'Auto Translation',
                          content: 'Your message will be automatically translated',
                        ),
                         const RowTextVoiceTrans(
                          imageIcon: AppAssets.realTimeChat,
                          title: 'Real-time Chat',
                          content: 'Chat naturally in your preferred language',
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '\$15/month',
                              style: TextStyle(
                                fontSize: 20.sp,
                                color: Theme.of(context).backgroundRed(context),
                                fontWeight: FontWeight.w700,
                                fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                              ),
                            ),
                            Text(
                              'Fee to upgrade Pro account',
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ],
                        ),
                        Gap(16.h),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 52),
                          child: BasicAppButton(
                            onPressed: () {
                              context.pop();
                              showConvertModal(context);
                            },
                            title: 'Upgrade to Pro',
                            sizeTitle: 20,
                            height: 49,
                            colorButton:Theme.of(context).secondary(context),
                            radius: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 10,
                  right: 10,
                  child: GestureDetector(
                    onTap: () {
                      context.pop();
                    },
                    child: const Icon(
                      Icons.close,
                      size: 24,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
