import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/data/models/social_comment_model.dart';
import '../../../../../core/constants/app_assets.dart';

class CommentList extends StatelessWidget {
  final List<SocialCommentModel> comments;
  final Function(BuildContext) showReportModal;
  final Function(BuildContext, String, String) showBlockModal;

  const CommentList({
    Key? key,
    required this.comments,
    required this.showReportModal,
    required this.showBlockModal,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ListView.builder(
        itemCount: comments.length,
        itemBuilder: (context, index) {
          final person = comments[index];
          return Container(
            padding: const EdgeInsets.all(8.0),
            margin: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipOval(
                  child: Image.network(
                    person.imageUrl,
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                  ),
                ),
                const Gap(10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            person.name,
                            style: Theme.of(context).textTheme.lightHeadingXSmall,
                          ),
                          const Gap(10),
                          Text(
                            person.time,
                            style: Theme.of(context).textTheme.lightBodySmallRegular.copyWith(color: Theme.of(context).disabledBase(context)),
                          ),
                        ],
                      ),
                      const Gap(5),
                      Text(
                        person.comment,
                        style: Theme.of(context).textTheme.lightBodyLargeRegular,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Reply',
                            style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(color: Theme.of(context).disabledBase(context)),
                          ),
                          const Spacer(),
                          Text(
                            'See translation',
                            style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(color: Theme.of(context).disabledBase(context)),
                          ),
                          const Spacer(),
                          IconButton(
                            onPressed: () {
                              _showOptionsDialog(context, person);
                            },
                            icon: const Icon(Icons.more_horiz),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showOptionsDialog(BuildContext context, SocialCommentModel person) {
    showDialog(
      context: context,
      barrierColor: Theme.of(context).greyScale200(context),
      builder: (BuildContext context) {
        return Center(
          child: Container(
            width: MediaQuery.of(context).size.width * (2 / 3),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                  onTap: () {
                    if(person.name != 'StrongBody'){
                      Navigator.pop(context);
                    } else{
                      Navigator.pop(context);
                      showBlockModal(context, person.imageUrl, person.name);
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                    child: Row(
                      children: [
                        if(person.name != 'StrongBody')
                        SvgPicture.asset(
                          AppAssets.block,
                          height: 24,
                          colorFilter:  ColorFilter.mode(Theme.of(context).blackPrimary(context), BlendMode.srcIn),
                        ),
                        if(person.name != 'StrongBody')
                        Gap(10.w),
                        Text(person.name == 'StrongBody' ? 'Edit' : 'Bloc user', style: Theme.of(context).textTheme.lightBodyLargeMedium,),
                      ],
                    ),
                  ),
                ),
                 Divider(height: 1, thickness: 1, color: Theme.of(context).disabledLight(context)),
                GestureDetector(
                  onTap: () {
                    if(person.name == 'StrongBody'){
                      Navigator.pop(context);
                    } else{
                      Navigator.pop(context);
                      showReportModal(context);
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                    child: Row(
                      children: [
                        if(person.name != 'StrongBody')
                        SvgPicture.asset(
                          AppAssets.warningNoteSvg,
                          height: 24,
                          colorFilter:  ColorFilter.mode(Theme.of(context).backgroundRed(context), BlendMode.srcIn),
                        ),
                        if(person.name != 'StrongBody')
                        Gap(10.w),
                        Text(
                          person.name == 'StrongBody' ? 'Delete Comment' : 'Report',
                          style:  TextStyle(color: Theme.of(context).backgroundRed(context)),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
