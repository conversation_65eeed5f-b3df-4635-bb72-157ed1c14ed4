import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/constants/app_assets.dart';

class ResonReport extends StatelessWidget {
  final String title;
  const ResonReport({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.push(
          RouteName.reportNew,
          extra: title,
        );
      },
      child: SizedBox(
        child: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment
                  .spaceBetween,
              children: [
                // Text component
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.lightBodyXLargeMedium,
                    softWrap: true, // Cho phép xuống dòng tự động
                    overflow: TextOverflow
                        .ellipsis,
                  ),
                ),
                // SvgIcon component
                SvgPicture.asset(AppAssets.arrowRightSvg),
              ],
            ),
            const SizedBox(
              height: 16,
            ),
             Divider(
              height: 1,
              thickness: 1,
              color:Theme.of(context).disabledLight(context),
            ),
          ],
        ),
      ),
    );
  }
}
