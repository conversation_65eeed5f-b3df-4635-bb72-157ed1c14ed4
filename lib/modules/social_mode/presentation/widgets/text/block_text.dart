import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../../core/constants/app_assets.dart';

class BlockText extends StatelessWidget {
  final String title;
  const BlockText({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            SvgPicture.asset(AppAssets.dot),
          ],
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            title,
            style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(fontWeight: FontWeight.w400,),
            softWrap: true,
            overflow: TextOverflow.visible,
          ),
        ),
      ],
    );
  }
}
