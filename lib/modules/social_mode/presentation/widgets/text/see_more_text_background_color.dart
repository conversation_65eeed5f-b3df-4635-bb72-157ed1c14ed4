import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class SeeMoreTextBackgroundColor extends StatefulWidget {
  final String content;
  final String contentTranslation;

  const SeeMoreTextBackgroundColor({
    super.key,
    required this.content,
    required this.contentTranslation,
  });

  @override
  _SeeMoreTextBackgroundColorWidgetState createState() => _SeeMoreTextBackgroundColorWidgetState();
}

class _SeeMoreTextBackgroundColorWidgetState extends State<SeeMoreTextBackgroundColor> {
  bool _isExpanded = false;
  bool _isTranslated = false;

  @override
  Widget build(BuildContext context) {
    final displayedContent = _isTranslated
        ? widget.contentTranslation
        : (_isExpanded
        ? widget.content
        : (widget.content.length > 50
        ? "${widget.content.substring(0, 50)}... "
        : widget.content));

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              children: [
                // Hiển thị nội dung (hiển thị bản gốc hoặc bản dịch)
                TextSpan(
                  text: displayedContent,
                  style: Theme.of(context).textTheme.darkHeadingMedium,
                ),
                // Nút See More / See Less (chỉ hiển thị nếu không phải bản dịch)
                if (!_isTranslated && widget.content.length > 50)
                  TextSpan(
                    text: _isExpanded ? "...See less" : "See more",
                    style: Theme.of(context).textTheme.lightBodyMediumBold.copyWith(color: Theme.of(context).textSecondary(context)),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        setState(() {
                          _isExpanded = !_isExpanded; // Thay đổi trạng thái
                        });
                      },
                  ),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          // Nút "See translation" hoặc "Hide translation"
          GestureDetector(
            onTap: () {
              setState(() {
                _isTranslated = !_isTranslated;
                _isExpanded =
                false;
              });
            },
            child: Text(
              _isTranslated ? "Hide translation" : "See translation",
              style: Theme.of(context).textTheme.darkBodyMediumRegular,
            ),
          ),
        ],
      ),
    );
  }
}
