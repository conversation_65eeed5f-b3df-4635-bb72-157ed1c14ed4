import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';


class SeeMoreTextWidget extends StatefulWidget {
  final String content;
  final String contentTranslation;

  const SeeMoreTextWidget({
    super.key,
    required this.content,
    required this.contentTranslation,
  });

  @override
  _SeeMoreTextWidgetState createState() => _SeeMoreTextWidgetState();
}

class _SeeMoreTextWidgetState extends State<SeeMoreTextWidget> {
  bool _isExpanded = false;
  bool _isTranslated = false;

  @override
  Widget build(BuildContext context) {
    final displayedContent = _isTranslated
        ? widget.contentTranslation
        : (_isExpanded
            ? widget.content
            : (widget.content.length > 50
                ? "${widget.content.substring(0, 50)}... "
                : widget.content));

    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: displayedContent,
                  style: Theme.of(context).textTheme.lightBodyMediumRegular,
                ),
                if (!_isTranslated && widget.content.length > 50)
                  TextSpan(
                    text: _isExpanded ? "...See less" : "See more",
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).greyScale600(context),
                      decoration: TextDecoration.underline,
                      decorationColor: Theme.of(context).greyScale600(context),
                      decorationThickness: 1.5,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                  ),
              ],
            ),
          ),
          SizedBox(height: 8.h),
          // Nút "See translation" hoặc "Hide translation"
          GestureDetector(
            onTap: () {
              setState(() {
                _isTranslated = !_isTranslated;
                _isExpanded =
                    false;
              });
            },
            child: Text(
              _isTranslated ? "Hide translation" : "See translation",
              style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(color:Theme.of(context).primary(context),),
            ),
          ),
        ],
      ),
    );
  }
}
