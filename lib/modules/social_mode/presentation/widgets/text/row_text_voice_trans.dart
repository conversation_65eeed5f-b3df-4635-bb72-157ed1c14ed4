import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';



class RowTextVoiceTrans extends StatelessWidget {
  final String imageIcon;
  final String title;
  final String content;

  const RowTextVoiceTrans({super.key, required this.imageIcon, required this.title, required this.content});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric( vertical: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // Căn chỉnh trên cùng
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.asset(
            imageIcon,
            width: 40, // Chỉnh kích thước nếu cần
            height: 40,
          ),
          Gap(8.w), // Khoảng cách giữa ảnh và cột văn bản
          Expanded( // Đảm bảo cột văn bản chiếm phần còn lại của chiều rộng
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(color: Theme.of(context).greyScale700(context)),
                ),
                const SizedBox(height: 4), // Khoảng cách giữa các dòng văn bản
                Text(
                  content,
                  style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(color: Theme.of(context).greyScale500(context)),
                  textAlign: TextAlign.start,
                  softWrap: true, // Tự động xuống dòng
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
