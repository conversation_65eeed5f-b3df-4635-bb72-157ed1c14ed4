import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../../../application/app_bar/app_bar.dart';
import '../button/basic_button.dart';

class AccessLocation extends StatelessWidget {
  const AccessLocation({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.social.tr(),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(vertical: 56, horizontal: 16),
        child: BasicAppButton(onPressed: (){
          (context).push(RouteName.checkIn);
        }, title: 'Continue', sizeTitle: 16, fontW: FontWeight.w500, height: 40, radius: 8,),
      ),
      body: Align(
        alignment: Alignment.center,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Gap(24.h),
              Image.asset(AppAssets.createPostLocation),
              Gap(24.h),
              Text(
                'Please allow access to your device\'s location',
                style: Theme.of(context).textTheme.lightHeadingMedium,
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
              Gap(24.h),
              _accessYourDivice(context, AppAssets.checkInLoction, 'Add your location', 'You choose when to share your location, such as when checking in at locations'),
              Gap(24.h),
              _accessYourDivice(context, AppAssets.archiveTick, 'We rely on location to display what\'s around',
                  'We will display elements such as content and advertising near you'),
              Gap(24.h),
              _accessYourDivice(context, AppAssets.updateSettingTime, 'Update settings at any time', 'Even if you turn off Location Services, we still rely on things like your IP address to estimate your proximity'),
              Gap(24.h),

            ],
          ),
        ),
      ),
    );
  }
  Row _accessYourDivice(BuildContext context,String image, String title, String text){
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SvgPicture.asset(
          image,
          height: 24,
          colorFilter:  ColorFilter.mode(Theme.of(context).greyScale600(context), BlendMode.srcIn),
        ),
        Gap(12.w),
        Expanded( // Giới hạn chiều rộng của Column
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                softWrap: true,
              ),
              Text(
                text,
                style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(color: Theme.of(context).greyScale600(context),),
                softWrap: true,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
