import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';

class FirstTimePostMessage extends StatelessWidget {
  final VoidCallback onClose;

  const FirstTimePostMessage({
    Key? key,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 240,
          width: double.infinity,
          color: Theme.of(context).secondary(context),
        ),
        Positioned.fill(
          top: 64,
          left: 32,
          right: 32,
          bottom: 32,
          child: Center(
            child: Text(
              LocaleKeys.postFirstTimeMessage.tr(),
              style: Theme.of(context).textTheme.darkBodyMediumRegular,
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // Vị trí Text và Icon
        Positioned(
          top: 32,
          left: 32,
          right: 32,
          child: Row(
            children: [
              const Spacer(), // Đẩy chữ "Post your first article" vào giữa
              Text(
                LocaleKeys.postFirstTimeSubTitle.tr(),
                style: Theme.of(context).textTheme.darkBodyLargeBold,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              GestureDetector(
                onTap: onClose,
                child: SvgPicture.asset(AppAssets.x),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
