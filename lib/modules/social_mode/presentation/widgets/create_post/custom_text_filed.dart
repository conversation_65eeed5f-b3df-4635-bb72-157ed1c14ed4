import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/l10n/locale_keys.g.dart';

class SocialCustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final Function(String) onChanged;

  const SocialCustomTextField({
    super.key,
    required this.controller,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: TextField(
        controller: controller,
        minLines: 3,
        maxLines: null,
        decoration: InputDecoration(
          hintText: LocaleKeys.postHintText.tr(),
          hintStyle: Theme.of(context)
              .textTheme
              .bodyLarge
              ?.copyWith(color: Theme.of(context).greyScale500(context)),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: onChanged,
      ),
    );
  }
}
