import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/themes/app_colors.dart';

class RecordingWidget extends StatefulWidget {
  final Function() pickImageRecord;
  final Function() playRecording;
  final bool isPlaying;
  final bool selectIcon;
  final bool selectRecord;
  final PlayerController playerController;
  final Duration totalDuration;

  const RecordingWidget({
    super.key,
    required this.pickImageRecord,
    required this.playRecording,
    required this.isPlaying,
    required this.selectIcon,
    required this.selectRecord,
    required this.playerController,
    required this.totalDuration,
  });

  @override
  _RecordingWidgetState createState() => _RecordingWidgetState();
}

class _RecordingWidgetState extends State<RecordingWidget> {
  XFile? _imageFile;
  final ImagePicker _picker = ImagePicker();

  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _imageFile = pickedFile;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildImagePickerButton(),
              Gap(8.w),
              Text(
                'Download background image for \nrecording file.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 10,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          Gap(12.h),
          _buildAudioWaveform(),
        ],
      ),
    );
  }

  Widget _buildImagePickerButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        height: 60,
        width: 60,
        decoration: BoxDecoration(
          border: Border.all(width: 1, color: AppColors.textSecondary),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: _imageFile == null
              ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(AppAssets.imageOutline),
              Gap(4),
              Text(
                'Upload',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 8,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          )
              : ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.file(
              File(_imageFile!.path),
              height: 60,
              width: 60,
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAudioWaveform() {
    return Stack(
      children: [
        Container(
          height: 38,
          width: MediaQuery.of(context).size.width * 0.7, // Chỉ chiếm 70% chiều rộng màn hình
          decoration: BoxDecoration(
            border: Border.all(width: 1, color:Theme.of(context).greyScale700(context),),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        Positioned(
          left: 0,
          bottom: -6,
          child: IconButton(
            icon: Icon(
              widget.isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
              size: 32,
              color: Theme.of(context).secondaryBase(context)
            ),
            onPressed: widget.playRecording,
          ),
        ),
        Positioned(
          left: 42,
          bottom: -2,
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.65, // Sóng âm thanh nhỏ hơn khung một chút
            height: 40,
            child: AudioFileWaveforms(
              size: const Size(double.infinity, 40),
              playerController: widget.playerController,
              waveformType: WaveformType.long,
              enableSeekGesture: true,
              playerWaveStyle: const PlayerWaveStyle(
                fixedWaveColor: Colors.grey,
                liveWaveColor: Colors.black,
                waveThickness: 2.0,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
