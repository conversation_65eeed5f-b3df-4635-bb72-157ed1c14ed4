import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/themes/app_colors.dart';

class IncognitoSwitch extends StatefulWidget {
  final bool switchValue;
  final Function(bool) onChanged;

  const IncognitoSwitch({
    super.key,
    required this.switchValue,
    required this.onChanged,
  });

  @override
  _IncognitoSwitchState createState() => _IncognitoSwitchState();
}

class _IncognitoSwitchState extends State<IncognitoSwitch> {
  late bool _switchValue;

  @override
  void initState() {
    super.initState();
    _switchValue = widget.switchValue;
  }

  void _toggleSwitch(bool value) {
    setState(() {
      _switchValue = value;
    });
    widget.onChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration:  BoxDecoration(
        color: Theme.of(context).greyScale50(context),
      ),
      child: Row(
        children: [
          Text(
            'Disable incognito mode',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
          ),
          Gap(32.w),
          SizedBox(
            height: 26,
            width: 44,
            child: CupertinoSwitch(
              value: _switchValue,
              trackColor: Theme.of(context).greyScale700(context),
              activeColor: Theme.of(context).secondaryBase(context),
              onChanged: _toggleSwitch,
            ),
          ),
        ],
      ),
    );
  }
}
