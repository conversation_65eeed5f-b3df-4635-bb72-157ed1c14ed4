import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/l10n/locale_keys.g.dart';

class BackgroundColorTextField extends StatelessWidget {
  final TextEditingController controller;
  final Function(String) onChanged;
  final Color selectedColor;

  const BackgroundColorTextField({
    super.key,
    required this.controller,
    required this.onChanged,
    required this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          color: selectedColor,
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            controller: controller,
            maxLines: 20,
            style: Theme.of(context)
                .textTheme
                .headlineMedium
                ?.copyWith(color: Colors.white),
            decoration: InputDecoration(
              hintText: LocaleKeys.postHintText.tr(),
              hintStyle: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(color: Theme.of(context).greyScale500(context)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.transparent,
                  width: 1.5,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: Colors.transparent,
                  width: 1.5,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }
}
