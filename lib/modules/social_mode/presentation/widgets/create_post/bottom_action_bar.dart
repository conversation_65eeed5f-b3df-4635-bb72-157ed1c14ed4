import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/constants/app_assets.dart';

class BottomActionBar extends StatelessWidget {
  final double height;
  final VoidCallback onPickImage;
  final VoidCallback onSelectBackground;
  final ValueChanged<bool> onSelectIcon;

  const BottomActionBar({
    super.key,
    required this.height,
    required this.onPickImage,
    required this.onSelectBackground,
    required this.onSelectIcon,
  });

  Widget _buildIcon(String asset, Color color) {
    return SvgPicture.asset(
      asset,
      height: 24,
      colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300, width: 1.0),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildIconAction(AppAssets.image, Theme.of(context).successBase(context), () {
              onPickImage();
              onSelectIcon(false);
            }),
            _buildIconAction(AppAssets.checkInLoction, Theme.of(context).attentionBase(context),
                () {
              context.push(RouteName.accessLocation);
              onSelectIcon(false);
            }),
            _buildIconAction(AppAssets.backgroundColor,Theme.of(context).informationBase(context),
                () {
              onSelectIcon(true);
            }),
            _buildIconAction(AppAssets.record, Theme.of(context).backgroundRed(context), () {
              context.push(RouteName.record);
              onSelectIcon(false);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildIconAction(String asset, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: _buildIcon(asset, color),
    );
  }
}
