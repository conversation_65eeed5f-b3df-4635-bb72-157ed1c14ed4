import 'package:flutter/material.dart';

class ColorPicker extends StatelessWidget {
  final List<List<Color>> colors;
  final Function(Color) onColorSelected;
  final Color selectedColor;

  const ColorPicker({
    super.key,
    required this.colors,
    required this.onColorSelected,
    required this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: colors.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () {
              onColorSelected(colors[index][0]); // Chọn màu chính
            },
            child: Container(
              width: 30,
              margin: const EdgeInsets.symmetric(horizontal: 8.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: colors[index],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                border: Border.all(
                  color: selectedColor == colors[index][0]
                      ? Colors.black
                      : Colors.transparent,
                  width: 2.0,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
