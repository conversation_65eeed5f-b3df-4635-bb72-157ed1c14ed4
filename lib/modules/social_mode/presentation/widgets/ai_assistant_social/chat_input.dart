import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

class ChatInputWidget extends StatelessWidget {
  final TextEditingController controller;
  final Function(String) onSendMessage;
  final VoidCallback onPickImage;
  final int textLength;
  final ValueChanged<String> onTextChanged;

  const ChatInputWidget({
    Key? key,
    required this.controller,
    required this.onSendMessage,
    required this.onPickImage,
    required this.textLength,
    required this.onTextChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).whitePrimary(context),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller,
                    maxLength: 2500,
                    onChanged: onTextChanged,
                    decoration: InputDecoration(
                      hintText: 'Message assistant LION',
                      hintStyle:
                          Theme.of(context).textTheme.displaySmall!.copyWith(
                                color: Theme.of(context).greyScale600(context),
                              ),
                      border: const OutlineInputBorder(),
                      fillColor: Theme.of(context).lightGrey(context),
                      filled: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                      suffixIcon: IconButton(
                        icon: SvgPicture.asset(AppAssets.sendMessageSvg,
                            width: 24, height: 24),
                        onPressed: () => onSendMessage(controller.text),
                      ),
                      counterText: '',
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: onPickImage,
                  child: SvgPicture.asset(AppAssets.gallerySvg,
                      width: 24, height: 24),
                ),
                const SizedBox(width: 16),
                SvgPicture.asset(AppAssets.attachFile, width: 24, height: 24),
                const SizedBox(width: 16),
                Text(
                  '$textLength/2500',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
