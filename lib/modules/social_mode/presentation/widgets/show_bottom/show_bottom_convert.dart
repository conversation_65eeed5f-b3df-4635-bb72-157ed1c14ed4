import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/constants/app_assets.dart';


void showConvertModal(BuildContext context) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    barrierColor: Colors.black.withOpacity(0.5), // Làm mờ nền
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    isDismissible: true,
    isScrollControlled: true,
    builder: (context) {
      return FractionallySizedBox(
        heightFactor: 0.5, // Điều chỉnh chiều cao modal
        widthFactor: 1, // Mo<PERSON> chiếm toàn bộ chiều rộng
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Gap(14.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Auto Language Conversion',
                            style: Theme.of(context).textTheme.lightHeadingSmall,
                          ),
                        ],
                      ),
                      Gap(30.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            height: 40,
                            width: MediaQuery.of(context).size.width * 0.35, // Chiều rộng chiếm 30% màn hình
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(
                                width: 0.5,
                                color: Theme.of(context).disabledBase(context),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Gap(4.w),
                                Image.asset(AppAssets.english),
                                Gap(8.w),
                                Text('English',
                                  style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(fontWeight: FontWeight.w400),
                                )
                              ],
                            ),
                          ),
                          SvgPicture.asset(AppAssets.convert),
                          Container(
                            height: 40,
                            width: MediaQuery.of(context).size.width * 0.35, // Chiều rộng chiếm 30% màn hình
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(
                                width: 0.5,
                                color: Theme.of(context).disabledBase(context)
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Gap(4.w),
                                Image.asset(AppAssets.franch),
                                Gap(8.w),
                                Text('France',
                                  style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(fontWeight: FontWeight.w400),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                      Gap(24.h),
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              width: 0.5,
                              color: Theme.of(context).disabledBase(context)
                            )
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 14),
                          child: Text('C’est un fait établi depuis longtemps qu’un lecteur sera distrait par le contenu lisible d’une page lorsqu’il examinera sa mise en page.',
                            style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(fontWeight: FontWeight.w400),
                            softWrap: true,
                            overflow: TextOverflow.visible,
                          ),
                        ),
                      )

                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              right: 20,
              top: 18,
              child: GestureDetector(
                onTap: () {
                 context .pop();
                },
                child: GestureDetector(
                  onTap: () {
                   context.pop();
                  },
                    child: const Center(
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: AppColors.secondaryBase, // Màu của icon
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    },
  );
}