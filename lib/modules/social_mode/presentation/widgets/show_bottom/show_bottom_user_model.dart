import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/constants/app_assets.dart';

void showBottomUserModal(BuildContext context, String image, String name) {
  showModalBottomSheet(
    context: context,
    barrierColor: Colors.black.withOpacity(0.8),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration:  BoxDecoration(
          color: Theme.of(context).disabledLight(context),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 2.37,
              width: 58,
              decoration: BoxDecoration(
                color: Theme.of(context).disabledBase(context),
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            Gap(14),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SizedBox(
                height: 114,
                width: double.infinity,
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).whitePrimary(context),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          ListTile(
                            title: Row(
                              children: [
                                SvgPicture.asset(
                                  AppAssets.penMessageSvg,
                                  height: 24,
                                  colorFilter:  ColorFilter.mode(Theme.of(context).blackPrimary(context), BlendMode.srcIn),
                                ),
                                Gap(16),
                                Text(
                                  'Edit',
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular,
                                ),
                              ],
                            ),
                            onTap: () {
                              context.pop();
                            },
                          ),
                           Divider(
                            height: 1,
                            thickness: 1,
                              color: Theme.of(context).disabledLight(context)
                          ),
                          ListTile(
                            title: Row(
                              children: [
                                SvgPicture.asset(
                                  AppAssets.commentTrash,
                                  height: 24,
                                  colorFilter:  ColorFilter.mode(Theme.of(context).backgroundRed(context), BlendMode.srcIn),
                                ),
                                Gap(16),
                                Text(
                                  'Delete',
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular
                                      .copyWith(color: Theme.of(context).backgroundRed(context)),
                                ),
                              ],
                            ),
                            onTap: () {
                              context.pop();
                              _showDeleteDialog(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Gap(28),
          ],
        ),
      );
    },
  );
}

void _showDeleteDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierColor: Theme.of(context).greyScale700(context),
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width - 28,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Do you want to delete this post?',
                style: Theme.of(context).textTheme.lightHeadingSmall,
              ),
              const SizedBox(height: 16),
              Text(
                'This post will be deleted permanently.',
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumRegular
                    .copyWith(
                        color:Theme.of(context).greyScale700(context), fontWeight: FontWeight.w400),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () {
                      context.pop();
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 30,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                        side: const BorderSide(
                          color: Colors.black,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: Theme.of(context).textTheme.lightBodyLargeRegular,
                    ),
                  ),
                  const SizedBox(width: 16),
                  TextButton(
                    onPressed: () {
                      context.pop();
                      debugPrint('Post deleted');
                    },
                    style: TextButton.styleFrom(
                      backgroundColor:Theme.of(context).backgroundRed(context),
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 32,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      'Done',
                      style: Theme.of(context).textTheme.darkBodyLargeRegular,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}
