import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_block.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/show_bottom/show_bottom_report_model.dart';

import '../../../../../core/constants/app_assets.dart';


void showBottomModal(BuildContext context, String image, String name) {
  showModalBottomSheet(
    context: context,
    barrierColor: Colors.black.withOpacity(0.8),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration:  BoxDecoration(
          color: Theme.of(context).disabledLight(context),
          borderRadius:const  BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 2.37,
              width: 58,
              decoration: BoxDecoration(
                color: Theme.of(context).disabledBase(context),
                borderRadius: BorderRadius.circular(100),
              ),
            ),
            Gap(14.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SizedBox(
                height: 114,
                width: double.infinity,
                child: Column(
                  children: [
                    // Block User
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).whitePrimary(context),
                        // Màu nền cho Block User
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          ListTile(
                            title: Row(
                              children: [
                                SvgPicture.asset(
                                  AppAssets.block,
                                  height: 24,
                                  colorFilter:  ColorFilter.mode(Theme.of(context).blackPrimary(context), BlendMode.srcIn),
                                ),
                                Gap(16.w),
                                Text(
                                  'Block user',
                                  style: Theme.of(context).textTheme.lightBodyLargeRegular,

                                ),
                              ],
                            ),
                            onTap: () {
                              context.pop();
                              showBlockModal(context, image, name);
                            },
                          ),
                           Divider(
                            height: 1, // Chiều cao của Divider
                            thickness: 1, // Độ dày của Divider
                            color: Theme.of(context).disabledLight(context), // Màu sắc Divider
                          ),
                          ListTile(
                            title: Row(
                              children: [
                                SvgPicture.asset(
                                  AppAssets.warningNoteSvg,
                                  height: 24,
                                  colorFilter: ColorFilter.mode(Theme.of(context).backgroundRed(context), BlendMode.srcIn),
                                ),
                                Gap(16.w),
                                Text(
                                  'Report',
                                  style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(color: Theme.of(context).primary(context),),
                                ),
                              ],
                            ),
                            onTap: () {
                             context .pop();
                              showReportModal(context);
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Gap(28.h),
          ],
        ),
      );
    },
  );
}