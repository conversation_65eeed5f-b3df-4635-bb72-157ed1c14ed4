import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../report/reson_report.dart';


void showReportModal(BuildContext context) {
  showModalBottomSheet(
    context: context,
    barrierColor: Colors.black.withOpacity(0.3),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
    ),
    isDismissible: true,
    isScrollControlled: true,
    builder: (context) {
      return FractionallySizedBox(
        heightFactor: 0.84, // Chiều cao modal
        widthFactor: 1, // Chiều rộng modal
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Tiêu đề
                  ListTile(
                    title: Align(
                      alignment: Alignment.center,
                      child: Column(
                        children: [
                          Text(
                            'Report',
                            style: Theme.of(context).textTheme.lightHeadingMedium,
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Why are you reporting this ad?',
                            style: Theme.of(context).textTheme.lightHeadingSmall,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Center(
                    child: Text(
                      "Your report will be anonymous. If someone is in immediate danger, don't hesitate to contact local emergency services.",
                      style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(fontWeight: FontWeight.w400),
                      softWrap: true,
                      overflow: TextOverflow.visible,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const ResonReport(
                    title: 'Fraud, scams, or impersonation',
                  ),
                  const ResonReport(
                    title: 'Suicide or self-harm',
                  ),
                  const ResonReport(
                    title: 'Violence, hate speech, or exploitation',
                  ),
                  const ResonReport(
                    title: 'Intellectual property infringement',
                  ),
                  const ResonReport(
                    title: 'Suicide or self-harm',
                  ),
                  const ResonReport(
                    title: 'False information',
                  ),
                  const ResonReport(
                    title: 'Issues for those under 18',
                  ),
                  Gap(60.h),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}
