import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';


class BottomModal extends StatelessWidget {
  final bool selectIcon;
  final VoidCallback pickImage;
  final Function(bool) onSelectIconChanged;

  const BottomModal({
    super.key,
    required this.selectIcon,
    required this.pickImage,
    required this.onSelectIconChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 3,
            blurRadius: 10,
            offset: const Offset(3, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 5,
            width: 60,
            decoration: BoxDecoration(
                color: Theme.of(context).greyScale400(context),
                borderRadius: BorderRadius.circular(100)),
          ),
          const SizedBox(height: 14),
          ListTile(
            leading: SvgPicture.asset(
              AppAssets.image,
              height: 24,
              colorFilter:  ColorFilter.mode(Theme.of(context).successBase(context), BlendMode.srcIn),
            ),
            title: Text(
              LocaleKeys.imageText.tr(),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            onTap: () {
              pickImage();
              onSelectIconChanged(false);
            },
          ),
          ListTile(
            leading: SvgPicture.asset(
              AppAssets.checkInLoction,
              height: 24,
              colorFilter:  ColorFilter.mode(Theme.of(context).attentionBase(context), BlendMode.srcIn),
            ),
            title: Text(
              LocaleKeys.checkIn.tr(),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            onTap: () {
              context.push(RouteName.accessLocation);
              onSelectIconChanged(false);
            },
          ),
          ListTile(
            leading: SvgPicture.asset(
              AppAssets.backgroundColor,
              height: 24,
              colorFilter:  ColorFilter.mode(Theme.of(context).informationBase(context), BlendMode.srcIn),
            ),
            title: Text(
              LocaleKeys.backgroundColor.tr(),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            onTap: () {

              onSelectIconChanged(true);
              context.pop();
            },
          ),
          ListTile(
            leading: SvgPicture.asset(
              AppAssets.record,
              height: 24,
              colorFilter:  ColorFilter.mode(Theme.of(context).backgroundRed(context), BlendMode.srcIn),
            ),
            title: Text(
              LocaleKeys.record.tr(),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            onTap: () {
              context.push(RouteName.record);
              onSelectIconChanged(false);
            },
          ),
          Gap(26.h),
        ],
      ),
    );
  }
}
