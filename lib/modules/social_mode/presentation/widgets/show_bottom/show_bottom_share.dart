import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/constants/app_assets.dart';
import '../button/basic_button.dart';
void showShareModal(BuildContext context) {
  showModalBottomSheet(
    context: context,
    barrierColor: Colors.black.withOpacity(0.3),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
    ),
    isDismissible: true,
    isScrollControlled: true,
    builder: (context) {
      return FractionallySizedBox(
        heightFactor: 0.8, // <PERSON>ều cao modal
        widthFactor: 1, // Chiều rộng modal
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
          ),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView( // Thêm cuộn cho nội dung chính
                  child: Column(
                    children: [
                      // Nội dung chính của modal
                      ListTile(
                        title: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                GestureDetector(
                                    onTap: (){
                                     context .pop();
                                    },
                                    child:  Icon(Icons.close, size: 26, color: Theme.of(context).disabledDark(context),)),
                                const Spacer(),
                                const Spacer(),
                                Text(
                                  'Send To',
                                  style: Theme.of(context).textTheme.lightHeadingMedium,
                                ),
                                const Spacer(),
                                const Spacer(),
                              ],
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'I’d love to add you to my Multime contact app! This app can help you expand your global social network and unlock many business and life opportunities!\nhttps://multime.co',
                              style: Theme.of(context).textTheme.lightBodyXLargeRegular,
                              softWrap: true,
                              overflow: TextOverflow.visible,
                            ),
                            const SizedBox(height: 28),
                            SizedBox(
                              width: double.infinity,
                              height: 52,
                              child: TextField(
                                decoration: InputDecoration(
                                  filled: true,
                                  fillColor: Theme.of(context).greyScale100(context),
                                  contentPadding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                    horizontal: 20,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: BorderSide.none,
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: BorderSide(
                                      color: Theme.of(context).disabledLight(context),
                                      width: 0,
                                    ),
                                  ),
                                  suffixIcon: Padding(
                                    padding: const EdgeInsets.all(10.0), // Thêm padding nếu cần
                                    child: SvgPicture.asset(
                                      AppAssets.searchSvg,
                                      height: 20,
                                      width: 20,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(15),
                                    borderSide: BorderSide(
                                      color: Theme.of(context).disabledBase(context),
                                      width: 0,
                                    ),
                                  ),
                                ),
                                onChanged: (value) {
                                  debugPrint('Từ khóa tìm kiếm: $value');
                                },
                              ),
                            ),
                            const SizedBox(
                              height: 12,
                            ),
                            connectUser(
                                'https://1.bp.blogspot.com/--Lon61jgJCc/XlkgYBzrHoI/AAAAAAAAFOQ/znpvAbvqJtA9LVOZx2yR-Acqt9RiIxfYACLcBGAsYHQ/s1600/Thailand%2Bbeaufiful%2Bmodel%2B-%2BView%2BBenyapa%2B-%2BYoung%2Bcharming%2Bgirl%2Bwith%2Blong%2Bhair%2B%25282%2529.jpg',
                                'joe_evans',
                                '@joe_evans',context),
                            connectUser(
                                'https://1.bp.blogspot.com/--Lon61jgJCc/XlkgYBzrHoI/AAAAAAAAFOQ/znpvAbvqJtA9LVOZx2yR-Acqt9RiIxfYACLcBGAsYHQ/s1600/Thailand%2Bbeaufiful%2Bmodel%2B-%2BView%2BBenyapa%2B-%2BYoung%2Bcharming%2Bgirl%2Bwith%2Blong%2Bhair%2B%25282%2529.jpg',
                                'joe_evans',
                                '@joe_evans',context),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Row chứa biểu tượng nằm cuối modal
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: SizedBox(
                  height: 100, // Đặt chiều cao phù hợp để các mục hiển thị đầy đủ
                  child: ListView(
                    scrollDirection: Axis.horizontal, // Cuộn theo chiều ngang
                    children: [
                      iconAppSVG(context,AppAssets.telegram, 'Telegram'),
                      Gap(20.w),
                      iconAppSVG(context,AppAssets.snapChat, 'SnapChat'),
                      Gap(20.w),
                      iconAppSvg(context,AppAssets.facebookSvg, 'Facebook'),
                      Gap(20.w),
                      iconAppSvg(context,AppAssets.messengerSvg, 'Messenger'),
                      Gap(20.w),
                      iconAppPNG(context,AppAssets.sms, 'SMS'),
                      Gap(20.w),
                      iconAppPNG(context,AppAssets.copyLink, 'Copy link'),
                      Gap(20.w),
                      iconAppPNG(context,AppAssets.other, 'Other'),
                      // iconAppSVG(AppAssets.copyLink,'Copy link')
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget iconAppSVG(BuildContext context,String image, String title) {
  return Column(
    children: [
      SvgPicture.asset(image),
      Text(
        title,
        style: Theme.of(context).textTheme.lightBodySmallRegular.copyWith(color: Theme.of(context).greyScale600(context)),
      ),
    ],
  );
}

Widget iconAppPNG(BuildContext context,String image, String title) {
  return Column(
    children: [
      Image.asset(image),
      Text(
        title,
        style: Theme.of(context).textTheme.lightBodySmallRegular.copyWith(color: Theme.of(context).greyScale600(context)),
      ),
    ],
  );
}

Widget iconAppSvg(BuildContext context,String image, String title) {
  return Column(
    children: [
      SvgPicture.asset(image),
      Text(
        title,
        style: Theme.of(context).textTheme.lightBodySmallRegular.copyWith(color: Theme.of(context).greyScale600(context)),
      ),
    ],
  );
}

Widget connectUser(String image, String nameUser, String tag, BuildContext context) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            CircleAvatar(
              radius: 21,
              backgroundImage: NetworkImage(image),
            ),
            const SizedBox(
              width: 12,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nameUser,
                  style: TextStyle(
                    fontSize: 18,
                    color: Theme.of(context).blackPrimary(context),
                    fontWeight: FontWeight.w700,
                    fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                  ),
                ),
                Text(
                  tag,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).textSecondary(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                  ),
                ),
              ],
            )
          ],
        ),
        SizedBox(
            height: 22,
            width: 76,
            child: BasicAppButton(
              onPressed: () {},
              title: 'Send',
              sizeTitle: 12,
              fontW: FontWeight.w400,
              colorButton: Theme.of(context).backgroundRed(context),
            ))
      ],
    ),
  );
}