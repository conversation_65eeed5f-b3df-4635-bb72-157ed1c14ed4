import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../button/basic_button.dart';
import '../text/block_text.dart';


void showBlockModal(BuildContext context, String imageAvatar, String name) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    barrierColor: Colors.black.withOpacity(0.3),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
    ),
    isDismissible: true,
    isScrollControlled: true,
    builder: (context) {
      return FractionallySizedBox(
        heightFactor: 0.6,
        widthFactor: 1,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 14),
                      Column(
                        children: [
                          ListTile(
                            title: Align(
                              alignment: Alignment.center,
                              child: Column(
                                children: [
                                  CircleAvatar(
                                    radius: 21,
                                    backgroundImage: NetworkImage(imageAvatar),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Block $name ?',
                                    style: Theme.of(context).textTheme.lightBodyXLargeBold,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 48),
                          const BlockText(
                              title:
                              'They will not be able to find your profile or content on MultiMe.'),
                          const BlockText(
                              title:
                              'Their feedback on your article will not be displayed to anyone unless you unblock them.'),
                          const BlockText(
                              title:
                              'They will not be notified that you have blocked them.'),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ],
                  ),
                ),
                 Divider(
                  height: 1,
                  thickness: 1,
                  color: Theme.of(context).disabledLight(context),
                ),
                const SizedBox(height: 28),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: BasicAppButton(
                    onPressed: () {
                     context .pop();
                    },
                    title: 'Block',
                    sizeTitle: 16,
                    radius: 12,
                    colorButton: Colors.white,
                    height: 50,
                    fontW: FontWeight.w500,
                    colorTitle: Theme.of(context).backgroundRed(context),
                    borderColor: Theme.of(context).backgroundRed(context),
                    borderWidth: 1,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}