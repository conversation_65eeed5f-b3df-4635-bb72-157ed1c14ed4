import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/presentation/social_record/play_back_screen.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../../../application/app_bar/app_bar.dart';
import '../widgets/button/button_record.dart';
import '../widgets/button/button_un_record.dart';

class SocialRecord extends StatefulWidget {
  @override
  _SocialRecordState createState() => _SocialRecordState();
}

class _SocialRecordState extends State<SocialRecord> {
  FlutterSoundRecorder? _recorder;
  FlutterSoundPlayer? _player;
  RecorderController _waveController = RecorderController();
  bool _isRecording = false;
  bool _isPlaying = false;
  bool _isPaused = false;
  String _recordedFilePath = '/data/user/0/com.example.multime_app/cache/record.aac';  // Đường dẫn tệp ghi âm
  double _playbackPosition = 0;  // Vị trí phát lại hiện tại
  int _playbackDuration = 0;  // Thời gian phát lại (tính bằng giây)
  late Timer _timer;  // Timer để cập nhật thời gian

  // Biến mới để lưu thời gian bắt đầu và kết thúc ghi âm
  DateTime? _startTime;
  DateTime? _endTime;

  @override
  void initState() {
    super.initState();
    _recorder = FlutterSoundRecorder();
    _player = FlutterSoundPlayer();
    _initializeRecorder();
  }

  Future<void> _initializeRecorder() async {
    await _recorder!.openRecorder();
    await _player!.openPlayer();
    setState(() {});
  }

  Future<bool> _checkPermissions() async {
    var status = await Permission.microphone.status;
    if (!status.isGranted) {
      status = await Permission.microphone.request();
    }
    return status.isGranted;
  }

  Future<void> _startRecording() async {
    if (!await _checkPermissions()) {
      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Microphone permission is required to record audio.')));
      return;
    }

    await _recorder!.startRecorder(
      toFile: _recordedFilePath,
      codec: Codec.aacADTS,
      sampleRate: 44100,
      bitRate: 128000,
    );

    _startTime = DateTime.now();  // Ghi lại thời gian bắt đầu
    _waveController.record();
    setState(() {
      _isRecording = true;
    });
  }

  Future<void> _stopRecording() async {
    await _recorder!.stopRecorder();
    _endTime = DateTime.now();  // Ghi lại thời gian kết thúc

    setState(() {
      _isRecording = false;
    });

    _waveController.stop();

    // Tính toán thời gian ghi âm
    if (_startTime != null && _endTime != null) {
      Duration recordingDuration = _endTime!.difference(_startTime!);
      print('Total recording time: ${recordingDuration.inSeconds} seconds');

      // Chuyển sang màn hình phát lại và truyền thời gian ghi âm
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PlaybackScreen(
            recordedFilePath: _recordedFilePath,
            totalDuration: recordingDuration.inSeconds,  // Truyền thời gian ghi âm
          ),
        ),
      );
      // context.push(
      //   RouteName.playBackScreen,
      //   extra: {
      //     "totalDuration": recordingDuration.inSeconds,
      //     "recordedFilePath": _recordedFilePath,
      //   },
      // );
    }
  }




  Future<void> _playRecording() async {
    if (_recordedFilePath.isNotEmpty) {
      setState(() {
        _isPlaying = true;
        _playbackPosition = 0;
        _playbackDuration = 0;  // Đặt lại thời gian phát lại
      });

      await _player!.startPlayer(
        fromURI: _recordedFilePath,
        codec: Codec.aacADTS,
        whenFinished: () {
          setState(() {
            _isPlaying = false;
            _playbackPosition = 0;
            _playbackDuration = 0;
          });
        },
      );

      _startTimer();
    }
  }

  Future<void> _pausePlayback() async {
    await _player!.pausePlayer();
    setState(() {
      _isPaused = true;
      _isPlaying = false;
    });
    _stopTimer();
  }

  Future<void> _resumePlayback() async {
    await _player!.startPlayer(
      fromURI: _recordedFilePath,
      codec: Codec.aacADTS,
      whenFinished: () {
        setState(() {
          _isPlaying = false;
          _playbackPosition = 0;
          _playbackDuration = 0;
        });
      },
    );
    _startTimer();
  }

  Future<void> _stopPlayback() async {
    await _player!.stopPlayer();
    setState(() {
      _isPlaying = false;
      _playbackPosition = 0;
      _playbackDuration = 0;
    });
    _stopTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _playbackPosition += 1;
        _playbackDuration = _playbackPosition.toInt();
      });
    });
  }

  void _stopTimer() {
    _timer.cancel();
  }

  @override
  void dispose() {
    _recorder!.closeRecorder();
    _player!.closePlayer();
    _waveController.dispose();
    _timer.cancel();  // Hủy Timer khi dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.social.tr(),
      ),
      body: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        context.pop();
                      },
                      child: SvgPicture.asset(AppAssets.arrowLeftSvg),
                    ),
                    const Spacer(),
                    Text(
                      'Record',
                      style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(fontWeight: FontWeight.w700),
                    ),
                    const Spacer(),
                  ],
                ),
              ),
              if(_isRecording == false)
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(40, (index) {
                    return Container(
                      height: 5,
                      width: 2,
                      color: Theme.of(context).greyScale400(context),
                      margin: const EdgeInsets.only(right: 4),
                    );
                  }),
                ),
              if(_isRecording == true)
              Padding(
                padding: const EdgeInsets.all(48.0),
                child: Center(
                  child: AudioWaveforms(
                    size: const Size(double.infinity, 100),
                    recorderController: _waveController,
                    waveStyle: const WaveStyle(
                      waveColor: Colors.black,
                      extendWaveform: true,
                      showMiddleLine: false,
                    ),
                  ),
                ),
              ),
              Container(),
              Container(),
            ],
          ),
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: GestureDetector(
              onTap: _isRecording ? _stopRecording : _startRecording,
              child: _isRecording ? ButtonUnRecord() : ButtonRecord(),
            ),
          ),
        ],
      ),
    );
  }
}