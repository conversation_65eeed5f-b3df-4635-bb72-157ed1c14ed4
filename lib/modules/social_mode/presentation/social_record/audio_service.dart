import 'dart:async';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:permission_handler/permission_handler.dart';

class AudioService {
  FlutterSoundRecorder? recorder;
  FlutterSoundPlayer? player;
  RecorderController waveController = RecorderController();
  bool isRecording = false;
  String recordedFilePath = '/data/user/0/com.example.multime_app/cache/record.aac';

  AudioService() {
    recorder = FlutterSoundRecorder();
    player = FlutterSoundPlayer();
  }

  Future<void> initialize() async {
    await recorder!.openRecorder();
    await player!.openPlayer();
  }

  Future<bool> checkPermissions() async {
    var status = await Permission.microphone.status;
    if (!status.isGranted) {
      status = await Permission.microphone.request();
    }
    return status.isGranted;
  }

  Future<void> startRecording() async {
    if (!await checkPermissions()) return;
    await recorder!.startRecorder(
      toFile: recordedFilePath,
      codec: Codec.aacADTS,
      sampleRate: 44100,
      bitRate: 128000,
    );
    waveController.record();
    isRecording = true;
  }

  Future<void> stopRecording() async {
    await recorder!.stopRecorder();
    waveController.stop();
    isRecording = false;
  }

  Future<void> dispose() async {
    await recorder!.closeRecorder();
    await player!.closePlayer();
    waveController.dispose();
  }
}
