import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../../../application/app_bar/app_bar.dart';
import '../social_create_post/social_create_post_record.dart';

class PlaybackScreen extends StatefulWidget {
  final int totalDuration;
  final String recordedFilePath;

  const PlaybackScreen(
      {required this.recordedFilePath, required this.totalDuration});

  @override
  _PlaybackScreenState createState() => _PlaybackScreenState();
}

class _PlaybackScreenState extends State<PlaybackScreen> {
  FlutterSoundPlayer? _player;
  PlayerController _playerController = PlayerController();
  bool _isPlaying = false;
  double _currentPosition = 0.0;
  late Duration _totalDuration;

  @override
  void initState() {
    super.initState();
    _player = FlutterSoundPlayer();
    _player!.openPlayer();
    _totalDuration = Duration(seconds: widget.totalDuration);

    /// **Chuẩn bị PlayerController để phát & hiển thị waveform**
    _loadAudio();

    /// **Cập nhật tiến trình phát lại**
    _playerController.onCurrentDurationChanged.listen((duration) {
      setState(() {
        _currentPosition = duration.toDouble() / _totalDuration.inMilliseconds;
      });
    });

    /// **Lắng nghe khi file phát hết**
    _playerController.onCompletion.listen((_) async {
      await _replayRecording(); // Tự động phát lại từ đầu khi hết file
    });
  }

  /// **Hàm tải lại file audio**
  Future<void> _loadAudio() async {
    await _playerController.preparePlayer(
      path: widget.recordedFilePath,
      shouldExtractWaveform: true,
    );
  }

  /// **Replay (Phát lại từ đầu ngay lập tức)**
  Future<void> _replayRecording() async {
    await _loadAudio(); // Đảm bảo file được nạp lại
    await _playerController.seekTo(0); // Đưa về đầu file
    await _playerController.startPlayer(); // Phát lại từ đầu
    setState(() {
      _isPlaying = true;
      _currentPosition = 0.0;
    });
  }

  /// **Bắt đầu / Tạm dừng phát lại**
  Future<void> _playRecording() async {
    if (!_isPlaying) {
      // Nếu không đang phát, bắt đầu phát từ vị trí hiện tại
      await _playerController.startPlayer();
      setState(() {
        _isPlaying = true;
      });
    } else {
      // Nếu đang phát, tạm dừng
      await _playerController.pausePlayer();
      setState(() {
        _isPlaying = false;
      });
    }
  }

  /// **Chuyển đổi giây sang dạng `00:08 / 00:40`**
  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _player?.stopPlayer();
    _player?.closePlayer();
    _playerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(moden: LocaleKeys.social.tr()),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      context.pop();
                    },
                    child: SvgPicture.asset(AppAssets.arrowLeftSvg),
                  ),
                  const Spacer(),
                  const Spacer(),
                  Text(
                    'Record',
                    style: Theme.of(context)
                        .textTheme
                        .lightHeadingSmall
                        .copyWith(fontWeight: FontWeight.w700),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => CreatePostRecord(
                                    totalDuration: widget.totalDuration,
                                    recordedFilePath: widget.recordedFilePath,
                                    record: true,
                                  )));
                    },
                    child: Container(
                      height: 40,
                      width: 84,
                      decoration: BoxDecoration(
                        color: Theme.of(context).backgroundRed(context),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          LocaleKeys.post.tr(),
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeMedium
                              .copyWith(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              /// **Thanh waveform từ file gốc**
              // Text(
              //   LocaleKeys.post.tr(),
              //   style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith( color: _postIsEmpty ? Theme.of(context).greyScale600(context) : Colors.white),
              // ),
              GestureDetector(
                onHorizontalDragUpdate: (details) async {
                  // Tính toán vị trí mới dựa trên vị trí kéo
                  double width = MediaQuery.of(context).size.width;
                  double position =
                      details.localPosition.dx / width; // Tính toán phần trăm
                  int seekTo =
                      (position * _totalDuration.inMilliseconds).toInt();

                  // Gọi seekTo để di chuyển đến vị trí mới
                  await _playerController.seekTo(seekTo);

                  // Nếu player đang dừng, bắt đầu phát lại từ vị trí mới
                  if (!_isPlaying) {
                    await _playRecording(); // Bắt đầu phát lại
                  }
                },
                child: AudioFileWaveforms(
                  size: const Size(double.infinity, 100),
                  playerController: _playerController,
                  waveformType: WaveformType.long,
                  enableSeekGesture: true,
                  playerWaveStyle: const PlayerWaveStyle(
                    fixedWaveColor: Colors.grey,
                    liveWaveColor: Colors.black,
                    waveThickness: 2.0,
                  ),
                ),
              ),

              /// **Bộ đếm thời gian `00:08 / 00:40`**
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${_formatTime((_currentPosition * widget.totalDuration).toInt())}/${_formatTime(widget.totalDuration)}',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodySmallRegular
                        .copyWith(color: AppColors.disabledBase),
                  ),
                ],
              ),
            ],
          ),

          /// **Nút điều khiển**
          Positioned(
            left: 0,
            right: 0,
            bottom: 20,
            child: IconButton(
              icon: Icon(
                  _isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                  size: 32,
                  color: Theme.of(context).secondaryBase(context)),
              onPressed: _playRecording,
            ),
          ),
        ],
      ),
    );
  }
}
