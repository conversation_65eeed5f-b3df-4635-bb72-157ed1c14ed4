import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../application/app_bar/app_bar.dart';
import '../widgets/button/basic_button.dart';

class Report extends StatefulWidget {
  final String reson;
  const Report({super.key, required this.reson});

  @override
  State<Report> createState() => _ReportState();
}

class _ReportState extends State<Report> {
  final TextEditingController _descriptionController = TextEditingController();
  int numberText = 0;
  int numberImage = 0;
  final int maxLength = 300;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 52),
        child: BasicAppButton(
          onPressed: () {
            _showThankForReport(context);
          },
          title: LocaleKeys.send.tr(),
          sizeTitle: 16,
          fontW: FontWeight.w500,
          colorButton: Theme.of(context).backgroundRed(context),
          height: 40,
          radius: 8,
        ),
      ),
      appBar: CustomAppBar(
        moden: LocaleKeys.social.tr(),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                      onTap: () {
                        context.pop();
                      },
                      child: SvgPicture.asset(AppAssets.arrowLeftSvg)),
                  const Spacer(),
                  Text(
                    LocaleKeys.report.tr(),
                    style: Theme.of(context).textTheme.lightHeadingSmall,
                  ),
                  const Spacer(),
                ],
              ),
            ),
            Gap(16.h),
            Container(
              height: 52,
              width: double.infinity,
              color: Theme.of(context).lightGrey(context),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                            text: '${LocaleKeys.resonate.tr()}: ',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyMediumRegular,
                            children: <TextSpan>[
                              TextSpan(
                                text: widget.reson,
                                style: const TextStyle(
                                    fontWeight: FontWeight.w400),
                              )
                            ]),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  right: 16, left: 16, top: 20, bottom: 8),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 69,
                        width: 87,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            'https://capvirgo.com/wp-content/uploads/2022/06/wsxc1656255139011_0.jpg',
                            fit: BoxFit.fill,
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      Text(
                        LocaleKeys.nameProduct.tr(),
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular,
                      )
                    ],
                  ),
                  Gap(20.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${LocaleKeys.descriptionOption.tr()}: ',
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular,
                      ),
                      Text(
                        "$numberText / $maxLength",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(
                                color: Theme.of(context).greyScale500(context),
                                fontWeight: FontWeight.w200),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: TextField(
                controller: _descriptionController,
                maxLength: maxLength,
                maxLines: 6,
                decoration: InputDecoration(
                  hintText: LocaleKeys.shareIssue.tr(),
                  hintStyle: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                          color: Theme.of(context).greyScale500(context),
                          fontWeight: FontWeight.w300,
                          fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Colors.white,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: const BorderSide(
                      color: Colors.white,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  counter: null,
                  counterText: '',
                ),
                onChanged: (value) {
                  setState(() {
                    numberText = value.length;
                  });
                },
              ),
            ),
            Gap(20.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    height: 60,
                    width: 60,
                    color: Theme.of(context).greyScale600(context),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(AppAssets.imageOutline),
                          const SizedBox(
                            height: 4,
                          ),
                          Text(
                            '$numberImage/4',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyMediumRegular
                                .copyWith(fontSize: 8.sp),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Gap(20.h),
            Divider(
              height: 1,
              thickness: 1,
              color: Theme.of(context).disabledLight(context),
            ),
            Gap(12.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                LocaleKeys.ifImmediately.tr(),
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumRegular
                    .copyWith(color: Theme.of(context).greyScale500(context)),
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            )
          ],
        ),
      ),
    );
  }

  void _showThankForReport(BuildContext context) {
    showModalBottomSheet(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      ),
      isDismissible: true,
      isScrollControlled: true,
      builder: (context) {
        return FractionallySizedBox(
          heightFactor: 0.68,
          widthFactor: 1,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Gap(8.h),
                        SvgPicture.asset(AppAssets.line126Svg),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Column(
                            children: [
                              ListTile(
                                title: Align(
                                  alignment: Alignment.center,
                                  child: Column(
                                    children: [
                                      Gap(20.h),
                                      Text(
                                        'Thanks for reporting',
                                        style: Theme.of(context)
                                            .textTheme
                                            .lightHeadingMedium,
                                      ),
                                      Gap(30.h),
                                      SvgPicture.asset(
                                        AppAssets.checkReport,
                                        colorFilter: ColorFilter.mode(
                                          Color.lerp(
                                              Theme.of(context)
                                                  .errorBase(context),
                                              Theme.of(context)
                                                  .attentionBase(context),
                                              0.5)!,
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                      Gap(30.h),
                                      Center(
                                        child: Text(
                                          "Thank you for reporting, if enough 10 reports this comment will be permanently deleted and we will base on this report to avoid repeating similar comments.",
                                          style: Theme.of(context)
                                              .textTheme
                                              .lightBodySmallRegular,
                                          softWrap: true,
                                          overflow: TextOverflow.visible,
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  child: BasicAppButton(
                    onPressed: () {
                      Navigator.popUntil(context, (route) {
                        return route.isFirst;
                      });
                    },
                    title: 'Finish',
                    sizeTitle: 16,
                    radius: 12,
                    colorButton: Theme.of(context).primary(context),
                    height: 40,
                    fontW: FontWeight.w600,
                    colorTitle: Colors.white,
                    borderColor: Theme.of(context).primary(context),
                    borderWidth: 1,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color combineColors(Color color1, Color color2, {double ratio = 0.5}) {
    assert(ratio >= 0.0 && ratio <= 1.0, 'Ratio must be between 0 and 1');

    final int red = ((color1.red * ratio) + (color2.red * (1 - ratio))).toInt();
    final int green =
        ((color1.green * ratio) + (color2.green * (1 - ratio))).toInt();
    final int blue =
        ((color1.blue * ratio) + (color2.blue * (1 - ratio))).toInt();
    final int alpha =
        ((color1.alpha * ratio) + (color2.alpha * (1 - ratio))).toInt();

    return Color.fromARGB(alpha, red, green, blue);
  }
}
