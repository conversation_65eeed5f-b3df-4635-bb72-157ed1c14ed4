import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/social_mode/presentation/social_report/bloc/social_report_event.dart';
import 'package:multime_app/modules/social_mode/presentation/social_report/bloc/social_report_state.dart';


class ReportBloc extends Bloc<ReportEvent, ReportState> {
  ReportBloc() : super(ReportInitial());

  @override
  Stream<ReportState> mapEventToState(ReportEvent event) async* {
    if (event is ReportTextChanged) {
      yield ReportEditing(event.text);
    } else if (event is SubmitReport) {
      yield ReportSubmitting();
      await Future.delayed(const Duration(seconds: 2)); // Gi<PERSON> lập xử lý
      yield ReportSuccess();
    }
  }
}