import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/application/bottom_bar/application_page.dart';
import 'package:multime_app/modules/social_mode/presentation/social_home/social_home_page.dart';
import 'package:multime_app/modules/application/bottom_bar/bottomSheet_model.dart';

class SocialPage extends StatelessWidget {
  const SocialPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ApplicationPage(
      page: const SocialHomePage(),
      bottomSheetItems: [
        BottomSheetItem(
          name: "AI assistant writes for you",
          iconLead: AppAssets.aiAssistantSocial,
          onTap: () {
            (context).push(RouteName.socialAiAssistant);
          },
        ),
        BottomSheetItem(
          name: "Create post",
          iconLead: AppAssets.editSvg,
          onTap: () {
            (context).push(RouteName.homeSocialPost);
          },
        ),
        BottomSheetItem(
          name: "Review my posts",
          iconLead: AppAssets.history,
          onTap: () {
            (context).push(RouteName.historySocialPage);
          },
        ),
      ],
    );
  }
}
