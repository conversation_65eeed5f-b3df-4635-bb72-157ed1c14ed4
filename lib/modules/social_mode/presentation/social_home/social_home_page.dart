import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/post_card/post_card_background_color.dart';
import '../../../../core/constants/app_assets.dart';
import '../../data/fake_data/social_fake_data.dart';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/home/<USER>';
import '../widgets/post_card/post_cart_recording.dart';
import '../../../application/app_bar/app_bar.dart';
import '../widgets/post_card/post_card.dart';

class SocialHomePage extends StatefulWidget {
  const SocialHomePage({super.key});

  @override
  State<SocialHomePage> createState() => _SocialHomePageState();
}

class _SocialHomePageState extends State<SocialHomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String selectedItem = '';

  @override
  Widget build(BuildContext context) {
    String? tempSelectedItem;
    return SafeArea(
      child: Scaffold(
        key: _scaffoldKey,
        endDrawer: Drawer(
          elevation: 0, // Loại bỏ shadow
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(0), bottomRight: Radius.circular(0)),
          ),
          child: ListTitleCustomize(
            selectedItem: selectedItem,
            onItemSelected: (newItem) {
              setState(() {
                selectedItem = newItem;
              });
            },
          ),
        ),
        appBar: CustomAppBar(
          moden: LocaleKeys.social.tr(),
        ),
        body: SafeArea(
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  children: [
                    Gap(80.h),
                    if (selectedItem == '') const Customize(),
                    if (selectedItem != '')
                      GestureDetector(
                        onTap: () {
                          _scaffoldKey.currentState?.openEndDrawer();
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  SvgPicture.asset(AppAssets.customRed),
                                  const SizedBox(width: 10),
                                  Text(
                                    'Customize your feed',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodyMediumRegular,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    if (selectedItem != '')
                      ListView.builder(
                        shrinkWrap:
                        true,
                        physics:
                        const NeverScrollableScrollPhysics(),
                        itemCount: peoplePostCart.length,
                        itemBuilder: (context, index) {
                          final itemPostCart = peoplePostCart[index];
                          if(itemPostCart.name != 'StrongBody') {
                            return PostCard(
                              imageUrls: itemPostCart.imageUrls,
                              name: itemPostCart.name,
                              avatarUrl: itemPostCart.avatarUrl,
                              content: itemPostCart.content,
                              country: itemPostCart.country,
                              contentTranlation:
                              itemPostCart.contentTranlation,
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    Gap(12.h),
                    if (selectedItem != '')
                      // Nội dung bổ sung
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  'Invite more contact',
                                  style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(fontWeight: FontWeight.w700, fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
                                ),
                              ],
                            ),
                            Gap(10.h),
                            AddContact(contact: contacts),
                          ],
                        ),
                      ),
                    if (selectedItem != '')
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: postCardRecording.length,
                        itemBuilder: (context, index) {
                          final itemPostCart = postCardRecording[index];
                          if (itemPostCart.name != 'StrongBody') {
                            return PostCartRecording(
                              imageUrls: itemPostCart.imageUrls,
                              name: itemPostCart.name,
                              avatarUrl: itemPostCart.avatarUrl,
                              content: itemPostCart.content,
                              country: itemPostCart.country,
                              contentTranlation: itemPostCart.contentTranlation,
                              recordingTitle: itemPostCart.recordingTitle,
                              nameId: itemPostCart.nameId,
                            );
                          }
                          return null;
                        },
                      ),
                    if (selectedItem != '')
                      ListView.builder(
                        shrinkWrap:
                        true,
                        physics:
                        const NeverScrollableScrollPhysics(),
                        itemCount: postCardBackgroundColor.length,
                        itemBuilder: (context, index) {
                          final itemPorCartBackgroundColor = postCardBackgroundColor[index];
                          if(itemPorCartBackgroundColor.name != 'StrongBody') {
                            return PostCardBackgroundColor(
                                name: itemPorCartBackgroundColor.name,
                                avatarUrl: itemPorCartBackgroundColor.avatarUrl,
                                content: itemPorCartBackgroundColor.content,
                                contentTranlation: itemPorCartBackgroundColor.contentTranlation,
                                country: itemPorCartBackgroundColor.country);
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    const SizedBox(
                      height: 100,
                    ),
                  ],
                ),
              ),
              const  Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: PostInputWidget(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
