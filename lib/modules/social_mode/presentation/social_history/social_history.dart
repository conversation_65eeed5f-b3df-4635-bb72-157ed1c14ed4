import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/post_card/post_cart_recording.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../../data/fake_data/social_fake_data.dart';
import '../widgets/home/<USER>';
import '../widgets/post_card/post_card.dart';
import '../../../application/app_bar/app_bar.dart';
import '../widgets/post_card/post_card_background_color.dart';

class History extends StatefulWidget {
  const History({super.key});

  @override
  State<History> createState() => _HistoryState();
}

class _HistoryState extends State<History> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const History(),
  ];

  void _onTabSelected(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.social.tr(),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                          onTap: () {
                            context.pop();
                          },
                          child: SvgPicture.asset(AppAssets.arrowLeftSvg)),
                      const Spacer(),
                      Text(
                        LocaleKeys.history.tr(),
                        style: Theme.of(context).textTheme.lightHeadingSmall,
                      ),
                      const Spacer(),
                    ],
                  ),
                ),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: postCardRecording.length,
                  itemBuilder: (context, index) {
                    final itemPostCart = postCardRecording[index];
                    if (itemPostCart.name == 'StrongBody') {
                      return PostCartRecording(
                        imageUrls: itemPostCart.imageUrls,
                        name: itemPostCart.name,
                        avatarUrl: itemPostCart.avatarUrl,
                        content: itemPostCart.content,
                        country: itemPostCart.country,
                        contentTranlation: itemPostCart.contentTranlation,
                        recordingTitle: itemPostCart.recordingTitle,
                        nameId: itemPostCart.nameId,
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),

                ListView.builder(
                  shrinkWrap:
                  true,
                  physics:
                  const NeverScrollableScrollPhysics(),
                  itemCount: peoplePostCart.length,
                  itemBuilder: (context, index) {
                    final itemPostCart = peoplePostCart[index];
                    if(itemPostCart.name == 'StrongBody') {
                      return PostCard(
                        imageUrls: itemPostCart.imageUrls,
                        name: itemPostCart.name,
                        avatarUrl: itemPostCart.avatarUrl,
                        content: itemPostCart.content,
                        country: itemPostCart.country,
                        contentTranlation:
                        itemPostCart.contentTranlation,
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            'Invite more contact',
                            style:
                                Theme.of(context).textTheme.lightHeadingMedium,
                          ),
                        ],
                      ),
                      Gap(10.h),
                      AddContact(contact: contacts),
                      Gap(50.h),
                    ],
                  ),
                ),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: postCardRecording.length,
                  itemBuilder: (context, index) {
                    final itemPostCart = postCardRecording[index];
                    if (itemPostCart.name == 'StrongBody') {
                      return PostCartRecording(
                        imageUrls: itemPostCart.imageUrls,
                        name: itemPostCart.name,
                        avatarUrl: itemPostCart.avatarUrl,
                        content: itemPostCart.content,
                        country: itemPostCart.country,
                        contentTranlation: itemPostCart.contentTranlation,
                        recordingTitle: itemPostCart.recordingTitle,
                        nameId: itemPostCart.nameId,
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
                ListView.builder(
                  shrinkWrap:
                  true,
                  physics:
                  const NeverScrollableScrollPhysics(),
                  itemCount: postCardBackgroundColor.length,
                  itemBuilder: (context, index) {
                    final itemPorCartBackgroundColor = postCardBackgroundColor[index];
                    if(itemPorCartBackgroundColor.name == 'StrongBody') {
                      return PostCardBackgroundColor(
                        name: itemPorCartBackgroundColor.name,
                        avatarUrl: itemPorCartBackgroundColor.avatarUrl,
                        content: itemPorCartBackgroundColor.content,
                        contentTranlation: itemPorCartBackgroundColor.contentTranlation,
                        country: itemPorCartBackgroundColor.country);
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
