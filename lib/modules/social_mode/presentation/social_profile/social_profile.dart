import 'dart:io';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../app/routers/routers_name.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../../../../core/themes/app_colors.dart';
import '../../../application/app_bar/app_bar.dart';
import '../../../auth_mode/presentation/widgets/strong_body_text_field.dart';
import '../widgets/button/basic_button.dart';
import 'bloc/social_profile_bloc.dart';
import 'bloc/social_profile_event.dart';
import 'bloc/social_profile_state.dart';

class SocialEditProfile extends StatelessWidget {
  const SocialEditProfile({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SocialEditProfileBloc(),
      child: const SocialEditProfileView(),
    );
  }
}

class SocialEditProfileView extends StatefulWidget {
  const SocialEditProfileView({super.key});

  @override
  State<SocialEditProfileView> createState() => _SocialEditProfileViewState();
}

class _SocialEditProfileViewState extends State<SocialEditProfileView> {
  final TextEditingController _countryController = TextEditingController();
  final TextEditingController _aboutController = TextEditingController();
  final TextEditingController _occupationController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: const CustomAppBar(moden: 'Social'),
        body: BlocBuilder<SocialEditProfileBloc, SocialEditProfileState>(
          builder: (context, state) {
            return SingleChildScrollView(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Column(
                  children: [
                    Text(
                      'Profile Setting',
                      style: Theme.of(context).textTheme.lightHeadingMedium,
                    ),
                    Gap(18.h),
                    Stack(
                      children: [
                        Column(
                          children: [
                            Stack(
                              children: [
                                Container(
                                  width: double.infinity,
                                  height: 190,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: state.coverImage != null &&
                                              File(state.coverImage!.path)
                                                  .existsSync()
                                          ? FileImage(
                                                  File(state.coverImage!.path))
                                              as ImageProvider
                                          : const AssetImage(
                                              AppAssets.coverImage),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Positioned(
                                  right: 14,
                                  bottom: 16,
                                  child: InkWell(
                                    onTap: () async {
                                      final XFile? pickedFile =
                                          await ImagePicker().pickImage(
                                              source: ImageSource.gallery);
                                      if (pickedFile != null) {
                                        context
                                            .read<SocialEditProfileBloc>()
                                            .add(PickCoverImageEvent(
                                                pickedFile));
                                      }
                                    },
                                    child: SvgPicture.asset(
                                        AppAssets.profileCoverImage),
                                  ),
                                ),
                              ],
                            ),
                            Gap(24.h),
                            Stack(
                              children: [
                                Container(
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                      image: state.avatarImage != null &&
                                              File(state.avatarImage!.path)
                                                  .existsSync()
                                          ? FileImage(
                                                  File(state.avatarImage!.path))
                                              as ImageProvider
                                          : const NetworkImage(
                                              'https://cellphones.com.vn/sforum/wp-content/uploads/2023/10/avatar-trang-4.jpg',
                                            ),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Positioned(
                                  right: 0,
                                  bottom: 5,
                                  child: InkWell(
                                    onTap: () async {
                                      final XFile? pickedFile =
                                          await ImagePicker().pickImage(
                                              source: ImageSource.gallery);
                                      if (pickedFile != null) {
                                        context
                                            .read<SocialEditProfileBloc>()
                                            .add(PickAvatarImageEvent(
                                                pickedFile));
                                      }
                                    },
                                    child: SvgPicture.asset(AppAssets.up),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                    Gap(24.h),
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        'Provide additional photos to increase your credibility',
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular,
                        textAlign: TextAlign.center,
                        softWrap: true, // Cho phép xuống dòng
                        overflow: TextOverflow.visible,
                      ),
                    ),
                    Gap(24.h),
                    Stack(
                      children: [
                        TextField(
                          controller: _aboutController,
                          maxLines: 6,
                          onChanged: (text) {
                            print('about: $text');
                            context
                                .read<SocialEditProfileBloc>()
                                .add(UpdateAboutTextEvent(text));
                          },
                          decoration: InputDecoration(
                            labelText: 'About',
                            labelStyle: Theme.of(context)
                                .textTheme
                                .lightBodyLargeRegular
                                .copyWith(
                                  color: AppColors.textSecondary100,
                                ),
                            floatingLabelBehavior: FloatingLabelBehavior.auto,
                            alignLabelWithHint:
                                true, // Giúp căn label gần viền trên
                            contentPadding: const EdgeInsets.only(
                                top: 20, left: 12, right: 12),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Theme.of(context).lightGrey(context),
                                width: 1.5,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Theme.of(context).lightGrey(context),
                                width: 1.5,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                        if (_aboutController.text.isNotEmpty)
                          Positioned(
                            top: 12,
                            right: 12,
                            child: SvgPicture.asset(
                              AppAssets.penMessageSvg,
                              height: 20,
                              colorFilter: const ColorFilter.mode(
                                  AppColors.textPrimary, BlendMode.srcIn),
                            ),
                          ),
                      ],
                    ),
                    Gap(24.h),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        StrongBodyTextField(
                          label: LocaleKeys.country.tr(),
                          controller: _countryController,
                          hintText: LocaleKeys.country.tr(),
                          labalText: 'Country',
                          textInputAction: TextInputAction.none,
                          readOnly: true,
                          contentPadding: const EdgeInsets.only(
                              left: 15.0, bottom: 16, top: 10),
                          hintStyle: TextStyle(
                              fontSize: 14.sp,
                              color: Theme.of(context).greyScale600(context)),
                          suffixIcon: CountryCodePicker(
                            onChanged: (country) {
                              _countryController.text = country.name!;
                            },
                            builder: (country) {
                              return Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: SvgPicture.asset(
                                  AppAssets.arrowDownSvg,
                                  height: 20,
                                ),
                              );
                            },
                          ),
                          isSuffixIcon: true,
                          enabled: true,
                        ),
                      ],
                    ),
                    Gap(24.h),
                    TextField(
                      controller: _occupationController,
                      onChanged: (text) {
                        context
                            .read<SocialEditProfileBloc>()
                            .add(UpdateOccupationTextEvent(text));
                      },
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(
                              color: Theme.of(context).lightGrey(context),
                              width: 1),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(
                              color: Theme.of(context).lightGrey(context),
                              width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide(
                              color: Theme.of(context).lightGrey(context),
                              width: 1),
                        ),
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 10),
                        labelText: "Occupation",
                        labelStyle: Theme.of(context)
                            .textTheme
                            .lightBodyLargeRegular
                            .copyWith(
                              color: AppColors.textSecondary100,
                            ),
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                        suffixIcon: _occupationController.text.isNotEmpty
                            ? Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: SvgPicture.asset(
                                  AppAssets.penMessageSvg,
                                  height: 20,
                                  colorFilter: const ColorFilter.mode(
                                      AppColors.textSecondary, BlendMode.srcIn),
                                ),
                              )
                            : null,
                      ),
                    ),
                    Gap(20.h),
                    BasicAppButton(
                      onPressed: () {
                        (context).push(RouteName.homeSocialPost);
                      },
                      colorButton: AppColors.secondary,
                      title: 'Save',
                      sizeTitle: 16,
                      fontW: FontWeight.w700,
                      height: 48,
                      radius: 8,
                    ),
                    Gap(50.h),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
