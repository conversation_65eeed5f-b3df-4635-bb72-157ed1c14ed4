import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';

abstract class SocialEditProfileEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class PickAvatarImageEvent extends SocialEditProfileEvent {
  final XFile avatarImage;
  PickAvatarImageEvent(this.avatarImage);

  @override
  List<Object?> get props => [avatarImage];
}

class SocialEditProfileInitial extends SocialEditProfileEvent {}
class PickCoverImageEvent extends SocialEditProfileEvent {
  final XFile coverImage;

  PickCoverImageEvent(this.coverImage);

  @override
  List<Object?> get props => [coverImage];
}


class UpdateAboutTextEvent extends SocialEditProfileEvent {
  final String aboutText;
  UpdateAboutTextEvent(this.aboutText);

  @override
  List<Object?> get props => [aboutText];
}

class UpdateOccupationTextEvent extends SocialEditProfileEvent {
  final String occupation;
  UpdateOccupationTextEvent(this.occupation);

  @override
  List<Object?> get props => [occupation];
}

class UpdateCountryEvent extends SocialEditProfileEvent {
  final String countryName;
  UpdateCountryEvent(this.countryName);

  @override
  List<Object?> get props => [countryName];
}
