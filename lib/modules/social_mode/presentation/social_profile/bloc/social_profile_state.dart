import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';

class SocialEditProfileState extends Equatable {
  final XFile? avatarImage;
  final XFile? coverImage;
  final String aboutText;
  final String countryName;
  final String occupation;

  const SocialEditProfileState({
    this.avatarImage,
    this.coverImage,
    this.aboutText = '',
    this.countryName = '',
    this.occupation = '',
  });

  SocialEditProfileState copyWith({
    XFile? avatarImage,
    XFile? coverImage,
    String? aboutText,
    String? countryName,
    String? occupation,
  }) {
    return SocialEditProfileState(
      avatarImage: avatarImage ?? this.avatarImage,
      coverImage: coverImage ?? this.coverImage,
      aboutText: aboutText ?? this.aboutText,
      countryName: countryName ?? this.countryName,
      occupation: occupation ?? this.occupation,
    );
  }

  @override
  List<Object?> get props => [avatarImage, coverImage, aboutText, countryName, occupation];
}