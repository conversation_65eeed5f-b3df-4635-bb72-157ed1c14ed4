import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/modules/social_mode/presentation/social_profile/bloc/social_profile_event.dart';
import 'package:multime_app/modules/social_mode/presentation/social_profile/bloc/social_profile_state.dart';

class SocialEditProfileBloc extends Bloc<SocialEditProfileEvent, SocialEditProfileState> {
  final ImagePicker _picker = ImagePicker();

  SocialEditProfileBloc() : super(const SocialEditProfileState()) {
    on<PickAvatarImageEvent>(_onPickAvatarImage);
    on<PickCoverImageEvent>(_onPickCoverImage);
    on<UpdateAboutTextEvent>(_onUpdateAbout);
    on<UpdateCountryEvent>(_onSelectCountry);
    on<UpdateOccupationTextEvent>(_onUpdateOccupation);
  }

  Future<void> _onPickAvatarImage(
      PickAvatarImageEvent event, Emitter<SocialEditProfileState> emit) async {
    emit(state.copyWith(avatarImage: event.avatarImage));
  }

  Future<void> _onPickCoverImage(
      PickCoverImageEvent event, Emitter<SocialEditProfileState> emit) async {
    emit(state.copyWith(coverImage: event.coverImage));
  }

  void _onUpdateAbout(UpdateAboutTextEvent event, Emitter<SocialEditProfileState> emit) {
    emit(state.copyWith(aboutText: event.aboutText));
  }

  void _onSelectCountry(UpdateCountryEvent event, Emitter<SocialEditProfileState> emit) {
    emit(state.copyWith(countryName: event.countryName));
  }

  void _onUpdateOccupation(UpdateOccupationTextEvent event, Emitter<SocialEditProfileState> emit) {
    emit(state.copyWith(occupation: event.occupation));
  }
}