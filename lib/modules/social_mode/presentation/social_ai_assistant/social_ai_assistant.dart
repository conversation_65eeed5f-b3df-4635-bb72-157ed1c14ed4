import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../social_create_post/social_create_post_ai.dart';
import '../widgets/ai_assistant_social/chat_input.dart';

class SocialAiAssistant extends StatefulWidget {
  const SocialAiAssistant({super.key});

  @override
  State<SocialAiAssistant> createState() => _SocialAiAssistantState();
}

class _SocialAiAssistantState extends State<SocialAiAssistant> {
  final TextEditingController _controller = TextEditingController();
  final List<Map<String, dynamic>> _messages = [];
  bool _isFirstMessage = true;
  int _textLength = 0;
  String? _selectedAction;
  bool _isPosted = false;

  @override
  void initState() {
    super.initState();
    _messages.add(
        {'text': 'What topic do you want to write about?', 'isUser': false});
  }

  void _sendMessage(String text) {
    if (text.isNotEmpty) {
      setState(() {
        _messages.add({'text': text, 'isUser': true});
        _controller.clear();
        _textLength = 0;
      });

      if (_isFirstMessage) {
        setState(() {
          _messages.add({
            'text':
                "I'll write the content for you. Do you have any additional requirements?",
            'isUser': false
          });
          _messages.add({
            'type': 'suggestions',
            'options': [
              {'label': 'Start Writing', 'action': 'start_writing'},
              {'label': 'Write With Images', 'action': 'write_with_images'},
              {
                'label': 'Write Without Images',
                'action': 'write_without_images'
              },
            ]
          });
        });
        _isFirstMessage = false;
      }
    }
  }

  void _handleUserSelection(String action) {
    if (_isPosted && action == 'see_post') {
      return;
    }

    if (action == 'start_writing' ||
        action == 'write_with_images' ||
        action == 'write_without_images') {
      setState(() {
        _selectedAction = action;
        _messages.add({
          'text': 'I have written an article for you, click to view details.',
          'isUser': false
        });
        _messages.add({
          'type': 'see_post_button',
          'isUser': false,
        });
      });
    } else if (action == 'see_post') {
      _navigateToCreatePost();
    }
  }

  void _navigateToCreatePost() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SocialCreatePostAI(
          initialText: "Here is a sample post generated by AI.",
          // initialImages: [
          //   "https://file.hstatic.net/1000312435/file/yoga-an-do_714f74de9d094d20b5f1b75d01fba3c1_grande.png",
          // ],
          onPost: _onPost,
        ),
      ),
    );
  }

  void _onPost() {
    setState(() {
      _isPosted = true;
      _messages.add({'text': 'I posted for you', 'isUser': false});
      _messages.add(
          {'text': 'Do you need any other content written?', 'isUser': false});
    });
  }

  Widget _buildMessage(Map<String, dynamic> message) {
    if (message.containsKey('type') && message['type'] == 'suggestions') {
      return Align(
        alignment: Alignment.centerRight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: message['options'].map<Widget>((option) {
            return Padding(
              padding:
                  const EdgeInsets.only(top: 4.0, bottom: 4.0, right: 16.0),
              child: OutlinedButton(
                onPressed: () => _handleUserSelection(option['action']),
                style: OutlinedButton.styleFrom(
                    side:
                        BorderSide(color: Theme.of(context).secondary(context)),
                    backgroundColor: _isSelected(option['action'])
                        ? Theme.of(context).secondary(context)
                        : Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    )),
                child: Text(
                  option['label'],
                  style: TextStyle(
                    color: _isSelected(option['action'])
                        ? Colors.white
                        : Theme.of(context).secondary(context),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      );
    } else if (message.containsKey('type') &&
        message['type'] == 'see_post_button') {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!message['isUser'])
            Padding(
              padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
              child: SvgPicture.asset(
                AppAssets.aiAvatar,
                width: 20,
                height: 20,
              ),
            ),
          Expanded(
            child: Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: ElevatedButton(
                  onPressed: () => _handleUserSelection('see_post'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isPosted
                        ? Theme.of(context).secondary(context)
                        : Theme.of(context).successBase(context),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: Text(
                    _isPosted ? 'See the posted' : 'See the post',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (!message['isUser'])
          Padding(
            padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
            child: SvgPicture.asset(
              AppAssets.aiAvatar,
              width: 20,
              height: 20,
            ),
          ),
        Expanded(
          child: Align(
            alignment: message['isUser']
                ? Alignment.centerRight
                : Alignment.centerLeft,
            child: Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: message['isUser']
                    ? Theme.of(context).greyScale50(context)
                    : Theme.of(context).whitePrimary(context),
                borderRadius: BorderRadius.circular(12),
              ),
              child:
                  Text(message['text'], style: const TextStyle(fontSize: 16)),
            ),
          ),
        ),
      ],
    );
  }

  bool _isSelected(String action) {
    return _selectedAction == action;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('AI assistant writes for you',
            style: Theme.of(context).textTheme.headlineMedium,
            textAlign: TextAlign.center),
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg, width: 24, height: 24),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  return _buildMessage(_messages[index]);
                },
              ),
            ),
            ChatInputWidget(
              controller: _controller,
              onSendMessage: _sendMessage,
              onPickImage: () {},
              textLength: _textLength,
              onTextChanged: (String value) {
                setState(() {
                  _textLength = value.length;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
