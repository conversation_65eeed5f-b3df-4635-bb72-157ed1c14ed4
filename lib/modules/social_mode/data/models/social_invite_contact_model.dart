class SocialInviteContactModel {
  final String avatarUrl;
  final String name;
  final String professional;

  SocialInviteContactModel({
    required this.avatarUrl,
    required this.name,
    required this.professional,
  });

  factory SocialInviteContactModel.fromJson(Map<String, dynamic> json) {
    return SocialInviteContactModel(
      avatarUrl: json['avatarUrl'] ?? '',
      name: json['name'] ?? '',
      professional: json['professional'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'avatarUrl': avatarUrl,
      'name': name,
      'professional': professional,
    };
  }
}
