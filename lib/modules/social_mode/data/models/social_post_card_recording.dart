class SocialPostCardRecording {
  final String name;
  final String avatarUrl;
  final String content;
  final String contentTranlation;
  final String country;
  final String recordingTitle;
  final String nameId;
  final List<String> imageUrls;

  SocialPostCardRecording({
    required this.avatarUrl,
    required this.name,
    required this.content,
    required this.contentTranlation,
    required this.country,
    required this.recordingTitle,
    required this.nameId,
    required this.imageUrls,
  });

  factory SocialPostCardRecording.fromJson(Map<String, dynamic> json) {
    return SocialPostCardRecording(
      avatarUrl: json['avatarUrl'] ?? '',
      name: json['name'] ?? '',
      content: json['content'] ?? '',
      contentTranlation: json['contentTranlation'] ?? '',
      country: json['country'] ?? '',
      recordingTitle: json['recordingTitle'] ?? '',
      nameId: json['nameId'] ?? '',
      imageUrls: json['imageUrls'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'avatarUrl': avatarUrl,
      'name': name,
      'content': content,
      'contentTranlation': contentTranlation,
      'country': country,
      'recordingTitle': recordingTitle,
      'nameId': nameId,
      'imageUrls': imageUrls,
    };
  }
}


