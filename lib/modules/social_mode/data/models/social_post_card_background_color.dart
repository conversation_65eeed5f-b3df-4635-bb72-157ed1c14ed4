class SocialPostCardBackgroundColor {
  final String name;
  final String avatarUrl;
  final String content;
  final String contentTranlation;
  final String country;

  SocialPostCardBackgroundColor({
    required this.avatarUrl,
    required this.name,
    required this.content,
    required this.contentTranlation,
    required this.country,
  });

  factory SocialPostCardBackgroundColor.fromJson(Map<String, dynamic> json) {
    return SocialPostCardBackgroundColor(
      avatarUrl: json['avatarUrl'] ?? '',
      name: json['name'] ?? '',
      content: json['content'] ?? '',
      contentTranlation: json['contentTranlation'] ?? '',
      country: json['country'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'avatarUrl': avatarUrl,
      'name': name,
      'content': content,
      'contentTranlation': contentTranlation,
      'country': country,
    };
  }
}


