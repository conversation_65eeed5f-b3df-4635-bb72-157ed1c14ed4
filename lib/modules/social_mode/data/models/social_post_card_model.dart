class SocialPostCardModel {
  final String name;
  final String avatarUrl;
  final String content;
  final String contentTranlation;
  final String country;
  final List<String> imageUrls;

  SocialPostCardModel({
    required this.avatarUrl,
    required this.name,
    required this.content,
    required this.contentTranlation,
    required this.country,
    required this.imageUrls,
  });

  factory SocialPostCardModel.fromJson(Map<String, dynamic> json) {
    return SocialPostCardModel(
      avatarUrl: json['avatarUrl'] ?? '',
      name: json['name'] ?? '',
      content: json['content'] ?? '',
      contentTranlation: json['contentTranlation'] ?? '',
      country: json['country'] ?? '',
      imageUrls: json['imageUrls'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'avatarUrl': avatarUrl,
      'name': name,
      'content': content,
      'contentTranlation': contentTranlation,
      'country': country,
      'imageUrls': imageUrls,
    };
  }
}

