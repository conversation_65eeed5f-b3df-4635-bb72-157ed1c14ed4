class SocialCommentModel {
  final String name;
  final String time;
  final String comment;
  final String imageUrl;

  SocialCommentModel({
    required this.name,
    required this.time,
    required this.comment,
    required this.imageUrl,
  });

  factory SocialCommentModel.fromJson(Map<String, dynamic> json) {
    return SocialCommentModel(
      name: json['name'] ?? '',
      time: json['time'] ?? '',
      comment: json['comment'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'time': time,
      'comment': comment,
      'imageUrl': imageUrl,
    };
  }
}
