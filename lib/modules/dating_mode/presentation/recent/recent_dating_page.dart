import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/data/fakeData/useLikeMode.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/card_recent_user.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/divider_title_dating.dart';

class RecentDatingnPage extends StatelessWidget {
  const RecentDatingnPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: const EdgeInsets.only(left: 25),
          child: Icon<PERSON><PERSON>on(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              context.pop();
            },
          ),
        ),
        title: Text(
          LocaleKeys.recent.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                color: Theme.of(context).errorText(context),
              ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: 10.h,
            ),
            Padding(
              padding: const EdgeInsets.only(
                  top: 6, left: 12, right: 12, bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.reviewThePeopleYouHaveSwipedLeftOn.tr(),
                  ),
                  DividerTitle(title: LocaleKeys.today.tr()),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    itemCount: dummyUsers.length,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      childAspectRatio: 3 / 4,
                    ),
                    itemBuilder: (context, index) {
                      final user = dummyUsers[index];
                      return CardRecentUser(
                        user: user,
                        onTapView: () {},
                        onTapClose: () {},
                        onTapFavorite: () {},
                      );
                    },
                  ),
                  DividerTitle(title: LocaleKeys.yesterday.tr()),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    itemCount: dummyUsers.length,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      childAspectRatio: 3 / 4,
                    ),
                    itemBuilder: (context, index) {
                      final user = dummyUsers[index];
                      return CardRecentUser(
                        user: user,
                        onTapView: () {},
                        onTapClose: () {},
                        onTapFavorite: () {},
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
