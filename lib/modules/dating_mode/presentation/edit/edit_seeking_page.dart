import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_cubit.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_state.dart';
import 'package:multime_app/shared/widgets/sames/custom_dropdown_button.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/interests_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';

import '../../../busines_mode/presentation/find_job/cubit/find_job_event.dart';

class EditSeekingPage extends StatelessWidget {
  const EditSeekingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(
          LocaleKeys.Managewhoicanview.tr(),
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
                color: Theme.of(context).textPrimary(context),
                fontWeight: FontWeight.w700,
              ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
          child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<FindJobBloc, FindJobState>(
                    builder: (context, state) {
                  return Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            LocaleKeys.age.tr(),
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          Text("${state.start.toInt()} - ${state.end.toInt()}"),
                        ],
                      ),
                      RangeSlider(
                        values: RangeValues(state.start, state.end),
                        min: 18,
                        inactiveColor: Theme.of(context).greyScale300(context),
                        max: 60,
                        activeColor: Colors.red,
                        onChanged: (RangeValues values) {
                          context
                              .read<FindJobBloc>()
                              .add(UpdateAgeRange(values.start, values.end));
                        },
                      ),
                    ],
                  );
                }),
                BlocBuilder<FindJobBloc, FindJobState>(
                  builder: (context, state) {
                    return Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              LocaleKeys.distance.tr(),
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            Text("${state.distance.toInt()} km"),
                          ],
                        ),
                        Slider(
                          value: state.distance,
                          min: 0,
                          max: 100,
                          divisions: 100,
                          inactiveColor:
                              Theme.of(context).greyScale300(context),
                          activeColor: Colors.red,
                          onChanged: (double value) {
                            context
                                .read<FindJobBloc>()
                                .add(UpdateDistance(value));
                          },
                        ),
                      ],
                    );
                  },
                ),
                Text(
                  LocaleKeys.preferredGender.tr(),
                  style: Theme.of(context).textTheme.labelLarge,
                ),
                Gap(8.h),
                CustomDropDownButton(
                  arr: [
                    LocaleKeys.maleText.tr(),
                    LocaleKeys.femaleText.tr(),
                    LocaleKeys.otherText.tr()
                  ],
                  hintText: 'Male',
                ),
                Gap(8.h),
                const InterestsItem(),
              ],
            ),
            Gap(60.h),
            CustomeButtonDating(
                onTap: () {},
                text: LocaleKeys.save.tr(),
                colorBackground: Theme.of(context).backgroundRed(context),
                colorText: Theme.of(context).whitePrimary(context),
                height: 48.h,
                width: 396.w)
          ],
        ),
      )),
    );
  }
}
