import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';

class EditImage extends StatelessWidget {
  const EditImage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text('Image', style: Theme.of(context).textTheme.bodyMedium),
      const SizedBox(height: 8),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(
            3,
            (index) => InkWell(
              onTap: () {
                // Xử lý khi bấm nút
              },
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  SizedBox(
                      height: 145,
                      width: 103,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          'https://cdn.goenhance.ai/user/2024/07/19/c0c1400b-abc2-4541-a849-a7e4f361d28d_0.jpg',
                          fit: BoxFit.cover,
                        ),
                      )),
                  Gap(10.w),
                  Positioned(
                    bottom: -15.h, // Đặt nút vượt ra ngoài vùng container
                    right: -5.w,
                    child: Container(
                      width: 35,
                      height: 35,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: IconButton(
                          onPressed: () {
                            // Xử lý khi bấm nút close
                          },
                          icon: SvgPicture.asset(
                            AppAssets.unionSvg,
                            colorFilter: ColorFilter.mode(Theme.of(context).backgroundRed(context), BlendMode.srcIn),
                          )),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      )
    ]);
  }
}
