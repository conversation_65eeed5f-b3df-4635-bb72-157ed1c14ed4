import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class CardDetailReport extends StatefulWidget {
  final List<bool> actionsExpanded;
  const CardDetailReport({super.key, required this.actionsExpanded});

  @override
  State<CardDetailReport> createState() => _CardDetailReportState();
}

class _CardDetailReportState extends State<CardDetailReport> {
  Widget _buildAction(BuildContext context, String title, int index,
      void Function(void Function()) setState, List<bool>? actionsExpanded) {
    if (actionsExpanded == null) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(left: 5.h),
      child: Row(
        children: [
          Text(title,
              style: Theme.of(context).textTheme.lightBodyXLargeSemiBold),
          const Spacer(),
          Checkbox(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6.w),
            ),
            side: BorderSide(
              color: Theme.of(context).greyScale400(context),
              width: 2.w,
            ),
            value: actionsExpanded[index],
            onChanged: (value) {
              setState(() {
                actionsExpanded[index] = value ?? false;
              });
            },
            activeColor: Theme.of(context).errorText(context),
            checkColor: Theme.of(context).whitePrimary(context),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).whitePrimary(context),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, -4),
              blurRadius: 4,
              spreadRadius: 0,
              color: Theme.of(context).whitePrimary(context).withOpacity(0.2),
            ),
          ],
        ),
        child: Column(
          children: [
            Gap(5.h),
            Row(
              mainAxisAlignment:
                  MainAxisAlignment.center, // Phân phối khoảng trống giữa
              children: [
                // Văn bản ở giữa
                Expanded(
                  child: Text(
                    'Report',
                    textAlign: TextAlign.center,
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyXLargeSemiBold
                        .copyWith(
                          fontSize: 22.sp,
                        ),
                  ),
                ),
                // Nút ở bên phải
                GestureDetector(
                  onTap: () => context.pop(),
                  child: SvgPicture.asset(AppAssets.xSvg),
                ),
              ],
            ),
            Gap(12.h),
            Divider(
              height: 0.5.h,
              color: Theme.of(context).lightGrey(context),
            ),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.ReportThisDatingProfile.tr(),
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyXLargeBold
                        .copyWith(
                          fontSize: 22.sp,
                        ),
                  ),
                  Gap(5.h),
                  Text(
                    LocaleKeys.YouCanReportThisDating.tr(),
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumSemiBold
                        .copyWith(
                            color: Theme.of(context).disabledBase(context)),
                  ),
                  // Gap(15.h),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.padding2,
                        vertical: AppSpacing.padding16h),
                    child: Column(
                      children: [
                        _buildAction(context, 'Fake account', 0, setState,
                            widget.actionsExpanded),
                        Gap(10.h),
                        _buildAction(context, 'Share inappropriate content', 1,
                            setState, widget.actionsExpanded),
                        Gap(10.h),
                        _buildAction(context, 'Profile someone under 18', 2,
                            setState, widget.actionsExpanded),
                        Gap(10.h),
                        _buildAction(context, 'Scam', 3, setState,
                            widget.actionsExpanded),
                        Gap(10.h),
                        _buildAction(context, 'Harassment', 4, setState,
                            widget.actionsExpanded),
                        Gap(10.h),
                        _buildAction(
                            context,
                            'I\'m not interested in this person',
                            5,
                            setState,
                            widget.actionsExpanded),
                        Gap(10.h),
                        _buildAction(context, 'Crime reports', 6, setState,
                            widget.actionsExpanded),
                        Gap(10.h),
                        _buildAction(context, 'I\'m prefer not to answer that',
                            7, setState, widget.actionsExpanded),
                        Gap(10.h),
                        Center(
                          child: ElevatedButton(
                            onPressed: () {
                              // Add your save logic here
                            },
                            style: ElevatedButton.styleFrom(
                              minimumSize: Size(396.w, 48.h),
                              backgroundColor:
                                  Theme.of(context).backgroundRed(context),
                              foregroundColor:
                                  Theme.of(context).whitePrimary(context),
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius10),
                              ),
                            ),
                            child: Text(
                              LocaleKeys.save.tr(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
