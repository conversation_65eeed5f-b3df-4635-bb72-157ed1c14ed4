import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CustomModalBottomSheet extends StatefulWidget {
  const CustomModalBottomSheet({super.key});

  @override
  _CustomModalBottomSheetState createState() => _CustomModalBottomSheetState();
}

class _CustomModalBottomSheetState extends State<CustomModalBottomSheet> {
  final List<dynamic> _isSelectedList = [];
  int _click = 0;
  final List<bool> _selected = [false, false];

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setState) {
        return Column(
          children: [
            Expanded(
              child: Container(
                height: MediaQuery.of(context).size.height * 0.8,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0, -2),
                      blurRadius: 4,
                      spreadRadius: 0,
                      color: Theme.of(context).blackPrimary(context),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Gap(20.h),
                    Text(
                      LocaleKeys.ThisAppWillHaveAccess.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallRegular
                          .copyWith(
                            color: Theme.of(context).textPrimary(context),
                          ),
                    ),
                    Gap(20.h),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          TextButton(
                            onPressed: () {
                              context.pop();
                            },
                            child: Text(
                              LocaleKeys.cancel.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyXLargeBold
                                  .copyWith(
                                    color:
                                        Theme.of(context).disabledBase(context),
                                  ),
                            ),
                          ),
                          Container(
                            width: 116.8.w,
                            height: 31.91.h,
                            decoration: BoxDecoration(
                              color: Theme.of(context).greyScale300(context),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: ToggleButtons(
                              fillColor: Colors.white,
                              selectedColor: Colors.black,
                              isSelected: _selected,
                              onPressed: (index) {
                                setState(() {
                                  for (int i = 0; i < _selected.length; i++) {
                                    _selected[i] = i == index;
                                  }
                                });
                              },
                              borderRadius: BorderRadius.only(
                                topLeft: _selected[0]
                                    ? const Radius.circular(12.0)
                                    : Radius.zero,
                                topRight: _selected[1]
                                    ? const Radius.circular(12.0)
                                    : Radius.zero,
                                bottomLeft: _selected[0]
                                    ? const Radius.circular(12.0)
                                    : Radius.zero,
                                bottomRight: _selected[1]
                                    ? const Radius.circular(12.0)
                                    : Radius.zero,
                              ),
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 5, vertical: 2),
                                  child: Text("Photos",
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumRegular),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 5, vertical: 2),
                                  child: Text("Albums",
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumRegular),
                                ),
                              ],
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              context.pop();
                            },
                            child: Text(
                              LocaleKeys.Done.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyXLargeBold
                                  .copyWith(
                                    color: Theme.of(context)
                                        .backgroundRed(context),
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 25),
                    Expanded(
                      child: GridView.builder(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 1,
                          mainAxisSpacing: 1,
                        ),
                        itemCount: 12,
                        itemBuilder: (context, index) {
                          return Stack(
                            children: [
                              SvgPicture.asset(
                                AppAssets.logoMultiSvg,
                                fit: BoxFit.cover,
                                width: double.infinity,
                                height: double.infinity,
                              ),
                              Positioned(
                                top: 10,
                                right: 10,
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      if (_isSelectedList.contains(index)) {
                                        _isSelectedList.remove(index);
                                        _click--;
                                      } else {
                                        _isSelectedList.add(index);
                                        _click++;
                                      }
                                    });
                                  },
                                  child: Icon(
                                    _isSelectedList.contains(index)
                                        ? Icons.check_circle
                                        : Icons.circle_outlined,
                                    color: _isSelectedList.contains(index)
                                        ? Theme.of(context)
                                            .whitePrimary(context)
                                        : Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: const [BoxShadow(blurRadius: 33.59)],
                border: Border.all(
                  color: Theme.of(context).blackPrimary(context),
                  width: 0.34,
                ),
              ),
              child: Center(
                child: Text(
                  '${LocaleKeys.ShowSelected.tr()} ($_click/3)',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyXLargeRegular
                      .copyWith(
                        color: Theme.of(context).informationBase(context),
                      ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
