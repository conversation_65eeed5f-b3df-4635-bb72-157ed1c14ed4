// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

// ignore: must_be_immutable
class TextFieldDating extends StatelessWidget {
  String label;
  String hint;
  TextEditingController? controller;
  int maxLines;
  TextFieldDating({
    super.key,
    required this.label,
    required this.hint,
    required this.maxLines,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                  color: Theme.of(context).textPrimary(context),
                ),
          ),
          Gap(8.h),
          TextField(
            controller: controller,
            style: Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
              color: Theme.of(context).textPrimary(context),
                ),
            maxLines: maxLines,
            keyboardType: TextInputType.multiline,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle:
                  Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                        color: Theme.of(context).greyScale600(context),
                      ),
              hintMaxLines: 2,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusColor: Theme.of(context).textPrimary(context),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
