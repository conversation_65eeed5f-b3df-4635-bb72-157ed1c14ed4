import 'package:flutter/material.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/card_detail_report.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/card_report.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/showDialog_dating.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Future<void> report(BuildContext context, List<bool> actionsExpanded) {
  // L<PERSON>y kích thước màn hình
  final screenHeight = MediaQuery.of(context).size.height;
  final screenWidth = MediaQuery.of(context).size.width;

  double heightFactor = screenHeight * 0.3.h;

  return showModalBottomSheet(
    barrierColor: Colors.transparent,
    context: context,
    builder: (context) => FractionallySizedBox(
      heightFactor:
          heightFactor / screenHeight, // Chuyển đổi thành tỷ lệ phần trăm
      widthFactor: 1, // <PERSON><PERSON><PERSON> rộng chiếm toàn bộ màn hình
      child: StatefulBuilder(
        builder: (context, setState) {
          return CardReport(
            onBlockPressed: () => showBlock(context),
            onReportPressed: () => showReport(context, actionsExpanded),
          );
        },
      ),
    ),
  );
}

Future<void> showReport(BuildContext context, List<bool> actionsExpanded) {
  final screenHeight = MediaQuery.of(context).size.height;

  double heightFactor = screenHeight * 0.7.h;

  return showModalBottomSheet(
    barrierColor: Colors.transparent,
    isDismissible: true,
    isScrollControlled: true,
    context: context,
    builder: (context) => FractionallySizedBox(
      heightFactor: heightFactor / screenHeight,
      widthFactor: 1,
      child: StatefulBuilder(builder: (context, setState) {
        return CardDetailReport(actionsExpanded: actionsExpanded);
      }),
    ),
  );
}
