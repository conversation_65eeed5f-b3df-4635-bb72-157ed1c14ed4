import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/data/models/user_like_model.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm.dart';

class CardRecentUser extends StatelessWidget {
  final UserLikeModel user;
  final Function() onTapView;
  final Function() onTapClose;
  final Function() onTapFavorite;

  const CardRecentUser({
    super.key,
    required this.user,
    required this.onTapView,
    required this.onTapClose,
    required this.onTapFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          height: 269.h,
          width: 185.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            image: DecorationImage(
              image: NetworkImage(user.images[0]),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          bottom: 5.h,
          child: Padding(
            padding: const EdgeInsets.all(5.0),
            child: Column(
              children: [
                //button
                Row(
                  children: [
                    GestureDetector(
                      onTap: onTapFavorite,
                      child: Container(
                          width: 39.w,
                          height: 40.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.25),
                                  blurRadius: 8.0,
                                  offset: const Offset(0, 4),
                                ),
                              ]),
                          child: Center(
                              child: SvgPicture.asset(AppAssets.unionSvg))),
                    ),
                    Gap(50.w),
                    GestureDetector(
                      onTap: onTapFavorite,
                      child: Container(
                        width: 39.w,
                        height: 40.h,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.25),
                                blurRadius: 8.0,
                                offset: const Offset(0, 4),
                              ),
                            ]),
                        child: Center(
                            child: Icon(
                          Icons.favorite,
                          size: 20.sp,
                          color: Theme.of(context).backgroundRed(context),
                        )),
                      ),
                    ),
                  ],
                ),
                Gap(6.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        RichText(
                            text: TextSpan(
                          children: [
                            TextSpan(
                              text: "${user.name}, ",
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeBold
                                  .copyWith(
                                    color: Theme.of(context).whitePrimary(context),
                                  ),
                            ),
                            TextSpan(
                              text: "${user.age}",
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeMedium
                                  .copyWith(
                                color: Theme.of(context).whitePrimary(context),
                                  ),
                            ),
                          ],
                        )),
                        Text(
                          user.job,
                          style: Theme.of(context)
                              .textTheme
                              .lightBodySmallSemiBold
                              .copyWith(
                            color: Theme.of(context).whitePrimary(context),
                              ),
                        ),
                      ],
                    ),
                    Gap(15.w),
                    // Nút View
                    SizedBox(
                      width: 60.w,
                      height: 30.h,
                      child: ButtonSB(
                        onTap: onTapView,
                        text: "View",
                        color: Theme.of(context).errorText(context),
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
