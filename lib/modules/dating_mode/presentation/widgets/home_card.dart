import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/stackcard.dart';

class HomeCard extends StatefulWidget {
  final List<Stackcard> cards;
  const HomeCard({super.key, required this.cards});

  @override
  _HomeCardState createState() => _HomeCardState();
}

class _HomeCardState extends State<HomeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotateAnimation;

  double xPosition = 0;
  double yPosition = 0;
  double rotationAngle = 0;

  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void resetCardPosition() {
    setState(() {
      xPosition = 0;
      yPosition = 0;
      rotationAngle = 0;
    });
  }

  void swipeCard(bool isRightSwipe) {
    setState(() {
      _slideAnimation = Tween<Offset>(
        begin: Offset.zero,
        end: Offset(isRightSwipe ? 1.5 : -1.5, 0),
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));

      _rotateAnimation = Tween<double>(
        begin: 0,
        end: isRightSwipe ? 0.2 : -0.2,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));
    });

    _controller.forward().then((_) {
      setState(() {
        currentIndex++;
        resetCardPosition();
      });
      _controller.reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (currentIndex < widget.cards.length)
          Stack(
            children: [
              // Card thứ 2 (sau card hiện tại)
              if (currentIndex + 1 < widget.cards.length)
                buildCard(widget.cards[currentIndex + 1], false),
              // Card hiện tại
              buildCard(widget.cards[currentIndex], true),
            ],
          )
        else
          Center(
            child: Text(
              LocaleKeys.noMoreProfile.tr(),
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
          ),
      ],
    );
  }

  Widget buildCard(Stackcard cards, bool isFrontCard) {
    final double scaleFactor = isFrontCard ? 1.0 : 0.95;
    final double opacityFactor = isFrontCard ? 1.0 : 0.7;

    return Positioned(
      top: isFrontCard ? 0 : 0,
      left: isFrontCard ? 0 : 0,
      right: isFrontCard ? 0 : 0,
      child: GestureDetector(
        onPanUpdate: (details) {
          if (isFrontCard) {
            setState(() {
              xPosition += details.delta.dx;
              yPosition += details.delta.dy;
              rotationAngle = xPosition / 300; // Tính toán góc xoay
            });
          }
        },
        onPanEnd: (details) {
          if (isFrontCard) {
            if (xPosition.abs() > 150) {
              swipeCard(xPosition > 0);
            } else {
              resetCardPosition();
            }
          }
        },
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform.translate(
              offset: isFrontCard
                  ? Offset(
                      xPosition +
                          (_slideAnimation.value.dx *
                              MediaQuery.of(context).size.width),
                      yPosition,
                    )
                  : const Offset(0, 0),
              child: Transform(
                alignment: Alignment.center,
                transform: isFrontCard
                    ? Matrix4.rotationZ(rotationAngle + _rotateAnimation.value)
                    : Matrix4.identity(),
                child: Transform.scale(
                  scale: scaleFactor,
                  child: Opacity(
                    opacity: opacityFactor,
                    child: Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(23.w),
                      ),
                      elevation: 8,
                      child: Stackcard(
                        userLikeModel: cards.userLikeModel,
                        onTapView: cards.onTapView,
                        onSwipeLeft: () => swipeCard(false),
                        onSwipeRight: () => swipeCard(true),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
