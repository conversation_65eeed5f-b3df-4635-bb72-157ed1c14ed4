// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ButtonDatting extends StatelessWidget {
  String text;
  IconData icon;
  Function()? onTap;
  ButtonDatting({
    super.key,
    required this.text,
    required this.icon,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Icon(
            icon,
            size: 30,
            color: Theme.of(context).greyScale600(context),
          ),
          Gap(8.h),
          Text(
            text,
            style: Theme.of(context)
                .textTheme
                .lightBodyLargeRegular
                .copyWith(fontWeight: FontWeight.w200),
          ),
        ],
      ),
    );
  }
}
