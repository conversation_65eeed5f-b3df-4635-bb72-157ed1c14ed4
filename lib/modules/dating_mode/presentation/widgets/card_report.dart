import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CardReport extends StatefulWidget {
  final VoidCallback onReportPressed; // Callback khi nhấn Report
  final VoidCallback onBlockPressed; // Callback khi nhấn Block

  const CardReport({
    super.key,
    required this.onReportPressed,
    required this.onBlockPressed,
  });

  @override
  State<CardReport> createState() => _CardReportState();
}

class _CardReportState extends State<CardReport> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).whitePrimary(context),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).whitePrimary(context).withOpacity(0.2),
              spreadRadius: 8.h,
              blurRadius: 28.h,
              offset: Offset(0, 9.h),
            ),
          ],
        ),
        child: Column(
          children: [
            SizedBox(height: 5.h),
            SvgPicture.asset(AppAssets.lineSvg),
            Align(
              alignment: Alignment.centerRight,
              child: Container(
                height: 38.h,
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
                child: InkWell(
                  onTap: () => context.pop(),
                  child: SvgPicture.asset(AppAssets.xSvg),
                ),
              ),
            ),
            Container(
              height: 115.h,
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
              child: Column(
                children: [
                  GestureDetector(
                    onTap: () {
                      // showReport(context, actionsExpanded);
                      widget.onReportPressed();
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(AppAssets.warningSvg),
                        Gap(10.h),
                        Text('Report profile of Diana',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeMedium),
                      ],
                    ),
                  ),
                  Gap(30.h),
                  GestureDetector(
                    onTap: () {
                      widget.onBlockPressed();
                      // showBlock(context);
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(AppAssets.banSvg),
                        SizedBox(width: 10.w),
                        Text(
                            '${LocaleKeys.Block.tr()} Dr.Nguyen ${LocaleKeys.InTheDatingSection.tr()}',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeMedium),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
