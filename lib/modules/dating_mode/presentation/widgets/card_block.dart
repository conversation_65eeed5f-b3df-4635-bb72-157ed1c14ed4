import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class CardBlock extends StatelessWidget {
  const CardBlock({super.key});

  Widget _buildButton(Color colorBackground, Color colorText, String text,
      BuildContext context) {
    return Expanded(
      child: ElevatedButton(
        onPressed: () {},
        style: ElevatedButton.styleFrom(
          backgroundColor: colorBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.w),
          ),
        ),
        child: Text(text,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                  color: colorText,
                )),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300.w,
      decoration: BoxDecoration(
        color: Theme.of(context).whitePrimary(context),
        borderRadius: BorderRadius.circular(16.w),
        border: Border(
          top: BorderSide(
            color: Theme.of(context).greyScale500(context),
            width: 1.w,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Gap(10.h),
                  SizedBox(
                    width: 233.w,
                    height: 25.h,
                    child: Text(
                      '${LocaleKeys.Block} Dr.Nguyen?',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.lightBodyLargeBold,
                    ),
                  ),
                  Gap(10.h),
                  SizedBox(
                    height: 75.h,
                    width: 233.w,
                    child: Text(
                      LocaleKeys.Theywillnotbe.tr(),
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.lightBodyLargeRegular,
                    ),
                  ),
                ],
              )),
          SizedBox(
            width: 233.w,
            height: 40.h,
            child: Row(
              children: [
                _buildButton(
                  Theme.of(context).whitePrimary(context),
                  Theme.of(context).greyScale900(context),
                  LocaleKeys.cancel.tr(),
                  context,
                ),
                Gap(10.h),
                _buildButton(
                  Theme.of(context).backgroundRed(context),
                  AppColors.white,
                  LocaleKeys.Block.tr(),
                  context,
                ),
              ],
            ),
          ),
          Gap(20.h),
        ],
      ),
    );
  }
}
