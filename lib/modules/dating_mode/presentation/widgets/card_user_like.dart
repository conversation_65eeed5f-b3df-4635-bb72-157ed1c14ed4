import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/data/models/user_like_model.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm.dart';

class CardUserLike extends StatelessWidget {
  final UserLikeModel user;

  final Function() onTapView;

  const CardUserLike({
    super.key,
    required this.user,
    required this.onTapView,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          height: 260,
          width: 180,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            image: DecorationImage(
              image: NetworkImage(user.images[0]),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          bottom: 5,
          child: Padding(
            padding: const EdgeInsets.all(5.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    RichText(
                        text: TextSpan(
                      children: [
                        TextSpan(
                          text: "${user.name}, ",
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeBold
                              .copyWith(
                                color: Theme.of(context).whitePrimary(context),
                              ),
                        ),
                        TextSpan(
                          text: "${user.age}",
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeMedium
                              .copyWith(
                                color: Theme.of(context).whitePrimary(context),
                              ),
                        ),
                      ],
                    )),
                    Text(
                      user.job,
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallSemiBold
                          .copyWith(
                            color: Theme.of(context).whitePrimary(context),
                          ),
                    ),
                  ],
                ),
                Gap(15.w),
                // Nút View
                SizedBox(
                  width: 60.w,
                  height: 30.h,
                  child: ButtonSB(
                    onTap: onTapView,
                    text: LocaleKeys.View.tr(),
                    color: Theme.of(context).errorText(context),
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }
}
