import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/card_block.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Future<void> showBlock(BuildContext context) {
  return showDialog(
    context: context,
    barrierDismissible: true,
    barrierColor: Theme.of(context).greyScale300(context).withOpacity(0.32),
    builder: (BuildContext context) {
      return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.w),
          ),
          contentPadding: EdgeInsets.zero,
          content: CardBlock());
    },
  );
}
