import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class InterestsItem extends StatefulWidget {
  const InterestsItem({super.key});

  @override
  State<InterestsItem> createState() => _InterestsItemState();
}

class _InterestsItemState extends State<InterestsItem> {
  bool light = false;
  List<String> selectedTags = [];
  bool isExpanded = false;
  int numberOfTagsToShow = 13;
  List<String> availableTags = [
    "SoundCloud",
    "Spa",
    "Self Care",
    "Gin Tonic",
    "Gymnastics",
    "Meditation",
    "Spotify",
    "Sushi",
    "Hockey",
    "Basketball",
    "Slam Poetry",
    "Home Workout",
    "Blogging",
    "meditation",
    "Animal Care",
    "DIY Projects",
    "Gardening",
    "Yoga",
    "Fishing",
    "Swimming",
    "Board Games",
    "Surfing",
    "Dancing",
    "Collecting",
    "Camping",
    "Writing",
    "Archery"
  ];

  // Hàm xử lý khi click tag bên dưới
  void selectTag(String tag) {
    setState(() {
      selectedTags.add(tag);
      availableTags.remove(tag);
    });
  }

  // Hàm xử lý khi xóa tag bên trên
  void removeTag(String tag) {
    setState(() {
      availableTags.add(tag);
      selectedTags.remove(tag);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Lấy danh sách thẻ hiển thị dựa trên số lượng đã chọn
    List<String> displayedTags =
        availableTags.take(numberOfTagsToShow).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(LocaleKeys.Interests.tr(),
            style: Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                  color: Theme.of(context).textPrimary(context),
                )),
        Gap(8.h),
        Container(
          height: 60,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: Theme.of(context).textSecondary(context),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Wrap(
              spacing: 8,
              children: selectedTags
                  .map((tag) => Chip(
                        label: Text(tag),
                        side: BorderSide(
                          color: Theme.of(context).greyScale300(context),
                          width: 1,
                        ),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(16)),
                        ),
                        deleteIcon: const Icon(Icons.close),
                        onDeleted: () {
                          setState(() {
                            selectedTags.remove(tag);
                          });
                        },
                        labelStyle: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(),
                        backgroundColor:
                            Theme.of(context).whitePrimary(context),
                      ))
                  .toList(),
            ),
          ),
        ),
        Gap(8.h),
        Wrap(
          spacing: 8,
          children: displayedTags
              .map((tag) => GestureDetector(
                    onTap: () {
                      setState(() {
                        if (!selectedTags.contains(tag)) {
                          selectedTags.add(tag); // Chọn thẻ
                        } else {
                          selectedTags.remove(tag); // Bỏ chọn thẻ
                        }
                      });
                    },
                    child: Chip(
                      label: Text(tag),
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(16)),
                      ),
                      labelStyle: selectedTags.contains(tag)
                          ? Theme.of(context)
                              .textTheme
                              .lightBodyMediumBold
                              .copyWith(
                                color: Theme.of(context).backgroundRed(context),
                              )
                          : Theme.of(context)
                              .textTheme
                              .lightBodyMediumBold
                              .copyWith(
                                color: Theme.of(context).greyScale600(context),
                              ),
                      side: selectedTags.contains(tag)
                          ? BorderSide(
                              color: Theme.of(context).backgroundRed(context),
                              width: 2)
                          : BorderSide(
                              color: Theme.of(context).greyScale50(context),
                              width: 2),
                      backgroundColor: Theme.of(context).whitePrimary(context),
                    ),
                  ))
              .toList(),
        ),
        Gap(18.h),
        GestureDetector(
          onTap: () {
            setState(() {
              if (isExpanded) {
                // Thu gọn danh sách
                numberOfTagsToShow = 13;
              } else {
                // Hiển thị tất cả
                numberOfTagsToShow = availableTags.length;
              }
              isExpanded = !isExpanded;
            });
          },
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Spacer(),
              Text(
                isExpanded ? LocaleKeys.hide.tr() : LocaleKeys.More.tr(),
                style:
                    Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                          color: Theme.of(context).backgroundRed(context),
                        ),
              ),
              Gap(5.w),
              isExpanded
                  ? SvgPicture.asset(
                      AppAssets.arrowUpSvg,
                      colorFilter: ColorFilter.mode(
                        Theme.of(context).backgroundRed(context),
                        BlendMode.srcIn,
                      ),
                    )
                  : SvgPicture.asset(
                      AppAssets.extraCircleSvg,
                      colorFilter: ColorFilter.mode(
                        Theme.of(context).backgroundRed(context),
                        BlendMode.srcIn,
                      ),
                    )
            ],
          ),
        ),
      ],
    );
  }
}
