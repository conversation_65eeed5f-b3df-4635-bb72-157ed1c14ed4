import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/shared/widgets/sames/form_card_widget.dart';
import 'package:multime_app/shared/widgets/sames/icon_button_widget.dart';

class CardHomeDating extends StatelessWidget {
  final List<String> image;

  const CardHomeDating({super.key, required this.image});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      clipBehavior: Clip.none,
      children: [
        FormCardMTM(
          height: 450,
          width: 400,
          images: image,
        ),
        Positioned(
          bottom: -35,
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButtonMTM(
                    sizedIcon: 35,
                    icon: Icons.close,
                    colorIcon: Colors.black,
                  ),
                 Gap(100.w),
                  IconButtonMTM(
                    icon: Icons.favorite,
                    sizedIcon: 30,
                    colorIcon: Colors.red,
                  ),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }
}
