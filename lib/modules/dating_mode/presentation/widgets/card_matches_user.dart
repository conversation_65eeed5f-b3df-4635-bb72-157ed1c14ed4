import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/data/models/user_like_model.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class CardMatchesUser extends StatelessWidget {
  final UserLikeModel user;
  final Function() onTapSend;

  const CardMatchesUser({
    super.key,
    required this.user,
    required this.onTapSend,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          height: 269.h,
          width: 194.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            image: DecorationImage(
              image: NetworkImage(user.images[0]),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          bottom: 15,
          left: 15,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  RichText(
                      text: TextSpan(
                    children: [
                      TextSpan(
                        text: "${user.name}, ",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeBold
                            .copyWith(
                              color: Theme.of(context).whitePrimary(context),
                            ),
                      ),
                      TextSpan(
                        text: "${user.age}",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeMedium
                            .copyWith(
                          color: Theme.of(context).whitePrimary(context),
                            ),
                      ),
                    ],
                  )),
                  Text(
                    user.job,
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                ],
              ),
              Gap(27.h),
              //Nút Send
              InkWell(
                onTap: () {
                  onTapSend();
                },
                child: SvgPicture.asset(AppAssets.sendSvg),
              )
            ],
          ),
        )
      ],
    );
  }
}
