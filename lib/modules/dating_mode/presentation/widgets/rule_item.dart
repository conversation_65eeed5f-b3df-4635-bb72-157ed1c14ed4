import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class RuleItem extends StatelessWidget {
  final String title;
  final String description;
  final String? linkText;

  const RuleItem(
      {super.key,
      required this.title,
      required this.description,
      this.linkText});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, right: 60),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Icon(
              Icons.check,
              color: Theme.of(context).backgroundRed(context),
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.lightBodyLargeBold.copyWith(
                    color: Theme.of(context).textPrimary(context),
                  ),
            ),
          ]),
          RichText(
            text: TextSpan(
              text: description,
              style:
                  Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                        color: Theme.of(context).greyScale600(context),
                      ),
              children: linkText != null
                  ? [
                      TextSpan(
                        text: " $linkText",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(
                                color: Theme.of(context).greyScale600(context),
                                decoration: TextDecoration.underline),
                        recognizer: TapGestureRecognizer()..onTap = () {},
                      ),
                    ]
                  : [],
            ),
          ),
        ],
      ),
    );
  }
}
