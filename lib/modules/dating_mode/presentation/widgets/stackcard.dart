import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/data/models/user_like_model.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/showBotomSheet_dating.dart';
import 'package:multime_app/shared/widgets/sames/form_card_widget.dart';
import 'package:multime_app/shared/widgets/sames/icon_button_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class Stackcard extends StatefulWidget {
  const Stackcard({
    super.key,
    required this.userLikeModel,
    required this.onTapView,
    required this.onSwipeLeft,
    required this.onSwipeRight,
  });
  final UserLikeModel userLikeModel;
  final Function() onTapView;
  final Function() onSwipeLeft;
  final Function() onSwipeRight;
  @override
  State<Stackcard> createState() => _StackcardState();
}

class _StackcardState extends State<Stackcard> {
  int currentIndex = 0;

  List<bool> actionsExpanded = [
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false
  ];

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      clipBehavior: Clip.none,
      children: [
        FormCardMTM(
          height: 470.h,
          width: 396.w,
          images: widget.userLikeModel.images,
        ),
        Positioned(
          bottom: -35.h,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      widget.onSwipeLeft();
                    },
                    child: IconButtonMTM(
                      sizedIcon: 35,
                      icon: Icons.close,
                      colorIcon: Theme.of(context).greyScale900(context),
                    ),
                  ),
                  Gap(100.w),
                  GestureDetector(
                      onTap: () {
                        widget.onSwipeRight();
                      },
                      child: IconButtonMTM(
                        icon: Icons.favorite,
                        sizedIcon: 35,
                        colorIcon: Theme.of(context).backgroundRed(context),
                      )),
                ],
              ),
            ],
          ),
        ),
        Positioned(
          top: 48.h,
          right: 10.w,
          child: InkWell(
            onTap: () {
              report(context, actionsExpanded);
            },
            child: SvgPicture.asset(AppAssets.infoSvg),
          ),
        ),
        Positioned(
          bottom: 40.h,
          left: 20.w,
          right: 25.w,
          child: Padding(
            padding: const EdgeInsets.symmetric(),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichText(
                            text: TextSpan(children: [
                          TextSpan(
                            text: '${widget.userLikeModel.name},',
                            style: Theme.of(context)
                                .textTheme
                                .lightHeadingSmall
                                .copyWith(
                                  color: Colors.white,
                                ),
                          ),
                          TextSpan(
                            text: ' ${widget.userLikeModel.age}',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeRegular
                                .copyWith(
                                  color: Colors.white,
                                ),
                          ),
                        ])),
                        Text(
                          widget.userLikeModel.job,
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeRegular
                              .copyWith(
                                color: Colors.white,
                              ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    SvgPicture.asset(AppAssets.locationSvg),
                    Gap(2.w),
                    Text(
                      '6km',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeRegular
                          .copyWith(
                            color: Colors.white,
                          ),
                    ),
                  ],
                ),
                Gap(10.h),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            'I am a cheerful person who enjoys exploring new things in life...',
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(
                              color: Colors.white,
                            ),
                      ),
                      TextSpan(
                        text: LocaleKeys.seeMore.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumBold
                            .copyWith(
                              color: Colors.white,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
