import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class DividerTitle extends StatelessWidget {
  final String title;
  const DividerTitle({required this.title, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: 10.h,
      ),
      child: Row(
        children: [
           Expanded(
            child: Divider(color: Theme.of(context).errorText(context), thickness: 1),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              title,
              style:
                  Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                        color: Theme.of(context).textPrimary(context),
                      ),
            ),
          ),
           Expanded(
            child: Divider( color: Theme.of(context).textPrimary(context), thickness: 1),
          ),
        ],
      ),
    );
  }
}
