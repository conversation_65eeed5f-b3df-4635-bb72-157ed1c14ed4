import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/rule_item.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';


class DatingModePage extends StatelessWidget {
  const DatingModePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () => (context).push(RouteName.settingPage)),
        title: SvgPicture.asset(AppAssets.logoMultiSvg),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          Column(
            children: [
              Gap(20.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  LocaleKeys.welcomeDatingMode.tr(),
                  style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                    color: Theme.of(context).textPrimary(context),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Gap(10.h),
              Text(LocaleKeys.followHouseRulesText.tr(),
                  textAlign: TextAlign.center,
                  style:
                  Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color:Theme.of(context).greyScale600(context),
                  )),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 32.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Gap(24.h),
                    RuleItem(
                      title: LocaleKeys.ruleBeYourselfTitle.tr(),
                      description: LocaleKeys.ruleBeYourselfDescription.tr(),
                    ),
                    RuleItem(
                      title: LocaleKeys.ruleStaySafeTitle.tr(),
                      description: LocaleKeys.ruleStaySafeDescription.tr(),
                      linkText: LocaleKeys.ruleStaySafeLinkText.tr(),
                    ),
                    RuleItem(
                      title: LocaleKeys.rulePlayItCoolTitle.tr(),
                      description: LocaleKeys.rulePlayItCoolDescription.tr(),
                    ),
                    RuleItem(
                      title: LocaleKeys.ruleBeProactiveTitle.tr(),
                      description: LocaleKeys.ruleBeProactiveDescription.tr(),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 40.h,
            left: 5.w,
            right: 5.w,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: CustomeButtonDating(
                  onTap: () {
                    (context).push(RouteName.inforPage);
                  },
                  text: LocaleKeys.iAgreeButtonText.tr(),
                  colorBackground: Theme.of(context).textPrimary(context),
                  colorText: Theme.of(context).whitePrimary(context),
                  height: 48.h,
                  width: 396.w),
            ),
          )
        ],
      ),
    );
  }
}


