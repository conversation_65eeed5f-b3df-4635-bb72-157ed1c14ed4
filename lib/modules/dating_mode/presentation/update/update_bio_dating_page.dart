import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/edit_image.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/text_field_dating.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UpdateBioDatingPage extends StatelessWidget {
  const UpdateBioDatingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(
          LocaleKeys.Managewhoicanview.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                color: Theme.of(context).textPrimary(context),
              ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
          child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const EditImage(),
                const SizedBox(height: 5),
                TextFieldDating(
                  label: LocaleKeys.displayName.tr(),
                  hint: LocaleKeys.name.tr(),
                  maxLines: 1,
                ),
                TextFieldDating(
                  label: LocaleKeys.yearOfBirth.tr(),
                  hint: LocaleKeys.yearOfBirth.tr(),
                  maxLines: 1,
                ),
                TextFieldDating(
                  label: LocaleKeys.yourOccupation.tr(),
                  hint: LocaleKeys.yourOccupation.tr(),
                  maxLines: 1,
                ),
                TextFieldDating(
                    label: LocaleKeys.language.tr(),
                    hint: LocaleKeys.language.tr(),
                    maxLines: 1),
                TextFieldDating(
                  label: LocaleKeys.shortBio.tr(),
                  hint: LocaleKeys.shortBio.tr(),
                  maxLines: 5,
                ),
              ],
            ),
            Gap(10.h),
            CustomeButtonDating(
                onTap: () {},
                text: LocaleKeys.save.tr(),
                colorBackground: Theme.of(context).backgroundRed(context),
                colorText: Theme.of(context).whitePrimary(context),
                height: 48.h,
                width: 396.w)
          ],
        ),
      )),
    );
  }
}
