import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_cubit.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_state.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:multime_app/shared/widgets/sames/custom_dropdown_button.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/interests_item.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/l10n/locale_keys.g.dart';
import '../../../application/bottom_bar/bloc/application_bloc.dart';
import '../../../application/bottom_bar/bloc/application_event.dart';
import '../../../busines_mode/presentation/find_job/cubit/find_job_event.dart';

class SeekingScreen extends StatelessWidget {
   const SeekingScreen({super.key});



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(moden: LocaleKeys.dating.tr()),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(LocaleKeys.whoAreYouSeeking.tr(),
                    style:
                        Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                              color: Theme.of(context).textPrimary(context),
                            )),
              ),
              Gap(18.h),
              BlocBuilder<FindJobBloc, FindJobState>(
                  builder: (context, state) {
                return Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(LocaleKeys.age.tr(),
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeSemiBold
                                .copyWith(
                                  color: Theme.of(context).backgroundRed(context),
                                )),
                        Text(
                          "${state.start.toInt()} - ${state.end.toInt()}",
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumRegular
                              .copyWith(
                                color: Theme.of(context).textPrimary(context),
                              ),
                        ),
                      ],
                    ),
                    RangeSlider(
                      values: RangeValues(state.start, state.end),
                      min: 18,
                      inactiveColor: Colors.grey[300],
                      max: 60,
                      activeColor: Colors.red,
                      onChanged: (RangeValues values) {
                        context
                            .read<FindJobBloc>()
                            .add(UpdateAgeRange(values.start, values.end));
                      },
                    ),
                  ],
                );
              }),
              BlocBuilder<FindJobBloc, FindJobState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(LocaleKeys.distance.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeSemiBold
                                  .copyWith(
                                    color: Theme.of(context).textPrimary(context),
                                  )),
                          Text(
                            "${state.distance.toInt()} km",
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyMediumRegular
                                .copyWith(
                              color: Theme.of(context).textPrimary(context),
                                ),
                          ),
                        ],
                      ),
                      Slider(
                        value: state.distance,
                        min: 0,
                        max: 100,
                        divisions: 100,
                        inactiveColor: Colors.grey[300],
                        activeColor: Colors.red,
                        onChanged: (double value) {
                          context.read<FindJobBloc>().add(UpdateDistance(value));
                        },
                      ),
                    ],
                  );
                },
              ),
              Text(
                LocaleKeys.preferredGender.tr(),
                style: Theme.of(context).textTheme.labelLarge,
              ),
              Gap(8.h),
              CustomDropDownButton(
                arr: [
                  LocaleKeys.maleText.tr(),
                  LocaleKeys.femaleText.tr(),
                LocaleKeys.Dontinterested.tr(),
                ],
                hintText:   LocaleKeys.maleText.tr(),
              ),
              Gap(18.h),
              const InterestsItem(),
              SizedBox(
                height: 15.h,
              ),
              //Button
              CustomeButtonDating(
                  onTap: () {
                    (context).push(RouteName.datingPage);
                    context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
                  },
                  text: LocaleKeys.continueButton.tr(),
                  colorBackground: Theme.of(context).textPrimary(context),
                  colorText: Theme.of(context).whitePrimary(context),
                  height: 48.h,
                  width: 396.w),
              SizedBox(
                height: 25.h,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
