import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'package:multime_app/modules/dating_mode/presentation/info/bloc/info_dating_bloc.dart';

class HobbiesPage extends StatelessWidget {
  HobbiesPage({super.key});
  final TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<InfoDatingBloc, InfoDatingState>(
      builder: (context, state) {
        final List<String> displayHobbies = state.listFilterHobby.isNotEmpty
            ? state.listFilterHobby
            : state.listHobby;

        return Scaffold(
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Gap(40.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios),
                      onPressed: () =>  context.pop(),
                    ),
                    TextButton(
                      onPressed: () {
                        // context.read<InfoDatingBloc>().add(SaveSelectedHobbies());
                        context.pop();
                      },
                      child: Text('Xong'),
                    ),
                  ],
                ),
                Gap(12.h),
                Text('Sở Thích:'),
                Gap(12.h),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Wrap(
                    spacing: 8,
                    children: state.listHobbySearch.map((hobby) {
                      return Chip(
                        label: Text(hobby),
                        side: BorderSide(
                          color: Theme.of(context).backgroundRed(context),
                          width: 2,
                        ),
                        deleteIcon: const Icon(Icons.close),
                        onDeleted: () {
                          context.read<InfoDatingBloc>().add(DeleteHobby(hobby));
                        },
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(16)),
                        ),
                        labelStyle: Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                          color: Theme.of(context).backgroundRed(context),
                        ),
                        backgroundColor: Theme.of(context).whitePrimary(context),
                      );
                    }).toList(),
                  ),
                ),
                Gap(12.h),
                StrongBodyTextField(
                  controller: searchController,
                  onChanged: (value) {
                    context.read<InfoDatingBloc>().add(SearchHobby(value));
                  },
                  hintText: 'Tìm kiếm',
                  hintStyle: Theme.of(context)
                      .textTheme
                      .lightBodyMediumSemiBold
                      .copyWith(color: Theme.of(context).modeLightText(context)),
                ),
                Gap(12.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: Wrap(
                      spacing: 8,
                      children: displayHobbies.map((hobby) {
                        final isSelected = state.listHobbySearch.contains(hobby);
                        return GestureDetector(
                          onTap: () {
                            context.read<InfoDatingBloc>().add(ChangeListHobby(hobby));
                          },
                          child: Chip(
                            label: Text(hobby),
                            side: BorderSide(
                              color: isSelected
                                  ? Theme.of(context).backgroundRed(context)
                                  : Theme.of(context).greyScale50(context),
                              width: 2,
                            ),
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.all(Radius.circular(16)),
                            ),
                            labelStyle: isSelected
                                ? Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                              color: Theme.of(context).backgroundRed(context),
                            )
                                : Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                              color: Theme.of(context).greyScale600(context),
                            ),
                            backgroundColor: Theme.of(context).whitePrimary(context),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

