import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'package:multime_app/modules/dating_mode/presentation/info/bloc/info_dating_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/utils/image_choice.dart';

class InforDatingPage extends StatelessWidget {
  InforDatingPage({super.key});
  TextEditingController hobbyController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(moden: LocaleKeys.dating.tr()),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(LocaleKeys.aboutYou.tr(),
                    style:
                        Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                              color: Theme.of(context).textPrimary(context),
                            )),
              ),
              Gap(16.h),
              StrongBodyTextField(
                label: LocaleKeys.displayName.tr(),
                hintText: LocaleKeys.displayName.tr(),
                hintStyle: Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(
                      color: Theme.of(context).modeLightText(context),
                    ),
              ),
              Gap(22.h),
              StrongBodyTextField(
                label: LocaleKeys.youYearOfBirth.tr(),
                hintText: LocaleKeys.youYearOfBirth.tr(),
                hintStyle: Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(
                      color: Theme.of(context).modeLightText(context),
                    ),
              ),
              Gap(22.h),
              StrongBodyTextField(
                label: LocaleKeys.yourOccupation.tr(),
                hintText: LocaleKeys.yourOccupation.tr(),
                hintStyle: Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(
                      color: Theme.of(context).modeLightText(context),
                    ),
              ),
              Gap(22.h),
              StrongBodyTextField(
                label: LocaleKeys.shortBio.tr(),
                hintText: LocaleKeys.yourBio.tr(),
                maxLines: 5,
                hintStyle: Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(
                      color: Theme.of(context).modeLightText(context),
                    ),
              ),
              Gap(22.h),
              BlocBuilder<InfoDatingBloc, InfoDatingState>(
                  builder: (context, state) {
                if (state.listHobbySearch.isNotEmpty) {
                  var temp = state.listHobbySearch.join(', ');
                  hobbyController.text = temp;
                }
                return StrongBodyTextField(
                  showCountryDialog: () {
                    context.push(RouteName.hobbiesPage);
                  },
                  controller: hobbyController,
                  readOnly: true,
                  hintText: 'Hobbies',
                  isSuffixIcon: true,
                  hintStyle: Theme.of(context)
                      .textTheme
                      .lightBodyMediumSemiBold
                      .copyWith(
                        color: Theme.of(context).modeLightText(context),
                      ),
                  suffixIcon: const Icon(Icons.keyboard_arrow_right_outlined),
                );
              }),
              // const InterestsItem(),
              Gap(22.h),
              Center(
                child: Column(
                  children: [
                    Text(LocaleKeys.addYourPhotos.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold),
                    Gap(8.h),
                    Text(
                      LocaleKeys.addAtLeast2.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallMedium
                          .copyWith(
                            color: Theme.of(context).modeLightText(context),
                          ),
                    ),
                  ],
                ),
              ),
              Gap(16.h),
              BlocBuilder<InfoDatingBloc, InfoDatingState>(
                builder: (context, state) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: List.generate(3, (index) {
                      String? imagePath = index < state.imagePaths.length
                          ? state.imagePaths[index]
                          : null;
                      return Stack(
                        clipBehavior: Clip.none,
                        children: [
                          imagePath != null
                              ? Container(
                                  height: 120.h,
                                  width: 90.w,
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).greyScale50(context),
                                  ),
                                  child: Image.file(
                                    File(imagePath),
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : DottedBorder(
                                  borderType: BorderType.RRect,
                                  color:
                                      Theme.of(context).greyScale500(context),
                                  radius: const Radius.circular(8),
                                  dashPattern: const [5, 4],
                                  strokeWidth: 1,
                                  child: Container(
                                    height: 120.h,
                                    width: 90.w,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context)
                                          .greyScale50(context),
                                    ),
                                  ),
                                ),
                          Positioned(
                            bottom: -8.h,
                            right: -8.w,
                            child: imagePath != null
                                ? InkWell(
                                    onTap: () {
                                      context.read<InfoDatingBloc>().add(
                                            RemoveImageEvent(imagePath),
                                          );
                                    },
                                    child: Container(
                                      width: 24.w,
                                      height: 24.h,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .whitePrimary(context),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                            AppAssets.trash_newSvg),
                                      ),
                                    ),
                                  )
                                : InkWell(
                                    onTap: () async {
                                      final pickedPath = await showImageChoices(
                                          context: context);
                                      if (pickedPath.isNotEmpty) {
                                        context.read<InfoDatingBloc>().add(
                                              AddImageEvent(pickedPath),
                                            );
                                      }
                                    },
                                    child: Container(
                                      width: 24.w,
                                      height: 24.h,
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .errorText(context),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                            AppAssets.unionRedSvg),
                                      ),
                                    ),
                                  ),
                          )
                        ],
                      );
                    }),
                  );
                },
              ),

              Gap(16.h),
              StrongBodyButton(
                  onPressed: () {
                    (context).push(RouteName.seekingScreen);
                  },
                  height: 40,
                  label: LocaleKeys.continueButton.tr()),
              Gap(24.h)
            ],
          ),
        ),
      ),
    );
  }
}
