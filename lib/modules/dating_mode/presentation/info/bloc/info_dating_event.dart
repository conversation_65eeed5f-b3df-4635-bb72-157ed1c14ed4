
part of 'info_dating_bloc.dart';
abstract class InfoDatingEvent extends Equatable {
  const InfoDatingEvent();

  @override
  List<Object> get props => [];
}

class ChangeListHobby extends InfoDatingEvent {
  final String hobby;
  const ChangeListHobby(this.hobby);

  @override
  List<Object> get props => [hobby];
}
class SearchHobby extends InfoDatingEvent {
  final String hobby;
  const SearchHobby(this.hobby);

  @override
  List<Object> get props => [hobby];
}

class DeleteHobby extends InfoDatingEvent {
  final String hobby;
  const DeleteHobby(this.hobby);

  @override
  List<Object> get props => [hobby];
}
class SaveSelectedHobbies extends InfoDatingEvent {}

class AddImageEvent extends InfoDatingEvent {
  final String imagePath;
  const AddImageEvent(this.imagePath);

  @override
  List<Object> get props => [imagePath];
}
class RemoveImageEvent extends InfoDatingEvent {
  final String imagePath;
  const RemoveImageEvent(this.imagePath);

  @override
  List<Object> get props => [imagePath];
}
