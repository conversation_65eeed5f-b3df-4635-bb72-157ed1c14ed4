part of 'info_dating_bloc.dart';

class InfoDatingState extends Equatable {
  final String displayName;
  final String yearOfBirth;
  final String occupation;
  final String shortBio;
  final List<String> listHobby;
  final List<String> listHobbySearch;
  final List<String> listFilterHobby;
  // final List<String> selectedHobbies;
  final List<String> imagePaths;

  const InfoDatingState({
    required this.displayName,
    required this.yearOfBirth,
    required this.occupation,
    required this.shortBio,
    required this.listHobby,
    this.listHobbySearch = const [],
    this.listFilterHobby = const [],
    // this.selectedHobbies = const [],
    this.imagePaths = const [],
  });

  factory InfoDatingState.initial() {
    return const InfoDatingState(
      displayName: '',
      yearOfBirth: '',
      occupation: '',
      shortBio: '',
      listHobby: [
        "SoundCloud", "Spa", "Self Care", "Gin Tonic", "Gymnastics",
        "Meditation", "Spotify", "Sushi", "Hockey", "Basketball",
        "Slam Poetry", "Home Workout", "Blogging", "meditation",
        "Animal Care", "DIY Projects", "Gardening", "Yoga", "Fishing",
        "Swimming", "Board Games", "Surfing", "Dancing", "Collecting",
        "Camping", "Writing", "Archery"
      ],
      listFilterHobby: [],
      listHobbySearch: [],
      // selectedHobbies: [],
      imagePaths: [],
    );
  }

  InfoDatingState copyWith({
    String? displayName,
    String? yearOfBirth,
    String? occupation,
    String? shortBio,
    List<String>? listHobby,
    List<String>? listHobbySearch,
    List<String>? listFilterHobby,
    // List<String>? selectedHobbies,
    List<String>? imagePaths,
  }) {
    return InfoDatingState(
      displayName: displayName ?? this.displayName,
      yearOfBirth: yearOfBirth ?? this.yearOfBirth,
      occupation: occupation ?? this.occupation,
      shortBio: shortBio ?? this.shortBio,
      listHobby: listHobby ?? this.listHobby,
      listHobbySearch: listHobbySearch ?? this.listHobbySearch,
      listFilterHobby: listFilterHobby ?? this.listFilterHobby,
      // selectedHobbies: selectedHobbies ?? this.selectedHobbies,
      imagePaths: imagePaths ?? this.imagePaths,
    );
  }

  @override
  List<Object> get props => [
    displayName,
    yearOfBirth,
    occupation,
    shortBio,
    listHobby,
    listHobbySearch,
    listFilterHobby,
    // selectedHobbies,
    imagePaths,
  ];
}
