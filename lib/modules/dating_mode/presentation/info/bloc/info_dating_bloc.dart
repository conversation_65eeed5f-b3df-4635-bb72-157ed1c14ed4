import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
part 'info_dating_event.dart';
part 'info_dating_state.dart';

class InfoDatingBloc extends Bloc<InfoDatingEvent, InfoDatingState> {
  InfoDatingBloc() : super(InfoDatingState.initial()) {
    // Xử lý sự kiện chọn/bỏ chọn sở thích yêu thích
    on<ChangeListHobby>((event, emit) {
      List<String> updatedHobbies = List.from(state.listHobbySearch);
      if (updatedHobbies.contains(event.hobby)) {
        updatedHobbies.remove(event.hobby);
      } else {
        updatedHobbies.add(event.hobby);
      }
      emit(state.copyWith(listHobbySearch: updatedHobbies));
    });

    // Xử lý tìm kiếm sở thích
    on<SearchHobby>((event, emit) {
      final filteredHobbies = state.listHobby
          .where((hobby) =>
              hobby.toLowerCase().contains(event.hobby.toLowerCase()))
          .toList();

      emit(state.copyWith(listFilterHobby: filteredHobbies));
    });

    // X<PERSON> lý xóa sở thích
    on<DeleteHobby>((event, emit) {
      List<String> updatedHobbies = List.from(state.listHobbySearch);
      updatedHobbies.remove(event.hobby);
      emit(state.copyWith(listHobbySearch: updatedHobbies));
    });
    // Lưu sở thích đã chọn
    // on<SaveSelectedHobbies>((event, emit) {
    //   emit(state.copyWith(selectedHobbies: state.selectedHobbies));
    // });

    // Xử lý thêm chọn ảnh
    on<AddImageEvent>(_onAddImageEvent);
    on<RemoveImageEvent>(_onRemoveImageEvent);
  }

  Future<void> _onAddImageEvent(
      AddImageEvent event, Emitter<InfoDatingState> emit) async {
    if (state.imagePaths.length < 3) {
      List<String> updatedImagePaths = List.from(state.imagePaths);
      updatedImagePaths.add(event.imagePath);
      emit(state.copyWith(imagePaths: updatedImagePaths));
    }
  }

  Future<void> _onRemoveImageEvent(
      RemoveImageEvent event, Emitter<InfoDatingState> emit) async {
    List<String> updatedImagePaths = List.from(state.imagePaths);
    updatedImagePaths.remove(event.imagePath);
    emit(state.copyWith(imagePaths: updatedImagePaths));
  }
}
