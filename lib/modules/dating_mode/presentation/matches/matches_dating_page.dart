import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/data/fakeData/useLikeMode.dart';
import 'package:multime_app/modules/dating_mode/data/models/user_like_model.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/card_matches_user.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/divider_title_dating.dart';

class MatchesDatingPage extends StatelessWidget {
  const MatchesDatingPage({super.key});

  @override
  Widget build(BuildContext context) {
    Widget buildUserGrid(String title, List<UserLikeModel> users) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DividerTitle(title: title),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(vertical: 5),
            itemCount: users.length,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              childAspectRatio: 3 / 4,
            ),
            itemBuilder: (context, index) {
              return CardMatchesUser(
                user: users[index],
                onTapSend: () {
                  (context).push(RouteName.matchesSuccessPage);
                },
              );
            },
          ),
        ],
      );
    }

    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: EdgeInsets.only(left: 15.w),
          child: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              context.pop();
            },
          ),
        ),
        title: Text(
          LocaleKeys.matches.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                color: Theme.of(context).errorText(context),
              ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            children: [
              Column(
                children: [
                  Gap(10.h),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.reviewThePeopleYouHaveSwipedLeftOn.tr(),
                      ),
                      buildUserGrid(LocaleKeys.today.tr(), dummyUsers),
                      buildUserGrid(LocaleKeys.Yes.tr(), dummyUsers)
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
