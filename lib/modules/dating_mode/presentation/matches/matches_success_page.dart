import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';

class MatchesSuccessPage extends StatelessWidget {
  const MatchesSuccessPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).backgroundRed(context), // M<PERSON>u nền đỏ
      body: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            bottom: -MediaQuery.of(context).size.height * 0.26.h,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: Lottie.asset('assets/aimation/animation_load.json',fit: BoxFit.contain),
            ),
          ),
          Column(
            children: [
              SizedBox(
                height: 60.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 290.w,
                    height: 390.h,
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        // Ảnh bên phải
                        Positioned(
                          top: 20.h,
                          right: 10.w,
                          child: Transform.rotate(
                            angle: pi / 15, // Góc xoay thuận chiều kim đồng hồ
                            child: _buildImageContainer(
                              'https://kenh14cdn.com/203336854389633024/2023/7/8/photo-9-16888099662661094157342.jpg', // Ảnh nam
                            ),
                          ),
                        ),
                        // Ảnh bên trái
                        Positioned(
                          bottom: 45.h,
                          left: 25
                              .w, // Giảm khoảng cách để ảnh bên trái đè lên ảnh bên phải
                          child: Transform.rotate(
                            angle: -pi / 15, // Góc xoay ngược chiều kim đồng hồ
                            child: _buildImageContainer(
                              'https://kenh14cdn.com/203336854389633024/2023/7/8/photo-9-16888099662661094157342.jpg', // Ảnh nữ
                            ),
                          ),
                        ),
                        Positioned(
                          left: 120.w,
                          child: Transform.rotate(
                              angle:
                                  pi / 15, // Góc xoay thuận chiều kim đồng hồ
                              child: _buildIconContainer(context)),
                        ),
                        Positioned(
                          bottom: 15.h,
                          left: 30.w,
                          child: Transform.rotate(
                              angle:
                                  -pi / 15, // Góc xoay ngược chiều kim đồng hồ
                              // Góc xoay thuận chiều kim đồng hồ
                              child: _buildIconContainer(context)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Text(
                LocaleKeys.itsAMatch.tr(),
                style: Theme.of(context).textTheme.headlineMedium,
                textAlign: TextAlign.center,
              ),
              Gap(10.h),
              // Nội dung phụ
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Text(
                  LocaleKeys.startAConversationNow.tr(),
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w400,
                      ),
                ),
              ),
              SizedBox(height: 120.h),
              CustomeButtonDating(
                  onTap: () {},
                  text: LocaleKeys.startChatting.tr(),
                  colorBackground: Theme.of(context).whitePrimary(context),
                  colorText: Theme.of(context).errorText(context),
                  height: 50.h,
                  width: 260.w),
              Gap(10.h),
              Text(LocaleKeys.keepSwiping.tr(),
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 20)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageContainer(String imageUrl) {
    return Container(
      height: 240.h,
      width: 160.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        image: DecorationImage(
          image: NetworkImage(imageUrl),
          fit: BoxFit.cover,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
    );
  }

  Widget _buildIconContainer(BuildContext context) {
    return  CircleAvatar(
        backgroundColor: Colors.white,
        radius: 25,
        child: Icon(Icons.favorite, color: Theme.of(context).backgroundRed(context)));
  }
}
