import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/dating_mode/data/fakeData/useLikeMode.dart';
import 'package:multime_app/modules/dating_mode/data/models/user_like_model.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/card_user_like.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/divider_title_dating.dart';

class LikedYouScreen extends StatefulWidget {
  const LikedYouScreen({super.key});

  @override
  State<LikedYouScreen> createState() => _LikedYouScreenState();
}

class _LikedYouScreenState extends State<LikedYouScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget buildGridView(String title, String title2, List<UserLikeModel> users) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DividerTitle(title: title),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: users.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 10,
                crossAxisSpacing: 10,
                childAspectRatio: 3 / 4,
              ),
              itemBuilder: (context, index) {
                return CardUserLike(
                  user: users[index],
                  onTapView: () {},
                );
              },
            ),
            DividerTitle(title: title2),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: users.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 10,
                crossAxisSpacing: 10,
                childAspectRatio: 3 / 4,
              ),
              itemBuilder: (context, index) {
                return CardUserLike(
                  user: users[index],
                  onTapView: () {},
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              context.pop();
            },
          ),
        ),
        title: Text(
          LocaleKeys.likedYou.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                color: Theme.of(context).backgroundRed(context),
              ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: TabBar(
              controller: _tabController,
              labelStyle: Theme.of(context).textTheme.bodySmall!.copyWith(
                    color: Theme.of(context).errorText(context),
                    fontWeight: FontWeight.w400,
                  ),
              indicatorSize: TabBarIndicatorSize.tab,
              unselectedLabelStyle:
                  Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: Colors.grey,
                        fontWeight: FontWeight.w300,
                      ),
              dividerColor: Colors.transparent,
              indicator:  BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).errorText(context),
                    width: 2,
                  ),
                ),
              ),
              tabs: [
                Tab(text: LocaleKeys.youLikedThem.tr()),
                Tab(text: LocaleKeys.theyLikedYou.tr()),
              ],
            ),
          ),
          Gap(16.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
            LocaleKeys.Reviewthepeople.tr()  ,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: Theme.of(context).textPrimary(context),
                    fontWeight: FontWeight.w400,
                  ),
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Tab "You liked them"
                buildGridView(LocaleKeys.today.tr(), LocaleKeys.yesterday.tr(),
                    dummyUsers),

                // Tab "They liked you"
                buildGridView(LocaleKeys.today.tr(), LocaleKeys.yesterday.tr(),
                    dummyUsers),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
