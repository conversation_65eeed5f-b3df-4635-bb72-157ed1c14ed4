// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:gap/gap.dart';
// import 'package:go_router/go_router.dart';
// import 'package:multime_app/app/routers/routers_name.dart';
// import 'package:multime_app/core/constants/app_assets.dart';
// import 'package:multime_app/core/constants/app_spacings.dart';
// import 'package:multime_app/core/l10n/locale_keys.g.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:multime_app/core/themes/app_text_style.dart';
// import 'package:multime_app/core/themes/theme.dart';
//
// class AddPhoto extends StatelessWidget {
//   final int index;
//   const AddPhoto({super.key, required this.index});
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         leadingWidth: 100.w,
//         leading: Padding(
//           padding: EdgeInsets.symmetric(horizontal: 16.w),
//           child: InkWell(
//             onTap: () {
//               (context).push(RouteName.inforPage);
//             },
//             child: Text(
//               LocaleKeys.cancel.tr(),
//               style: Theme.of(context).textTheme.bodyLarge!.copyWith(
//                   color: Theme.of(context).backgroundRed(context)),
//             ),
//           ),
//         ),
//       ),
//       body: Padding(
//         padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
//         child: Column(children: [
//
//           BlocBuilder<ImageBloc, ImageState>(
//             builder: (context, state) {
//               return Column(
//                 children: [
//                   actionItem(
//                     context,
//                     'Camera',
//                     AppAssets.cameraSvg,
//                         ()  {
//                        context.read<ImageBloc>().add(PickCamerasEvent(index));
//                       context.push(RouteName.inforPage);
//                     },
//                   ),
//                   actionItem(context, 'Gallery', AppAssets.pictureSvg,
//                           ()  {
//                          context.read<ImageBloc>().add(PickImagesEvent(index));
//                         context.push(RouteName.inforPage);
//                       }),
//                 ],
//               );
//             },
//           )
//         ]),
//       ),
//     );
//   }
//
//
//   Widget actionItem(BuildContext context, String title, String iconPath,
//       VoidCallback onTapAction) {
//     return InkWell(
//       onTap: onTapAction,
//       child: Container(
//         height: 86.h,
//         width: 396.w,
//         padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 10.w),
//         decoration:  BoxDecoration(
//           color: Theme.of(context).whitePrimary(context),
//           border: Border(
//             bottom: BorderSide(color: Theme.of(context).lightGrey(context), width: 1),
//           ),
//         ),
//         child: Row(
//           children: [
//             SvgPicture.asset(iconPath),
//             Gap(16.w),
//             Text(
//               title,
//               style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
//                 color: Theme.of(context).textPrimary(context),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
