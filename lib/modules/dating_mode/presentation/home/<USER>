import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';

import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:multime_app/modules/dating_mode/data/fakeData/dataStackCard.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/button_dating.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/modules/dating_mode/presentation/widgets/home_card.dart';


class HomeDatingPage extends StatefulWidget {
  const HomeDatingPage({
    super.key,
  });

  @override
  State<HomeDatingPage> createState() => _HomeDatingPageState();
}

class _HomeDatingPageState extends State<HomeDatingPage> {
  int _currentIndex = 0;
  final CardSwiperController controller = CardSwiperController();

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _onTabSelected(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(moden: LocaleKeys.dating.tr()),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Gap(10.h),
          Expanded(
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: HomeCard(cards: cards)),
          ),
          Container(
            padding: EdgeInsets.only(top: 15.h, bottom: 15.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                ButtonDatting(
                  text: LocaleKeys.recent.tr(),
                  icon: Icons.access_time,
                  onTap: () {
                    (context).push(RouteName.recentDatingnPage);
                  },
                ),
                ButtonDatting(
                  text: LocaleKeys.likedYou.tr(),
                  icon: Icons.workspace_premium,
                  onTap: () {
                    (context).push(RouteName.likedYouScreen);
                  },
                ),
                ButtonDatting(
                  text: LocaleKeys.matches.tr(),
                  icon: Icons.favorite_sharp,
                  onTap: () {
                    (context).push(RouteName.matchesDatingPage);
                  },
                ),
              ],
            ),
          ),
          Gap(25.h)
        ],
      ),
    );
  }
}
