import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/application/bottom_bar/application_page.dart';

import 'package:multime_app/modules/application/bottom_bar/bottomSheet_model.dart';
import 'package:multime_app/modules/dating_mode/presentation/home/<USER>';

import '../../../../core/l10n/locale_keys.g.dart';

// ignore: depend_on_referenced_packages

class DatingPage extends StatelessWidget {
  const DatingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ApplicationPage(
      page: const HomeDatingPage(),
      bottomSheetItems: [
        BottomSheetItem(
          name: LocaleKeys.editBio.tr(),
          iconLead: AppAssets.editSvg,
          onTap: () {
            (context).push(RouteName.editBioPage);
          },
        ),
        BottomSheetItem(
          name: LocaleKeys.editSeeking.tr(),
          iconLead: AppAssets.edit_seeking_icon,
          onTap: () {
            (context).push(RouteName.editSeekingPage);
          },
        ),
      ],
    );
  }
}
