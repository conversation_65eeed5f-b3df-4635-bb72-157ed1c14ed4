import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../core/constants/app_assets.dart';

class MyProductPage extends StatefulWidget {
  const MyProductPage({super.key});

  @override
  State<MyProductPage> createState() => _MyProductPageState();
}

class _MyProductPageState extends State<MyProductPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          title: Text(
            "My Product",
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: Center(
        child: Text('My Product Page'),
      ),
    );
  }
}
