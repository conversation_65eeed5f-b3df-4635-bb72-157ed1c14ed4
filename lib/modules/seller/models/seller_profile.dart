import 'package:multime_app/shared/models/user/user_model.dart';

import '../../../core/model/category_model.dart';
import '../../../core/model/certificates/certificates_model.dart';
import '../../../core/model/edutcation/education_model.dart';
import '../../../core/model/experience/experience_model.dart';
import '../../marketplace_mode/data/models/address.dart';

class SellerProfileModel {
  final int id;
  final int userId;
  final UserModel user;
  final int salesUserId;
  final UserModel? salesUser;
  final List<CategoryModel> categories;
  final String bannerPicture;
  final String profession;
  final String shopName;
  final List<String>? gallery;
  final String about;
  final String cv;
  final int? organizationId;
  final bool isOrgOwner;
  final String linkShop;
  final List<String> skills;
  final List<ExperienceModel> experiences;
  final List<EducationModel> educations;
  final List<Address> addresses;
  final List<dynamic> bankAccounts;
  final List<CertificateModel> certificates;

  SellerProfileModel({
    required this.id,
    required this.userId,
    required this.user,
    required this.bannerPicture,
    required this.profession,
    required this.shopName,
    required this.about,
    this.organizationId,
    required this.isOrgOwner,
    required this.skills,
    required this.experiences,
    required this.educations,
    required this.addresses,
    required this.bankAccounts,
    required this.certificates,
    required this.categories,
    required this.salesUserId,
    this.salesUser,
    this.gallery,
    required this.cv,
    required this.linkShop,
  });
  factory SellerProfileModel.fromJson(Map<String, dynamic> json) {
    return SellerProfileModel(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      user: UserModel.fromJson(json['user'] ?? {}),
      bannerPicture: json['banner_picture'] ?? '',
      profession: json['profession'] ?? '',
      shopName: json['shop_name'] ?? '',
      about: json['about'] ?? '',
      organizationId: json['organization_id'],
      isOrgOwner: json['is_org_owner'] ?? false,
      skills: json['skills'] != null ? List<String>.from(json['skills']) : [],
      experiences: (json['experiences'] as List<dynamic>?)
              ?.map((e) => ExperienceModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      educations: (json['educations'] as List<dynamic>?)
              ?.map((e) => EducationModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      addresses: (json['addresses'] as List<dynamic>?)
              ?.map((e) => Address.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      bankAccounts: json['bank_accounts'] ?? [],
      certificates: (json['certificates'] as List<dynamic>?)
              ?.map((e) => CertificateModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      categories: (json['categories'] as List<dynamic>?)
              ?.map((e) => CategoryModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      salesUserId: json['sales_user_id'] ?? 0,
      salesUser: json['sales_user'] != null
          ? UserModel.fromJson(json['sales_user'])
          : null,
      gallery:
          json['gallery'] != null ? List<String>.from(json['gallery']) : null,
      cv: json['cv'] ?? '',
      linkShop: json['link_shop'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user': user.toJson(),
      'banner_picture': bannerPicture,
      'profession': profession,
      'shop_name': shopName,
      'about': about,
      'organization_id': organizationId,
      'is_org_owner': isOrgOwner,
      'skills': skills,
      'experiences': experiences,
      'educations': educations,
      'addresses': addresses,
      'bank_accounts': bankAccounts,
      'certificates': certificates,
      'categories': categories.map((category) => category.toJson()).toList(),
      'sales_user_id': salesUserId,
      'sales_user': salesUser?.toJson(),
      'gallery': gallery,
      'cv': cv,
      'link_shop': linkShop,
    };
  }
}
