class OtpResponse {
  final OtpData? data;

  OtpResponse({this.data});

  factory OtpResponse.fromJson(Map<String, dynamic> json) {
    return OtpResponse(
      data: json['data'] != null
          ? OtpData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }
}

class OtpData {
  final String? secretToken;

  OtpData({this.secretToken});
  factory OtpData.fromJson(Map<String, dynamic> json) {
    return OtpData(
      secretToken: json['secret_token'] as String?,
    );
  }
}
