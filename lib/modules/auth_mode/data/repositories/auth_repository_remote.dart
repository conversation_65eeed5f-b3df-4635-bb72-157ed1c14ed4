import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/auth_mode/data/models/login_response.dart';
import 'package:multime_app/modules/auth_mode/data/services/auth_services.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/modules/auth_mode/response/otp_response.dart';
import 'package:multime_app/modules/setting_mode/data/user_reponse.dart';
import 'package:multime_app/shared/models/user/user_model.dart';
import '../../../../core/network/api.dart';
import '../../../../core/network/api_type.dart';

abstract class AuthRepository {
  Future<UserModel> createUser(Map<String, dynamic> data);
  Future<UserModel> getUser({required String userid});
  Future<ApiResponse<dynamic>> signUp(Map<String, dynamic> data);
  Future<ApiResponse<LoginResponse>> loginWithAccount(
      Map<String, dynamic> data);
  Future<ApiResponse<LoginResponse>> loginWithGoogle();
  Future<ApiResponse<LoginResponse>> loginWithFacebook();
  Future<ApiResponse<LoginResponse>> loginWithApple();
  Future<ApiResponse<dynamic>> sendVerificationEmail(Map<String, dynamic> data);
  Future<ApiResponse<dynamic>> changePassword(
      Map<String, dynamic> data, String token);
  Future<ApiResponse> signOut(String token);
  Future<ApiResponse<UserModelResponse>> updateUser(dynamic data, String token);
  Future<ApiResponse> updateAvatar(dynamic data, String token);
  Future<ApiResponse> updateBackground(dynamic data, String token);
  Future<ApiResponse> forgotPassword(String email);
  Future<ApiResponse> resetPassword(String newPassword, String secretToken);
  Future<ApiResponse<OtpResponse>> verifyOTPForgotPassword(
      String otp, String email, OtpFlowType flowType);
  Future<ApiResponse<OtpResponse>> verifyOTPEmail(
      String otp, String email, OtpFlowType flowType);
}

class AuthRepositoryRemote implements AuthRepository {
  final AuthServices authServices;
  final ApiClient _apiClient;
  AuthRepositoryRemote(
    this._apiClient, {
    required this.authServices,
  });
  @override
  Future<UserModel> createUser(Map<String, dynamic> data) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.user, method: ApiType.post, data: data);
      return UserModel.fromJson(response);
    } catch (e) {
      print("create user m error: $e");
      rethrow;
    }
  }

  @override
  Future<UserModel> getUser({required String userid}) async {
    try {
      final response = await _apiClient.request(
          path: "${ApiConst.user}/$userid", method: ApiType.get);

      debugPrint('getUser response: $response');

      // Handle different response formats
      if (response is Map<String, dynamic>) {
        // Check if response has error code
        if (response.containsKey('code') && response['code'] != 200) {
          throw NetworkException(
              message: response['message'] ?? 'User not found');
        }

        // Check if response has data field
        if (response.containsKey('data')) {
          final userData = response['data'];
          if (userData == null) {
            throw NetworkException(message: 'User data is null');
          }
          return UserModel.fromJson(userData);
        }

        // Assume response itself is user data
        return UserModel.fromJson(response);
      }

      throw NetworkException(message: 'Invalid response format');
    } catch (e) {
      debugPrint("get user error: $e");
      rethrow;
    }
  }

  @override
  Future<ApiResponse<LoginResponse>> loginWithAccount(
      Map<String, dynamic> data) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.login,
        method: ApiType.post,
        data: data,
        headers: {'Scope': '${AppConstants.multi_me}'},
      );
      return ApiResponse.completed(LoginResponse.fromJson(response));
    } on DioException catch (e) {
      final errorData = e.response?.data;
      if (errorData is Map<String, dynamic>) {
        return ApiResponse.error(
          errorData['message'] ?? 'Login failed',
          code: errorData['code'],
        );
      }
      return ApiResponse.error('Login failed: ${e.message ?? 'Unknown error'}');
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error(
          'An unexpected error occurred. Please try again.');
    }
  }

  @override
  Future<ApiResponse<LoginResponse>> loginWithApple() async {
    try {
      final result = await authServices.signInWithApple();
      if (result != null) {
        return result;
      } else {
        return ApiResponse.error("Apple sign-in failed");
      }
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error("Apple sign-in error: $e");
    }
  }

  @override
  Future<ApiResponse<LoginResponse>> loginWithFacebook() async {
    try {
      final result = await authServices.signInWithFacebook();
      if (result != null) {
        return result;
      } else {
        return ApiResponse.error("Facebook sign-in failed");
      }
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error("Facebook sign-in error: $e");
    }
  }

  @override
  Future<ApiResponse<LoginResponse>> loginWithGoogle() async {
    try {
      final result = await authServices.googleSignIn();
      if (result != null) {
        return result;
      } else {
        return ApiResponse.error("Google sign-in failed");
      }
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error("Google sign-in error: $e");
    }
  }

  @override
  Future<ApiResponse<dynamic>> sendVerificationEmail(
      Map<String, dynamic> data) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.resendOtp,
          method: ApiType.post,
          data: data,
          headers: {'Scope': '${AppConstants.strongbody_ai}'});
      return ApiResponse.completed(response);
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('Failed to send otp.');
    }
  }

  @override
  Future<ApiResponse> signUp(Map<String, dynamic> data) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.register,
        method: ApiType.post,
        data: data,
        headers: {'Scope': '${AppConstants.multi_me}'},
      );

      return ApiResponse.completed(response);
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error(
        'Email already exists or registration failed. Please try again.',
      );
    }
  }

  @override
  Future<ApiResponse> changePassword(
      Map<String, dynamic> data, String token) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.changePasswordUser,
        method: ApiType.patch,
        data: data,
        headers: {
          'Scope': '${AppConstants.multi_me}',
          'Authorization': 'Bearer $token',
        },
      );
      return ApiResponse.completed(response);
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('Current password is incorrect');
    }
  }

  @override
  Future<ApiResponse> signOut(String token) async {
    return await authServices.signOutAll(token);
  }

  @override
  Future<ApiResponse<UserModelResponse>> updateUser(
      dynamic data, String token) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.updateProfile,
          method: ApiType.put,
          data: data,
          headers: {
            'Scope': '${AppConstants.multi_me}',
            'Authorization': 'Bearer $token',
          });
      return ApiResponse.completed(UserModelResponse.fromJson(response));
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('Failed to update user profile.');
    }
  }

  @override
  Future<ApiResponse> updateAvatar(dynamic data, String token) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.updateAvatar,
          method: ApiType.put,
          data: data,
          headers: {
            'Scope': '${AppConstants.multi_me}',
            'Authorization': 'Bearer $token',
          });
      return ApiResponse.completed(response);
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('Failed to update avatar.');
    }
  }

  @override
  Future<ApiResponse> updateBackground(dynamic data, String token) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.updateBackground,
          method: ApiType.put,
          data: data,
          headers: {
            'Scope': '${AppConstants.multi_me}',
            'Authorization': 'Bearer $token',
          });
      return ApiResponse.completed(response);
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('Failed to update background.');
    }
  }

  @override
  Future<ApiResponse> forgotPassword(String email) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.forgotPassword,
        method: ApiType.post,
        data: {"email": email},
        headers: {
          'Scope': '${AppConstants.multi_me}',
          'x-api-key': 'your_api_key'
        },
      );
      return ApiResponse.completed(response);
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('Failed to reset password.');
    }
  }

  @override
  Future<ApiResponse> resetPassword(
      String newPassword, String secretToken) async {
    try {
      final result = await _apiClient.request(
        method: ApiType.patch,
        path: ApiConst.resetPassword,
        data: {
          'secret_token': secretToken,
          'new_password': newPassword,
        },
        headers: {
          'Scope': '${AppConstants.multi_me}',
          'x-api-key': 'your_api_key'
        },
      );
      if (result != null) {
        return ApiResponse.completed(result);
      } else {
        return ApiResponse.error("Reset password failed");
      }
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error("Reset password error: $e");
    }
  }

  @override
  Future<ApiResponse<OtpResponse>> verifyOTPForgotPassword(
      String email, String otp, OtpFlowType type) async {
    try {
      dynamic response = await _apiClient.request(
        method: ApiType.post,
        path: ApiConst.verifyForgotPassword,
        data: {
          'email': email,
          'otp': otp,
        },
        headers: {
          'Scope': '${AppConstants.multi_me}',
          'x-api-key': 'your_api_key'
        },
      );

      return ApiResponse.completed(OtpResponse.fromJson(response));
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('Otp is invalid.');
    }
  }

  @override
  Future<ApiResponse<OtpResponse>> verifyOTPEmail(
      String otp, String email, OtpFlowType flowType) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.verifyOtp,
        method: ApiType.post,
        data: {
          'email': email,
          'otp': otp,
        },
        headers: {
          'Scope': '${AppConstants.multi_me}',
          'x-api-key': 'your_api_key'
        },
      );
      return ApiResponse.completed(OtpResponse.fromJson(response));
    } on NetworkException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      debugPrint('Error verifying OTPdfadsfdf: $e');
      return ApiResponse.error('Otp is invalid.');
    }
  }
}
