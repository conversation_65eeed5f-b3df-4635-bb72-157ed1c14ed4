import 'package:multime_app/shared/models/user/user_model.dart';

class LoginResponse {
  final LoginData data;

  LoginResponse({required this.data});

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      data: LoginData.fromJson(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.toJson(),
    };
  }
}

class LoginData {
  final TokenData token;
  final UserModel? userModel;
  final String? userRole;

  LoginData({required this.token, this.userModel, this.userRole});

  factory LoginData.fromJson(Map<String, dynamic> json) {
    return LoginData(
      token: TokenData.fromJson(json['token'] ?? {}),
      userModel: UserModel.fromJson(json['user']['user'] ?? {}),
      userRole: json['user']['role'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token.toJson(),
    };
  }
}

class TokenData {
  final String accessToken;
  final String? refreshToken;

  TokenData({
    required this.accessToken,
    this.refreshToken,
  });

  factory TokenData.fromJson(Map<String, dynamic> json) {
    return TokenData(
      accessToken: json['accessToken'] ?? '',
      refreshToken: json['refreshToken'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'accessToken': accessToken,
      if (refreshToken != null) 'refreshToken': refreshToken,
    };
  }
}
