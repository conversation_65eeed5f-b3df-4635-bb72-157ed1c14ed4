import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/auth_mode/data/models/login_response.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:math';
import 'dart:io';

import '../../../../core/network/subscriber_service.dart';

final supabase = Supabase.instance.client;

class AuthServices {
  final ApiClient apiClient;

  AuthServices({required this.apiClient});

  // Method để force clear Facebook session một cách triệt để
  Future<void> _forceClearFacebookSession() async {
    try {
      debugPrint('Force clearing Facebook session...');

      // Bước 1: Logout nhiều lần liên tiếp với delay khác nhau
      final delays = [200, 500, 800, 1000, 1500];
      for (int i = 0; i < 5; i++) {
        await FacebookAuth.instance.logOut();
        debugPrint('Facebook logout attempt ${i + 1}/5');
        await Future.delayed(Duration(milliseconds: delays[i]));
      }

      // Bước 2: Kiểm tra access token và clear nếu còn
      var accessToken = await FacebookAuth.instance.accessToken;
      int attempts = 0;
      while (accessToken != null && attempts < 3) {
        debugPrint(
            'Access token still exists, clearing attempt ${attempts + 1}');
        await FacebookAuth.instance.logOut();
        await Future.delayed(const Duration(milliseconds: 1000));
        accessToken = await FacebookAuth.instance.accessToken;
        attempts++;
      }

      if (accessToken == null) {
        debugPrint('Facebook session completely cleared');
      } else {
        debugPrint('WARNING: Facebook session may still persist');
      }
    } catch (e) {
      debugPrint('Error force clearing Facebook session: $e');
      // Fallback: thử clear một lần nữa
      try {
        await FacebookAuth.instance.logOut();
      } catch (fallbackError) {
        debugPrint('Fallback clear also failed: $fallbackError');
      }
    }
  }

  // Method để clear hoàn toàn Facebook session với cách mạnh mẽ hơn
  Future<void> _clearFacebookSession() async {
    try {
      debugPrint('Starting Facebook session clearing...');

      // Bước 1: Logout nhiều lần để đảm bảo clear hoàn toàn
      for (int i = 0; i < 3; i++) {
        await FacebookAuth.instance.logOut();
        await Future.delayed(const Duration(milliseconds: 500));
        debugPrint('Facebook logout attempt ${i + 1}');
      }

      // Bước 2: Kiểm tra và clear access token nếu còn
      final accessToken = await FacebookAuth.instance.accessToken;
      if (accessToken != null) {
        debugPrint(
            'Access token still exists after multiple logouts, clearing again...');
        await FacebookAuth.instance.logOut();
        await Future.delayed(const Duration(milliseconds: 500));

        // Kiểm tra lại lần nữa
        final stillExists = await FacebookAuth.instance.accessToken;
        if (stillExists != null) {
          debugPrint(
              'Warning: Facebook access token still exists after clearing');
        }
      }

      debugPrint('Facebook session cleared completely');
    } catch (e) {
      debugPrint('Error clearing Facebook session: $e');
    }
  }

  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
      'openid',
    ],
  );

  // Google Sign In
  Future<ApiResponse<LoginResponse>?> googleSignIn() async {
    try {
      final currentUser = await _googleSignIn.signInSilently();
      if (currentUser != null) {
        await _googleSignIn.disconnect();
      }

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        debugPrint('User cancelled Google Sign-In');
        return null;
      }

      // Lấy idToken và accessToken
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final response = await apiClient.request(
        path: ApiConst.loginGoogle,
        method: ApiType.post,
        data: {
          "id_token": googleAuth.accessToken,
        },
        headers: {'Scope': '${AppConstants.multi_me}'},
      );
      return ApiResponse.completed(LoginResponse.fromJson(response));
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw NetworkException(
            message:
                'Connection timeout. Please check your internet connection.');
      } else if (e.response != null &&
          e.response?.data is Map<String, dynamic>) {
        final errorData = e.response!.data as Map<String, dynamic>;
        final message = errorData['message'] ?? 'Unknown error';
        debugPrint('Dio error data: $errorData');
        throw NetworkException(message: message);
      }
    } catch (e) {
      debugPrint('Unexpected error: $e');
      throw NetworkException(message: e.toString());
    }
    return null;
  }

  Future<ApiResponse<LoginResponse>?> signInWithFacebook() async {
    try {
      debugPrint('Starting Facebook login process...');

      // Bước 1: Clear hoàn toàn Facebook session
      await _forceClearFacebookSession();

      // Bước 2: Delay để đảm bảo clear hoàn tất
      await Future.delayed(const Duration(milliseconds: 500));

      debugPrint('Attempting Facebook login with forced authentication...');

      // Bước 3: Thử nhiều lần login với clear cache giữa các lần
      LoginResult? loginResult;

      for (int attempt = 1; attempt <= 2; attempt++) {
        debugPrint('Facebook login attempt $attempt');

        // Clear session trước mỗi lần thử
        if (attempt > 1) {
          await FacebookAuth.instance.logOut();
          await Future.delayed(const Duration(milliseconds: 500));
        }

        loginResult = await FacebookAuth.instance.login(
          permissions: ['email', 'public_profile'],
          loginBehavior: LoginBehavior.dialogOnly, // Sử dụng dialog trong app
        );

        // Nếu login thành công, break
        if (loginResult.status == LoginStatus.success) {
          debugPrint('Facebook login successful on attempt $attempt');
          break;
        }

        if (loginResult.status == LoginStatus.cancelled) {
          debugPrint('User cancelled Facebook login on attempt $attempt');
          return null;
        }
      }

      // Kiểm tra kết quả cuối cùng
      if (loginResult?.status == LoginStatus.cancelled) {
        debugPrint('User cancelled Facebook Sign-In');
        return null;
      }

      if (loginResult?.status == LoginStatus.success) {
        final accessToken = loginResult?.accessToken?.tokenString;
        if (accessToken == null) {
          throw NetworkException(message: 'Facebook access token is null');
        }

        final response = await apiClient.request(
          path: ApiConst.loginFacebook,
          method: ApiType.post,
          data: {
            "id_token": accessToken,
          },
          headers: {'Scope': '${AppConstants.multi_me}'},
        );
        return ApiResponse.completed(LoginResponse.fromJson(response));
      } else {
        throw NetworkException(
            message:
                'Facebook login failed: ${loginResult?.message ?? "Unknown error"}');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw NetworkException(
            message:
                'Connection timeout. Please check your internet connection.');
      } else if (e.response != null &&
          e.response?.data is Map<String, dynamic>) {
        final errorData = e.response!.data as Map<String, dynamic>;
        final message = errorData['message'] ?? 'Unknown error';
        debugPrint('Dio error data: $errorData');
        throw NetworkException(message: message);
      }
    } catch (e) {
      debugPrint('Unexpected error: $e');
      throw NetworkException(message: e.toString());
    }
    return null;
  }

  // Apple Sign In
  Future<ApiResponse<LoginResponse>?> signInWithApple() async {
    try {
      if (!Platform.isIOS) {
        throw NetworkException(
            message: 'Apple Sign In is only available on iOS');
      }

      // Generate nonce
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );

      // Extract the ID token
      final authorizationCode = credential.authorizationCode;

      final response = await apiClient.request(
        path: ApiConst.loginApple,
        method: ApiType.post,
        data: {
          "id_token": authorizationCode,
        },
        headers: {'Scope': '${AppConstants.multi_me}'},
      );

      return ApiResponse.completed(LoginResponse.fromJson(response));
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw NetworkException(
            message:
                'Connection timeout. Please check your internet connection.');
      } else if (e.response != null &&
          e.response?.data is Map<String, dynamic>) {
        final errorData = e.response!.data as Map<String, dynamic>;
        final message = errorData['message'] ?? 'Unknown error';
        debugPrint('Dio error data: $errorData');
        throw NetworkException(message: message);
      }
    } catch (e) {
      debugPrint('Unexpected error: $e');
      throw NetworkException(message: e.toString());
    }
    return null;
  }

  // Helper methods for Apple Sign In
  String _generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)])
        .join();
  }

  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Logout methods
  static Future<void> googleSignOut() async {
    try {
      await _googleSignIn.signOut();
      debugPrint('Google sign out successful');
    } catch (e) {
      debugPrint('Error signing out from Google: $e');
    }
  }

  static Future<void> facebookSignOut() async {
    try {
      debugPrint('Starting Facebook complete sign out...');

      // Logout nhiều lần để đảm bảo clear hoàn toàn
      await FacebookAuth.instance.logOut();
      await Future.delayed(const Duration(milliseconds: 500));

      // Kiểm tra và clear access token nếu còn
      final accessToken = await FacebookAuth.instance.accessToken;
      if (accessToken != null) {
        debugPrint('Access token still exists, logging out again...');
        await FacebookAuth.instance.logOut();
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Logout thêm lần nữa để chắc chắn
      await FacebookAuth.instance.logOut();

      debugPrint('Facebook sign out completed successfully');
    } catch (e) {
      debugPrint('Error signing out from Facebook: $e');
    }
  }

  Future<ApiResponse<dynamic>> signOut(String token) async {
    try {
      final response = await apiClient.request(
        path: ApiConst.logout,
        method: ApiType.post,
        headers: {
          'Scope': '${AppConstants.multi_me}',
          'Authorization': 'Bearer $token'
        },
      );
      return ApiResponse.completed(response);
    } on DioException catch (e) {
      if (e.response != null && e.response?.data is Map<String, dynamic>) {
        final errorData = e.response!.data as Map<String, dynamic>;
        final message = errorData['message'] ?? 'Unknown error';
        return ApiResponse.error(message);
      } else {
        debugPrint('Dio error without detailed response: $e');
        return ApiResponse.error('Network connection error');
      }
    } catch (e) {
      debugPrint('Unexpected error: $e');
      return ApiResponse.error(e.toString());
    }
  }

  Future<ApiResponse<dynamic>> signOutAll(String token) async {
    try {
      await signOut(token);
      await googleSignOut();
      await facebookSignOut();
      await subscriberService.deleteSubscriber();
      return ApiResponse.completed(null);
    } catch (e) {
      debugPrint('Error signing out from all services: $e');
      return ApiResponse.error(
        e.toString(),
      );
    }
  }

  static Future<void> twitterSignIn() async {
    await Supabase.instance.client.auth.signInWithOAuth(
      OAuthProvider.twitter,
      redirectTo: 'com.multime.app://login-callback',
    );
  }
}
