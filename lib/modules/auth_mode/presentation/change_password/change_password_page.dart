// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:gap/gap.dart';
// import 'package:bcrypt/bcrypt.dart';
// import 'package:go_router/go_router.dart';
// import 'package:multime_app/app/routers/routers_name.dart';
// import 'package:multime_app/core/constants/app_assets.dart';
// import 'package:multime_app/core/di/locator.dart';
// import 'package:multime_app/core/domain/storages/global_storage.dart';
// import 'package:multime_app/core/l10n/locale_keys.g.dart';
// import 'package:multime_app/core/themes/app_text_style.dart';
// import 'package:multime_app/core/themes/theme.dart';
// import 'package:multime_app/core/utils/validation_utils.dart';
// import 'package:multime_app/modules/auth_mode/presentation/change_password/bloc/change_password_bloc.dart';
// import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';

// class ChangePasswordPage extends StatelessWidget {
//   const ChangePasswordPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final TextEditingController passwordController = TextEditingController();
//     final globalKey = GlobalKey<FormState>();
//     final globalStorage = getIt<GlobalStorage>();
//     final user = globalStorage.user;
//     final userHashedPassword = user?.passwordHashed;

//     // Debug logging
//     print('🔍 Debug - User: ${user != null ? "exists" : "null"}');
//     print(
//         '🔍 Debug - UserHashedPassword: ${userHashedPassword == null ? "null" : userHashedPassword.isEmpty ? "empty string" : "has value (${userHashedPassword.length} chars)"}');
//     print('🔍 Debug - UserHashedPassword value: "$userHashedPassword"');
//     return Scaffold(
//       appBar: PreferredSize(
//         preferredSize: const Size.fromHeight(70),
//         child: AppBar(
//           title: Text(
//             LocaleKeys.passwordChanged.tr(),
//             style: Theme.of(context).textTheme.lightHeadingMedium,
//           ),
//           centerTitle: true,
//           leading: IconButton(
//             icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
//             onPressed: () {
//               Navigator.pop(context);
//             },
//           ),
//         ),
//       ),
//       body: Padding(
//         padding: EdgeInsets.all(16),
//         child: Column(
//           children: [
//             Form(
//               key: globalKey,
//               child: Column(
//                 children: [
//                   BlocBuilder<ChangePasswordBloc, ChangePasswordState>(
//                     builder: (context, state) {
//                       return StrongBodyTextField(
//                         isPassword: true,
//                         controller: passwordController,
//                         labalText: LocaleKeys.password.tr(),
//                         isLabel: true,
//                         showPassword: state.isPasswordChanged,
//                         toggleShowPassword: () => context
//                             .read<ChangePasswordBloc>()
//                             .add(const IsCheckPasswordEvent()),
//                         validator: (value) => ValidateForm.validatePassword(
//                           value,
//                         ),
//                       );
//                     },
//                   ),
//                   Align(
//                     alignment: Alignment.centerRight,
//                     child: GestureDetector(
//                       onTap: () {
//                         context.push(RouteName.forgotPasswordPage);
//                       },
//                       child: Text(
//                         LocaleKeys.forgotPassword.tr(),
//                         style: Theme.of(context)
//                             .textTheme
//                             .lightBodySmallSemiBold
//                             .copyWith(
//                               color: Theme.of(context).colorScheme.primary,
//                             ),
//                       ),
//                     ),
//                   ),
//                   Gap(20.h),
//                   TextButton(
//                       onPressed: () {
//                         // return Text('Change Password');
//                         // if (globalKey.currentState!.validate()) {
//                         //   final inputPassword = passwordController.text;
//                         //   final storedHashedPassword = userHashedPassword;

//                         //   // Check if stored password is null or empty
//                         //   if (storedHashedPassword == null ||
//                         //       storedHashedPassword.isEmpty ||
//                         //       storedHashedPassword.length < 28) {
//                         //     String errorMessage;
//                         //     if (user == null) {
//                         //       errorMessage =
//                         //           'User not found. Please login again.';
//                         //     } else if (storedHashedPassword == null ||
//                         //         storedHashedPassword.isEmpty) {
//                         //       errorMessage =
//                         //           'No password set. Please set a password first.';
//                         //     } else {
//                         //       errorMessage =
//                         //           'Password data is corrupted. Please reset your password.';
//                         //     }

//                         //     ScaffoldMessenger.of(context).showSnackBar(
//                         //       SnackBar(
//                         //         content: Text(errorMessage,
//                         //             style: Theme.of(context)
//                         //                 .textTheme
//                         //                 .lightBodyMediumMedium
//                         //                 .copyWith(
//                         //                   color: Theme.of(context)
//                         //                       .whitePrimary(context),
//                         //                 )),
//                         //         backgroundColor: Colors.red,
//                         //       ),
//                         //     );
//                         //     return;
//                         //   }

//                         //   try {
//                         //     bool isPasswordCorrect = BCrypt.checkpw(
//                         //         inputPassword, storedHashedPassword);

//                         //     if (!isPasswordCorrect) {
//                         //       ScaffoldMessenger.of(context).showSnackBar(
//                         //         SnackBar(
//                         //           content: Text('Password not match',
//                         //               style: Theme.of(context)
//                         //                   .textTheme
//                         //                   .lightBodyMediumMedium
//                         //                   .copyWith(
//                         //                     color: Theme.of(context)
//                         //                         .whitePrimary(context),
//                         //                   )),
//                         //           backgroundColor: Colors.red,
//                         //         ),
//                         //       );
//                         //     } else {
//                         //       context.push(RouteName.createPasswordPage,
//                         //           extra: inputPassword);
//                         //     }
//                         //   } catch (e) {
//                         //     // Handle BCrypt errors (invalid hash format, etc.)
//                         //     print('🔍 BCrypt Error: $e');
//                         //     print('🔍 Hash value: "$storedHashedPassword"');
//                         //     ScaffoldMessenger.of(context).showSnackBar(
//                         //       SnackBar(
//                         //         content: Text(
//                         //             'Password verification failed: ${e.toString()}',
//                         //             style: Theme.of(context)
//                         //                 .textTheme
//                         //                 .lightBodyMediumMedium
//                         //                 .copyWith(
//                         //                   color: Theme.of(context)
//                         //                       .whitePrimary(context),
//                         //                 )),
//                         //         backgroundColor: Colors.red,
//                         //       ),
//                         //     );
//                         //   }
//                         // }
//                       },
//                       style: TextButton.styleFrom(
//                         backgroundColor: Theme.of(context).colorScheme.primary,
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                         minimumSize: Size(double.infinity, 50),
//                       ),
//                       child: Text(
//                         'Confirm',
//                         style: Theme.of(context)
//                             .textTheme
//                             .lightBodyLargeMedium
//                             .copyWith(
//                               color: Theme.of(context).whitePrimary(context),
//                             ),
//                       ))
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
