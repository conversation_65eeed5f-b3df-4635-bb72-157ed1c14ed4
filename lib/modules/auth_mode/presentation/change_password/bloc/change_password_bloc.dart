import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'change_password_event.dart';
part 'change_password_state.dart';

class ChangePasswordBloc
    extends Bloc<ChangePasswordEvent, ChangePasswordState> {
  ChangePasswordBloc() : super(const ChangePasswordState()) {
    on<IsCheckPasswordEvent>(_onChangePasswordSubmit);
  }

  Future<void> _onChangePasswordSubmit(
      IsCheckPasswordEvent event, Emitter<ChangePasswordState> emit) async {
    bool isPasswordChanged = !state.isPasswordChanged;
    emit(state.copyWith(
        isSuccess: true,
        isLoading: false,
        isPasswordChanged: isPasswordChanged));
  }
}
