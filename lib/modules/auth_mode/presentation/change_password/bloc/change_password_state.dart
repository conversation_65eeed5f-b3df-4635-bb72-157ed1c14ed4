part of 'change_password_bloc.dart';

class ChangePasswordState extends Equatable {
  final bool isLoading;
  final String? errorMessage;
  final bool isSuccess;
  final bool isPasswordChanged;
  @override
  List<Object> get props => [
        isLoading,
        errorMessage ?? '',
        isSuccess,
        isPasswordChanged,
      ];
  const ChangePasswordState({
    this.isLoading = false,
    this.errorMessage,
    this.isSuccess = false,
    this.isPasswordChanged = false,
  });

  ChangePasswordState copyWith({
    bool? isLoading,
    String? errorMessage,
    bool? isSuccess,
    bool? isPasswordChanged,
  }) {
    return ChangePasswordState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
      isPasswordChanged: isPasswordChanged ?? this.isPasswordChanged,
    );
  }
}
