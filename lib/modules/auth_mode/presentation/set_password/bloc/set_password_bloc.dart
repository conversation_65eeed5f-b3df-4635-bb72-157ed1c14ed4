import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'set_password_event.dart';
import 'set_password_state.dart';

class SetPasswordBloc extends Bloc<SetPasswordEvent, SetPasswordState> {
  final AuthRepository authRepository;
  final GlobalStorage globalStorage;
  SetPasswordBloc({required this.authRepository, required this.globalStorage})
      : super(SetPasswordState.initial()) {
    on<SubmitSetPasswordEvent>(_onSubmitSetPassword);
    on<ForgotPasswordEvent>(_onForgotPassword);
    on<TogglePasswordVisibilityEvent>(_onTogglePasswordVisibilityEvent);
    on<TogglePasswordConfirmVisibilityEvent>(
        _onTogglePasswordConfirmVisibilityEvent);
  }

  Future<void> _onSubmitSetPassword(
    SubmitSetPasswordEvent event,
    Emitter<SetPasswordState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, error: null, isSuccess: false));

    try {
      final token = globalStorage.accessToken;

      if (token == null) {
        emit(state.copyWith(
          isLoading: false,
          error: 'Access token not found. Please login again.',
          isSuccess: false,
        ));
        return;
      }

      final response = await authRepository.changePassword({
        'currentPassword': event.currentPassword,
        'newPassword': event.newPassword,
      }, token);

      if (response.status == Status.error) {
        emit(state.copyWith(
          isLoading: false,
          error: response.message,
          isSuccess: false,
        ));
        return;
      }

      emit(state.copyWith(isLoading: false, isSuccess: true));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
        isSuccess: false,
      ));
    }
  }

  void _onForgotPassword(
    ForgotPasswordEvent event,
    Emitter<SetPasswordState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, error: null, isSuccess: false));

    try {
      final response = await authRepository.resetPassword(
        event.newPassword,
        event.secretToken,
      );
      if (response.status == Status.completed) {
        emit(state.copyWith(isLoading: false, isSuccess: true));
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to reset password',
          isSuccess: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
        isSuccess: false,
      ));
    }
  }

  void _onTogglePasswordVisibilityEvent(
    TogglePasswordVisibilityEvent event,
    Emitter<SetPasswordState> emit,
  ) {
    emit(state.copyWith(
      isPasswordVisible: !state.isPasswordVisible,
    ));
  }

  void _onTogglePasswordConfirmVisibilityEvent(
    TogglePasswordConfirmVisibilityEvent event,
    Emitter<SetPasswordState> emit,
  ) {
    emit(state.copyWith(
      isPasswordConfirmVisible: !state.isPasswordConfirmVisible,
    ));
  }
}
