class SetPasswordState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final bool isPasswordVisible;
  final bool isPasswordConfirmVisible;

  SetPasswordState({
    required this.isLoading,
    required this.isSuccess,
    this.error,
    required this.isPasswordVisible,
    required this.isPasswordConfirmVisible,
  });

  factory SetPasswordState.initial() {
    return SetPasswordState(
      isLoading: false,
      isSuccess: false,
      error: null,
      isPasswordVisible: false,
      isPasswordConfirmVisible: false,
    );
  }

  SetPasswordState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    bool? isPasswordVisible,
    bool? isPasswordConfirmVisible,
  }) {
    return SetPasswordState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error ?? this.error,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isPasswordConfirmVisible:
          isPasswordConfirmVisible ?? this.isPasswordConfirmVisible,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isSuccess,
        error,
        isPasswordVisible,
        isPasswordConfirmVisible,
      ];
}
