abstract class SetPasswordEvent {}

class SubmitSetPasswordEvent extends SetPasswordEvent {
  final String? currentPassword;
  final String newPassword;

  SubmitSetPasswordEvent(this.currentPassword, this.newPassword);
}

class ForgotPasswordEvent extends SetPasswordEvent {
  final String newPassword;
  final String secretToken;
  ForgotPasswordEvent({required this.newPassword, required this.secretToken});
}

class TogglePasswordVisibilityEvent extends SetPasswordEvent {
  TogglePasswordVisibilityEvent();
}

class TogglePasswordConfirmVisibilityEvent extends SetPasswordEvent {
  TogglePasswordConfirmVisibilityEvent();
}
