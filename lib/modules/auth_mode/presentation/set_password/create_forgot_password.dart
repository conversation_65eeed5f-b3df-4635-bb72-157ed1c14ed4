import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/set_password/bloc/set_password_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';

import '../../../../core/utils/validation_utils.dart';
import 'bloc/set_password_bloc.dart';
import 'bloc/set_password_state.dart';

class CreateForgotPasswordPage extends StatefulWidget {
  const CreateForgotPasswordPage({required this.secretToken});
  final String secretToken;
  @override
  State<CreateForgotPasswordPage> createState() =>
      _CreateForgotPasswordPageState();
}

class _CreateForgotPasswordPageState extends State<CreateForgotPasswordPage> {
  final createPasswordFormKey = GlobalKey<FormState>();
  final FocusNode passwordConfirmFocusNode = FocusNode();
  final FocusNode passwordFocusNode = FocusNode();
  TextEditingController passwordConfirmController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  void _validateAndSubmit() {
    if (createPasswordFormKey.currentState!.validate()) {
      context.read<SetPasswordBloc>().add(
            ForgotPasswordEvent(
                newPassword: passwordController.text,
                secretToken: widget.secretToken),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SetPasswordBloc, SetPasswordState>(
      listener: (context, state) {
        // Show/Hide AppLoader based on loading state
        if (state.isLoading) {
          AppLoader.show(context);
        } else {
          AppLoader.hide();
        }

        if (state.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Password updated successfully',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumMedium
                      .copyWith(
                        color: Theme.of(context).whitePrimary(context),
                      )),
              backgroundColor: Colors.green,
            ),
          );
          context.go(RouteName.loginPage);
        }
        if (state.error != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.error!)),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLogo(context),
                  Gap(22.h),
                  _buildHeaderCreatePassword(context),
                  Gap(22.h),
                  _buildPassword(context, state.isPasswordVisible,
                      state.isPasswordConfirmVisible),
                  Gap(38.h),
                  _buildButtonPasword(context, state.isLoading),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLogo(BuildContext context) {
    return Center(
      child: SvgPicture.asset(
        AppAssets.logoSvg,
        width: 138.w,
        height: 42.h,
      ),
    );
  }

  Widget _buildHeaderCreatePassword(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(LocaleKeys.createPassword.tr(),
            style: Theme.of(context)
                .textTheme
                .lightHeadingMedium
                .copyWith(color: Theme.of(context).textPrimary(context))),
        Gap(10.h),
        Text(LocaleKeys.createPasswordText.tr(),
            style: Theme.of(context)
                .textTheme
                .lightBodyLargeRegular
                .copyWith(color: Theme.of(context).textSecondary(context)))
      ],
    );
  }

  Widget _buildPassword(
      BuildContext context, bool isShowPassword, bool _isShowPasswordConfirm) {
    return Form(
      key: createPasswordFormKey,
      child: Column(
        children: [
          StrongBodyTextField(
            isPassword: true,
            controller: passwordController,
            labalText: LocaleKeys.password.tr(),
            isLabel: true,
            focusNode: passwordFocusNode,
            toggleShowPassword: () {
              context.read<SetPasswordBloc>().add(
                    TogglePasswordVisibilityEvent(),
                  );
            },
            showPassword: isShowPassword,
            validator: (value) => ValidateForm.validatePassword(
              value,
            ),
          ),
          Gap(14.h),
          StrongBodyTextField(
            isPassword: true,
            controller: passwordConfirmController,
            labalText: LocaleKeys.confirmPasswordHint.tr(),
            isLabel: true,
            focusNode: passwordConfirmFocusNode,
            toggleShowPassword: () {
              context.read<SetPasswordBloc>().add(
                    TogglePasswordConfirmVisibilityEvent(),
                  );
            },
            showPassword: _isShowPasswordConfirm,
            validator: (value) => ValidateForm.validateConfirmPassword(
              value,
              passwordController.text.trim(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonPasword(BuildContext context, bool isLoading) {
    return StrongBodyButton(
      fontSize: 16,
      backgroundColor: Theme.of(context).primary(context),
      label: LocaleKeys.resetPassword.tr(),
      onPressed: isLoading ? null : () => _validateAndSubmit(),
    );
  }
}
