import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/core/utils/validation_utils.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_state.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/widgets/register_button.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/widgets/register_password_field.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field_note.dart';
import 'package:multime_app/modules/country/model/country/country.dart';

import '../widgets/strong_body_text_field.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final GlobalKey<FormState> formRegisterKey = GlobalKey<FormState>();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _pwController = TextEditingController();
  final TextEditingController _occupationController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();
  int? selectedCountryId;

  String? validateFullName(String? fullName) {
    return ValidateForm.validateFullName(_firstNameController.text.trim());
  }

  String? validateCountry(String? country) {
    return ValidateForm.validateCountry(_countryController.text.trim());
  }

  String? validateOccupation(String? fullName) {
    return ValidateForm.validateOccupation(_occupationController.text.trim());
  }

  final globalStorage = getIt<GlobalStorage>();
  late List<Country> countries = [];

  @override
  void initState() {
    super.initState();
    countries = globalStorage.countries ?? [];
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _pwController.dispose();
    _occupationController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RegisterBloc, RegisterState>(
      listenWhen: (previous, current) {
        bool hasNewError = current.error != null &&
            !current.isRegistrationComplete &&
            !current.isLoading &&
            previous.isLoading;

        bool isComplete = current.isRegistrationComplete &&
            !current.isLoading &&
            previous.isLoading;

        bool shouldListen = hasNewError || isComplete;

        return shouldListen;
      },
      listener: (context, state) {
        // Handle error case
        if (state.error != null && !state.isRegistrationComplete) {
          ScaffoldMessenger.of(context).clearSnackBars();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.error!),
              backgroundColor: Colors.grey[500],
              duration: Duration(seconds: 3),
            ),
          );
        }

        if (state.isRegistrationComplete) {
          context.go(RouteName.otpPage, extra: {
            'userData': state.userData,
            'email': state.userData?['email'] ?? _emailController.text.trim(),
            'flowType': OtpFlowType.signup,
            'resendOtp': false,
          });
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: GestureDetector(
            onTap: () => context.pop(),
            child: Icon(
              Icons.arrow_back_ios,
              size: 24.sp,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Logo
              Center(
                child: SvgPicture.asset(
                  AppAssets.logoSvg,
                  fit: BoxFit.contain,
                ),
              ),
              Gap(22.h),

              // Header Text
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sign Up',
                    style: Theme.of(context)
                        .textTheme
                        .lightHeadingMedium
                        .copyWith(
                            color: Theme.of(context).secondaryBase(context)),
                  ),
                  Gap(12.h),
                  Text(
                    'Create an account to continue!',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeRegular
                        .copyWith(
                          color: Theme.of(context).greyScale600(context),
                        ),
                  )
                ],
              ),
              Gap(32.h),

              Form(
                key: formRegisterKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    StrongBodyTextField(
                      controller: _firstNameController,
                      validator: validateFullName,
                      labalText: "Fist name",
                    ),
                    Gap(14.h),

                    // Username Field
                    StrongBodyTextFieldNote(
                      labelText: 'Last name',
                      noteText:
                          'You can\'t change your username, so choose wisely',
                      controller: _lastNameController,
                      validator: validateFullName,
                    ),
                    Gap(20.h),
                    FormField<int>(
                      initialValue: selectedCountryId,
                      validator: (value) =>
                          ValidateForm.validateCountrySelection(value),
                      builder: (FormFieldState<int> field) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Theme(
                              data: Theme.of(context).copyWith(
                                dropdownMenuTheme: DropdownMenuThemeData(
                                  textStyle: TextStyle(color: Colors.black),
                                  menuStyle: MenuStyle(
                                    backgroundColor:
                                        WidgetStateProperty.all(Colors.white),
                                  ),
                                  inputDecorationTheme: InputDecorationTheme(
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide(
                                        color: field.hasError
                                            ? Theme.of(context)
                                                .colorScheme
                                                .error
                                            : Theme.of(context)
                                                .greyScale300(context),
                                        width: 1,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide(
                                        color: field.hasError
                                            ? Theme.of(context)
                                                .colorScheme
                                                .error
                                            : Theme.of(context)
                                                .primary(context),
                                        width: 2,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide(
                                        color:
                                            Theme.of(context).colorScheme.error,
                                        width: 1.5,
                                      ),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide(
                                        color:
                                            Theme.of(context).colorScheme.error,
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              child: DropdownMenu<int>(
                                menuHeight: 200,
                                width: double.infinity,
                                controller: _countryController,
                                dropdownMenuEntries: countries.map((country) {
                                  return DropdownMenuEntry<int>(
                                    value: country.id!,
                                    label: country.title!,
                                  );
                                }).toList(),
                                onSelected: (int? value) {
                                  setState(() {
                                    selectedCountryId = value;
                                  });
                                  field.didChange(value);
                                },
                                initialSelection: selectedCountryId,
                                label: Text(
                                  LocaleKeys.country.tr(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeMedium
                                      .copyWith(
                                          color: field.hasError
                                              ? Theme.of(context)
                                                  .colorScheme
                                                  .error
                                              : Theme.of(context)
                                                  .greyScale400(context)),
                                ),
                              ),
                            ),
                            if (field.hasError)
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: 8.0, left: 12.0),
                                child: Text(
                                  field.errorText!,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .error),
                                ),
                              ),
                          ],
                        );
                      },
                    ),

                    Gap(20.h),
                    // Occupation Field
                    // StrongBodyTextFieldNote(
                    //   labelText: 'Occupation',
                    //   controller: _occupationController,
                    //   validator: validateOccupation,
                    //   noteText: "You'll see why soon. You can change it later",
                    // ),
                    // Gap(20.h),

                    // Email Field
                    StrongBodyTextFieldNote(
                      controller: _emailController,
                      isNote: false,
                      noteText: '',
                      labelText: LocaleKeys.emailLabel.tr(),
                      validator: (value) => ValidateForm.validateEmail(value),
                    ),
                    Gap(20.h),

                    // Password Field
                    RegisterPasswordWidget(
                      pwController: _pwController,
                      emailController: _emailController,
                    ),
                    Gap(20.h),

                    // Terms & Privacy Text
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: TextStyle(fontSize: 13.sp, color: Colors.black),
                        children: [
                          TextSpan(text: LocaleKeys.byJoinStrongBody.tr()),
                          WidgetSpan(
                            child: InkWell(
                              onTap: () {
                                print("Terms of Service tapped");
                              },
                              child: Text(
                                LocaleKeys.termsOfService.tr(),
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyMediumSemiBold
                                    .copyWith(
                                      color: Theme.of(context).primary(context),
                                    ),
                              ),
                            ),
                          ),
                          TextSpan(text: LocaleKeys.andOur.tr()),
                          WidgetSpan(
                            child: InkWell(
                              onTap: () {
                                print("Privacy policy tapped");
                                context.pushNamed(RouteName.privacyPolicyPage);
                              },
                              child: Text(
                                LocaleKeys.privacyPolicy.tr(),
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyMediumSemiBold
                                    .copyWith(
                                      color: Theme.of(context).primary(context),
                                    ),
                              ),
                            ),
                          ),
                          TextSpan(text: LocaleKeys.toData.tr()),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              Gap(40.h),

              RegisterButton(
                formRegisterKey: formRegisterKey,
                firstNameController: _firstNameController,
                lastNameController: _lastNameController,
                emailController: _emailController,
                pwController: _pwController,
                occupationController: _occupationController,
                countryId: selectedCountryId ?? 0,
              ),
              Gap(30.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Already a member?",
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeRegular
                        .copyWith(
                          color: Theme.of(context).greyScale600(context),
                        ),
                  ),
                  Gap(8.w),
                  GestureDetector(
                    onTap: () {
                      context.pop();
                    },
                    child: Text(
                      "Login",
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeBold
                          .copyWith(
                            color: Theme.of(context).primary(context),
                          ),
                    ),
                  ),
                ],
              ),
              Gap(40.h),
            ],
          ),
        ),
      ),
    );
  }
}
