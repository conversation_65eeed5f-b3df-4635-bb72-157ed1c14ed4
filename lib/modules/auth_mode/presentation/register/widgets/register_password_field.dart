import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_state.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field_note.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../../../../core/utils/validation_utils.dart';

class RegisterPasswordWidget extends StatelessWidget {
  const RegisterPasswordWidget({
    super.key,
    required this.pwController,
    required this.emailController,
  });

  final TextEditingController pwController;
  final TextEditingController emailController;
  @override
  Widget build(BuildContext context) {
    final registerBloc = context.read<RegisterBloc>();
    return BlocBuilder<RegisterBloc, RegisterState>(
      buildWhen: (previous, current) {
        return previous.isPasswordVisible != current.isPasswordVisible;
      },
      builder: (context, state) {
        return StrongBodyTextFieldNote(
          isPassword: true,
          controller: pwController,
          labelText: LocaleKeys.password.tr(),
          noteText: "Min 8 characters, 1 uppercase, 1 lowercase, 1 number",
          toggleShowPassword: () =>
              registerBloc.add(TogglePasswordVisibilityEvent()),
          showPassword: state.isPasswordVisible,
          validator: ValidateForm.validatePassword,
        );
      },
    );
  }
}
