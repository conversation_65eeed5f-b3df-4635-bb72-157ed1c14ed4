import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/register/bloc/register_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

class RegisterButton extends StatelessWidget {
  const RegisterButton({
    super.key,
    required this.formRegisterKey,
    required this.firstNameController,
    required this.lastNameController,
    required this.emailController,
    required this.pwController,
    required this.occupationController,
    required this.countryId,
  });

  final GlobalKey<FormState> formRegisterKey;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final TextEditingController emailController;
  final TextEditingController pwController;
  final TextEditingController occupationController;
  final int countryId;

  @override
  Widget build(BuildContext context) {
    return StrongBodyButton(
      label: 'Sign Up',
      onPressed: () {
        if (formRegisterKey.currentState!.validate()) {
          context.read<RegisterBloc>().add(
                RegisterSubmitEvent(
                  firstName: firstNameController.text.trim(),
                  lastName: lastNameController.text.trim(),
                  email: emailController.text.trim(),
                  password: pwController.text.trim(),
                  occupation: occupationController.text.trim(),
                  country: countryId,
                ),
              );
        }
      },
      backgroundColor: Theme.of(context).backgroundRed(context),
    );

    // BlocBuilder<RegisterBloc, RegisterState>(
    //   builder: (context, state) {
    //     return StrongBodyButton(
    //       label: 'Sign Up',
    //       onPressed: state.isLoading
    //           ? null
    //           : () {
    //               if (formRegisterKey.currentState!.validate()) {
    //                 context.read<RegisterBloc>().add(
    //                       RegisterSubmitEvent(
    //                         username: usernameController.text.trim(),
    //                         email: emailController.text.trim(),
    //                         password: pwController.text.trim(),
    //                         occupation: occupationController.text.trim(),
    //                         country: countryId,
    //                       ),
    //                     );
    //               }
    //             },
    //       backgroundColor: state.isLoading
    //           ? Colors.grey.shade300
    //           : Theme.of(context).backgroundRed(context),
    //     );
    //   },
    // );
  }
}
