class RegisterState {
  final bool isLoading;
  final String? error;
  final bool isPasswordVisible;
  final bool isRegistrationComplete;
  final Map<String, dynamic>? userData;
  final bool formIsValid;

  RegisterState(
      {this.isLoading = false,
      this.error,
      this.isPasswordVisible = false,
      this.isRegistrationComplete = false,
      this.formIsValid = false,
      this.userData});

  factory RegisterState.initial() => RegisterState();

  RegisterState copyWith({
    bool? isLoading,
    String? error,
    bool? isPasswordVisible,
    bool? isRegistrationComplete,
    Map<String, dynamic>? userData,
    bool? formIsValid,
  }) {
    return RegisterState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isRegistrationComplete:
          isRegistrationComplete ?? this.isRegistrationComplete,
      userData: userData ?? this.userData,
      formIsValid: formIsValid ?? this.formIsValid,
    );
  }
}
