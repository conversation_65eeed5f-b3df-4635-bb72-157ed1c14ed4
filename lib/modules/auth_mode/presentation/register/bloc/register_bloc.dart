import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'register_event.dart';
import 'register_state.dart';

class RegisterBloc extends Bloc<RegisterEvent, RegisterState> {
  late final AuthRepository authRepository;
  RegisterBloc({
    required this.authRepository,
  }) : super(RegisterState.initial()) {
    // _authRepository = authRepository;
    on<RegisterSubmitEvent>(_onRegisterSubmit);
    on<TogglePasswordVisibilityEvent>(_onTogglePassword);
    on<RegisterFormChangedEvent>(_onRegisterFormChangedEvent);
  }

  void _onRegisterSubmit(
    RegisterSubmitEvent event,
    Emitter<RegisterState> emit,
  ) async {
    debugPrint('🚀 Starting registration process...');
    emit(state.copyWith(isLoading: true, error: null)); // Clear previous error

    try {
      final userData = {
        "first_name": event.firstName,
        "last_name": event.lastName,

        "email": event.email,
        "password": event.password,
        "profession": event.occupation,
        "country_id": event.country,
      };

      final response = await authRepository.signUp(userData);
      debugPrint('📥 SignUp Response: Status=${response.status}');

      if (response.status == Status.completed) {
        emit(state.copyWith(
          isLoading: false,
          error: null,
          isRegistrationComplete: true,
          userData: userData,
          formIsValid: true,
        ));
      } else if (response.status == Status.error) {
        emit(state.copyWith(
          isLoading: false,
          error: response.message,
          isRegistrationComplete: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
        isRegistrationComplete: false,
      ));
    }
  }

  void _onTogglePassword(
    TogglePasswordVisibilityEvent event,
    Emitter<RegisterState> emit,
  ) {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  void _onRegisterFormChangedEvent(
      RegisterFormChangedEvent event, Emitter<RegisterState> emit) {
    final isValid = event.firstName.isNotEmpty &&
        event.lastName.isNotEmpty &&
        event.email.isNotEmpty &&
        event.password.isNotEmpty &&
        event.occupation.isNotEmpty &&
        event.countryId > 0;

    emit(state.copyWith(formIsValid: isValid));
  }
}
