abstract class RegisterEvent {}

class RegisterSubmitEvent extends RegisterEvent {
  final String firstName;
  final String lastName;
  final String email;
  final String password;
  final String occupation;
  final int country;

  RegisterSubmitEvent({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
    required this.occupation,
    required this.country,
  });
}

class TogglePasswordVisibilityEvent extends RegisterEvent {}

class RegisterFormChangedEvent extends RegisterEvent {
  final String firstName;
  final String lastName;
  final String email;
  final String password;
  final String occupation;
  final int countryId;

  RegisterFormChangedEvent({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
    required this.occupation,
    required this.countryId,
  });
}
