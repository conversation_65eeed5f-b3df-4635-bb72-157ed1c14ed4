import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/network/subscriber_service.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/widgets/email_password_fields.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/widgets/login_button_widget.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/widgets/login_method_widgets.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/widgets/text_divider.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/login_promt.dart';

import '../../../../shared/widgets/app_loader/app_loader.dart';
import '../login/bloc/login_bloc.dart';
import '../login/bloc/login_state.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    final loginFormKey = GlobalKey<FormState>();
    final TextEditingController emailController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();
    return BlocListener<LoginBloc, LoginState>(
        listenWhen: (previous, current) {
          // Chỉ push OTP khi isEmailNotVerified chuyển từ false sang true
          if (previous.isEmailNotVerified != current.isEmailNotVerified &&
              current.isEmailNotVerified == true) {
            return true;
          }
          // Các điều kiện khác giữ nguyên
          return previous.isLoading != current.isLoading ||
              previous.isSuccess != current.isSuccess ||
              (previous.isFailure != current.isFailure && current.isFailure) ||
              (previous.message != current.message && current.message != null);
        },
        listener: (context, state) {
          if (state.isLoading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
          // Chỉ push OTP khi vừa chuyển sang true và chưa push lần nào
          if (state.isEmailNotVerified) {
            context.push(RouteName.otpPage, extra: {
              "email": emailController.text,
              "flowType": OtpFlowType.signup,
              "resendOtp": true,
            });
          }
          if (state.isSuccess) {
            debugPrint('Login success, navigating...');
            sendSubscriptionId();
            context.go(RouteName.homeStrongBody);
          } else if (state.isFailure && state.message != null) {
            ScaffoldMessenger.of(context).clearSnackBars();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message!),
                backgroundColor: Colors.grey[500],
                duration: Duration(seconds: 1),
              ),
            );
          }
        },
        child: Scaffold(
          body: Padding(
              padding: EdgeInsets.all(20.w),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Gap(30.h),
                    _buildLogo(),
                    Gap(24.h),
                    _buildHeaderLogin(context),
                    Gap(24.h),
                    LoginFormFields(
                      loginFormKey: loginFormKey,
                      emailController: emailController,
                      passwordController: passwordController,
                    ),
                    Gap(16.h),
                    _buildForgetPassword(context),
                    Gap(40.h),
                    LoginButton(
                        loginFormKey: loginFormKey,
                        emailController: emailController,
                        passwordController: passwordController),
                    Gap(24.h),
                    TextDivider(
                      label: LocaleKeys.orLoginWith.tr(),
                    ),
                    Gap(14.h),
                    LoginMethodButton(
                      method: LoginMethod.google,
                      onPressed: () =>
                          context.read<LoginBloc>().add(LoginWithGoogle()),
                    ),
                    Gap(13.h),
                    LoginMethodButton(
                      method: LoginMethod.facebook,
                      onPressed: () =>
                          context.read<LoginBloc>().add(LoginWithFacebook()),
                    ),
                    Gap(13.h),
                    Opacity(
                      opacity: Platform.isIOS ? 1.0 : 0.6,
                      child: LoginMethodButton(
                        method: LoginMethod.apple,
                        onPressed: () {
                          if (Platform.isIOS) {
                            context.read<LoginBloc>().add(LoginWithApple());
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Apple Sign-In is only available on iOS devices'),
                                backgroundColor: Colors.grey,
                                duration: Duration(seconds: 3),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                    Gap(40.h),
                    LoginAccountPrompt(
                      gotoPage: () {
                        context.push(RouteName.registerPage);
                      },
                      label: LocaleKeys.dontHaveAnAccount.tr(),
                      subLabel: ' ${LocaleKeys.signUp.tr()}',
                    ),
                  ],
                ),
              )),
        ));
  }

  Widget _buildForgetPassword(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () {
            (context).push(RouteName.forgotPasswordPage);
          },
          child: Text(
            LocaleKeys.forgotPassword.tr(),
            style: Theme.of(context).textTheme.darkBodySmallSemiBold.copyWith(
                  color: Theme.of(context).primary(context),
                ),
          ),
        )
      ],
    );
  }

  Widget _buildLogo() {
    return Center(
      child: SvgPicture.asset(
        AppAssets.logoSvg,
        width: 138.w,
        height: 42.h,
      ),
    );
  }

  Widget _buildHeaderLogin(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.headerLogin.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                color: AppColors.textBlack,
              ),
        ),
        Gap(12.h),
        RichText(
          text: TextSpan(
              text: '${LocaleKeys.headerLoginText1.tr()} ',
              style:
                  Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                        color: Theme.of(context).greyScale700(context),
                      ),
              children: [
                TextSpan(
                    text: '${LocaleKeys.headerLoginText2.tr()} ',
                    style:
                        Theme.of(context).textTheme.darkBodyMediumBold.copyWith(
                              color: AppColors.textBlack,
                              fontWeight: FontWeight.w600,
                            )),
                TextSpan(
                  text: LocaleKeys.headerLoginText3.tr(),
                )
              ]),
        ),
      ],
    );
  }
}
