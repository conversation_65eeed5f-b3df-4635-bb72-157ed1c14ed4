// ... existing imports ...

// Add these new widget classes before the LoginPage class
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_state.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';

import '../../../../../core/utils/validation_utils.dart';

class LoginFormFields extends StatelessWidget {
  final GlobalKey<FormState> loginFormKey;

  const LoginFormFields({
    super.key,
    required this.loginForm<PERSON>ey,
    this.emailController,
    this.passwordController,
  });
  final TextEditingController? emailController;
  final TextEditingController? passwordController;
  @override
  Widget build(BuildContext context) {
    // final bloc = context.read<LoginBloc>();

    return Form(
      key: loginFormKey,
      child: Column(
        children: [
          StrongBodyTextField(
            controller: emailController,
            labalText: LocaleKeys.emailLabel.tr(),
            isLabel: true,
            validator: (value) => ValidateForm.validateEmail(value),
          ),
          Gap(24.h),
          BlocBuilder<LoginBloc, LoginState>(
            buildWhen: (previous, current) {
              return previous.isPasswordVisible != current.isPasswordVisible;
            },
            builder: (context, state) {
              print('building password field');
              return StrongBodyTextField(
                isPassword: true,
                controller: passwordController,
                labalText: LocaleKeys.password.tr(),
                isLabel: true,
                toggleShowPassword: () =>
                    context.read<LoginBloc>().add(TogglePasswordVisibility()),
                showPassword: state.isPasswordVisible,
                validator: (value) => ValidateForm.validatePassword(
                  value,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
