import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_state.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

class LoginButton extends StatelessWidget {
  final GlobalKey<FormState> loginFormKey;

  const LoginButton({
    super.key,
    required this.loginFormKey,
    this.emailController,
    this.passwordController,
  });
  final TextEditingController? emailController;
  final TextEditingController? passwordController;
  void _validateAndSubmit(BuildContext context) {
    if (loginFormKey.currentState!.validate()) {
      context.read<LoginBloc>().add(
            SubmitLoginForm(
                email: emailController!.text.trim(),
                password: passwordController!.text.trim()),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LoginBloc, LoginState>(
      buildWhen: (previous, current) => previous.isLoading != current.isLoading,
      builder: (context, state) {
        print('building login button');
        return StrongBodyButton(
            backgroundColor: Theme.of(context).backgroundRed(context),
            label: LocaleKeys.login.tr(),
            onPressed: () => _validateAndSubmit(context));
      },
    );
  }
}
