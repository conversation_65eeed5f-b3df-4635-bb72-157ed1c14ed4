import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/themes/app_colors.dart';

class TextDivider extends StatelessWidget {
  final String label;
  const TextDivider({
    super.key,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Expanded(
              child: Divider(
            color: AppColors.lightGrey,
            thickness: 1,
          )),
          Gap(16.w),
          Text(
            label,
            style: Theme.of(context).textTheme.lightBodySmallMedium.copyWith(
                  color: Theme.of(context).greyScale700(context),
                ),
          ).tr(),
          Gap(16.w),
          const Expanded(
              child: Divider(
                color: AppColors.lightGrey,
                thickness: 1,
              )),
        ],
      ),
    );
  }
}
