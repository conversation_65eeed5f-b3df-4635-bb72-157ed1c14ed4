// ignore_for_file: non_constant_identifier_names

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

enum LoginMethod { google, facebook, apple, twitter }

class LoginMethodWidget extends StatelessWidget {
  final VoidCallback loginGoogle;
  final VoidCallback loginFacebook;
  final VoidCallback loginApple;
  final VoidCallback loginTwitter;
  const LoginMethodWidget(
      {super.key,
      required this.loginGoogle,
      required this.loginFacebook,
      required this.loginApple,
      required this.loginTwitter});

  @override
  Widget build(BuildContext context) {
    return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: LoginMethod.values
            .where((element) {
              if (Platform.isAndroid) {
                return element == LoginMethod.google;
              } else {
                return true;
              }
            })
            .toList()
            .map((method) {
              return LoginMethodButton(
                  method: method,
                  onPressed: () {
                    switch (method) {
                      case LoginMethod.google:
                        loginGoogle.call();
                        break;
                      case LoginMethod.facebook:
                        loginFacebook.call();
                        break;
                      case LoginMethod.apple:
                        loginApple.call();
                        break;
                      case LoginMethod.twitter:
                        loginTwitter.call();
                        break;
                    }
                  });
            })
            .toList());
  }
}

class LoginMethodButton extends StatelessWidget {
  final LoginMethod method;
  final VoidCallback onPressed;

  const LoginMethodButton(
      {super.key, required this.method, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    Widget icon;
    String title;
    switch (method) {
      case LoginMethod.google:
        icon = SvgPicture.asset(
          AppAssets.googleSvg,
          width: 32,
          height: 32,
        );
        title = 'Google';
        break;
      case LoginMethod.facebook:
        icon = SvgPicture.asset(
          AppAssets.facebookSvg,
          width: 32,
          height: 32,
        );
        title = 'Facebook';
        break;
      case LoginMethod.apple:
        icon = SvgPicture.asset(
          AppAssets.appleSvg,
          width: 32,
          height: 32,
        );
        title = 'Apple';
        break;
      case LoginMethod.twitter:
        icon = SvgPicture.asset(
          AppAssets.twitterSvg,
          width: 32,
          height: 32,
        );
        title = 'X';
        break;
    }

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        alignment: Alignment.center,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Theme.of(context).greyScale100(context), width: 1),
          boxShadow:  [
            BoxShadow(
              color: Theme.of(context).greyScale100(context),
              spreadRadius: 0,
              blurRadius: 6,
              offset: const Offset(0, -3),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            Gap(12.w),
            Text(title,
                style:
                    Theme.of(context).textTheme.lightBodyLargeSemiBold.copyWith(
                          color: Theme.of(context).textPrimary(context),
                        )),
          ],
        ),
      ),
    );
  }
}
