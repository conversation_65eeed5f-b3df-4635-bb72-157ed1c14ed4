import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';

import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

class LoginRedirect extends StatelessWidget {
  const LoginRedirect({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Login Redirect'),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 14.h,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Gap(60.h),
            SvgPicture.asset(
              AppAssets.lionHeart,
              height: 350.h,
              width: 200.w,
            ),
            Gap(20.h),
             Text('You have been redirected to the login page.'),
           Gap(20.h),
            Strong<PERSON><PERSON><PERSON>utton(
                label: 'Go To Login',
                onPressed: () {
                  context.go(RouteName.loginPage);
                })
          ],
        ),
      ),
    );
  }
}
