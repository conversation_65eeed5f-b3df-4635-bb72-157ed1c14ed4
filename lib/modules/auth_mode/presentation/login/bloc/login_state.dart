import 'package:equatable/equatable.dart';

class LoginState extends Equatable {
  final bool isPasswordVisible;
  final bool isLoading;
  final bool isSuccess;
  final bool isFailure;
  final bool isEmailNotVerified;
  final String? message;

  const LoginState({
    required this.isPasswordVisible,
    required this.isLoading,
    required this.isSuccess,
    required this.isFailure,
    required this.isEmailNotVerified,
    this.message,
  });

  // Initial state
  factory LoginState.initial() {
    return const LoginState(
        isPasswordVisible: false,
        isLoading: false,
        isSuccess: false,
        isFailure: false,
        isEmailNotVerified: false,
        message: null);
  }

  // Copy state
  LoginState copyWith(
      {bool? isPasswordVisible,
      bool? isLoading,
      bool? isSuccess,
      bool? isFailure,
      bool? isEmailNotVerified,
      String? message}) {
    return LoginState(
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      isFailure: isFailure ?? this.isFailure,
      message: message ?? this.message,
      isEmailNotVerified: isEmailNotVerified ?? this.isEmailNotVerified,
    );
  }

  @override
  List<Object?> get props =>
      [isPasswordVisible, isLoading, isSuccess, isFailure, message];
}
