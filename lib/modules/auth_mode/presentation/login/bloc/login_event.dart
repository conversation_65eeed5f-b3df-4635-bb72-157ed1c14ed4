import 'package:equatable/equatable.dart';

abstract class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object?> get props => [];
}

// Event for toggling password visibility
class TogglePasswordVisibility extends LoginEvent {}

// Event for submitting the login form
class SubmitLoginForm extends LoginEvent {
  final String email;
  final String password;

  const SubmitLoginForm({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

class ResetEmailNotVerified extends LoginEvent {}

// Event for submitting the login form
class LoginWithGoogle extends LoginEvent {
  const LoginWithGoogle();
}

class LoginWithFacebook extends LoginEvent {
  const LoginWithFacebook();
}

class LoginWithApple extends LoginEvent {
  const LoginWithApple();
}
