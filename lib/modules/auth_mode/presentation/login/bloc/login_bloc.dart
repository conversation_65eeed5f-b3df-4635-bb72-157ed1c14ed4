import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/core/utils/decode_uid.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../core/domain/global_dependencies.dart';
import '../../../../../core/streams/presence_stream_controller.dart';
import '../../../../chat/data/repository/chat_repository.dart';
import 'login_event.dart';
import 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final GlobalStorage storage;
  final AuthRepository authRepository;
  final ChatRepository chatRepository;
  LoginBloc({
    required this.storage,
    required this.authRepository,
    required this.chatRepository,
  }) : super(LoginState.initial()) {
    on<LoginWithGoogle>(_onLoginWithGoogle);
    on<ResetEmailNotVerified>((event, emit) {
      emit(state.copyWith(isEmailNotVerified: false));
    });
    on<TogglePasswordVisibility>((event, emit) {
      emit(state.copyWith(
        isPasswordVisible: !state.isPasswordVisible,
        isFailure: false,
        message: null,
      ));
    });
    on<LoginWithFacebook>(_onLoginWithFacebook);
    on<LoginWithApple>(_onLoginWithApple);
    on<SubmitLoginForm>((event, emit) async {
      try {
        emit(state.copyWith(
            isLoading: true, isFailure: false, isSuccess: false));
        final result = await authRepository.loginWithAccount(
          {
            "email": event.email,
            "password": event.password,
          },
        );
        if (result.status == Status.completed) {
          final accessToken = result.data!.data.token.accessToken;
          await storage.saveToken(accessToken);

          if (storage.accessToken != null && storage.accessToken!.isNotEmpty) {
            final uid = DecodeUID.decodeUID(storage.accessToken!);
            if (uid != null) {
              storage.saveUID(uid);
            }
          }
          await storage.saveUser(
              result.data!.data.userModel!, result.data!.data.userRole ?? '');
          await _initializePresenceSubscription();
          print("user role: ${result.data!.data.userRole}");
          emit(state.copyWith(
            isLoading: false,
            isSuccess: true,
            isFailure: false,
          ));
        } else {
          if (result.code == 40912) {
            emit(state.copyWith(
              isLoading: false,
              isFailure: true,
              message: result.message,
              isEmailNotVerified: false,
            ));
          }
          if (result.code == 40014) {
            emit(state.copyWith(
              isLoading: false,
              isFailure: true,
              message: result.message,
              isEmailNotVerified: false,
            ));
          }

          if (result.code == 40440) {
            emit(state.copyWith(
              isLoading: false,
              isFailure: true,
              message: result.message,
              isEmailNotVerified: false,
            ));
          }

          if (result.code == 40300) {
            emit(state.copyWith(
              isLoading: false,
              isFailure: true,
              message: result.message,
              isEmailNotVerified: true,
            ));
          }
          emit(state.copyWith(
            isLoading: false,
            isFailure: true,
            message: result.message ?? 'Login failed',
          ));
        }
      } on AuthException catch (e) {
        emit(state.copyWith(
            isLoading: false, isFailure: true, message: e.message));
      } on NetworkException catch (e) {
        emit(state.copyWith(
            isLoading: false, isFailure: true, message: e.message));
      } catch (e) {
        debugPrint('Unexpected error in login: $e');
        emit(state.copyWith(
            isLoading: false,
            isFailure: true,
            message: 'Login failed. Please try again.'));
      }
    });
  }

  Future<void> _onLoginWithGoogle(
    LoginWithGoogle event,
    Emitter<LoginState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true, isFailure: false, isSuccess: false));
      final result = await authRepository.loginWithGoogle();

      // Handle ApiResponse<LoginResponse>
      if (result.data != null) {
        final accessToken = result.data!.data.token.accessToken;
        await storage.saveToken(accessToken);

        if (result.data!.data.userModel != null) {
          await storage.saveUser(
              result.data!.data.userModel!, result.data!.data.userRole ?? '');
        }
        await _initializePresenceSubscription();

        emit(state.copyWith(
            isLoading: false, isSuccess: true, isFailure: false));
      } else {
        emit(state.copyWith(
            isLoading: false,
            isFailure: true,
            message: result.message ?? 'Google login failed'));
      }
    } on AuthException catch (e) {
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: e.message));
    } on NetworkException catch (e) {
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: e.message));
    } catch (e) {
      debugPrint('Unexpected error in Google Sign-In: $e');
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: 'Google login failed'));
    }
  }

  Future<void> _onLoginWithFacebook(
    LoginWithFacebook event,
    Emitter<LoginState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true, isFailure: false, isSuccess: false));
      final result = await authRepository.loginWithFacebook();

      // Handle ApiResponse<LoginResponse>
      if (result.data != null) {
        final accessToken = result.data!.data.token.accessToken;
        await storage.saveToken(accessToken);

        if (result.data!.data.userModel != null) {
          await storage.saveUser(
              result.data!.data.userModel!, result.data!.data.userRole ?? '');
        }
        await _initializePresenceSubscription();

        emit(state.copyWith(
            isLoading: false, isSuccess: true, isFailure: false));
      } else {
        emit(state.copyWith(
            isLoading: false,
            isFailure: true,
            message: result.message ?? 'Facebook login failed'));
      }
    } on AuthException catch (e) {
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: e.message));
    } on NetworkException catch (e) {
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: e.message));
    } catch (e) {
      debugPrint('Unexpected error in Facebook Sign-In: $e');
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: 'Facebook login failed'));
    }
  }

  Future<void> _onLoginWithApple(
    LoginWithApple event,
    Emitter<LoginState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true, isFailure: false, isSuccess: false));
      final result = await authRepository.loginWithApple();

      // Handle ApiResponse<LoginResponse>
      if (result.data != null) {
        final accessToken = result.data!.data.token.accessToken;
        debugPrint("Apple Access Token: $accessToken");
        await storage.saveToken(accessToken);

        if (result.data!.data.userModel != null) {
          await storage.saveUser(
              result.data!.data.userModel!, result.data!.data.userRole ?? '');
          print("user role: ${result.data!.data.userRole}");
        }
        await _initializePresenceSubscription();

        emit(state.copyWith(
            isLoading: false, isSuccess: true, isFailure: false));
      } else {
        emit(state.copyWith(
            isLoading: false,
            isFailure: true,
            message: result.message ?? 'Apple login failed'));
      }
    } on AuthException catch (e) {
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: e.message));
    } on NetworkException catch (e) {
      emit(state.copyWith(
          isLoading: false, isFailure: true, message: e.message));
    } catch (e) {
      debugPrint('Unexpected error in Apple Sign-In: $e');
      emit(state.copyWith(
          isLoading: false,
          isFailure: true,
          message: 'Đăng nhập Apple thất bại'));
    }
  }

  Future<void> _initializePresenceSubscription() async {
    try {
      if (gs.uid != null) {
        debugPrint(
            '🔄 LoginBloc: Bắt đầu khởi tạo presence subscription cho user: ${gs.uid}');

        // ✅ Lấy authKey từ api presenceToken của chatRepository
        final authKey = await chatRepository.presenceToken(gs.uid!);
        debugPrint(
            '🔑 LoginBloc: Lấy được authKey: ${authKey.token.substring(0, 10)}...');

        // ✅ Khởi tạo PresenceBloc với authKey thực tế
        debugPrint('📤 LoginBloc: Khởi tạo PresenceBloc với authKey...');

        // Emit event để ChatHomepage biết có thể khởi tạo PresenceBloc
        PresenceStreamController.emit(PresenceSubscribed(
          authKey: authKey.token,
          userId: gs.uid!,
        ));

        debugPrint(
            '✅ PresenceBloc sẽ được khởi tạo ở ChatHomepage với authKey thực tế');
      } else {
        debugPrint(
            '❌ LoginBloc: gs.uid is null, không thể khởi tạo presence subscription');
      }
    } catch (e) {
      debugPrint('❌ Failed to initialize presence subscription: $e');
    }
  }
}
