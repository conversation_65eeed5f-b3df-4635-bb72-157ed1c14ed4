import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_contacts/flutter_contacts.dart';

part 'complete_user_registration_event.dart';
part 'complete_user_registration_state.dart';

class CompleteUserRegistrationBloc
    extends Bloc<CompleteUserRegistrationEvent, CompleteUserRegistrationState> {
  CompleteUserRegistrationBloc() : super(CompleteUserRegistrationInitial()) {
    on<VerifyPhoneNumberEvent>(_onVerifyPhoneNumber);
    on<ImportContactsEvent>(_onImportContacts);
    on<SelectContactsEvent>(_onSelectContacts);
    on<ConnectWithContactsEvent>(_onConnectWithContacts);
  }

  Future<void> _onVerifyPhoneNumber(
    VerifyPhoneNumberEvent event,
    Emitter<CompleteUserRegistrationState> emit,
  ) async {
    try {
      emit(LoadingState());
      // TODO: Implement phone verification API call
      // For now, just simulate success
      await Future.delayed(const Duration(seconds: 1));
      emit(PhoneNumberVerifiedState());
    } catch (e) {
      emit(ErrorState(message: e.toString()));
    }
  }

  Future<void> _onImportContacts(
    ImportContactsEvent event,
    Emitter<CompleteUserRegistrationState> emit,
  ) async {
    try {
      emit(LoadingState());

      // Request contacts permission
      final permission = await FlutterContacts.requestPermission();
      if (!permission) {
        emit(const ErrorState(message: 'Contacts permission denied'));
        return;
      }

      // Get all contacts
      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: true,
      );

      emit(ContactsLoadedState(contacts: contacts));
    } catch (e) {
      emit(ErrorState(message: e.toString()));
    }
  }

  Future<void> _onSelectContacts(
    SelectContactsEvent event,
    Emitter<CompleteUserRegistrationState> emit,
  ) async {
    try {
      emit(LoadingState());
      // TODO: Implement contact selection logic
      emit(ContactsSelectedState(selectedContactIds: event.selectedContactIds));
    } catch (e) {
      emit(ErrorState(message: e.toString()));
    }
  }

  Future<void> _onConnectWithContacts(
    ConnectWithContactsEvent event,
    Emitter<CompleteUserRegistrationState> emit,
  ) async {
    try {
      emit(LoadingState());
      // TODO: Implement connection logic for different platforms
      // This will be replaced with actual API calls later
      await Future.delayed(const Duration(seconds: 1));
      emit(ContactsSelectedState(selectedContactIds: event.contactIds));
    } catch (e) {
      emit(ErrorState(message: e.toString()));
    }
  }
}
