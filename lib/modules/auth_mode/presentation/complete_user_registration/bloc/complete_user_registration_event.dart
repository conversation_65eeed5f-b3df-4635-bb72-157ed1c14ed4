part of 'complete_user_registration_bloc.dart';

sealed class CompleteUserRegistrationEvent extends Equatable {
  const CompleteUserRegistrationEvent();

  @override
  List<Object?> get props => [];
}

class VerifyPhoneNumberEvent extends CompleteUserRegistrationEvent {
  final String phoneNumber;
  final String countryCode;

  const VerifyPhoneNumberEvent({
    required this.phoneNumber,
    required this.countryCode,
  });

  @override
  List<Object?> get props => [phoneNumber, countryCode];
}

class ImportContactsEvent extends CompleteUserRegistrationEvent {}

class SelectContactsEvent extends CompleteUserRegistrationEvent {
  final List<String> selectedContactIds;

  const SelectContactsEvent({required this.selectedContactIds});

  @override
  List<Object?> get props => [selectedContactIds];
}

class ConnectWithContactsEvent extends CompleteUserRegistrationEvent {
  final List<String> contactIds;
  final String platform; // e.g., 'messenger', 'instagram', etc.

  const ConnectWithContactsEvent({
    required this.contactIds,
    required this.platform,
  });

  @override
  List<Object?> get props => [contactIds, platform];
}