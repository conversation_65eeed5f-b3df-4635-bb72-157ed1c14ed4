part of 'complete_user_registration_bloc.dart';

sealed class CompleteUserRegistrationState extends Equatable {
  const CompleteUserRegistrationState();
  
  @override
  List<Object?> get props => [];
}

class CompleteUserRegistrationInitial extends CompleteUserRegistrationState {}

class LoadingState extends CompleteUserRegistrationState {}

class PhoneNumberVerifiedState extends CompleteUserRegistrationState {}

class ContactsLoadedState extends CompleteUserRegistrationState {
  final List<Contact> contacts;

  const ContactsLoadedState({required this.contacts});

  @override
  List<Object?> get props => [contacts];
}

class ContactsSelectedState extends CompleteUserRegistrationState {
  final List<String> selectedContactIds;

  const ContactsSelectedState({required this.selectedContactIds});

  @override
  List<Object?> get props => [selectedContactIds];
}

class ErrorState extends CompleteUserRegistrationState {
  final String message;

  const ErrorState({required this.message});

  @override
  List<Object?> get props => [message];
}