import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../shared/widgets/image_widget/image_widget.dart';

class CardIcon extends StatelessWidget {
  const CardIcon({super.key, required this.obj, required this.onTap});
  final Map obj;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onTap,
          child: CustomImage(
            path: obj['image'],
            width: 54.w,
            height: 54.h,
            imageType: ImageType.svg,
          ),
        ),
        Gap(8.h),
        Text(
          obj['title'],
          style: Theme.of(context).textTheme.lightBodyXSmallMedium.copyWith(
                color: Theme.of(context).textBlack(context),
              ),
        ),
      ],
    );
  }
}
