import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

class CardContactConnect extends StatelessWidget {
  const CardContactConnect(
      {super.key,
      required this.name,
      required this.phone,
      required this.label,
      required this.onPressed,
      required this.backgroundColor,
      required this.widget,
      required this.textColor,
      this.borderColor});

  final String name, phone;
  final String label;
  final VoidCallback onPressed;
  final Color backgroundColor, textColor;
  final Color? borderColor;
  final double widget;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SvgPicture.asset(AppAssets.userSvg, width: 54.w, height: 54.h),
        Gap(14.h),
        Expanded(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name,
              style: Theme.of(context).textTheme.lightBodyLargeBold.copyWith(
                    color: Theme.of(context).secondaryBase(context),
                  ),
            ),
            Gap(6.h),
            Text(
              phone,
              style:
                  Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                        color: Theme.of(context).textBlack(context),
                      ),
            ),
          ],
        )),
        Gap(16.w),
        StrongBodyButton(
          label: label,
          onPressed: onPressed,
          backgroundColor: backgroundColor,
          height: 36.h,
          width: widget,
          textColor: textColor,
          borderColor: borderColor ?? Colors.transparent,
        )
      ],
    );
  }
}
