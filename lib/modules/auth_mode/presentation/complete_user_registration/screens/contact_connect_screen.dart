import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/complete_user_registration/widgets/card_contact.dart';
import 'package:multime_app/modules/auth_mode/presentation/complete_user_registration/widgets/card_icon.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/home_page/session_heading.dart';
import 'package:multime_app/share_native/share_native.dart';
import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../widgets/strong_body_button.dart';

class ContactConnectScreen extends StatefulWidget {
  const ContactConnectScreen({super.key});

  @override
  State<ContactConnectScreen> createState() => _ContactConnectScreenState();
}

class _ContactConnectScreenState extends State<ContactConnectScreen> {
  List<Contact> _contacts = [];
  bool _loading = true;

  Future<void> loadContacts() async {
    // Chỉ dùng FlutterContacts để thống nhất với bloc
    final flutterContactsGranted = await FlutterContacts.requestPermission();
    print(
        'FlutterContacts.requestPermission: [32m$flutterContactsGranted\u001b[0m');
    if (flutterContactsGranted) {
      final contacts = await FlutterContacts.getContacts(withProperties: true);
      print('Số lượng danh bạ lấy được: [32m${contacts.length}\u001b[0m');
      setState(() {
        _contacts = contacts;
        _loading = false;
      });
    } else {
      print('Không được cấp quyền bởi FlutterContacts');
      setState(() => _loading = false);
    }
  }

  List arrChat = [
    {
      'image': AppAssets.facebookIcon,
      'title': 'Messenger',
    },
    {
      'image': AppAssets.instagramSvg,
      'title': 'Instagram',
    },
    {
      'image': AppAssets.snapchatIcon,
      'title': 'Snapchat',
    },
    {
      'image': AppAssets.chatSvg,
      'title': 'Messages',
    },
  ];

  static const platform = MethodChannel("com.multime.app/share");

  // Hàm mở ứng dụng
  Future<void> shareToMessenger(String text) async {
    try {
      await platform.invokeMethod("shareToMessenger", {"text": text});
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToInstagram(String text) async {
    try {
      await platform.invokeMethod("shareToInstagram", {"text": text});
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToSnapchat(String text) async {
    try {
      await platform.invokeMethod("shareToSnapchat", {"text": text});
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToSMS(String text) async {
    try {
      await platform.invokeMethod("shareToSMS", {"text": text});
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToMessengerIOS(String text) async {
    try {
      await platform.invokeMethod('shareToMessenger', {"text": text});
    } catch (e) {
      print("Error: $e");
    }
  }

  TextEditingController copyLinkController = TextEditingController(
      text: 'https://network.strongbody.ai/u/ilyaskhansafi123');
  TextEditingController copyLinkController1 =
      TextEditingController(text: 'https://s.net.vn/hIsx');

  @override
  void dispose() {
    copyLinkController.dispose();
    copyLinkController1.dispose();
    super.dispose();
  }

  bool isShared = false; // ✅ Biến trạng thái

  void handleShare(Function shareFunction) {
    shareFunction();
    setState(() {
      isShared = true; // ✅ Cho phép bấm nút
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 14.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(70.h),
              RichText(
                  text: TextSpan(
                      text: '${LocaleKeys.ConnectWithSuggestedAccounts.tr()}\n',
                      style: Theme.of(context).textTheme.lightHeadingSmall,
                      children: [
                    TextSpan(
                      text: LocaleKeys.InviteAfriendToContinue.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyXLargeRegular
                          .copyWith(
                            color: Colors.grey.shade700,
                          ),
                    )
                  ])),
              Gap(30.h),
              searchView(context, AppAssets.searchSvg,
                  LocaleKeys.SearchYourContacts.tr()),
              Gap(24.h),
              TSectionHeading(
                title: LocaleKeys.AddYourContact.tr(),
                showActionButton: false,
              ),
              Gap(20.h),
              SizedBox(
                  height: 80.h,
                  child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) {
                        // ShareNative shareNative = ShareNative();
                        return CardIcon(
                          obj: arrChat[index],
                          onTap: () {
                            ShareNative shareNative = ShareNative();
                            if (index == 0) {
                              handleShare(() => shareNative.shareToMessenger(
                                  'https://strongbody-92266159995.asia-southeast1.run.app/',
                                  context));
                            } else if (index == 1) {
                              handleShare(() => shareNative.shareToInstagram(
                                  'https://strongbody-92266159995.asia-southeast1.run.app/',
                                  context));
                            } else if (index == 2) {
                              handleShare(() => shareNative.shareToSnapchat(
                                  'https://strongbody-92266159995.asia-southeast1.run.app/',
                                  context));
                            } else if (index == 3) {
                              handleShare(() => shareNative.shareToSMS(
                                  'https://strongbody-92266159995.asia-southeast1.run.app/',
                                  context));
                            }
                          },
                        );
                      },
                      separatorBuilder: (context, index) => Gap(40.w),
                      itemCount: arrChat.length)),
              _loading
                  ? const Center(child: CircularProgressIndicator())
                  : ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      separatorBuilder: (context, index) => Gap(20.h),
                      itemCount: _contacts.length,
                      itemBuilder: (context, index) {
                        final contact = _contacts[index];
                        final name = contact.displayName;
                        final rawPhone = contact.phones.isNotEmpty
                            ? contact.phones.first.number
                            : 'N/A';

                        // Clean phone number - chỉ giữ lại số và dấu +
                        final phone =
                            rawPhone.replaceAll(RegExp(r'[^\d+]'), '');

                        return CardContactConnect(
                          name: name,
                          phone: rawPhone, // Hiển thị số gốc
                          backgroundColor: Colors.red,
                          borderColor: Colors.transparent,
                          widget: 91.w,
                          label: 'Connect',
                          onPressed: () async {
                            try {
                              // Gửi link lời mời qua SMS đến số điện thoại của contact
                              ShareNative shareNative = ShareNative();
                              await shareNative.shareToSMSWithPhone(
                                'Join Multime with me! Download the application here: https://strongbody-92266159995.asia-southeast1.run.app/',
                                phone, // Sử dụng số đã clean
                                context,
                              );
                              // Đánh dấu đã share để kích hoạt nút Continue
                              setState(() {
                                isShared = true;
                              });
                            } catch (e) {
                              print('Error calling shareToSMSWithPhone: $e');
                            }
                          },
                          textColor: Colors.white,
                        );
                      },
                    ),
              Gap(10.h),
              StrongBodyButton(
                label: LocaleKeys.continueButton.tr(),
                onPressed: isShared
                    ? () => context.go(RouteName.homeStrongBody)
                    : null,
                backgroundColor: isShared
                    ? Colors.red
                    : Colors.grey.shade300, // Hoặc tuỳ chỉnh
                height: 50.h,
                width: double.infinity,
                textColor: isShared
                    ? Colors.white
                    : Theme.of(context).greyScale600(context),
              ),
              Gap(50.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget searchView(BuildContext context, String image, String hintText,
      {TextEditingController? controller}) {
    return Row(
      children: [
        Container(
          width: 340.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15.h),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1.w,
            ),
          ),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: SvgPicture.asset(image),
              ),
              Expanded(
                child: SizedBox(
                  height: 45.w,
                  child: TextField(
                    controller: controller,
                    keyboardType: TextInputType.multiline,
                    decoration: InputDecoration(
                      hintText: hintText,
                      contentPadding: const EdgeInsets.fromLTRB(2, 5, 2, 5),
                      border: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      disabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      hintStyle: Theme.of(context)
                          .textTheme
                          .lightBodyMediumRegular
                          .copyWith(
                            color: Colors.grey.shade300,
                          ),
                    ),
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumRegular
                        .copyWith(
                          color: Colors.grey.shade300,
                        ),
                    autocorrect: false,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
