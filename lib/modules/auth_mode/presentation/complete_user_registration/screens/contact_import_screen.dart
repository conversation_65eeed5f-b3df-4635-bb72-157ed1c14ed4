import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../bloc/complete_user_registration_bloc.dart';

class ContactImportScreen extends StatelessWidget {

  const ContactImportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<CompleteUserRegistrationBloc,
        CompleteUserRegistrationState>(
      listener: (context, state) {
        if (state is ContactsLoadedState) {
          print('ContactsLoadedState');

          context.push(
            RouteName.contactConnect,
            extra: context.read<CompleteUserRegistrationBloc>(),
          );
        }
      },
      child: Scaffold(
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 14.w),
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gap(70.h),
                  RichText(
                      text: TextSpan(
                          text: ' ${LocaleKeys.PickBestFriendsClients.tr()}\n',
                          style:
                              Theme.of(context).textTheme.lightBodyXLargeBold,
                          children: [
                        TextSpan(
                          text: LocaleKeys.InviteAfriendToContinue.tr(),
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeRegular
                              .copyWith(color: Colors.grey.shade300),
                        )
                      ])),
                  Gap(30.h),
                  searchView(context),
                  Gap(44.h),
                  Center(
                    child: SvgPicture.asset(AppAssets.contactsSvg),
                  ),
                  Gap(58.h),
                  RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                          text: '${LocaleKeys.ImportYourContacts.tr()}\n',
                          style: Theme.of(context).textTheme.lightHeadingSmall,
                          children: [
                            TextSpan(
                              text: LocaleKeys.StrongbodyNeverTexts.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeRegular,
                            )
                          ])),
                ],
              ),
              Positioned(
                bottom: 50.h,
                left: 0,
                right: 0,
                child: StrongBodyButton(
                  label: LocaleKeys.Import.tr(),
                  onPressed: () {
                    context.read<CompleteUserRegistrationBloc>().add(
                          ImportContactsEvent(),
                        );
                  },
                  backgroundColor: Colors.red,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget searchView(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 340.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15.h),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1.w,
            ),
          ),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: SvgPicture.asset(AppAssets.searchSvg),
              ),
              Expanded(
                child: SizedBox(
                  height: 45.w,
                  child: TextField(
                    keyboardType: TextInputType.multiline,
                    decoration: InputDecoration(
                      hintText: LocaleKeys.SearchYourContacts.tr(),
                      contentPadding: const EdgeInsets.fromLTRB(2, 5, 2, 5),
                      border: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      disabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      hintStyle: Theme.of(context)
                          .textTheme
                          .lightBodyMediumRegular
                          .copyWith(
                            color: Colors.grey.shade300,
                          ),
                    ),
                    style: Theme.of(context).textTheme.lightBodyMediumRegular,
                    autocorrect: false,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
