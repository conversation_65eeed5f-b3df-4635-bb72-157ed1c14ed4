// import 'package:country_code_picker/country_code_picker.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:gap/gap.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:go_router/go_router.dart';
// import 'package:multime_app/core/constants/padding_constant.dart';
// import 'package:multime_app/core/l10n/locale_keys.g.dart';
// import 'package:multime_app/core/themes/app_text_style.dart';
// import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
// import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
// import '../../../../../app/routers/routers_name.dart';
// import '../../../../../shared/widgets/app_loader/app_loader.dart';
// import '../bloc/complete_user_registration_bloc.dart';

// class VerifyPhoneScreen extends StatefulWidget {
//   const VerifyPhoneScreen({super.key});

//   @override
//   State<VerifyPhoneScreen> createState() => _VerifyPhoneScreenState();
// }

// class _VerifyPhoneScreenState extends State<VerifyPhoneScreen> {
//   late CountryCode countryCode;
//   TextEditingController txMobile = TextEditingController();

//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (context) => CompleteUserRegistrationBloc(),
//       child: BlocConsumer<CompleteUserRegistrationBloc,
//           CompleteUserRegistrationState>(
//         listener: (context, state) {
//           if (state is LoadingState) {
//             AppLoader.show(context);
//           } else if (state is PhoneNumberVerifiedState) {
//             AppLoader.hide();
//             context.push(
//               RouteName.contactImport,
//               extra: context.read<
//                   CompleteUserRegistrationBloc>(), // Truyền bloc qua extra
//             );
//           } else if (state is ErrorState) {
//             AppLoader.hide();
//             ScaffoldMessenger.of(context).showSnackBar(
//               SnackBar(content: Text(state.message)),
//             );
//           } else {
//             AppLoader.hide();
//           }
//         },
//         builder: (context, state) {
//           return Scaffold(
//             appBar: AppBar(),
//             body: SingleChildScrollView(
//               child: Padding(
//                 padding: PaddingConstants.padAll16,
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     //Title
//                     Text(
//                       LocaleKeys.phoneNumberTitle.tr(),
//                       style: Theme.of(context).textTheme.lightBodyMediumMedium,
//                     ),
//                     Gap(8.h),
//                     Text(
//                       LocaleKeys.phoneNumberDescription.tr(),
//                       style: Theme.of(context).textTheme.lightBodyMediumMedium,
//                     ),
//                     Gap(18.h),
//                     Container(
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         border: Border.all(
//                           color: Colors.grey.shade300,
//                         ),
//                         borderRadius: BorderRadius.circular(8),
//                       ),
//                       child: CountryCodePicker(
//                         onChanged: (code) {
//                           setState(() {
//                             countryCode = code;
//                           });
//                         },
//                         initialSelection: 'VN',
//                         favorite: const ['VN'],
//                         showCountryOnly: false,
//                         showOnlyCountryWhenClosed: false,
//                         alignLeft: false,
//                       ),
//                     ),
//                     Gap(18.h),
//                     StrongBodyTextField(
//                       controller: txMobile,
//                       labalText: LocaleKeys.enterMobileNumber.tr(),
//                       isLabel: false,
//                       isSuffixIcon: true,
//                       suffixIcon: txMobile.text.isNotEmpty
//                           ? IconButton(
//                               onPressed: () {
//                                 txMobile.clear();
//                               },
//                               icon: const Icon(Icons.clear),
//                             )
//                           : null,
//                     ),
//                     Gap(18.h),
//                     Row(
//                       children: [
//                         Expanded(
//                             child: StrongBodyButton(
//                           label: LocaleKeys.SKIP.tr(),
//                           onPressed: () {
//                             context.read<CompleteUserRegistrationBloc>().add(
//                                   VerifyPhoneNumberEvent(
//                                       phoneNumber: txMobile.text,
//                                       countryCode: countryCode.dialCode!),
//                                 );
//                           },
//                           backgroundColor: Colors.grey.shade300,
//                         )),
//                         Gap(22.w),
//                         Expanded(
//                           child: StrongBodyButton(
//                             label: LocaleKeys.continueButton,
//                             onPressed: () {
//                               context.read<CompleteUserRegistrationBloc>().add(
//                                     VerifyPhoneNumberEvent(
//                                       phoneNumber: txMobile.text,
//                                       countryCode: countryCode.dialCode!,
//                                     ),
//                                   );
//                             },
//                             backgroundColor: Colors.red,
//                           ),
//                         )
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
