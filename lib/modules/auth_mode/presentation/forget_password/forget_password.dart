import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:multime_app/modules/auth_mode/presentation/forget_password/bloc/forget_password_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/forget_password/bloc/forget_password_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/forget_password/bloc/forget_password_state.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';

import '../../../../core/utils/validation_utils.dart';

class ForgetPasswordPage extends StatefulWidget {
  const ForgetPasswordPage({super.key});

  @override
  State<ForgetPasswordPage> createState() => _ForgetPasswordPageState();
}

class _ForgetPasswordPageState extends State<ForgetPasswordPage> {
  final forgetPasswordFormKey = GlobalKey<FormState>();
  final FocusNode emailFocusNode = FocusNode();
  TextEditingController emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgetPasswordBloc(
        authRepository: getIt<AuthRepository>(),
      ),
      child: BlocConsumer<ForgetPasswordBloc, ForgetPasswordState>(
        listener: (context, state) {
          if (state.isLoading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
          if (state.isEmailSent &&
              (state.error == null || state.error!.isEmpty)) {
            context.push(RouteName.otpPage, extra: {
              "email": state.email,
              "flowType": OtpFlowType.forgotPassword,
              "resendOtp": false,
            });
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              leading: GestureDetector(
                onTap: () => context.pop(),
                child: Icon(
                  Icons.arrow_back_ios_rounded,
                  size: 24.sp,
                ),
              ),
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 22.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLogo(context),
                  Gap(22.h),
                  _buildHeaderForgetPassword(context),
                  Gap(22.h),
                  _buildEmail(context),
                  Gap(38.h),
                  _buildButtonForgetPassword(context)
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLogo(BuildContext context) {
    return Center(
      child: SvgPicture.asset(
        AppAssets.logoSvg,
        width: 138.w,
        height: 42.h,
      ),
    );
  }

  Widget _buildHeaderForgetPassword(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.resetPassword.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium,
        ),
        Gap(10.h),
        Text(
          LocaleKeys.resetPasswordText.tr(),
          style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                color: Theme.of(context).textSecondary(context),
              ),
        )
      ],
    );
  }

  Widget _buildEmail(BuildContext context) {
    return Form(
      key: forgetPasswordFormKey,
      child: Column(
        children: [
          StrongBodyTextField(
            controller: emailController,
            labalText: LocaleKeys.emailLabel.tr(),
            isLabel: true,
            focusNode: emailFocusNode,
            validator: (value) => ValidateForm.validateEmail(value),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonForgetPassword(BuildContext context) {
    return StrongBodyButton(
      fontSize: 16,
      backgroundColor: Theme.of(context).primary(context),
      label: LocaleKeys.sendEmail.tr(),
      onPressed: () {
        if (forgetPasswordFormKey.currentState!.validate()) {
          context.read<ForgetPasswordBloc>().add(
                SubmitForgetPasswordEvent(emailController.text),
              );
        }
      },
    );
  }
}
