import 'package:bloc/bloc.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'forget_password_event.dart';
import 'forget_password_state.dart';

class ForgetPasswordBloc
    extends Bloc<ForgetPasswordEvent, ForgetPasswordState> {
  final AuthRepository authRepository;
  ForgetPasswordBloc({required this.authRepository})
      : super(ForgetPasswordState()) {
    on<SubmitForgetPasswordEvent>(_onSubmitForgetPassword);
  }

  Future<void> _onSubmitForgetPassword(
    SubmitForgetPasswordEvent event,
    Emitter<ForgetPasswordState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));
    try {
      final response = await authRepository.forgotPassword(
        event.email,
      );
      if (response.status == Status.completed) {
        emit(state.copyWith(
            isLoading: false,
            isEmailSent: true,
            email: event.email,
            error: ''));
      } else {
        emit(state.copyWith(isLoading: false, error: response.message));
      }
    } on AuthException catch (e) {
      emit(state.copyWith(isLoading: false, error: e.message));
    } catch (e) {
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }
}
