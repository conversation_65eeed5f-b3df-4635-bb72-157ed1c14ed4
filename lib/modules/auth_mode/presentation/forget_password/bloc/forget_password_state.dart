class ForgetPasswordState {
  final bool isLoading;
  final String? error;
  final bool isEmailSent;
  final String? email;

  ForgetPasswordState({
    this.isLoading = false,
    this.error,
    this.isEmailSent = false,
    this.email,
  });

  ForgetPasswordState copyWith({
    bool? isLoading,
    String? error,
    bool? isEmailSent,
    String? email,
  }) {
    return ForgetPasswordState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isEmailSent: isEmailSent ?? this.isEmailSent,
      email: email ?? this.email,
    );
  }
}
