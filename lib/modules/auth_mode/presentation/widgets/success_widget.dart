import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';

import '../../../../app/routers/routers_name.dart';

class SuccessWidget extends StatefulWidget {
  const SuccessWidget({
    super.key,
  });

  @override
  State<SuccessWidget> createState() => _SuccessWidgetState();
}

class _SuccessWidgetState extends State<SuccessWidget> {
  @override
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 5), () {
      if (context.mounted) {
        context.go(RouteName.contactImport);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
            color: Colors.white,
            child: LottieBuilder.asset('assets/aimation/success_email.json')),
      ),
    );
  }
}
