import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class LoginAccountPrompt extends StatelessWidget {
  final VoidCallback gotoPage;
  final String label, subLabel;
  const LoginAccountPrompt(
      {super.key,
      required this.gotoPage,
      required this.label,
      required this.subLabel});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          tr(label),
          style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                color: Theme.of(context).greyScale700(context),
              ),
        ),
        Gap(2.w),
        GestureDetector(
            onTap: gotoPage,
            child: Text(
              tr(subLabel),
              style: Theme.of(context).textTheme.lightBodyLargeBold.copyWith(
                    color:Theme.of(context).primary(context),
                  ),
            ))
      ],
    );
  }
}
