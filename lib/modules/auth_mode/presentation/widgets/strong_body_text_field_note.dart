import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class StrongBodyTextFieldNote extends StatefulWidget {
  final String? labelText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextStyle? labelStyle;
  final Color? requiredIconColor;
  final String hintText;
  final bool isLarge;
  final bool isRequire;
  final bool isPassword;
  final bool showPassword;
  final bool enabled;
  final Function()? toggleShowPassword;
  final String? Function(String?)? validator;
  final String noteText;
  final bool isNote;
  final TextInputAction textInputAction;
  final bool readOnly;
  final Widget? suffixIcon;
  final bool isSuffixIcon;
  final EdgeInsetsGeometry? contentPadding;

  const StrongBodyTextFieldNote(
      {super.key,
      this.labelText,
      this.controller,
      this.focusNode,
      this.labelStyle,
      this.requiredIconColor,
      this.hintText = '',
      this.isLarge = false,
      this.isRequire = false,
      this.isPassword = false,
      this.showPassword = false,
      this.enabled = true,
      this.toggleShowPassword,
      this.validator,
      this.isNote = true,
       required this.noteText,
      this.textInputAction = TextInputAction.next,
      this.readOnly = false,
      this.suffixIcon,
      this.isSuffixIcon = false,
      this.contentPadding,
      });

  @override
  State<StrongBodyTextFieldNote> createState() =>
      _StrongBodyTextFieldNoteState();
}

class _StrongBodyTextFieldNoteState extends State<StrongBodyTextFieldNote> {
  bool _isValid = true;
  String? _errorText;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          readOnly: widget.readOnly,
          textInputAction: TextInputAction.next,
          focusNode: widget.focusNode,
          validator: (value) {
            String? error = widget.validator!(value);
            setState(() {
              if (error != null) {
                _isValid = false;
                _errorText = error;
              } else {
                _isValid = true;
                _errorText = null;
              }
            });
            return error;
          },
          enabled: widget.enabled,
          maxLines: widget.isLarge ? 3 : 1,
          obscureText: widget.isPassword && !widget.showPassword,
          controller: widget.controller,
          cursorColor: Colors.black,
          decoration: InputDecoration(
            contentPadding: widget.contentPadding ??
                const EdgeInsets.only(left: 15.0, bottom: 16, top: 10),
            suffixIcon: widget.isPassword
                ? IconButton(
                    onPressed: widget.toggleShowPassword!,
                    icon: Icon(
                      widget.showPassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Colors.grey,
                    ),
                  )
                : widget.isSuffixIcon
                    ? widget.suffixIcon
                    : null,
            hintText: widget.hintText != '' ? tr(widget.hintText) : '',
            labelText: widget.labelText,
          ),
          // name: label,
        ),
        if (_isValid && widget.isNote)
          Column(
            children: [
              Gap(8.h),
              Text(
                widget.noteText,
                style:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Theme.of(context).greyScale600(context),
                        ),
              ),
            ],
          )
      ],
    );
  }
}
