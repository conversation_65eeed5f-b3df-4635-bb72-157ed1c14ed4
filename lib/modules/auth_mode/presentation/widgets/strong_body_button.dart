import 'package:flutter/material.dart';

class StrongBodyButton extends StatelessWidget {
  final String label;
  final Function()? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double circular;

  final bool enable;
  final double width;
  final double height;
  final int? fontSize;

  const StrongBodyButton(
      {super.key,
      required this.label,
      required this.onPressed,
      this.backgroundColor,
      this.textColor,
      this.borderColor,
      this.enable = true,
      this.width = double.infinity,
      this.height = 50,
      this.circular = 10,
      this.fontSize});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: enable ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
        minimumSize: Size(width, height),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(circular),
          side: BorderSide(
            color: borderColor ?? Colors.transparent,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: fontSize?.toDouble(),
                  color: textColor ?? Colors.white,
                ),
          ),
        ],
      ),
    );
  }
}
