import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../core/themes/app_colors.dart';

class StrongBodyTextField extends StatelessWidget {
  final String? label;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextStyle? labelStyle;
  final Color? requiredIconColor;
  final String hintText;
  final bool isLarge;
  final bool isRequire;
  final bool isPassword;
  final bool showPassword;
  final bool isLabel;
  final bool enabled;
  final Function()? toggleShowPassword;
  final String? Function(String?)? validator;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? hintStyle;
  final VoidCallback? showCountryDialog;
  final Widget? suffixIcon;
  final bool isSuffixIcon;
  final Function(String)? onChanged;
  final TextInputAction textInputAction;
  final bool readOnly;
  final int maxLines;
  final String? errorText;
  final String? labalText;
  final Widget? prefixIcon;
  final double minWidth;
  final TextInputType? keyboardType;
  final InputBorder? border;
  final InputBorder? focusedBorder;
  final InputBorder? enabledBorder;

  const StrongBodyTextField(
      {super.key,
      this.label,
      this.controller,
      this.focusNode,
      this.labelStyle,
      this.requiredIconColor,
      this.hintText = '',
      this.isLarge = false,
      this.isRequire = false,
      this.isPassword = false,
      this.showPassword = false,
      this.enabled = true,
      this.toggleShowPassword,
      this.validator,
      this.isLabel = true,
      this.errorText,
      this.contentPadding,
      this.hintStyle,
      this.showCountryDialog,
      this.suffixIcon,
      this.prefixIcon,
      this.isSuffixIcon = false,
      this.onChanged,
      this.textInputAction = TextInputAction.next,
      this.maxLines = 1,
      this.minWidth = 30,
      this.keyboardType,
      this.border,
      this.focusedBorder,
      this.enabledBorder,
      this.readOnly = false,
      this.labalText});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          textInputAction: textInputAction,
          onChanged: onChanged,
          focusNode: focusNode,
          validator: validator,
          enabled: enabled,
          maxLines: maxLines,
          obscureText: isPassword && !showPassword,
          controller: controller,
          cursorColor: Colors.black,
          onTap: enabled ? () => showCountryDialog?.call() : null,
          readOnly: readOnly,
          decoration: InputDecoration(
            errorText: errorText,
            suffixIcon: isPassword
                ? IconButton(
                    onPressed: toggleShowPassword!,
                    icon: Icon(
                      showPassword
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                      color: Colors.grey,
                    ),
                  )
                : isSuffixIcon
                    ? suffixIcon
                    : null,
            hintText: hintText != '' ? hintText : '',
            labelText: labalText,
            labelStyle:
                Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                      color: AppColors.textSecondary100,
                    ),
            alignLabelWithHint: true,
            isCollapsed: true,
            contentPadding: EdgeInsets.symmetric(
              vertical: 12.h,
              horizontal: 16.w,
            ),
            hintStyle: hintStyle,
            prefixIcon: prefixIcon,
            prefixIconConstraints: BoxConstraints(
              minWidth: minWidth,
            ),
            border: border ??
                OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
            focusedBorder: focusedBorder ??
                OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Theme.of(context).greyScale500(context),
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
            enabledBorder: enabledBorder ??
                OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Theme.of(context).greyScale100(context),
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
          ),
          // name: label,
        ),
      ],
    );
  }
}
