import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../bloc/otp_bloc.dart';
import '../bloc/otp_event.dart';
import '../bloc/otp_state.dart';

class CountDownTimer extends StatefulWidget {
  final int secondsRemaining;
  const CountDownTimer({
    super.key,
    required this.secondsRemaining,
  });

  @override
  State<CountDownTimer> createState() => _CountDownTimerState();
}

class _CountDownTimerState extends State<CountDownTimer> {
  late Timer _timer;
  late int _secondsRemaining;

  @override
  void initState() {
    super.initState();
    _secondsRemaining = widget.secondsRemaining;
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_secondsRemaining > 0) {
        setState(() {
          _secondsRemaining--;
        });
      } else {
        _timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OtpBloc, OtpState>(
      listener: (context, state) {
        if (state.isSuccess) {
          setState(() {
            _secondsRemaining = widget.secondsRemaining;
          });
          _timer.cancel();
          _startTimer();
        }
      },
      child: _secondsRemaining > 0
          ? Text(
              'Resend (${_secondsRemaining ~/ 60}:${(_secondsRemaining % 60).toString().padLeft(2, '0')})',
              style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color: Theme.of(context).primary(context),
                  ),
            )
          : GestureDetector(
              onTap: () => context.read<OtpBloc>().add(const ResendOtp()),
              child: Text(
                'Resend',
                style:
                    Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                          color: Theme.of(context).primary(context),
                        ),
              ),
            ),
    );
  }
}
