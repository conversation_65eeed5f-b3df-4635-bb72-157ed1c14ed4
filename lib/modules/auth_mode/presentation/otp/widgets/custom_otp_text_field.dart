import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:pinput/pinput.dart';

class CustomOtpTextField extends StatelessWidget {
  // final Function(String) onCompleted;
  final TextEditingController controller;

  const CustomOtpTextField({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Pinput(
        controller: controller,
        // onCompleted: onCompleted,
        length: 6,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        defaultPinTheme: PinTheme(
          width: 56,
          height: 56,
          textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 20.sp,
              fontWeight: FontWeight.w700,
              color: Colors.black),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey.shade500,
              width: 1,
            ),
          ),
        ),
        focusedPinTheme: PinTheme(
          width: 56,
          height: 56,
          textStyle: Theme.of(context).textTheme.lightHeadingSmall,
          decoration: BoxDecoration(
            color: Theme.of(context).greyScale50(context),
            borderRadius: BorderRadius.circular(8),
          ),
        ));
  }
}
