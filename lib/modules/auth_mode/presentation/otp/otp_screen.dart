import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_state.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

import '../../../../shared/widgets/app_loader/app_loader.dart';
import 'bloc/otp_event.dart';
import 'widgets/count_down_widget.dart';
import 'widgets/custom_otp_text_field.dart';

class OTPScreen extends StatefulWidget {
  OTPScreen({
    super.key,
    this.email,
    this.resendOtp,
  });
  final String? email;
  final bool? resendOtp;
  @override
  State<OTPScreen> createState() => _OTPScreenState();
}

class _OTPScreenState extends State<OTPScreen> {
  late TextEditingController otpController;

  @override
  void initState() {
    super.initState();
    otpController = TextEditingController();
    if (widget.resendOtp == true) {
      context.read<OtpBloc>().add(ResendOtp());
    }
  }

  @override
  void dispose() {
    otpController.dispose();
    super.dispose();
  }

  String _getHeaderText(OtpFlowType flowType) {
    // switch (flowType) {
    //   case OtpFlowType.signup:
    return 'Enter OTP';
    // case OtpFlowType.forgotPassword:
    //   return 'Reset Password OTP';
    // }
  }

  String _getSubHeaderText(OtpFlowType flowType, String email) {
    // switch (flowType) {
    //   case OtpFlowType.signup:
    return 'An 6 digit code has been sent to $email';
    // case OtpFlowType.forgotPassword:
    //   return 'A 6 digit code has been sent to $email to reset your password';
    // }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('Building OTP Screen with flowType: ${widget.resendOtp}');
    final otpBloc = context.read<OtpBloc>();
    return Scaffold(
      appBar: AppBar(
        leading: GestureDetector(
          onTap: () {
            context.go(RouteName.loginPage);
          },
          child: Icon(
            Icons.arrow_back_ios_rounded,
            size: 24.sp,
          ),
        ),
      ),
      body: BlocListener<OtpBloc, OtpState>(
        // listenWhen: (previous, current) {
        //   return previous.error != current.error &&
        //       current.error == true &&
        //       current.isLoading == false;
        // },
        listener: (context, state) {
          debugPrint(
              '🔄 OTP State changed: isSuccess=${state.isSuccess}, error=${state.error}, isLoading=${state.isLoading}');

          if (state.isLoading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }

          if (state.isSuccess) {
            if (otpBloc.flowType == OtpFlowType.signup) {
              context.go(RouteName.success);
            } else if (otpBloc.flowType == OtpFlowType.forgotPassword) {
              context.push(RouteName.createForgotPasswordPage,
                  extra: state.secretToken);
            }
          } else if (state.error) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.messageError),
                duration: Duration(seconds: 1),
              ),
            );
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height - kToolbarHeight,
              ),
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Gap(20.h),
                    Center(child: SvgPicture.asset(AppAssets.securityOtpSvg)),
                    Gap(62.h),
                    Text(_getHeaderText(otpBloc.flowType),
                        style: Theme.of(context).textTheme.lightHeadingMedium),
                    Gap(12.h),
                    Text(
                      _getSubHeaderText(otpBloc.flowType, otpBloc.email),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeRegular
                          .copyWith(
                            color: Theme.of(context).textSecondary(context),
                          ),
                    ),
                    Gap(32.h),
                    CustomOtpTextField(
                      // onCompleted: (pin) {
                      //   otpBloc.add(VerifyOtp(
                      //     pin,
                      //     flowType: otpBloc.flowType,
                      //   ));
                      // },
                      controller: otpController,
                    ),
                    Gap(16.h),
                    Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Didn\'t receive the code?',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeRegular
                                .copyWith(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                ),
                          ),
                          const CountDownTimer(
                            secondsRemaining: 60,
                          ),
                        ],
                      ),
                    ),
                    Gap(30.h),
                    StrongBodyButton(
                        fontSize: 16,
                        backgroundColor: Theme.of(context).primary(context),
                        label: 'Confirm',
                        onPressed: () {
                          if (otpBloc.flowType == OtpFlowType.signup) {
                            otpBloc.add(VerifyOtpEmail(
                              otpController.text,
                              flowType: otpBloc.flowType,
                            ));
                          } else if (otpBloc.flowType ==
                              OtpFlowType.forgotPassword) {
                            otpBloc.add(verifyOTPForgotPassword(
                              otpController.text,
                              flowType: otpBloc.flowType,
                            ));
                          }
                        }),
                    const Spacer(),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: Theme.of(context).textTheme.lightBodyLargeMedium,
                        children: [
                          const TextSpan(
                            text:
                                'Did not receive the email? Check your spam filter, or ',
                          ),
                          TextSpan(
                            text: 'try another email address',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeMedium
                                .copyWith(
                                  color: Theme.of(context).primary(context),
                                ),
                          ),
                        ],
                      ),
                    ),
                    Gap(20.h),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
