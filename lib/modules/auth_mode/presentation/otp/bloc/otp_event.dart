import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';

abstract class OtpEvent extends Equatable {
  const OtpEvent();

  @override
  List<Object?> get props => [];
}

class EmailChange extends OtpEvent {
  final String email;
  const EmailChange(this.email);

  @override
  List<Object?> get props => [email];
}

class verifyOTPForgotPassword extends OtpEvent {
  final String? otp;
  final OtpFlowType? flowType;
  const verifyOTPForgotPassword(
    this.otp, {
    this.flowType,
  });

  @override
  List<Object?> get props => [
        otp,
        flowType,
      ];
}

class VerifyOtpEmail extends OtpEvent {
  final String? otp;
  final OtpFlowType? flowType;
  const VerifyOtpEmail(
    this.otp, {
    this.flowType,
  });

  @override
  List<Object?> get props => [
        otp,
        flowType,
      ];
}

class ResendOtp extends OtpEvent {
  const ResendOtp();
  @override
  List<Object?> get props => [];
}
