import 'package:equatable/equatable.dart';

class OtpState extends Equatable {
  final bool isLoading;
  final String? errorMessage;
  final bool isSuccess;
  final bool error;
  final String messageError;
  final String? secretToken;
  const OtpState({
    this.isLoading = false,
    this.errorMessage,
    this.isSuccess = false,
    this.error = false,
    this.messageError = '',
    this.secretToken,
  });
  factory OtpState.initial() {
    return const OtpState(
      isLoading: false,
      errorMessage: null,
      isSuccess: false,
      error: false,
      messageError: '',
      secretToken: null,
    );
  }
  OtpState copyWith({
    bool? isLoading,
    String? errorMessage,
    bool? isSuccess,
    bool? error,
    String? messageError,
    String? secretToken,
  }) {
    return OtpState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error ?? this.error,
      messageError: messageError ?? this.messageError,
      secretToken: secretToken ?? this.secretToken,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        errorMessage,
        isSuccess,
        error,
        messageError,
        secretToken,
      ];
}

// class OtpInitial extends OtpState {
//   final OtpFlowType flowType;
//   final String email;
//   const OtpInitial({required this.flowType, required this.email});

//   @override
//   List<Object?> get props => [flowType, email];
// }

// class OtpLoading extends OtpState {}

// class OtpVerified extends OtpState {}

// class OtpError extends OtpState {
//   final String message;
//   const OtpError(this.message);

//   @override
//   List<Object?> get props => [message];
// }

// class OtpResendSuccess extends OtpState {}

// class OtpResendError extends OtpState {
//   final String message;
//   const OtpResendError(this.message);

//   @override
//   List<Object?> get props => [message];
// }
