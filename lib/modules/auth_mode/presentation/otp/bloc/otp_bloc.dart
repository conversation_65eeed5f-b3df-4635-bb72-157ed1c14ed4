import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:multime_app/modules/auth_mode/data/services/auth_services.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_event.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_state.dart';
import '../../../../../core/di/locator.dart';

class OtpBloc extends Bloc<OtpEvent, OtpState> {
  final OtpFlowType flowType;
  final Map<String, dynamic>? userData;
  final String email;
  final GlobalStorage storage;
  final AuthRepository authRepository;
  OtpBloc({
    required this.authRepository,
    required this.flowType,
    this.userData,
    required this.email,
    required this.storage,
  }) : super(OtpState.initial()) {
    on<verifyOTPForgotPassword>(_onVerifyOtpForgotPassword);
    on<VerifyOtpEmail>(_onVerifyOtpEmail);
    on<ResendOtp>(_onResendOtp);
  }

  Future<void> _onVerifyOtpForgotPassword(
      verifyOTPForgotPassword event, Emitter<OtpState> emit) async {
    emit(state.copyWith(
        isLoading: true, error: false, isSuccess: false, messageError: ''));

    try {
      // (Nếu muốn kiểm tra OTP rỗng thì mở lại đoạn này)
      // if (event.otp == null || event.otp!.isEmpty) {
      //   emit(state.copyWith(
      //     isLoading: false,
      //     error: true,
      //     isSuccess: false,
      //     messageError: 'OTP cannot be empty',
      //   ));
      //   return;
      // }

      final result = await authRepository.verifyOTPForgotPassword(
          email, event.otp!, flowType);

      if (result.status == Status.completed) {
        String? secretToken;

        secretToken = result.data!.data!.secretToken;
        emit(state.copyWith(
          isSuccess: true,
          isLoading: false,
          error: false,
          secretToken: secretToken,
          messageError: '',
        ));
      } else {
        emit(state.copyWith(
          error: true,
          isSuccess: false,
          isLoading: false,
          messageError: result.message ?? 'OTP verification failed',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: true,
        isSuccess: false,
        messageError: 'OTP verification failed: ${e.toString()}',
      ));
    }
  }

  void _onVerifyOtpEmail(VerifyOtpEmail event, Emitter<OtpState> emit) async {
    emit(state.copyWith(
        isLoading: true, error: false, isSuccess: false, messageError: ''));

    try {
      debugPrint('Verifying OTP for email: ${event.otp}');
      final result =
          await authRepository.verifyOTPEmail(event.otp!, email, flowType);

      if (result.status == Status.completed) {
        emit(state.copyWith(
          isSuccess: true,
          isLoading: false,
          error: false,
          messageError: '',
        ));
      } else {
        debugPrint('OTP verification failed: ${result.message}');
        emit(state.copyWith(
          error: true,
          isSuccess: false,
          isLoading: false,
          messageError: result.message ?? 'OTP verification failed',
        ));
      }
    } catch (e) {
      debugPrint('Error verifying OTP: $e');
      emit(state.copyWith(
        isLoading: false,
        error: true,
        isSuccess: false,
        messageError: 'OTP verification failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onResendOtp(ResendOtp event, Emitter<OtpState> emit) async {
    try {
      final data = {
        'email': email,
      };
      emit(state.copyWith(isLoading: true));
      switch (flowType) {
        case OtpFlowType.signup:
          await authRepository.sendVerificationEmail(data);
          break;
        case OtpFlowType.forgotPassword:
          final authServices = getIt<AuthServices>();
          // await authRepository.sendVerificationEmail(email: email);
          break;
      }
      emit(state.copyWith(isLoading: false, isSuccess: false));
    } catch (e) {
      emit(state.copyWith(error: true, messageError: e.toString()));
    }
  }
}
