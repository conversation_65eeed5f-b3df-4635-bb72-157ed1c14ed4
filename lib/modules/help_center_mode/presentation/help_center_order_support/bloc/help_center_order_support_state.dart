import 'package:equatable/equatable.dart';

enum FormSubmissionStatus { initial, submitting, success, failure }

class HelpCenterOrderSupportState extends Equatable {
  final String? typeOfSupport;
  final String orderId;
  final String description;
  final bool hasImage;
  final bool isFormValid;
  final FormSubmissionStatus submissionStatus;
  final String? errorMessage;

  const HelpCenterOrderSupportState({
    this.typeOfSupport,
    this.orderId = '',
    this.description = '',
    this.hasImage = false,
    this.isFormValid = false,
    this.submissionStatus = FormSubmissionStatus.initial,
    this.errorMessage,
  });

  HelpCenterOrderSupportState copyWith({
    String? Function()? typeOfSupport,
    String? orderId,
    String? description,
    bool? hasImage,
    bool? isFormValid,
    FormSubmissionStatus? submissionStatus,
    String? Function()? errorMessage,
  }) {
    return HelpCenterOrderSupportState(
      typeOfSupport: typeOfSupport != null ? typeOfSupport() : this.typeOfSupport,
      orderId: orderId ?? this.orderId,
      description: description ?? this.description,
      hasImage: hasImage ?? this.hasImage,
      isFormValid: isFormValid ?? this.isFormValid,
      submissionStatus: submissionStatus ?? this.submissionStatus,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    typeOfSupport,
    orderId,
    description,
    hasImage,
    isFormValid,
    submissionStatus,
    errorMessage,
  ];
}