import 'package:flutter_bloc/flutter_bloc.dart';

import 'help_center_order_support_event.dart';
import 'help_center_order_support_state.dart';

class HelpCenterOrderSupportBloc
    extends Bloc<HelpCenterOrderSupportEvent, HelpCenterOrderSupportState> {
  HelpCenterOrderSupportBloc() : super(const HelpCenterOrderSupportState()) {
    on<TypeOfSupportChanged>(_onTypeOfSupportChanged);
    on<OrderIdChanged>(_onOrderIdChanged);
    on<DescriptionChanged>(_onDescriptionChanged);
    on<ImageUploaded>(_onImageUploaded);
    on<FormSubmitted>(_onFormSubmitted);
  }

  void _onTypeOfSupportChanged(
      TypeOfSupportChanged event, Emitter<HelpCenterOrderSupportState> emit) {
    emit(state.copyWith(
      typeOfSupport: () => event.typeOfSupport,
      isFormValid: _validateForm(
        typeOfSupport: event.typeOfSupport,
        orderId: state.orderId,
        description: state.description,
        hasImage: state.hasImage,
      ),
    ));
  }

  void _onOrderIdChanged(
      OrderIdChanged event, Emitter<HelpCenterOrderSupportState> emit) {
    emit(state.copyWith(
      orderId: event.orderId,
      isFormValid: _validateForm(
        typeOfSupport: state.typeOfSupport,
        orderId: event.orderId,
        description: state.description,
        hasImage: state.hasImage,
      ),
    ));
  }

  void _onDescriptionChanged(
      DescriptionChanged event, Emitter<HelpCenterOrderSupportState> emit) {
    emit(state.copyWith(
      description: event.description,
      isFormValid: _validateForm(
        typeOfSupport: state.typeOfSupport,
        orderId: state.orderId,
        description: event.description,
        hasImage: state.hasImage,
      ),
    ));
  }

  void _onImageUploaded(
      ImageUploaded event, Emitter<HelpCenterOrderSupportState> emit) {
    emit(state.copyWith(
      hasImage: event.hasImage,
      isFormValid: _validateForm(
        typeOfSupport: state.typeOfSupport,
        orderId: state.orderId,
        description: state.description,
        hasImage: event.hasImage,
      ),
    ));
  }

  void _onFormSubmitted(
      FormSubmitted event, Emitter<HelpCenterOrderSupportState> emit) async {
    if (!state.isFormValid) return;

    emit(state.copyWith(submissionStatus: FormSubmissionStatus.submitting));

    try {
      // TODO: Add API call to submit support request
      // Simulating API call with delay
      await Future.delayed(const Duration(seconds: 1));
      emit(state.copyWith(submissionStatus: FormSubmissionStatus.success));
    } catch (e) {
      emit(state.copyWith(
        submissionStatus: FormSubmissionStatus.failure,
        errorMessage: () => e.toString(),
      ));
    }
  }

  bool _validateForm({
    String? typeOfSupport,
    required String orderId,
    required String description,
    required bool hasImage,
  }) {
    return typeOfSupport != null &&
        orderId.isNotEmpty &&
        description.isNotEmpty &&
        hasImage;
  }
}