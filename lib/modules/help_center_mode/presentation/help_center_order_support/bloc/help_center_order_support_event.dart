import 'package:equatable/equatable.dart';

abstract class HelpCenterOrderSupportEvent extends Equatable {
  const HelpCenterOrderSupportEvent();

  @override
  List<Object?> get props => [];
}

class TypeOfSupportChanged extends HelpCenterOrderSupportEvent {
  final String typeOfSupport;

  const TypeOfSupportChanged(this.typeOfSupport);

  @override
  List<Object?> get props => [typeOfSupport];
}

class OrderIdChanged extends HelpCenterOrderSupportEvent {
  final String orderId;

  const OrderIdChanged(this.orderId);

  @override
  List<Object?> get props => [orderId];
}

class DescriptionChanged extends HelpCenterOrderSupportEvent {
  final String description;

  const DescriptionChanged(this.description);

  @override
  List<Object?> get props => [description];
}

class ImageUploaded extends HelpCenterOrderSupportEvent {
  final bool hasImage;

  const ImageUploaded(this.hasImage);

  @override
  List<Object?> get props => [hasImage];
}

class FormSubmitted extends HelpCenterOrderSupportEvent {
  const FormSubmitted();
}