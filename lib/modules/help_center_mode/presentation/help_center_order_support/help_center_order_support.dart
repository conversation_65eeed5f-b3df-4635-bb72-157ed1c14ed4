import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../widgets/app_bar/help_center_app_bar.dart';
import '../widgets/help_center_order_support/help_center_order_support_content.dart';
import '../widgets/success_dialog.dart';
import 'bloc/help_center_order_support_bloc.dart';
import 'bloc/help_center_order_support_event.dart';
import 'bloc/help_center_order_support_state.dart';

class HelpCenterOrderSupport extends StatefulWidget {
  const HelpCenterOrderSupport({super.key});

  @override
  State<HelpCenterOrderSupport> createState() => _HelpCenterOrderSupportState();
}

class _HelpCenterOrderSupportState extends State<HelpCenterOrderSupport> {
  bool isSubmitEnabled = false;

  void _onFormStateChange(bool isComplete) {
    setState(() {
      isSubmitEnabled = isComplete;
    });
  }

  final GlobalKey<FormState> formRegisterKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HelpCenterOrderSupportBloc(),
      child:
          BlocConsumer<HelpCenterOrderSupportBloc, HelpCenterOrderSupportState>(
        listenWhen: (previous, current) =>
            previous.submissionStatus != current.submissionStatus,
        listener: (context, state) {
          if (state.submissionStatus == FormSubmissionStatus.success) {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return const SuccessDialog();
              },
            );
          } else if (state.submissionStatus == FormSubmissionStatus.failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'An error occurred'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Scaffold(
              appBar: const HelpCenterAppBar(title: 'Order Support'),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Gap(24.h),
                            Form(
                              key: formRegisterKey,
                              child: HelpCenterOrderSupportContent(
                                  onFormStateChange: _onFormStateChange),
                            ),
                            Gap(100.h),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  backgroundColor:
                                      WidgetStateProperty.resolveWith<Color>(
                                    (states) {
                                      if (states
                                          .contains(WidgetState.disabled)) {
                                        return Theme.of(context)
                                            .errorLight(context);
                                      }
                                      return Theme.of(context).primary(context);
                                    },
                                  ),
                                  foregroundColor:
                                      WidgetStateProperty.all(Colors.white),
                                  padding: WidgetStateProperty.all(
                                      const EdgeInsets.symmetric(vertical: 12)),
                                  shape: WidgetStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                                onPressed: state.submissionStatus !=
                                        FormSubmissionStatus.submitting
                                    ? () {
                                        final isValid = formRegisterKey
                                                .currentState
                                                ?.validate() ??
                                            false;
                                        if (isValid) {
                                          context
                                              .read<
                                                  HelpCenterOrderSupportBloc>()
                                              .add(const FormSubmitted());
                                        }
                                      }
                                    : null,
                                child: state.submissionStatus ==
                                        FormSubmissionStatus.submitting
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Text("Report"),
                              ),
                            ),
                            Gap(40.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
