import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../widgets/app_bar/help_center_app_bar.dart';
import '../widgets/help_center_request/help_center_request_content.dart';
import '../widgets/success_dialog.dart';
import 'bloc/help_center_request_bloc.dart';
import 'bloc/help_center_request_event.dart';
import 'bloc/help_center_request_state.dart';

class HelpCenterRequest extends StatefulWidget {
  const HelpCenterRequest({super.key});

  @override
  State<HelpCenterRequest> createState() => _HelpCenterRequestState();
}

class _HelpCenterRequestState extends State<HelpCenterRequest> {
  bool isSubmitEnabled = false;

  void _onFormStateChange(bool isComplete) {
    setState(() {
      isSubmitEnabled = isComplete;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HelpCenterRequestBloc(),
      child: BlocConsumer<HelpCenterRequestBloc, HelpCenterRequestState>(
        listenWhen: (previous, current) =>
            previous.submissionStatus != current.submissionStatus,
        listener: (context, state) {
          if (state.submissionStatus == FormSubmissionStatus.success) {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return const SuccessDialog();
              },
            );
          } else if (state.submissionStatus == FormSubmissionStatus.failure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'An error occurred'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Scaffold(
              appBar: const HelpCenterAppBar(title: 'Request Support'),
              body: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Gap(24.h),
                            HelpCenterRequestContent(
                                onFormStateChange: _onFormStateChange),
                            Gap(100.h),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  backgroundColor:
                                      WidgetStateProperty.resolveWith<Color>(
                                    (states) {
                                      if (states
                                          .contains(WidgetState.disabled)) {
                                        return Theme.of(context)
                                            .errorLight(context);
                                      }
                                      return Theme.of(context).primary(context);
                                    },
                                  ),
                                  foregroundColor:
                                      WidgetStateProperty.all(Colors.white),
                                  padding: WidgetStateProperty.all(
                                      const EdgeInsets.symmetric(vertical: 12)),
                                  shape: WidgetStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                                onPressed: state.isFormValid &&
                                        state.submissionStatus !=
                                            FormSubmissionStatus.submitting
                                    ? () {
                                        context
                                            .read<HelpCenterRequestBloc>()
                                            .add(const FormSubmitted());
                                      }
                                    : null,
                                child: state.submissionStatus ==
                                        FormSubmissionStatus.submitting
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Text("Submit Request"),
                              ),
                            ),
                            Gap(40.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
