import 'package:flutter_bloc/flutter_bloc.dart';

import 'help_center_request_event.dart';
import 'help_center_request_state.dart';

class HelpCenterRequestBloc
    extends Bloc<HelpCenterRequestEvent, HelpCenterRequestState> {
  HelpCenterRequestBloc() : super(const HelpCenterRequestState()) {
    on<RequestTypeChanged>(_onRequestTypeChanged);
    on<DescriptionChanged>(_onDescriptionChanged);
    on<ImageUploaded>(_onImageUploaded);
    on<FormSubmitted>(_onFormSubmitted);
  }

  void _onRequestTypeChanged(
      RequestTypeChanged event, Emitter<HelpCenterRequestState> emit) {
    emit(state.copyWith(
      requestType: () => event.requestType,
      isFormValid: _validateForm(
        requestType: event.requestType,
        description: state.description,
        hasImage: state.hasImage,
      ),
    ));
  }

  void _onDescriptionChanged(
      DescriptionChanged event, Emitter<HelpCenterRequestState> emit) {
    emit(state.copyWith(
      description: event.description,
      isFormValid: _validateForm(
        requestType: state.requestType,
        description: event.description,
        hasImage: state.hasImage,
      ),
    ));
  }

  void _onImageUploaded(
      ImageUploaded event, Emitter<HelpCenterRequestState> emit) {
    emit(state.copyWith(
      hasImage: event.hasImage,
      isFormValid: _validateForm(
        requestType: state.requestType,
        description: state.description,
        hasImage: event.hasImage,
      ),
    ));
  }

  void _onFormSubmitted(
      FormSubmitted event, Emitter<HelpCenterRequestState> emit) async {
    if (!state.isFormValid) return;

    emit(state.copyWith(submissionStatus: FormSubmissionStatus.submitting));

    try {
      // Giả lập cuộc gọi API với độ trễ
      await Future.delayed(const Duration(seconds: 1));

      // Thành công
      emit(state.copyWith(submissionStatus: FormSubmissionStatus.success));
    } catch (e) {
      emit(state.copyWith(
        submissionStatus: FormSubmissionStatus.failure,
        errorMessage: () => e.toString(),
      ));
    }
  }

  bool _validateForm({
    String? requestType,
    required String description,
    required bool hasImage,
  }) {
    return requestType != null && description.isNotEmpty && hasImage;
  }
}