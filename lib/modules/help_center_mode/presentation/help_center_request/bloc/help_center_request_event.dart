import 'package:equatable/equatable.dart';

abstract class HelpCenterRequestEvent extends Equatable {
  const HelpCenterRequestEvent();

  @override
  List<Object?> get props => [];
}

class RequestTypeChanged extends HelpCenterRequestEvent {
  final String requestType;

  const RequestTypeChanged(this.requestType);

  @override
  List<Object?> get props => [requestType];
}

class DescriptionChanged extends HelpCenterRequestEvent {
  final String description;

  const DescriptionChanged(this.description);

  @override
  List<Object?> get props => [description];
}

class ImageUploaded extends HelpCenterRequestEvent {
  final bool hasImage;

  const ImageUploaded(this.hasImage);

  @override
  List<Object?> get props => [hasImage];
}

class FormSubmitted extends HelpCenterRequestEvent {
  const FormSubmitted();
}