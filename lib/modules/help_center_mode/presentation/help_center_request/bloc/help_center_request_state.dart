import 'package:equatable/equatable.dart';

enum FormSubmissionStatus { initial, submitting, success, failure }

class HelpCenterRequestState extends Equatable {
  final String? requestType;
  final String description;
  final bool hasImage;
  final bool isFormValid;
  final FormSubmissionStatus submissionStatus;
  final String? errorMessage;

  const HelpCenterRequestState({
    this.requestType,
    this.description = '',
    this.hasImage = false,
    this.isFormValid = false,
    this.submissionStatus = FormSubmissionStatus.initial,
    this.errorMessage,
  });

  HelpCenterRequestState copyWith({
    String? Function()? requestType,
    String? description,
    bool? hasImage,
    bool? isFormValid,
    FormSubmissionStatus? submissionStatus,
    String? Function()? errorMessage,
  }) {
    return HelpCenterRequestState(
      requestType: requestType != null ? requestType() : this.requestType,
      description: description ?? this.description,
      hasImage: hasImage ?? this.hasImage,
      isFormValid: isFormValid ?? this.isFormValid,
      submissionStatus: submissionStatus ?? this.submissionStatus,
      errorMessage: errorMessage != null ? errorMessage() : this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    requestType,
    description,
    hasImage,
    isFormValid,
    submissionStatus,
    errorMessage,
  ];
}