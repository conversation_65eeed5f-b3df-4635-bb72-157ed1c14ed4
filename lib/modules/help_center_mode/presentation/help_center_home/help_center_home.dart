import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/app_bar/help_center_app_bar.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/recommended_articles.dart';

import '../../../../app/routers/routers_name.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../widgets/help_center_home_topic/help_topic_list.dart';
import '../widgets/help_center_home_topic/notification_topic.dart';

class HelpCenterHome extends StatelessWidget {
  const HelpCenterHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HelpCenterAppBar(
        title: LocaleKeys.help_center.tr(),
        trailingIcon: SvgPicture.asset(
          AppAssets.searchSvg,
          height: 24,
          width: 24,
        ),
        onTrailingIconPressed: () {
          (context).push(RouteName.helpSearch);
        },
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(24.h),
            const NotificationTopic(),
            Gap(16.h),
            HelpTopicList(),
            Gap(16.h),
            RecommendedArticles(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primary(context),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                  ),
                  onPressed: () {
                    (context).push(RouteName.supportRequest);
                  },
                  child: Text(
                    LocaleKeys.SubmitASupportRequest.tr(),
                    style: Theme.of(context).textTheme.displayMedium!.copyWith(
                          color: Colors.white,
                        ),
                  ),
                ),
              ),
            ),
            Gap(40.h),
          ],
        ),
      ),
    );
  }
}
