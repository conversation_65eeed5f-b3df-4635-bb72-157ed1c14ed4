import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/validation_utils.dart';
import 'package:multime_app/modules/help_center_mode/presentation/help_center_order_support/bloc/help_center_order_support_bloc.dart';
import 'package:multime_app/modules/help_center_mode/presentation/help_center_order_support/bloc/help_center_order_support_event.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/custom_dropdown_button.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/upload_image.dart';

import '../../../../../core/l10n/locale_keys.g.dart';
import '../../help_center_order_support/bloc/help_center_order_support_state.dart';

class HelpCenterOrderSupportContent extends StatefulWidget {
  final Function(bool) onFormStateChange;
  const HelpCenterOrderSupportContent(
      {super.key, required this.onFormStateChange});

  @override
  State<HelpCenterOrderSupportContent> createState() =>
      _HelpCenterOrderSupportContentState();
}

class _HelpCenterOrderSupportContentState
    extends State<HelpCenterOrderSupportContent> {
  final FocusNode _orderIdFocusNode = FocusNode();
  final FocusNode _reasonFocusNode = FocusNode();

  @override
  void dispose() {
    _orderIdFocusNode.dispose();
    _reasonFocusNode.dispose();
    super.dispose();
  }

  String? validateOrderId(String? orderId) {
    return ValidateForm.validateOrderId(orderId);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child:
          BlocListener<HelpCenterOrderSupportBloc, HelpCenterOrderSupportState>(
        listenWhen: (previous, current) =>
            previous.isFormValid != current.isFormValid,
        listener: (context, state) {
          widget.onFormStateChange(state.isFormValid);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomDropDownButton(
              arr: const [
                'Request a refund',
                'Cancel order',
                'Modify order details',
                'Report product issue',
                'Check order status',
                'Request exchange/return',
                'Report delivery delay',
                'Complain about service',
                'Other support request'
              ],
              hintText: 'Choose your action',
              onChanged: (String? value) {
                if (value != null) {
                  context
                      .read<HelpCenterOrderSupportBloc>()
                      .add(TypeOfSupportChanged(value));
                }
              },
              labelText: 'Type of support request',
            ),
            Gap(24.h),
            TextFormField(
              validator: (value) => validateOrderId(value),
              onChanged: (value) {
                context
                    .read<HelpCenterOrderSupportBloc>()
                    .add(OrderIdChanged(value));
              },
              focusNode: _orderIdFocusNode,
              style: Theme.of(context)
                  .textTheme
                  .displayMedium!
                  .copyWith(fontWeight: FontWeight.w400),
              decoration: InputDecoration(
                labelText: LocaleKeys.order_id.tr(),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
                floatingLabelStyle:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                labelStyle:
                    Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                          color: Theme.of(context).textSecondary100(context),
                        ),
                contentPadding:
                    EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide:
                      BorderSide(color: Theme.of(context).lightGrey(context)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(color: Colors.black),
                ),
              ),
            ),
            Gap(24.h),
            TextField(
              onChanged: (value) {
                context
                    .read<HelpCenterOrderSupportBloc>()
                    .add(DescriptionChanged(value));
              },
              focusNode: _reasonFocusNode,
              style: Theme.of(context)
                  .textTheme
                  .displayMedium!
                  .copyWith(fontWeight: FontWeight.w400),
              minLines: 5,
              maxLines: null,
              decoration: InputDecoration(
                alignLabelWithHint: true,
                labelText: LocaleKeys.describeTheReason.tr(),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
                floatingLabelStyle:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                labelStyle:
                    Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                          color: Theme.of(context).textSecondary100(context),
                        ),
                contentPadding:
                    EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide:
                      BorderSide(color: Theme.of(context).lightGrey(context)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(color: Colors.black),
                ),
              ),
            ),
            Gap(16.h),
            Text(
              LocaleKeys.your_photo.tr(),
              style:
                  Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                        color: Theme.of(context).textSecondary(context),
                      ),
            ),
            Gap(12.h),
            UploadImage(
              onImageChange: (hasImage) {
                context
                    .read<HelpCenterOrderSupportBloc>()
                    .add(ImageUploaded(hasImage));
              },
            ),
          ],
        ),
      ),
    );
  }
}
