import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class ExpandTitleQuestion extends StatefulWidget {
  final String title;
  final Widget child;

  const ExpandTitleQuestion({
    super.key,
    required this.title,
    required this.child,
  });

  @override
  State<ExpandTitleQuestion> createState() => _ExpandTitleQuestionState();
}

class _ExpandTitleQuestionState extends State<ExpandTitleQuestion> {
  bool _isExpanded = false;

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleExpand,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  widget.title,
                  style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                ),
              ),
              Gap(10.w),
              Icon(
                _isExpanded ? Icons.remove : Icons.add,
                color: Colors.black,
              ),
            ],
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.only(top: 16),
            child: Column(
              children: [
                _isExpanded ? widget.child : Container(),
                _isExpanded ? Gap(16.h) : Container(),
              ],
            ),
          ),
          const Divider(),
          Gap(16.h),
        ],
      ),
    );
  }
}
