import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../../../core/constants/app_assets.dart';

class HelpCenterAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? trailingIcon;
  final VoidCallback? onTrailingIconPressed;

  const HelpCenterAppBar(
      {super.key,
      required this.title,
      this.trailingIcon,
      this.onTrailingIconPressed});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: IconButton(
          icon: Transform.scale(
            scale: 2.5,
            child: SvgPicture.asset(
              AppAssets.arrowLeftSvg,
            ),
          ),
          onPressed: () {
            context.pop();
          },
        ),
      ),
      title: Text(title, style: Theme.of(context).textTheme.headlineMedium),
      centerTitle: true,
      elevation: 0,
      actions: [
        if (trailingIcon != null)
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: IconButton(
              icon: trailingIcon!,
              onPressed: onTrailingIconPressed,
            ),
          ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
