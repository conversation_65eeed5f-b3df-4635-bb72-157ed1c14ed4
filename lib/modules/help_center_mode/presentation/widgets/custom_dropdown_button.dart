import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CustomDropDownButton extends StatefulWidget {
  final List<String> arr;
  final String hintText;
  final String labelText;
  final Function(String?) onChanged;

  const CustomDropDownButton({
    super.key,
    required this.arr,
    required this.hintText,
    required this.labelText,
    required this.onChanged,
  });

  @override
  State<CustomDropDownButton> createState() => _CustomDropDownButtonState();
}

class _CustomDropDownButtonState extends State<CustomDropDownButton> {
  String? _selectedText;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isDropdownVisible = false;
  final GlobalKey _dropdownKey = GlobalKey();

  void _toggleDropdown() {
    setState(() {
      _isDropdownVisible ? _removeOverlay() : _addOverlay();
      _isDropdownVisible = !_isDropdownVisible;
    });
  }

  void _addOverlay() {
    RenderBox renderBox =
        _dropdownKey.currentContext!.findRenderObject() as RenderBox;
    double parentWidth = renderBox.size.width;
    _overlayEntry = OverlayEntry(
      builder: (context) {
        double itemHeight = 50.h;
        double maxHeight = 400.h;
        double listHeight =
            (widget.arr.length * itemHeight).clamp(0, maxHeight);

        return Positioned(
          // width: MediaQuery.of(context).size.width - 32,
          width: parentWidth,
          child: CompositedTransformFollower(
            link: _layerLink,
            offset: Offset(0, 60.h),
            child: Material(
              elevation: 2,
              borderRadius: BorderRadius.circular(10),
              child: Container(
                constraints: BoxConstraints(maxHeight: listHeight),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Theme.of(context).lightGrey(context),
                    width: 1.w,
                  ),
                ),
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: widget.arr.length,
                  itemBuilder: (context, index) {
                    return _buildDropdownItem(widget.arr[index]);
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  Widget _buildDropdownItem(String item) {
    bool isSelected = _selectedText == item;
    TextStyle defaultTextStyle =
        Theme.of(context).textTheme.lightBodyLargeRegular;
    TextStyle selectedTextStyle = defaultTextStyle.copyWith(
      color: Theme.of(context).hyperlink(context),
      fontWeight: FontWeight.w600,
    );

    return GestureDetector(
      onTap: () => _selectItem(item),
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).textSecondary200(context)
              : Colors.transparent,
        ),
        child: Text(
          item,
          style: isSelected ? selectedTextStyle : defaultTextStyle,
        ),
      ),
    );
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _selectItem(String item) {
    setState(() {
      _selectedText = item;
      _removeOverlay();
      _isDropdownVisible = false;
    });
    widget.onChanged(item);
  }

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      key: _dropdownKey,
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleDropdown,
        child: InputDecorator(
          decoration: InputDecoration(
            labelText: _selectedText == null ? "" : widget.labelText,
            hintText: widget.hintText,
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            floatingLabelStyle:
                Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                      color: Theme.of(context).textSecondary(context),
                    ),
            contentPadding:
                EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                color: Theme.of(context).lightGrey(context),
                width: 1.w,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                color: Theme.of(context).lightGrey(context),
                width: 1.w,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.blue, width: 1.5.w),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _selectedText ?? widget.hintText,
                  style: (_selectedText == null)
                      ? Theme.of(context)
                          .textTheme
                          .lightBodyLargeRegular
                          .copyWith(
                            color: Theme.of(context).textSecondary100(context),
                          )
                      : Theme.of(context).textTheme.lightBodyLargeRegular,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SvgPicture.asset(
                AppAssets.arrowDownSvg,
                color: Theme.of(context).textSecondary100(context),
                width: 20.w,
                height: 20.h,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
