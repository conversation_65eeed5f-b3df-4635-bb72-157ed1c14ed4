import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/help_center_mode/presentation/help_center_request/bloc/help_center_request_bloc.dart';
import 'package:multime_app/modules/help_center_mode/presentation/help_center_request/bloc/help_center_request_event.dart';
import 'package:multime_app/modules/help_center_mode/presentation/help_center_request/bloc/help_center_request_state.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/custom_dropdown_button.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/upload_image.dart';

class HelpCenterRequestContent extends StatefulWidget {
  final Function(bool) onFormStateChange;
  const HelpCenterRequestContent({super.key, required this.onFormStateChange});

  @override
  State<HelpCenterRequestContent> createState() =>
      _HelpCenterRequestContentState();
}

class _HelpCenterRequestContentState extends State<HelpCenterRequestContent> {
  final FocusNode _descriptionFocusNode = FocusNode();

  @override
  void dispose() {
    _descriptionFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: BlocListener<HelpCenterRequestBloc, HelpCenterRequestState>(
        listenWhen: (previous, current) =>
            previous.isFormValid != current.isFormValid,
        listener: (context, state) {
          widget.onFormStateChange(state.isFormValid);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomDropDownButton(
              arr: const [
                'Is my payment information secure?',
                'How can I reset my password?',
                'How do I update my profile information?',
                'What should I do if I encounter a technical issue?',
                'Where can I find your shipping policy?'
              ],
              hintText: 'Select the type of support request',
              onChanged: (String? value) {
                if (value != null) {
                  context
                      .read<HelpCenterRequestBloc>()
                      .add(RequestTypeChanged(value));
                }
              },
              labelText: 'Request support',
            ),
            Gap(24.h),
            TextField(
              onChanged: (value) {
                context
                    .read<HelpCenterRequestBloc>()
                    .add(DescriptionChanged(value));
              },
              focusNode: _descriptionFocusNode,
              style: Theme.of(context).textTheme.displayMedium!.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
              minLines: 5,
              maxLines: null,
              decoration: InputDecoration(
                alignLabelWithHint: true,
                labelText: 'Describe',
                labelStyle: Theme.of(context)
                    .textTheme
                    .lightBodyLargeRegular
                    .copyWith(
                        color: Theme.of(context).textSecondary100(context)),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
                floatingLabelStyle:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: Theme.of(context).lightGrey(context),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(color: Colors.black),
                ),
              ),
            ),
            Gap(16.h),
            Text('Your photo',
                style: Theme.of(context).textTheme.lightBodyMediumSemiBold),
            Gap(12.h),
            UploadImage(
              onImageChange: (hasImage) {
                context
                    .read<HelpCenterRequestBloc>()
                    .add(ImageUploaded(hasImage));
              },
            )
          ],
        ),
      ),
    );
  }
}
