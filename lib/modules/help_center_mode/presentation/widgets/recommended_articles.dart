import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'expand_title_question.dart';

class RecommendedArticles extends StatelessWidget {
  RecommendedArticles({super.key});

  final List<Map<String, dynamic>> articles = [
    {
      "title": LocaleKeys.my_account_from_being_hacked.tr(),
      "content": [
        LocaleKeys.how_to_protect_my_account_from_being_hacked.tr(),
      ],
    },
    {
      "title": LocaleKeys.get_notified_about_suspicious_activity.tr(),
      "content": [
        LocaleKeys.how_to_get_notified_about_suspicious_activity.tr(),
      ]
    },
    {
      "title": LocaleKeys.report_or_block_posts_i_dont_like.tr(),
      "content": [
        LocaleKeys.how_to_report_or_block_posts_i_dont_like.tr(),
        LocaleKeys.follow_these_steps.tr(),
        LocaleKeys.how_to_block_or_report_a_post_1.tr(),
        LocaleKeys.how_to_block_or_report_a_post_2.tr(),
      ]
    },
    {
      "title": LocaleKeys.how_to_like_and_react_to_posts.tr(),
      "content": [
        LocaleKeys.When_you_click_Like.tr(),
        LocaleKeys.When_you_like_something.tr(),
        LocaleKeys.How_to_like_something.tr(),
        LocaleKeys.Go_to_the_post.tr(),
        LocaleKeys.ClickLike.tr(),
        LocaleKeys.How_to_unlike_something.tr(),
        LocaleKeys.post_or_photo.tr(),
        LocaleKeys.ClickUnlike.tr()
      ]
    },
    {
      "title": LocaleKeys.how_to_send_a_message.tr(),
      "content": [LocaleKeys.only_send_messages.tr()]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.RecommendedArticles.tr(),
            style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                  color: Theme.of(context).textSecondary(context),
                ),
          ),
          Gap(20.h),
          ...articles.map((article) => ExpandTitleQuestion(
                title: article["title"]!,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var item in article["content"]!)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!_isSubContent(item)) ...[
                              const Text("•"),
                              Gap(8.w),
                            ] else ...[
                              Gap(20.w),
                              Text(item.substring(0, 2),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular),
                              Gap(8.w),
                            ],
                            Expanded(
                              child: Text(
                                _isSubContent(item) ? item.substring(3) : item,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Gap(10.h),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  bool _isSubContent(String text) {
    return RegExp(r'^\d+\.\s').hasMatch(text);
  }
}
