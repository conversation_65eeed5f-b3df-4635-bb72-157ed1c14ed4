import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';

class NotificationTopic extends StatelessWidget {
  const NotificationTopic({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.help_center_greeting.tr(),
                  style: Theme.of(context).textTheme.lightBodyXLargeBold,
                ),
                Gap(12.h),
                Text(LocaleKeys.help_center_description.tr(),
                    style: Theme.of(context).textTheme.lightBodyMediumRegular)
              ],
            ),
          ),
          Gap(16.w),
          SvgPicture.asset(AppAssets.helpCenter, width: 100.w, height: 100.h),
        ],
      ),
    );
  }
}
