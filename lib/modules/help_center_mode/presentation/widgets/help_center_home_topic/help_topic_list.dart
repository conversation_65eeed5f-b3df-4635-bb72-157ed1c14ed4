import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/help_center_home_topic/topic_card.dart';

import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';

class HelpTopic {
  final String svgPicture;
  final String title;
  final void Function(BuildContext)? onTap;

  HelpTopic({required this.svgPicture, required this.title, this.onTap});
}

class HelpTopicList extends StatelessWidget {
  HelpTopicList({super.key});

  final List<HelpTopic> topics = [
    HelpTopic(
      svgPicture: AppAssets.account,
      title: LocaleKeys.account.tr(),
      onTap: (context) {
        (context).push(RouteName.helpCenterQuestion, extra: 0);
      },
    ),
    HelpTopic(
      svgPicture: AppAssets.tickShield,
      title: LocaleKeys.privacy_and_security.tr(),
      onTap: (context) {
        (context).push(RouteName.helpCenterQuestion, extra: 1);
      },
    ),
    HelpTopic(
      svgPicture: AppAssets.market,
      title: LocaleKeys.marketplace.tr(),
      onTap: (context) {
        (context).push(RouteName.helpCenterQuestion, extra: 2);
      },
    ),
    HelpTopic(
      svgPicture: AppAssets.social,
      title: LocaleKeys.social.tr(),
      onTap: (context) {
        (context).push(RouteName.helpCenterQuestion, extra: 3);
      },
    ),
    HelpTopic(
      svgPicture: AppAssets.business_icon,
      title: LocaleKeys.business_mode.tr(),
      onTap: (context) {
        (context).push(RouteName.helpCenterQuestion, extra: 4);
      },
    ),
    HelpTopic(
      svgPicture: AppAssets.datingSvg,
      title: LocaleKeys.dating_mode.tr(),
      onTap: (context) {
        (context).push(RouteName.helpCenterQuestion, extra: 5);
      },
    ),
    HelpTopic(
      svgPicture: AppAssets.news,
      title: LocaleKeys.news.tr(),
      onTap: (context) {
        (context).push(RouteName.helpCenterQuestion, extra: 6);
      },
    ),
    HelpTopic(
      svgPicture: AppAssets.orderSupport,
      title: LocaleKeys.order_support.tr(),
      onTap: (context) {
        (context).push(RouteName.orderSupport);
      },
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.help_topics.tr(),
            style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                  color: Theme.of(context).textSecondary(context),
                ),
          ),
          Gap(12.h),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisExtent: 100,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: topics.length,
              itemBuilder: (context, index) {
                return TopicCard(
                  svgPicture: topics[index].svgPicture,
                  title: topics[index].title,
                  onTap: topics[index].onTap,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
