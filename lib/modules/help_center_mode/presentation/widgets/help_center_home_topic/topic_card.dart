import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/themes/app_colors.dart';

class TopicCard extends StatelessWidget {
  final String svgPicture;
  final String title;
  final void Function(BuildContext)? onTap;

  const TopicCard(
      {super.key, required this.svgPicture, required this.title, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap != null
          ? () {
              onTap!(context);
            }
          : null,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).lightGrey(context),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            SvgPicture.asset(
              svgPicture,
              height: 38.h,
              width: 38.w,
            ),
            Gap(10.h),
            Text(title,
                style: Theme.of(context).textTheme.lightBodySmallSemiBold),
          ],
        ),
      ),
    );
  }
}
