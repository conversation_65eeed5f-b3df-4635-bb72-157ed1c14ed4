import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class SuccessDialog extends StatelessWidget {
  const SuccessDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      backgroundColor: Colors.white,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              LocaleKeys.SUCCESS.tr(),
              style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                    color: Theme.of(context).primary(context),
                  ),
            ),
            Gap(10.h),
            Text(
              LocaleKeys.Thank_you_for_your_request.tr(),
              style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color: Theme.of(context).secondaryDark(context),
                  ),
              textAlign: TextAlign.center,
            ),
            Gap(10.h),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primary(context),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 40),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
              ),
              onPressed: () {
                context.pop();
              },
              child: Text(LocaleKeys.continueButton.tr()),
            ),
          ],
        ),
      ),
    );
  }
}
