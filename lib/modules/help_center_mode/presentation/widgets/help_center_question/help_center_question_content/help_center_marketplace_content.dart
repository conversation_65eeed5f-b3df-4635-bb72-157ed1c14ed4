import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../expand_title_question.dart';

class HelpCenterMarketplaceContent extends StatelessWidget {
  HelpCenterMarketplaceContent({super.key});

  final List<Map<String, dynamic>> articles = [
    {
      "title": "How can I cancel items and orders?",
      "content": [
        "You can cancel physical items or orders that haven't entered the shipping process yet.",
        "To cancel an order that has not entered the shipping process, follow these steps:",
        "1. Go to Your Orders. Find the order that you want to cancel and select View or edit order.",
        "2. Select Cancel items, Check box of the item that you want to cancel from the order. To cancel the entire order, select all the items.",
        "3. Choose a reason for the cancellation and select Request cancellation."
      ]
    },
    {
      "title": "How can I see what's in my cart?",
      "content": [
        "You can see what's in your cart by clicking on the order icon in the navigation bar."
      ]
    },
    {
      "title": "Where can I check my order status?",
      "content": [
        "You can see all your order and their status by clicking on the order icon in the navigation bar."
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(20.h),
          ...articles.map((article) => ExpandTitleQuestion(
                title: article["title"]!,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var item in article["content"]!)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!_isSubContent(item)) ...[
                              const Text("•"),
                              Gap(8.w),
                            ] else ...[
                              Gap(20.w),
                              Text(item.substring(0, 2),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular),
                              Gap(8.w),
                            ],
                            Expanded(
                              child: Text(
                                _isSubContent(item) ? item.substring(3) : item,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Gap(10.h),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  bool _isSubContent(String text) {
    return RegExp(r'^\d+\.\s').hasMatch(text);
  }
}
