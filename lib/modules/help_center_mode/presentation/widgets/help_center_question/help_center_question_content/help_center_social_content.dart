import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../expand_title_question.dart';

class HelpCenterSocialContent extends StatelessWidget {
  HelpCenterSocialContent({super.key});

  final List<Map<String, dynamic>> articles = [
    {
      "title": "How do I report or block posts I don't like?",
      "content": [
        "You can block or report content you don't like. This lets us know you don't want to see more posts like this.",
        "To block or report the post, follow these steps:",
        "1. Click on the icon on the right side of the post.",
        "2. Then click block or report and confirm again."
      ]
    },
    {
      "title": "Disable incognito mode",
      "content": [
        "You can post anonymously. By clicking the disable or enable incognito mode below the post button."
      ],
    },
    {
      "title": "How to “Like” and “React” to posts?",
      "content": [
        "When you click Like below a post, you tell people you like it without commenting. Like comments, anyone who can see the post can see that you liked it.",
        "When you like something, it tells us what other things you want to see.",
        "How to like something?",
        "1. Go to the post or photo.",
        "2. Click Like.",
        "How to unlike something? You can only unlike posts, photos, and comments that you previously liked.",
        "1. Go to the post or photo.",
        "2. Click Unlike."
      ],
    },
    {
      "title": "Posting by voice",
      "content": [
        "You can post by voice by tapping the icon",
        "Note: You can only post one recording up to 50MB in size."
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(20.h),
          ...articles.map((article) => ExpandTitleQuestion(
                title: article["title"]!,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var item in article["content"]!)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!_isSubContent(item)) ...[
                              const Text("•"),
                              Gap(8.w),
                            ] else ...[
                              Gap(20.w),
                              Text(item.substring(0, 2),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular),
                              Gap(8.w),
                            ],
                            Expanded(
                              child: Text(
                                _isSubContent(item) ? item.substring(3) : item,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Gap(10.h),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  bool _isSubContent(String text) {
    return RegExp(r'^\d+\.\s').hasMatch(text);
  }
}
