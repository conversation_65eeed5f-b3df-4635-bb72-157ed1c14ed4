import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../expand_title_question.dart';

class HelpCenterBusinessContent extends StatelessWidget {
  HelpCenterBusinessContent({super.key});

  final List<Map<String, dynamic>> articles = [
    {
      "title": "How does business mode work?",
      "content": [
        "Business mode is a job platform where offers and requesters meet.",
        "Requesters have the ability to hire offers on the platform. Offers have the ability to offer their services to requesters on the platform."
      ],
    },
    {
      "title": "How do I protect my account from being hacked?",
      "content": [
        "To protect your account, use a strong password, don't share your password with others, and enable two-factor authentication."
      ]
    },
    {
      "title": "How can I see what's in my cart?",
      "content": [
        "You can see what's in your cart by clicking on the order icon in the navigation bar."
      ]
    },
    {
      "title": "How do I create a new account?",
      "content": [
        "You can create a new account by clicking the “Register” button on the login screen and filling in the required information such as email, password, and other personal information."
      ]
    },
    {
      "title": "How to report or block another user?",
      "content": [
        "You can report and block another user by clicking to icon on the top right of the profile."
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(20.h),
          ...articles.map((article) => ExpandTitleQuestion(
                title: article["title"]!,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var item in article["content"]!)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!_isSubContent(item)) ...[
                              const Text("•"),
                              Gap(8.w),
                            ] else ...[
                              Gap(20.w),
                              Text(item.substring(0, 2),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular),
                              Gap(8.w),
                            ],
                            Expanded(
                              child: Text(
                                _isSubContent(item) ? item.substring(3) : item,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Gap(10.h),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  bool _isSubContent(String text) {
    return RegExp(r'^\d+\.\s').hasMatch(text);
  }
}
