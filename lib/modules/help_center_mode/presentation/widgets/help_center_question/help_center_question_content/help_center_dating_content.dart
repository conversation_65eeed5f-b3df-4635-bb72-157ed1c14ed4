import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../expand_title_question.dart';

class HelpCenterDatingContent extends StatelessWidget {
  HelpCenterDatingContent({super.key});

  final List<Map<String, dynamic>> articles = [
    {
      "title": "How does Dating Mode work?",
      "content": [
        "Dating mode connects you with profiles using location-based technology according to the gender, distance, and direction filters you set."
      ]
    },
    {
      "title": "How to update personal profile?",
      "content": [
        "You can update your profile in Setting Dating Mode, then select Edit Profile Dating Mode and edit as desired."
      ]
    },
    {
      "title": "How to find and match with others?",
      "content": [
        "\"A Match\" is only formed when both members use the right swipe feature to show interest in each other."
      ]
    },
    {
      "title": "How to send a message?",
      "content": [
        "You can only send messages when both members use the right swipe feature to show interest in each other. It’s a match!."
      ]
    },
    {
      "title": "How to report or block another user?",
      "content": [
        "You can report and block another user by clicking to icon on the top right of the profile."
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(20.h),
          ...articles.map((article) => ExpandTitleQuestion(
                title: article["title"]!,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var item in article["content"]!)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!_isSubContent(item)) ...[
                              const Text("•"),
                              Gap(8.w),
                            ] else ...[
                              Gap(20.w),
                              Text(item.substring(0, 2),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular),
                              Gap(8.w),
                            ],
                            Expanded(
                              child: Text(
                                _isSubContent(item) ? item.substring(3) : item,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Gap(10.h),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  bool _isSubContent(String text) {
    return RegExp(r'^\d+\.\s').hasMatch(text);
  }
}
