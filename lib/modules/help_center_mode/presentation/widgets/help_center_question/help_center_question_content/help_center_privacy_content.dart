import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../expand_title_question.dart';

class HelpCenterPrivacyContent extends StatelessWidget {
  HelpCenterPrivacyContent({super.key});

  final List<Map<String, dynamic>> articles = [
    {
      "title": "How do I update my privacy setting?",
      "content": [
        "Go to the “Privacy” section of your account settings to update your privacy settings."
      ],
    },
    {
      "title": "How do I protect my account from being hacked?",
      "content": [
        "To protect your account, use a strong password, don't share your password with others, and enable two-factor authentication."
      ]
    },
    {
      "title": "How do I protect my payment information?",
      "content": [
        "Ensure you only enter your payment information on trusted websites or apps and use secure payment methods."
      ]
    },
    {
      "title": "How to enable two-factor authentication?",
      "content": [
        "You can enable two-factor authentication in the “Account Settings” section of your “Settings” app. Follow the instructions to set it up."
      ]
    },
    {
      "title": "How do I get notified about suspicious activity on my account?",
      "content": [
        "You can set up notifications about suspicious activity in the “Security” section of your account settings."
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(20.h),
          ...articles.map((article) => ExpandTitleQuestion(
                title: article["title"]!,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var item in article["content"]!)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!_isSubContent(item)) ...[
                              const Text("•"),
                              Gap(8.w),
                            ] else ...[
                              Gap(20.w),
                              Text(item.substring(0, 2),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular),
                              Gap(8.w),
                            ],
                            Expanded(
                              child: Text(
                                _isSubContent(item) ? item.substring(3) : item,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  bool _isSubContent(String text) {
    return RegExp(r'^\d+\.\s').hasMatch(text);
  }
}
