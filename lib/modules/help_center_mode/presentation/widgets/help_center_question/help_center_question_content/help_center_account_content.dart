import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../expand_title_question.dart';

class HelpCenterAccountContent extends StatelessWidget {
  HelpCenterAccountContent({super.key});

  final List<Map<String, dynamic>> articles = [
    {
      "title": "How do I create a new account?",
      "content": [
        "You can create a new account by clicking the “Register” button on the login screen and filling in the required information such as email, password, and other personal information."
      ],
    },
    {
      "title": "How to change password?",
      "content": [
        "To change your password, go to “Settings” and select “Account Settings”, then select “Change Password” and follow the instructions."
      ],
    },
    {
      "title": "I forgot my password, how do I recover it?",
      "content": [
        "If you forget your password, click the “Forgot Password” link on the login screen and enter your email to receive password recovery instructions."
      ],
    },
    {
      "title":
          "How to update personal information (email, phone number, address)?",
      "content": [
        "You can update your personal information by going to \"Settings\", then selecting “Account Settings” and editing the necessary information."
      ],
    },
    {
      "title": "How to enable two-factor authentication?",
      "content": [
        "You can enable two-factor authentication in the “Account Settings” section of your “Settings” app. Follow the instructions to set it up."
      ],
    },
    {
      "title": "How to manage payment methods?",
      "content": [
        "You can manage your payment methods in the “Wallet” section of the app’s “Settings”."
      ]
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(20.h),
          ...articles.map((article) => ExpandTitleQuestion(
                title: article["title"]!,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var item in article["content"]!)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!_isSubContent(item)) ...[
                              const Text("•"),
                              Gap(8.w),
                            ] else ...[
                              Gap(20.w),
                              Text(item.substring(0, 2),
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular),
                              Gap(8.w),
                            ],
                            Expanded(
                              child: Text(
                                _isSubContent(item) ? item.substring(3) : item,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular,
                                softWrap: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  bool _isSubContent(String text) {
    return RegExp(r'^\d+\.\s').hasMatch(text);
  }
}
