import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/themes/app_colors.dart';

class HelpCenterQuestionCard extends StatelessWidget {
  final String title;
  final String svgPicture;
  final bool isSelected;
  final VoidCallback onTap;

  const HelpCenterQuestionCard(
      {super.key,
      required this.title,
      required this.isSelected,
      required this.onTap,
      required this.svgPicture});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).primary(context)
                : Theme.of(context).greyScale400(context),
            width: 1.w,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            SvgPicture.asset(
              svgPicture,
              height: 24.h,
              width: 24.w,
            ),
            Gap(10.h),
            Text(title,
                style: Theme.of(context).textTheme.lightBodyMediumSemiBold),
          ],
        ),
      ),
    );
  }
}
