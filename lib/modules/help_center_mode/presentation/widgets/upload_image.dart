import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../core/l10n/locale_keys.g.dart';

class UploadImage extends StatefulWidget {
  final Function(bool) onImageChange;
  const UploadImage({super.key, required this.onImageChange});

  @override
  State<UploadImage> createState() => _UploadImageState();
}

class _UploadImageState extends State<UploadImage> {
  bool isUploaded = false;
  List<File?> imageFiles = [null, null, null];

  final ImagePicker _picker = ImagePicker();

  Future<void> selectImage(int index) async {
    final XFile? pickedFile = await _picker.pickImage(
      source: ImageSource.gallery,
      maxHeight: 1024,
      maxWidth: 1024,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      setState(() {
        imageFiles[index] = File(pickedFile.path);
        isUploaded = true;
        widget.onImageChange(true);
      });
    }
  }

  void removeImage(int index) {
    setState(() {
      imageFiles[index] = null;
      if (imageFiles.every((file) => file == null)) {
        isUploaded = false;
        widget.onImageChange(false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return isUploaded
        ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(3, (index) {
              return GestureDetector(
                onTap: () {
                  if (imageFiles[index] == null) {
                    selectImage(index);
                  }
                },
                child: Container(
                  width: MediaQuery.of(context).size.width / 3.5,
                  height: 150.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: imageFiles[index] != null
                      ? Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.file(
                                imageFiles[index]!,
                                fit: BoxFit.cover,
                                width: 114.w,
                                height: 144.h,
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: GestureDetector(
                                onTap: () => removeImage(index),
                                child: Container(
                                    padding: const EdgeInsets.all(2),
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child:
                                        SvgPicture.asset(AppAssets.trashSvg)),
                              ),
                            ),
                          ],
                        )
                      : Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: Theme.of(context).greyScale100(context),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.add,
                              color: Theme.of(context).backgroundRed(context),
                            ),
                          ),
                        ),
                ),
              );
            }),
          )
        : GestureDetector(
            onTap: () {
              selectImage(0);
            },
            child: DottedBorder(
              borderType: BorderType.RRect,
              radius: const Radius.circular(10),
              dashPattern: const [5, 3],
              color: Theme.of(context).textSecondary(context),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      Gap(18.h),
                      SvgPicture.asset(
                        AppAssets.uploadImageSvg,
                      ),
                      Gap(8.h),
                      Text(
                        LocaleKeys.Click_here_to_upload_file.tr(),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Gap(8.h),
                      Text.rich(
                        TextSpan(
                          text: LocaleKeys.Supports_image_formats.tr(),
                          style: Theme.of(context)
                              .textTheme
                              .labelSmall!
                              .copyWith(
                                color: Theme.of(context).textSecondary(context),
                              ),
                          children: [
                            TextSpan(
                              text: "10MB",
                              style: TextStyle(
                                color: Theme.of(context).secondary(context),
                              ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      Gap(18.h),
                    ],
                  ),
                ),
              ),
            ),
          );
  }
}
