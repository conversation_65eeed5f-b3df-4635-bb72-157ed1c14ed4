import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';
import 'bloc/help_center_search_bloc.dart';
import 'bloc/help_center_search_event.dart';
import 'bloc/help_center_search_state.dart';

class HelpCenterSearch extends StatelessWidget {
  const HelpCenterSearch({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => HelpCenterSearchBloc(),
      child: const HelpCenterSearchView(),
    );
  }
}

class HelpCenterSearchView extends StatefulWidget {
  const HelpCenterSearchView({super.key});

  @override
  State<HelpCenterSearchView> createState() => _HelpCenterSearchViewState();
}

class _HelpCenterSearchViewState extends State<HelpCenterSearchView> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();

    final state = context.read<HelpCenterSearchBloc>().state;
    _controller.text = state.query;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: SvgPicture.asset(
              AppAssets.arrowLeftSvg,
              width: 24,
              height: 24,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          title: BlocBuilder<HelpCenterSearchBloc, HelpCenterSearchState>(
            builder: (context, state) {
              return TextField(
                controller: _controller,
                decoration: InputDecoration(
                  hintText: LocaleKeys.searchContactsHint.tr(),
                  hintStyle: Theme.of(context)
                      .textTheme
                      .lightBodyLargeRegular
                      .copyWith(
                        color: Theme.of(context).greyScale400(context),
                      ),
                  prefixIcon: state.showIcon
                      ? Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: ColorFiltered(
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).greyScale600(context),
                              BlendMode.srcIn,
                            ),
                            child: SvgPicture.asset(
                              AppAssets.searchSvg,
                              width: 20,
                              height: 20,
                            ),
                          ))
                      : null,
                  suffixIcon: state.query.isNotEmpty
                      ? IconButton(
                          icon: SvgPicture.asset(AppAssets.closeCircle),
                          onPressed: () {
                            _controller.clear();
                            context
                                .read<HelpCenterSearchBloc>()
                                .add(ClearSearch());
                          },
                        )
                      : null,
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: Theme.of(context).greyScale600(context)),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: Theme.of(context).greyScale600(context)),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                style: Theme.of(context).textTheme.lightBodyLargeRegular,
                onChanged: (text) {
                  context
                      .read<HelpCenterSearchBloc>()
                      .add(SearchQueryChanged(text));
                },
              );
            },
          ),
        ),
        body: BlocBuilder<HelpCenterSearchBloc, HelpCenterSearchState>(
          builder: (context, state) {
            return Center(
              child: state.noSearchResults
                  ? _buildNoResults()
                  : _buildSearchPrompt(),
            );
          },
        ),
      ),
    );
  }

  Widget _buildNoResults() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 45),
      child: Column(
        children: [
          Gap(32.h),
          SvgPicture.asset(
            AppAssets.noSearch,
            width: 120,
            height: 120,
          ),
          Gap(32.h),
          Text(
            LocaleKeys.no_search_results.tr(),
            style: Theme.of(context).textTheme.lightBodyLargeRegular,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchPrompt() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 68),
      child: Column(
        children: [
          Gap(32.h),
          SvgPicture.asset(
            AppAssets.marketplaceSearch,
            width: 120,
            height: 120,
          ),
          Gap(32.h),
          Text(
            LocaleKeys.help_center_search_prompt.tr(),
            style: Theme.of(context).textTheme.lightBodyLargeRegular,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
