import 'package:equatable/equatable.dart';

class HelpCenterSearchState extends Equatable {
  final String query;
  final bool showIcon;
  final bool noSearchResults;

  const HelpCenterSearchState({
    this.query = '',
    this.showIcon = true,
    this.noSearchResults = false,
  });

  HelpCenterSearchState copyWith({
    String? query,
    bool? showIcon,
    bool? noSearchResults,
  }) {
    return HelpCenterSearchState(
      query: query ?? this.query,
      showIcon: showIcon ?? this.showIcon,
      noSearchResults: noSearchResults ?? this.noSearchResults,
    );
  }

  @override
  List<Object> get props => [query, showIcon, noSearchResults];
}
