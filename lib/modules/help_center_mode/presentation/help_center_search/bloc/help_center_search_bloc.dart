import 'package:flutter_bloc/flutter_bloc.dart';
import 'help_center_search_event.dart';
import 'help_center_search_state.dart';

class HelpCenterSearchBloc
    extends Bloc<HelpCenterSearchEvent, HelpCenterSearchState> {
  HelpCenterSearchBloc() : super(const HelpCenterSearchState()) {
    on<SearchQueryChanged>((event, emit) {
      emit(state.copyWith(
        query: event.query,
        showIcon: event.query.isEmpty,
        noSearchResults: event.query.isNotEmpty && event.query.length > 2,
      ));
    });

    on<ClearSearch>((event, emit) {
      emit(const HelpCenterSearchState());
    });
  }
}
