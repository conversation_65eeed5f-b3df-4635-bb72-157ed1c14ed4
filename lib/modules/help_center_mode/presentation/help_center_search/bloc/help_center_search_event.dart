import 'package:equatable/equatable.dart';

abstract class HelpCenterSearchEvent extends Equatable {
  const HelpCenterSearchEvent();
  @override
  List<Object> get props => [];
}

class SearchQueryChanged extends HelpCenterSearchEvent {
  final String query;
  const SearchQueryChanged(this.query);

  @override
  List<Object> get props => [query];
}

class ClearSearch extends HelpCenterSearchEvent {}
