import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/help_center_question/help_center_question_content/help_center_account_content.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/help_center_question/help_center_question_content/help_center_privacy_content.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/help_center_question/help_center_question_content/help_center_social_content.dart';

import '../../../../app/routers/routers_name.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../widgets/app_bar/help_center_app_bar.dart';
import '../widgets/help_center_question/help_center_question_card.dart';
import '../widgets/help_center_question/help_center_question_content/help_center_business_content.dart';
import '../widgets/help_center_question/help_center_question_content/help_center_dating_content.dart';
import '../widgets/help_center_question/help_center_question_content/help_center_marketplace_content.dart';
import '../widgets/help_center_question/help_center_question_content/help_center_news_content.dart';

class HelpCenterQuestion extends StatefulWidget {
  final int? selectedIndex;

  const HelpCenterQuestion({super.key, this.selectedIndex});

  @override
  State<HelpCenterQuestion> createState() => _HelpCenterQuestionState();
}

class _HelpCenterQuestionState extends State<HelpCenterQuestion> {
  int? selectedIndex;
  final ScrollController _scrollController = ScrollController();
  final ScrollController _listViewController = ScrollController();

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.selectedIndex;
    if (selectedIndex != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToSelectedCard(selectedIndex!);
      });
    }
  }

  final List<Map<String, String>> questions = [
    {'title': LocaleKeys.account.tr(), 'icon': AppAssets.account},
    {
      'title': LocaleKeys.privacy_and_security.tr(),
      'icon': AppAssets.tickShield
    },
    {'title': LocaleKeys.marketplace.tr(), 'icon': AppAssets.market},
    {'title': LocaleKeys.social.tr(), 'icon': AppAssets.social},
    {'title': LocaleKeys.business_mode.tr(), 'icon': AppAssets.business_icon},
    {'title': LocaleKeys.dating_mode.tr(), 'icon': AppAssets.datingSvg},
    {'title': LocaleKeys.news.tr(), 'icon': AppAssets.news},
  ];

  final List<Widget> contentWidgets = [
    HelpCenterAccountContent(),
    HelpCenterPrivacyContent(),
    HelpCenterMarketplaceContent(),
    HelpCenterSocialContent(),
    HelpCenterBusinessContent(),
    HelpCenterDatingContent(),
    HelpCenterNewsContent(),
  ];

  void _onQuestionSelected(int index) {
    setState(() {
      selectedIndex = index;
    });
    _scrollToSelectedCard(index);
  }

  void _scrollToSelectedCard(int index) {
    _listViewController.animateTo(
      index * (MediaQuery.of(context).size.width * 0.4 + 8.w),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HelpCenterAppBar(
        title: LocaleKeys.help_center.tr(),
        trailingIcon: SvgPicture.asset(
          AppAssets.searchSvg,
          height: 24,
          width: 24,
        ),
        onTrailingIconPressed: () {
          (context).push(RouteName.helpSearch);
        },
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(24.h),
              SizedBox(
                height: 80.h,
                child: ListView.builder(
                  controller: _listViewController,
                  scrollDirection: Axis.horizontal,
                  itemCount: questions.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(right: 8.w),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * 0.4,
                        child: HelpCenterQuestionCard(
                          title: questions[index]['title']!,
                          svgPicture: questions[index]['icon']!,
                          isSelected: selectedIndex == index,
                          onTap: () => _onQuestionSelected(index),
                        ),
                      ),
                    );
                  },
                ),
              ),
              Gap(16.h),
              Text(LocaleKeys.related_questions.tr(),
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).textSecondary(context),
                      )),
              if (selectedIndex != null) contentWidgets[selectedIndex!],
              Gap(40.h),
            ],
          ),
        ),
      ),
      floatingActionButton: SizedBox(
        width: 36,
        height: 36,
        child: FloatingActionButton(
          onPressed: () {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
          backgroundColor: Theme.of(context).primary(context),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          child: const Icon(
            Icons.arrow_upward,
            size: 20,
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }
}
