import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/utils/enum.dart';
import 'package:multime_app/modules/notification_mode/presentation/product_page/product_notification_page.dart';
import 'package:multime_app/modules/notification_mode/presentation/service_page/service_notification_page.dart';
import 'bloc/notification_bloc.dart';
import 'multi_me_page/multi_me_notification_page.dart';

class NotificationPage extends StatelessWidget {
  const NotificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Notification'),
          centerTitle: true,
        ),
        body: Column(
          children: [
            TabBar(
              onTap: (index) {
                final tabType = TabNotificationType.values[index];
                debugPrint('tabType: $tabType');
                context
                    .read<NotificationBloc>()
                    .add(TabChangedNotificationEvent(tabType));
              },
              tabs: const [
                Tab(text: "Multi.me"),
                Tab(text: "Service"),
                Tab(text: "Product"),
              ],
            ),
            const Expanded(
              child: TabBarView(
                children: [
                  MultiMeNotificationPage(),
                  ServiceNotificationPage(),
                  ProductNotificationPage(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
