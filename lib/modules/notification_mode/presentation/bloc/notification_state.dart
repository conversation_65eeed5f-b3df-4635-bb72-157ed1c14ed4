part of 'notification_bloc.dart';

class NotificationState extends Equatable {
  final TabNotificationType activeTab;
  final bool hasMoreMultipleNotification;
  final bool hasMoreServiceNotification;
  final bool hasMoreProductNotification;
  final List<MultiMeNotificationModel> listMultiMeNotification;
  final List<ServiceNotificationModel> listServiceNotification;
  final List<ProductNotificationModel> listProductNotification;
  final bool isRefreshing;
  final bool isLoading;
  final String? errorMessage;
  final int currentPage;
  final int totalNotifications;

  const NotificationState({
    required this.activeTab,
    required this.hasMoreMultipleNotification,
    required this.hasMoreServiceNotification,
    required this.hasMoreProductNotification,
    required this.listMultiMeNotification,
    required this.listServiceNotification,
    required this.listProductNotification,
    required this.isRefreshing,
    required this.isLoading,
    this.errorMessage,
    required this.currentPage,
    required this.totalNotifications,
  });

  factory NotificationState.initial() {
    return const NotificationState(
      activeTab: TabNotificationType.tabMultiMeNotification,
      hasMoreMultipleNotification: true,
      hasMoreServiceNotification: true,
      hasMoreProductNotification: true,
      listMultiMeNotification: [],
      listServiceNotification: [],
      listProductNotification: [],
      isRefreshing: false,
      isLoading: false,
      currentPage: 1,
      totalNotifications: 0,
    );
  }

  NotificationState copyWith(
      {TabNotificationType? activeTab,
      bool? hasMoreMultipleNotification,
      bool? hasMoreServiceNotification,
      bool? hasMoreProductNotification,
      bool? isRefreshing,
      bool? isLoading,
      String? errorMessage,
      int? currentPage,
      int? totalNotifications,
      List<MultiMeNotificationModel>? listMultiMeNotification,
      List<ServiceNotificationModel>? listServiceNotification,
      List<ProductNotificationModel>? listProductNotification}) {
    return NotificationState(
        activeTab: activeTab ?? this.activeTab,
        hasMoreMultipleNotification:
            hasMoreMultipleNotification ?? this.hasMoreMultipleNotification,
        hasMoreServiceNotification:
            hasMoreServiceNotification ?? this.hasMoreServiceNotification,
        hasMoreProductNotification:
            hasMoreProductNotification ?? this.hasMoreProductNotification,
        isRefreshing: isRefreshing ?? this.isRefreshing,
        isLoading: isLoading ?? this.isLoading,
        errorMessage: errorMessage ?? this.errorMessage,
        currentPage: currentPage ?? this.currentPage,
        totalNotifications: totalNotifications ?? this.totalNotifications,
        listMultiMeNotification:
            listMultiMeNotification ?? this.listMultiMeNotification,
        listServiceNotification:
            listServiceNotification ?? this.listServiceNotification,
        listProductNotification:
            listProductNotification ?? this.listProductNotification);
  }

  @override
  List<Object?> get props => [
        activeTab,
        isRefreshing,
        isLoading,
        errorMessage,
        currentPage,
        totalNotifications,
        hasMoreMultipleNotification,
        hasMoreProductNotification,
        hasMoreServiceNotification,
        listMultiMeNotification,
        listServiceNotification,
        listProductNotification
      ];
}
