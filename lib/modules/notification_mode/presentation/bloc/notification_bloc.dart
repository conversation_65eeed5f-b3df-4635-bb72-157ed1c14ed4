import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/utils/enum.dart';
import 'package:multime_app/modules/notification_mode/data/models/multi_me_notification_model.dart';
import 'package:multime_app/modules/notification_mode/data/models/product_notification_model.dart';
import 'package:multime_app/modules/notification_mode/data/repositories/notification_repository.dart';
import '../../data/models/service_notification_model.dart';

part 'notification_event.dart';
part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationRepository notificationRepository;

  NotificationBloc({required this.notificationRepository})
      : super(NotificationState.initial()) {
    on<LoadNotificationHistoryEvent>(_onLoadNotificationHistory);
    on<TabChangedNotificationEvent>((event, emit) async {
      switch (event.tabType) {
        case TabNotificationType.tabMultiMeNotification:
          // Just change the active tab, don't reset data from API
          emit(state.copyWith(
              activeTab: TabNotificationType.tabMultiMeNotification));
          break;
        case TabNotificationType.tabServiceNotification:
          final listService = await fetchServiceNotification(0);
          emit(state.copyWith(
              activeTab: TabNotificationType.tabServiceNotification,
              listServiceNotification: listService,
              hasMoreServiceNotification: listService.length == 10));
          break;
        case TabNotificationType.tabProductNotification:
          final listProduct = await fetchProductNotification(0);
          emit(state.copyWith(
              activeTab: TabNotificationType.tabProductNotification,
              listProductNotification: listProduct,
              hasMoreProductNotification: listProduct.length == 10));
          break;
      }
    });

    on<LoadMoreNotificationEvent>((event, emit) async {
      switch (state.activeTab) {
        case TabNotificationType.tabMultiMeNotification:
          if (!state.hasMoreMultipleNotification) return;
          // Load more from API
          final nextPage = state.currentPage + 1;
          try {
            emit(state.copyWith(isLoading: true));
            final response =
                await notificationRepository.getNotificationHistory(
              userId: 1, // TODO: Get real user ID
              page: nextPage,
              limit: 20,
            );

            final notifications = response.data.data;
            emit(state.copyWith(
              listMultiMeNotification: [
                ...state.listMultiMeNotification,
                ...notifications
              ],
              currentPage: nextPage,
              hasMoreMultipleNotification:
                  state.listMultiMeNotification.length + notifications.length <
                      response.data.total,
              isLoading: false,
            ));
          } catch (e) {
            emit(state.copyWith(isLoading: false));
          }
          break;
        case TabNotificationType.tabServiceNotification:
          if (!state.hasMoreServiceNotification) return;
          final list = await fetchServiceNotification(
              state.listServiceNotification.length);
          emit(state.copyWith(listServiceNotification: [
            ...state.listServiceNotification,
            ...list
          ], hasMoreServiceNotification: list.length == 10));
          break;
        case TabNotificationType.tabProductNotification:
          if (!state.hasMoreProductNotification) return;
          final list = await fetchProductNotification(
              state.listProductNotification.length);

          emit(state.copyWith(listProductNotification: [
            ...state.listProductNotification,
            ...list
          ], hasMoreProductNotification: list.length == 10));
          break;
      }
    });

    on<onRefreshNotificationEvent>((event, emit) async {
      emit(state.copyWith(isRefreshing: true));
      await Future.delayed(const Duration(seconds: 1));

      switch (state.activeTab) {
        case TabNotificationType.tabMultiMeNotification:
          // Don't reset API data, just stop refreshing
          emit(state.copyWith(isRefreshing: false));
          break;
        case TabNotificationType.tabServiceNotification:
          final listService = await fetchServiceNotification(0);
          emit(state.copyWith(
              listServiceNotification: listService,
              hasMoreServiceNotification: listService.length == 10,
              isRefreshing: false));
          break;
        case TabNotificationType.tabProductNotification:
          final listProduct = await fetchProductNotification(0);
          emit(state.copyWith(
              listProductNotification: listProduct,
              hasMoreProductNotification: listProduct.length == 10,
              isRefreshing: false));
          break;
      }
    });
  }

  Future<void> _onLoadNotificationHistory(
    LoadNotificationHistoryEvent event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true, errorMessage: null));

      final response = await notificationRepository.getNotificationHistory(
        userId: event.userId,
        page: event.page,
        limit: event.limit,
      );

      final notifications = response.data.data;
      final isFirstPage = (event.page ?? 1) == 1;

      if (isFirstPage) {
        emit(state.copyWith(
          listMultiMeNotification: notifications,
          currentPage: response.data.page,
          totalNotifications: response.data.total,
          hasMoreMultipleNotification:
              notifications.length < response.data.total,
          isLoading: false,
        ));
      } else {
        emit(state.copyWith(
          listMultiMeNotification: [
            ...state.listMultiMeNotification,
            ...notifications
          ],
          currentPage: response.data.page,
          hasMoreMultipleNotification:
              state.listMultiMeNotification.length + notifications.length <
                  response.data.total,
          isLoading: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<List<MultiMeNotificationModel>> fetchMultiMeNotification(
      int offset) async {
    await Future.delayed(const Duration(seconds: 1));
    return []; // Return empty list since we're using API now
  }

  Future<List<ProductNotificationModel>> fetchProductNotification(
      int offset) async {
    await Future.delayed(const Duration(seconds: 1));
    return []; // Return empty list since we're using API now
  }

  Future<List<ServiceNotificationModel>> fetchServiceNotification(
      int offset) async {
    await Future.delayed(const Duration(seconds: 1));
    return []; // Return empty list since we're using API now
  }
}
