part of 'notification_bloc.dart';

sealed class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object> get props => [];
}

class TabChangedNotificationEvent extends NotificationEvent {
  final TabNotificationType tabType;

  const TabChangedNotificationEvent(this.tabType);

  @override
  List<Object> get props => [tabType];
}

class LoadNotificationHistoryEvent extends NotificationEvent {
  final int userId;
  final int? page;
  final int? limit;

  const LoadNotificationHistoryEvent({
    required this.userId,
    this.page,
    this.limit,
  });

  @override
  List<Object> get props => [userId, page ?? 1, limit ?? 20];
}

class LoadMoreNotificationEvent extends NotificationEvent {
  @override
  List<Object> get props => [];
}

class onRefreshNotificationEvent extends NotificationEvent {
  @override
  List<Object> get props => [];
}
