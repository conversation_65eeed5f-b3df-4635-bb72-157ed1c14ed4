import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:lottie/lottie.dart';
import 'package:multime_app/modules/notification_mode/presentation/bloc/notification_bloc.dart';
import 'package:multime_app/modules/notification_mode/presentation/multi_me_page/widgets/card_noti_multi_me.dart';
import 'package:multime_app/modules/notification_mode/presentation/widgets/empty_notification_widget.dart';
import '../../../../core/domain/global_dependencies.dart';
import '../../../../shared/widgets/app_loader/loading_strongbody.dart';

class MultiMeNotificationPage extends StatefulWidget {
  const MultiMeNotificationPage({super.key});

  @override
  _MultiMeNotificationPageState createState() =>
      _MultiMeNotificationPageState();
}

class _MultiMeNotificationPageState extends State<MultiMeNotificationPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        context.read<NotificationBloc>().add(LoadMoreNotificationEvent());
      }
    });
    _loadNotificationHistory();
  }

  void _loadNotificationHistory() {
    int userId = gs.uid ?? 1;
    context
        .read<NotificationBloc>()
        .add(LoadNotificationHistoryEvent(userId: userId, page: 1, limit: 20));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    _loadNotificationHistory();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, state) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Stack(
          children: [
            RefreshIndicator(
              onRefresh: _onRefresh,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  if (state.errorMessage != null)
                    SliverToBoxAdapter(
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        margin: EdgeInsets.only(bottom: 8.h),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline,
                                color: Colors.red, size: 20.w),
                            Gap(8.w),
                            Expanded(
                              child: Text(
                                state.errorMessage!,
                                style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontSize: 14.sp),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (state.listMultiMeNotification.isEmpty && !state.isLoading)
                    SliverFillRemaining(
                      child: EmptyNotificationWidget(
                        title: "There is no notice",
                        description:
                            "There are currently no notice from Multi.me.",
                        icon: Icons.notifications_off_outlined,
                      ),
                    )
                  else
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                          if (index == state.listMultiMeNotification.length) {
                            return Center(child: const LoadingStrongBody());
                          }

                          final item = state.listMultiMeNotification[index];
                          return CardNotiMultiMe(
                            model: item,
                          );
                        },
                        childCount: state.hasMoreMultipleNotification
                            ? state.listMultiMeNotification.length + 1
                            : state.listMultiMeNotification.length,
                      ),
                    ),
                ],
              ),
            ),
            if (state.isRefreshing || state.isLoading)
              Positioned.fill(
                child: Lottie.asset('assets/aimation/loading.json',
                    width: 200.w, height: 200.h),
              ),
          ],
        ),
      );
    });
  }
}
