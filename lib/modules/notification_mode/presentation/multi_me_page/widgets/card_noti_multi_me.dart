import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/notification_mode/data/models/multi_me_notification_model.dart';

class CardNotiMultiMe extends StatelessWidget {
  const CardNotiMultiMe({super.key, required this.model});
  final MultiMeNotificationModel model;

  String _formatTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return dateTimeString;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 60.h,
              height: 60.h,
              decoration: BoxDecoration(
                color: Theme.of(context).hyperlink(context).withOpacity(0.1),
              ),
              child: Image.network(
                "https://fujigroup.vn/media/news/1308_cach-massage-duong-sinh-1.jpg",
                fit: BoxFit.cover,
              ),
            ),
            Gap(12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  if (model.description.isNotEmpty) ...[
                    Gap(4.h),
                    Text(
                      model.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).greyScale600(context),
                          ),
                    ),
                  ],
                  Gap(6.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _formatTime(model.sentAt),
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: Theme.of(context).greyScale400(context),
                            fontSize: 12),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
        Gap(8.h),
        const Divider(),
      ],
    );
  }
}
