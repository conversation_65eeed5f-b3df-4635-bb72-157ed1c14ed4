import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/modules/notification_mode/presentation/product_page/widgets/card_noti_product.dart';
import 'package:multime_app/modules/notification_mode/presentation/widgets/empty_notification_widget.dart';

import '../../../../shared/widgets/app_loader/loading_strongbody.dart';
import '../bloc/notification_bloc.dart';

class ProductNotificationPage extends StatefulWidget {
  const ProductNotificationPage({super.key});

  @override
  State<ProductNotificationPage> createState() =>
      _ProductNotificationPageState();
}

class _ProductNotificationPageState extends State<ProductNotificationPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    BlocProvider.of<NotificationBloc>(context);
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        context.read<NotificationBloc>().add(LoadMoreNotificationEvent());
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    context.read<NotificationBloc>().add(onRefreshNotificationEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, state) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Stack(
          children: [
            RefreshIndicator(
              onRefresh: _onRefresh,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  if (state.listProductNotification.isEmpty && !state.isLoading)
                    SliverFillRemaining(
                      child: EmptyNotificationWidget(
                        title: "There is no product notification",
                        description:
                            "There are currently no announcements about the product.",
                        icon: Icons.shopping_bag_outlined,
                      ),
                    )
                  else
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                          if (index == state.listProductNotification.length) {
                            return state.hasMoreProductNotification
                                ? const LoadingStrongBody()
                                : const SizedBox.shrink();
                          }

                          final item = state.listProductNotification[index];
                          return CardNotiProduct(
                            model: item,
                          );
                        },
                        childCount: state.hasMoreProductNotification
                            ? state.listProductNotification.length + 1
                            : state.listProductNotification.length,
                      ),
                    ),
                ],
              ),
            ),
            if (state.isRefreshing)
              Positioned.fill(
                child: Center(
                  child: AlertDialog(
                    backgroundColor: Colors.transparent,
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const LoadingStrongBody(),
                        Gap(10.h),
                        const Text('Loading...'),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }
}
