import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/notification_mode/data/models/service_notification_model.dart';

class CardNotiService extends StatelessWidget {
  const CardNotiService(  {super.key, required this.model});
  final ServiceNotificationModel model;


  @override
  Widget build(BuildContext context) {
    return    Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                image:  DecorationImage(
                  image: AssetImage(model.image),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const Gap(10),
            Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      model.title,
                      textAlign: TextAlign.justify,
                      style:
                      Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      'Order ID: ${model.orderId}',
                      style: TextStyle(
                          fontWeight: FontWeight.w300,
                          color: Theme.of(context)
                              .textPrimary(context),
                          fontSize: 14),
                      textAlign: TextAlign.justify,
                    ),
                    Text(
                      textAlign: TextAlign.justify,
                      model.time,
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          color: Theme.of(context).textSecondary(context)),
                    )
                  ],
                )),
          ],
        ),
        Gap(8.h),
        const Divider(),
      ],
    );
  }
}
