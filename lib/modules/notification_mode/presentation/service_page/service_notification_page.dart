import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/modules/notification_mode/presentation/service_page/widgets/card_noti_service.dart';
import 'package:multime_app/modules/notification_mode/presentation/widgets/empty_notification_widget.dart';
import '../../../../shared/widgets/app_loader/loading_strongbody.dart';
import '../bloc/notification_bloc.dart';

class ServiceNotificationPage extends StatefulWidget {
  const ServiceNotificationPage({super.key});

  @override
  State<ServiceNotificationPage> createState() =>
      _ServiceNotificationPageState();
}

class _ServiceNotificationPageState extends State<ServiceNotificationPage> {
  final ScrollController _scrollController = ScrollController();
  late NotificationBloc notificationBloc;

  @override
  void initState() {
    super.initState();
    notificationBloc = BlocProvider.of<NotificationBloc>(context);
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        context.read<NotificationBloc>().add(LoadMoreNotificationEvent());
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    notificationBloc.add(onRefreshNotificationEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, state) {
      return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Stack(
            children: [
              RefreshIndicator(
                onRefresh: _onRefresh,
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    if (state.listServiceNotification.isEmpty &&
                        !state.isLoading)
                      SliverFillRemaining(
                        child: EmptyNotificationWidget(
                          title: "There is no service notice",
                          description:
                              "There are currently no notices about the service.",
                          icon: Icons.room_service_outlined,
                        ),
                      )
                    else
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (BuildContext context, int index) {
                            if (index == state.listServiceNotification.length) {
                              return const LoadingStrongBody();
                            }

                            final item = state.listServiceNotification[index];
                            return CardNotiService(
                              model: item,
                            );
                          },
                          childCount: state.hasMoreServiceNotification
                              ? state.listServiceNotification.length + 1
                              : state.listServiceNotification.length,
                        ),
                      ),
                  ],
                ),
              ),
              if (state.isRefreshing)
                Positioned.fill(
                  child: Center(
                    child: AlertDialog(
                      backgroundColor: Colors.transparent,
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const LoadingStrongBody(),
                          Gap(10.h),
                          const Text('Loading...'),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ));
    });
  }
}
