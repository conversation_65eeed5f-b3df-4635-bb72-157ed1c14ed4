import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import '../models/notification_history_response.dart';

abstract class NotificationRepository {
  Future<NotificationHistoryResponse> getNotificationHistory({
    required int userId,
    int? page,
    int? limit,
  });
}

class NotificationRepositoryRemote implements NotificationRepository {
  final ApiClient _apiClient;

  NotificationRepositoryRemote(this._apiClient);

  @override
  Future<NotificationHistoryResponse> getNotificationHistory({
    required int userId,
    int? page,
    int? limit,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};
      if (page != null) queryParams['page'] = page;
      if (limit != null) queryParams['limit'] = limit;

      final response = await _apiClient.request(
        path: '${ApiConst.notificationHistory}/$userId',
        method: ApiType.get,
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
        headers: {'Scope': 'multi-me'},
      );

      return NotificationHistoryResponse.fromJson(response);
    } catch (e) {
      print("getNotificationHistory error: $e");
      rethrow;
    }
  }
}
