import 'multi_me_notification_model.dart';

class NotificationHistoryResponse {
  final int code;
  final String message;
  final NotificationHistoryData data;

  const NotificationHistoryResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory NotificationHistoryResponse.fromJson(Map<String, dynamic> json) {
    return NotificationHistoryResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: NotificationHistoryData.fromJson(json['data'] ?? {}),
    );
  }
}

class NotificationHistoryData {
  final List<MultiMeNotificationModel> data;
  final int total;
  final int page;
  final int limit;
  final int userId;

  const NotificationHistoryData({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.userId,
  });

  factory NotificationHistoryData.fromJson(Map<String, dynamic> json) {
    return NotificationHistoryData(
      data: (json['data'] as List<dynamic>? ?? [])
          .map((item) => MultiMeNotificationModel.fromJson(item))
          .toList(),
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      userId: json['user_id'] ?? 0,
    );
  }
}
