class MultiMeNotificationModel {
  final int id;
  final int userId;
  final String deviceId;
  final String title;
  final String description;
  final String type;
  final String status;
  final String? data;
  final String? onesignalResponse;
  final String sentAt;
  final String createdAt;
  final String updatedAt;

  const MultiMeNotificationModel({
    required this.id,
    required this.userId,
    required this.deviceId,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    this.data,
    this.onesignalResponse,
    required this.sentAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MultiMeNotificationModel.fromJson(Map<String, dynamic> json) {
    return MultiMeNotificationModel(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      deviceId: json['device_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      type: json['type'] ?? '',
      status: json['status'] ?? '',
      data: json['data'],
      onesignalResponse: json['onesignal_response'],
      sentAt: json['sent_at'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'device_id': deviceId,
      'title': title,
      'description': description,
      'type': type,
      'status': status,
      'data': data,
      'onesignal_response': onesignalResponse,
      'sent_at': sentAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}
