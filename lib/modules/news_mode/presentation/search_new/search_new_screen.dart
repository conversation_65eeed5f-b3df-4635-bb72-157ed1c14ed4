import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/modules/news_mode/fake_data/user_new_data.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/bloc/search_bloc.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/bloc/search_state.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/filter_widget.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/search_access_widget.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/search_defaul_widget.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/app_bar_widget.dart';

class SearchNewScreen extends StatefulWidget {
  const SearchNewScreen({super.key});

  @override
  State<SearchNewScreen> createState() => _SearchNewScreenState();
}

class _SearchNewScreenState extends State<SearchNewScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late SearchBloc _searchBloc;

  @override
  void initState() {
    super.initState();
    _searchBloc = SearchBloc()..setInitalData(userNewData);
  }

  @override
  void dispose() {
    _searchBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SearchBloc>.value(
      value: _searchBloc,
      child: Scaffold(
        key: _scaffoldKey,
        endDrawer: const Drawer(
          backgroundColor: AppColors.white,
          child: FilterWidget(),
        ),
        appBar: AppBarWidget(scaffoldKey: _scaffoldKey),
        body: BlocBuilder<SearchBloc, SearchState>(
          builder: (context, state) {
            return state.query.isEmpty
                ? const SearchDefaulWidget()
                : const SearchAccessWidget();
          },
        ),
      ),
    );
  }
}
