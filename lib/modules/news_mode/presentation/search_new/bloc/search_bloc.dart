import 'package:bloc/bloc.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/bloc/search_state.dart';

class SearchBloc extends Cubit<SearchState> {
  SearchBloc() : super(SearchState());

  void setInitalData(List<UserNew> allUsers) {
    emit(state.copyWith(allUsers: allUsers, fillterUsers: allUsers));
  }

  void searchByName(String query) {
    final fillterUsers = state.allUsers.where((user) {
      return user.author.toLowerCase().contains(query.toLowerCase());
    }).toList();

    emit(state.copyWith(fillterUsers: fillterUsers, query: query));
  }
}
