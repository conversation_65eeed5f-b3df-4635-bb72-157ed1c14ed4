import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';

class SearchState extends Equatable {
  final List<UserNew> allUsers;
  final List<UserNew> fillterUsers;
  final String query;
  SearchState(
      {this.allUsers = const [],
      this.fillterUsers = const [],
      this.query = ''});

  @override
  List<Object> get props => [allUsers, fillterUsers, query];

  SearchState copyWith({
    List<UserNew>? allUsers,
    List<UserNew>? fillterUsers,
    String? query,
  }) {
    return SearchState(
      allUsers: allUsers ?? this.allUsers,
      fillterUsers: fillterUsers ?? this.fillterUsers,
      query: query ?? this.query,
    );
  }
}
