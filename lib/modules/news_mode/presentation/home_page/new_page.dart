import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/application/bottom_bar/application_page.dart';
import 'package:multime_app/modules/application/bottom_bar/bottomSheet_model.dart';
import 'package:multime_app/modules/news_mode/presentation/home_page/home_page.dart';

// ignore: depend_on_referenced_packages

class NewPage extends StatelessWidget {
  const NewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ApplicationPage(
      page: const HomeNewScreen(),
      bottomSheetItems: [
        BottomSheetItem(
          name: "AI assistant writes for you",
          iconLead: AppAssets.aiAssistantSocial,
          onTap: () {
            (context).push(RouteName.aiAssistant);
          },
        ),
        BottomSheetItem(
          name: "Share news",
          iconLead: AppAssets.editSvg,
          onTap: () {
            (context).push(RouteName.postNew);
          },
        ),
        BottomSheetItem(
          name: "Review my post",
          iconLead: AppAssets.history,
          onTap: () {
            (context).go(RouteName.reviewNew);
          },
        ),
        BottomSheetItem(
          name: "Liked",
          iconLead: AppAssets.favoriteMarket,
          onTap: () {
            (context).push(RouteName.likedNew);
          },
        ),
      ],
    );
  }
}
