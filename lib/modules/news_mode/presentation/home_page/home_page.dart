import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/fake_data/user_new_data.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/home_page/invite_contact_new.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/home_page/tab_bar_home_page.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/card_news.dart';
import '../widgets/home_page/card_latest.dart';
import '../widgets/home_page/session_heading.dart';

class HomeNewScreen extends StatefulWidget {
  const HomeNewScreen({super.key});

  @override
  State<HomeNewScreen> createState() => _HomeNewScreenState();
}

class _HomeNewScreenState extends State<HomeNewScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool isHeader = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.newText.tr(),
        onTapSearch: () {
          (context).push(RouteName.SearchNewScreen);
        },
      ),
      body: Column(
        children: [
          if (isHeader)
            Container(
              height: sizeHeight * 0.2,
              width: sizeWidth,
              color: Theme.of(context).secondary(context),
              child: Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 8.0.h),
                    child: Center(
                      child: Column(
                        children: [
                          Text(
                            LocaleKeys.readAndShare.tr(),
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyMediumRegular
                                .copyWith(
                                  color:
                                      Theme.of(context).whitePrimary(context),
                                ),
                            textAlign: TextAlign.center,
                          ),
                          Gap(15.h),
                          Text(
                            LocaleKeys.createCommunity.tr(),
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyMediumRegular
                                .copyWith(
                                  color:
                                      Theme.of(context).whitePrimary(context),
                                ),
                            textAlign: TextAlign.center,
                          )
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 0.h,
                    right: 0.w,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          isHeader = false;
                        });
                      },
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: Theme.of(context).whitePrimary(context),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          TabBarHomePage(controller: _tabController),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap(25.h),
                        TSectionHeading(
                          title: LocaleKeys.hotNew.tr(),
                          showActionButton: false,
                        ),
                        Gap(10.h),
                        const CardNews(
                            title: 'Europe',
                            description:
                                "Lorem ipsum dolor sit sample text goes here",
                            author: 'Diddy',
                            avatar:
                                'https://hoanghamobile.com/tin-tuc/wp-content/webp-express/webp-images/uploads/2024/04/anh-con-gai-1-1.jpg.webp'),
                        Gap(10.h),
                        const TSectionHeading(
                          title: 'Invite more contact',
                          showActionButton: false,
                        ),
                        Gap(10.h),
                        const InviteContactNew(
                          referralLink: 'Referral link',
                          link: '/google.com',
                          textButton: 'Invite more people',
                          title: 'Invite more contact',
                        ),
                        TSectionHeading(
                          title: LocaleKeys.latest.tr(),
                        ),
                        ListView.separated(
                            shrinkWrap: true,
                            padding: EdgeInsets.only(bottom: 20.h),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: userNewData.length,
                            itemBuilder: (context, index) {
                              return GestureDetector(
                                  onTap: () {
                                    context.push(RouteName.NewsScreen,
                                        extra: {'userNew': userNewData[index]});
                                  },
                                  child:
                                      CardLatest(userNew: userNewData[index]));
                            },
                            separatorBuilder: (context, index) => Gap(20.h)),
                      ],
                    ),
                  ),
                ),
                Container(),
                Container(),
                Container(),
                Container(),
                Container(),
                Container(),
              ],
            ),
          )
        ],
      ),
    );
  }
}
