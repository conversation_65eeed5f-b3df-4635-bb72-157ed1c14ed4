import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class TabBarHomePage extends StatelessWidget {
  const TabBarHomePage({super.key, required this.controller});
  final TabController controller;

  @override
  Widget build(BuildContext context) {
    return TabBar(
        controller: controller,
        isScrollable: true,
        indicatorColor: Theme.of(context).primary(context),
        tabAlignment: TabAlignment.start,
        labelStyle: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
              color: Theme.of(context).primary(context),
            ),
        tabs: [
          Tab(
            text: LocaleKeys.tabAll.tr(),
          ),
          Tab(
            text: LocaleKeys.tabSports.tr(),
          ),
          Tab(
            text: LocaleKeys.tabPolitics.tr(),
          ),
          Tab(
            text: LocaleKeys.tabBusiness.tr(),
          ),
          Tab(
            text: LocaleKeys.tabHealth.tr(),
          ),
          Tab(
            text: LocaleKeys.tabTravel.tr(),
          ),
          Tab(
            text: LocaleKeys.tabScience.tr(),
          ),
        ]);
  }
}
