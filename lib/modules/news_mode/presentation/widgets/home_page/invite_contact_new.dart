import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_cubit.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_state.dart';

class InviteContactNew extends StatefulWidget {
  final String title;
  final String referralLink;
  final String link;
  final String textButton;

  const InviteContactNew(
      {super.key,
      required this.title,
      required this.referralLink,
      required this.link,
      required this.textButton});

  @override
  State<InviteContactNew> createState() => _InviteContactNewState();
}

class _InviteContactNewState extends State<InviteContactNew> {
  double _sliderValue = 0;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => FindJobBloc(),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
              color: Theme.of(context).greyScale300(context), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(widget.title,
                style: Theme.of(context).textTheme.lightBodySmallMedium),
            Gap(10.h),
            BlocBuilder<FindJobBloc, FindJobState>(
              builder: (context, state) {
                return Column(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height * 0.025,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: Theme.of(context).greyScale300(context),
                            width: 1.0),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            trackHeight: 16.0,
                            activeTrackColor:
                                Theme.of(context).textPrimary(context),
                            inactiveTrackColor:
                                Theme.of(context).whitePrimary(context),
                            thumbColor: Theme.of(context).textPrimary(context),
                            thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 6.0),
                            overlayColor: Colors.transparent,
                            trackShape: CustomTrackShape(),
                          ),
                          child: Slider(
                            value: _sliderValue,
                            min: 0,
                            max: 100,
                            onChanged: (value) {
                              setState(() {
                                _sliderValue = value;
                              });
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
            Text(
              '${_sliderValue.toStringAsFixed(0)}% progress',
              style: Theme.of(context)
                  .textTheme
                  .lightBodySmallMedium
                  .copyWith(color: Theme.of(context).textPrimary(context)),
            ),
            Gap(10.h),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: Theme.of(context).textPrimary(context), width: 1),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Row(
                children: [
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                          text: widget.referralLink,
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumRegular
                              .copyWith(
                                  color:
                                      Theme.of(context).textPrimary(context))),
                      TextSpan(
                          text: widget.link,
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumRegular
                              .copyWith(
                                  color:
                                      Theme.of(context).textPrimary(context))),
                    ]),
                  ),
                  const Spacer(),
                ],
              ),
            ),
            Gap(10.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                      backgroundColor: Theme.of(context).textPrimary(context),
                      foregroundColor: Theme.of(context).whitePrimary(context)),
                  child: Text(
                    widget.textButton,
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumMedium
                        .copyWith(
                            color: Theme.of(context).whitePrimary(context)),
                  )),
            )
          ],
        ),
      ),
    );
  }
}

class CustomTrackShape extends RoundedRectSliderTrackShape {
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 4.0;
    final double trackLeft = offset.dx;
    final double trackTop =
        offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }
}
