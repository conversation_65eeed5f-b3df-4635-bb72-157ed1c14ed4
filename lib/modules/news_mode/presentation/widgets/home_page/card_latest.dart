import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';

class CardLatest extends StatefulWidget {
  UserNew userNew;
  CardLatest({super.key, required this.userNew});

  @override
  State<CardLatest> createState() => _CardLatestState();
}

class _CardLatestState extends State<CardLatest> {
  bool isFavourite = false;

  @override
  Widget build(BuildContext context) {
    final sizeHight = MediaQuery.of(context).size.height;
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 1, // Chia tỷ lệ giữa ảnh và nội dung
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.network(
                height: sizeHight * 0.12,
                widget.userNew.image,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Gap(10.w),
          Expanded(
            flex: 2, // Cột text chiếm nhiều không gian hơn
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      // Để tránh lỗi tràn dòng
                      child: Text(
                        widget.userNew.title,
                        style:
                            Theme.of(context).textTheme.lightBodyLargeRegular,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        setState(() {
                          isFavourite = !isFavourite;
                        });
                      },
                      child: SvgPicture.asset(
                        AppAssets.heart,
                        color: isFavourite
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).greyScale100(context),
                      ),
                    ),
                  ],
                ),
                Gap(5.h),
                Text(
                  widget.userNew.description,
                  style: Theme.of(context).textTheme.lightBodyLargeRegular,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  softWrap: true,
                ),
                const Spacer(),
                Row(
                  children: [
                    CircleAvatar(
                      radius: 12,
                      backgroundImage: NetworkImage(widget.userNew.authorImage),
                    ),
                    Gap(5.h),
                    Text(
                      widget.userNew.author,
                      style: Theme.of(context).textTheme.lightBodyMediumRegular,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
