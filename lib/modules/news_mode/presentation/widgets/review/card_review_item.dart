import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/showdialog_new.dart';

class CardReviewItem extends StatefulWidget {
  const CardReviewItem({
    super.key,
    required this.userNew,
  });
  final UserNew userNew;

  @override
  State<CardReviewItem> createState() => _CardReviewItemState();
}

class _CardReviewItemState extends State<CardReviewItem> {
  bool isFavorite = false;
  final GlobalKey _key = GlobalKey();
  @override
  Widget build(BuildContext context) {
    final sizeHight = MediaQuery.of(context).size.height;

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 1,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.network(
                widget.userNew.image,
                height: sizeHight * 0.12,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Gap(10.w),
          Expanded(
            flex: 2,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(
                widget.userNew.title,
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumRegular
                    .copyWith(color: Theme.of(context).textSecondary(context)),
              ),
              Text(
                widget.userNew.description,
                style: Theme.of(context)
                    .textTheme
                    .lightBodyLargeSemiBold
                    .copyWith(color: Theme.of(context).textPrimary(context)),
              ),
              Gap(12.h),
              Row(
                children: [
                  CircleAvatar(
                    radius: 12,
                    backgroundImage: NetworkImage(widget.userNew.authorImage),
                  ),
                  Gap(10.w),
                  Text(
                    widget.userNew.author,
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumSemiBold
                        .copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                  ),
                ],
              ),
            ]),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    isFavorite = !isFavorite; // Đảo trạng thái yêu thích
                  });
                },
                child: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border_outlined,
                  color: isFavorite
                      ? Theme.of(context).secondary(context)
                      : Theme.of(context).secondary(context),
                ),
              ),
              GestureDetector(
                key: _key,
                onTap: () {
                  // Lấy vị trí của widget
                  final RenderBox renderBox =
                      _key.currentContext!.findRenderObject() as RenderBox;
                  final position = renderBox
                      .localToGlobal(Offset.zero); // Lấy tọa độ trên màn hình

                  // Hiển thị menu tại vị trí của nút
                  showMenu<String>(
                    context: context,
                    position: RelativeRect.fromLTRB(
                      position.dx, // Tọa độ X
                      position.dy, // Tọa độ Y
                      position.dx, // Tọa độ X bên phải
                      position.dy, // Tọa độ Y dưới cùng
                    ),
                    color: AppColors.white,
                    items: [
                      PopupMenuItem<String>(
                        value: 'Edit',
                        child: ListTile(
                          leading: SvgPicture.asset(AppAssets.noteEditSvg),
                          title: Text('Edit'),
                        ),
                      ),
                      PopupMenuItem<String>(
                        value: 'Delete',
                        child: ListTile(
                          leading: SvgPicture.asset(AppAssets.trushSquareSvg),
                          title: Text('Delete'),
                        ),
                      ),
                    ],
                  ).then((value) {
                    if (value == 'Edit') {
                      context.push(
                        RouteName.editNewPage,
                        extra: widget
                            .userNew, // Truyền giá trị userNew qua `extra`.
                      );
                    } else if (value == 'Delete') {
                      showDeleteDialog(context);
                    }
                  });
                },
                child: const Icon(Icons.more_horiz_outlined),
              )
            ],
          )
        ],
      ),
    );
  }
}
