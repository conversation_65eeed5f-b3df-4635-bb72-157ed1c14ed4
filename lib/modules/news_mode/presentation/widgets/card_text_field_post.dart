import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CardTextFieldPost extends StatelessWidget {
  const CardTextFieldPost(
      {super.key,
      required this.title,
      required this.icon,
      required this.checkMaxLine,
      this.isFloatingLabel = false,
      this.hintText});

  final String title;
  final String? hintText;
  final String icon;
  final bool checkMaxLine;
  final bool isFloatingLabel;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      decoration: InputDecoration(
        suffixIcon: Padding(
          padding: const EdgeInsets.only(right: 15),
          child: SvgPicture.asset(icon),
        ),
        floatingLabelBehavior: isFloatingLabel
            ? FloatingLabelBehavior.always
            : FloatingLabelBehavior.auto,
        hintText: hintText,
        hintStyle: Theme.of(context).textTheme.lightBodyLargeRegular,
        suffixIconConstraints: BoxConstraints(
          maxHeight: 50.h,
          maxWidth: 50.w,
        ),
        labelText: title,
        labelStyle: Theme.of(context).textTheme.lightBodyMediumRegular,
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
            borderSide: BorderSide(
                color: Theme.of(context).greyScale200(context), width: 1)),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
            borderSide: BorderSide(
                color: Theme.of(context).greyScale200(context), width: 1)),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 15.h),
        alignLabelWithHint: true,
      ),
      maxLines: checkMaxLine ? 5 : null,
      keyboardType: TextInputType.multiline,
      textAlignVertical: TextAlignVertical.top,
      textInputAction: TextInputAction.done,
      onFieldSubmitted: (value) {
        FocusScope.of(context).requestFocus(FocusNode());
      },
    );
  }
}
