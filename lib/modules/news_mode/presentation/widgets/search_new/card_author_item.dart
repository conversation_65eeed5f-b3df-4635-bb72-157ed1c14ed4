import 'package:flutter/material.dart';

import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:multime_app/core/constants/app_spacings.dart';


import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';

class CardAuthorItem extends StatelessWidget {
  final UserNew userNew;
  const CardAuthorItem({super.key, required this.userNew});

  @override
  Widget build(BuildContext context) {
    final sizeHeight = MediaQuery.of(context).size.height;
    final sizeWidth = MediaQuery.of(context).size.width;
    return Container(
      padding: EdgeInsets.symmetric(vertical: AppSpacing.padding10h),
      decoration:  BoxDecoration(
          border: Border(
        bottom: BorderSide(
          color: Theme.of(context).greyScale500(context),
          width: 1,
        ),
      )),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Image.network(
              userNew.image,
              width: sizeWidth * 0.15,
              height: sizeHeight * 0.07,
              fit: BoxFit.cover,
            ),
          ),
          Gap(16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  userNew.author,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeRegular
                      .copyWith(
                        color: Theme.of(context).textPrimary(context),
                      ),
                ),
                Gap(8.h),
                Text(
                  userNew.title,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeRegular
                      .copyWith(
                        color: Theme.of(context).textSecondary(context),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
