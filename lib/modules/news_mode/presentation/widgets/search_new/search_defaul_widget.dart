import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/history_search_widget.dart';

class SearchDefaulWidget extends StatefulWidget {
  const SearchDefaulWidget({super.key});

  @override
  State<SearchDefaulWidget> createState() => _SearchDefaulWidgetState();
}

class _SearchDefaulWidgetState extends State<SearchDefaulWidget> {
  int numberOfTagsToShow = 5;
  bool showAll = false;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Gap(20.h),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: showAll ? 10 : numberOfTagsToShow,
            itemBuilder: (context, index) {
              return const HistorySearchWidget();
            },
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                showAll = !showAll;
              });
            },
            child: Text(
              showAll ? 'Show less' : 'Show more',
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    color:Theme.of(context).greyScale500(context),
                    fontWeight: FontWeight.w400,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
