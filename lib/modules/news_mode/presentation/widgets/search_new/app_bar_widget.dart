import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/bloc/search_bloc.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/bloc/search_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;

  const AppBarWidget({Key? key, required this.scaffoldKey}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(160.h),
      child: Container(
        padding: EdgeInsets.only(top: 60.h, left: 5.w, right: 12.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            IconButton(
              onPressed: () {
                context.push(RouteName.newPage);
              },
              icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            ),
            Expanded(
              child: Container(
                height: MediaQuery.of(context).size.height * 0.045,
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  border: Border.all(color: Theme.of(context).greyScale500(context), width: 1),
                  borderRadius: BorderRadius.circular(AppRadius.radius16),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: BlocBuilder<SearchBloc, SearchState>(
                        builder: (context, state) {
                          return TextFormField(
                            onChanged: (value) {
                              context.read<SearchBloc>().searchByName(value);
                            },
                            decoration: InputDecoration(
                              hintText: 'Search',
                              hintStyle: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    color: Theme.of(context).greyScale500(context),
                                    fontWeight: FontWeight.w400,
                                  ),
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              // contentPadding: EdgeInsets.only(bottom: 10.h),
                            ),
                          );
                        },
                      ),
                    ),
                    SvgPicture.asset(AppAssets.searchSvg),
                  ],
                ),
              ),
            ),
            Gap(10.w),
            GestureDetector(
              onTap: () {
                scaffoldKey.currentState!.openEndDrawer();
              },
              child: Row(
                children: [
                  SvgPicture.asset(AppAssets.filterSvg),
                  Gap(5.w),
                  Text(
                    'Filter',
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: Theme.of(context).primary(context),
                          fontWeight: FontWeight.w400,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(160.h);
}
