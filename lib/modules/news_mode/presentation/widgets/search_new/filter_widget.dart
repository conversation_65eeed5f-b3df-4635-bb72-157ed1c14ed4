import 'package:country_code_picker/country_code_picker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/l10n/locale_keys.g.dart';
import '../../../../auth_mode/presentation/widgets/strong_body_text_field.dart';

class FilterWidget extends StatefulWidget {
  const FilterWidget({super.key});

  @override
  State<FilterWidget> createState() => _FilterWidgetState();
}

class _FilterWidgetState extends State<FilterWidget> {
  final TextEditingController _countryController = TextEditingController();

  int selectedIndex = 0;
  String currentText = 'Sports';
  List<String> sortBy = ['Recommentded', 'News', 'Latest', 'Most Viewed'];
  List<String> categorys = [
    'All',
    'Business',
    'Entertainment',
    'General',
    'Health',
    'Science',
    'Sports',
    'Technology'
  ];
  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;
    return Padding(
        padding: const EdgeInsets.only(top: 120, left: 27, right: 27),
        child: SizedBox(
            width: sizeWidth,
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    children: [
                      Text(
                        'Filter',
                        style: Theme.of(context)
                            .textTheme
                            .lightHeadingMedium
                            .copyWith(
                                color: Theme.of(context).textPrimary(context)),
                      ),
                      const Spacer(),
                      IconButton(
                          onPressed: () => context.pop(),
                          icon: SvgPicture.asset(AppAssets.closeSvg)),
                    ],
                  ),
                  Gap(16.h),
                  Text(
                    'Sort By',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeMedium
                        .copyWith(
                          color: Theme.of(context).textPrimary(context),
                        ),
                  ),
                  Gap(16.h),
                  SizedBox(
                    width: sizeWidth * 0.247,
                    child: Wrap(
                      spacing: 5,
                      runSpacing: 10,
                      children: List.generate(sortBy.length, (index) {
                        final isSelected = selectedIndex == index;
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedIndex = index;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 10), // Padding khung
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Theme.of(context).textPrimary(context)
                                  : Theme.of(context)
                                      .whitePrimary(context), // Nền khi chọn
                              border: Border.all(
                                color: isSelected
                                    ? Theme.of(context).textPrimary(context)
                                    : Theme.of(context).alertWhite(context),
                                width: 1, // Độ dày viền
                              ),
                              borderRadius: BorderRadius.circular(5), // Bo góc
                            ),
                            child: Text(
                              sortBy[index],
                              textAlign: TextAlign.center,
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodySmallMedium
                                  .copyWith(
                                    color: isSelected
                                        ? Theme.of(context)
                                            .whitePrimary(context)
                                        : Theme.of(context)
                                            .blackPrimary(context),
                                  ),
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                  Gap(16.h),
                  Text(
                    'Category',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeMedium
                        .copyWith(
                          color: Theme.of(context).textPrimary(context),
                        ),
                  ),
                  Gap(16.h),
                  Container(
                    width: sizeWidth,
                    height: sizeHeight * 0.05,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: AppColors.white,
                        border:
                            Border.all(color: Theme.of(context).greyScale500(context), width: 1),
                        borderRadius:
                            BorderRadius.circular(AppRadius.radius10)),
                    child: DropdownButton<String>(
                      value: currentText,
                      onChanged: (String? newValue) {
                        setState(() {
                          currentText = newValue!;
                        });
                      },
                      isExpanded: true,
                      icon: SvgPicture.asset(AppAssets.arrowDownSvg),
                      underline: const SizedBox(),
                      items: categorys
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                            value: value,
                            child: Text(
                              value,
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumMedium
                                  .copyWith(color: Theme.of(context).secondaryBase(context)),
                            ));
                      }).toList(),
                    ),
                  ),
                  Gap(16.h),
                  Text(
                    'Country',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeMedium
                        .copyWith(
                          color: Theme.of(context).secondaryBase(context)
                        ),
                  ),
                  Gap(16.h),
                  StrongBodyTextField(
                    controller: _countryController,
                    labalText: LocaleKeys.country.tr(),
                    textInputAction: TextInputAction.none,
                    readOnly: true,
                    contentPadding:
                        const EdgeInsets.only(left: 15.0, bottom: 16, top: 10),
                    hintStyle: TextStyle(
                        fontSize: 14.sp, color: Theme.of(context).greyScale600(context)),
                    suffixIcon: CountryCodePicker(
                      onChanged: (country) {
                        _countryController.text = country.name!;
                      },
                      builder: (country) {
                        return const Icon(Icons.arrow_drop_down);
                      },
                    ),
                    isSuffixIcon: true,
                    enabled: true,
                  ),
                  Gap(50.h),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 40.h,
                          width: 161.w,
                          decoration: BoxDecoration(
                              color: AppColors.white,
                              border: Border.all(
                                  color: Theme.of(context).secondaryBase(context), width: 1),
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius10)),
                          child: Center(
                            child: Text('Reset',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeMedium),
                          ),
                        ),
                      ),
                      Gap(10.w),
                      Expanded(
                        child: Container(
                          height: 40.h,
                          width: 161.w,
                          decoration: BoxDecoration(
                              color: Theme.of(context).primary(context),
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius10)),
                          child: Center(
                            child: Text(
                              'Apply',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeMedium
                                  .copyWith(
                                    color: AppColors.white,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                ])));
  }
}
