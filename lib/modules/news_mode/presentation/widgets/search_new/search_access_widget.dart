import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/bloc/search_bloc.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/bloc/search_state.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/card_author_item.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/card_category_item.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/search_new/card_like_item.dart';

class SearchAccessWidget extends StatefulWidget {
  const SearchAccessWidget({super.key});

  @override
  State<SearchAccessWidget> createState() => _SearchAccessWidgetState();
}

class _SearchAccessWidgetState extends State<SearchAccessWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget buildListView(Widget Function(UserNew obj) cardBuilder) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      child: BlocBuilder<SearchBloc, SearchState>(
        builder: (context, state) {
          if (state.fillterUsers.isEmpty) {
            return Center(
              child: Text(
                'User not aviailable',
                style:
                    Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
              ),
            );
          } else {
            return ListView.builder(
              itemCount: state.fillterUsers.length,
              itemBuilder: (context, index) {
                var obj = state.fillterUsers[index];
                return cardBuilder(obj);
              },
            );
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Gap(20.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: TabBar(
            controller: _tabController,
            isScrollable: false,
            labelStyle:
                Theme.of(context).textTheme.lightBodyLargeBold.copyWith(
                      color: Theme.of(context).primary(context),
                    ),
            unselectedLabelStyle:
                Theme.of(context).textTheme.lightBodyLargeBold.copyWith(
                      color: Theme.of(context).textSecondary(context),
                    ),
            indicatorColor: Theme.of(context).primary(context),
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Theme.of(context).greyScale700(context),
            tabAlignment: TabAlignment.fill,
            tabs: const [
              Tab(
                text: 'News',
              ),
              Tab(
                text: 'Category',
              ),
              Tab(
                text: 'Author',
              ),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              buildListView(
                (obj) => CardLikeItem(
                  userNew: obj,
                ),
              ),
              buildListView((obj) => CardCategoryItem(userNew: obj)),
              buildListView((obj) => CardAuthorItem(userNew: obj)),
            ],
          ),
        ),
      ],
    );
  }
}
