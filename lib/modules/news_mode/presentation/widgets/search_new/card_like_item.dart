import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';

class CardLikeItem extends StatefulWidget {
  final UserNew userNew;

  const CardLikeItem({
    super.key,
    required this.userNew,
  });

  @override
  State<CardLikeItem> createState() => _CardLikeItemState();
}

class _CardLikeItemState extends State<CardLikeItem> {
  bool isFavorite = false;
  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 1,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.network(
                widget.userNew.image,
                height: sizeWidth * 0.11,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Gap(10.w),
          Expanded(
            flex: 2,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(
                widget.userNew.title,
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumRegular
                    .copyWith(color: Theme.of(context).textSecondary(context)),
              ),
              Text(
                widget.userNew.description,
                style: Theme.of(context)
                    .textTheme
                    .lightBodyLargeSemiBold
                    .copyWith(color: Theme.of(context).textPrimary(context)),
              ),
              Gap(5.h),
              Row(
                children: [
                  CircleAvatar(
                    radius: 12,
                    backgroundImage: NetworkImage(widget.userNew.authorImage),
                  ),
                  Gap(10.w),
                  Text(
                    widget.userNew.author,
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumSemiBold
                        .copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                  ),
                ],
              ),
            ]),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    isFavorite = !isFavorite; // Đảo trạng thái yêu thích
                  });
                },
                child: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border_outlined,
                  color: isFavorite
                      ? Theme.of(context).primary(context)
                      : Theme.of(context).secondary(context),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
