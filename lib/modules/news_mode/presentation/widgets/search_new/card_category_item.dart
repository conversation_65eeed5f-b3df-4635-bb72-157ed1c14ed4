import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';

class CardCategoryItem extends StatelessWidget {
  final UserNew userNew;
  const CardCategoryItem({super.key, required this.userNew});

  @override
  Widget build(BuildContext context) {
    final sizeHeight = MediaQuery.of(context).size.height;
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Image.network(
                userNew.image,
                fit: BoxFit.cover,
                height: sizeHeight * 0.09,
              ),
            ),
          ),
          Gap(10.w),
          Expanded(
            flex: 4,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start, // Căn trái nội dung
              children: [
                Text(
                  userNew.title,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeRegular
                      .copyWith(
                        color: Theme.of(context).textPrimary(context),
                      ),
                ),
                Gap(8.h),
                Text(
                  userNew.description,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).textSecondary(context),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
