import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class HistorySearchWidget extends StatefulWidget {
  const HistorySearchWidget({super.key});

  @override
  State<HistorySearchWidget> createState() => _HistorySearchWidgetState();
}

class _HistorySearchWidgetState extends State<HistorySearchWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 30.h,
          padding: EdgeInsets.symmetric(horizontal: AppRadius.radius16),
          child: Text('Diddy',
              style: Theme.of(context)
                  .textTheme
                  .lightBodyMediumMedium
                  .copyWith(color: Theme.of(context).textSecondary(context))),
        ),
        Divider(
          color: Theme.of(context).greyScale600(context),
          thickness: 1,
        ),
        Gap(10.h)
      ],
    );
  }
}
