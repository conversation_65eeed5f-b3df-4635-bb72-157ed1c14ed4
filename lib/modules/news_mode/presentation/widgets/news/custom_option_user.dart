import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/showModelBottom_new.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/show_dia_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomOptionWidget extends StatefulWidget {
  const CustomOptionWidget({super.key});

  @override
  State<CustomOptionWidget> createState() => _CustomOptionWidgetState();
}

class _CustomOptionWidgetState extends State<CustomOptionWidget> {
  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 14.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.radius16),
          topRight: Radius.circular(AppRadius.radius16),
        ),
        border: Border.all(
          color:Theme.of(context).greyScale900(context),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          SvgPicture.asset(AppAssets.line126Svg),
          Gap(10.h),
          Text(
            'Option',
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
           Divider(color: Theme.of(context).whitePrimary(context), thickness: 1),
          GestureDetector(
            onTap: () {
              buildCustom(context);
            },
            child: Container(
              height: sizeHeight * 0.05,
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
              child: Row(
                children: [
                  SvgPicture.asset(AppAssets.excludeSvg),
                  Gap(10.w),
                  Text(
                    'Custom',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  const Spacer(),
                  SvgPicture.asset(AppAssets.arrowRightSvg),
                ],
              ),
            ),
          ),
           Divider(color: Theme.of(context).whitePrimary(context), thickness: 1),
          Gap(10.h),
          GestureDetector(
            onTap: () {
              buildReport(context);
            },
            child: Container(
              height: sizeHeight * 0.05,
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
              child: Row(
                children: [
                  SvgPicture.asset(AppAssets.warningSvg),
                  Gap(10.w),
                  Text(
                    'Report this article',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  const Spacer(),
                  SvgPicture.asset(AppAssets.arrowRightSvg),
                ],
              ),
            ),
          ),
           Divider(color: Theme.of(context).whitePrimary(context), thickness: 1),
          const Spacer(),
          GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Text(
                'Close',
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).greyScale600(context),),
              )),
        ],
      ),
    );
  }
}
