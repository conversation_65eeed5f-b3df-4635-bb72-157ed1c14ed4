import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CommentInputWidget extends StatelessWidget {
  final bool switchValue;
  final ValueChanged<bool> onSwitchChanged;

  const CommentInputWidget({
    super.key,
    required this.switchValue,
    required this.onSwitchChanged,
    required this.height,
    required this.width,
  });

  final double height, width;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height * 0.2,
      padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(4.r),
          topRight: Radius.circular(4.r),
        ),
        border: Border.all(
          color: Theme.of(context).alertWhite(context),
          width: 0.2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Anonymous',
                style: Theme.of(context)
                    .textTheme
                    .lightBodyLargeRegular
                    .copyWith(color: Theme.of(context).greyScale500(context)),
              ),
              const Gap(15),
              SizedBox(
                height: 40.h,
                width: 44.w,
                child: CupertinoSwitch(
                  value: switchValue,
                  onChanged: onSwitchChanged,
                  focusColor: Theme.of(context).greyScale500(context)
                ),
              ),
            ],
          ),
          Gap(15.h),
          Row(
            children: [
              SvgPicture.asset(AppAssets.gallerySvg),
              Gap(10.w),
              Expanded(
                child: TextFormField(
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Theme.of(context).alertWhite(context),
                    hintText: 'Enter something here',
                    hintStyle: Theme.of(context)
                        .textTheme
                        .lightBodyMediumRegular
                        .copyWith(color: Theme.of(context).alertWhite(context)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(32),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(32),
                      borderSide: BorderSide(
                          color: Theme.of(context).whitePrimary(context),
                          width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(32),
                      borderSide: BorderSide(
                          color: Theme.of(context).whitePrimary(context),
                          width: 1),
                    ),
                  ),
                ),
              ),
              Gap(5.w),
              InkWell(
                onTap: () {},
                child: Text(
                  'Send',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumSemiBold
                      .copyWith(color: Theme.of(context).informationBase(context),),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
