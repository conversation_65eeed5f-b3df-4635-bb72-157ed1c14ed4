import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class UpgradeUser extends StatefulWidget {
  const UpgradeUser({super.key});

  @override
  State<UpgradeUser> createState() => _UpgradeUserState();
}

class _UpgradeUserState extends State<UpgradeUser> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SvgPicture.asset(
              AppAssets.translateSvg,
              width: 20.w,
              height: 20.h,
            ),
            Gap(10.w),
            Text("Automatic translation",
                style: Theme.of(context).textTheme.lightBodyMediumRegular)
          ],
        ),
        <PERSON>(20.h),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color.fromARGB(0, 17, 17, 27).withOpacity(0.15),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(AppRadius.radius16),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Text will be translated more accurately with a Pro account",
                  style: Theme.of(context).textTheme.lightBodyMediumRegular),
              Gap(10.h),
              OutlinedButton(
                onPressed: () {},
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(362.w, 40.h),
                  side:  BorderSide(
                    color: Theme.of(context).informationBase(context),
                    width: 1,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                ),
                child: Text(
                  "Upgrade to Pro Account",
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeBold
                      .copyWith(color: Theme.of(context).informationBase(context)),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
