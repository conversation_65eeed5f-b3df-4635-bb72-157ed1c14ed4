import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';

class ViewUser extends StatelessWidget {
  UserNew userNew;
  ViewUser({super.key, required this.userNew});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Gap(20.h),
        Row(
          children: [
            ClipOval(
              child: Image.network(
                userNew.authorImage,
                width: 50.h,
                height: 50.h,
                fit: BoxFit.cover,
              ),
            ),
            Gap(10.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(userNew.author,
                    style: Theme.of(context).textTheme.lightBodyLargeSemiBold),
                Text(userNew.title,
                    style: Theme.of(context).textTheme.lightBodyMediumRegular)
              ],
            ),
            const Spacer(),
            ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  minimumSize: Size(110.w, 40.h),
                  backgroundColor: Theme.of(context).secondaryBase(context),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius10),
                  ),
                ),
                child: Row(
                  children: [
                    SvgPicture.asset(
                      AppAssets.iconChatSvg,
                    ),
                    Gap(8.w),
                    Text(
                      "Chat",
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeMedium
                          .copyWith(color: AppColors.white),
                    )
                  ],
                ))
          ],
        ),
        Gap(10.h),
        Container(
          height: 183.h,
          width: double.infinity,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppRadius.radius8),
            child: Image.network(
              userNew.image,
              fit: BoxFit.cover, // Kéo dãn ảnh để lấp đầy khung
            ),
          ),
        ),
        Gap(20.h),
        Text(
          "Healthy living: Diet and exercise Tips & Tool for Success",
          style: Theme.of(context).textTheme.lightBodyXLargeMedium.copyWith(
                fontSize: 24.sp,
              ),
        ),
        Gap(10.h),
        Row(
          children: [
            Text("Sports",
                style: Theme.of(context).textTheme.lightBodyMediumRegular),
            const Spacer(),
            Text("20/09/2023",
                style: Theme.of(context).textTheme.lightBodyMediumRegular)
          ],
        ),
      ],
    );
  }
}
