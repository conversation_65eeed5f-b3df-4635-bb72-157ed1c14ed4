import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/news_mode/fake_data/user_new_data.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/Comment_input_widget.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/comment_item.dart';

class CommentNewWidget extends StatefulWidget {
  final double keyboardHeight;
  bool switchValue;

  CommentNewWidget(
      {super.key, required this.switchValue, required this.keyboardHeight});

  @override
  State<CommentNewWidget> createState() => _CommentNewWidgetState();
}

class _CommentNewWidgetState extends State<CommentNewWidget> {
  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;

    return Padding(
      padding: EdgeInsets.only(
        bottom: widget.keyboardHeight,
      ),
      child: SingleChildScrollView(
        // Bọc toàn bộ widget trong SingleChildScrollView
        child: Column(
          children: [
            const Gap(10),
            SvgPicture.asset(AppAssets.line126Svg),
            const Gap(10),
            Text('Comments (25)',
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold),
            const Gap(10),
            Divider(color: Colors.grey[400], thickness: 1),
            const Gap(10),
            ConstrainedBox(
              constraints: BoxConstraints(maxHeight: sizeHeight * 0.4),
              child: ListView.builder(
                itemCount: 3,
                itemBuilder: (context, index) {
                  var userNew = userNewData[index];
                  return CommentItem(
                    userNew: userNew,
                  );
                },
              ),
            ),
            CommentInputWidget(
              height: sizeHeight,
              width: sizeWidth,
              switchValue: widget.switchValue,
              onSwitchChanged: (value) {
                setState(() {
                  widget.switchValue = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
