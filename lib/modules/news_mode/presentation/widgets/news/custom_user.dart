import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CustomUser extends StatefulWidget {
  const CustomUser({super.key});

  @override
  State<CustomUser> createState() => _CustomUserState();
}

class _CustomUserState extends State<CustomUser> {
  bool isClicked = false;
  String selectedFont = 'Plus Jakarta';
  double currentSliderValue = 0;
  double brightNess = 0;
  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(vertical: 14.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppRadius.radius16),
            topRight: Radius.circular(AppRadius.radius16),
          ),
          border: Border.all(
            color: Theme.of(context).greyScale900(context),
            width: 1,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: SingleChildScrollView(
            child: Column(children: [
              SvgPicture.asset(AppAssets.line126Svg),
              Gap(10.h),
              Text('Custom',
                  style: Theme.of(context).textTheme.lightBodyLargeBold),
               Divider(color: Theme.of(context).whitePrimary(context), thickness: 1),
              SizedBox(
                height: 133.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Font style',
                      style: Theme.of(context)
                          .textTheme
                          .lightHeadingMedium
                          .copyWith(
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    Gap(16.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 5.w),
                      child: Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () => setState(() {
                                selectedFont = 'Plus Jakarta';
                              }),
                              child: Container(
                                width: 184.w,
                                height: 60.h,
                                alignment: Alignment.center,
                                decoration:  BoxDecoration(
                                  color:Theme.of(context).greyScale200(context),
                                  borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(16),
                                      bottomLeft: Radius.circular(16)),
                                ),
                                child: Text(
                                  'Plus Jakarta',
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyXLargeBold
                                      .copyWith(
                                        color: selectedFont == 'Plus Jakarta'
                                            ? Theme.of(context).informationBase(context)
                                            : Theme.of(context).greyScale500(context),
                                      ),
                                ),
                              ),
                            ),
                          ),
                          Gap(15.h),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  selectedFont = 'Bookerly';
                                });
                              },
                              child: Container(
                                width: 184.w,
                                height: 60.h,
                                alignment: Alignment.center,
                                decoration:  BoxDecoration(
                                  color: Theme.of(context).greyScale200(context),
                                  borderRadius: const BorderRadius.only(
                                      topRight: Radius.circular(16),
                                      bottomRight: Radius.circular(16)),
                                ),
                                child: Text(
                                  'Bookerly',
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular
                                      .copyWith(
                                        color: selectedFont == 'Bookerly'
                                            ? Theme.of(context).informationBase(context)
                                            : Theme.of(context).greyScale500(context),
                                        fontFamily: 'Bookerly',
                                      ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Gap(5.w),
                     Divider(color: Theme.of(context).whitePrimary(context), thickness: 1),
                  ],
                ),
              ),
              SizedBox(
                height: 119.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Font size',
                      style: Theme.of(context)
                          .textTheme
                          .lightHeadingMedium
                          .copyWith(
                            fontSize: 22.sp,
                          ),
                    ),
                    Gap(16.h),
                    Container(
                        height: 46.h,
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: Theme.of(context).greyScale200(context),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Text('A',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyMediumRegular),
                            Expanded(
                              child: Slider(
                                value: currentSliderValue,
                                min: 0,
                                max: 100,
                                divisions: 10, // Số lượng divisions

                                onChanged: (double value) {
                                  setState(() {
                                    currentSliderValue = value;
                                  });
                                },
                                activeColor: Colors.blue,
                                inactiveColor: Colors.grey,
                              ),
                            ),
                            Text('A',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyXLargeRegular),
                          ],
                        )),
                    Gap(5.h),
                     Divider(color: Theme.of(context).whitePrimary(context), thickness: 1),
                  ],
                ),
              ),
              SizedBox(
                height: 125.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Brightness',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeBold
                          .copyWith(
                            fontSize: 22.sp,
                          ),
                    ),
                    Gap(16.h),
                    Container(
                        height: 51.h,
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: Theme.of(context).greyScale200(context),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            SvgPicture.asset(AppAssets.sunNewSvg),
                            Expanded(
                              child: Slider(
                                value: brightNess,
                                min: 0,
                                max: 100,
                                divisions: 10,

                                onChanged: (double value) {
                                  setState(() {
                                    brightNess = value;
                                  });
                                },
                                activeColor: Colors.blue,
                                inactiveColor: Colors.grey,
                              ),
                            ),
                            SvgPicture.asset(
                              AppAssets.sunNewSvg,
                              color: Theme.of(context).greyScale800(context),
                            ),
                          ],
                        )),
                    Gap(5.h),
                     Divider(color: Theme.of(context).whitePrimary(context), thickness: 1),
                  ],
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  context.pop();
                },
                child: Text(
                  'Close',
                  style:
                      Theme.of(context).textTheme.lightBodyXLargeBold.copyWith(
                            color:Theme.of(context).greyScale600(context),
                          ),
                ),
              ),
            ]),
          ),
        ));
  }
}
