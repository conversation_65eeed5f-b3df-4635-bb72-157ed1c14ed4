import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';

class CommentItem extends StatelessWidget {
  final UserNew userNew;
  const CommentItem({super.key, required this.userNew});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipOval(
            child: Image.network(
              userNew.image,
              width: 40,
              height: 40,
              fit: BoxFit.cover,
            ),
          ),
          const Gap(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      userNew.author,
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyXLargeBold
                          .copyWith(
                              color: Theme.of(context).textPrimary(context)),
                    ),
                    const Gap(10),
                    Text(
                      '@${(userNew.author).toLowerCase()}',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyMediumRegular
                          .copyWith(
                              color: Theme.of(context).textSecondary(context)),
                    ),
                    const Gap(10),
                    Text(
                      '1h',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyMediumRegular
                          .copyWith(
                              color: Theme.of(context).textSecondary(context)),
                    ),
                  ],
                ),
                const Gap(10),
                Text(
                  userNew.description,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeRegular
                      .copyWith(color: Theme.of(context).textPrimary(context)),
                  maxLines: 2,
                ),
                const Gap(10),
                Text(
                  'Reply',
                  style: Theme.of(context).textTheme.lightBodyLargeMedium,
                ),
                const Gap(10),
                Row(
                  children: [
                    SvgPicture.asset(AppAssets.translateSvg),
                    const Gap(10),
                    Text("Dịch tự động",
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular)
                  ],
                ),
              ],
            ),
          ),
          Gap(10.h),
          Column(
            children: [
              const Gap(90),
              IconButton(
                onPressed: () {},
                icon: Icon(
                  Icons.more_horiz,
                  color: Theme.of(context).textPrimary(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
