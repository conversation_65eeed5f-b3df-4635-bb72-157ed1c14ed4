import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';

class ReportUser extends StatefulWidget {
  const ReportUser({super.key});

  @override
  State<ReportUser> createState() => _ReportUserState();
}

class _ReportUserState extends State<ReportUser> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.99,
      height: 454.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        border: Border.all(
          color: Theme.of(context).greyScale500(context),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).greyScale900(context),
            offset: const Offset(0, 6),
            blurRadius: 6,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Are you sure?',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.lightBodyXLargeBold.copyWith(
                  fontSize: 22.sp,
                ),
          ),
          SizedBox(height: 8.h),
          Text('When you click confirm, it means you want to report this news',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.lightBodyLargeRegular),
          Gap(16.h),
          Align(
            alignment: Alignment.centerLeft,
            child: Text('Reason for report?',
                style: Theme.of(context).textTheme.lightBodyXLargeBold),
          ),
          Gap(8.h),
          Row(
            children: [
              SvgPicture.asset(AppAssets.rrInfoSvg),
              Gap(8.w),
              Expanded(
                child: Text(
                    'We will delete this post if there are many similar reasons',
                    style: Theme.of(context).textTheme.lightBodySmallRegular),
              ),
            ],
          ),
          Gap(8.h),
          Expanded(
            child: Container(
              height: 120.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppRadius.radius16),
                color:Theme.of(context).greyScale500(context),
              ),
              child: TextFormField(
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'Enter reason refusal',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(12.w),
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                ),
              ),
            ),
          ),
          Gap(16.h),
          CustomeButtonDating(
            onTap: () {
              // Handle report logic
              context.pop();
            },
            text: 'Report',
            colorBackground:Theme.of(context).primary(context),
            colorText: AppColors.white,
            height: 40.h,
            width: double.infinity,
          ),
          SizedBox(height: 10.h),
          GestureDetector(
            onTap: () {
             context.pop();
            },
            child: Text(
              'No',
              style:
                  Theme.of(context).textTheme.lightBodyLargeSemiBold.copyWith(
                        color:Theme.of(context).backgroundRed(context),
                      ),
            ),
          ),
        ],
      ),
    );
  }
}
