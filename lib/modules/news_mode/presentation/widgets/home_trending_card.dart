import 'package:flutter/material.dart';

class HomeTrendingCard extends StatelessWidget {
  String imageUrl;
  String location;
  String description;
  String avatarUrl;
  String time;
  String name;

  HomeTrendingCard.name(
      {super.key, required this.imageUrl,
      required this.location,
      required this.description,
      required this.avatarUrl,
      required this.time,
      required this.name});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 280,
      width: 380,
      margin: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.network(
              imageUrl,
              width: 364,
              height: 183,
              fit: BoxFit.fill,
            ),
          ),
          Text(
            location,
            style: const TextStyle(fontFamily: "Plus Jakarta Sans", fontSize: 13),
          ),
          Text(
            description,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Row(
            children: [
              CircleAvatar(
                radius: 10,
                backgroundImage: NetworkImage(avatarUrl),
              ),
              const SizedBox(
                width: 10,
              ),
              Text(
                name,
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(
                width: 100,
              ),
              const Icon(
                Icons.access_time,
                size: 14,
              ),
              Text(time),
              const Spacer(),
              const Icon(
                Icons.more_horiz,
                size: 14,
              )
            ],
          )
        ],
      ),
    );
  }
}
