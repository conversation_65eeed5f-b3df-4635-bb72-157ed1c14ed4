import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class AuthorTile extends StatelessWidget {
  String avatarUrl;
  String name;
  String career;

  AuthorTile(this.avatarUrl, this.name, this.career, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              avatarUrl,
              height: 44,
              width: 44,
              fit: BoxFit.fill,
            ),
          ),
          Gap(8.h),
          Column(
            children: [
              Text(
                name,
                style: Theme.of(context).textTheme.lightBodyMediumBold,
              ),
              Text(
                career,
                style: Theme.of(context).textTheme.lightBodySmallRegular,
              )
            ],
          ),
          const Spacer(),
          Si<PERSON><PERSON><PERSON>(
            width: 111,
            height: 36,
            child: ElevatedButton(
                style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    backgroundColor: Colors.red),
                onPressed: () {},
                child: Row(
                  children: [
                    Icon(
                      Icons.message_outlined,
                      color: Colors.white,
                      size: 15,
                    ),
                    Text(
                      "Contact",
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallRegular
                          .copyWith(color: Colors.white),
                    )
                  ],
                )),
          )
        ],
      ),
    );
  }
}
