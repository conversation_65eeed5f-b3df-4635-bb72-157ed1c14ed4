import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CardNews extends StatelessWidget {
  const CardNews(
      {super.key,
      required this.title,
      required this.description,
      required this.author,
      required this.avatar});

  final String title, description, author, avatar;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: MediaQuery.of(context).size.height * 0.2,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            image: DecorationImage(
              image: NetworkImage(
                  'https://img.tripi.vn/cdn-cgi/image/width=700,height=700/https://gcs.tripi.vn/public-tripi/tripi-feed/img/474113Wtw/anh-thac-nuoc-dep-nhat_014852764.jpg'),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16)),
            border: Border.all(
                color: Theme.of(context).greyScale300(context), width: 1),
          ),
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                          color: Theme.of(context).textSecondary(context))),
              Gap(10.h),
              Text(description,
                  style: Theme.of(context).textTheme.lightBodyLargeSemiBold),
              Gap(10.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  ClipOval(
                    child: Image.network(
                      avatar,
                      width: 30.w,
                      height: 30.h,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Gap(10.w),
                  Text(author,
                      style:
                          Theme.of(context).textTheme.lightBodyMediumRegular),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}
