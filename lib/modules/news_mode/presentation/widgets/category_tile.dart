import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';

class CategoryTile extends StatelessWidget {
  String imageUrl;
  String title;
  String description;

  CategoryTile(this.imageUrl, this.title, this.description, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.network(
              imageUrl,
              height: 70.h,
              width: 70.w,
              fit: BoxFit.fill,
            ),
          ),
          Gap(10.w),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title,
                  style: Theme.of(context).textTheme.lightBodyLargeBold),
              Text(
                description,
                style: Theme.of(context).textTheme.lightBodyMediumRegular,
              )
            ],
          )),
          Column(
            children: [
              Gap(10.h),
              <PERSON><PERSON><PERSON><PERSON>(
                width: 66.w,
                height: 34.h,
                child: OutlinedButton(
                  onPressed: () {},
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    padding: EdgeInsets.zero, // Remove default padding
                  ),
                  child: Text(
                    "View",
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyXSmallRegular
                        .copyWith(
                            color: Theme.of(context).backgroundRed(context)), // Adjust font size
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
