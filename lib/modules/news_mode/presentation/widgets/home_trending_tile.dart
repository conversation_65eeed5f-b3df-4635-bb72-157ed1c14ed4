import 'package:flutter/material.dart';

class HomeTrendingTile extends StatelessWidget {
  String imageUrl;
  String location;
  String description;
  String avatarUrl;
  String time;
  String name;

  HomeTrendingTile(this.imageUrl, this.location, this.description,
      this.avatarUrl, this.time, this.name, {super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Container(
        margin: const EdgeInsets.all(10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Image.network(
                imageUrl,
                width: 96,
                height: 96,
                fit: BoxFit.fill,
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
                child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      location,
                      style: const TextStyle(
                          fontFamily: "Plus Jakarta Sans", fontSize: 13),
                    ),
                    const Spacer(),
                    const Icon(Icons.favorite_border)
                  ],
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                ),
                Row(
                  children: [
                    CircleAvatar(
                      radius: 10,
                      backgroundImage: NetworkImage(avatarUrl),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      name,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      width: 50,
                    ),
                    const Icon(
                      Icons.access_time,
                      size: 14,
                    ),
                    Text(time),
                    const Spacer(),
                    PopupMenuButton<String>(
                      onSelected: (String item) {
                        print('Selected: $item');
                      },
                      itemBuilder: (BuildContext context) {
                        return [
                          const PopupMenuItem<String>(
                            value: 'Edit',
                            child: Row(
                              children: [
                                Icon(Icons.mode_edit_outline_outlined),
                                Text("Edit")
                              ],
                            ),
                          ),
                          const PopupMenuItem<String>(
                            value: 'Edit',
                            child: Row(
                              children: [
                                Icon(Icons.mode_edit_outline_outlined),
                                Text("Delete")
                              ],
                            ),
                          ),
                        ];
                      },
                      child: const Icon(Icons.more_horiz, size: 14),
                    ),
                  ],
                )
              ],
            ))
          ],
        ),
      ),
    );
  }
}
