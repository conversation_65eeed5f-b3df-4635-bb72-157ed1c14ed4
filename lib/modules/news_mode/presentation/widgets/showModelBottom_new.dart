import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/comment_new.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/custom_option_user.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/custom_user.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Future<void> buildCustom(BuildContext context) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: AppColors.white,
    barrierColor: Theme.of(context).greyScale700(context),
    isScrollControlled: true,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          return FractionallySizedBox(
              heightFactor: 0.65.h, widthFactor: 1, child: const CustomUser());
        },
      );
    },
  );
}

Future<void> showComment(BuildContext context, bool switchValue) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: AppColors.white,
    barrierColor: Colors.transparent,
    builder: (context) {
      return StatefulBuilder(builder: (context, setState) {
        return FractionallySizedBox(
          heightFactor: 0.7,
          child: LayoutBuilder(
            builder: (context, constraints) {
              double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
              return CommentNewWidget(
                  switchValue: switchValue, keyboardHeight: keyboardHeight);
            },
          ),
        );
      });
    },
  );
}

Future<void> buildOption(BuildContext context) {
  return showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.white,
      barrierColor: Theme.of(context).greyScale700(context),
      isScrollControlled: true,
      builder: (context) {
        return const FractionallySizedBox(
          heightFactor: 0.35,
          widthFactor: 1,
          child: CustomOptionWidget(),
        );
      });
}
