import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';


Future<void> showDeleteDialog(BuildContext context) {
  return showDialog(
    context: context,
    barrierDismissible: true,
    barrierColor: Theme.of(context).greyScale700(context),
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.w),
        ),
        contentPadding: EdgeInsets.zero,
        content: Container(
          width: 273.w,
          height: 144.h,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(16.w),
            border: Border(
              top: BorderSide(
                color: Theme.of(context).greyScale500(context),
                width: 1.w,
              ),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Gap(10.h),
                      Container(
                        width: 233.w,
                        height: 25.h,
                        child: Text('Do you want to delete this article?',
                            textAlign: TextAlign.center,
                            style:
                                Theme.of(context).textTheme.lightBodyLargeBold),
                      ),
                      Gap(10.h),
                    ],
                  )),
              Container(
                width: 233.w,
                height: 40.h,
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).greyScale500(context),
                          foregroundColor:Theme.of(context).greyScale900(context),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.w),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          textAlign: TextAlign.center,
                          style:
                              Theme.of(context).textTheme.lightBodyLargeMedium,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).backgroundRed(context),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.w),
                          ),
                        ),
                        child: Text(
                          'Block',
                          textAlign: TextAlign.center,
                          style:
                              Theme.of(context).textTheme.lightBodyLargeMedium,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      );
    },
  );
}

