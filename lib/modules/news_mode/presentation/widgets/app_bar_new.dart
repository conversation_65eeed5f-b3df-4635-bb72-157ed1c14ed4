import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class AppBarNew extends StatelessWidget implements PreferredSizeWidget {
  final String? title; // Cho phép null
  final Widget? icon; // Cho phép null
  final VoidCallback? onBackPressed;
  final Function()? onPressed;

  const AppBarNew({
    super.key,
    this.title,
    this.icon,
    this.onBackPressed,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: onPressed ?? () => (context).push(RouteName.newPage)),
      title: title != null
          ? Text(
              title!,
              style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                    color:Theme.of(context).whitePrimary(context)
                  ),
            )
          : null,
      centerTitle: true,
      actions: [
        if (icon != null)
          IconButton(
            icon: icon!,
            onPressed: () {
              onBackPressed!();
            },
          ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(50);
}
