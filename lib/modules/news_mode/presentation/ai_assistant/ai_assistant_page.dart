import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/app_bar_new.dart';

class AiAssistantPage extends StatelessWidget {
  const AiAssistantPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarNew(
          title: 'AI assistant writes for you',
          onPressed: () {
            context.go(RouteName.newPage);
          }),
      body: AiAssistantWidget(),
    );
  }
}

class AiAssistantWidget extends StatefulWidget {
  const AiAssistantWidget({super.key});

  @override
  State<AiAssistantWidget> createState() => _AiAssistantWidgetState();
}

class _AiAssistantWidgetState extends State<AiAssistantWidget> {
  List<String> availableTags = [
    "Health",
    "Technology",
    "Art",
    "Politics",
    "Sport",
    "Travel",
    "Money"
  ];

  late List<bool> selectedTags;

  @override
  void initState() {
    super.initState();
    selectedTags = List.generate(availableTags.length, (index) => false);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Container(
            child: ListView.separated(
              itemCount: 3,
              separatorBuilder: (context, index) => Gap(10.h),
              itemBuilder: (context, index) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListTile(
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(160),
                        child: Image.network(
                          'https://wellavn.com/wp-content/uploads/2024/11/hinh-nen-gai-xinh-full-hd-cho-iphone-1.jpeg',
                          width: 20.w,
                          height: 20.h,
                        ),
                      ),
                      title: Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.backgroundColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.padding16,
                            vertical: AppSpacing.padding10h,
                          ),
                          child: Text(
                            'What Category do you want to write about?',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyMediumRegular,
                          ),
                        ),
                      ),
                    ),
                    Gap(8.h),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.9,
                      padding: EdgeInsets.only(
                        left: AppSpacing.padding32,
                      ),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 10,
                        alignment: WrapAlignment.end,
                        children:
                            List.generate(availableTags.length, (tagIndex) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedTags[tagIndex] = !selectedTags[
                                    tagIndex]; // Cập nhật trạng thái cho thẻ đã chọn
                              });
                            },
                            child: Chip(
                              label: Text(availableTags[tagIndex]),
                              shape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10)),
                              ),
                              labelStyle: selectedTags[tagIndex]
                                  ? Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeMedium
                                      .copyWith(
                                        color: AppColors.white,
                                      )
                                  : Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeMedium
                                      .copyWith(
                                        color: Theme.of(context).secondaryBase(context),
                                      ),
                              side: selectedTags[tagIndex]
                                  ? const BorderSide(
                                      color: Colors.transparent, width: 1)
                                  :  BorderSide(
                                      color: Theme.of(context).secondaryBase(context), width: 1),
                              backgroundColor: selectedTags[tagIndex]
                                  ? Theme.of(context).secondaryBase(context)
                                  : AppColors.white,
                            ),
                          );
                        }),
                      ),
                    ),
                    Gap(20.h),
                    ListTile(
                      leading: ClipRRect(
                        borderRadius: BorderRadius.circular(160),
                        child: Image.network(
                          'https://wellavn.com/wp-content/uploads/2024/11/hinh-nen-gai-xinh-full-hd-cho-iphone-1.jpeg',
                          width: 20.w,
                          height: 20.h,
                        ),
                      ),
                      title: Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.backgroundColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSpacing.padding16,
                              vertical: AppSpacing.padding10h,
                            ),
                            child: Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Image.network(
                                    'https://wellavn.com/wp-content/uploads/2024/11/hinh-nen-gai-xinh-full-hd-cho-iphone-1.jpeg',
                                    width: MediaQuery.of(context).size.width *
                                        0.25,
                                    height: MediaQuery.of(context).size.height *
                                        0.12,
                                  ),
                                ),
                                Gap(10.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Gap(10.h),
                                      Text(
                                        'Europe',
                                        style: Theme.of(context)
                                            .textTheme
                                            .lightBodyMediumRegular
                                            .copyWith(
                                              color: Theme.of(context).greyScale600(context),
                                            ),
                                      ),
                                      Gap(10.h),
                                      Text(
                                        'Lorem ipsum dolar sit amet sample text goes here',
                                        style: Theme.of(context)
                                            .textTheme
                                            .lightBodyLargeSemiBold,
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            )),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
        Container(
          height: MediaQuery.of(context).size.height * 0.18,
          width: MediaQuery.of(context).size.width,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: AppColors.backgroundColor,
          ),
          child: Column(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height * 0.07,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  color:Theme.of(context).greyScale100(context),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                        child: TextFormField(
                      decoration: InputDecoration(
                        hintText: 'Message assistant LION',
                        hintStyle: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(
                              color: Theme.of(context).greyScale700(context),
                            ),
                        hoverColor: Colors.transparent,
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                      ),
                    )),
                    Gap(5.w),
                    SvgPicture.asset(AppAssets.sendMessageSvg),
                  ],
                ),
              ),
              Gap(20.h),
              Row(
                children: [
                  SvgPicture.asset(AppAssets.gallerySvg),
                  Gap(15.w),
                  SvgPicture.asset(AppAssets.attachFileSvg),
                  Gap(15.w),
                  Text(
                    '0/2500',
                    style: Theme.of(context).textTheme.lightBodyMediumMedium,
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}
