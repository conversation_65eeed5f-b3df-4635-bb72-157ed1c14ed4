import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/modules/news_mode/fake_data/user_new_data.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/app_bar_new.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/review/card_review_item.dart';

class ReviewNews extends StatefulWidget {
  const ReviewNews({super.key});

  @override
  State<ReviewNews> createState() => _ReviewNewsState();
}

class _ReviewNewsState extends State<ReviewNews> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarNew(
        title: 'Review your posts',
        onPressed: () {
          (context).push(RouteName.newPage);
        },
      ),
      body: Column(
        children: [
          Gap(30.h),
          Expanded(
            child: ListView.separated(
                padding: EdgeInsets.only(top: 16.h, right: 16.w, left: 16.w),
                itemBuilder: (context, index) {
                  var obj = userNewData[index];
                  return CardReviewItem(
                    userNew: obj,
                  );
                },
                separatorBuilder: (context, index) => Gap(20.h),
                itemCount: userNewData.length),
          ),
        ],
      ),
    );
  }
}
