import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/app_bar_new.dart';

class EditNewScreen extends StatelessWidget {
  UserNew userNew;
  EditNewScreen({super.key, required this.userNew});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarNew(
        title: 'Edit new',
        onPressed: () {
          (context).push(RouteName.newPage);
        },
      ),
      body: EditNewWidget(
        userNew: userNew,
      ),
      bottomNavigationBar: BottomEditWidget(),
    );
  }
}

class EditNewWidget extends StatefulWidget {
  UserNew userNew;
  EditNewWidget({super.key, required this.userNew});

  @override
  State<EditNewWidget> createState() => _EditNewWidgetState();
}

class _EditNewWidgetState extends State<EditNewWidget> {
  @override
  Widget build(BuildContext context) {
    TextEditingController _controllerTextField = TextEditingController();

    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;
    String currentText = 'Sports';
    final List<String> listText = ['Sports', 'Health', 'Technology'];

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(25.h),
            Stack(
              children: [
                Container(
                  height: sizeHeight * 0.25,
                  width: sizeWidth,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                    child: Image.network(
                      widget.userNew.image,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 15.h,
                  right: 15.w,
                  child: Center(
                    child: SvgPicture.asset(
                      AppAssets.noteEditSvg,
                    ),
                  ),
                ),
              ],
            ),
            Gap(20.h),
            buildTextFormField(
                'News title',
                'Healthy living: Diet and exercise Tips & Tools for Success',
                context,
                16),
            Gap(20.h),
            TextFormField(
              decoration: InputDecoration(
                floatingLabelBehavior: FloatingLabelBehavior.always,
                labelText: 'Category',
                labelStyle: Theme.of(context)
                    .textTheme
                    .lightBodyMediumRegular
                    .copyWith(color: Theme.of(context).textSecondary(context)),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius10),
                  borderSide: BorderSide(color: Colors.grey, width: 1),
                ),
                suffixIcon: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: DropdownButton<String>(
                    value: currentText,
                    onChanged: (String? newValue) {
                      setState(() {
                        currentText = newValue!;
                        _controllerTextField.text =
                            currentText; // Cập nhật giá trị cho TextFormField
                      });
                    },
                    dropdownColor: Colors.white,
                    isExpanded: true,
                    icon: SvgPicture.asset(AppAssets.arrowDownSvg),
                    underline: const SizedBox(),
                    items:
                        listText.map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value,
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeRegular),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
            Gap(20.h),
            buildTextFormField('Country', 'England', context, 16),
            Gap(20.h),
            buildTextFormField(
                'Artlcle source',
                'https://edition.cnn.com/2024/11/10/europe/russia-north-korea-forces-prepared-kursk-ukraine',
                context,
                16),
            Gap(20.h),
            buildTextFormField(
                'Content',
                'Ukrainian President Volodymyr Zelensky has accused European countries that continue to buy Russian oil of "earning their money in other people\'s blood".'
                    '\n'
                    '\n'
                    'In an interview with the BBC, President Zelensky singled out Germany and Hungary, accusing them of blocking efforts to embargo energy sales, from which Russia stands to make up to £250bn (\$326bn) this year.'
                    '\n'
                    '\n'
                    'There has been a growing frustration among Ukraine\'s leadership with Berlin, which has backed some sanctions against Russia but so far resisted calls to back tougher action on oil sales.',
                context,
                16,
                checkMaxLine: true)
          ],
        ),
      ),
    );
  }

  Widget buildTextFormField(
      String title, String hintText, BuildContext context, double fontSize,
      {bool checkMaxLine = false}) {
    return TextFormField(
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.always,
        suffixIcon: SvgPicture.asset(
          AppAssets.editSvg,
        ),
        suffixIconConstraints: BoxConstraints(
          maxHeight: 40.h,
          maxWidth: 40.w,
        ),
        hintText: hintText,
        labelText: title,
        labelStyle: Theme.of(context)
            .textTheme
            .lightBodyMediumRegular
            .copyWith(color: Theme.of(context).textSecondary(context)),
        hintStyle: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
              fontSize: fontSize.sp,
            ),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
            borderSide: BorderSide(color: Colors.grey, width: 1)),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
            borderSide: BorderSide(color: Colors.grey, width: 1)),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 15.h),
        alignLabelWithHint: true,
      ),
      maxLines: checkMaxLine ? null : 5,
      minLines: 1,
      keyboardType: TextInputType.multiline,
      textAlignVertical: TextAlignVertical.top,
      textInputAction: TextInputAction.done,
      onFieldSubmitted: (value) {
        FocusScope.of(context).requestFocus(FocusNode());
      },
    );
  }
}

class BottomEditWidget extends StatefulWidget {
  const BottomEditWidget({super.key});

  @override
  State<BottomEditWidget> createState() => _BottomEditWidgetState();
}

class _BottomEditWidgetState extends State<BottomEditWidget> {
  final QuillController _controller = QuillController.basic();
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget buildButton(String title, Color backgroundColor, Color textColor,
      Color? borderColors) {
    return ElevatedButton(
      onPressed: () {},
      child: Text(
        title,
        textAlign: TextAlign.center,
        style: Theme.of(context)
            .textTheme
            .lightBodyLargeMedium
            .copyWith(color: textColor),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        minimumSize: Size(191.w, 40.h),
        side: borderColors != null
            ? BorderSide(color: borderColors, width: 1.w)
            : BorderSide.none,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.radius10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;
    return Container(
      height: sizeHeight * 0.15,
      width: sizeWidth,
      padding: EdgeInsets.only(
        top: 32.h,
        bottom: 40.h,
        left: 16.w,
        right: 16.w,
      ),
      child: Row(
        children: [
          Expanded(
              child: buildButton(
            'Save draff',
            AppColors.white,
                Theme.of(context).whitePrimary(context),
                Theme.of(context).whitePrimary(context) ,
          )),
          Gap(12.w),
          Expanded(
              child: buildButton(
                  'Post', Theme.of(context).primary(context), AppColors.white, null)),
        ],
      ),
    );
  }
}
