import 'dart:io';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/image_choice.dart';
import 'package:multime_app/modules/news_mode/presentation/post/bloc/post_bloc.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/card_text_field_post.dart';

import 'package:multime_app/modules/news_mode/presentation/widgets/app_bar_new.dart';

class PostRequestNews extends StatefulWidget {
  const PostRequestNews({super.key});

  @override
  State<PostRequestNews> createState() => _PostRequestNewsState();
}

class _PostRequestNewsState extends State<PostRequestNews> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBarNew(
        title: 'Share news',
        onPressed: () {
          (context).push(RouteName.newPage);
        },
      ),
      body: BodyPostNews(),
      bottomNavigationBar: BottomEditWidget(),
    );
  }
}

class BodyPostNews extends StatefulWidget {
  const BodyPostNews({super.key});

  @override
  State<BodyPostNews> createState() => _BodyPostNewsState();
}

class _BodyPostNewsState extends State<BodyPostNews> {
  TextEditingController txMobile = TextEditingController();
  late CountryCode countryCode;

  final QuillController _controller = QuillController.basic();
  String? selectedValue;
  bool isShow = false;
  TextEditingController _controllerTextField = TextEditingController();
  String currentText = 'Sports';
  final List<String> listText = ['Sports', 'Health', 'Technology'];
  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      child: Column(
        children: [
          Gap(10.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<PostNewsBloc, PostNewsState>(
                  builder: (context, state) {
                    return Stack(
                      children: [
                        GestureDetector(
                          onTap: () async {
                            final pickedPath =
                                await showImageChoices(context: context);
                            if (pickedPath.isNotEmpty) {
                              context
                                  .read<PostNewsBloc>()
                                  .add(AddImageNewsEvent(pickedPath));
                            }
                          },
                          child: DottedBorder(
                            borderType: BorderType.RRect,
                            strokeWidth: 1,
                            radius: const Radius.circular(10),
                            dashPattern: const [6, 6, 6, 6],
                            child: SizedBox(
                              height: sizeHeight * 0.22,
                              width: sizeWidth,
                              child: state.image.isNotEmpty
                                  ? Image.file(
                                      File(state.image),
                                      height: 200.h,
                                      width: double.infinity,
                                      fit: BoxFit.cover,
                                    )
                                  : _buildPlaceholder(
                                      AppAssets.uploadImageSvg,
                                      'Click here to upload file',
                                      'Supports image formats .png, .jpg, .jpeg',
                                    ),
                            ),
                          ),
                        ),
                        if (state.image.isNotEmpty) ...[
                          Positioned(
                            right: 5,
                            top: 5,
                            child: Container(
                              height: 24.h,
                              width: 24.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Theme.of(context).whitePrimary(context),
                                borderRadius: BorderRadius.circular(99),
                              ),
                              child: IconButton(
                                onPressed: () {
                                  context
                                      .read<PostNewsBloc>()
                                      .add(const RemoveImageNewsEvent(''));
                                },
                                icon: const Icon(Icons.close),
                                iconSize: 16,
                                padding: EdgeInsets.zero,
                                constraints:
                                    const BoxConstraints(), // Loại bỏ ràng buộc kích thước mặc định
                              ),
                            ),
                          ),
                        ],
                      ],
                    );
                  },
                ),
                Gap(20.h),
                const CardTextFieldPost(
                  title: 'New title',
                  icon: AppAssets.editSvg,
                  checkMaxLine: false,
                ),
                Gap(20.h),
                TextFormField(
                  decoration: InputDecoration(
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    labelText: 'Category',
                    labelStyle: Theme.of(context)
                        .textTheme
                        .lightBodyMediumRegular
                        .copyWith(
                            color: Theme.of(context).textSecondary(context)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius10),
                      borderSide: BorderSide(
                          color: Theme.of(context).greyScale200(context),
                          width: 1),
                    ),
                    suffixIcon: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: DropdownButton<String>(
                        value: currentText,
                        onChanged: (String? newValue) {
                          setState(() {
                            currentText = newValue!;
                            _controllerTextField.text = currentText;
                          });
                        },
                        dropdownColor: Colors.white,
                        isExpanded: true,
                        icon: SvgPicture.asset(AppAssets.arrowDownSvg),
                        underline: const SizedBox(),
                        items: listText
                            .map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ),
                Gap(20.h),
                TextFormField(
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 10.w),
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    labelText: 'Country',
                    labelStyle: Theme.of(context)
                        .textTheme
                        .lightBodyMediumRegular
                        .copyWith(
                            color: Theme.of(context).textSecondary(context)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius10),
                      borderSide: BorderSide(
                          color: Theme.of(context).greyScale200(context),
                          width: 1),
                    ),
                    suffixIcon: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: CountryCodePicker(
                        // barrierColor: Colors.transparent,
                        backgroundColor: Colors.transparent,
                        onChanged: (code) {
                          setState(() {
                            countryCode = code;
                          });
                        },
                        showFlag: true,
                        builder: (countryCode) {
                          return Container(
                            width: sizeWidth,
                            height: sizeHeight * 0.05,
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius10)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Image.asset(
                                      countryCode!.flagUri!,
                                      package: 'country_code_picker',
                                    ),
                                    Gap(10.w),
                                    Text("${countryCode.name}",
                                        style: Theme.of(context)
                                            .textTheme
                                            .lightBodyLargeRegular
                                            .copyWith(
                                                color: Theme.of(context)
                                                    .textPrimary(context))),
                                  ],
                                ),
                                SvgPicture.asset(AppAssets.arrowDownSvg)
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
                Gap(20.h),
                const CardTextFieldPost(
                  title: 'Article source',
                  icon: AppAssets.editSvg,
                  checkMaxLine: false,
                ),
                Gap(20.h),
                const CardTextFieldPost(
                  title: 'Content',
                  icon: AppAssets.editSvg,
                  checkMaxLine: false,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholder(String imagePath, String title, String subtitle) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize:
          MainAxisSize.min, // Đảm bảo Column chỉ chiếm không gian cần thiết
      children: [
        SvgPicture.asset(imagePath),
        Gap(10.h),
        Text(
          title,
          style: Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                color: Theme.of(context).textPrimary(context),
              ),
        ),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.lightBodySmallBold.copyWith(
                color: Theme.of(context).textSecondary(context),
              ),
        )
      ],
    );
  }
}

class BottomEditWidget extends StatelessWidget {
  const BottomEditWidget({super.key});

  @override
  Widget build(BuildContext context) {
    Widget buildButton(String title, Color backgroundColor, Color textColor,
        Color? borderColors) {
      return ElevatedButton(
        onPressed: () {},
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: Theme.of(context)
              .textTheme
              .lightBodyLargeMedium
              .copyWith(color: textColor),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          side: borderColors != null
              ? BorderSide(color: borderColors, width: 1.w)
              : BorderSide.none,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
          ),
        ),
      );
    }

    return Container(
      height: MediaQuery.of(context).size.height * 0.12,
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
      decoration: const BoxDecoration(
        color: AppColors.white,
      ),
      child: Row(
        children: [
          Expanded(
              child: buildButton(
                  'Save draff',
                  Theme.of(context).whitePrimary(context),
                  Theme.of(context).blackPrimary(context),
                  Theme.of(context).textPrimary(context))),
          Gap(12.w),
          Expanded(
              child: buildButton('Post', Theme.of(context).primary(context),
                  Theme.of(context).whitePrimary(context), null)),
        ],
      ),
    );
  }
}
