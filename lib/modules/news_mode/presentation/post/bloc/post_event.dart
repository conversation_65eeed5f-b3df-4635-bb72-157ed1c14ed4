
part of 'post_bloc.dart';
abstract class PostNewsEvent extends Equatable {
  const PostNewsEvent();

  @override
  List<Object> get props => [];
}

class AddImageNewsEvent extends PostNewsEvent {
  final String imagePath;
  const AddImageNewsEvent(this.imagePath);

  @override
  List<Object> get props => [imagePath];
}
class RemoveImageNewsEvent extends PostNewsEvent {
  final String imagePath;
  const RemoveImageNewsEvent(this.imagePath);

  @override
  List<Object> get props => [imagePath];
}