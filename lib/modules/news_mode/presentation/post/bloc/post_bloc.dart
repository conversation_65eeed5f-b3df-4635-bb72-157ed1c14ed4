import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'post_event.dart';
part 'post_state.dart';

class PostNewsBloc extends Bloc<PostNewsEvent, PostNewsState>{
  PostNewsBloc() : super(PostNewsState.initial()){
    on<AddImageNewsEvent>(_onAddImageNewsEvent);
    on<RemoveImageNewsEvent>(_onRemoveImageNewsEvent);
  }

  Future<void> _onAddImageNewsEvent(AddImageNewsEvent event, Emitter<PostNewsState> emit) async {
    emit(state.copyWith(image: event.imagePath));
  }

  Future<void> _onRemoveImageNewsEvent(RemoveImageNewsEvent event, Emitter<PostNewsState> emit) async {
    emit(state.copyWith(image: ''));
  }


}