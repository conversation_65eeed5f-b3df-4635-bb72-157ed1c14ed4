part of 'post_bloc.dart';

 class PostNewsState extends Equatable {
   final String image;
  const PostNewsState({required this.image});

  factory PostNewsState.initial() {
    return const PostNewsState(
     image: '',
    );
  }

  PostNewsState copyWith({
    String? image,
  }) {
    return PostNewsState(
      image: image ?? this.image,
    );
  }

  @override
  List<Object> get props => [image];
}
