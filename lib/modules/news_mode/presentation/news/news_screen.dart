import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';
import 'package:multime_app/modules/news_mode/fake_data/user_new_data.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/upgrade_user.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/view_user.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/app_bar_new.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/home_page/card_latest.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/showModelBottom_new.dart';

class NewsScreen extends StatelessWidget {
  UserNew userNew;
  NewsScreen({super.key, required this.userNew});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarNew(
        title: 'News',
        onPressed: () {
          (context).push(RouteName.newPage);
        },
        icon:  Icon(
          Icons.more_vert,
          color: Theme.of(context).greyScale600(context),
        ),
        onBackPressed: () {
          buildOption(context);
        },
      ),
      // AppBarNew(
      //   title: 'New',
      //   onPressed: () {
      //     Navigator.of(context)
      //         .pushNamedAndRemoveUntil(RouteName.newPage, (route) => false);
      //   },
      // ),
      body: NewsWidget(userNew: userNew),
      bottomNavigationBar: const BottomNewsWidget(),
    );
  }
}

class NewsWidget extends StatelessWidget {
  UserNew userNew;

  NewsWidget({super.key, required this.userNew});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            ViewUser(userNew: userNew),
            Gap(20.h),
            UpgradeUser(),
            Gap(10.h),
            Text(
              "Ukrainian President Volodymyr Zelensky has accused European countries that continue to buy Russian oil of "
              "'earning their money in other people's blood'\n"
              "\n"
              "In an interview with the BBC, President Zelensky singled out Germany and Hungary, accusing them of blocking efforts to embargo energy sales, from which Russia stands to make up to £250bn (\$326bn) this year.\n"
              "\n"
              "There has been a growing frustration among Ukraine's leadership with Berlin, which has backed some sanctions against Russia but so far resisted calls to back tougher action on oil sales.",
              style: Theme.of(context).textTheme.lightBodyLargeRegular,
              textAlign: TextAlign.justify,
            ),
            Gap(10.h),
            RichText(
                text: TextSpan(children: [
              TextSpan(
                text: "Source: ",
                style: Theme.of(context).textTheme.lightBodyLargeRegular,
              ),
              TextSpan(
                text: "ABC News-https://abcnews.go.com",
                style:
                    Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                          color: Theme.of(context).secondary(context),
                        ),
              )
            ])),
            Gap(10.h),
            Row(
              children: [
                Text('Other news',
                    style: Theme.of(context).textTheme.lightBodyLargeBold),
                const Spacer(),
                Text(
                  'See all',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(color: Theme.of(context).greyScale600(context),),
                )
              ],
            ),
            Gap(10.h),
            ListView.separated(
              shrinkWrap: true,
              padding: EdgeInsets.only(bottom: 20.h),
              physics: const NeverScrollableScrollPhysics(),
              itemCount: userNewData.length,
              itemBuilder: (context, index) {
                return InkWell(
                    onTap: () {
                      context.push(
                        RouteName.NewsScreen,
                        extra: {'userNew': userNewData[index]},
                      );
                    },
                    child: CardLatest(userNew: userNewData[index]));
              },
              separatorBuilder: (context, index) => Gap(20.h),
            ),
          ],
        ),
      ),
    );
  }
}

class BottomNewsWidget extends StatefulWidget {
  const BottomNewsWidget({super.key});

  @override
  State<BottomNewsWidget> createState() => _BottomNewsWidgetState();
}

class _BottomNewsWidgetState extends State<BottomNewsWidget> {
  bool isLiked = false;
  bool switchValue = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height * 0.07,
      padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.radius4),
          topRight: Radius.circular(AppRadius.radius4),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).greyScale900(context),
            offset: const Offset(0, -1),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          buildIconButton(
              SvgPicture.asset(
                AppAssets.heart_icon,
                color: isLiked ? Theme.of(context).primary(context) : Theme.of(context).greyScale600(context),
              ),
              'Like', () {
            setState(() {
              isLiked = !isLiked;
            });
          }),
          buildIconButton(
            SvgPicture.asset(AppAssets.message_newSvg),
            'Comment',
            () {
              showComment(context, switchValue);
            },
          ),
          buildIconButton(
              SvgPicture.asset(
                AppAssets.share_newSvg,
                color: Theme.of(context).greyScale600(context),
              ),
              'Share',
              () {}),
        ],
      ),
    );
  }

  Widget buildIconButton(SvgPicture svg, String text, Function() onTap) {
    return GestureDetector(
      onTap: () {
        onTap();
      },
      child: Row(
        children: [
          svg,
          Gap(12.w),
          Text(
            text,
            style: Theme.of(context)
                .textTheme
                .lightBodyMediumRegular
                .copyWith(color: Theme.of(context).greyScale600(context),),
          )
        ],
      ),
    );
  }
}
