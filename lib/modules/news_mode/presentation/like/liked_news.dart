import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/modules/news_mode/fake_data/user_new_data.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/app_bar_new.dart';
import '../widgets/search_new/card_like_item.dart';

class LikedNews extends StatefulWidget {
  const LikedNews({super.key});

  @override
  State<LikedNews> createState() => _LikedNewsState();
}

class _LikedNewsState extends State<LikedNews> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarNew(
        title: 'Liked',
        onPressed: () {
          (context).push(RouteName.newPage);
        },
      ),
      body: Container(
        padding: EdgeInsets.only(
            top: AppSpacing.padding16h,
            right: AppSpacing.padding16,
            left: AppSpacing.padding16),
        child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              var obj = userNewData[index];
              return CardLikeItem(
                userNew: obj,
              );
            },
            separatorBuilder: (context, index) => Gap(20.h),
            itemCount: userNewData.length),
      ),
    );
  }
}
