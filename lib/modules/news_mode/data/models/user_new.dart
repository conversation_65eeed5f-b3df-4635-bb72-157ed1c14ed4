class UserNew {
  String author;
  String title;
  String image;
  String description;
  String authorImage;

  UserNew({
    required this.author,
    required this.title,
    required this.image,
    required this.description,
    required this.authorImage,
  });

  // factory User.fromJson(Map<String, dynamic> json) {
  //   return User(
  //     name: json['name'],
  //     email: json['email'],
  //     password: json['password'],
  //   );
  // }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'name': name,
  //     'email': email,
  //     'password': password,
  //   };
  // }
}
