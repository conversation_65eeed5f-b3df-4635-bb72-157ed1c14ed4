// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cupertino_onboarding/cupertino_onboarding.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';

class OverViewBusinesPage extends StatelessWidget {
  const OverViewBusinesPage({
    super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.business.tr(),
      ),
      body: CupertinoOnboarding(
        onPressedOnLastPage: () => (context).push(RouteName.businesMode),
        pages: [
          CartViewBusines(
            icon: AppAssets.overview1,
            title: "Connect to partner, chances",
            subTitle: 'You are in business mode',
          ),
          CartViewBusines(
            icon: AppAssets.overview2,
            title: "You can choose an action ",
            subTitle: 'Post your offer or request a job',
          ),
          CartViewBusines(
            icon: AppAssets.overview3,
            title: "Fill your text",
            subTitle: 'Set budget, timeline, description,...',
          ),
          CartViewBusines(
            icon: AppAssets.overview4,
            title: "Connect and transact securely",
            subTitle: " Swipe left mean you aren’t interested\f "
                "Swipe right you can chat with each other",
          ),
          CartViewBusines(
            icon: AppAssets.checkedIcon,
            title: "That’s it",
            subTitle: "Let’s start your experience",
          ),
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class CartViewBusines extends StatelessWidget {
  String icon;
  String title;
  String subTitle;

  CartViewBusines({
    super.key,
    required this.icon,
    required this.title,
    required this.subTitle,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoOnboardingPage(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(icon),
            Gap(20.h),
            Text(
              title,
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            Gap(10.h),
            Text(
              subTitle,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
        title: const Text(""));
  }
}
