import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/busines_mode/presentation/home_busines/busines_home_page.dart';
import 'package:multime_app/modules/application/bottom_bar/bottomSheet_model.dart';

import '../../../../core/l10n/locale_keys.g.dart';
import '../../../application/bottom_bar/application_page.dart';


class BusinesBottomSheetItem extends StatelessWidget {
  const BusinesBottomSheetItem({super.key});

  @override
  Widget build(BuildContext context) {
    return ApplicationPage(page: const BusinesHomePage(),
        bottomSheetItems: [
      BottomSheetItem(
        name: LocaleKeys.Postoffer.tr(),
        iconLead: AppAssets.profile_tick,
        onTap: () {
          (context).push(RouteName.postProductScreen);
        },
      ),
      BottomSheetItem(
        name:LocaleKeys.PostRequest.tr(),
        iconLead: AppAssets.icon_note,
        onTap: () {
          (context).push(RouteName.likedScreen);
        },
      ),
    ]);
  }
}
