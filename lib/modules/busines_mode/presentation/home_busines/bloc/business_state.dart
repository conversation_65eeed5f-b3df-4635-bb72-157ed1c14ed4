import 'package:equatable/equatable.dart';

import '../../../data/models/business_post.dart';

abstract class BusinessState extends Equatable {
  @override
  List<Object?> get props => [];
}

class BusinessInitial extends BusinessState {}

class BusinessLoading extends BusinessState {}

class BusinessLoaded extends BusinessState {
  final List<BusinessPost> posts;
  BusinessLoaded(this.posts);

  @override
  List<Object?> get props => [posts];
}

class BusinessError extends BusinessState {
  final String message;
  BusinessError(this.message);

  @override
  List<Object?> get props => [message];
}
