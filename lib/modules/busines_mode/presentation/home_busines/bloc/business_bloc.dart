import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/repositories/business_repositories.dart';
import '../../../data/repositories/fakeData.dart';
import 'business_event.dart';
import 'business_state.dart';

class BusinessBloc extends Bloc<BusinessEvent, BusinessState> {
  final BusinessRepository repository;

  BusinessBloc(this.repository) : super(BusinessInitial()) {
    on<LoadBusinessPosts>(_onLoadBusinessPosts);
    // on<RemoveBusinessPost>(_onRemoveBusinessPost);
  }
  Future<void> _onLoadBusinessPosts(LoadBusinessPosts event, Emitter<BusinessState> emit) async {
  emit(BusinessLoading());
  await Future.delayed(Duration(seconds: 5)); // Giả lập thời gian load dữ liệu
  emit(BusinessLoaded(fakeDataPost));
}


  // Future<void> _onLoadBusinessPosts(LoadBusinessPosts event, Emitter<BusinessState> emit) async {
  //   emit(BusinessLoading());
  //   try {
  //     final posts = await repository.fetchBusinessPosts();
  //     emit(BusinessLoaded(posts));
  //   } catch (e) {
  //     emit(BusinessError("Failed to load data"));
  //   }
  // }

  // void _onRemoveBusinessPost(RemoveBusinessPost event, Emitter<BusinessState> emit) {
  //   if (state is BusinessLoaded) {
  //     final updatedPosts = (state as BusinessLoaded).posts.where((post) => post.id != event.postId).toList();
  //     emit(BusinessLoaded(updatedPosts));
  //   }
  // }
}
