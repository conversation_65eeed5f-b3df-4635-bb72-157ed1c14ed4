import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:multime_app/modules/busines_mode/presentation/home_busines/bloc/business_bloc.dart';
import 'package:multime_app/modules/busines_mode/presentation/home_busines/bloc/business_event.dart';
import 'package:multime_app/shared/widgets/dialog_widgets/dialog_multime.dart';
import 'package:readmore/readmore.dart';
import '../../../../shared/widgets/app_loader/404_strongbody.dart';
import '../../../../shared/widgets/app_loader/loading_strongbody.dart';
import '../../data/repositories/business_repositories.dart';
import 'bloc/business_state.dart';
import 'widgets/button_business.dart';

class BusinesHomePage extends StatefulWidget {
  const BusinesHomePage({super.key});
  @override
  State<BusinesHomePage> createState() => _BusinesHomePageState();
}

class _BusinesHomePageState extends State<BusinesHomePage> {
  bool isExpanded = false;

  bool _onSwipe(
    int previousIndex,
    int? currentIndex,
    CardSwiperDirection direction,
  ) {
    switch (direction) {
      case CardSwiperDirection.left:
        AlertMultimeMedium(
          context: context,
          titleAlert: LocaleKeys.notInterested.tr(),
          contentAlert: LocaleKeys.swipeLeftNoInterest.tr(),
          onTapButton: () {},
          textButton: LocaleKeys.notInterested.tr(),
        );
        break;

      case CardSwiperDirection.right:
        AlertMultimeMedium(
          context: context,
          titleAlert: LocaleKeys.askMore.tr(),
          contentAlert: LocaleKeys.swipeRightNoInterest.tr(),
          onTapButton: () {},
          textButton: LocaleKeys.askMore.tr(),
        );
        break;
      case CardSwiperDirection.none:
        break;
      case CardSwiperDirection.top:
      // TODO: Handle this case.
      case CardSwiperDirection.bottom:
      // TODO: Handle this case.
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          BusinessBloc(BusinessRepository())..add(LoadBusinessPosts()),
      child: Scaffold(
        appBar: CustomAppBar(
          moden: LocaleKeys.business.tr(),
        ),
        body: BlocBuilder<BusinessBloc, BusinessState>(
          builder: (context, state) {
            if (state is BusinessLoading) {
              return const LoadingStrongBody();
            } else if (state is BusinessLoaded) {
              return CardSwiper(
                  onSwipe: _onSwipe,
                  cardsCount: state.posts.length,
                  numberOfCardsDisplayed: 2,
                  backCardOffset: const Offset(0, 0),
                  cardBuilder: (
                    context,
                    index,
                    horizontalThresholdPercentage,
                    verticalThresholdPercentage,
                  ) {
                    final post = state.posts[index];

                    return Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 10.w, vertical: 10.h),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: Theme.of(context).disabledLight(context),
                          width: 1,
                        ),
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 23.w,
                                  backgroundImage: NetworkImage(
                                    post.avatar,
                                  ),
                                ),
                                SizedBox(width: 15.w),
                                // Văn bản
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        post.name,
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                      Text(
                                        post.role,
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 10.w),
                                Row(
                                  children: [
                                    Text(post.views.toString(),
                                        style: Theme.of(context)
                                            .textTheme
                                            .lightBodyLargeMedium
                                            .copyWith(
                                              color: Colors.grey,
                                            )),
                                    SizedBox(width: 10.w),
                                    SvgPicture.asset(AppAssets.eyes_icon),
                                    SizedBox(width: 10.w),
                                  ],
                                )
                              ],
                            ),
                            Gap(10.h),
                            Container(
                              width: double.infinity.w,
                              height:
                                  MediaQuery.of(context).size.height * 0.3.h,
                              decoration: BoxDecoration(
                                color: Theme.of(context).disabledLight(context),
                                borderRadius: BorderRadius.circular(10),
                                image: DecorationImage(
                                  image: NetworkImage(
                                    post.imageUrl,
                                  ),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            Gap(10.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      LocaleKeys.request.tr(),
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyXLargeSemiBold
                                          .copyWith(
                                            color: Theme.of(context)
                                                .backgroundRed(context),
                                          ),
                                    ),
                                    SizedBox(height: 6.h),
                                    Text(
                                      post.country,
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyLargeSemiBold,
                                    ),
                                  ],
                                ),
                                Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ButtonBusiness(
                                          sized: 60,
                                          sizedIcon: 28,
                                          icon: AppAssets.close_icon,
                                        ),
                                        SizedBox(width: 20.w),
                                        ButtonBusiness(
                                          sized: 60,
                                          sizedIcon: 28,
                                          icon: AppAssets.send_icon,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Gap(10.h),
                            Text(post.title,
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyMediumRegular),
                            Gap(10.h),
                            ReadMoreText(
                              post.description,
                              trimMode: TrimMode.Line,
                              trimLines: 2,
                              colorClickableText: Colors.pink,
                              trimCollapsedText: LocaleKeys.readMore.tr(),
                              trimExpandedText: LocaleKeys.readLess.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumRegular,
                              moreStyle: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.red),
                            )
                          ],
                        ),
                      ),
                    );
                  });
            }
            return const NoDataStrongBody();
          },
        ),
      ),
    );
  }
}
