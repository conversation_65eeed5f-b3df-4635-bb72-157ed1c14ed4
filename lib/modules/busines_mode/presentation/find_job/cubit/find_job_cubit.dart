import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_event.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_state.dart';

class FindJobBloc extends Bloc<FindJobEvent,FindJobState> {
  FindJobBloc() : super(const FindJobState(start: 20, end: 28, distance: 40)){
    on<UpdateAgeRange>(_onUpdateAgeRange);
    on<UpdateDistance>(_onUpdateDistance);
  }

  void _onUpdateAgeRange(UpdateAgeRange event, Emitter<FindJobState> emit) {
    emit(state.copyWith(start: event.start, end: event.end));
  }

  void _onUpdateDistance(UpdateDistance event, Emitter<FindJobState> emit) {
    emit(state.copyWith(distance: event.distance));
  }


}
