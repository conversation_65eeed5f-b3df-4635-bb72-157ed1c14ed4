
import 'package:equatable/equatable.dart';

 class FindJobState extends Equatable {
  final double start;
  final double end;
  final double distance;
  const FindJobState(
      {required this.start, required this.end, required this.distance}
      );
  // Initial state
  factory FindJobState.initial() {
    return const FindJobState(
     start: 20,
     end: 28,
      distance: 40
    );
  }


  // Copy state
  FindJobState copyWith({
    double? start,
    double? end,
    double? distance,
  }) {
    return FindJobState(
      start: start ?? this.start,
      end: end ?? this.end,
      distance: distance ?? this.distance,
    );
  }
  @override
  List<Object> get props => [start, end, distance];
}

// class AgeRangeState extends FindJobState {
//   final double start;
//   final double end;
//
//   AgeRangeState({required this.start, required this.end});
// }
//
// class DistanceState {
//   final double distance;
//
//   DistanceState({required this.distance});
//
// }