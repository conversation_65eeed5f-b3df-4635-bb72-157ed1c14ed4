

import 'package:equatable/equatable.dart';

abstract class FindJobEvent extends Equatable {
  const FindJobEvent();

  @override
  List<Object?> get props => [];
}

class UpdateAgeRange extends FindJobEvent {
  final double start;
  final double end;

  const UpdateAgeRange(this.start, this.end);

  @override
  List<Object?> get props => [start, end];
}
class UpdateDistance extends FindJobEvent {
  final double distance;

  const UpdateDistance(this.distance);

  @override
  List<Object?> get props => [distance];
}