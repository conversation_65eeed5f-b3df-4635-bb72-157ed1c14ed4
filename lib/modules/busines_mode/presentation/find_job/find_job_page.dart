import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/padding_constant.dart';

import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/application/bottom_bar/bloc/application_bloc.dart';
import 'package:multime_app/modules/application/bottom_bar/bloc/application_event.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_cubit.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/cubit/find_job_state.dart';
import 'package:multime_app/shared/widgets/sames/custom_dropdown_button.dart';
import 'package:multime_app/modules/busines_mode/presentation/widgets/true_faild_button_widget.dart';
import 'package:multime_app/modules/busines_mode/presentation/widgets/upload_image_widget.dart';
import '../../../auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'cubit/find_job_event.dart';

class FindJobPage extends StatefulWidget {
  const FindJobPage({super.key});

  @override
  State<FindJobPage> createState() => _FindJobPageState();
}

class _FindJobPageState extends State<FindJobPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: CustomAppBar(moden: LocaleKeys.business.tr()),
        body: Padding(
          padding: PaddingConstants.padSymH16,
          child: Column(
            children: [
              TabBar(
                controller: _tabController,
                labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Theme.of(context).primary(context),
                      fontWeight: FontWeight.w800,
                    ),
                indicatorSize: TabBarIndicatorSize.tab,
                unselectedLabelStyle:
                    Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Colors.grey,
                          fontWeight: FontWeight.w800,
                        ),
                dividerColor: Colors.grey,
                indicator: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(context).primary(context),
                      width: 2,
                    ),
                  ),
                ),
                tabs: [
                  Tab(text: LocaleKeys.offer.tr()),
                  Tab(text: LocaleKeys.request.tr()),
                ],
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOfferContent(context),
                    _buildRequestContent(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfferContent(BuildContext context) {
    final style = Theme.of(context).textTheme.titleMedium;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Gap(20.h),
          Text(
            LocaleKeys.whatDoYouWantToDo.tr(),
            style: style?.copyWith(
              fontSize: 21.sp,
              fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
              fontWeight: FontWeight.w800,
            ),
            textAlign: TextAlign.center,
          ),
          Gap(20.h),
          const StrongBodyTextField(labalText: "Desired job"),
          Gap(20.h),
          const StrongBodyTextField(
            labalText: "Introduce your abilities",
            maxLines: 5,
          ),
          Gap(10.h),
          Row(
            children: [
              Text(
                "Your photo",
                style: Theme.of(context).textTheme.lightBodyMediumBold,
              ),
            ],
          ),
          Gap(10.h),
          //TODO Image
          UploadImage(
            onTap: () {},
          ),
          Gap(20.h),

          CustomDropDownButton(
            arr: [
              '1 ${LocaleKeys.dayago.tr()}',
              '2 ${LocaleKeys.dayago.tr()}',
              '3 ${LocaleKeys.dayago.tr()}',
              '1 ${LocaleKeys.weekago.tr()}',
              '2 ${LocaleKeys.weekago.tr()}',
              '3 ${LocaleKeys.weekago.tr()}',
            ],
            hintText: LocaleKeys.SelectaTimeRange.tr(),
          ),
          Gap(20.h),
          BlocBuilder<FindJobBloc, FindJobState>(
            builder: (context, state) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.distance.tr(),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                        ),
                      ),
                      Text("${state.distance.toInt()} km"),
                    ],
                  ),
                  Slider(
                    value: state.distance,
                    min: 0,
                    max: 100,
                    divisions: 100,
                    inactiveColor: Colors.grey[300],
                    activeColor: Colors.red,
                    onChanged: (double value) {
                      context.read<FindJobBloc>().add(UpdateDistance(value));
                    },
                  ),
                ],
              );
            },
          ),

          Gap(20.h),
          TrueFaildButton(
            onTapButton1: () {
              context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
              context.push(RouteName.bussinesBottomSheet);
            },
            onTapButton2: () {},
            colorText1: Theme.of(context).greyScale600(context),
            // colorText2: Colors.white,
            textButton1: LocaleKeys.skip.tr(),
            textButton2: LocaleKeys.post.tr(),
            // colorButton1: Theme.of(context).greyScale600(context),
            colorButton2: Theme.of(context).secondary(context),
          ),
          SizedBox(height: 80.h),
        ],
      ),
    );
  }

  Widget _buildRequestContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Gap(20.h),
          Text(
            LocaleKeys.postBusinessOpportunity.tr(),
            style: Theme.of(context).textTheme.headlineMedium,
            textAlign: TextAlign.center,
          ),
          Gap(20.h),
          StrongBodyTextField(labalText: LocaleKeys.title.tr()),
          Gap(10.h),
          const StrongBodyTextField(labalText: "Field/ Industry"),
          Gap(10.h),
          StrongBodyTextField(
            labalText: LocaleKeys.description.tr(),
            maxLines: 5,
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child:
                const StrongBodyTextField(labalText: "Budget/Investment (USD)"),
            /////
          ),
          Row(
            children: [
              Text(
                "Your photo",
                style: Theme.of(context).textTheme.lightBodyMediumBold,
              ),
            ],
          ),
          Gap(10.h),
          //TODO Image
          UploadImage(
            onTap: () {},
          ),
          Gap(20.h),

          CustomDropDownButton(
            arr: [
              '1 ${LocaleKeys.dayago.tr()}',
              '2 ${LocaleKeys.dayago.tr()}',
              '3 ${LocaleKeys.dayago.tr()}',
              '1 ${LocaleKeys.weekago.tr()}',
              '2 ${LocaleKeys.weekago.tr()}',
              '3 ${LocaleKeys.weekago.tr()}',
            ],
            hintText: LocaleKeys.SelectaTimeRange.tr(),
          ),
          Gap(20.h),
          BlocBuilder<FindJobBloc, FindJobState>(
            builder: (context, state) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.distance.tr(),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                        ),
                      ),
                      Text("${state.distance.toInt()} km"),
                    ],
                  ),
                  Slider(
                    value: state.distance,
                    min: 0,
                    max: 100,
                    divisions: 100,
                    inactiveColor: Colors.grey[300],
                    activeColor: Colors.red,
                    onChanged: (double value) {
                      context.read<FindJobBloc>().add(UpdateDistance(value));
                    },
                  ),
                ],
              );
            },
          ),

          Gap(20.h),
          TrueFaildButton(
            onTapButton1: () {
              context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
              context.push(RouteName.bussinesBottomSheet);
            },
            onTapButton2: () {},
            colorText1: Theme.of(context).greyScale600(context),
            // colorText2: Colors.white,
            textButton1: LocaleKeys.skip.tr(),
            textButton2: LocaleKeys.post.tr(),
            // colorButton1: Theme.of(context).greyScale600(context),
            colorButton2: Theme.of(context).secondary(context),
          ),
          SizedBox(height: 80.h),
        ],
      ),
    );
  }
}
