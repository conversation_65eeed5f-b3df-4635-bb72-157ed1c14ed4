import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:readmore/readmore.dart';

import '../../../../shared/widgets/sames/icon_button_widget.dart';

class SwipperUserPage extends StatefulWidget {
  const SwipperUserPage({super.key});

  @override
  State<SwipperUserPage> createState() => _SwipperUserPageState();
}

class _SwipperUserPageState extends State<SwipperUserPage> {
  bool isExpanded = false;
  void _showAddContactDialog(
      BuildContext context, String title, String content, String okText) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          contentPadding: EdgeInsets.zero,
          content: Container(
            width: 300.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).greyScale500(context),
                  width: 1.w,
                ),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      height: 1.5.h,
                    ),
                  ),
                ),
                Text(
                  content,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w300,
                  ),
                ),
                Gap(10.h),
                Divider(
                  height: 1.h,
                  color: Theme.of(context).greyScale500(context),
                ),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => context.pop(),
                        child: Container(
                          height: 60.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border(
                              right: BorderSide(
                                color: Theme.of(context).greyScale500(context),
                                width: 1.w,
                              ),
                            ),
                          ),
                          child: Text(
                            LocaleKeys.cancel.tr(),
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w400,
                              color: Theme.of(context).textSecondary(context),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          context.pop();
                        },
                        child: Container(
                          height: 58.h,
                          alignment: Alignment.center,
                          child: Text(
                            title,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  bool _onSwipe(
    int previousIndex,
    int? currentIndex,
    CardSwiperDirection direction,
  ) {
    switch (direction) {
      case CardSwiperDirection.left:
        _showAddContactDialog(context, LocaleKeys.notInterested.tr(),
            LocaleKeys.swipeLeftNoInterest.tr(), LocaleKeys.notInterested.tr());
        break;
      case CardSwiperDirection.right:
        _showAddContactDialog(context, LocaleKeys.askMore.tr(),
            LocaleKeys.swipeRightNoInterest.tr(), LocaleKeys.askMore.tr());
        break;
      case CardSwiperDirection.none:
        break;
      case CardSwiperDirection.top:
      // TODO: Handle this case.
      case CardSwiperDirection.bottom:
      // TODO: Handle this case.
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.business.tr(),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Theme.of(context).disabledLight(context),
            width: 1,
          ),
        ),
        child: CardSwiper(
          onSwipe: _onSwipe,
          cardsCount: 2,
          numberOfCardsDisplayed: 2,
          backCardOffset: const Offset(0, 0),
          cardBuilder: (
            context,
            index,
            horizontalThresholdPercentage,
            verticalThresholdPercentage,
          ) =>
              Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  offset: Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Row(
                    children: [
                      const CircleAvatar(
                        radius: 24,
                        backgroundImage: NetworkImage(
                          'https://kenh14cdn.com/2020/7/17/brvn-15950048783381206275371.jpg',
                        ),
                      ),
                      SizedBox(width: 15.w),
                      // Văn bản
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Test ${index + 1}',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            Text(
                              'Ceo',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Row(
                        children: [
                          Text(
                            '20',
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: 10.w),
                          const Icon(
                            Icons.remove_red_eye_outlined,
                            size: 30,
                          ),
                          SizedBox(width: 10.w),
                        ],
                      )
                    ],
                  ),
                  Gap(10.h),
                  Container(
                    width: double.infinity.w,
                    height: MediaQuery.of(context).size.height * 0.3.h,
                    decoration: BoxDecoration(
                      color: Theme.of(context).disabledLight(context),
                      borderRadius: BorderRadius.circular(10),
                      image: const DecorationImage(
                        image: NetworkImage(
                          'https://kenh14cdn.com/2020/7/17/brvn-15950048783381206275371.jpg',
                        ),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Gap(10.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.request.tr(),
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w700,
                              color: Theme.of(context).backgroundRed(context),
                            ),
                          ),
                          SizedBox(height: 6.h),
                          Text(
                            'Việt Nam - Hà Nội',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w700,
                              color: Theme.of(context).greyScale800(context),
                            ),
                          ),
                        ],
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButtonMTM(
                                sized: 60,
                                sizedIcon: 28,
                                icon: Icons.close,
                                colorIcon: Colors.black,
                              ),
                              SizedBox(width: 20.w),
                              IconButtonMTM(
                                sized: 60,
                                sizedIcon: 28,
                                icon: Icons.send_outlined,
                                colorIcon: Colors.red,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                  Gap(10.h),
                  Text(
                    'I’m looking for a person who master in illustrator and design for my company',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w300,
                      color: Theme.of(context).greyScale800(context),
                    ),
                  ),
                  Gap(10.h),
                  ReadMoreText(
                    'It is a long established fact that a reader will be'
                    ' distracted by the readable content of a page when looking at its layout.'
                    ' The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters.',
                    trimMode: TrimMode.Line,
                    trimLines: 2,
                    colorClickableText: Colors.pink,
                    trimCollapsedText: LocaleKeys.readMore.tr(),
                    trimExpandedText: LocaleKeys.readLess.tr(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w300,
                      color: Theme.of(context).greyScale800(context),
                    ),
                    moreStyle: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w300,
                        color: Colors.red),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
      // bottomNavigationBar: BottomNavBar(
      //   currentIndex: 0,
      //   onTabSelected: (int value) {},
      //   bottomSheetItems: [],
      //   pages: [],
      // ),
    );
  }
}
