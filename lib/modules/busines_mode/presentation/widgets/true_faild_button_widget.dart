// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// ignore: must_be_immutable
class TrueFaildButton extends StatelessWidget {
  Color? colorText1;
  String textButton1;
  String textButton2;
  Color? colorButton1;
  Color? colorButton2;
  Color? colorText2;
  Function()? onTapButton1;
  Function()? onTapButton2;

  TrueFaildButton({
    super.key,
    this.colorText1,
    this.colorText2,
    required this.textButton1,
    required this.textButton2,
    this.colorButton1,
    this.colorButton2,
    this.onTapButton1,
    this.onTapButton2,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: onTapButton1,
            child: Container(
              height: 50.h,
              decoration: BoxDecoration(
                color: colorButton1,
                border: Border.all(width: 2, color: colorText1 ?? Colors.black),
                borderRadius: BorderRadius.circular(8),
                // color: colorButton1,
              ),
              child: Center(
                child: Text(
                  textButton1,
                  style: TextStyle(
                    color: colorText1,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: 15.h),
        Expanded(
          child: GestureDetector(
            onTap: onTapButton2,
            child: Container(
              height: 50.h,
              decoration: BoxDecoration(
                border:
                    Border.all(width: 2, color: colorText2 ?? colorButton2!),
                borderRadius: BorderRadius.circular(8),
                color: colorButton2,
              ),
              child: Center(
                child: Text(
                  textButton2,
                  style: TextStyle(
                    color: colorText2 ?? Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
