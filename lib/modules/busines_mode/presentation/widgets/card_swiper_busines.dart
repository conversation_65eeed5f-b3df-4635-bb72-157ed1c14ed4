import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:readmore/readmore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/l10n/locale_keys.g.dart';
import '../../../../shared/widgets/sames/icon_button_widget.dart';

class CardSwiperBusiness extends StatefulWidget {
  const CardSwiperBusiness({
    super.key,
    required this.name,
    required this.position,
    required this.description,
    required this.image,
    required this.country,
    required this.avatar,
    required this.view,
    this.onSwipe,
    required this.title,
  });

  final String name, position, description, image, country, avatar, title;
  final int view;
  final FutureOr<bool> Function(int, int?, CardSwiperDirection)? onSwipe;

  @override
  State<CardSwiperBusiness> createState() => _CardSwiperBusinessState();
}

class _CardSwiperBusinessState extends State<CardSwiperBusiness> {
  @override
  Widget build(BuildContext context) {
    return CardSwiper(
      onSwipe: widget.onSwipe,
      cardsCount: 2,
      numberOfCardsDisplayed: 2,
      backCardOffset: const Offset(0, 0),
      cardBuilder: (
        context,
        index,
        horizontalThresholdPercentage,
        verticalThresholdPercentage,
      ) =>
          Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              offset: Offset(0, 4),
              blurRadius: 10,
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundImage: NetworkImage(
                      widget.avatar,
                    ),
                  ),
                  SizedBox(width: 20.w),
                  // Văn bản
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.name,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          widget.position,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 20.w),
                  Row(
                    children: [
                      Text(
                        widget.view.toString(),
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(width: 10.w),
                      const Icon(
                        Icons.remove_red_eye_outlined,
                        size: 30,
                      ),
                      SizedBox(width: 10.w),
                    ],
                  )
                ],
              ),
              Gap(10.h),
              Container(
                width: double.infinity.w,
                height: 300.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).disabledLight(context),
                  borderRadius: BorderRadius.circular(10),
                  image: DecorationImage(
                    image: NetworkImage(
                      widget.image,
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              SizedBox(height: 12.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.request.tr(),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).primary(context),
                        ),
                      ),
                      Gap(10.h),
                      Text(
                        widget.country,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).greyScale800(context),
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButtonMTM(
                            sized: 60,
                            sizedIcon: 28,
                            icon: Icons.close,
                            colorIcon: Colors.red,
                          ),
                          const SizedBox(width: 20),
                          IconButtonMTM(
                            sized: 60,
                            icon: Icons.send,
                            sizedIcon: 28,
                            colorIcon: Colors.red,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              Gap(10.h),
              Text(
                widget.title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w300,
                  color: Theme.of(context).greyScale800(context),
                ),
              ),
              Gap(10.h),
              ReadMoreText(
                widget.description,
                trimMode: TrimMode.Line,
                trimLines: 2,
                colorClickableText: Colors.pink,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w300,
                  color: Theme.of(context).greyScale800(context),
                ),
                trimCollapsedText: LocaleKeys.Readmore.tr(),
                trimExpandedText: LocaleKeys.Readless.tr(),
                moreStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w300,
                  color: Colors.pink,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
