import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/constants/app_assets.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class UploadImage extends StatelessWidget {
  Function()? onTap;
  UploadImage({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            width: 1,
            color: Colors.blue,
          ),
        ),
        child: Center(
          child: Column(
            children: [
              Gap(18.h),
              SvgPicture.asset(
                AppAssets.uploadImageSvg,
              ),
              Gap(8.h),
              Text(
                LocaleKeys.Clickheretouploadimage.tr(),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
              Text(
                LocaleKeys.Supportsimageformats.tr(),
                style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                    fontSize: 12,
                    fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
                textAlign: TextAlign.center,
              ),
              Gap(18.h),
            ],
          ),
        ),
      ),
    );
  }
}
