import '../models/business_post.dart';

List<BusinessPost> fakeDataPost = [
  const BusinessPost(
    id: "1",
    name: "<PERSON>",
    avatar:
        "https://www.inseec.com/wp-content/uploads/sites/6/2021/11/DSF4657.jpg?resize=1024,683",
    role: "CEO A COMPANY",
    country: "Thailand",
    description:
        "I'm looking for a person who masters in Illustrator and design for my company.Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    title:
        "Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old.",
    imageUrl:
        "https://www.inseec.com/wp-content/uploads/sites/6/2021/11/DSF4657.jpg?resize=1024,683",
    views: 23,
  ),
  const BusinessPost(
    id: "2",
    name: "<PERSON>",
    avatar: "https://randomuser.me/api/portraits/women/2.jpg",
    role: "Marketing Manager",
    country: "Vietnam",
    description:
        "Need a professional to handle branding for our new product launch.Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    title:
        "Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old.",
    imageUrl:
        "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRlx_TT1PbFIyB6GoFLR3ZHuy08b7PRzR19-VNh0FAjja2U5qU5buCuhov4pByV1W2iuPU&usqp=CAU",
    views: 45,
  ),
  const BusinessPost(
    id: "3",
    name: "David L",
    avatar:
        "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQFFmDfECJuEkPkyCAE6QKBWswm8MQhjZKaFMHSHEC-RKsTkSt2Mg3Q7Cd-zJSZeln2vkE&usqp=CAU",
    role: "Tech Lead",
    country: "USA",
    description:
        "Looking for a Flutter developer to join our startup team.Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    title:
        "Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old.",
    imageUrl:
        "https://fastwork.vn/wp-content/uploads/2020/12/fastwork-Talent-Acquisition-Business-Partner-2.jpg",
    views: 67,
  ),
  const BusinessPost(
    id: "4",
    name: "Emily R",
    avatar: "https://randomuser.me/api/portraits/women/4.jpg",
    role: "HR Manager",
    country: "Singapore",
    description:
        "We need a UI/UX designer for our company website redesign project.Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    title:
        "Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old.",
    imageUrl:
        "https://www.inseec.com/wp-content/uploads/sites/6/2021/11/DSF4657.jpg?resize=1024,683",
    views: 30,
  ),
  const BusinessPost(
    id: "5",
    name: "Michael B",
    avatar: "https://randomuser.me/api/portraits/men/5.jpg",
    role: "Founder",
    country: "Germany",
    description:
        "Seeking investment opportunities in the fintech industry.Lorem Ipsum is simply dummy text of the printing and typesetting industry. ",
    title:
        "Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old.",
    imageUrl:
        "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR43S5zAiFQiFvnDuLMzzjHVm_6tIiq5r2owQ&s",
    views: 52,
  ),
];
