import 'package:dio/dio.dart';
import '../models/business_post.dart';

class BusinessRepository {
  final Dio dio;

  BusinessRepository({Dio? dio}) : dio = dio ?? Dio();

  Future<List<BusinessPost>> fetchBusinessPosts() async {
    try {
      final response = await dio.get("https://yourapi.com/business-posts");
      return (response.data as List).map((json) => BusinessPost.fromJson(json)).toList();
    } catch (e) {
      throw Exception("Failed to load business posts");
    }
  }
}
