import 'package:equatable/equatable.dart';

class BusinessPost extends Equatable {
  final String id;
  final String name;
  final String avatar;
  final String role;
  final String country;
  final String title;
  final String description;
  final String imageUrl;
  final int views;

  const BusinessPost({
    required this.id,
    required this.name,
    required this.avatar,
    required this.role,
    required this.country,
    required this.description,
    required this.title,
    required this.imageUrl,
    required this.views,
  });

  factory BusinessPost.fromJson(Map<String, dynamic> json) {
    return BusinessPost(
      id: json['id'],
      name: json['name'],
      avatar: json['avatar'],
      role: json['role'],
      country: json['country'],
      title: json['title'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      views: json['views'],
    );
  }

  @override
  List<Object?> get props =>
      [id, name, avatar, role, country, description, imageUrl, views];
}
