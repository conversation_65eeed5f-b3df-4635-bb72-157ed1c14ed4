import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/category_repository.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

import '../../../../core/l10n/locale_keys.g.dart';
import '../../../auth_mode/presentation/widgets/strong_body_text_field.dart';
import '../../../busines_mode/presentation/widgets/true_faild_button_widget.dart';
import '../../../create_service_mode/presentation/create_service_home/bloc/create_service_bloc.dart';
import '../../../create_service_mode/presentation/widgets/service_app_bar.dart';
import '../../../create_service_mode/presentation/widgets/service_home/service_description.dart';
import '../../../create_service_mode/presentation/widgets/service_home/service_image.dart';

class PostRequiredHome extends StatelessWidget {
  const PostRequiredHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ServiceAppBar(
        title: 'Post request',
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocProvider(
          create: (context) => CreateServiceBloc(
              getIt<HomeStrongbodyAiRepository>(),
              getIt<CategoryRepositoryRemote>()),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Gap(24.h),
                StrongBodyTextField(labalText: LocaleKeys.title.tr()),
                Gap(24.h),
                ServiceDescription(
                  lableText: LocaleKeys.description,
                ),
                Gap(14.h),
                const ServiceImage(),
                Gap(20.h),
                TrueFaildButton(
                  onTapButton1: () => Navigator.pop(context),
                  onTapButton2: () {},
                  colorText1: Theme.of(context).greyScale600(context),
                  // colorText2: Colors.white,
                  textButton1: LocaleKeys.skip.tr(),
                  textButton2: LocaleKeys.post.tr(),
                  colorButton2: Theme.of(context).primary(context),
                ),
                SizedBox(height: 80.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
