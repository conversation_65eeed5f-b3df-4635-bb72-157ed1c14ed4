import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/extension.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>/build_start_item.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>/duo_action_button.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>/my_order_profile.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>/switch_camera_icon.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>/user_cart_infor.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:multime_app/modules/setting_mode/bloc/user_bloc.dart';
import '../../../../core/l10n/locale_keys.g.dart';
import '../../../../core/utils/image_choice.dart';
import 'widgets/add_friend_contact.dart';
import 'widgets/custom_clipper.dart';
import 'widgets/premium_subscription.dart';
import 'widgets/seller_center.dart';
import 'widgets/support_selection.dart';
import 'widgets/switch_to_seller.dart';

class HomeUserPage extends StatefulWidget {
  const HomeUserPage({super.key});

  @override
  State<HomeUserPage> createState() => _HomeUserPageState();
}

class _HomeUserPageState extends State<HomeUserPage>
    with WidgetsBindingObserver, RouteAware {
  final bool isCheckPremium = false;
  final int currentFriends = 8;
  final int totalFriends = 15;
  late UserBloc _userBloc;
  final userRole = getIt<GlobalStorage>().userRole;

  // Helper function to safely create ImageProvider for background images
  ImageProvider? _getBackgroundImageProvider(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return null;
    }

    // Check if it's a network URL
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return NetworkImage(imagePath);
    }

    // Check if it's a local file path (for temporary preview)
    if (imagePath.startsWith('/')) {
      final file = File(imagePath);
      if (file.existsSync()) {
        return FileImage(file);
      } else {
        // File doesn't exist, return null to use default
        return null;
      }
    }

    // Fallback to network image
    return NetworkImage(imagePath);
  }

  @override
  void initState() {
    super.initState();
    _userBloc = UserBloc(
      authRepository: getIt<AuthRepository>(),
      globalStorage: getIt<GlobalStorage>(),
    );
  }

  @override
  void dispose() {
    _userBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget _divider(
      BuildContext context,
    ) =>
        Container(
          color: Theme.of(context).secondary(context),
          width: 1,
          height: 90,
        );

    return BlocProvider.value(
      value: _userBloc,
      child: BlocListener<UserBloc, UserState>(
        bloc: _userBloc,
        listener: (context, state) {
          // Clear previous snackbars
          ScaffoldMessenger.of(context).clearSnackBars();

          if (state.isLoading == true) {
            // Show loading indicator
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Updating...'),
                duration: Duration(seconds: 1),
              ),
            );
          } else if (state.isSuccess == true && state.errorMessage.isEmpty) {
            // Show success message only if no error
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Background updated successfully'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          } else if (state.errorMessage.isNotEmpty &&
              state.isSuccess == false) {
            // Show error message only if operation failed
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 3),
              ),
            );
          }
        },
        child: Scaffold(
          backgroundColor: Colors.grey[50],
          appBar: AppBar(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SvgPicture.asset(AppAssets.logoSvg),
                Row(
                  children: [
                    // SvgPicture.asset(AppAssets.shoppingCart),
                    Gap(10.w),
                    GestureDetector(
                        onTap: () {
                          context.push(RouteName.notificationPage).then((_) {
                            _userBloc.add(FetchUserEvent());
                          });
                        },
                        child: SvgPicture.asset(AppAssets.notiSvg)),
                    Gap(10.w),
                    GestureDetector(
                      onTap: () async {
                        context.push(RouteName.settingAccount).then((_) {
                          _userBloc.add(FetchUserEvent());
                        });
                      },
                      child: SvgPicture.asset(AppAssets.setting_2),
                    ),
                  ],
                ),
              ],
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                Stack(
                  children: [
                    BlocBuilder<UserBloc, UserState>(
                      builder: (context, state) {
                        final user = state.user ?? getIt<GlobalStorage>().user;
                        final backgroundImageProvider =
                            _getBackgroundImageProvider(
                          user?.backgroundPicture,
                        );

                        return Container(
                          padding: EdgeInsets.only(top: 12.5.h),
                          alignment: Alignment.topCenter,
                          height: 300.h,
                          width: 1.sw,
                          decoration: BoxDecoration(
                            color: context.theme.primaryColor.withOpacity(0.1),
                            image: backgroundImageProvider != null
                                ? DecorationImage(
                                    image: backgroundImageProvider,
                                    fit: BoxFit.cover,
                                  )
                                : null,
                          ),
                          child: backgroundImageProvider == null
                              ? Image.asset(
                                  'assets/image/strongBodyAi/BG.png',
                                  fit: BoxFit.cover,
                                  height: 300.h,
                                  width: 1.sw,
                                )
                              : null,
                        );
                      },
                    ),
                    Column(
                      children: [
                        100.verticalSpace,
                        ClipPath(
                          clipper: CurvedTopClipper(),
                          child: Container(
                            padding: EdgeInsets.only(top: 70.h),
                            color: Colors.white,
                            child: BlocBuilder<UserBloc, UserState>(
                              builder: (context, state) {
                                final user =
                                    state.user ?? getIt<GlobalStorage>().user;
                                return Column(
                                  children: [
                                    Gap(20.h),
                                    UserCartInfor(
                                      isCheckPremium: isCheckPremium,
                                      isCheckPremium2: isCheckPremium,
                                      userName: user!.fullName,
                                      userEmail: user.email ?? 'Unknown',
                                      imgUser: user.profilePicture ?? '',
                                    ),
                                    Gap(10.h),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          BuildStatItem(
                                            svgImg: AppAssets.balanceUser,
                                            value: '123',
                                            label: LocaleKeys.Connected.tr(),
                                          ),
                                          _divider(context),
                                          BuildStatItem(
                                            svgImg: AppAssets.eyeSvg,
                                            value: '6032',
                                            label: LocaleKeys.Views.tr(),
                                          ),
                                          _divider(context),
                                          BuildStatItem(
                                            svgImg: AppAssets.menu1,
                                            value: '65',
                                            label: LocaleKeys
                                                    .TotalPostsAcrossAllPlatforms
                                                .tr(),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Gap(20.h),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                      child: DualActionButton(
                                        onTapButton1: () {
                                          (context)
                                              .push(RouteName.generalSetting)
                                              .then((_) {
                                            _userBloc.add(FetchUserEvent());
                                          });
                                        },
                                        onTapButton2: () {},
                                        textButton1:
                                            LocaleKeys.Editprofile.tr(),
                                        textButton2:
                                            LocaleKeys.Previewprofile.tr(),
                                      ),
                                    ),
                                    Gap(20.h),
                                    AddFriendContact(
                                      onTap: () => context
                                          .push(RouteName.connectFriendPage),
                                      currentFriends: currentFriends,
                                      totalFriends: totalFriends,
                                    ),
                                    PremiumSubscription(
                                      onTap: () {},
                                    ),
                                    MyOrderProfile(
                                      onTapViewAll: () {},
                                      onTapPay: () {},
                                      onTapShip: () {},
                                      onTapReceive: () {},
                                      onTapRate: () {},
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                    // SwitchToSeller(onTap: () {}),
                    SwitchCameraIcon(
                      onTap: () async {
                        final selectedPath =
                            await showImageChoices(context: context);
                        debugPrint('Selected path: "$selectedPath"');
                        if (selectedPath.isNotEmpty &&
                            selectedPath.trim().isNotEmpty) {
                          debugPrint(
                              'Triggering UpdateBackgroundUserEvent with path: $selectedPath');
                          _userBloc
                              .add(UpdateBackgroundUserEvent(selectedPath));
                        } else {
                          debugPrint(
                              'No image selected or empty path, skipping update');
                        }
                      },
                    ),
                  ],
                ),
                Gap(20.h),
                // PersonalSpaceProfile(
                //   onTapFavorite: () {},
                //   onTapMyCloud: () {},
                //   onTapMyPost: () {},
                //   onTapPurchase: () {},
                // ),
                // Gap(20.h),
                // const ModelSection(),
                if (userRole == "Seller mobile" || userRole == "Seller")
                  const SellerCenter(),
                // Gap(20.h),
                const SupportSection(),
                Gap(20.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
