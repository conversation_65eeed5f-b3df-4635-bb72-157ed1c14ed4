// ignore: must_be_immutable
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

// ignore: must_be_immutable
class UserCartInfor extends StatelessWidget {
  final bool isCheckPremium;
  final bool isCheckPremium2;
  String imgUser;
  String userName;
  String userEmail;

  UserCartInfor({
    super.key,
    required this.isCheckPremium,
    required this.imgUser,
    required this.userName,
    required this.userEmail,
    this.isCheckPremium2 = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          SizedBox(
            width: 100.w,
            height: 100.w,
            child: Stack(
              alignment: Alignment.centerLeft,
              children: [
                Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(99),
                      border: Border.all(
                          width: 2,
                          color: isCheckPremium ? Colors.amber : Colors.white)),
                  child: SizedBox(
                    height: 72.w,
                    width: 72.w,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(99),
                      child: imgUser.isNotEmpty
                          ? Image.network(
                              imgUser,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: Colors.grey[300],
                              alignment: Alignment.center,
                              child: Text(
                                userName.isNotEmpty
                                    ? userName[0].toUpperCase()
                                    : '?',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeBold,
                              ),
                            ),
                    ),
                  ),
                ),
                isCheckPremium
                    ? Positioned(
                        top: 0,
                        right: 8,
                        child: SvgPicture.asset(AppAssets.Frame1597884104))
                    : const SizedBox(),
                isCheckPremium
                    ? Positioned(
                        top: 65,
                        right: 20,
                        child: SvgPicture.asset(AppAssets.Frame1984078728))
                    : const SizedBox(),
              ],
            ),
          ),
          // Gap(5.h),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    userName,
                    style: Theme.of(context).textTheme.lightBodyXLargeBold,
                  ),
                  const Gap(10),
                  isCheckPremium2
                      ? SvgPicture.asset(AppAssets.verify)
                      : const SizedBox()
                ],
              ),
              Text(
                userEmail,
                style: Theme.of(context).textTheme.lightBodySmallMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
