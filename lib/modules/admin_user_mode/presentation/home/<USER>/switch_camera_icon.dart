import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../shared/widgets/image_widget/image_widget.dart';

class SwitchCameraIcon extends StatelessWidget {
  void Function()? onTap;
   SwitchCameraIcon({super.key, this.onTap,});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 168.h,
      right: 16.w,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(8.w),
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: CustomImage(
            path: AppAssets.cameraSvg,
            imageType: ImageType.svg,
            height: 20.w,
            width: 20.w,
          ),
        ),
      ),
    );
  }
}
