import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/modules/admin_user_mode/data/models/seller_profile_simplified.dart';
import 'package:multime_app/modules/admin_user_mode/data/repositories/seller_repository.dart';

part 'seller_center_event.dart';
part 'seller_center_state.dart';

class SellerCenterBloc extends Bloc<SellerCenterEvent, SellerCenterState> {
  final SellerRepository _sellerRepository;

  SellerCenterBloc(this._sellerRepository) : super(const SellerCenterState()) {
    on<LoadSellerProfile>(_onLoadSellerProfile);
    on<RefreshSellerProfile>(_onRefreshSellerProfile);
  }

  Future<void> _onLoadSellerProfile(
    LoadSellerProfile event,
    Emitter<SellerCenterState> emit,
  ) async {
    emit(state.copyWith(
      isLoading: true,
      errorMessage: '',
    ));

    try {
      final response = await _sellerRepository.getSeller(event.userId);

      if (response.status == Status.completed) {
        emit(state.copyWith(
          sellerResponse: response,
          isLoading: false,
          errorMessage: '',
        ));
      } else if (response.status == Status.error) {
        emit(state.copyWith(
          sellerResponse: response,
          isLoading: false,
          errorMessage: response.message ?? 'Failed to load seller profile',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'An unexpected error occurred: $e',
      ));
    }
  }

  Future<void> _onRefreshSellerProfile(
    RefreshSellerProfile event,
    Emitter<SellerCenterState> emit,
  ) async {
    // Force refresh by calling load again
    add(LoadSellerProfile(event.userId));
  }
}
