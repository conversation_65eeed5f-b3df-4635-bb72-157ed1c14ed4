import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/utils/extension.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../../../../shared/widgets/image_widget/image_widget.dart';

class SwitchToSeller extends StatelessWidget {
  void Function()? onTap;
   SwitchToSeller({super.key, this.onTap,});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 22.h,
      left: 0,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 42.h,
          padding: EdgeInsets.all(10.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(8.r),
              bottomRight: Radius.circular(8.r),
            ),
          ),
          child: Row(
            children: [
              CustomImage(
                path: AppAssets.shopSvg,
                imageType: ImageType.svg,
                height: 18.h,
                width: 18.h,
              ),
              5.horizontalSpace,
              Text(
                LocaleKeys.SwitchtoSeller.tr()  ,
                style: context.textTheme.bodySmall,
              ),
              8.horizontalSpace,
              Icon(
                Icons.arrow_forward_ios,
                size: 13.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
