import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>/user_cart_infor.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/di/locator.dart';
import '../../../../../core/domain/storages/global_storage.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../../../../core/themes/app_colors.dart';

Future<void> buildScription(BuildContext context) {
  return showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.white,
      barrierColor: Theme.of(context).greyScale700(context),
      isScrollControlled: true,
      builder: (context) {
        return FractionallySizedBox(
          heightFactor: 0.6.h,
          widthFactor: 1,
          child: const CustomSubscriptionWidget(),
        );
      });
}

class CustomSubscriptionWidget extends StatefulWidget {
  const CustomSubscriptionWidget({super.key});

  @override
  State<CustomSubscriptionWidget> createState() =>
      _CustomSubscriptionWidgetState();
}

class _CustomSubscriptionWidgetState extends State<CustomSubscriptionWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 14.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.radius16),
          topRight: Radius.circular(AppRadius.radius16),
        ),
        border: Border.all(
          color: Theme.of(context).greyScale900(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(child: SvgPicture.asset(AppAssets.line126Svg)),
          Gap(10.h),
          UserCartInfor(
            isCheckPremium: false,
            isCheckPremium2: true,
            imgUser: getIt<GlobalStorage>().user?.profilePicture ?? '',
            userName: getIt<GlobalStorage>().user?.fullName ?? "Unknown",
            userEmail: getIt<GlobalStorage>().user?.email ?? "Unknown",
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Divider(
                    color: Color.fromARGB(255, 204, 203, 203), thickness: 1),
                Gap(10.h),
                Text(
                  LocaleKeys.VoiceTranslatePlan.tr(),
                  style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                ),
                Gap(10.h),
                Text(
                  LocaleKeys.benefitsOfSubscribing.tr(),
                  style: Theme.of(context).textTheme.lightBodyMediumRegular,
                ),
                Gap(15.h),
                _ticketTitle(
                  context: context,
                  title: LocaleKeys.Unlimitedaccesstovoice.tr(),
                  svgImg: AppAssets.tick23,
                ),
                Gap(10.h),
                _ticketTitle(
                  context: context,
                  title: LocaleKeys.Instanttranslationformessaging.tr(),
                  svgImg: AppAssets.tick23,
                ),
                Gap(10.h),
                _ticketTitle(
                  context: context,
                  title: LocaleKeys.Cancelyoursubscriptionanytime.tr(),
                  svgImg: AppAssets.tick23,
                ),
                Gap(15.h),
                Container(
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(32),
                    color: Theme.of(context).errorLight(context),
                  ),
                  child: Text(
                    "\$15.00/mo",
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumSemiBold
                        .copyWith(
                          color: Theme.of(context).primary(context),
                        ),
                  ),
                ),
                Gap(20.h),
                DualActionButton2(
                  textButton1: LocaleKeys.Subscribe.tr(),
                  textButton2: LocaleKeys.ViewallourUpgrades.tr(),
                  onTapButton1: () {
                    Navigator.pop(context);
                  },
                  onTapButton2: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  _ticketTitle(
      {required BuildContext context,
      required String title,
      required String svgImg}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SvgPicture.asset(svgImg),
        Gap(5.w),
        Text(title, style: Theme.of(context).textTheme.lightBodyMediumRegular),
      ],
    );
  }
}

// ignore: must_be_immutable
class DualActionButton2 extends StatelessWidget {
  final String textButton1;
  final String textButton2;
  final Function()? onTapButton1;
  final Function()? onTapButton2;

  DualActionButton2({
    super.key,
    required this.textButton1,
    required this.textButton2,
    this.onTapButton1,
    this.onTapButton2,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTapButton1,
          child: Container(
            height: 50.h,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(
                width: 2,
                color: Theme.of(context).primary(context),
              ),
              borderRadius: BorderRadius.circular(8),
              color: AppColors.primary,
            ),
            child: Center(
              child: Text(
                textButton1,
                style:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Colors.white,
                        ),
              ),
            ),
          ),
        ),
        Gap(10.h),
        GestureDetector(
          onTap: onTapButton2,
          child: Container(
            height: 50.h,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(
                width: 1,
                color: Theme.of(context).secondary(context),
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(textButton2,
                  style: Theme.of(context).textTheme.lightBodySmallRegular),
            ),
          ),
        ),
      ],
    );
  }
}
