// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/l10n/locale_keys.g.dart';

class PremiumSubscription extends StatelessWidget {
  void Function()? onTap;
  PremiumSubscription({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(context).primary(context),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.VoiceTranslatePlan.tr(),
              style:
                  Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w800,
                      ),
            ),
            Container(
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(32),
                color: Colors.white,
              ),
              child: Text(
                "\$15.00/mo",
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(
                      color: Theme.of(context).primary(context),
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
