import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/l10n/locale_keys.g.dart';

// ignore: must_be_immutable
class AddFriendContact extends StatelessWidget {
  AddFriendContact({
    super.key,
    required this.currentFriends,
    required this.totalFriends,
    required this.onTap,
  });

  final int currentFriends;
  final int totalFriends;
  void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    int addFriends = totalFriends - currentFriends;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12), color: Colors.white),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.ReferralProgram.tr(),
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).textSecondary(context),
                      ),
                ),
                const Gap(5),
                Row(
                  children: [
                    Text(
                      "$currentFriends/$totalFriends  ${LocaleKeys.Friends.tr()}",
                      style:
                          Theme.of(context).textTheme.lightBodyXLargeSemiBold,
                    ),
                    Gap(70.h),
                    SvgPicture.asset(AppAssets.user_add)
                  ],
                ),
                const Gap(8),
                SizedBox(
                  width: 190.w,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: currentFriends / totalFriends,
                      backgroundColor: Colors.grey[300],
                      color: Colors.red,
                      minHeight: 6,
                    ),
                  ),
                ),
                const Gap(5),
                Text(
                  "${LocaleKeys.Add.tr()} $addFriends ${LocaleKeys.morepeopletoget.tr()}",
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallRegular
                      .copyWith(
                        color: Theme.of(context).textSecondary(context),
                      ),
                ),
              ],
            ),
            Image.asset("assets/svg/admin_user_icon/Asset 8 1.png")
          ],
        ),
      ),
    );
  }
}
