import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../../../application/bottom_bar/bloc/application_bloc.dart';
import '../../../../application/bottom_bar/bloc/application_event.dart';

class ModelSection extends StatelessWidget {
  const ModelSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "multi.Me modes",
            style: Theme.of(context)
                .textTheme
                .lightBodyLargeRegular
                .copyWith(color: Colors.grey),
          ),
          Gap(10.h),
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: (1 / .45),
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            children: [
              _buildModeCard(
                onTap: () => context.go(RouteName.socialMode),
                context: context,
                iconSvg: AppAssets.social_icon,
                title: LocaleKeys.SocialMode.tr(),
                subtitle: LocaleKeys.manageYourConnection.tr(),
                borderColor: Theme.of(context).errorBase(context),
              ),
              _buildModeCard(
                onTap: () => context.go(RouteName.businesMode),
                context: context,
                iconSvg: AppAssets.business_icon,
                title: LocaleKeys.BusinessMode.tr(),
                subtitle: LocaleKeys.connectToPartnerChances.tr(),
                borderColor: Theme.of(context).informationBase(context),
              ),
              _buildModeCard(
                onTap: () {
                  context.go(RouteName.newPage);
                  context
                      .read<ApplicationBloc>()
                      .add(ApplicationSelectIndex(0));
                },
                context: context,
                iconSvg: AppAssets.new_icon,
                title: LocaleKeys.newsMode.tr(),
                subtitle: LocaleKeys.readAndShare.tr(),
                borderColor: Theme.of(context).errorBase(context),
              ),
              _buildModeCard(
                onTap: () {
                  context.go(RouteName.marketMode);
                  context
                      .read<ApplicationBloc>()
                      .add(ApplicationSelectIndex(0));
                },
                context: context,
                iconSvg: AppAssets.market_icon,
                title: LocaleKeys.marketplace.tr(),
                subtitle: LocaleKeys.Shoppingproducts.tr(),
                borderColor: Theme.of(context).focusHighlightBase(context),
              ),
              _buildModeCard(
                onTap: () => context.go(RouteName.datingMode),
                context: context,
                iconSvg: AppAssets.dating_icon,
                title: LocaleKeys.datingMode.tr(),
                subtitle: LocaleKeys.findRealPartnerForYou.tr(),
                borderColor: Theme.of(context).errorBase(context),
              ),
              _buildModeCard(
                onTap: () {
                  context.go(RouteName.homeStrongBody);
                  context
                      .read<ApplicationBloc>()
                      .add(ApplicationSelectIndex(0));
                },
                context: context,
                iconSvg: AppAssets.strongbody_icon,
                title: 'StrongBody.ai',
                subtitle: LocaleKeys.Serviceforyouglobally.tr(),
                borderColor: Theme.of(context).successDark(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModeCard({
    required String iconSvg,
    required String title,
    required String subtitle,
    required Color borderColor,
    required BuildContext context,
    void Function()? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: borderColor, width: 1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              iconSvg,
              width: 30,
            ),
            Gap(12.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(title,
                    style: Theme.of(context).textTheme.lightBodyMediumBold),
                SizedBox(
                  width: 100,
                  child: Text(
                    subtitle,
                    style: Theme.of(context).textTheme.lightBodyXSmallRegular,
                    softWrap: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
