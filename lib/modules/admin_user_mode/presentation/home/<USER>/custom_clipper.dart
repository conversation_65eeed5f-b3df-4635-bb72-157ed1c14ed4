import 'package:flutter/material.dart';

class CurvedTopClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();

    // Start at the top-left corner
    path.moveTo(0, size.height * 0.12);

    // First hill - flatter peak
    path.quadraticBezierTo(
      size.width * 0.25, // Control point x
      size.height * 0.035, // Control point y (flatter peak)
      size.width * 0.5, // End point x
      size.height * 0.12, // End point y (flatter end)
    );

    // Second hill - flatter peak
    path.quadraticBezierTo(
      size.width * 0.6, // Control point x
      size.height * 0.16, // Control point y (flatter peak)
      size.width * 0.8, // End point x
      size.height * 0.16, // End point y
    );

    // // Stable section
    // path.lineTo(size.width * 0.8, size.height * 0.5);

    // Final slope down - less steep
    path.quadraticBezierTo(
      size.width * 0.95, // Control point x
      size.height * 0.165, // Control point y (flatter dip)
      size.width, // End point x (bottom-right corner)
      size.height * 0.21, // End point y (flatter end)
    );

    // Fill the rest of the path
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
