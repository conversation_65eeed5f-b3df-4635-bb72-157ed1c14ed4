import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

// ignore: must_be_immutable
class DualActionButton extends StatelessWidget {
  final String textButton1;
  final String textButton2;
  final Function()? onTapButton1;
  final Function()? onTapButton2;

  DualActionButton({
    super.key,
    required this.textButton1,
    required this.textButton2,
    this.onTapButton1,
    this.onTapButton2,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: onTapButton1,
            child: Container(
              height: 50.h,
              decoration: BoxDecoration(
                border: Border.all(
                  width: 2,
                  color: Theme.of(context).secondaryBase(context),
                ),
                borderRadius: BorderRadius.circular(8),
                color: Theme.of(context).secondaryBase(context),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(AppAssets.penMessageSvg),
                    const Gap(5),
                    Text(
                      textButton1,
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallRegular
                          .copyWith(
                            color: Colors.white,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: 15.h),
        Expanded(
          child: GestureDetector(
            onTap: onTapButton2,
            child: Container(
              height: 50.h,
              decoration: BoxDecoration(
                border: Border.all(
                  width: 1,
                  color: Theme.of(context).secondary(context),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(AppAssets.login_2),
                    const Gap(5),
                    Text(
                      textButton2,
                      style: Theme.of(context).textTheme.lightBodySmallRegular,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
