import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import 'selection_header.dart';

// ignore: must_be_immutable
class MyOrderProfile extends StatelessWidget {
  void Function()? onTapViewAll;
  void Function()? onTapPay;
  void Function()? onTapShip;
  void Function()? onTapReceive;
  void Function()? onTapRate;

  MyOrderProfile(
      {super.key,
      this.onTapViewAll,
      this.onTapPay,
      this.onTapShip,
      this.onTapRate,
      this.onTapReceive});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SectionHeader(
              onViewAll: onTapViewAll,
              title: LocaleKeys.MyOrder.tr(),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _selectionOrder(
                context,
                LocaleKeys.ToPay.tr(),
                AppAssets.cards,
                onTapPay,
              ),
              _selectionOrder(
                context,
                LocaleKeys.ToShip.tr(),
                AppAssets.truckFast,
                onTapPay,
              ),
              _selectionOrder(
                context,
                LocaleKeys.ToReceive.tr(),
                AppAssets.box,
                onTapReceive,
              ),
              _selectionOrder(
                context,
                LocaleKeys.ToRate.tr(),
                AppAssets.star,
                onTapRate,
              )
            ],
          ),
          const Gap(16),
        ],
      ),
    );
  }

  _selectionOrder(BuildContext context, String text, String svgImg,
      void Function()? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Center(
        child: Column(
          children: [
            SvgPicture.asset(svgImg),
            const Gap(5),
            Text(
              text,
              style: Theme.of(context).textTheme.lightBodyLargeRegular,
            ),
          ],
        ),
      ),
    );
  }
}
