import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/l10n/locale_keys.g.dart';

class SupportSection extends StatelessWidget {
  const SupportSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocaleKeys.Support.tr(),
              style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color: Colors.black,
                  ),
            ),
            Gap(10.h),
            ListTile(
              leading: SvgPicture.asset(AppAssets.support24),
              title: Text(
                LocaleKeys.HelpCenter.tr(),
                style: Theme.of(context).textTheme.lightBodyMediumRegular,
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                (context).push(RouteName.helpCenter);
              },
            ),
            ListTile(
              leading: SvgPicture.asset(AppAssets.support_agent),
              title: Text(
                LocaleKeys.ChatwithMultime.tr(),
                style: Theme.of(context).textTheme.lightBodyMediumRegular,
              ),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }
}

// class SupportTile extends StatelessWidget {
//   final String title;
//   final IconData icon;
//   final VoidCallback onTap;

//   const SupportTile({
//     Key? key,
//     required this.title,
//     required this.icon,
//     required this.onTap,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return ListTile(
//       leading: Icon(icon),
//       title: Text(title),
//       trailing: const Icon(Icons.arrow_forward_ios, size: 16),
//       onTap: onTap,
//     );
//   }
// }
