import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/l10n/locale_keys.g.dart';

class SectionHeader extends StatelessWidget {
  final String title;
  final VoidCallback? onViewAll;

  const SectionHeader({super.key, required this.title, this.onViewAll});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                color: Colors.black,
              ),
        ),
        // GestureDetector(
        //   onTap: onViewAll,
        //   child: Text(
        //     LocaleKeys.viewAll.tr(),
        //     style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
        //           color: Colors.blue,
        //         ),
        //   ),
        // ),
      ],
    );
  }
}
