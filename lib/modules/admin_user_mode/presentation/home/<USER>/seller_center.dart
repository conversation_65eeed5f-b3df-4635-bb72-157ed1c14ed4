import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/base/api_response/status.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/di/locator.dart';
import '../../../../../core/domain/storages/global_storage.dart';
import '../../../../../core/network/api.dart';
import '../../../../../shared/widgets/image_widget/image_widget.dart';
import '../../../data/repositories/seller_repository.dart';
import '../bloc/seller_center_bloc.dart';

class SellerCenter extends StatefulWidget {
  const SellerCenter({super.key});

  @override
  State<SellerCenter> createState() => _SellerCenterState();
}

class _SellerCenterState extends State<SellerCenter> {
  late SellerCenterBloc _sellerCenterBloc;

  @override
  void initState() {
    super.initState();
    _sellerCenterBloc = SellerCenterBloc(
      SellerRepositoryRemote(getIt<ApiClient>(), getIt<GlobalStorage>()),
    );

    // Load seller profile on init
    final currentUser = getIt<GlobalStorage>().user;
    if (currentUser != null && currentUser.id != null) {
      _sellerCenterBloc.add(LoadSellerProfile(currentUser.id!));
    }
  }

  @override
  void dispose() {
    _sellerCenterBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _sellerCenterBloc,
      child: BlocBuilder<SellerCenterBloc, SellerCenterState>(
        builder: (context, state) {
          return Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                ),
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with title and View Shop button
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Seller Center",
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w300,
                              color: const Color.fromARGB(255, 67, 67, 67),
                            ),
                          ),
                        ],
                      ),
                      Gap(20.h),

                      // Profile section
                      _buildProfileSection(context, state),
                      Gap(10.h),

                      // Action buttons
                      SizedBox(
                        width: double.infinity,
                        height: 135.h,
                        child: Column(
                          children: [
                            SellerButton(
                              onTap: () =>
                                  (context).push(RouteName.createService),
                              title: "Create a service",
                              color: Colors.green,
                            ),
                            Divider(),
                            SellerButton(
                              onTap: () {},
                              title: "Upload a product",
                              color: Colors.orange,
                            ),
                            Divider(),
                          ],
                        ),
                      ),
                      Gap(15.h),

                      // Menu grid
                      _buildMenuGrid(context, state),
                    ],
                  ),
                ),
              ),
              Gap(20.h),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, SellerCenterState state) {
    if (state.isLoading) {
      return Row(
        children: [
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(25.r),
            ),
            child: CircularProgressIndicator(),
          ),
          Gap(12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 120.w,
                  height: 16.h,
                  color: Colors.grey.shade200,
                ),
                Gap(4.h),
                Container(
                  width: 80.w,
                  height: 14.h,
                  color: Colors.grey.shade200,
                ),
              ],
            ),
          ),
        ],
      );
    }

    if (state.sellerResponse.status == Status.error ||
        state.errorMessage.isNotEmpty) {
      return Row(
        children: [
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(25.r),
            ),
            child: Icon(Icons.error, color: Colors.red),
          ),
          Gap(12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Error loading profile",
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
                Text(
                  state.errorMessage,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    final seller = state.sellerResponse.data;
    final currentUser = getIt<GlobalStorage>().user;

    return Row(
      children: [
        Container(
          width: 50.w,
          height: 50.w,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(25.r),
            child: CustomImage(
              imageType: ImageType.network,
              path: seller?.avatarShop ??
                  currentUser?.profilePicture ??
                  "https://giaoduc247.vn/uploads/082021/images/bkhn.png",
            ),
          ),
        ),
        Gap(12.w),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    seller?.shopName ??
                        seller?.userName ??
                        currentUser?.fullName ??
                        "Shop Name",
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(
                    width: 150.w,
                    child: Text(
                      seller?.linkShop != null && seller!.linkShop.isNotEmpty
                          ? seller.linkShop
                          : "No shop link available",
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        overflow: TextOverflow.ellipsis,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),
              GestureDetector(
                onTap: () {
                  // View Profile seller
                },
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black87),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    "View Shop",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMenuGrid(BuildContext context, SellerCenterState state) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      crossAxisSpacing: 16.w,
      mainAxisSpacing: 16.h,
      childAspectRatio: 1.4,
      children: [
        _buildMenuItem(
          icon: AppAssets.icon1Svg,
          title: "Orders",
          color: Colors.blue,
          onTap: () {},
          notificationCount: state.sellerResponse.data?.totalOrders,
        ),
        _buildMenuItem(
          icon: AppAssets.icon2Svg,
          title: "My Product",
          color: Colors.orange,
          onTap: () => context.push(RouteName.myProductPage),
          notificationCount: state.sellerResponse.data?.totalProducts,
        ),
        _buildMenuItem(
          icon: AppAssets.icon3Svg,
          title: "My Service",
          color: Colors.green,
          onTap: () {},
          notificationCount: state.sellerResponse.data?.totalServices,
        ),
        _buildMenuItem(
          icon: AppAssets.icon4Svg,
          title: "Requests",
          color: Colors.pink,
          onTap: () {},
          notificationCount: 0,
        ),
        _buildMenuItem(
          icon: AppAssets.icon5Svg,
          title: "My offers",
          color: Colors.purple,
          onTap: () {},
          notificationCount: 0,
        ),
        _buildMenuItem(
          icon: AppAssets.icon6Svg,
          title: "Balance",
          color: Colors.deepOrange,
          onTap: () {},
          notificationCount: 0,
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required String icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    int? notificationCount,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Center(
            child: Container(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 40.w,
                    height: 40.w,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: SvgPicture.asset(
                      icon,
                      width: 20.w,
                      height: 20.w,
                    ),
                  ),
                  Gap(8.h),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          // Notification badge
          if (notificationCount != null && notificationCount > 0)
            Positioned(
              right: 22,
              child: Container(
                constraints: BoxConstraints(minWidth: 16.w),
                height: 20.h,
                width: 20.h,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(99.r),
                  border: Border.all(color: Colors.white, width: 1.w),
                ),
                child: Center(
                  child: Text(
                    notificationCount > 99
                        ? '99+'
                        : notificationCount.toString(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class SellerButton extends StatelessWidget {
  final String title;
  final Color color;
  final VoidCallback onTap;

  const SellerButton({
    super.key,
    required this.title,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 50.h,
        // color: Colors.yellow,
        padding: EdgeInsets.symmetric(
          vertical: 12.h,
          horizontal: 16.w,
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              AppAssets.addSquareSvg,
              color: color,
              width: 24.w,
              height: 24.w,
            ),
            Gap(10.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
