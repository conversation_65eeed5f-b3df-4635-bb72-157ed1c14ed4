part of 'seller_center_bloc.dart';

class SellerCenterState {
  final ApiResponse<SellerProfileSimplified> sellerResponse;
  final bool isLoading;
  final String errorMessage;

  const SellerCenterState({
    this.sellerResponse = const ApiResponse.loading(),
    this.isLoading = false,
    this.errorMessage = '',
  });

  SellerCenterState copyWith({
    ApiResponse<SellerProfileSimplified>? sellerResponse,
    bool? isLoading,
    String? errorMessage,
  }) {
    return SellerCenterState(
      sellerResponse: sellerResponse ?? this.sellerResponse,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
