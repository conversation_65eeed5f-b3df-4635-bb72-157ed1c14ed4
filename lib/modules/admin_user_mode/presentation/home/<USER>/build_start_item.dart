import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

// ignore: must_be_immutable
class BuildStatItem extends StatelessWidget {
  String value;
  String label;
  String svgImg;
  BuildStatItem(
      {super.key,
      required this.value,
      required this.label,
      required this.svgImg});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            SvgPicture.asset(svgImg),
            const Gap(5),
            Text(
              value,
              style: Theme.of(context).textTheme.lightBodyXLargeBold,
            ),
          ],
        ),
        SizedBox(
          width: 90.w,
          child: Text(
            label,
            style: Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                  color: Theme.of(context).textSecondary(context),
                ),
          ),
        ),
      ],
    );
  }
}
