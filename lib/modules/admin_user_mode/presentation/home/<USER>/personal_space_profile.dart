// ignore: must_be_immutable
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/constants/app_assets.dart';

// ignore: must_be_immutable
class PersonalSpaceProfile extends StatelessWidget {
  void Function()? onTapMyCloud;
  void Function()? onTapMyPost;
  void Function()? onTapFavorite;
  void Function()? onTapPurchase;

  PersonalSpaceProfile({
    super.key,
    this.onTapMyCloud,
    this.onTapMyPost,
    this.onTapFavorite,
    this.onTapPurchase,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              "Personal Space",
              style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color: Theme.of(context).greyScale600(context),
                  ),
            ),
          ),
          Gap(10.h),
          SizedBox(
            height: 65.h,
            width: double.infinity,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _selectionOrder(
                  context,
                  "My Cloud",
                  AppAssets.cloud_icon,
                  onTapMyCloud,
                ),
                _selectionOrder(
                  context,
                  "My Post",
                  AppAssets.snow_icon,
                  onTapMyPost,
                ),
                _selectionOrder(
                  context,
                  "Favorite",
                  AppAssets.heart_icon,
                  onTapFavorite,
                ),
                _selectionOrder(
                  context,
                  "Purchase",
                  AppAssets.wind_icon,
                  onTapPurchase,
                ),
              ],
            ),
          ),
          Gap(10.h),
        ],
      ),
    );
  }

  Widget _selectionOrder(
      BuildContext context, String text, String svgImg, VoidCallback? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.only(left: 10.h),
        width: 120.h,
        height: 50.h,
        child: Column(
          children: [
            SvgPicture.asset(svgImg),
            Gap(5.h),
            Text(
              text,
              style: Theme.of(context).textTheme.lightBodyLargeRegular,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
