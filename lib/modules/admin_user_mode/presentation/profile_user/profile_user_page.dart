import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class ProfileUserPage extends StatefulWidget {
  const ProfileUserPage({super.key});

  @override
  State<ProfileUserPage> createState() => _ProfileUserPageState();
}

class _ProfileUserPageState extends State<ProfileUserPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.red,
          ),
          onPressed: () {
            context.pop(context);
          },
        ),
        title: Row(
          children: [
            Text(
              LocaleKeys.MyProfile.tr(),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).secondary(context),
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          MyAccountSection(),
        ],
      ),
    );
  }
}

class MyAccountSection extends StatelessWidget {
  final List<String> accountSettings = [
    LocaleKeys.General.tr(),
    LocaleKeys.MyAddresses.tr(),
    LocaleKeys.CreditDebitCard.tr(),
    LocaleKeys.passwordChanged.tr(),
    LocaleKeys.NotificationSetting.tr(),
    'Language',
  ];

  MyAccountSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              LocaleKeys.myAccount.tr(),
              style:  Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).secondary(context),
                fontSize: 16.sp,
              ),
            ),
          ),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: accountSettings.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(
                  accountSettings[index],
                  style:Theme.of(context).textTheme.displayLarge?.copyWith(
                    color: Theme.of(context).secondary(context),
                    fontSize: 16.sp,
                  ),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              );
            },
          ),
        ],
      ),
    );
  }
}
