import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/complete_user_registration/widgets/card_contact.dart';
import 'package:multime_app/modules/auth_mode/presentation/complete_user_registration/widgets/card_icon.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/home_page/session_heading.dart';
import 'package:multime_app/share_native/share_native.dart';
import '../../../../../core/constants/app_assets.dart';

class ConnectFriendPage extends StatefulWidget {
  const ConnectFriendPage({super.key});

  @override
  State<ConnectFriendPage> createState() => _ConnectFriendPageState();
}

class _ConnectFriendPageState extends State<ConnectFriendPage> {
  List arr = [
    CardContactConnect(
      name: 'Soai',
      phone: '123456',
      backgroundColor: Colors.white,
      borderColor: Colors.red,
      widget: 109.w,
      label: 'Connected',
      onPressed: () {},
      textColor: Colors.red,
    ),
    CardContactConnect(
      name: 'Soai',
      phone: '123456',
      backgroundColor: Colors.red,
      widget: 91.w,
      label: 'Connect',
      onPressed: () {},
      textColor: Colors.white,
    ),
    CardContactConnect(
      name: 'Soai',
      phone: '123456',
      backgroundColor: Colors.red,
      widget: 91.w,
      label: 'Connect',
      onPressed: () {},
      textColor: Colors.white,
    ),
    CardContactConnect(
      name: 'Soai',
      phone: '123456',
      backgroundColor: Colors.red,
      widget: 91.w,
      label: 'Connect',
      onPressed: () {},
      textColor: Colors.white,
    ),
    CardContactConnect(
      name: 'Soai',
      phone: '123456',
      backgroundColor: Colors.red,
      widget: 91.w,
      label: 'Connect',
      onPressed: () {},
      textColor: Colors.white,
    ),
  ];

  List arrChat = [
    {
      'image': AppAssets.facebookIcon,
      'title': 'Messenger',
    },
    {
      'image': AppAssets.instagramIcon,
      'title': 'Instagram',
    },
    {
      'image': AppAssets.snapchatIcon,
      'title': 'Snapchat',
    },
    {
      'image': AppAssets.chatSvg,
      'title': 'Chat',
    },
  ];
  TextEditingController copyLinkController = TextEditingController(
      text: 'https://network.strongbody.ai');
  TextEditingController copyLinkController1 =
      TextEditingController(text: 'https://s.net.vn/hIsx');

  @override
  void dispose() {
    copyLinkController.dispose();
    copyLinkController1.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 14.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Find friends?',
                style: Theme.of(context).textTheme.lightHeadingSmall,
              ),
              Gap(30.h),
              searchView(context, AppAssets.searchSvg,
                  LocaleKeys.SearchYourContacts.tr()),
              Gap(24.h),
              Container(
                height: 70.h,
                width: 382.w,
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).secondary(context),
                  borderRadius: BorderRadius.circular(8.h),
                ),
                child: Row(
                  children: [
                    Container(
                      height: 60.h,
                      width: 60.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).secondaryBase(context),
                        borderRadius: BorderRadius.circular(99),
                      ),
                      child: SvgPicture.asset(
                        AppAssets.contactsSvg,
                        color: Colors.white,
                      ),
                    ),
                    Gap(10.w),
                    RichText(
                        text: TextSpan(
                            text: 'Contacts\n',
                            style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                            children: [
                          TextSpan(
                              text: LocaleKeys.ContactsUsingPhoneNumber.tr(),
                              style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.white))
                        ])),
                    Gap(30.h),
                  ],
                ),
              ),
              Gap(24.h),
              Container(
                height: 70.h,
                width: 382.w,
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.h),
                  border: Border.all(
                    color: AppColors.secondary,
                    width: 1.w,
                  ),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      height: 60.h,
                      width: 60.w,
                      child: SvgPicture.asset(AppAssets.personSvg),
                    ),
                    Gap(10.w),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          text: '${LocaleKeys.AutoAddFirend.tr()}\n',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).secondary(context),
                          ),
                          children: [
                            TextSpan(
                              text: LocaleKeys.AutomaticallyAddContactsAsFriends
                                  .tr(),
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w400,
                                color: Theme.of(context).secondary(context),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    Gap(10.h),
                    Container(
                      width: 54.w,
                      height: 40.h,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Theme.of(context).secondary(context),
                        borderRadius: BorderRadius.circular(10.h),
                      ),
                      child: Text(
                        LocaleKeys.OK.tr(),
                        style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade50),
                      ),
                    ),
                  ],
                ),
              ),
              Gap(10.h),
              TSectionHeading(
                title: LocaleKeys.AddYourContact.tr(),
                showActionButton: false,
              ),
              Gap(20.h),
              SizedBox(
                  height: 80.h,
                  child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) {
                        ShareNative shareNative = ShareNative();
                        return CardIcon(
                          obj: arrChat[index],
                          onTap: () {
                            if (index == 0) {
                              shareNative.shareToMessenger(
                                  'https://www.facebook.com/soaidoviet',
                                  context);
                            } else if (index == 1) {
                              shareNative.shareToInstagram(
                                  'https://www.facebook.com/soaidoviet',
                                  context);
                            } else if (index == 2) {
                              shareNative.shareToSnapchat(
                                  'https://www.facebook.com/soaidoviet',
                                  context);
                            } else {
                              shareNative.shareToSMS(
                                  'https://www.facebook.com/soaidoviet',
                                  context);
                            }
                          },
                        );
                      },
                      separatorBuilder: (context, index) => Gap(40.w),
                      itemCount: arrChat.length)),
              Gap(24.h),

              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                separatorBuilder: (context, index) => Gap(20.h),
                itemCount: arr.length,
                itemBuilder: (context, index) {
                  return arr[index];
                },
              ),
              Gap(16.h),
              TSectionHeading(
                title: LocaleKeys.CoppyLink.tr(),
                showActionButton: false,
              ),
              Gap(10.h),
              searchView(
                  context, AppAssets.copySvg, LocaleKeys.CopyLinkToShare.tr(),
                  controller: copyLinkController),
              Gap(10.h),
              Center(
                child: Text(
                  LocaleKeys.or.tr(),
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumMedium
                      .copyWith(
                        color: Theme.of(context).secondary(context),
                      ),
                ),
              ),
              Gap(10.h),
              searchView(
                  context, AppAssets.copySvg, LocaleKeys.CopyLinkToShare.tr(),
                  controller: copyLinkController1),
              Gap(30.h),
              // StrongBodyButton(
              //   label: LocaleKeys.continueButton.tr(),
              //   onPressed: () {},
              //   backgroundColor: Colors.grey.shade300,
              //   height: 50.h,
              //   width: double.infinity,
              //   textColor: Colors.white,
              // ),
              Gap(50.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget searchView(BuildContext context, String image, String hintText,
      {TextEditingController? controller}) {
    return Row(
      children: [
        Container(
          width: 340.w,
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15.h),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1.w,
            ),
          ),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: SvgPicture.asset(image),
              ),
              Expanded(
                child: SizedBox(
                  height: 45.w,
                  child: TextField(
                    readOnly: true,
                    controller: controller,
                    keyboardType: TextInputType.multiline,
                    decoration: InputDecoration(
                      hintText: hintText,
                      contentPadding: const EdgeInsets.fromLTRB(2, 5, 2, 5),
                      border: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      disabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.transparent,
                        ),
                      ),
                      hintStyle:
                          Theme.of(context).textTheme.lightBodyMediumRegular,
                    ),
                    style: Theme.of(context).textTheme.lightBodyMediumRegular,
                    autocorrect: false,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
