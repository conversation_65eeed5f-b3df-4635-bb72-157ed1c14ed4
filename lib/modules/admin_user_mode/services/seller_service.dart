import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/modules/admin_user_mode/data/models/seller_profile_simplified.dart';
import 'package:multime_app/modules/admin_user_mode/data/repositories/seller_repository.dart';

/// Example service to test getSeller API
class SellerService {
  static Future<void> testGetSellerAPI() async {
    try {
      final currentUser = getIt<GlobalStorage>().user;
      if (currentUser == null || currentUser.id == null) {
        print('No current user found');
        return;
      }

      final sellerRepository =
          SellerRepositoryRemote(getIt<ApiClient>(), getIt<GlobalStorage>());
      final response = await sellerRepository.getSeller(currentUser.id!);

      print('=== getSeller API Test Result ===');
      print('Status: ${response.status}');

      if (response.data != null) {
        final seller = response.data!;
        print('Seller ID: ${seller.id}');
        print('User ID: ${seller.userId}');
        print('Shop Name: ${seller.shopName}');
        print('Link Shop: ${seller.linkShop}');
        print('Avatar Shop: ${seller.avatarShop}');
        print('User Name: ${seller.userName}');
        print('User Email: ${seller.userEmail}');
        print('Views: ${seller.views}');
        print('Score Profile: ${seller.scoreProfile}');
        print('Total Products: ${seller.totalProducts}');
        print('Total Services: ${seller.totalServices}');
        print('Total Orders: ${seller.totalOrders}');
        print('Rating: ${seller.rating}');
      } else {
        print('Error: ${response.message}');
      }
      print('=== End Test Result ===');
    } catch (e) {
      print('Error testing getSeller API: $e');
    }
  }

  /// Mock data for testing when API is not available
  static SellerProfileSimplified getMockSellerData() {
    return const SellerProfileSimplified(
      id: 1,
      userId: 123,
      shopName: "Dr. Vandana Shiva's Shop",
      linkShop: "https://shop.example.com",
      avatarShop: "https://giaoduc247.vn/uploads/082021/images/bkhn.png",
      views: 1250,
      scoreProfile: 4.8,
      userName: "Dr. Vandana Shiva",
      userEmail: "<EMAIL>",
      totalProducts: 15,
      totalServices: 8,
      totalOrders: 42,
      rating: 4.7,
    );
  }
}
