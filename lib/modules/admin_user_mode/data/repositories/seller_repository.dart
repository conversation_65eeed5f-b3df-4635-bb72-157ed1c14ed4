import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/admin_user_mode/data/models/seller_profile_simplified.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/domain/storages/global_storage.dart';

abstract class SellerRepository {
  Future<ApiResponse<SellerProfileSimplified>> getSeller(int userId);
}

class SellerRepositoryRemote implements SellerRepository {
  final ApiClient _apiClient;
  final GlobalStorage _globalStorage;

  SellerRepositoryRemote(this._apiClient, this._globalStorage);

  @override
  Future<ApiResponse<SellerProfileSimplified>> getSeller(int userId) async {
    try {
      // Replace {id} in the endpoint with actual userId
      final path = ApiConst.getSeller.replaceAll('{id}', userId.toString());

      final response = await _apiClient.request(
        path: path,
        method: ApiType.get,
        headers: {
          'Authorization': 'Bearer ${_globalStorage.accessToken}',
          'Scope': AppConstants.multi_me,
          'x-api-key': 'your_api_key'
        },
      );

      print('API Response for getSeller: $response');

      // Check if response is successful
      if (response['code'] != 0) {
        return ApiResponse.error(
            response['message'] ?? 'Failed to get seller profile');
      }

      final data = response['data'];
      if (data == null) {
        return ApiResponse.error('No seller data found');
      }

      final sellerProfile = SellerProfileSimplified.fromJson(data);
      return ApiResponse.completed(sellerProfile);
    } catch (e) {
      print('Error in getSeller: $e');
      return ApiResponse.error('Failed to load seller profile: $e');
    }
  } 
}
