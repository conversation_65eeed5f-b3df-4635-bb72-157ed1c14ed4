/// Simplified Seller Profile Model for SellerCenter widget
/// Contains only essential information needed for display
class SellerProfileSimplified {
  final int id;
  final int userId;
  final String shopName;
  final String linkShop;
  final String avatarShop; // profile picture from user
  final int views;
  final double scoreProfile;

  // User basic info
  final String userName;
  final String userEmail;

  // SellerCenter statistics
  final int totalProducts;
  final int totalServices;
  final int totalOrders;
  final double rating;

  const SellerProfileSimplified({
    required this.id,
    required this.userId,
    required this.shopName,
    required this.linkShop,
    required this.avatarShop,
    required this.views,
    required this.scoreProfile,
    required this.userName,
    required this.userEmail,
    required this.totalProducts,
    required this.totalServices,
    required this.totalOrders,
    required this.rating,
  });

  factory SellerProfileSimplified.fromJson(Map<String, dynamic> json) {
    final user = json['user'] as Map<String, dynamic>? ?? {};

    return SellerProfileSimplified(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      shopName: json['shop_name'] ?? '',
      linkShop: json['link_shop'] ?? '',
      avatarShop: json['banner_picture'] ?? '',
      views: json['views'] ?? 0,
      scoreProfile: (json['score_profile'] ?? 0).toDouble(),
      userName: '${user['first_name'] ?? ''} ${user['last_name'] ?? ''}'.trim(),
      userEmail: user['email'] ?? '',
      // Calculate statistics from existing data
      totalProducts: _calculateTotalProducts(json),
      totalServices: _calculateTotalServices(json),
      totalOrders: _calculateTotalOrders(json),
      rating: _calculateAverageRating(json),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'shop_name': shopName,
      'link_shop': linkShop,
      'avatar_shop': avatarShop,
      'views': views,
      'score_profile': scoreProfile,
      'user_name': userName,
      'user_email': userEmail,
      'total_products': totalProducts,
      'total_services': totalServices,
      'total_orders': totalOrders,
      'rating': rating,
    };
  }

  // Helper methods to calculate statistics from response data
  static int _calculateTotalProducts(Map<String, dynamic> json) {
    try {
      final user = json['user'] as Map<String, dynamic>? ?? {};
      final products = user['products'] as List? ?? [];
      return products.length;
    } catch (e) {
      return 0;
    }
  }

  static int _calculateTotalServices(Map<String, dynamic> json) {
    try {
      // Services might be in different location in the response
      // Adjust this based on actual API response structure
      return 0; // Placeholder - adjust based on actual API response
    } catch (e) {
      return 0;
    }
  }

  static int _calculateTotalOrders(Map<String, dynamic> json) {
    try {
      // Orders might be calculated from product ratings/views
      // Adjust this based on actual API response structure
      return 0; // Placeholder - adjust based on actual API response
    } catch (e) {
      return 0;
    }
  }

  static double _calculateAverageRating(Map<String, dynamic> json) {
    try {
      final user = json['user'] as Map<String, dynamic>? ?? {};
      final products = user['products'] as List? ?? [];

      if (products.isEmpty) return 0.0;

      double totalRating = 0.0;
      int ratedProducts = 0;

      for (final product in products) {
        final rating = product['rating'];
        if (rating != null && rating > 0) {
          totalRating += rating.toDouble();
          ratedProducts++;
        }
      }

      return ratedProducts > 0 ? totalRating / ratedProducts : 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  @override
  String toString() {
    return 'SellerProfileSimplified(id: $id, shopName: $shopName, userName: $userName)';
  }
}
