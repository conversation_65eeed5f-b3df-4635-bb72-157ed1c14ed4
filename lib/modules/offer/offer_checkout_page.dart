import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/offer/offers.dart';
import 'package:multime_app/core/model/payment_result_data.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/date_time_utils.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/offer/presentation/bloc/offer_bloc.dart';
import 'package:multime_app/modules/offer/presentation/widgets/voucher_picker.dart';
import 'package:multime_app/service/pusher_service.dart';

import '../../core/services/stripe_service.dart';

class OfferCheckoutPage extends StatefulWidget {
  final int offerID;
  final Offers? offer;
  final OfferBloc? bloc;

  const OfferCheckoutPage({
    super.key,
    required this.offerID,
    this.offer,
    this.bloc,
  });

  @override
  State<OfferCheckoutPage> createState() => _OfferCheckoutPageState();
}

class _OfferCheckoutPageState extends State<OfferCheckoutPage> {
  // 'ListView.separated' of payment methods
  String _selectedMethod = 'stripe';
  final List<Map<String, dynamic>> paymentMethods = [
    {
      'id': 'stripe',
      'name': 'Stripe',
      'icon': AppAssets.stripe,
    },
  ];

  double _voucherDiscount = 0;
  final double _platformFee = 10;
  int? customerID;
  Timer? _timer;
  bool _isPaymentInProgress = false;
  String? _lastProcessedClientSecret; // Track processed client secrets

  double get total {
    final offer = widget.bloc?.state.offer ?? widget.offer;

    if (offer == null) return 0.0;
    return offer.price + _platformFee - _voucherDiscount;
  }

  String _getSelectedMethod() {
    return paymentMethods.firstWhere(
      (method) => method['id'] == _selectedMethod,
      orElse: () => {'name': 'Unknown'},
    )['name'] as String;
  }

  @override
  void initState() {
    super.initState();
    PusherService.initialize();
    customerID = gs.uid;

    // WidgetsBinding.instance.addPostFrameCallback(
    //   (_) {
    //     if (widget.bloc != null) {
    //       widget.bloc!.add(
    //         GetOffer(
    //           offerID: widget.offerID,
    //         ),
    //       );
    //     }
    //   },
    // );
  }

  @override
  void dispose() {
    _timer?.cancel();
    if (customerID != null) {
      PusherService.unsubscribeFromOrderChannel(customerID!);
    }

    super.dispose();
  }

  void _handlePayment() {
    if (_isPaymentInProgress) return;

    if (_selectedMethod == 'stripe') {
      widget.bloc?.add(
        PayOfferWithStripe(
          offerID: widget.offerID,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This payment method isn\'t supported yet.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OfferBloc, OfferState>(
      listener: (context, state) {
        if (state.error != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('An unexpected error occurred: ${state.error}'),
            ),
          );
        }

        // Chỉ xử lý nếu có clientSecret mới và chưa có payment đang xử lý
        if (state.stripePaymentResponse?.clientSecret != null &&
            state.stripePaymentResponse!.clientSecret.isNotEmpty &&
            _lastProcessedClientSecret !=
                state.stripePaymentResponse!.clientSecret &&
            !_isPaymentInProgress) {
          _lastProcessedClientSecret =
              state.stripePaymentResponse!.clientSecret;

          setState(() {
            _isPaymentInProgress = true;
          });
          _showStripePaymentSheet(
            context,
            state.stripePaymentResponse!.clientSecret,
          );
        } else if (state.stripePaymentResponse?.clientSecret != null &&
            _isPaymentInProgress) {
          print('⏸️  Payment already in progress, ignoring new clientSecret');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'Checkout',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppAssets.arrowLeftSvg,
              width: 24.w,
              height: 24.h,
            ),
            onPressed: () => context.pop(),
          ),
          centerTitle: true,
        ),
        body: _buildCheckout(
          context,
          offer: widget.offer,
        ),
      ),
    );
  }

  Widget _buildCheckout(
    BuildContext context, {
    Offers? offer,
    OfferBloc? bloc,
  }) {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: PaddingConstants.padAll16.copyWith(
              bottom: 0.h,
            ),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: PaddingConstants.padAll16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).greyScale300(context),
                      ),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Seller',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: Theme.of(context).textSecondary(context),
                              ),
                        ),
                        Gap(2.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              (bloc != null)
                                  ? bloc.state.offer!.sellerName
                                  : offer!.sellerName,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    fontFamily: AppFontFamily.semiBold,
                                  ),
                            ),
                            Text(
                              (bloc != null)
                                  ? bloc.state.offer!.sellerPhone
                                  : offer!.sellerPhone,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                            ),
                          ],
                        ),
                        Gap(16.h),
                        Text(
                          'Buyer',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: Theme.of(context).textSecondary(context),
                              ),
                        ),
                        Gap(2.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              (bloc != null)
                                  ? bloc.state.offer!.customerName
                                  : offer!.customerName,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    fontFamily: AppFontFamily.semiBold,
                                  ),
                            ),
                            Text(
                              (bloc != null)
                                  ? bloc.state.offer!.customerPhone
                                  : offer!.customerPhone,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Gap(16.h),
                  Container(
                    width: double.infinity,
                    padding: PaddingConstants.padAll16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).greyScale300(context),
                      ),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Offer details',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        Gap(12.h),
                        Container(
                          width: double.infinity,
                          padding: PaddingConstants.padAll12,
                          decoration: BoxDecoration(
                            color: Theme.of(context).greyScale100(context),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Row(
                            children: [
                              _buildOfferPreview(context, offer),
                              Gap(16.w),
                              Expanded(
                                child: Text(
                                  (bloc != null)
                                      ? bloc.state.offer!.title
                                      : offer!.title,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Gap(12.h),
                        RichText(
                          text: TextSpan(
                            style: Theme.of(context).textTheme.bodyMedium,
                            children: [
                              TextSpan(
                                text: 'Price: ',
                              ),
                              TextSpan(
                                text: (bloc != null)
                                    ? '\$${TFormatter.formatPrice(bloc.state.offer!.price)}'
                                    : '\$${TFormatter.formatPrice(offer!.price)}',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontFamily: AppFontFamily.bold,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        Gap(4.h),
                        RichText(
                          text: TextSpan(
                            style: Theme.of(context).textTheme.bodyMedium,
                            children: [
                              TextSpan(
                                text: 'Date: ',
                              ),
                              TextSpan(
                                text: (bloc != null)
                                    ? formatOfferDateTime(
                                        bloc.state.offer!.createdAt,
                                      )
                                    : formatOfferDateTime(
                                        offer!.createdAt,
                                      ),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontFamily: AppFontFamily.bold,
                                    ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  Gap(16.h),
                  Container(
                    width: double.infinity,
                    padding: PaddingConstants.padAll16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).greyScale300(context),
                      ),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Payment method',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            InkWell(
                              onTap: () {},
                              child: Text(
                                'Manage',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .informationBase(context),
                                      decoration: TextDecoration.underline,
                                      decorationColor: Theme.of(context)
                                          .informationBase(context),
                                    ),
                              ),
                            )
                          ],
                        ),
                        Gap(12.h),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: paymentMethods.length,
                          separatorBuilder: (context, index) => Divider(
                            color: Theme.of(context).greyScale100(context),
                          ),
                          itemBuilder: (context, index) {
                            final method = paymentMethods[index];

                            return RadioListTile(
                              onChanged: (value) {
                                setState(() {
                                  _selectedMethod = value!;
                                });
                              },
                              value: method['id']!,
                              groupValue: _selectedMethod,
                              activeColor:
                                  Theme.of(context).informationBase(context),
                              contentPadding: EdgeInsets.zero,
                              visualDensity: VisualDensity.compact,
                              title: Row(
                                children: [
                                  Text(
                                    method['name']!,
                                    style:
                                        Theme.of(context).textTheme.bodyLarge,
                                  ),
                                  Gap(4.w),
                                  SvgPicture.asset(
                                    method['icon']!,
                                    height: 28.h,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                        Gap(16.h),
                        Text(
                          'To complete the transaction, we will transfer you to ${_getSelectedMethod()}\'s secure servers.',
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(
                                color: Theme.of(context).textSecondary(context),
                                fontFamily: AppFontFamily.regular,
                              ),
                        ),
                      ],
                    ),
                  ),
                  Gap(16.h),
                  Container(
                    width: double.infinity,
                    padding: PaddingConstants.padAll16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).greyScale300(context),
                      ),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Payment summary',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        Gap(12.h),
                        ElevatedButton(
                          onPressed: () {
                            showModalBottomSheet(
                              context: context,
                              backgroundColor:
                                  Theme.of(context).lightGrey(context),
                              isScrollControlled: true,
                              builder: (context) {
                                return VoucherPicker();
                              },
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).transparent(context),
                            foregroundColor:
                                Theme.of(context).textPrimary(context),
                            overlayColor: Theme.of(context)
                                .blackPrimary(context)
                                .withOpacity(0.005),
                            minimumSize: Size(double.infinity, 40.h),
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                color: Theme.of(context).textPrimary(context),
                              ),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ).copyWith(
                            elevation: WidgetStateProperty.all(0),
                          ),
                          child: Text('Select a voucher'),
                        ),
                        Gap(16.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Price',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                            ),
                            Text(
                              (bloc != null)
                                  ? '\$${TFormatter.formatPrice(bloc.state.offer!.price)}'
                                  : '\$${TFormatter.formatPrice(offer!.price)}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontFamily: AppFontFamily.bold,
                                  ),
                            ),
                          ],
                        ),
                        Gap(4.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Voucher',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                            ),
                            Text(
                              '-\$${TFormatter.formatPrice(_voucherDiscount)}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontFamily: AppFontFamily.bold,
                                  ),
                            ),
                          ],
                        ),
                        Gap(4.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Platform fee',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .textSecondary(context),
                                      ),
                                ),
                                Gap(4.w),
                                Icon(
                                  Icons.info_outline_rounded,
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  size: 20,
                                ),
                              ],
                            ),
                            Text(
                              '\$${TFormatter.formatPrice(_platformFee)}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontFamily: AppFontFamily.bold,
                                  ),
                            ),
                          ],
                        ),
                        Gap(16.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Total payment',
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                            Text(
                              '\$${TFormatter.formatPrice(total)}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color: Theme.of(context).primary(context),
                                    fontFamily: AppFontFamily.bold,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Gap(16.h),
                ],
              ),
            ),
          ),
        ),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).greyScale300(context),
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Total',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).textSecondary(context),
                          ),
                    ),
                    Text(
                      '\$${TFormatter.formatPrice(total)}',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: Theme.of(context).primary(context),
                                fontFamily: AppFontFamily.bold,
                              ),
                    ),
                  ],
                ),
              ),
              Gap(16.w),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: () => _handlePayment(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primary(context),
                    foregroundColor: Theme.of(context).whitePrimary(context),
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: ContinuousRectangleBorder(),
                  ),
                  child: Text(
                    'Continue',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).whitePrimary(context),
                          fontFamily: AppFontFamily.bold,
                        ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _showStripePaymentSheet(
    BuildContext context,
    String clientSecret,
  ) async {
    final offer = widget.bloc?.state.offer ?? widget.offer;
    if (offer == null) return;

    _listenOfferStatus(gs.uid!);

    try {
      final result = await StripePaymentService.processPayment(
        paymentIntentClientSecret: clientSecret,
        amount: total,
        currency: 'USD',
      );

      if (result['success'] == true) {
        _showPaymentProcessing();
        _startTimeout();
      } else {
        if (result['errorCode'] == 'user_cancelled') {
          print('👤 User cancelled payment');
          _resetOfferStatusListener();
          setState(() {
            _isPaymentInProgress = false;
          });
        } else {
          print('❌ Payment failed: ${result['error']}');
          _resetOfferStatusListener();
          setState(() {
            _isPaymentInProgress = false;
          });
          _paymentFailureNavigation(offer);
        }
      }
    } catch (e) {
      print('💥 Unexpected error: $e');
      _resetOfferStatusListener();
      setState(() {
        _isPaymentInProgress = false;
      });
      _paymentFailureNavigation(offer);
    }
  }

  void _listenOfferStatus(int offerID) {
    print('🔗 Setting up Pusher listener for Offer ID: $offerID');

    PusherService.subscribeToOrderChannel(
      customerID!,
      (offerUpdate) {
        if (offerUpdate.isPaymentSuccessful) {
          print('✅ Processing as successful payment...');
          _handlePaymentSuccess(offerUpdate);
        } else if (offerUpdate.isPaymentFailed) {
          print('❌ Processing as failed payment...');
          _handlePaymentFailure(offerUpdate);
        } else {
          print('⏳ Payment status pending or unknown...');
        }
      },
    );
  }

  void _resetOfferStatusListener() {
    _timer?.cancel();
    if (customerID != null) {
      PusherService.unsubscribeFromOrderChannel(customerID!);
    }
  }

  void _handlePaymentSuccess(dynamic offerUpdate) {
    _resetOfferStatusListener();
    _dismissPaymentProcessing();
    setState(() {
      _isPaymentInProgress = false;
    });

    final offer = widget.bloc?.state.offer ?? widget.offer;
    final successData = PaymentResultData.success(
      amount: total,
      itemName: offer?.title ?? '',
      quantity: 1,
      itemPrice: offer?.price ?? 0,
      imageUrl: offer?.files!
          .where((file) => file.type.contains('image'))
          .map((file) => file.url)
          .toList(),
    ).copyWith(
      paymentMethod: _selectedMethod,
      transactionDate: DateTime.now(),
      orderCode: offerUpdate.order_no,
      orderTime: DateTime.now(),
      orderStatus: offerUpdate.order_status,
      phone: offer?.customerPhone,
      recipientName: offer?.customerName,
      userId: offer?.customerID,
    );

    context.push(
      RouteName.paymentResultPage,
      extra: {
        'paymentResult': successData,
        'onPrimaryAction': () {},
        'onSecondaryAction': () {},
      },
    );
  }

  void _handlePaymentFailure(dynamic orderUpdate) {
    _resetOfferStatusListener();
    _dismissPaymentProcessing();
    setState(() {
      _isPaymentInProgress = false;
    });

    final offer = widget.bloc?.state.offer ?? widget.offer;
    _paymentFailureNavigation(
      offer,
      offerUpdate: orderUpdate,
    );
  }

  void _showPaymentProcessing() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'Processing Payment...',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Please wait while we confirm your order.',
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                'This may take up to 30 seconds.',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _paymentFailureNavigation(
    Offers? offer, {
    dynamic offerUpdate,
  }) {
    final failedData = PaymentResultData.failed(
      amount: total,
      itemName: offer?.title ?? '',
      quantity: 1,
      itemPrice: offer?.price ?? 0,
      imageUrl: offer?.files!
          .where((file) => file.type.contains('image'))
          .map((file) => file.url)
          .toList(),
    ).copyWith(
      paymentMethod: _selectedMethod,
      transactionDate: DateTime.now(),
      orderCode: offerUpdate?.order_no,
      orderTime: DateTime.now(),
      orderStatus: offerUpdate?.order_status,
      phone: offer?.customerPhone,
      recipientName: offer?.customerName,
      userId: offer?.customerID,
    );

    context.push(
      RouteName.paymentResultPage,
      extra: {
        'paymentResult': failedData,
        'onPrimaryAction': () => context.pop(),
        'onSecondaryAction': () => context.pop(),
      },
    );
  }

  void _dismissPaymentProcessing() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  void _startTimeout() {
    _timer = Timer(
      Duration(seconds: 30),
      () {
        _resetOfferStatusListener();
        _dismissPaymentProcessing();
        _handlePaymentTimeout();
      },
    );
  }

  void _handlePaymentTimeout() {
    setState(() {
      _isPaymentInProgress = false;
    });
    final offer = widget.bloc?.state.offer ?? widget.offer;
    final timeoutData = PaymentResultData.failed(
      amount: total,
      itemName: offer?.title ?? '',
      quantity: 1,
      itemPrice: offer?.price ?? 0,
      imageUrl: offer?.files!
          .where((file) => file.type.contains('image'))
          .map((file) => file.url)
          .toList(),
    ).copyWith(
      paymentMethod: _selectedMethod,
      transactionDate: DateTime.now(),
      orderCode: 'Processing...',
      orderTime: DateTime.now(),
      orderStatus: 'Pending',
      phone: offer?.customerPhone,
      recipientName: offer?.customerName,
      userId: offer?.customerID,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': timeoutData,
      'onPrimaryAction': () => context.pop(),
      'onSecondaryAction': () => context.pop(),
    });
  }

  Widget _buildOfferPreview(
    BuildContext context,
    Offers? offer,
  ) {
    final imageFiles = offer!.files?.firstWhere(
      (file) =>
          file.type.toLowerCase().contains('image') ||
          file.name.toLowerCase().contains('.png') ||
          file.name.toLowerCase().contains('.jpg') ||
          file.name.toLowerCase().contains('.jpeg'),
      orElse: () => OfferFile(name: '', size: '', type: '', url: ''),
    );
    final fileFiles = offer.files?.firstWhere(
      (file) =>
          file.type.toLowerCase().contains('document') ||
          file.name.toLowerCase().contains('.pdf') ||
          file.name.toLowerCase().contains('.doc') ||
          file.name.toLowerCase().contains('.docx'),
      orElse: () => OfferFile(name: '', size: '', type: '', url: ''),
    );

    if (imageFiles != null && imageFiles.url.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: imageFiles.url,
        width: 100.w,
        height: 60.h,
        fit: BoxFit.contain,
        placeholder: (context, url) => Container(
          width: 100.w,
          height: 60.h,
          color: Theme.of(context).lightGrey(context),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          width: 100.w,
          height: 60.h,
          color: Theme.of(context).lightGrey(context),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(Icons.image),
              Gap(2.h),
              Text(
                'N/A',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      );
    } else if (fileFiles != null && fileFiles.url.isNotEmpty) {
      return Container(
        width: 100.w,
        height: 60.h,
        color: Theme.of(context).greyScale50(context),
        child: Padding(
          padding: PaddingConstants.padAll10,
          child: SvgPicture.asset(
            AppAssets.pdf1Svg,
          ),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }
}
