import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/theme.dart';

class VoucherPicker extends StatefulWidget {
  const VoucherPicker({super.key});

  @override
  State<VoucherPicker> createState() => _VoucherPickerState();
}

class _VoucherPickerState extends State<VoucherPicker> {
  // 'ListView.separated' of vouchers
  String? _selectedVoucher;
  final List<Map<String, dynamic>> vouchers = [
    {
      'id': 'voucher_1',
      'discount': 'Discount 12% up to 500\$',
      'minimum': 'Minimum order 200\$',
    },
    {
      'id': 'voucher_2',
      'discount': 'Discount 10% up to 300\$',
      'minimum': 'Minimum order 150\$',
    },
    {
      'id': 'voucher_3',
      'discount': 'Discount 5% up to 100\$',
      'minimum': 'Minimum order 50\$',
    },
    {
      'id': 'voucher_4',
      'discount': 'Discount 15% up to 700\$',
      'minimum': 'Minimum order 300\$',
    },
    {
      'id': 'voucher_5',
      'discount': 'Discount 20% up to 1000\$',
      'minimum': 'Minimum order 500\$',
    },
    {
      'id': 'voucher_6',
      'discount': 'Discount 25% up to 1500\$',
      'minimum': 'Minimum order 700\$',
    },
  ];

  @override
  void initState() {
    super.initState();

    _selectedVoucher = vouchers.first['id'];
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: PaddingConstants.padAll20,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select a voucher',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          Gap(16.h),
          Row(
            children: [
              Expanded(
                flex: 5,
                child: SizedBox(
                  height: 44.h,
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Enter a voucher...',
                      hintStyle: Theme.of(context)
                          .textTheme
                          .bodyLarge
                          ?.copyWith(
                            color: Theme.of(context).textSecondary100(context),
                          ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                        borderSide: BorderSide(
                          color: Theme.of(context).textSecondary100(context),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                        borderSide: BorderSide(
                          color: Theme.of(context).textSecondary100(context),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                        borderSide: BorderSide(
                          color: Theme.of(context).textSecondary100(context),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Gap(8.w),
              Expanded(
                flex: 3,
                child: SizedBox(
                  height: 44.h,
                  child: ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).transparent(context),
                      foregroundColor: Theme.of(context).textPrimary(context),
                      overlayColor: Theme.of(context)
                          .blackPrimary(context)
                          .withOpacity(0.005),
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          color: Theme.of(context).textPrimary(context),
                        ),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ).copyWith(
                      elevation: WidgetStateProperty.all(0),
                    ),
                    child: Text('Apply'),
                  ),
                ),
              ),
            ],
          ),
          Gap(16.h),
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: 400.h,
            ),
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: vouchers.length,
              separatorBuilder: (context, index) => Gap(12.h),
              itemBuilder: (context, index) {
                final voucher = vouchers[index];
                final isSelected = _selectedVoucher == voucher['id'];

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedVoucher = voucher['id'];
                    });
                  },
                  child: Container(
                    height: 100.h,
                    padding: PaddingConstants.padAll16,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primary(context)
                            : Theme.of(context).textSecondary100(context),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(10.r),
                      color: isSelected
                          ? Theme.of(context).primary(context).withOpacity(0.01)
                          : Theme.of(context).transparent(context),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 48.w,
                          height: 48.h,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primary(context),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        Gap(16.w),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                voucher['discount'],
                                style: Theme.of(context).textTheme.bodyLarge,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Gap(2.h),
                              Text(
                                voucher['minimum'],
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .textSecondary(context),
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        Gap(8.w),
                        Container(
                          width: 24.w,
                          height: 24.h,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected
                                ? Theme.of(context).primary(context)
                                : Theme.of(context).transparent(context),
                          ),
                          child: isSelected
                              ? Icon(
                                  Icons.check,
                                  size: 16.w,
                                  color:
                                      Theme.of(context).whitePrimary(context),
                                )
                              : null,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          Gap(20.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton(
                onPressed: () => context.pop(),
                style: ElevatedButton.styleFrom(
                  padding: PaddingConstants.padSymV12.add(
                    PaddingConstants.padSymH20,
                  ),
                  backgroundColor: Theme.of(context).transparent(context),
                  foregroundColor: Theme.of(context).textPrimary(context),
                  overlayColor: Theme.of(context)
                      .blackPrimary(context)
                      .withOpacity(0.005),
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      color: Theme.of(context).textPrimary(context),
                    ),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text('Cancel'),
              ),
              Gap(8.w),
              ElevatedButton(
                onPressed: () => context.pop({
                  'id': _selectedVoucher,
                }),
                style: ElevatedButton.styleFrom(
                  padding: PaddingConstants.padSymV12.add(
                    PaddingConstants.padSymH20,
                  ),
                  backgroundColor: Theme.of(context).primary(context),
                  foregroundColor: Theme.of(context).textSecondary200(context),
                  overlayColor: Theme.of(context)
                      .blackPrimary(context)
                      .withOpacity(0.005),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text('Continue'),
              ),
            ],
          )
        ],
      ),
    );
  }
}
