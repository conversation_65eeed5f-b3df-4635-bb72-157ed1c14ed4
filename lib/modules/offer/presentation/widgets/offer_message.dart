import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/offer/create_offer_response.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/offer/presentation/bloc/offer_bloc.dart';

class OfferMessage extends StatefulWidget {
  final int offerID;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String orderNo;
  final String title;
  final String description;
  final double price;
  final String status;
  final String type;
  final int sellerID;
  final int? customerID;
  final int? requestID;
  final int? serviceID;
  final List<OfferFile>? files;
  final String? paymentStatus;
  final String? paymentMethod;
  final String? transactionID;
  final String sellerName;
  final String sellerAvatar;
  final OfferBloc? offerBloc;
  final ChatBloc? chatBloc;
  final String? channelName;

  const OfferMessage({
    super.key,
    required this.offerID,
    required this.createdAt,
    required this.updatedAt,
    required this.orderNo,
    required this.title,
    required this.description,
    required this.price,
    required this.status,
    required this.type,
    required this.sellerID,
    this.customerID,
    this.requestID,
    this.serviceID,
    this.files,
    this.paymentStatus,
    this.paymentMethod,
    this.transactionID,
    required this.sellerName,
    required this.sellerAvatar,
    this.offerBloc,
    this.chatBloc,
    this.channelName,
  });

  @override
  State<OfferMessage> createState() => _OfferMessageState();
}

class _OfferMessageState extends State<OfferMessage> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: PaddingConstants.padSymV6,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).greyScale300(context),
          ),
          borderRadius: BorderRadius.circular(10.r),
        ),
        padding: PaddingConstants.padAll16,
        child: Column(
          children: [
            InkWell(
              onTap: () {
                if (gs.uid == widget.sellerID) {
                  context.push(
                    RouteName.sellerOfferDetails,
                    extra: {
                      'offer_bloc': widget.offerBloc,
                      'offer_id': widget.offerID,
                      'chat_bloc': widget.chatBloc,
                      'partner_id': widget.customerID,
                      'channel_name': widget.channelName,
                    },
                  );
                  debugPrint('Seller ${widget.sellerID} offer message');
                } else {
                  context.push(
                    RouteName.buyerOfferDetails,
                    extra: {
                      'offer_bloc': widget.offerBloc,
                      'offer_id': widget.offerID,
                      'chat_bloc': widget.chatBloc,
                      'partner_id': widget.sellerID,
                      'channel_name': widget.channelName,
                    },
                  );
                  debugPrint('Buyer offer message from ${widget.sellerID}');
                }
              },
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Offer #${widget.orderNo}',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style:
                            Theme.of(context).textTheme.displayLarge?.copyWith(
                                  fontFamily: AppFontFamily.bold,
                                ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 24,
                      ),
                    ],
                  ),
                  Gap(16.h),
                  Row(
                    children: [
                      _buildOfferPreview(),
                      Gap(16.w),
                      Expanded(
                        child: Text(
                          widget.title,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  Gap(16.h),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Theme.of(context).greyScale100(context),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    padding: PaddingConstants.padSymH16.add(
                      PaddingConstants.padSymV8,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Price',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                color: Theme.of(context).textSecondary(context),
                                fontFamily: AppFontFamily.regular,
                              ),
                        ),
                        Text(
                          'USD \$${TFormatter.formatPrice(widget.price)}',
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge
                              ?.copyWith(
                                fontFamily: AppFontFamily.bold,
                                color:
                                    Theme.of(context).informationBase(context),
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfferPreview() {
    final imageFiles = widget.files?.firstWhere(
      (file) =>
          file.type.toLowerCase().contains('image') ||
          file.name.toLowerCase().contains('.png') ||
          file.name.toLowerCase().contains('.jpg') ||
          file.name.toLowerCase().contains('.jpeg'),
      orElse: () => OfferFile(name: '', size: '', type: '', url: ''),
    );
    final fileFiles = widget.files?.firstWhere(
      (file) =>
          file.type.toLowerCase().contains('document') ||
          file.name.toLowerCase().contains('.pdf') ||
          file.name.toLowerCase().contains('.doc') ||
          file.name.toLowerCase().contains('.docx'),
      orElse: () => OfferFile(name: '', size: '', type: '', url: ''),
    );

    if (imageFiles != null && imageFiles.url.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: imageFiles.url,
        width: 100.w,
        height: 60.h,
        fit: BoxFit.contain,
        placeholder: (context, url) => Container(
          width: 100.w,
          height: 60.h,
          color: Theme.of(context).lightGrey(context),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          width: 100.w,
          height: 60.h,
          color: Theme.of(context).lightGrey(context),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(Icons.image),
              Gap(2.h),
              Text(
                'N/A',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      );
    } else if (fileFiles != null && fileFiles.url.isNotEmpty) {
      return Container(
        width: 100.w,
        height: 60.h,
        color: Theme.of(context).greyScale50(context),
        child: Padding(
          padding: PaddingConstants.padAll10,
          child: SvgPicture.asset(
            AppAssets.pdf1Svg,
          ),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }
}
