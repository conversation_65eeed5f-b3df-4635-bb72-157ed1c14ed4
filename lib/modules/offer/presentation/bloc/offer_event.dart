part of 'offer_bloc.dart';

class OfferEvent extends Equatable {
  const OfferEvent();

  @override
  List<Object?> get props => [];
}

class CreateOffer extends OfferEvent {
  final String title;
  final String description;
  final double price;
  final int customerID;
  final List<PlatformFile>? files;

  const CreateOffer({
    required this.title,
    required this.description,
    required this.price,
    required this.customerID,
    this.files,
  });

  @override
  List<Object?> get props => [
        title,
        description,
        price,
        customerID,
        files,
      ];
}

class GetOffer extends OfferEvent {
  final int offerID;

  const GetOffer({required this.offerID});

  @override
  List<Object?> get props => [offerID];
}

class PayOfferWithStripe extends OfferEvent {
  final int offerID;

  const PayOfferWithStripe({required this.offerID});

  @override
  List<Object?> get props => [offerID];
}
