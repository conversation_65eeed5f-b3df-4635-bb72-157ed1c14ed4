import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';
import 'package:multime_app/core/model/offer/create_offer_response.dart';
import 'package:multime_app/core/model/offer/offers.dart';
import 'package:multime_app/core/model/offer/stripe_payment_response.dart';
import 'package:multime_app/modules/offer/data/repository/offer_repository.dart';

part 'offer_event.dart';
part 'offer_state.dart';

class OfferBloc extends Bloc<OfferEvent, OfferState> {
  final OfferRepository _repository;

  OfferBloc(this._repository) : super(OfferState()) {
    on<CreateOffer>(_onCreateOffer);
    on<GetOffer>(_onGetOffer);
    on<PayOfferWithStripe>(_onPayOfferWithStripe);
  }

  Future<void> _onCreateOffer(
    CreateOffer event,
    Emitter<OfferState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: true,
        error: null,
      ),
    );

    try {
      final response = await _repository.createOfferFromChat(
        event.title,
        event.description,
        event.price,
        event.customerID,
        event.files,
      );

      emit(
        state.copyWith(
          isLoading: false,
          createOfferResponse: response,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          isLoading: false,
          error: error.toString(),
        ),
      );
    }
  }

  Future<void> _onGetOffer(
    GetOffer event,
    Emitter<OfferState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: true,
        error: null,
      ),
    );

    try {
      final response = await _repository.getOfferByID(
        event.offerID,
      );

      emit(
        state.copyWith(
          isLoading: false,
          offer: response,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          isLoading: false,
          error: error.toString(),
        ),
      );
    }
  }

  Future<void> _onPayOfferWithStripe(
    PayOfferWithStripe event,
    Emitter<OfferState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: true,
        error: null,
      ),
    );

    try {
      final response = await _repository.payOfferWithStripe(
        event.offerID,
      );

      emit(
        state.copyWith(
          isLoading: false,
          stripePaymentResponse: response,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          isLoading: false,
          error: error.toString(),
        ),
      );
    }
  }
}
