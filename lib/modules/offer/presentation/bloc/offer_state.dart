part of 'offer_bloc.dart';

class OfferState extends Equatable {
  final bool isLoading;
  final String? error;
  final CreateOfferResponse? createOfferResponse;
  final StripePaymentResponse? stripePaymentResponse;
  final Offers? offer;

  OfferState({
    this.isLoading = false,
    this.error,
    this.createOfferResponse,
    this.stripePaymentResponse,
    this.offer,
  });

  OfferState copyWith({
    bool? isLoading,
    String? error,
    CreateOfferResponse? createOfferResponse,
    StripePaymentResponse? stripePaymentResponse,
    Offers? offer,
  }) {
    return OfferState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      createOfferResponse: createOfferResponse ?? this.createOfferResponse,
      stripePaymentResponse:
          stripePaymentResponse ?? this.stripePaymentResponse,
      offer: offer ?? this.offer,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        error,
        createOfferResponse,
        stripePaymentResponse,
        offer,
      ];
}
