import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/model/offer/create_offer_response.dart';
import 'package:multime_app/core/model/offer/offers.dart';
import 'package:multime_app/core/model/offer/stripe_payment_response.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';

abstract class OfferRepository {
  Future<CreateOfferResponse> createOfferFromChat(
    String title,
    String description,
    double price,
    int customerID,
    List<PlatformFile>? files, {
    String status = 'pending',
    String type = 'service',
    CancelToken? cancelToken,
  });

  Future<Offers> getOfferByID(
    int offerID, {
    CancelToken? cancelToken,
  });

  Future<StripePaymentResponse> payOfferWithStripe(
    int offerID,
  );
}

class OfferRepositoryImpl implements OfferRepository {
  final ApiClient _apiClient = getIt<ApiClient>();

  @override
  Future<CreateOfferResponse> createOfferFromChat(
    String title,
    String description,
    double price,
    int customerID,
    List<PlatformFile>? files, {
    String status = 'pending',
    String type = 'service',
    CancelToken? cancelToken,
  }) async {
    try {
      // Transform PlatformFile list to MultipartFile list
      List<MultipartFile> uploadFiles = [];
      if (files != null && files.isNotEmpty) {
        for (PlatformFile file in files) {
          if (file.path != null) {
            uploadFiles.add(
              await MultipartFile.fromFile(
                file.path!,
                filename: file.name,
              ),
            );
          } else if (file.bytes != null) {
            uploadFiles.add(
              await MultipartFile.fromBytes(
                file.bytes!,
                filename: file.name,
              ),
            );
          } else {
            throw Exception('File path is null for ${file.name}');
          }
        }
      }

      final response = await _apiClient.request(
        path: ApiConst.sellerOffer,
        method: ApiType.post,
        headers: {'Scope': 'multi-me'},
        data: FormData.fromMap({
          'title': title,
          'description': description,
          'price': price,
          'status': status,
          'type': type,
          'customer_id': customerID,
          'upload_files': uploadFiles,
        }),
        cancelToken: cancelToken,
      );
      return CreateOfferResponse.fromJson(response);
    } catch (error) {
      print('Error creating offer from chat: $error');
      rethrow;
    }
  }

  @override
  Future<Offers> getOfferByID(
    int offerID, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: '${ApiConst.offer}/$offerID',
        method: ApiType.get,
        headers: {'Scope': 'multi-me'},
      );
      return Offers.fromJson(response);
    } catch (error) {
      print('Error fetching offer by ID: $error');
      rethrow;
    }
  }

  @override
  Future<StripePaymentResponse> payOfferWithStripe(
    int offerID,
  ) async {
    try {
      final response = await _apiClient.request(
        path: '${ApiConst.userOffer}/$offerID/payment-intent',
        method: ApiType.post,
        headers: {'Scope': 'multi-me'},
      );
      return StripePaymentResponse.fromJson(response);
    } catch (error) {
      print('Error accepting offer: $error');
      rethrow;
    }
  }
}
