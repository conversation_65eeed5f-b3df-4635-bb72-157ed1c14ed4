import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/date_time_utils.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/chat/presentation/widgets/custom_message_dialog.dart';
import 'package:multime_app/modules/offer/presentation/bloc/offer_bloc.dart';

import '../../core/model/offer/offers.dart';

class SellerOfferDetailsPage extends StatefulWidget {
  final int offerID;
  final ChatBloc? chatBloc;
  final int? partnerID;
  final String? channelName;

  const SellerOfferDetailsPage({
    super.key,
    required this.offerID,
    this.chatBloc,
    this.partnerID,
    this.channelName,
  });

  @override
  State<SellerOfferDetailsPage> createState() => _SellerOfferDetailsPageState();
}

class _SellerOfferDetailsPageState extends State<SellerOfferDetailsPage> {
  bool _isVisible = false;
  bool _showMore = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        context.read<OfferBloc>().add(
              GetOffer(
                offerID: widget.offerID,
              ),
            );
      },
    );
  }

  void _handleShowMore(String description) {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontFamily: AppFontFamily.semiBold,
                ),
          ),
          maxLines: 5,
          textDirection: TextDirection.ltr,
        );

        final availableWidth =
            1.sw - 32.w; // (Left + Right)*2 = (16.w + 16.w)*2
        textPainter.layout(maxWidth: availableWidth);

        setState(() {
          _showMore = textPainter.didExceedMaxLines;
        });
      },
    );
  }

  Color _getStatusContainerColor(BuildContext context, String status) {
    switch (status) {
      case 'pending':
        return Theme.of(context).attentionLight(context);
      case 'accepted':
        return Theme.of(context).successLight(context);
      case 'cancelled':
        return Theme.of(context).errorLight(context);
      case 'completed':
        return Theme.of(context).informationLight(context);
      default:
        return Theme.of(context).lightGrey(context);
    }
  }

  Color _getStatusTextColor(BuildContext context, String status) {
    switch (status) {
      case 'pending':
        return Theme.of(context).attentionBase(context);
      case 'accepted':
        return Theme.of(context).successBase(context);
      case 'cancelled':
        return Theme.of(context).errorBase(context);
      case 'completed':
        return Theme.of(context).informationBase(context);
      default:
        return Theme.of(context).textSecondary(context);
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'Accepted';
      case 'cancelled':
        return 'Cancelled';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Offer details',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            width: 24.w,
            height: 24.h,
          ),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
      ),
      body: BlocConsumer<OfferBloc, OfferState>(
        listener: (context, state) {
          if (state.offer != null && state.offer!.description.isNotEmpty) {
            _handleShowMore(state.offer!.description);
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return Center(
              child: CircularProgressIndicator(),
            );
          }

          return SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: PaddingConstants.padAll16,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'My offer:',
                            style: Theme.of(context)
                                .textTheme
                                .bodyLarge
                                ?.copyWith(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  fontFamily: AppFontFamily.semiBold,
                                ),
                          ),
                          Gap(8.h),
                          const Divider(),
                          Gap(8.h),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              _buildOfferPreview(context),
                              Gap(20.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: PaddingConstants.padSymH14.add(
                                        PaddingConstants.padSymV4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: _getStatusContainerColor(
                                          context,
                                          state.offer?.status ?? '',
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(6.r),
                                      ),
                                      child: Text(
                                        _getStatusText(
                                            state.offer?.status ?? ''),
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall
                                            ?.copyWith(
                                              color: _getStatusTextColor(
                                                context,
                                                state.offer?.status ?? '',
                                              ),
                                            ),
                                      ),
                                    ),
                                    Gap(8.h),
                                    Text(
                                      'Price',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            color: Theme.of(context)
                                                .textSecondary(context),
                                          ),
                                    ),
                                    Text(
                                      'USD \$${TFormatter.formatPrice(state.offer?.price ?? 0)}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.copyWith(
                                            fontFamily: AppFontFamily.bold,
                                            color: Theme.of(context)
                                                .informationBase(context),
                                          ),
                                    ),
                                    Gap(4.h),
                                    Text(
                                      state.offer?.title ?? '',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          Gap(16.h),
                          Text(
                            'ID',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                ),
                          ),
                          Text(
                            '#${state.offer?.orderNo}',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontFamily: AppFontFamily.semiBold,
                                ),
                          ),
                          Gap(16.h),
                          Text(
                            'Date',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                ),
                          ),
                          Text(
                            formatOfferDateTime(
                                state.offer?.createdAt ?? DateTime.now()),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontFamily: AppFontFamily.semiBold,
                                ),
                          ),
                          Gap(16.h),
                          if (state.offer?.files != null &&
                              state.offer!.files!.isNotEmpty) ...[
                            Text(
                              'Attachments (${state.offer!.files!.length})',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                            ),
                            Gap(4.h),
                            Column(
                              children: state.offer!.files!
                                  .map(
                                    (file) => InkWell(
                                      onTap: () {
                                        TFormatter.downloadOfferAttachment(
                                          context,
                                          file,
                                        );
                                      },
                                      child: Container(
                                        margin: EdgeInsets.only(bottom: 8.h),
                                        padding: PaddingConstants.padAll12,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12.r),
                                          border: Border.all(
                                            color: Theme.of(context)
                                                .greyScale300(context),
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                              AppAssets.pdf1Svg,
                                              width: 32.w,
                                              height: 32.h,
                                            ),
                                            Gap(12.w),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    file.name,
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyMedium
                                                        ?.copyWith(
                                                          fontFamily:
                                                              AppFontFamily
                                                                  .bold,
                                                        ),
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                  Gap(2.h),
                                                  Text(
                                                    '${(double.parse(file.size) / (1024 * 1024)).toStringAsFixed(2)} MB',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                          fontFamily:
                                                              AppFontFamily
                                                                  .regular,
                                                          color: Theme.of(
                                                                  context)
                                                              .textSecondary(
                                                                  context),
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                            Gap(8.h),
                          ],
                          Text(
                            'Description',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Visibility(
                                visible: !_isVisible,
                                child: Text(
                                  state.offer?.description ?? '',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        fontFamily: AppFontFamily.semiBold,
                                      ),
                                  maxLines: 5,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Visibility(
                                visible: _isVisible,
                                child: Text(
                                  state.offer?.description ?? '',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        fontFamily: AppFontFamily.semiBold,
                                      ),
                                ),
                              ),
                              Gap(4.h),
                              if (_showMore && !_isVisible)
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _isVisible = !_isVisible;
                                    });
                                  },
                                  child: Text(
                                    'Show more',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: Theme.of(context)
                                              .textSecondary(context),
                                          fontFamily: AppFontFamily.italic,
                                        ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: PaddingConstants.padAll16,
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    border: Border(
                      top: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: SizedBox(
                    width: double.infinity,
                    height: 48.h,
                    child: _buildButtons(
                      context,
                      state.offer?.status ?? '',
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildButtons(
    BuildContext context,
    String status,
  ) {
    switch (status) {
      case 'pending':
        return Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  context.push(
                    RouteName.editOfferPage,
                    extra: {
                      'partner_id': widget.partnerID,
                      'channel_name': widget.channelName,
                      'chat_bloc': widget.chatBloc,
                      'offer': context.read<OfferBloc>().state.offer,
                    },
                  );
                },
                style: ElevatedButton.styleFrom(
                  overlayColor: Theme.of(context)
                      .blackPrimary(context)
                      .withOpacity(0.005),
                  backgroundColor: Theme.of(context).transparent(context),
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.r),
                    side: BorderSide(
                      color: Theme.of(context).blackPrimary(context),
                    ),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text(
                  'Edit',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).textPrimary(context),
                      ),
                ),
              ),
            ),
            Gap(16.w),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (_) {
                      return CustomMessageDialog(
                        title: 'Do you want to cancel this offer?',
                        subtitle:
                            'This offer will be canceled and can\'t be undone. If there\'s a demand, you have to create a new one.',
                        cancelText: 'Cancel',
                        onCancel: () => context.pop(),
                        confirmText: 'OK',
                        onConfirm: () => context.pop(),
                      );
                    },
                  );
                },
                style: ElevatedButton.styleFrom(
                  overlayColor: Theme.of(context)
                      .blackPrimary(context)
                      .withOpacity(0.005),
                  backgroundColor: Theme.of(context).transparent(context),
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.r),
                    side: BorderSide(
                      color: Theme.of(context).blackPrimary(context),
                    ),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text(
                  'Cancel',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).textPrimary(context),
                      ),
                ),
              ),
            ),
          ],
        );
      /*
      case 'cancelled':
        return ElevatedButton(
          onPressed: () {},
          style: ElevatedButton.styleFrom(
            overlayColor:
                Theme.of(context).blackPrimary(context).withOpacity(0.005),
            backgroundColor: Theme.of(context).transparent(context),
            padding: EdgeInsets.symmetric(
              horizontal: 24.w,
              vertical: 12.h,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
              side: BorderSide(
                color: Theme.of(context).blackPrimary(context),
              ),
            ),
          ).copyWith(
            elevation: WidgetStateProperty.all(0),
          ),
          child: Text(
            'Resend offer',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).textPrimary(context),
                ),
          ),
        );
      */
      case 'completed':
      /*
        return ElevatedButton(
          onPressed: () {},
          style: ElevatedButton.styleFrom(
            overlayColor:
                Theme.of(context).blackPrimary(context).withOpacity(0.005),
            backgroundColor: Theme.of(context).transparent(context),
            padding: EdgeInsets.symmetric(
              horizontal: 24.w,
              vertical: 12.h,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
              side: BorderSide(
                color: Theme.of(context).blackPrimary(context),
              ),
            ),
          ).copyWith(
            elevation: WidgetStateProperty.all(0),
          ),
          child: Text(
            'Review',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).textPrimary(context),
                ),
          ),
        );
      */
      case 'accepted':
        return ElevatedButton(
          onPressed: () {},
          style: ElevatedButton.styleFrom(
            overlayColor:
                Theme.of(context).blackPrimary(context).withOpacity(0.005),
            backgroundColor: Theme.of(context).transparent(context),
            padding: EdgeInsets.symmetric(
              horizontal: 24.w,
              vertical: 12.h,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
              side: BorderSide(
                color: Theme.of(context).blackPrimary(context),
              ),
            ),
          ).copyWith(
            elevation: WidgetStateProperty.all(0),
          ),
          child: Text(
            'View order',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).textPrimary(context),
                ),
          ),
        );
      default:
        return SizedBox.shrink();
    }
  }

  Widget _buildOfferPreview(BuildContext context) {
    final imageFiles = context.read<OfferBloc>().state.offer?.files?.firstWhere(
          (file) =>
              file.type.toLowerCase().contains('image') ||
              file.name.toLowerCase().contains('.png') ||
              file.name.toLowerCase().contains('.jpg') ||
              file.name.toLowerCase().contains('.jpeg'),
          orElse: () => OfferFile(name: '', size: '', type: '', url: ''),
        );
    final fileFiles = context.read<OfferBloc>().state.offer?.files?.firstWhere(
          (file) =>
              file.type.toLowerCase().contains('document') ||
              file.name.toLowerCase().contains('.pdf') ||
              file.name.toLowerCase().contains('.doc') ||
              file.name.toLowerCase().contains('.docx'),
          orElse: () => OfferFile(name: '', size: '', type: '', url: ''),
        );

    if (imageFiles != null && imageFiles.url.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: imageFiles.url,
        width: 180.w,
        height: 120.h,
        fit: BoxFit.contain,
        placeholder: (context, url) => Container(
          width: 180.w,
          height: 120.h,
          color: Theme.of(context).lightGrey(context),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          width: 180.w,
          height: 120.h,
          color: Theme.of(context).lightGrey(context),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(Icons.image),
              Gap(2.h),
              Text(
                'N/A',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      );
    } else if (fileFiles != null && fileFiles.url.isNotEmpty) {
      return Container(
        width: 180.w,
        height: 120.h,
        color: Theme.of(context).greyScale50(context),
        child: Padding(
          padding: PaddingConstants.padAll10,
          child: SvgPicture.asset(
            AppAssets.pdf1Svg,
          ),
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }
}
