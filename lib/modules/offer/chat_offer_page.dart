import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/model/offer/create_offer_response.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/chat/presentation/widgets/custom_message_dialog.dart';
import 'package:multime_app/modules/offer/data/repository/offer_repository.dart';
import 'package:multime_app/modules/offer/presentation/bloc/offer_bloc.dart';

class ChatOfferPage extends StatefulWidget {
  final int partnerID;
  final String channelName;
  final ChatBloc chatBloc;

  const ChatOfferPage({
    super.key,
    required this.partnerID,
    required this.channelName,
    required this.chatBloc,
  });

  @override
  State<ChatOfferPage> createState() => _ChatOfferPageState();
}

class _ChatOfferPageState extends State<ChatOfferPage> {
  List<PlatformFile> selectedFiles = [];
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  late BuildContext _pageContext;

  String? _titleError;
  String? _descriptionError;
  String? _priceError;

  @override
  void initState() {
    super.initState();

    _titleController.addListener(_validateTitle);
    _descriptionController.addListener(_validateDescription);
    _priceController.addListener(_validatePrice);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();

    super.dispose();
  }

  void _validateTitle() {
    setState(() {
      _titleError =
          _titleController.text.trim().isEmpty ? "Title can't be empty" : null;
    });
  }

  void _validateDescription() {
    setState(() {
      _descriptionError = _descriptionController.text.trim().isEmpty
          ? "Description can't be empty"
          : null;
    });
  }

  void _validatePrice() {
    setState(() {
      _priceError =
          _priceController.text.trim().isEmpty ? "Price can't be empty" : null;
    });
  }

  bool get _isFormValid {
    return _titleController.text.trim().isNotEmpty &&
        _descriptionController.text.trim().isNotEmpty &&
        _priceController.text.trim().isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => OfferBloc(
            getIt<OfferRepository>(),
          ),
        ),
        BlocProvider.value(
          value: widget.chatBloc,
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<OfferBloc, OfferState>(
            listener: (context, state) {
              // [PubNub] Send an offer after it is created
              if (!state.isLoading &&
                  state.error == null &&
                  state.createOfferResponse != CreateOfferResponse.empty()) {
                widget.chatBloc.add(
                  SendOffer(
                    channelName: widget.channelName,
                    offer: state.createOfferResponse ??
                        CreateOfferResponse.empty(),
                  ),
                );
              }

              // [Offer] Handle offer creation errors
              if (state.error != null) {
                debugPrint('Error creating offer: ${state.error}');
                showDialog(
                  context: context,
                  builder: (_) {
                    return CustomMessageDialog(
                      title: 'Create an Offer',
                      subtitle:
                          'There was an error creating the offer. Please try again.',
                      confirmText: 'OK',
                      onConfirm: () => context.pop(),
                    );
                  },
                );
              }
            },
          ),
          BlocListener<ChatBloc, ChatState>(
            listener: (context, state) {
              if (state.isOfferSent) {
                widget.chatBloc.add(
                  ResetOfferState(),
                );
                _pageContext.pop();
              }
            },
          ),
        ],
        child: BlocBuilder<OfferBloc, OfferState>(
          builder: (context, state) {
            _pageContext = context;

            return Stack(
              children: [
                Scaffold(
                  appBar: AppBar(
                    title: Text(
                      'Create an offer',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    leading: IconButton(
                      icon: SvgPicture.asset(
                        AppAssets.arrowLeftSvg,
                        width: 24.w,
                        height: 24.h,
                      ),
                      onPressed: () => context.pop(),
                    ),
                    centerTitle: true,
                  ),
                  body: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          padding: PaddingConstants.padAll16,
                          child: Column(
                            children: [
                              _buildTextField(
                                context,
                                'Offer title',
                                _titleController,
                                errorText: _titleError,
                              ),
                              Gap(20.h),
                              _buildFileUploader(context),
                              (selectedFiles.isNotEmpty) ? Gap(8.h) : Gap(24.h),
                              SizedBox(
                                height: 140.h,
                                child: _buildTextField(
                                  context,
                                  'Describe your offer...',
                                  _descriptionController,
                                  isMultiline: true,
                                  errorText: _descriptionError,
                                ),
                              ),
                              Gap(24.h),
                              _buildTextField(
                                context,
                                'Enter price',
                                _priceController,
                                prefix: Icons.attach_money_rounded,
                                isPrice: true,
                                errorText: _priceError,
                              ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        padding: PaddingConstants.padAll16,
                        decoration: BoxDecoration(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          border: Border(
                            top: BorderSide(
                              color: Theme.of(context).dividerColor,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: SizedBox(
                          width: double.infinity,
                          height: 48.h,
                          child: ElevatedButton(
                            onPressed: _isFormValid
                                ? () async {
                                    context.read<OfferBloc>().add(
                                          CreateOffer(
                                            title: _titleController.text.trim(),
                                            description: _descriptionController
                                                .text
                                                .trim(),
                                            price: double.tryParse(
                                              _priceController.text.trim(),
                                            )!,
                                            customerID: widget.partnerID,
                                            files: selectedFiles,
                                          ),
                                        );
                                  }
                                : null,
                            style: ElevatedButton.styleFrom(
                              overlayColor: Theme.of(context)
                                  .blackPrimary(context)
                                  .withOpacity(0.005),
                              backgroundColor:
                                  Theme.of(context).textPrimary(context),
                              disabledBackgroundColor: Theme.of(context)
                                  .textPrimary(context)
                                  .withOpacity(0.4),
                              padding: EdgeInsets.symmetric(
                                horizontal: 24.w,
                                vertical: 12.h,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.r),
                              ),
                            ).copyWith(
                              elevation: WidgetStateProperty.all(0),
                            ),
                            child: Text(
                              'Create',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color:
                                        Theme.of(context).whitePrimary(context),
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (state.isLoading)
                  Positioned.fill(
                    child: Container(
                      color: Theme.of(context)
                          .blackPrimary(context)
                          .withOpacity(0.5),
                      child: Center(
                        child: Container(
                          padding: PaddingConstants.padAll20,
                          decoration: BoxDecoration(
                            color: Theme.of(context).whitePrimary(context),
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 48.w,
                                height: 48.h,
                                child: CircularProgressIndicator(),
                              ),
                              Gap(16.h),
                              Text(
                                'Creating offer...',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontFamily: AppFontFamily.semiBold,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildFileUploader(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: _uploadFile,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Upload file',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontFamily: AppFontFamily.semiBold,
                    ),
              ),
              Gap(12.h),
              DottedBorder(
                borderType: BorderType.RRect,
                dashPattern: const [12, 12],
                strokeWidth: 2,
                radius: Radius.circular(8.r),
                color: Theme.of(context).greyScale400(context),
                child: Container(
                  width: double.infinity,
                  height: 180.h,
                  decoration: BoxDecoration(
                    color: Theme.of(context).greyScale100(context),
                    borderRadius: BorderRadius.circular(6.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppAssets.offerUploadFile,
                        width: 40.w,
                        height: 40.h,
                      ),
                      Gap(8.h),
                      Text(
                        'Tap here to upload file',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontFamily: AppFontFamily.semiBold,
                            ),
                      ),
                      Gap(4.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            style: Theme.of(context).textTheme.bodySmall,
                            children: [
                              TextSpan(
                                text:
                                    'Supports file formats png, jpg, jpeg, pdf, docx with a size under ',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .textSecondary(context),
                                    ),
                              ),
                              const TextSpan(
                                text: '10MB',
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // Render selected files
        if (selectedFiles.isNotEmpty) ...[
          Gap(20.h),
          ...selectedFiles.asMap().entries.map(
            (item) {
              int index = item.key;
              PlatformFile file = item.value;

              return Column(
                children: [
                  Container(
                    padding: PaddingConstants.padAll16,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: Theme.of(context).greyScale300(context),
                      ),
                    ),
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AppAssets.pdf1Svg,
                          width: 32.w,
                          height: 32.h,
                        ),
                        Gap(12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                file.name,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontFamily: AppFontFamily.bold,
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Gap(2.h),
                              Text(
                                '${(file.size / (1024 * 1024)).toStringAsFixed(2)} MB',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      fontFamily: AppFontFamily.regular,
                                      color: Theme.of(context)
                                          .textSecondary(context),
                                    ),
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => _removeFile(index),
                          child: Container(
                            width: 24.w,
                            height: 24.h,
                            child: Icon(
                              Icons.close,
                              size: 20.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(12.h),
                ],
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildTextField(
    BuildContext context,
    String hintText,
    TextEditingController controller, {
    IconData? prefix,
    bool isMultiline = false,
    bool isPrice = false,
    String? errorText,
  }) {
    return TextField(
      controller: controller,
      keyboardType: isMultiline
          ? TextInputType.multiline
          : (isPrice ? TextInputType.number : TextInputType.text),
      maxLines: isMultiline ? 100 : 1,
      inputFormatters: isPrice
          ? [
              FilteringTextInputFormatter.allow(
                RegExp(r'^\d*\.?\d{0,2}'),
              ),
              TextInputFormatter.withFunction(
                (oldValue, newValue) {
                  if (newValue.text.startsWith('0') &&
                      newValue.text.length > 1 &&
                      !newValue.text.startsWith('0.')) {
                    return oldValue;
                  }

                  if (newValue.text.isEmpty) {
                    return newValue;
                  }

                  final double? value = double.tryParse(newValue.text);
                  if (value != null) {
                    return newValue;
                  }

                  return oldValue;
                },
              ),
            ]
          : null,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: Theme.of(context).textTheme.bodyLarge!.copyWith(
              color: Theme.of(context).textSecondary100(context),
            ),
        errorText: errorText,
        errorStyle: TextStyle(
          color: Theme.of(context).primary(context),
        ),
        prefixIcon: (prefix != null)
            ? Icon(
                prefix,
                color: Theme.of(context).textSecondary100(context),
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.r),
          borderSide: BorderSide(
            color: Theme.of(context).disabledBase(context),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.r),
          borderSide: BorderSide(
            color: Theme.of(context).disabledBase(context),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.r),
          borderSide: BorderSide(
            color: Theme.of(context).disabledBase(context),
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 12.h,
        ),
      ),
    );
  }

  Future<void> _uploadFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx'],
        allowMultiple: true,
        withData: false,
      );

      if (result != null) {
        const int maxFileSize = 10 * 1024 * 1024;
        List<PlatformFile> validFiles = result.files.where(
          (file) {
            return file.size <= maxFileSize;
          },
        ).toList();

        if (validFiles.length != result.files.length) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Some files were skipped due to size limits.'),
            ),
          );
        }

        setState(() {
          selectedFiles.addAll(validFiles);
        });
      }
    } catch (error) {
      debugPrint('Error uploading files: $error');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('There was an error uploading files. Please try again.'),
        ),
      );
    }
  }

  void _removeFile(int index) {
    setState(() {
      selectedFiles.removeAt(index);
    });
  }
}
