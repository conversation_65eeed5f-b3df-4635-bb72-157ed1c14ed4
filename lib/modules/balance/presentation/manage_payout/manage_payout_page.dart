import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/payout_stack/payout_method_stack.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class ManagePayoutPage extends StatefulWidget {
  const ManagePayoutPage({super.key});

  @override
  State<ManagePayoutPage> createState() => _ManagePayoutPageState();
}

class _ManagePayoutPageState extends State<ManagePayoutPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          LocaleKeys.Paymentmethod.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            width: 24,
          ),
          onPressed: () => context.pop(),
        ),
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
      ),
      body: Padding(
        padding: PaddingConstants.padAll16,
        child: SingleChildScrollView(
          child: Column(
            children: [
              PayoutMethodStack(
                method: LocaleKeys.Creditcard.tr(),
                methodIcon: const Icon(
                  Icons.credit_card_rounded,
                  size: 24,
                ),
                methodList: [
                  {
                    'logo': AppAssets.visa,
                    'method': 'Visa Debit ****** 1234',
                    'expiryDate': '${LocaleKeys.Expirydate.tr()} 12/30',
                  },
                  {
                    'logo': AppAssets.mastercard,
                    'method': 'MasterCard ****** 1234',
                    'expiryDate': '${LocaleKeys.Expirydate.tr()} 12/30',
                  },
                ],
              ),
              Gap(20.h),
              PayoutMethodStack(
                method: 'PayPal',
                methodIcon: SvgPicture.asset(
                  AppAssets.paypal,
                  width: 24,
                ),
                methodList: [
                  {
                    'logo': AppAssets.paypal,
                    'method': 'PayPal - James Madison',
                    'expiryDate': '${LocaleKeys.Expirydate.tr()} 12/30',
                  },
                ],
              ),
              Gap(20.h),
              PayoutMethodStack(
                method: 'Payoneer',
                methodIcon: SvgPicture.asset(
                  AppAssets.payoneer,
                  width: 24,
                ),
                methodList: const [],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
