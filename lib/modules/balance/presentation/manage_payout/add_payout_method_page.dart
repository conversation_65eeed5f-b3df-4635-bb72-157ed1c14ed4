import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/bloc/card_input_bloc.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/credit_card_input/card_input_field.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/formatters/card_number_input_formatter.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/formatters/cardholders_name_input_formatter.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/formatters/expiry_date_input_formatter.dart';

class AddPayoutMethodPage extends StatelessWidget {
  const AddPayoutMethodPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CardInputBloc(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            LocaleKeys.Addacreditcard.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                  fontFamily: AppFontFamily.semiBold,
                ),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(
              AppAssets.arrowLeftSvg,
              width: 24,
            ),
            onPressed: () => context.pop(),
          ),
          backgroundColor: Colors.transparent,
          scrolledUnderElevation: 0,
        ),
        body: Padding(
          padding: PaddingConstants.padAll16,
          child: BlocBuilder<CardInputBloc, CardInputState>(
            builder: (context, state) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        LocaleKeys.Carddetails.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightHeadingSmall
                            .copyWith(
                              fontFamily: AppFontFamily.semiBold,
                            ),
                      ),
                      SvgPicture.asset(
                        AppAssets.supportedMethods,
                        width: 220,
                      ),
                    ],
                  ),
                  Gap(20.h),
                  // This will be disabled and auto-filled depends on card number
                  CardInputField(
                    label: LocaleKeys.Cardtype.tr(),
                    hintText: LocaleKeys.Cardtype.tr(),
                    keyboardType: TextInputType.text,
                  ),
                  Gap(20.h),
                  Row(
                    children: [
                      Expanded(
                        child: CardInputField(
                          label: LocaleKeys.Cardnumber.tr(),
                          hintText: '0000-0000-0000-0000',
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(16),
                            CardNumberInputFormatter(),
                          ],
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            context
                                .read<CardInputBloc>()
                                .add(UpdateCardNumber(value));
                          },
                        ),
                      ),
                      Gap(12.w),
                      GestureDetector(
                        onTap: () {},
                        child: SvgPicture.asset(
                          AppAssets.scanner,
                          width: 24.w,
                        ),
                      )
                    ],
                  ),
                  Gap(16.h),
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: CardInputField(
                          label: LocaleKeys.Expirydate.tr(),
                          hintText: 'MM/YY',
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(4),
                            ExpiryDateInputFormatter(allowLeadingZero: true),
                          ],
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            context
                                .read<CardInputBloc>()
                                .add(UpdateExpiryDate(value));
                          },
                        ),
                      ),
                      Gap(16.h),
                      Expanded(
                        flex: 1,
                        child: CardInputField(
                          label: 'CVV',
                          hintText: 'CVV',
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(3),
                          ],
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            context.read<CardInputBloc>().add(UpdateCVV(value));
                          },
                        ),
                      ),
                    ],
                  ),
                  Gap(16.h),
                  CardInputField(
                    label: LocaleKeys.Cardholdername.tr(),
                    hintText: 'Name on card',
                    inputFormatters: [
                      CardholdersNameInputFormatter(),
                    ],
                    keyboardType: TextInputType.text,
                    onChanged: (value) {
                      context
                          .read<CardInputBloc>()
                          .add(UpdateCardholdersName(value));
                    },
                  ),
                  Gap(16.h),
                  Text(
                    '${LocaleKeys.verificationTransaction.tr()} \$0.50 ${LocaleKeys.Verifyfeedialogue.tr()}',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumRegular
                        .copyWith(
                          color: Theme.of(context).greyScale500(context),
                        ),
                  ),
                  Gap(20.h),
                  ElevatedButton(
                    onPressed: state.isValid ? () {} : null,
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.resolveWith<Color>(
                        (states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Theme.of(context).greyScale100(context);
                          }
                          return Theme.of(context).primary(context);
                        },
                      ),
                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      elevation: WidgetStateProperty.all(0),
                      minimumSize: WidgetStateProperty.all(
                        const Size(double.infinity, 48),
                      ),
                    ),
                    child: Text(
                      LocaleKeys.confirmButtonText.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .darkBodyLargeMedium
                          .copyWith(
                            color: state.isValid
                                ? AppColors.whitePrimary
                                : Theme.of(context).greyScale600(context),
                          ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
