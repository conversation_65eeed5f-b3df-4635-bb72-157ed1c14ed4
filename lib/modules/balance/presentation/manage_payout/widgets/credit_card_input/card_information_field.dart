import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CardInformationField extends StatelessWidget {
  final String label;
  final String hintText;
  final TextEditingController? controller;
  final bool isEnabled;

  const CardInformationField({
    super.key,
    required this.label,
    required this.hintText,
    this.controller,
    required this.isEnabled,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      enabled: isEnabled,
      style: const TextStyle(
        color: AppColors.blackPrimary,
      ),
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.always,
        label: Text(label),
        labelStyle: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
              color: Theme.of(context).greyScale600(context),
            ),
        floatingLabelStyle:
            Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                  color:Theme.of(context).greyScale600(context),
                ),
        contentPadding: EdgeInsets.symmetric(
          vertical: 16.h,
          horizontal: 20.w,
        ),
        hintText: hintText,
        hintStyle: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
              color: Theme.of(context).greyScale500(context),
            ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide:  BorderSide(
            color: Theme.of(context).greyScale100(context),
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide:  BorderSide(
            color: Theme.of(context).greyScale100(context),
          ),
        ),
      ),
    );
  }
}
