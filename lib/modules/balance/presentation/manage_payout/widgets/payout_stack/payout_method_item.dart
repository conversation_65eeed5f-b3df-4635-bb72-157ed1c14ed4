import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class PayoutMethodItem extends StatelessWidget {
  final String logo;
  final String method;
  final String expiryDate;

  const PayoutMethodItem({
    super.key,
    required this.logo,
    required this.method,
    required this.expiryDate,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => context.push(RouteName.manageExistingPayout),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.whitePrimary,
        overlayColor: Colors.grey.withOpacity(0.1),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        minimumSize: Size(double.infinity, 96.h),
      ).copyWith(
        elevation: WidgetStateProperty.all(0),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        leading: Padding(
          padding: const EdgeInsets.only(
            right: 8,
          ),
          child: SvgPicture.asset(
            logo,
            width: 48,
          ),
        ),
        title: Text(
          method,
          style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
        ),
        subtitle: Text(
          expiryDate,
          style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                color:Theme.of(context).greyScale600(context),
              ),
        ),
      ),
    );
  }
}
