import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/payout_stack/add_payout_method_button.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/payout_stack/payout_method_item.dart';

class PayoutMethodStack extends StatelessWidget {
  final String method;
  final Widget methodIcon;
  final List<Map<String, String>> methodList;

  const PayoutMethodStack({
    super.key,
    required this.method,
    required this.methodIcon,
    required this.methodList,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).greyScale100(context),
        border: Border.all(
          color: Theme.of(context).greyScale100(context),
          width: 1.w,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ExpansionTile(
        clipBehavior: Clip.antiAlias,
        iconColor: AppColors.blackPrimary,
        collapsedIconColor: AppColors.blackPrimary,
        title: Row(
          children: [
            methodIcon,
            Gap(10.w),
            Text(
              method,
              style: Theme.of(context).textTheme.lightBodyXLargeSemiBold,
            ),
          ],
        ),
        children: [
          ...List.generate(
            methodList.length,
            (index) => Column(
              children: [
                 Divider(
                  color: Theme.of(context).greyScale100(context),
                  height: 0,
                  thickness: 1,
                ),
                PayoutMethodItem(
                  logo: methodList[index]['logo']!,
                  method: methodList[index]['method']!,
                  expiryDate: methodList[index]['expiryDate']!,
                ),
              ],
            ),
          ),
           Divider(
            color: Theme.of(context).greyScale100(context),
            height: 0,
            thickness: 1,
          ),
          AddPayoutMethodButton(
            method: method,
          ),
        ],
      ),
    );
  }
}
