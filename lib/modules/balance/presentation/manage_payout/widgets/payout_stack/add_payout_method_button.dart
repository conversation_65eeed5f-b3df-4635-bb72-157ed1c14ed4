import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class AddPayoutMethodButton extends StatelessWidget {
  final String method;

  const AddPayoutMethodButton({
    super.key,
    required this.method,
  });

  String _getAddMethodText() {
    switch (method.toLowerCase()) {
      case 'credit card':
        return LocaleKeys.Addanewcreditcard.tr();
      case 'paypal':
        return LocaleKeys.AddanewPayPalaccount.tr();
      case 'payoneer':
        return LocaleKeys.AddanewPayoneeraccount.tr();
      default:
        return LocaleKeys.Addanewpayoutmethod.tr();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => context.push(RouteName.addNewPayout),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.whitePrimary,
        overlayColor: Colors.grey.withOpacity(0.1),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(6),
            bottomRight: Radius.circular(6),
          ),
        ),
        minimumSize: Size(double.infinity, 72.h),
      ).copyWith(
        elevation: WidgetStateProperty.all(0),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        tileColor: AppColors.whitePrimary,
        leading: const Padding(
          padding: PaddingConstants.padSymH12,
          child: Icon(
            Icons.add_rounded,
            color: AppColors.blackPrimary,
            size: 24,
          ),
        ),
        title: Text(
          _getAddMethodText(),
          style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                color: Theme.of(context).greyScale600(context),
              ),
        ),
      ),
    );
  }
}
