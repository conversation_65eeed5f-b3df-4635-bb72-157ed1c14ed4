import 'package:flutter/services.dart';

class ExpiryDateInputFormatter extends TextInputFormatter {
  final bool allowLeadingZero;

  ExpiryDateInputFormatter({
    this.allowLeadingZero = false,
  });

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final text = newValue.text.replaceAll('/', '');
    if (text.isEmpty) return newValue;

    final month =
        int.tryParse(text.substring(0, text.length > 2 ? 2 : text.length)) ?? 0;
    if (month > 12 || (!allowLeadingZero && month == 0)) {
      return oldValue;
    }
    final buffer = StringBuffer();

    for (int i = 0; i < text.length; i++) {
      if (i == 2) buffer.write('/');
      buffer.write(text[i]);
    }

    final formattedText = buffer.toString();
    return newValue.copyWith(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}
