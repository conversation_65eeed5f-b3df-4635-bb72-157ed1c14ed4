import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/widgets/credit_card_input/card_information_field.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class ManagePayoutMethodPage extends StatelessWidget {
  const ManagePayoutMethodPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          LocaleKeys.Managepayoutmethod.tr(),
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                fontFamily: AppFontFamily.semiBold,
              ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            width: 24,
          ),
          onPressed: () => context.pop(),
        ),
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
      ),
      body: Padding(
        padding: PaddingConstants.padAll16,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocaleKeys.Carddetails.tr(),
              style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
                    fontFamily: AppFontFamily.semiBold,
                  ),
            ),
            Gap(20.h),
            CardInformationField(
              label: LocaleKeys.Cardtype.tr(),
              hintText: LocaleKeys.Cardtype.tr(),
              controller: TextEditingController(
                text: 'VISA',
              ),
              isEnabled: false,
            ),
            Gap(20.h),
            Row(
              children: [
                Expanded(
                  child: CardInformationField(
                    label: LocaleKeys.Cardnumber.tr(),
                    hintText: '0000-0000-0000-0000',
                    controller: TextEditingController(
                      text: '****-****-****-3456',
                    ),
                    isEnabled: false,
                  ),
                ),
                Gap(12.w),
                GestureDetector(
                  onTap: () {},
                  child: SvgPicture.asset(
                    AppAssets.scanner,
                    width: 24.w,
                  ),
                )
              ],
            ),
            Gap(20.h),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: CardInformationField(
                    label: LocaleKeys.Expirydate.tr(),
                    hintText: 'MM/YY',
                    controller: TextEditingController(
                      text: '**/**',
                    ),
                    isEnabled: false,
                  ),
                ),
                Gap(16.w),
                Expanded(
                  flex: 1,
                  child: CardInformationField(
                    label: 'CVV',
                    hintText: 'CVV',
                    controller: TextEditingController(
                      text: '***',
                    ),
                    isEnabled: false,
                  ),
                ),
              ],
            ),
            Gap(20.h),
            CardInformationField(
              label: LocaleKeys.Cardholdername.tr(),
              hintText: '',
              controller: TextEditingController(
                text: 'NGUYEN VAN A',
              ),
              isEnabled: false,
            ),
            Gap(20.h),
            Center(
              child: TextButton(
                onPressed: () {},
                style: TextButton.styleFrom(
                  overlayColor: Colors.grey.withOpacity(0.1),
                ),
                child: Text(
                  LocaleKeys.Deletepayoutmethod.tr(),
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeSemiBold
                      .copyWith(
                        color: Theme.of(context).greyScale600(context),
                        decoration: TextDecoration.underline,
                        decorationColor: Theme.of(context).greyScale600(context),
                      ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
