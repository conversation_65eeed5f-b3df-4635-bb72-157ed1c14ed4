part of 'card_input_bloc.dart';

abstract class CardInput<PERSON>vent extends Equatable {
  @override
  List<Object> get props => [];
}

class UpdateCardNumber extends CardInputEvent {
  final String cardNumber;

  UpdateCardNumber(this.cardNumber);

  @override
  List<Object> get props => [cardNumber];
}

class UpdateExpiryDate extends CardInputEvent {
  final String expiryDate;

  UpdateExpiryDate(this.expiryDate);

  @override
  List<Object> get props => [expiryDate];
}

class UpdateCVV extends CardInputEvent {
  final String cvv;

  UpdateCVV(this.cvv);

  @override
  List<Object> get props => [cvv];
}

class UpdateCardholdersName extends CardInputEvent {
  final String cardholdersName;

  UpdateCardholdersName(this.cardholdersName);

  @override
  List<Object> get props => [cardholdersName];
}
