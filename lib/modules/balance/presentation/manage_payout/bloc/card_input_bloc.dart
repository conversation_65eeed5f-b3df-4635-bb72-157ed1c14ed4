import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'card_input_event.dart';
part 'card_input_state.dart';

class CardInputBloc extends Bloc<CardInputEvent, CardInputState> {
  CardInputBloc() : super(const CardInputState()) {
    on<UpdateCardNumber>((event, emit) {
      final isValid = _validateInput(
          event.cardNumber, state.expiryDate, state.cvv, state.cardholdersName);
      emit(state.update(cardNumber: event.cardNumber, isValid: isValid));
    });
    on<UpdateExpiryDate>((event, emit) {
      final isValid = _validateInput(
          state.cardNumber, event.expiryDate, state.cvv, state.cardholdersName);
      emit(state.update(expiryDate: event.expiryDate, isValid: isValid));
    });
    on<UpdateCVV>((event, emit) {
      final isValid = _validateInput(
          state.cardNumber, state.expiryDate, event.cvv, state.cardholdersName);
      emit(state.update(cvv: event.cvv, isValid: isValid));
    });
    on<UpdateCardholdersName>((event, emit) {
      final isValid = _validateInput(
          state.cardNumber, state.expiryDate, state.cvv, event.cardholdersName);
      emit(state.update(
          cardholdersName: event.cardholdersName, isValid: isValid));
    });
  }

  bool _validateInput(
      String cardNumber, String expiryDate, String cvv, String cardholderName) {
    return cardNumber.length == 19 &&
        expiryDate.length == 5 &&
        cvv.length == 3 &&
        cardholderName.isNotEmpty;
  }
}
