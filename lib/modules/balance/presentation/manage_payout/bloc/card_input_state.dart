part of 'card_input_bloc.dart';

class CardInputState extends Equatable {
  final String cardNumber;
  final String expiryDate;
  final String cvv;
  final String cardholdersName;
  final bool isValid;

  const CardInputState({
    this.cardNumber = '',
    this.expiryDate = '',
    this.cvv = '',
    this.cardholdersName = '',
    this.isValid = false,
  });

  CardInputState update({
    String? cardNumber,
    String? expiryDate,
    String? cvv,
    String? cardholdersName,
    bool? isValid,
  }) {
    return CardInputState(
      cardNumber: cardNumber ?? this.cardNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      cvv: cvv ?? this.cvv,
      cardholdersName: cardholdersName ?? this.cardholdersName,
      isValid: isValid ?? this.isValid,
    );
  }

  @override
  List<Object> get props =>
      [cardNumber, expiryDate, cvv, cardholdersName, isValid];
}
