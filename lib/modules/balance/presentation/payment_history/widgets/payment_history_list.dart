import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history_tab_bloc.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history/history_card.dart';

class PaymentHistoryList extends StatelessWidget {
  final ScrollController scrollController;
  final List<Map<String, dynamic>> data;

  const PaymentHistoryList({
    super.key,
    required this.scrollController,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HistoryTabBloc, HistoryTabState>(
      builder: (context, state) {
        return SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: PaddingConstants.padSymH16.add(
              EdgeInsets.only(top: 16.h),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${LocaleKeys.Showing.tr()} ${data.length} ${LocaleKeys.Results.tr()}',
                  style:
                      Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                            color: Theme.of(context).greyScale500(context)
                          ),
                ),
                Gap(16.h),
                ...data.map((data) {
                  return Column(
                    children: [
                      HistoryCard(
                        status: data['status'],
                        date: data['date'],
                        amount: data['amount'],
                        paymentID: data['paymentID'],
                        onTap: () {},
                      ),
                      Gap(14.h),
                    ],
                  );
                }),
              ],
            ),
          ),
        );
      },
    );
  }
}
