import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history_tab_bloc.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history/history_tab_bar.dart';
import 'package:multime_app/modules/balance/presentation/payment_history/widgets/payment_history_list.dart';
import 'package:multime_app/shared/models/history/history_mock_data.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class PaymentHistoryPage extends StatefulWidget {
  const PaymentHistoryPage({super.key});

  @override
  State<PaymentHistoryPage> createState() => _PaymentHistoryPageState();
}

class _PaymentHistoryPageState extends State<PaymentHistoryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();
    _tabController = TabController(
      length: 2,
      vsync: this,
    );

    _tabController.addListener(_handleTabSelection);

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();

    super.dispose();
  }

  void _handleTabSelection() {
    if (!_tabController.indexIsChanging) {
      _resetScroll();
    }
  }

  void _resetScroll() {
    if (_scrollController.hasClients) {
      _scrollController
          .animateTo(
        0,
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOut,
      )
          .then((_) {
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HistoryTabBloc()..add(const HistoryTabChanged(0)),
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              LocaleKeys.history.tr(),
              style: Theme.of(context).textTheme.lightHeadingMedium,
            ),
            leading: IconButton(
              icon: SvgPicture.asset(
                AppAssets.arrowLeftSvg,
                width: 24.w,
              ),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.transparent,
            scrolledUnderElevation: 0,
          ),
          body: Column(
            children: [
              Padding(
                padding: PaddingConstants.padSymH16,
                child: HistoryTabBar(
                  tabController: _tabController,
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    PaymentHistoryList(
                      scrollController: _scrollController,
                      data: mockSellerHistoryData,
                    ),
                    PaymentHistoryList(
                      scrollController: _scrollController,
                      data: mockBuyerHistoryData,
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
