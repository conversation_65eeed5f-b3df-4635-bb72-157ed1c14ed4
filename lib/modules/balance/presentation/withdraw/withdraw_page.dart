import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/bloc/withdraw_amount/withdraw_amount_bloc.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw/withdraw_amount_input.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw/withdraw_warning_dialogue.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/bloc/withdraw_method/withdraw_method_bloc.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw/withdraw_method_card.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class WithdrawPage extends StatelessWidget {
  const WithdrawPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => WithdrawMethodBloc(),
        ),
        BlocProvider(
          create: (context) => WithdrawAmountBloc(),
        ),
      ],
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          title: Text(
            LocaleKeys.Withdraw.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppAssets.arrowLeftSvg,
              width: 24.w,
            ),
            onPressed: () => context.pop(),
          ),
          centerTitle: true,
          automaticallyImplyLeading: false,
          backgroundColor: Colors.transparent,
          scrolledUnderElevation: 0,
        ),
        body: Padding(
          padding: PaddingConstants.padAll16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.Chooseawithdrawmethod.tr(),
                style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
                      fontFamily: AppFontFamily.medium,
                    ),
              ),
              const WithdrawMethodCard(
                selectedOptionValue: 'visa',
                withdrawMethodLogo: AppAssets.visa,
                withdrawMethod: 'Visa Debit ****** 1234',
                withdrawMethodExpiryDate: '12/30',
              ),
              const WithdrawMethodCard(
                selectedOptionValue: 'paypal',
                withdrawMethodLogo: AppAssets.paypal,
                withdrawMethod: 'PayPal - James Madison',
                withdrawMethodExpiryDate: '12/30',
              ),
              Gap(40.h),
              BlocBuilder<WithdrawAmountBloc, WithdrawAmountState>(
                builder: (context, state) {
                  return WithdrawAmountInput(
                    onChanged: (amount) =>
                        context.read<WithdrawAmountBloc>().add(
                              UpdateWithdrawAmountEvent(
                                amount: amount,
                              ),
                            ),
                  );
                },
              ),
              Gap(30.h),
              const WithdrawWarningDialogue(),
              const Spacer(),
              BlocBuilder<WithdrawAmountBloc, WithdrawAmountState>(
                builder: (context, state) {
                  return ElevatedButton(
                    onPressed: state.amount > 0
                        ? () {
                            context.read<WithdrawAmountBloc>().add(
                                  SubmitWithdrawEvent(
                                    amount: state.amount,
                                  ),
                                );
                            context.push(
                              RouteName.withdrawResult,
                              extra: {
                                'amount': state.amount,
                              },
                            );
                          }
                        : null,
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.resolveWith<Color>(
                        (states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Theme.of(context).greyScale600(context);
                          }
                          return Theme.of(context).secondaryBase(context);
                        },
                      ),
                      overlayColor:
                          WidgetStateProperty.all(Colors.transparent),
                      splashFactory: NoSplash.splashFactory,
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      minimumSize: WidgetStateProperty.all(
                        Size(double.infinity, 48.h),
                      ),
                      elevation: WidgetStateProperty.all(0),
                    ),
                    child: Text(
                      LocaleKeys.Withdraw.tr(),
                      style: Theme.of(context).textTheme.darkBodyLargeSemiBold,
                    ),
                  );
                },
              ),
              Gap(14.h),
              ElevatedButton(
                onPressed: () => context.pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.whitePrimary,
                  overlayColor:Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  side:  BorderSide(
                    color: Theme.of(context).greyScale600(context),
                  ),
                  minimumSize: Size(double.infinity, 48.h),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text(
                  LocaleKeys.cancel.tr(),
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeSemiBold
                      .copyWith(
                        color: Theme.of(context).greyScale600(context),
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
