part of 'withdraw_amount_bloc.dart';

abstract class WithdrawAmountEvent extends Equatable {
  const WithdrawAmountEvent();

  @override
  List<Object> get props => [];
}

class UpdateWithdrawAmountEvent extends WithdrawAmountEvent {
  final num amount;

  const UpdateWithdrawAmountEvent({required this.amount});

  @override
  List<Object> get props => [amount];
}

class SubmitWithdrawEvent extends WithdrawAmountEvent {
  final num amount;

  const SubmitWithdrawEvent({required this.amount});

  @override
  List<Object> get props => [amount];
}
