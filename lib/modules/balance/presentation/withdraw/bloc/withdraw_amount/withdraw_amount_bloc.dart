import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'withdraw_amount_event.dart';
part 'withdraw_amount_state.dart';

class WithdrawAmountBloc
    extends Bloc<WithdrawAmountEvent, WithdrawAmountState> {
  WithdrawAmountBloc() : super(const WithdrawAmountState()) {
    on<UpdateWithdrawAmountEvent>((event, emit) {
      emit(state.update(
        amount: event.amount,
      ));
    });

    on<SubmitWithdrawEvent>((event, emit) {
      emit(state.update(
        amount: event.amount,
      ));
    });
  }
}
