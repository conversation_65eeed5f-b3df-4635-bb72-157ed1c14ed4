import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class WithdrawStatusCard extends StatelessWidget {
  final String status;
  final num amount;

  String _getWithdrawStatus() {
    switch (status) {
      case 'waiting':
        return LocaleKeys.Waitingtransaction.tr();
      case 'success':
        return LocaleKeys.Withdrawalsuccessful.tr();
      case 'failed':
        return LocaleKeys.Withdrawalfailed.tr();
      default:
        return '';
    }
  }

  Color _getWithdrawStatusColor(BuildContext context) {
    switch (status) {
      case 'waiting':
        return AppColors.warningBase;
      case 'success':
        return AppColors.successBase;
      case 'failed':
        return Theme.of(context).primary(context);
      default:
        return Colors.black;
    }
  }

  String _getWithdrawStatusIcon() {
    switch (status) {
      case 'waiting':
        return AppAssets.waitingTransaction;
      case 'success':
        return AppAssets.withdrawalSuccessful;
      case 'failed':
        return AppAssets.withdrawalFailed;
      default:
        return '';
    }
  }

  const WithdrawStatusCard({
    super.key,
    required this.status,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: PaddingConstants.padAll18,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).greyScale100(context),
          width: 1.w,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          SvgPicture.asset(
            _getWithdrawStatusIcon(),
            width: 80.w,
          ),
          Gap(6.h),
          Text(
            _getWithdrawStatus(),
            style: Theme.of(context)
                .textTheme
                .lightHeadingSmall
                .copyWith(
                  fontFamily: AppFontFamily.semiBold,
                )
                .copyWith(
                  color: _getWithdrawStatusColor(context),
                ),
          ),
          Gap(12.h),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.lightHeadingMediumLarge,
          )
        ],
      ),
    );
  }
}
