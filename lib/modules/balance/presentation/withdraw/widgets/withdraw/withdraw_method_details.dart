import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class WithdrawMethodDetails extends StatelessWidget {
  final String logo;
  final String method;
  final String expiryDate;

  const WithdrawMethodDetails({
    super.key,
    required this.logo,
    required this.method,
    required this.expiryDate,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          logo,
          width: 48.w,
        ),
        Gap(16.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              method,
              style: Theme.of(context).textTheme.lightBodyLargeMedium,
            ),
            Gap(2.h),
            Text(
              '${LocaleKeys.Expirydate.tr()} $expiryDate',
              style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color:Theme.of(context).greyScale600(context),
                  ),
            ),
          ],
        ),
      ],
    );
  }
}
