import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class WithdrawAmountInput extends StatelessWidget {
  final Function(num) onChanged;

  const WithdrawAmountInput({
    super.key,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(
          RegExp(r'^\d*\.?\d{0,2}'),
        ),
        TextInputFormatter.withFunction((oldValue, newValue) {
          if (newValue.text.startsWith('0') &&
              newValue.text.length > 1 &&
              !newValue.text.startsWith('0.')) {
            return oldValue;
          }

          if (newValue.text.isEmpty) {
            onChanged(0);
            return newValue;
          }

          final double? value = double.tryParse(newValue.text);
          if (value != null) {
            onChanged(value);
            return newValue;
          }

          return oldValue;
        }),
      ],
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.always,
        label: Text(
          LocaleKeys.Withdrawamount.tr(),
        ),
        labelStyle: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
              color: Theme.of(context).greyScale600(context),
            ),
        floatingLabelStyle:
            Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                  color: Theme.of(context).greyScale600(context),
                ),
        hintText: '0.00',
        hintStyle: Theme.of(context).textTheme.lightBodyLargeRegular,
        suffixIcon: Padding(
          padding: PaddingConstants.padSymH20,
          child: SvgPicture.asset(
            AppAssets.dollarSign,
            width: 20.w,
          ),
        ),
        contentPadding: PaddingConstants.padAll20,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14),
          borderSide:  BorderSide(
            color: Theme.of(context).greyScale100(context),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14),
          borderSide:  BorderSide(
            color: Theme.of(context).greyScale100(context),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(14),
          borderSide:  BorderSide(
            color:Theme.of(context).greyScale100(context),
          ),
        ),
      ),
      style: Theme.of(context).textTheme.lightBodyLargeRegular,
    );
  }
}
