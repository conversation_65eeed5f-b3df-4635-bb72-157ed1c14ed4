import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class WithdrawWarningDialogue extends StatelessWidget {
  const WithdrawWarningDialogue({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          AppAssets.warningIcon,
          width: 24.w,
        ),
        Gap(12.w),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(right: 6.w),
            child: Text(
              LocaleKeys.Withdrawwarningdialogue.tr(),
              style:
                  Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                        height: 1.3,
                      ),
              textAlign: TextAlign.justify,
            ),
          ),
        )
      ],
    );
  }
}
