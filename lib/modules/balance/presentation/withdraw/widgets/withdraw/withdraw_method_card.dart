import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw/withdraw_method_details.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/bloc/withdraw_method/withdraw_method_bloc.dart';

class WithdrawMethodCard extends StatelessWidget {
  final String selectedOptionValue;
  final String withdrawMethodLogo;
  final String withdrawMethod;
  final String withdrawMethodExpiryDate;

  const WithdrawMethodCard({
    super.key,
    required this.selectedOptionValue,
    required this.withdrawMethodLogo,
    required this.withdrawMethod,
    required this.withdrawMethodExpiryDate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).greyScale100(context),
            width: 1.w,
          ),
        ),
      ),
      child: BlocBuilder<WithdrawMethodBloc, String>(
        builder: (context, selectedOption) {
          return Column(
            children: [
              ListTile(
                contentPadding: PaddingConstants.padSymV10,
                onTap: () {
                  context.read<WithdrawMethodBloc>().add(
                        SelectWithdrawMethod(selectedOptionValue),
                      );
                },
                leading: Transform.scale(
                  scale: 1.4,
                  child: Radio<String>(
                    value: selectedOptionValue,
                    groupValue: selectedOption,
                    onChanged: (String? value) {
                      if (value != null) {
                        context.read<WithdrawMethodBloc>().add(
                              SelectWithdrawMethod(value),
                            );
                      }
                    },
                    activeColor: Theme.of(context).primary(context),
                  ),
                ),
                title: WithdrawMethodDetails(
                  logo: withdrawMethodLogo,
                  method: withdrawMethod,
                  expiryDate: withdrawMethodExpiryDate,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
