import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class TransactionMethod extends StatelessWidget {
  final String transactionMethod;

  String _getMethodIcon() {
    switch (transactionMethod.toLowerCase()) {
      case var method when method.contains('visa'):
        return AppAssets.visa;
      case var method when method.contains('mastercard'):
        return AppAssets.mastercard;
      case var method when method.contains('paypal'):
        return AppAssets.paypal;
      case var method when method.contains('payoneer'):
        return AppAssets.payoneer;
      default:
        return '';
    }
  }

  const TransactionMethod({
    super.key,
    required this.transactionMethod,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: PaddingConstants.padSymH10,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            LocaleKeys.Method.tr(),
            style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                  color: Theme.of(context).greyScale600(context),
                ),
          ),
          Row(
            children: [
              SvgPicture.asset(
                _getMethodIcon(),
                height: 24,
              ),
              Gap(6.w),
              Text(
                transactionMethod,
                style: Theme.of(context).textTheme.lightBodyMediumBold.copyWith(
                      color:Theme.of(context).secondaryBase(context)
                    ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
