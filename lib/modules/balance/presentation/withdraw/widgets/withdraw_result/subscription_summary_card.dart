import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/extension.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw_result/subscription_summary_dialogue.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw_result/transaction_date.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw_result/transaction_id.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw_result/transaction_method.dart';

class SubscriptionSummaryCard extends StatelessWidget {
  final String status;
  final num amount;
  final VoidCallback? onTap;

  const SubscriptionSummaryCard({
    super.key,
    required this.status,
    required this.amount,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          margin: EdgeInsets.only(top: 26.h),
          padding: PaddingConstants.padAll20,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).greyScale100(context),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Gap(10.h),
              Text(
                LocaleKeys.Subscriptionsummary.tr(),
                style: Theme.of(context).textTheme.lightHeadingSmall,
              ),
              Gap(24.h),
              SubscriptionSummaryDialogue(
                status: status,
                amount: amount,
              ),
              Gap(24.h),
              const TransactionMethod(
                transactionMethod: 'Visa Debit',
              ),
              Gap(12.h),
              const TransactionDate(
                transactionDate: '26/12/2024',
              ),
              Gap(12.h),
              const TransactionID(
                transactionID: '*************',
              ),
              _getSubscriptionDirectLink(context),
              _getChoiceButton(context),
            ],
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ClipOval(
            child: SvgPicture.asset(
              AppAssets.sellerAvatarSvg,
              height: 52.h,
              width: 52.w,
              fit: BoxFit.cover,
            ),
          ),
        ),
      ],
    );
  }

  Widget _getSubscriptionDirectLink(BuildContext context) {
    switch (status) {
      // SUCCESS CASE
      case 'success':
        return Column(
          children: [
            Gap(20.h),
            TextField(
              readOnly: true,
              controller: TextEditingController(
                text: 'https://multime.ai/s/GVAQGq',
              ),
              decoration: InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.symmetric(
                  vertical: 10.h,
                ),
                suffixIcon: IconButton(
                  icon: SvgPicture.asset(
                    AppAssets.clipboard,
                  ),
                  onPressed: () {
                    // This clipboard is only a placeholder
                    Clipboard.setData(
                      const ClipboardData(text: 'https://multime.ai/s/GVAQGq'),
                    );
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          LocaleKeys.Linkcopiedtoclipboard.tr(),
                        ),
                      ),
                    );
                  },
                ),
                border:  UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: Theme.of(context).greyScale500(context),
                  ),
                ),
                enabledBorder:  UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: Theme.of(context).greyScale500(context),
                  ),
                ),
                focusedBorder:  UnderlineInputBorder(
                  borderSide: BorderSide(
                    color:Theme.of(context).greyScale500(context)
                  ),
                ),
              ),
              textAlignVertical: TextAlignVertical.bottom,
              style:
                  Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                        color:Theme.of(context).greyScale600(context),
                      ),
            ),
            Gap(30.h),
          ],
        );

      // FAILED CASE
      case 'failed':
        return Gap(24.h);

      // WAITING CASE
      case 'waiting':
        return Gap(24.h);

      // DEFAULT
      default:
        return Container();
    }
  }

  Widget _getChoiceButton(BuildContext context) {
    switch (status) {
      // SUCCESS CASE
      case 'success':
        return Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () =>
                    GoRouter.of(context).popUntil(RouteName.balancePage),
                style: ElevatedButton.styleFrom(
                  padding: PaddingConstants.padAll12,
                  backgroundColor: AppColors.whitePrimary,
                  overlayColor: Colors.grey.withOpacity(0.1),
                  shape: RoundedRectangleBorder(
                    side:  BorderSide(
                      color: Theme.of(context).secondaryBase(context)
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text(
                  LocaleKeys.Returntobalance.tr(),
                  style:
                      Theme.of(context).textTheme.darkBodyMediumMedium.copyWith(
                            color: Theme.of(context).secondaryBase(context)
                          ),
                ),
              ),
            ),
            Gap(12.w),
            Expanded(
              child: ElevatedButton(
                onPressed: onTap,
                style: ElevatedButton.styleFrom(
                  padding: PaddingConstants.padAll12,
                  backgroundColor:Theme.of(context).secondaryBase(context),
                  overlayColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text(
                  LocaleKeys.Saveimage.tr(),
                  style: Theme.of(context).textTheme.darkBodyMediumMedium,
                ),
              ),
            ),
          ],
        );

      // FAILED CASE
      case 'failed':
        return Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () =>
                    GoRouter.of(context).popUntil(RouteName.balancePage),
                style: ElevatedButton.styleFrom(
                  padding: PaddingConstants.padAll12,
                  backgroundColor: AppColors.whitePrimary,
                  overlayColor: Colors.grey.withOpacity(0.1),
                  shape: RoundedRectangleBorder(
                    side:  BorderSide(
                      color:Theme.of(context).secondaryBase(context)
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text(
                  LocaleKeys.Returntobalance.tr(),
                  style:
                      Theme.of(context).textTheme.darkBodyMediumMedium.copyWith(
                            color: Theme.of(context).secondaryBase(context)
                          ),
                ),
              ),
            ),
            Gap(12.w),
            Expanded(
              child: ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  padding: PaddingConstants.padAll12,
                  backgroundColor: Theme.of(context).secondaryBase(context),
                  overlayColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                ),
                child: Text(
                  LocaleKeys.Tryagain.tr(),
                  style: Theme.of(context).textTheme.darkBodyMediumMedium,
                ),
              ),
            ),
          ],
        );

      // WAITING CASE
      case 'waiting':
        return ElevatedButton(
          onPressed: () => GoRouter.of(context).popUntil(RouteName.balancePage),
          style: ElevatedButton.styleFrom(
            padding: PaddingConstants.padAll12,
            backgroundColor: AppColors.whitePrimary,
            overlayColor: Colors.grey.withOpacity(0.1),
            shape: RoundedRectangleBorder(
              side:  BorderSide(
                color: Theme.of(context).secondaryBase(context)
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            minimumSize: const Size(double.infinity, 44),
          ).copyWith(
            elevation: WidgetStateProperty.all(0),
          ),
          child: Text(
            LocaleKeys.Returntobalance.tr(),
            style: Theme.of(context).textTheme.darkBodyMediumMedium.copyWith(
                  color:Theme.of(context).secondaryBase(context)
                ),
          ),
        );

      // DEFAULT
      default:
        return Container();
    }
  }
}
