import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class SubscriptionSummaryDialogue extends StatelessWidget {
  final String status;
  final num amount;

  const SubscriptionSummaryDialogue({
    super.key,
    required this.status,
    required this.amount,
  });

  String _getWithdrawDialogue() {
    switch (status) {
      case 'waiting':
        return LocaleKeys.Waitingtransactiondialogue.tr();
      case 'success':
        return '${LocaleKeys.Youhavesuccessfullywithdrawn.tr()} ${amount.toStringAsFixed(amount.truncateToDouble() == amount ? 0 : 2)}\$ ${LocaleKeys.Toyourvisadebitaccount.tr()}';
      case 'failed':
        return LocaleKeys.Somethingwentwrong.tr();
      default:
        return '';
    }
  }

  Color _getWithdrawDialogueColor(BuildContext context) {
    switch (status) {
      case 'waiting':
        return AppColors.warningBase;
      case 'success':
        return AppColors.informationBase;
      case 'failed':
        return Theme.of(context).primary(context);
      default:
        return Colors.black;
    }
  }

  Color _getWithdrawDialogueBackgroundColor() {
    switch (status) {
      case 'waiting':
        return AppColors.warningLight;
      case 'success':
        return AppColors.informationLight;
      case 'failed':
        return AppColors.errorLight;
      default:
        return Colors.black;
    }
  }

  String _getWithdrawDialogueIcon() {
    switch (status) {
      case 'waiting':
        return AppAssets.yellowWarningIcon;
      case 'success':
        return AppAssets.informationIcon;
      case 'failed':
        return AppAssets.warningNoteSvg;
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: PaddingConstants.padAll12,
      width: double.infinity,
      decoration: BoxDecoration(
        color: _getWithdrawDialogueBackgroundColor(),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            _getWithdrawDialogueIcon(),
            height: 24,
            width: 24,
          ),
          Gap(10.w),
          Expanded(
            child: Text(
              _getWithdrawDialogue(),
              style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                    color: _getWithdrawDialogueColor(context),
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
