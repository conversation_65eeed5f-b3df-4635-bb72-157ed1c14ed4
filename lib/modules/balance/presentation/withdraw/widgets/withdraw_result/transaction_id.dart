import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class TransactionID extends StatelessWidget {
  final String transactionID;

  const TransactionID({
    super.key,
    required this.transactionID,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: PaddingConstants.padSymH10,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            LocaleKeys.TransactionID.tr(),
            style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                  color: Theme.of(context).greyScale600(context),
                ),
          ),
          Text(
            transactionID,
            style: Theme.of(context).textTheme.lightBodyMediumBold.copyWith(
                  color: AppColors.informationBase,
                ),
          )
        ],
      ),
    );
  }
}
