import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class TransactionDate extends StatelessWidget {
  final String transactionDate;

  const TransactionDate({
    super.key,
    required this.transactionDate,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: PaddingConstants.padSymH10,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            LocaleKeys.Transactiondate.tr(),
            style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                  color: Theme.of(context).greyScale600(context),
                ),
          ),
          Text(
            transactionDate,
            style: Theme.of(context).textTheme.lightBodyMediumBold.copyWith(
                  color:Theme.of(context).secondaryBase(context)
                ),
          )
        ],
      ),
    );
  }
}
