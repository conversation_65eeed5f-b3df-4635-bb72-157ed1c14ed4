import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/utils/extension.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw_result/subscription_summary_card.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/widgets/withdraw/withdraw_status_card.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:screenshot/screenshot.dart';

class WithdrawResultPage extends StatelessWidget {
  final String status;
  final num amount;

  WithdrawResultPage({
    super.key,
    required this.status,
    required this.amount,
  });

  final ScreenshotController _screenshotController = ScreenshotController();

  Future<void> _saveImageAsScreenshot(BuildContext context) async {
    final permission = await Permission.manageExternalStorage.request();
    if (!permission.isGranted) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocaleKeys.Permissiondenied.tr()),
        ),
      );
      return;
    }

    try {
      final image = await _screenshotController.capture();
      if (image == null) {
        if (!context.mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocaleKeys.Unabletosaveimage.tr()),
          ),
        );
        return;
      }

      final fileName =
          'multime_withdraw_${DateFormat('ddMMyyyy_HHmm').format(DateTime.now())}.png';

      if (Platform.isAndroid) {
        final directory = Directory('/storage/emulated/0/Download');
        final filePath = '${directory.path}/$fileName';

        final file = File(filePath);
        await file.writeAsBytes(image);

        if (!context.mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('${LocaleKeys.ImagesavedtoDownloads.tr()} $filePath')),
        );
      } else {
        final directory = await getExternalStorageDirectory();
        final filePath = '${directory!.path}/$fileName';

        final file = File(filePath);
        await file.writeAsBytes(image);

        if (!context.mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${LocaleKeys.Imagesavedat.tr()} $filePath')),
        );
      }
    } catch (err) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocaleKeys.Therewasanerror.tr()),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Screenshot(
      controller: _screenshotController,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            LocaleKeys.Withdraw.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppAssets.arrowLeftSvg,
              width: 24.w,
            ),
            onPressed: () =>
                GoRouter.of(context).popUntil(RouteName.balancePage),
          ),
          centerTitle: true,
          automaticallyImplyLeading: false,
          backgroundColor: Colors.transparent,
          scrolledUnderElevation: 0,
        ),
        body: Padding(
          padding: PaddingConstants.padAll16,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              WithdrawStatusCard(
                status: status,
                amount: amount,
              ),
              Gap(18.h),
              SubscriptionSummaryCard(
                status: status,
                amount: amount,
                onTap: () => _saveImageAsScreenshot(context),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
