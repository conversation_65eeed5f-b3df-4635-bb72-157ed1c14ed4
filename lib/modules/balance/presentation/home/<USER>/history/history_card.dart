import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class HistoryCard extends StatelessWidget {
  final String status;
  final String date;
  final num amount;
  final num paymentID;
  final VoidCallback onTap;

  const HistoryCard({
    super.key,
    required this.status,
    required this.date,
    required this.amount,
    required this.paymentID,
    required this.onTap,
  });

  Color _getStatusColor(BuildContext context) {
    switch (status.toLowerCase()) {
      case 'withdrawal':
        return Theme.of(context).primary(context);
      case 'completed':
        return Theme.of(context).primary(context);
      case 'delivered':
        return Theme.of(context).primary(context);
      case 'earning':
        return AppColors.successBase;
      case 'waiting for rating':
        return AppColors.successBase;
      default:
        return AppColors.blackPrimary;
    }
  }

  String _getAmountPrefix() {
    switch (status.toLowerCase()) {
      case 'withdrawal':
        return '-';
      case 'completed':
        return '-';
      case 'delivered':
        return '-';
      case 'earning':
        return '+';
      case 'waiting for rating':
        return '+';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 14.h,
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).greyScale100(context),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status,
                  style: Theme.of(context).textTheme.lightHeadingSmall,
                ),
                Gap(20.h),
                Text(
                  date,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumMedium
                      .copyWith(
                        color: Theme.of(context).greyScale600(context),
                      ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${_getAmountPrefix()}\$${amount.toStringAsFixed(2)}',
                  style:
                      Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                            color: _getStatusColor(context),
                          ),
                ),
                Gap(20.h),
                Text(
                  '#$paymentID',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumMedium
                      .copyWith(
                        color: Theme.of(context).greyScale600(context),
                        decoration: TextDecoration.underline,
                        decorationColor: Theme.of(context).greyScale600(context),
                      ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
