import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history_tab_bloc.dart';

import '../../../../../../core/l10n/locale_keys.g.dart';

class HistoryTabBar extends StatelessWidget {
  final TabController? tabController;

  const HistoryTabBar({
    super.key,
    this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return TabBar(
      controller: tabController,
      labelColor: Theme.of(context).primary(context),
      unselectedLabelColor: Theme.of(context).greyScale600(context),
      labelStyle: Theme.of(context).textTheme.lightBodyXLargeBold,
      unselectedLabelStyle: Theme.of(context).textTheme.lightBodyXLargeRegular,
      indicator:  UnderlineTabIndicator(
        borderSide: BorderSide(
          color: Theme.of(context).primary(context),
          width: 2,
        ),
        insets: EdgeInsets.zero,
      ),
      indicatorSize: TabBarIndicatorSize.tab,
      tabs: [
        Tab(text: LocaleKeys.AsaSeller.tr()),
        Tab(text: LocaleKeys.AsaBuyer.tr()),
      ],
      onTap: (index) {
        context.read<HistoryTabBloc>().add(HistoryTabChanged(index));
      },
    );
  }
}
