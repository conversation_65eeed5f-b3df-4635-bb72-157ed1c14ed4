import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history_tab_bloc.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history/history_card.dart';

class HistoryList extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const HistoryList({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HistoryTabBloc, HistoryTabState>(
      builder: (context, state) {
        if (state is HistoryTabLoaded) {
          return Padding(
            padding: PaddingConstants.padSymH16.copyWith(
              top: 6.h,
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      data.length >= 5
                          ? '${LocaleKeys.Showingresults15of.tr()} ${data.length}'
                          : '${LocaleKeys.Showingresults1.tr()}–${data.length} of ${data.length}',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeMedium
                          .copyWith(
                            color:Theme.of(context).greyScale500(context),
                          ),
                    ),
                    TextButton(
                      onPressed: () => context.push(RouteName.paymentHistory),
                      style: TextButton.styleFrom(
                        overlayColor: Colors.grey.withOpacity(0.1),
                      ),
                      child: Text(
                        LocaleKeys.Viewall.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumMedium
                            .copyWith(
                              color: AppColors.informationBase,
                            ),
                      ),
                    ),
                  ],
                ),
                Gap(6.h),
                ...data.take(5).map((data) {
                  return Column(
                    children: [
                      HistoryCard(
                        status: data['status'],
                        date: data['date'],
                        amount: data['amount'],
                        paymentID: data['paymentID'],
                        onTap: () {},
                      ),
                      Gap(14.h),
                    ],
                  );
                }),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}
