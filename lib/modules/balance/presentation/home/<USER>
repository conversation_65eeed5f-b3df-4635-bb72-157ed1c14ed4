import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history_tab_bloc.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history/history_list.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/payment_active_status/cleared_payment_card.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/available_funds/available_funds_card.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/payment_active_status/active_status_card.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>/history/history_tab_bar.dart';
import 'package:multime_app/shared/models/history/history_mock_data.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class BalancePage extends StatelessWidget {
  const BalancePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HistoryTabBloc()..add(const HistoryTabChanged(0)),
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              LocaleKeys.Balance.tr(),
              style: Theme.of(context).textTheme.lightHeadingMedium,
            ),
            centerTitle: true,
            leading: IconButton(
              icon: SvgPicture.asset(
                AppAssets.arrowLeftSvg,
                width: 24,
              ),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.transparent,
            scrolledUnderElevation: 0,
          ),
          body: CustomScrollView(
            shrinkWrap: true,
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.only(
                    top: 16.h,
                    bottom: 12.h,
                    left: 16.w,
                    right: 16.w,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.Availablefunds.tr(),
                        style: Theme.of(context).textTheme.lightHeadingSmall,
                      ),
                      Gap(8.h),
                      const AvailableFundsCard(amount: 30.85),
                      Gap(30.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ActiveStatusCard(
                            title: LocaleKeys.ActiveService.tr(),
                            amount: 300.58,
                            activeItemCount: 6,
                            icon: AppAssets.activeService,
                          ),
                          Gap(24.w),
                          ActiveStatusCard(
                            title: LocaleKeys.ActiveProduct.tr(),
                            amount: 300.58,
                            activeItemCount: 6,
                            icon: AppAssets.activeProduct,
                          ),
                        ],
                      ),
                      Gap(10.h),
                      const ClearedPaymentCard(
                        amount: 300.58,
                        paymentCount: 2,
                      ),
                      Gap(22.h),
                      Text(
                        LocaleKeys.history.tr(),
                        style: Theme.of(context).textTheme.lightHeadingSmall,
                      ),
                    ],
                  ),
                ),
              ),
              const SliverToBoxAdapter(
                child: Padding(
                  padding: PaddingConstants.padSymH16,
                  child: HistoryTabBar(),
                ),
              ),
              SliverFillRemaining(
                child: TabBarView(
                  children: [
                    SingleChildScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      child: HistoryList(
                        data: mockSellerHistoryData,
                      ),
                    ),
                    SingleChildScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      child: HistoryList(
                        data: mockBuyerHistoryData,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
