import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../../core/l10n/locale_keys.g.dart';

class AvailableFundsCard extends StatelessWidget {
  final num amount;

  const AvailableFundsCard({
    super.key,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: 24.w,
        vertical: 20.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: Theme.of(context).greyScale100(context),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.lightHeadingLarge,
          ),
          Gap(6.h),
          Text(
            LocaleKeys.Withdrawntodate.tr(),
            style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                  color: AppColors.textSecondary100,
                ),
          ),
          Gap(6.h),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '\$800.00 ',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeSemiBold
                      .copyWith(
                        color: Theme.of(context).greyScale600(context),
                      ),
                ),
                TextSpan(
                  text: '/ 1000.00\$',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeSemiBold
                      .copyWith(
                        color: AppColors.textSecondary100,
                      ),
                ),
              ],
            ),
          ),
          Gap(36.h),
          ElevatedButton(
            onPressed: () => context.push(RouteName.withdrawPage),
            style: ElevatedButton.styleFrom(
              backgroundColor:Theme.of(context).primary(context),
              overlayColor: Colors.transparent,
              splashFactory: NoSplash.splashFactory,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              minimumSize: Size(double.infinity, 44.h),
            ).copyWith(
              elevation: WidgetStateProperty.all(0),
            ),
            child: Text(
              LocaleKeys.Withdraw.tr(),
              style: Theme.of(context).textTheme.darkBodyLargeSemiBold,
            ),
          ),
          Gap(6.h),
          TextButton(
            onPressed: () => context.push(RouteName.managePayout),
            style: TextButton.styleFrom(
              overlayColor: Colors.grey.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              minimumSize: Size(double.infinity, 44.h),
            ),
            child: Text(
              LocaleKeys.Managepayoutmethod.tr(),
              style:
                  Theme.of(context).textTheme.lightBodyLargeSemiBold.copyWith(
                        decoration: TextDecoration.underline,
                        decorationColor:Theme.of(context).secondaryBase(context)
                      ),
            ),
          )
        ],
      ),
    );
  }
}
