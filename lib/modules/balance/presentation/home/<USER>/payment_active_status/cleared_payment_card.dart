import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../../core/l10n/locale_keys.g.dart';

class ClearedPaymentCard extends StatelessWidget {
  final num amount;
  final num paymentCount;

  const ClearedPaymentCard({
    super.key,
    required this.amount,
    required this.paymentCount,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.PaymentBeingCleared.tr(),
          style: Theme.of(context).textTheme.lightBodyLargeSemiBold.copyWith(
                color: Theme.of(context).greyScale600(context),
              ),
        ),
        Gap(16.h),
        Container(
          width: double.infinity,
          padding: PaddingConstants.padAll20,
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).greyScale100(context),),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '\$${amount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.lightHeadingSmall,
              ),
              Gap(10.h),
              Text(
                '$paymentCount  ${LocaleKeys.OrdersisActiveNow.tr()}',
                style:
                    Theme.of(context).textTheme.lightBodySmallMedium.copyWith(
                          color:Theme.of(context).greyScale600(context),
                        ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
