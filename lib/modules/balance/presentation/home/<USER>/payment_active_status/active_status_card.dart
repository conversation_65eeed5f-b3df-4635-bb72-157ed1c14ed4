import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../../core/l10n/locale_keys.g.dart';

class ActiveStatusCard extends StatelessWidget {
  final String title;
  final num amount;
  final num activeItemCount;
  final String icon;

  const ActiveStatusCard({
    super.key,
    required this.title,
    required this.amount,
    required this.activeItemCount,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.lightBodyLargeSemiBold.copyWith(
                  color:Theme.of(context).greyScale600(context),
                ),
          ),
          Gap(16.h),
          Container(
            padding: PaddingConstants.padAll20,
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).greyScale100(context),),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  icon,
                  width: 40.w,
                ),
                Gap(8.h),
                Text(
                  '\$${amount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.lightHeadingSmall,
                ),
                Gap(10.h),
                Text(
                  title.toLowerCase() == 'active service'
                      ? '$activeItemCount  ${LocaleKeys.ServicesareActiveNow.tr()}'
                      : '$activeItemCount ${LocaleKeys.ProductsareActiveNow.tr()}',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallRegular
                      .copyWith(
                        color:Theme.of(context).greyScale600(context),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
