part of 'history_tab_bloc.dart';

abstract class HistoryTabState extends Equatable {
  const HistoryTabState();

  @override
  List<Object> get props => [];
}

class HistoryTabInitial extends HistoryTabState {}

class HistoryTabLoaded extends HistoryTabState {
  final int tabIndex;
  final List<Map<String, dynamic>> data;

  const HistoryTabLoaded({
    required this.tabIndex,
    required this.data,
  });

  @override
  List<Object> get props => [tabIndex, data];
}
