import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/shared/models/history/history_mock_data.dart';

part 'history_tab_event.dart';
part 'history_tab_state.dart';

class HistoryTabBloc extends Bloc<HistoryTabEvent, HistoryTabState> {
  HistoryTabBloc() : super(HistoryTabInitial()) {
    on<HistoryTabChanged>((event, emit) {
      final data =
          event.tabIndex == 0 ? mockSellerHistoryData : mockBuyerHistoryData;

      emit(HistoryTabLoaded(
        tabIndex: event.tabIndex,
        data: data,
      ));
    });
  }
}
