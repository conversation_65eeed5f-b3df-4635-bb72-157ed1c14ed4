import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

// ignore: must_be_immutable
class InformationSellerAndBuyer extends StatelessWidget {
  bool checkSeller;
  String name;
  String numberPhone;
  InformationSellerAndBuyer({
    super.key,
    this.checkSeller = true,
    required this.name,
    required this.numberPhone,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            checkSeller ? "Seller" : "Buyer",
            style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                  color: Theme.of(context).textSecondary(context),
                ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                name,
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
              ),
              Text(
                numberPhone,
                style:
                    Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
