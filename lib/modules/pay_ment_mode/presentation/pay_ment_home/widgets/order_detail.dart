import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class OrderDetail extends StatelessWidget {
  final String orderId;
  final String status;
  final String imageUrl;
  final String message;
  const OrderDetail({
    super.key,
    required this.orderId,
    required this.status,
    required this.imageUrl,
    required this.message,
  });
  Color getStatusColor() {
    switch (status) {
      case "Pending":
        return Colors.green;
      case "Failed":
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                "Order Details",
                style: Theme.of(context).textTheme.lightBodyXLargeSemiBold,
              ),
            ],
          ),
          Gap(10.h),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(
                width: 1,
                color: Theme.of(context).greyScale400(context),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(99),
                        color: getStatusColor(),
                      ),
                    ),
                    const Gap(10),
                    Text(
                      status,
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyMediumSemiBold
                          .copyWith(color: getStatusColor()),
                    )
                  ],
                ),
                const Gap(10),
                Text(
                  "Order ID:  $orderId",
                  style: Theme.of(context).textTheme.lightBodyMediumMedium,
                ),
                const Gap(10),
                Row(
                  children: [
                    Container(
                      height: 60,
                      width: 90,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10)),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const Gap(10),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.6,
                      height: 40,
                      child: Text(
                        message,
                      ),
                    )
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
