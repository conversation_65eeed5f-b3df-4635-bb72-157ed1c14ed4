import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/constants/app_assets.dart';
import '../../../../core/themes/app_font_family.dart';
import '../../../../shared/widgets/sames/build_bottom_total.dart';
import 'widgets/information_seller_buyer.dart';
import 'widgets/order_detail.dart';

class PayMentHome extends StatefulWidget {
  const PayMentHome({super.key});

  @override
  State<PayMentHome> createState() => _PayMentHomeState();
}

class _PayMentHomeState extends State<PayMentHome> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Payment",
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                fontFamily: AppFontFamily.semiBold,
              ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            width: 24,
          ),
          onPressed: () => context.pop(),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: IconButton(
              icon: SvgPicture.asset(
                AppAssets.balanceUser,
                width: 24,
              ),
              onPressed: () {},
            ),
          ),
        ],
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Gap(10.h),
            InformationSellerAndBuyer(
              name: "Dr. Laura Nguyen",
              numberPhone: "(+91) 3618 263 553",
            ),
            Gap(10.h),
            InformationSellerAndBuyer(
              checkSeller: false,
              name: "Customer ABC",
              numberPhone: "(+91) 3618 263 553",
            ),
            const OrderDetail(
              orderId: '832632',
              status: 'Pending',
              imageUrl:
                  "https://t4.ftcdn.net/jpg/02/60/04/09/360_F_260040900_oO6YW1sHTnKxby4GcjCvtypUCWjnQRg5.jpg",
              message: "I want to change the price from \$200 to \$180.",
            ),
            Gap(10.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        "Payment Details",
                        style:
                            Theme.of(context).textTheme.lightBodyLargeSemiBold,
                      ),
                    ],
                  ),
                  const Gap(10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Offer pirce",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeRegular
                            .copyWith(
                                color:
                                    Theme.of(context).textSecondary(context)),
                      ),
                      Text(
                        "\$ 180",
                        style:
                            Theme.of(context).textTheme.lightBodyLargeSemiBold,
                      ),
                    ],
                  ),
                  const Gap(5),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Voucher",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeRegular
                            .copyWith(
                                color:
                                    Theme.of(context).textSecondary(context)),
                      ),
                      Text(
                        "-\$ 2",
                        style:
                            Theme.of(context).textTheme.lightBodyLargeSemiBold,
                      ),
                    ],
                  ),
                  const Gap(5),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Platform fee",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeRegular
                            .copyWith(
                                color:
                                    Theme.of(context).textSecondary(context)),
                      ),
                      Text(
                        "\$ 9",
                        style:
                            Theme.of(context).textTheme.lightBodyLargeSemiBold,
                      ),
                    ],
                  ),
                  const Gap(10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Total cost",
                        style:
                            Theme.of(context).textTheme.lightBodyXLargeSemiBold,
                      ),
                      Text(
                        "\$ 187",
                        style:
                            Theme.of(context).textTheme.lightBodyXLargeSemiBold,
                      ),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
      bottomNavigationBar: BuildBottomTotal(
        total: "187",
        onPressed: () {},
      ),
    );
  }
}
