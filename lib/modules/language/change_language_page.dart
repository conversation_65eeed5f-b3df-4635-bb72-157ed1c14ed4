import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/translate/bloc/language_bloc.dart';
import 'package:multime_app/modules/translate/bloc/language_state.dart';
import 'package:multime_app/modules/translate/model/language_option.dart';

import '../translate/bloc/language_event.dart';

class ChangeLanguagePage extends StatefulWidget {
  const ChangeLanguagePage({super.key});

  @override
  State<ChangeLanguagePage> createState() => _ChangeLanguagePageState();
}

class _ChangeLanguagePageState extends State<ChangeLanguagePage> {
  TextEditingController searchLanguageController = TextEditingController();


  @override
  void initState() {
    super.initState();
    searchLanguageController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    searchLanguageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageBloc = context.read<LanguageBloc>();
    return Scaffold(
      body: BlocBuilder<LanguageBloc,LanguageState>(
        builder: (context,state){
         List<LanguageOption> listLanguage = [];
         if(state is LanguageSearchState){
            listLanguage = state.filteredLanguages;
         }else {
           listLanguage = context.read<LanguageBloc>().getLanguageOptions();
         }
         return Padding(
           padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
           child: SingleChildScrollView(
             child: Column(
               crossAxisAlignment: CrossAxisAlignment.start,
               children: [
                 Gap(60.h),
                 Center(
                   child: Text('Select Your Language',style: Theme.of(context).textTheme.headlineMedium,),
                 ),
                 Gap(16.h),
                 Container(
                   height: 50.h,
                   width: double.infinity,
                   padding: const EdgeInsets.symmetric(horizontal: 12),
                   decoration: BoxDecoration(
                     border: Border.all(
                         color: Theme.of(context).greyScale300(context), width: 1),
                     borderRadius: BorderRadius.circular(8),
                   ),
                   child: Row(
                     crossAxisAlignment: CrossAxisAlignment.center,
                     children: [
                       if (searchLanguageController.text.isEmpty)
                          Icon(
                           Icons.search,
                           size: 24,
                           color: Theme.of(context).textSecondary100(context)
                         ),
                       Expanded(
                         child: TextFormField(
                           controller: searchLanguageController,
                           decoration: InputDecoration(
                             hintText: 'Search',
                             hintStyle: TextStyle(
                               fontSize: 14.sp,
                               color:Theme.of(context).textSecondary100(context)
                             ),
                             suffixIcon: searchLanguageController.text.isNotEmpty
                                 ? GestureDetector(
                                 onTap: () {
                                   searchLanguageController.clear();
                                    languageBloc.add(SearchLanguageEvent(''));
                                 },
                                 child: const Icon(Icons.close))
                                 : null,
                             border: InputBorder.none,
                             enabledBorder: InputBorder.none,
                             focusedBorder: InputBorder.none,
                           ),
                           onChanged: (value) {
                             languageBloc.add(SearchLanguageEvent(value));
                           },
                         ),
                       ),
                     ],
                   ),
                 ),
                 Gap(16.h),
                 Text(
                   'SUGGESTED LANGUAGES',
                   style: Theme.of(context).textTheme.labelMedium!.copyWith(
                     color: Theme.of(context).greyScale600(context)
                   ),
                 ),
                 Gap(16.h),
                 if(
                  listLanguage.isEmpty
                  )
                    Center(
                      child: Text(
                        'No Language Found',
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                 ),
                 Column(
                   mainAxisSize: MainAxisSize.min,
                   children: listLanguage.map((option) {
                     return ListTile(
                       leading: ClipRRect(
                         borderRadius: BorderRadius.circular(3),
                         child: SvgPicture.asset(
                           option.flagIcon,
                           width: 25,
                           height: 25,
                           fit: BoxFit.fill,
                         ),
                       ),
                       title: Text(
                         option.displayName,
                         style: Theme.of(context).textTheme.titleLarge,
                       ),
                       onTap: () {
                         languageBloc.add(
                             ChangeLanguageEvent(option.locale, context));
                         debugPrint('Change Language to ${option.displayName}');
                       },
                     );
                   }).toList(),
                 ),
               ],
             ),
           ),
         );
        },
      ),
    );
  }
}
