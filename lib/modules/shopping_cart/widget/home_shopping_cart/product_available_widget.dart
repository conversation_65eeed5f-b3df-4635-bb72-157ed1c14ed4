import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_bloc.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_event.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_state.dart';
import 'package:multime_app/modules/shopping_cart/data/fake_data.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';
import 'package:multime_app/modules/shopping_cart/widget/home_shopping_cart/cart_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProductAvailableWidget extends StatefulWidget {
  final TabController tabController;
  const ProductAvailableWidget({super.key, required this.tabController});

  @override
  State<ProductAvailableWidget> createState() => _ProductAvailableWidgetState();
}

class _ProductAvailableWidgetState extends State<ProductAvailableWidget> {
  bool isChecked = false;
  @override
  Widget build(BuildContext context) {
    return TabBarView(
      controller: widget.tabController,
      children: [
        _buildShopList(context),
        _buildShopList(context),
      ],
    );
  }

  Widget _buildShopList(BuildContext context) {
    return ListView.builder(
      itemCount: shops.length,
      itemBuilder: (context, shopIndex) {
        final shop = shops[shopIndex];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildShopHeader(
              shop,
              context,
            ),
            ...shop.products.map((product) {
              return CartItem(
                shop: shop,
                product: product,
                onIncrease: () {
                  setState(() {
                    product.quantity++;
                  });
                },
                onDecrease: () {
                  setState(() {
                    if (product.quantity > 0) {
                      product.quantity--;
                    } else {
                      product.quantity = 0;
                    }
                  });
                },
                onDelete: () {},
              );
            }).toList(),
            Gap(10.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
              child: RichText(
                text: TextSpan(children: [
                  TextSpan(
                    text: '* ',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodySmallRegular
                        .copyWith(
                          color: Theme.of(context).primary(context),
                        ),
                  ),
                  TextSpan(
                    text:
                        'Purchase from reputable experts. Chat with experts to create a personalized offer.'
                        'Have a reliable platform. Personalize your specific needs. With guaranteed certification.',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodySmallRegular
                        .copyWith(
                          color: Theme.of(context).greyScale600(context),
                        ),
                  ),
                ]),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildShopHeader(
    Shop shop,
    BuildContext context,
  ) {
    return BlocBuilder<ShoppingCartBloc, ShoppingCartState>(
      builder: (context, state) {
        // Kiểm tra xem cửa hàng có trong danh sách không
        final isChecked = state.shops.any((s) => s.shopId == shop.shopId);

        return Row(
          children: [
            Checkbox(
              value: state.isSelectAll ? true : isChecked,
              checkColor: AppColors.white,
              activeColor: Theme.of(context).primary(context),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppRadius.radius6),
              ),
              onChanged: (value) {
                if (state.isSelectAll == true) {
                  context.read<ShoppingCartBloc>().add(SelectShopEvent(shop));
                } else {
                  context.read<ShoppingCartBloc>().add(SelectShopEvent(shop));
                }
              },
            ),
            Text(
              shop.shopName,
              style: Theme.of(context).textTheme.lightBodyMediumBold,
            ),
            Gap(10.w),
            SvgPicture.asset(
              AppAssets.arrowRightSvg,
              height: 25.h,
            ),
          ],
        );
      },
    );
  }
}
