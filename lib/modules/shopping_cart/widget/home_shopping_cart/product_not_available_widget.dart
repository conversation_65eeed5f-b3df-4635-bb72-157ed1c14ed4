import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ProductNotAvailableWidget extends StatelessWidget {
  final TabController tabController;

  ProductNotAvailableWidget({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    return TabBarView(
      controller: tabController,
      children: List.generate(2, (index) => _buildTabContent(context)),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      child: Container(
        padding: EdgeInsets.only(
            top: AppSpacing.padding16h, bottom: AppSpacing.padding24h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(AppAssets.lionShoppingSvg),
            Gap(24.h),
            Text(
              '"Explore our products with many attractive offers. Purchase from reputable experts. Chat with experts to create a personalized offer. Have a reliable platform. Personalize your specific needs. With guaranteed certification."',
              style: Theme.of(context).textTheme.lightBodyMediumMedium,
              textAlign: TextAlign.center,
            ),
            Gap(24.h),
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                minimumSize: Size(115, 40),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius10),
                ),
                backgroundColor:Theme.of(context).primary(context),
                foregroundColor: AppColors.white,
              ),
              child: Text('Shop now'),
            ),
          ],
        ),
      ),
    );
  }
}
