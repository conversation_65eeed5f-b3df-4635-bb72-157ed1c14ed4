import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_bloc.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_event.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_state.dart';
import 'package:multime_app/modules/shopping_cart/model/product.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';

class CartItem extends StatefulWidget {
  final Shop shop;
  final Product product;
  final Function() onIncrease;
  final Function() onDecrease;
  final Function() onDelete;
  const CartItem({
    super.key,
    required this.shop,
    required this.product,
    required this.onIncrease,
    required this.onDecrease,
    required this.onDelete,
  });
  @override
  State<CartItem> createState() => _CartItemState();
}

class _CartItemState extends State<CartItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 130.h,
      width: 396.w,
      padding: EdgeInsets.only(
        bottom: 16.h,
        right: 16.w,
      ),
      decoration:  BoxDecoration(
        color: AppColors.white,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).greyScale500(context),
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              BlocBuilder<ShoppingCartBloc, ShoppingCartState>(
                builder: (context, state) {
                  final isChecked = state.shops
                      .where((shop) => shop.shopId == widget.shop.shopId)
                      .expand((shop) => shop.products)
                      .any((product) =>
                          product.productId ==
                          widget.product
                              .productId); // Kiểm tra xem có sản phẩm nào có productId phù hợp

                  return Checkbox(
                    value: isChecked,
                    checkColor: AppColors.white,
                    activeColor: Theme.of(context).primary(context),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius6),
                    ),
                    onChanged: (value) {
                      // Khi checkbox được thay đổi (tích hoặc bỏ tích)
                      context.read<ShoppingCartBloc>().add(
                            UpdateProductInShopEvent(
                                widget.product, widget.shop),
                          );
                    },
                  );
                },
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(AppRadius.radius8),
                child: Image.network(
                  widget.product.imageUrl,
                  width: 46.w,
                  height: 46.h,
                ),
              ),
              Gap(10.w),
              Container(
                width: 150.w,
                height: 40.h,
                child: Text(
                  widget.product.productName,
                  style:
                      Theme.of(context).textTheme.lightBodySmallMedium.copyWith(
                            color: Theme.of(context).secondaryBase(context)
                          ),
                ),
              ),
              Spacer(),
              Column(
                children: [
                  Text(
                    '${widget.product.oldPrice.toStringAsFixed(2)}\$',
                    style:
                        Theme.of(context).textTheme.lightBodySmallBold.copyWith(
                              color: Theme.of(context).greyScale600(context),
                            ),
                  ),
                  Text(
                    '${widget.product.price.toStringAsFixed(2)}\$',
                    style:
                        Theme.of(context).textTheme.lightBodySmallBold.copyWith(
                              color:Theme.of(context).primary(context),
                            ),
                  ),
                ],
              ),
            ],
          ),
          Spacer(),
          Padding(
            padding: EdgeInsets.only(left: 45.w),
            child: Row(
              children: [
                GestureDetector(
                  onTap: widget.onDecrease,
                  child: Container(
                    height: 24.h,
                    width: 24.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).greyScale100(context),
                      borderRadius: BorderRadius.circular(AppRadius.radius6),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        AppAssets.minusSvg,
                        width: 12.w,
                      ),
                    ),
                  ),
                ),
                Gap(15.w),
                Text(widget.product.quantity.toStringAsFixed(0)),
                Gap(15.w),
                GestureDetector(
                  onTap: widget.onIncrease,
                  child: Container(
                    height: 24.h,
                    width: 24.w,
                    decoration: BoxDecoration(
                      color:Theme.of(context).greyScale500(context),
                      borderRadius: BorderRadius.circular(AppRadius.radius8),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        AppAssets.plusSvg,
                        width: 12.w,
                      ),
                    ),
                  ),
                ),
                Spacer(),
                InkWell(
                  onTap: widget.onDelete,
                  child: Container(
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AppAssets.trash_newSvg,
                          width: 24.w,
                          color: Theme.of(context).greyScale600(context),
                        ),
                        Gap(8.w),
                        Text(
                          'Delete',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumBold
                              .copyWith(
                                color: Theme.of(context).greyScale600(context),
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
