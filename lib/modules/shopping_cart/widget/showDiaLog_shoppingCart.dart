import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_bloc.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_event.dart';
import 'package:multime_app/modules/shopping_cart/data/fake_data.dart';

Future<void> showDiaLog_shoppingCart(BuildContext context) async {
  return showDialog(
    context: context,
    builder: (context) {
      int selectedIndex = -1;
      return StatefulBuilder(
        builder: (context, setState) => Material(
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8.w,
              height: MediaQuery.of(context).size.height * 0.8.h,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(AppRadius.radius10),
              ),
              padding: EdgeInsets.all(16.w),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      'Select Voucher',
                      style: Theme.of(context).textTheme.lightBodyXLargeBold,
                    ),
                    Gap(15.h),
                    // Voucher Code Input
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                              hintText: 'Enter voucher code',
                              hintStyle: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeRegular,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 12.h,
                              ),
                              border: OutlineInputBorder(
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius5),
                              ),
                            ),
                          ),
                        ),
                        Gap(10.w),
                        ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            elevation: 0,
                            side: BorderSide(
                                color: Theme.of(context).whitePrimary(context), width: 1),
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius10),
                            ),
                          ),
                          child: Text(
                            'Apply',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyMediumMedium
                                .copyWith(
                                  color: Theme.of(context).whitePrimary(context)
                                ),
                          ),
                        ),
                      ],
                    ),
                    Gap(20.h),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.5.h,
                      child: ListView.builder(
                        itemCount: vouchers.length,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          final voucher = vouchers[index];
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                // Handle selection logic here
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: 10.h),
                              padding: EdgeInsets.all(12.w),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius5),
                                border: Border.all(
                                  color: selectedIndex == index
                                      ? Theme.of(context).primary(context)
                                      : Theme.of(context).greyScale500(context), // Màu viền khi không chọn
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    width: 40.w,
                                    height: 40.h,
                                    color:
                                        Colors.red, // Placeholder for image
                                  ),
                                  Gap(10.w),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Giảm ${voucher.percentageDiscount}% tối đa ${voucher.maxDiscountAmount}\$',
                                          style: Theme.of(context)
                                              .textTheme
                                              .lightBodyMediumMedium,
                                        ),
                                        Gap(4.h),
                                        Text(
                                          'Tối thiểu ${voucher.minDiscountAmount}\$',
                                          style: Theme.of(context)
                                              .textTheme
                                              .lightBodyMediumMedium,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Checkbox(
                                    value: selectedIndex == index,
                                    onChanged: (value) {
                                      context
                                          .read<ShoppingCartBloc>()
                                          .add(SelectVoucherEvent(voucher));

                                      setState(() {
                                        if (value == true) {
                                          selectedIndex = index;
                                        } else {
                                          selectedIndex = -1;
                                        }
                                      });
                                    },
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          AppRadius.radius100),
                                    ),
                                    activeColor: Colors.white.withOpacity(0.9),
                                    checkColor: Theme.of(context).greyScale600(context),
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    Gap(24.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color:Theme.of(context).greyScale600(context),),
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius10),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                          ),
                        ),
                        Gap(10.w),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius10),
                              ),
                            ),
                            child: Text(
                              'OK',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge!
                                  .copyWith(color: AppColors.white),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    },
  );
}
