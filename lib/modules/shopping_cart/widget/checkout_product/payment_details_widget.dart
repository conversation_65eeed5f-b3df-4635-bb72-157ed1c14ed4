import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class PaymentDetailsWidget extends StatefulWidget {
  const PaymentDetailsWidget({super.key});

  @override
  State<PaymentDetailsWidget> createState() => _PaymentDetailsWidgetState();
}

class _PaymentDetailsWidgetState extends State<PaymentDetailsWidget> {
  int selectedIndex = -1;

  final List<Map<String, String>> paymentMethods = [
    {'title': 'Paypal', 'icon': AppAssets.iconPaypalSvg, 'number': '*1234'},
    {'title': 'Visa Card', 'icon': AppAssets.iconVisaSvg, 'number': '*5678'},
    {'title': 'Payoneer', 'icon': AppAssets.iconPayoneerSvg, 'number': '*2414'},
    {
      'title': 'Google Pay',
      'icon': AppAssets.iconGooglePaySvg,
      'number': '*8768'
    },
    {
      'title': 'Apple pay',
      'icon': AppAssets.iconApplePaySvg,
      'number': '*1124'
    },
  ];
  @override
  Widget build(
    BuildContext context,
  ) {
    Widget _buildPaymentMethod(BuildContext context, int index,
        String paymentMethod, String icon, String number) {
      return Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).lightGrey(context),
              width: 1,
            ),
          ),
          borderRadius: BorderRadius.circular(AppRadius.radius10),
        ),
        child: Row(
          children: [
            Checkbox(
              activeColor: Theme.of(context).secondary(context),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppRadius.radius100),
              ),
              checkColor: Theme.of(context).whitePrimary(context),
              value: selectedIndex == index,
              onChanged: (value) {
                setState(() {
                  selectedIndex = value! ? index : -1;
                });
              },
            ),
            Text(
              paymentMethod,
              style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                    color: Theme.of(context).textPrimary(context),
                  ),
            ),
            Gap(10),
            SvgPicture.asset(icon),
            Spacer(),
            Text(
              number,
              style: Theme.of(context).textTheme.lightBodySmallMedium.copyWith(
                    color: Theme.of(context).textSecondary(context),
                  ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.padding16, vertical: AppSpacing.padding10h),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).lightGrey(context),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppRadius.radius10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Payment Details',
                style: Theme.of(context).textTheme.lightBodyXLargeBold.copyWith(
                      color: Theme.of(context).secondary(context),
                    ),
              ),
              Text(
                'View all',
                style: Theme.of(context)
                    .textTheme
                    .lightBodySmallMedium
                    .copyWith(
                      color: Theme.of(context).alertInformationBase(context),
                    ),
              ),
            ],
          ),
          Gap(10),
          Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: List.generate(
                    paymentMethods.length,
                    (index) => _buildPaymentMethod(
                      context,
                      index,
                      paymentMethods[index]['title'] ?? '',
                      paymentMethods[index]['icon'] ?? '',
                      paymentMethods[index]['number'] ?? '',
                    ),
                  ),
                ),
                Gap(30.h),
                Text(
                  'To complete the transaction, we will transfer you to PayPal\'s secure servers.',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).textSecondary(context),
                      ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
