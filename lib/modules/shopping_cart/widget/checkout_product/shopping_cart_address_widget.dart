import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ShoppingCartAddressWidget extends StatelessWidget {
  const ShoppingCartAddressWidget({super.key});
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color:Theme.of(context).greyScale100(context),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppRadius.radius16),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(AppAssets.locationShopSvg),
              Gap(10.w),
              Text('Delivery Address',
                  style:
                      Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                            color:Theme.of(context).primary(context),
                          )),
            ],
          ),
          Gap(10.h),
          Row(
            children: [
              Text(
                'David Cursnor',
                style: Theme.of(context).textTheme.lightBodyLargeBold,
              ),
              Text(
                '(+84) 0123456678',
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
              ),
              Spacer(),
              SvgPicture.asset(AppAssets.arrowLeftSvg),
            ],
          ),
          Gap(10.h),
          Row(
            children: [
              Text(
                '10 Silicon Valley, USA',
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
              ),
            ],
          )
        ],
      ),
    );
  }
}
