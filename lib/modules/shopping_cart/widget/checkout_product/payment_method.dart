import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_bloc.dart';
import 'package:multime_app/modules/shopping_cart/widget/showDiaLog_shoppingCart.dart';

class PaymentMethod extends StatefulWidget {
  const PaymentMethod({super.key});
  @override
  State<PaymentMethod> createState() => _PaymentMethodState();
}

class _PaymentMethodState extends State<PaymentMethod> {
  int selectedIndex = -1;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ShoppingCartBloc(),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).greyScale100(context),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(AppRadius.radius10),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Payment Details',
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(
                      color: Theme.of(context).whitePrimary(context)
                    )),
            Gap(15.h),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.white,
                minimumSize: Size(364.w, 40.h),
                side:  BorderSide(
                  color: Theme.of(context).whitePrimary(context),
                  width: 1,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius10),
                ),
              ),
              onPressed: () {
                showDiaLog_shoppingCart(context);
              },
              child: Text(
                'Select or change a code',
                style: Theme.of(context).textTheme.lightBodyMediumMedium,
              ),
            ),
            Gap(15.h),
            _buildPayment('Subtotal (2 Packages)', '\$500'),
            _buildPayment('Shipping Fee', '\$5'),
            _buildPayment('Platform Fee', '\$25'),
            Gap(15.h),
            _buildPayment('Total Payment', '\$530'),
          ],
        ),
      ),
    );
  }

  Widget _buildPayment(String title, String value) {
    return Row(
      children: [
        Text(title,
            style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                  color:Theme.of(context).greyScale600(context),
                )),
        Spacer(),
        Text(value, style: Theme.of(context).textTheme.lightBodyMediumMedium),
      ],
    );
  }

  Widget _buildMethodsPayment(
    String svg,
    String code,
    bool isChecked,
    int index,
  ) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height * 0.06,
      padding: EdgeInsets.symmetric(horizontal: 17.w, vertical: 12.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(AppRadius.radius10)),
        color: AppColors.white,
        border: Border.all(
          color: isChecked ? Theme.of(context).whitePrimary(context) : Theme.of(context).greyScale100(context),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SvgPicture.asset(svg),
          Gap(17.w),
          Text(
            code,
            style: Theme.of(context).textTheme.lightBodySmallSemiBold,
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              setState(() {
                if (selectedIndex == index) {
                  selectedIndex = -1;
                } else {
                  selectedIndex = index;
                }
              });
            },
            child: isChecked
                ?  Icon(
                    Icons.check_circle_outline,
                    color: Theme.of(context).whitePrimary(context)
                  )
                : const Icon(
                    Icons.circle_outlined,
                  ),
          ),
        ],
      ),
    );
  }
}
