import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';
import 'package:multime_app/modules/shopping_cart/widget/checkout_product/payment_details_widget.dart';
import 'package:multime_app/modules/shopping_cart/widget/checkout_product/payment_method.dart';
import 'package:multime_app/modules/shopping_cart/widget/checkout_product/shopping_cart_address_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CheckoutWidget extends StatefulWidget {
  const CheckoutWidget({super.key, required this.shops});

  final List<Shop> shops;
  @override
  State<CheckoutWidget> createState() => _CheckoutWidgetState();
}

class _CheckoutWidgetState extends State<CheckoutWidget> {
  Widget _buildShopHeader(Shop shop, BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: AppSpacing.padding10),
      decoration: BoxDecoration(
          border: Border(
        bottom: BorderSide(
          color: Theme.of(context).greyScale500(context),
          width: 0.5,
        ),
      )),
      child: Row(
        children: [
          Text(
            shop.shopName,
            style: Theme.of(context).textTheme.lightBodyMediumBold,
          ),
          Gap(10.w),
          ElevatedButton.icon(
            onPressed: () {},
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.white,
              side: BorderSide(color: Theme.of(context).whitePrimary(context), width: 1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppRadius.radius6),
              ),
            ),
            icon: SvgPicture.asset(AppAssets.messagesShoppingSvg),
            label: Text(
              'Chat',
              style: Theme.of(context)
                  .textTheme
                  .lightBodyMediumRegular
                  .copyWith(color: Theme.of(context).whitePrimary(context)),
            ),
          ),
          Spacer(),
          Column(
            children: [
              Text(
                '7:55 AM',
                style: Theme.of(context).textTheme.lightBodySmallMedium,
              ),
              Gap(5.h),
              Text(
                'Apr 19,2024',
                style: Theme.of(context).textTheme.lightBodySmallMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            Container(
              child: Column(
                children: [
                  Gap(20.h),
                  GestureDetector(
                    onTap: () {
                      context.push(RouteName.addressSelectionScreen);
                    },
                    child: ShoppingCartAddressWidget(),
                  ),
                  Gap(16.h),
                  Container(
                    padding:
                        EdgeInsets.symmetric(vertical: AppSpacing.padding10),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).greyScale100(context),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(AppRadius.radius16),
                    ),
                    child: Column(
                      children: [
                        ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: widget.shops.length,
                          itemBuilder: (context, shopIndex) {
                            final shop = widget.shops[shopIndex];
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 10),
                              child: Column( 
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildShopHeader(shop, context),
                                  Gap(15.h),
                                  ...shop.products.map((product) {
                                    return Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: AppSpacing.padding10),
                                      decoration:  BoxDecoration(
                                        border: Border(
                                          bottom: BorderSide(
                                            color: Theme.of(context).greyScale500(context),
                                            width: 0.5,
                                          ),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: AppSpacing.padding10),
                                        child: Row(
                                          children: [
                                            Image.network(
                                              product.imageUrl,
                                              width: 40,
                                              height: 40.h,
                                              fit: BoxFit.cover,
                                            ),
                                            Gap(10.w),
                                            Flexible(
                                              flex: 3,
                                              child: Text(
                                                product.productName,
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .lightBodySmallMedium,
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 2,
                                              ),
                                            ),
                                            Gap(20.w),
                                            Text(
                                              'x${product.quantity}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyMediumMedium,
                                            ),
                                            Spacer(),
                                            Text(
                                              '\$${product.price}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyMediumMedium,
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ],
                              ),
                            );
                          },
                        ),
                        Gap(10.h),
                        TextFormField(
                          decoration: InputDecoration(
                            hintText: 'Leave your comment here',
                            hintStyle: Theme.of(context)
                                .textTheme
                                .lightBodyMediumRegular
                                .copyWith(color: Theme.of(context).greyScale500(context)),
                            border: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius10),
                              borderSide:
                                  BorderSide(color: Theme.of(context).greyScale500(context)),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: AppSpacing.padding20,
                                vertical: AppSpacing.padding16h),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(15),
                  PaymentDetailsWidget(),
                  Gap(15.h),
                  PaymentMethod(),
                  Gap(20.h),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
