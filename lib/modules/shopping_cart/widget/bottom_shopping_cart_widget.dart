import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_bloc.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_event.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_state.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomShoppingCart extends StatefulWidget {
  BottomShoppingCart({
    super.key,
  });
  @override
  State<BottomShoppingCart> createState() => _BottomShoppingCartState();
}

class _BottomShoppingCartState extends State<BottomShoppingCart> {
  bool isChecked = false;
  @override
  Widget build(BuildContext context) {
    final sizeHeight = MediaQuery.of(context).size.height;
    final sizeWidth = MediaQuery.of(context).size.width;
    return Container(
        height: sizeHeight * 0.1,
        width: sizeWidth,
        decoration:  BoxDecoration(
          color: AppColors.white,
          border: Border(
            top: BorderSide(
              color: Theme.of(context).greyScale100(context),
              width: 1,
            ),
          ),
        ),
        padding: EdgeInsets.only(left: 10.w),
        child: BlocBuilder<ShoppingCartBloc, ShoppingCartState>(
            builder: (context, state) {
          return Row(
            children: [
              Checkbox(
                  value: state.isSelectAll,
                  checkColor: AppColors.white,
                  activeColor: Theme.of(context).primary(context),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius6),
                  ),
                  onChanged: (value) {
                    setState(() {
                      state.isSelectAll = value!;
                      context
                          .read<ShoppingCartBloc>()
                          .add(IsSelectAll(state.isSelectAll));
                    });
                  }),
              const Text('All'),
              const Spacer(),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Total',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyXLargeMedium
                        .copyWith(
                          color: Theme.of(context).secondaryBase(context)
                        ),
                  ),
                  Text(
                    '\$ ${state.total}',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyXLargeBold
                        .copyWith(
                          color: Theme.of(context).primary(context),
                        ),
                  ),
                ],
              ),
              Gap(10.w),
              ElevatedButton(
                onPressed: () {
                  List<Map<String, dynamic>> shopsData =
                      state.shops.map((shop) {
                    return {
                      'shopName': shop.shopName,
                      'products': shop.products.map((product) {
                        return {
                          'productName': product.productName,
                          'imageUrl': product.imageUrl,
                          'oldPrice': product.oldPrice,
                          'quantity': product.quantity,
                          'price': product.price,
                        };
                      }).toList(),
                    };
                  }).toList();

                  context.pushNamed(
                    RouteName.checkoutProduct,
                    extra: {'shops': shopsData, 'total': state.total},
                  );
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: Size(
                    207.w,
                    sizeHeight,
                  ),
                  backgroundColor:Theme.of(context).primary(context),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
                child: Text(
                  'Checkout',
                  style:
                      Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                            color: AppColors.white,
                          ),
                ),
              )
            ],
          );
        }));
  }
}
