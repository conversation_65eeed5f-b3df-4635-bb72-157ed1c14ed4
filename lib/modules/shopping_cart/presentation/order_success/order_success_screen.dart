import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class OrderSuccessScreen extends StatelessWidget {
  const OrderSuccessScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: EdgeInsets.only(left: 10.w, right: 10.w),
          child: SizedBox(
            child: SvgPicture.asset(
              AppAssets.homeShoppingCartSvg,
            ),
          ),
        ),
        title: Text(
          'Order Successfully',
          style: Theme.of(context).textTheme.lightHeadingSmall,
        ),
      ),
      body: OrderSuccessWidget(),
    );
  }
}

class OrderSuccessWidget extends StatelessWidget {
  const OrderSuccessWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(AppAssets.lionShoppingSvg),
          Gap(32.h),
          Container(
            width: 250.w,
            child: Text(
              'Your order has been successfully delivered',
              style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
                    color: Theme.of(context).primary(context),
                  ),
              textAlign: TextAlign.center,
            ),
          ),
          Gap(10.h),
          Container(
            width: 270.w,
            child: Text(
              'Share it with your loved ones for tracking assistance.',
              style: Theme.of(context).textTheme.lightBodyLargeMedium,
              textAlign: TextAlign.center,
            ),
          ),
          Gap(32.h),
          Container(
            width: 340.w,
            height: 50.h,
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppRadius.radius10),
                border: Border.all(
                    color: Theme.of(context).whitePrimary(context), width: 1)),
            child: Row(
              children: [
                Text(
                  'https://strongbody.ai/s/GVAQGq',
                  style: Theme.of(context).textTheme.lightBodyMediumRegular,
                ),
                Spacer(),
                SvgPicture.asset(AppAssets.copyShoppingCartSvg),
              ],
            ),
          ),
          Gap(32.h),
          Container(
            width: 300.w,
            height: 50.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SvgPicture.asset(
                  AppAssets.logoMessageSvg,
                ),
                SvgPicture.asset(
                  AppAssets.logoFacebookSvg,
                ),
                SvgPicture.asset(
                  AppAssets.logoTwitterSvg,
                ),
                SvgPicture.asset(
                  AppAssets.logoInSvg,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
