import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/data/fake_data.dart';
import 'package:multime_app/modules/shopping_cart/widget/bottom_shopping_cart_widget.dart';
import 'package:multime_app/modules/shopping_cart/widget/home_shopping_cart/product_available_widget.dart';
import 'package:multime_app/modules/shopping_cart/widget/home_shopping_cart/product_not_available_widget.dart';

class HomeShoppingCartScreen extends StatefulWidget {
  const HomeShoppingCartScreen({super.key});
  @override
  State<HomeShoppingCartScreen> createState() => _HomeShoppingCartScreenState();
}

class _HomeShoppingCartScreenState extends State<HomeShoppingCartScreen>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(
          'Shopping Cart',
          style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
                color: Theme.of(context).textPrimary(context),
              ),
        ),
        centerTitle: true,
        elevation: 0,
        bottom: TabBar(
          padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
          controller: _tabController,
          labelStyle: Theme.of(context).textTheme.lightBodyXLargeBold.copyWith(
                color: Theme.of(context).primary(context),
              ),
          indicatorSize: TabBarIndicatorSize.tab,
          unselectedLabelStyle:
              Theme.of(context).textTheme.lightBodyXLargeRegular.copyWith(
                    color: Theme.of(context).textSecondary(context),
                  ),
          dividerColor: Theme.of(context).greyScale700(context),
          indicator: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).primary(context),
                width: 2,
              ),
            ),
          ),
          tabs: const [
            Tab(
              text: 'Product',
            ),
            Tab(
              text: 'Service',
            ),
          ],
        ),
      ),
      body: shops.isNotEmpty
          ? ProductAvailableWidget(tabController: _tabController)
          : ProductNotAvailableWidget(
              tabController: _tabController,
            ),
      bottomNavigationBar: BottomShoppingCart(),
    );
  }
}
