import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class AddressSelectionScreen extends StatelessWidget {
  const AddressSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Widget _buildAddressSelection(BuildContext context, bool isDefault,
        {VoidCallback? onTap}) {
      return GestureDetector(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.only(right: 15),
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        color: Theme.of(context).greyScale500(context),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Text(
                    'Elena',
                    style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                  ),
                ),
                Gap(15),
                Text(
                  '(+84) 981823455',
                  style: Theme.of(context).textTheme.lightBodyLargeRegular,
                ),
                Gap(15),
                SvgPicture.asset(AppAssets.editSvg)
              ],
            ),
            Text(
              'Apartment ABC, Building ABC',
              style: Theme.of(context).textTheme.lightBodyMediumRegular,
            ),
            Text('Singapore, Singapore',
                style: Theme.of(context).textTheme.lightBodyMediumRegular),
            Gap(10),
            if (isDefault)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Theme.of(context).errorLight(context),
                ),
                child: Text(
                  'Default',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallBold
                      .copyWith(color: Theme.of(context).primary(context)),
                ),
              )
          ],
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text('Address Selection'),
        titleTextStyle: Theme.of(context).textTheme.lightHeadingSmall,
        centerTitle: true,
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
        child: Column(
          children: [
            _buildAddressSelection(
              context,
              true,
              onTap: () {
                context.push(RouteName.editAddressSelectionScreen);
              },
            ),
            _buildAddressSelection(context, false),
            ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).whitePrimary(context),
                  side: BorderSide(
                    color: Theme.of(context).primary(context),
                    width: 1,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add),
                    Gap(10),
                    Text(
                      'Add New Address',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeMedium
                          .copyWith(color: Theme.of(context).primary(context)),
                    )
                  ],
                ))
          ],
        ),
      ),
    );
  }
}
