import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/card_text_field_post.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/home_page/session_heading.dart';

class EditAddressSelectionScreen extends StatefulWidget {
  const EditAddressSelectionScreen({super.key});

  @override
  State<EditAddressSelectionScreen> createState() =>
      _EditAddressSelectionScreenState();
}

class _EditAddressSelectionScreenState
    extends State<EditAddressSelectionScreen> {
  bool isChecked = false;
  @override
  Widget build(BuildContext context) {
    Widget buildButton(BuildContext context, String title,
        Color backgroundColor, Color textColor,
        {required bool isBorderSide}) {
      return SizedBox(
        width: MediaQuery.of(context).size.width,
        child: ElevatedButton(
          onPressed: () {},
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            side: isBorderSide
                ? BorderSide(
                    color: Theme.of(context).secondary(context),
                    width: 1,
                  )
                : BorderSide.none,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          child: Text(
            title,
            style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                  color: textColor,
                ),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text('Address Selection'),
        titleTextStyle: Theme.of(context).textTheme.lightHeadingSmall,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              const TSectionHeading(
                title: 'Contact',
                showActionButton: false,
              ),
              const Gap(15),
              const CardTextFieldPost(
                  title: 'Name', icon: AppAssets.editSvg, checkMaxLine: true),
              Gap(15),
              const CardTextFieldPost(
                  title: 'Phone numbers',
                  icon: AppAssets.editSvg,
                  checkMaxLine: true),
              const Gap(15),
              const CardTextFieldPost(
                  title: 'Alternate correct phone Number(Optional)',
                  icon: AppAssets.editSvg,
                  checkMaxLine: true),
              const Gap(20),
              const TSectionHeading(
                title: 'Address',
                showActionButton: false,
              ),
              const Gap(15),
              const CardTextFieldPost(
                  title: 'Location',
                  icon: AppAssets.editSvg,
                  checkMaxLine: true),
              const Gap(15),
              const CardTextFieldPost(
                  title: 'City', icon: AppAssets.editSvg, checkMaxLine: true),
              const Gap(15),
              const CardTextFieldPost(
                  title: 'Delivery Address',
                  icon: AppAssets.editSvg,
                  checkMaxLine: true),
              const Gap(15),
              SvgPicture.asset(AppAssets.basemapImageSvg),
              const Gap(15),
              Row(
                children: [
                  Checkbox(
                      value: isChecked,
                      activeColor: Theme.of(context).secondary(context),
                      checkColor: Theme.of(context).whitePrimary(context),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5),
                      ),
                      onChanged: (value) {
                        setState(() {
                          isChecked = value!;
                        });
                      }),
                  Text(
                    'Set as default address',
                    style: Theme.of(context).textTheme.lightBodyMediumMedium,
                  ),
                ],
              ),
              buildButton(
                  context,
                  'Delete Address',
                  Theme.of(context).whitePrimary(context),
                  Theme.of(context).textPrimary(context),
                  isBorderSide: true),
              const Gap(15),
              buildButton(
                  context,
                  'Submit',
                  Theme.of(context).secondary(context),
                  Theme.of(context).whitePrimary(context),
                  isBorderSide: false),
            ],
          ),
        ),
      ),
    );
  }
}
