import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';
import 'package:multime_app/modules/shopping_cart/widget/checkout_product/checkout_widget.dart';

class CheckoutProductScreen extends StatelessWidget {
  CheckoutProductScreen({super.key, required this.shops, required this.total});
  final double total;
  final List<Shop> shops;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: () {
            context.go(RouteName.homeShoppingCart);
          },
        ),
        title: Text(
          'Checkout',
          style: Theme.of(context)
              .textTheme
              .lightHeadingSmall
              .copyWith(color: Theme.of(context).secondaryBase(context)),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: CheckoutWidget(shops: shops),
      bottomNavigationBar: BottomShoppingCart(
        total: total,
      ),
    );
  }
}

class BottomShoppingCart extends StatefulWidget {
  const BottomShoppingCart({super.key, required this.total});
  final double total;
  @override
  State<BottomShoppingCart> createState() => _BottomShoppingCartState();
}

class _BottomShoppingCartState extends State<BottomShoppingCart> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70.h,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).greyScale100(context),
            width: 1,
          ),
        ),
      ),
      padding: EdgeInsets.only(left: 10.w),
      child: Row(
        children: [
          const Spacer(),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Total',
                style: Theme.of(context)
                    .textTheme
                    .lightBodyXLargeMedium
                    .copyWith(color: Theme.of(context).secondaryBase(context)),
              ),
              Text(
                '\$ ${widget.total}',
                style: Theme.of(context).textTheme.lightBodyXLargeBold.copyWith(
                      color: Theme.of(context).primary(context),
                    ),
              ),
            ],
          ),
          Gap(10.w),
          ElevatedButton(
            onPressed: () {
              context.go(RouteName.orderSuccessScreen);
            },
            style: ElevatedButton.styleFrom(
              minimumSize: Size(207.w, 70.h),
              backgroundColor: Theme.of(context).primary(context),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(1),
              ),
            ),
            child: Text(
              'Checkout',
              style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                    color: AppColors.white,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
