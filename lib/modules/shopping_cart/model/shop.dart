import 'package:multime_app/modules/shopping_cart/model/product.dart';

class Shop {
  final int? shopId;
  final String shopName;
  final List<Product> products;
  Shop({
    this.shopId,
    required this.shopName,
    required this.products,
  });
  // Phương thức để khởi tạo từ JSON (nếu dữ liệu lấy từ API)
  factory Shop.fromJson(Map<String, dynamic> json) {
    return Shop(
      shopId: json['id'] as int,
      shopName: json['shopName'] as String,
      products: (json['products'] as List<dynamic>)
          .map((productJson) => Product.fromJson(productJson))
          .toList(),
    );
  }
  // Phương thức để chuyển đối tượng Shop thành JSON
  Map<String, dynamic> toJson() {
    return {
      'shopName': shopName,
      'products': products.map((product) => product.toJson()).toList(),
    };
  }

  Shop copyWith({String? id, String? name, List<Product>? products}) {
    return Shop(
      shopId: shopId ?? this.shopId,
      shopName: shopName ?? this.shopName,
      products: products ?? this.products,
    );
  }
}
