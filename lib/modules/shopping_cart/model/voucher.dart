class Voucher {
  int id; // Mã ID của voucher
  String code; // Mã voucher
  double discountValue; // <PERSON>i<PERSON> trị giảm giá (fixed hoặc percentage)
  String type; // Loại giảm giá (percentage/fixed)
  DateTime expiryDate; // <PERSON><PERSON><PERSON> hết hạn
  bool isUsed; // Trạng thái đã sử dụng hay chưa
  String description; // Mô tả voucher
  double? maxDiscountAmount; // Giá trị giảm tối đa
  double? minDiscountAmount; // Giá trị giảm tối thiểu
  double? percentageDiscount; // Phần trăm giảm giá (0-100)

  Voucher({
    required this.id,
    required this.code,
    required this.discountValue,
    required this.type,
    required this.expiryDate,
    this.isUsed = false,
    this.description = '',
    this.maxDiscountAmount,
    this.minDiscountAmount,
    this.percentageDiscount,
  });
  factory Voucher.fromJson(Map<String, dynamic> json) {
    return Voucher(
      id: json['id'] as int,
      code: json['code'] as String,
      discountValue: json['discountValue'] as double,
      type: json['type'] as String,
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      isUsed: json['isUsed'] as bool,
      description: json['description'] as String,
      maxDiscountAmount: json['maxDiscountAmount'] as double,
      minDiscountAmount: json['minDiscountAmount'] as double,
      percentageDiscount: json['percentageDiscount'] as double,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'discountValue': discountValue,
      'type': type,
      'expiryDate': expiryDate.toIso8601String(),
      'isUsed': isUsed,
      'description': description,
      'maxDiscountAmount': maxDiscountAmount,
      'minDiscountAmount': minDiscountAmount,
      'percentageDiscount': percentageDiscount,
    };
  }
}
