class Product {
  final int? productId;
  final String productName;
  final String imageUrl;
  final double price;
  final double oldPrice;
  int quantity;
  Product({
    this.productId,
    required this.productName,
    required this.imageUrl,
    required this.price,
    required this.oldPrice,
    required this.quantity,
  });
  // Phương thức khởi tạo từ JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      productId: json['productId'] as int,
      productName: json['productName'] as String,
      imageUrl: json['imageUrl'] as String,
      price: json['price'] as double,
      oldPrice: json['oldPrice'] as double,
      quantity: json['quantity'] as int,
    );
  }
  // Phương thức để chuyển đối tượng Product thành JSON
  Map<String, dynamic> toJson() {
    return {
      'productName': productName,
      'imageUrl': imageUrl,
      'price': price,
      'oldPrice': oldPrice,
      'quantity': quantity,
    };
  }
}
