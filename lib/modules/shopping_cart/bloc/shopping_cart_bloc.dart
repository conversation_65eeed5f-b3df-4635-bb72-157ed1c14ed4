import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_event.dart';
import 'package:multime_app/modules/shopping_cart/bloc/shopping_cart_state.dart';
import 'package:multime_app/modules/shopping_cart/model/product.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';

class ShoppingCartBloc extends Bloc<ShoppingCartEvent, ShoppingCartState> {
  ShoppingCartBloc()
      : super(ShoppingCartState(
            shops: [], total: 0, products: [], isSelectAll: false)) {
    on<SelectShopEvent>(_onSelectShopEvent);
    on<SelectProductEvent>(_onSelectProductEvent);
    on<UpdateProductInShopEvent>(_onUpdateProductInShopEvent);
    on<IsSelectAll>(_onIsSelectAll);
    on<SelectVoucherEvent>(_onSelectVoucherEvent);
  }

  void _onSelectShopEvent(
      SelectShopEvent event, Emitter<ShoppingCartState> emit) {
    List<Shop> updatedShops = List.from(state.shops);

    final isShopExist =
        updatedShops.any((shop) => shop.shopId == event.shop.shopId);

    if (isShopExist) {
      updatedShops.removeWhere((shop) => shop.shopId == event.shop.shopId);
    } else {
      updatedShops.add(event.shop);
    }

    emit(state.copyWith(
      shops: updatedShops,
    ));
  }

  void _onSelectProductEvent(
      SelectProductEvent event, Emitter<ShoppingCartState> emit) {
    List<Product> updateProducts = List.from(state.products);
    final isProductExist = updateProducts
        .any((product) => product.productId == event.product.productId);
    if (isProductExist) {
      updateProducts.removeWhere(
          (product) => product.productId == event.product.productId);
    } else {
      updateProducts.add(event.product);
    }
    print(updateProducts);
    emit(state.copyWith(products: updateProducts));
  }

  void _onIsSelectAll(IsSelectAll event, Emitter<ShoppingCartState> emit) {
    emit(state.copyWith(isSelectAll: event.isSelectAll));
  }

  void _onSelectVoucherEvent(
      SelectVoucherEvent event, Emitter<ShoppingCartState> emit) {
    if (state.voucher == event.voucher) {
      emit(state.copyWith(voucher: null));
    } else {
      emit(state.copyWith(voucher: event.voucher));
      print(state.voucher);
    }
  }

  // void _onSelectProductEvent(
  //     SelectProductEvent event, Emitter<ShoppingCartState> emit) {
  //   List<Shop> updatedShops = List.from(state.shops);

  //   final isShopExist = updatedShops.any((shop) => shop.id == event.shop.id);

  //   if (isShopExist) {
  //     updatedShops.removeWhere((shop) => shop.id == event.shop.id);
  //   } else {
  //     updatedShops.add(event.shop);
  //   }

  //   emit(state.copyWith(shops: updatedShops));
  // }

  void _onUpdateProductInShopEvent(
      UpdateProductInShopEvent event, Emitter<ShoppingCartState> emit) {
    final updatedShops = List<Shop>.from(state.shops);

    final shopIndex =
        updatedShops.indexWhere((shop) => shop.shopId == event.shop.shopId);

    if (shopIndex != -1) {
      final updatedProducts =
          List<Product>.from(updatedShops[shopIndex].products);

      final productIndex = updatedProducts.indexWhere(
          (product) => product.productId == event.product.productId);

      if (productIndex != -1) {
        // Nếu sản phẩm đã có, xóa nó khỏi danh sách (bỏ tích checkbox)
        updatedProducts.removeAt(productIndex);
      } else {
        // Nếu sản phẩm chưa có, thêm nó vào danh sách (tích checkbox)
        updatedProducts.add(event.product);
      }

      final updatedShop =
          updatedShops[shopIndex].copyWith(products: updatedProducts);
      updatedShops[shopIndex] = updatedShop;

      emit(state.copyWith(
          shops: updatedShops)); // Cập nhật lại state với danh sách các shop
    }
  }
}
