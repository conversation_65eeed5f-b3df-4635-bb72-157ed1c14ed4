import 'package:multime_app/modules/shopping_cart/model/product.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';
import 'package:multime_app/modules/shopping_cart/model/voucher.dart';

class ShoppingCartState {
  List<Shop> shops;
  double total;
  List<Product> products;
  Voucher? voucher;
  bool isSelectAll = false;
  ShoppingCartState(
      {required this.shops,
      required this.total,
      required this.products,
      this.voucher,
      required this.isSelectAll});

  ShoppingCartState copyWith(
      {List<Shop>? shops,
      List<Product>? products,
      bool? isSelectAll,
      Voucher? voucher}) {
    double newTotal = calculateTotalQuantity(shops ?? this.shops);
    return ShoppingCartState(
        shops: shops ?? this.shops,
        voucher: voucher ?? this.voucher,
        products: products ?? this.products,
        total: newTotal,
        isSelectAll: isSelectAll ?? this.isSelectAll);
  }

  double calculateTotalQuantity(List<Shop> shops) {
    double totalQuantity = 0;
    for (var shop in shops) {
      for (var product in shop.products) {
        totalQuantity += product.price;
      }
    }
    return totalQuantity;
  }
}
