import 'package:multime_app/modules/shopping_cart/model/product.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';
import 'package:multime_app/modules/shopping_cart/model/voucher.dart';

abstract class ShoppingCartEvent {
  const ShoppingCartEvent();
}

class SelectShopEvent implements ShoppingCartEvent {
  final Shop shop;
  SelectShopEvent(this.shop);
}

class SelectProductEvent implements ShoppingCartEvent {
  final Product product;
  SelectProductEvent(this.product);
}

class UpdateProductInShopEvent implements ShoppingCartEvent {
  final Product product;
  final Shop shop;

  UpdateProductInShopEvent(this.product, this.shop);
}

class TotalQuantyProductEvent implements ShoppingCartEvent {
  final int total;
  TotalQuantyProductEvent(this.total);
}

class SelectVoucherEvent implements ShoppingCartEvent {
  final Voucher voucher;
  SelectVoucherEvent(this.voucher);
}

class IsSelectAll implements ShoppingCartEvent {
  final bool isSelectAll;
  IsSelectAll(this.isSelectAll);
}
