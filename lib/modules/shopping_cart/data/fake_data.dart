import 'package:multime_app/modules/shopping_cart/model/shop.dart';
import 'package:multime_app/modules/shopping_cart/model/product.dart';
import 'package:multime_app/modules/shopping_cart/model/voucher.dart';

List<Shop> shops = [
  Shop(
    shopId: 1,
    shopName: "Shop A",
    products: [
      Product(
        productId: 1,
        productName: "Gum Balm Gel Curr for Kid Curr",
        imageUrl:
            'https://images.pexels.com/photos/3270223/pexels-photo-3270223.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
        price: 250,
        oldPrice: 532,
        quantity: 1,
      ),
      Product(
        productId: 2,
        productName: "Gum Balm Gel Curr for Kid Curr",
        imageUrl:
            'https://images.pexels.com/photos/3270223/pexels-photo-3270223.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
        price: 250,
        oldPrice: 532,
        quantity: 1,
      ),
    ],
  ),
  Shop(
    shopId: 2,
    shopName: "Lincoln Shop",
    products: [
      Product(
        productId: 3,
        productName: "Gum Balm Gel Curr for Kid Curr",
        imageUrl:
            'https://images.pexels.com/photos/3907507/pexels-photo-3907507.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
        price: 250,
        oldPrice: 532,
        quantity: 1,
      ),
      Product(
        productId: 4,
        productName: "Gum Balm Gel Curr for Kid Curr",
        imageUrl:
            'https://images.pexels.com/photos/3907507/pexels-photo-3907507.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
        price: 250,
        oldPrice: 532,
        quantity: 1,
      ),
    ],
  ),
];

List<Voucher> vouchers = [
  Voucher(
    id: 1,
    code: '001',
    discountValue: 300,
    type: '',
    expiryDate: DateTime(2021, 10, 24),
    description: '',
    isUsed: false,
    maxDiscountAmount: 500,
    minDiscountAmount: 200,
    percentageDiscount: 12,
  ),
  Voucher(
    id: 2,
    code: '002',
    discountValue: 200,
    type: '',
    expiryDate: DateTime(2021, 10, 24),
    description: '',
    isUsed: false,
    maxDiscountAmount: 200,
    minDiscountAmount: 300,
    percentageDiscount: 10,
  ),
  Voucher(
    id: 3,
    code: '003',
    discountValue: 500,
    type: '',
    expiryDate: DateTime(2021, 10, 24),
    description: '',
    isUsed: false,
    maxDiscountAmount: 250,
    minDiscountAmount: 150,
    percentageDiscount: 8,
  ),
  Voucher(
    id: 4,
    code: '004',
    discountValue: 150,
    type: '',
    expiryDate: DateTime(2021, 10, 24),
    description: '',
    isUsed: false,
    maxDiscountAmount: 200,
    minDiscountAmount: 50,
    percentageDiscount: 4,
  ),
  Voucher(
    id: 5,
    code: '005',
    discountValue: 100,
    type: '',
    expiryDate: DateTime(2021, 10, 24),
    description: '',
    isUsed: false,
    maxDiscountAmount: 50,
    minDiscountAmount: 10,
    percentageDiscount: 2,
  )
];
