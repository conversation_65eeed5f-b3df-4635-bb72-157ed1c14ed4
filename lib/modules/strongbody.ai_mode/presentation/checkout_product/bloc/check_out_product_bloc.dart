import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/data/param/order_product_param.dart';
import 'package:multime_app/core/model/order/create_order_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/response/check_out_product_reponse.dart';

import '../../../../../core/model/order/order_product_model.dart';

part 'check_out_product_event.dart';
part 'check_out_product_state.dart';

class CheckoutProductBloc
    extends Bloc<CheckoutProductEvent, CheckoutProductState> {
  final CheckoutProductReponseRemote checkoutProductReponseRemote;

  CheckoutProductBloc(this.checkoutProductReponseRemote)
      : super(CheckoutProductState.initial()) {
    on<CheckoutProductOrderEvent>(_onCheckoutProductOrderEvent);
  }

  Future<void> _onCheckoutProductOrderEvent(CheckoutProductOrderEvent event,
      Emitter<CheckoutProductState> emit) async {
    emit(state.copyWith(
      orderProductResponse: ApiResponse.loading(),
    ));
    try {
      final response =
          await checkoutProductReponseRemote.checkoutOrderProduct(event.param);
      emit(state.copyWith(
        orderProductResponse: response,
      ));
    } catch (e) {
      emit(state.copyWith(
        orderProductResponse:
            ApiResponse.error('Checkout failed: ${e.toString()}'),
      ));
    }
  }
}
