part of 'check_out_product_bloc.dart';

class CheckoutProductState extends Equatable {
  final ApiResponse<CreateOrderProductModel> productResponse;
  final ApiResponse<OrderProductModel> orderProductResponse;

  const CheckoutProductState(this.productResponse, this.orderProductResponse);

  factory CheckoutProductState.initial() => const CheckoutProductState(
        ApiResponse.loading(),
        ApiResponse.loading(),
      );
  // factory CheckoutProductState.loading() => const CheckoutProductState(ApiResponse.loading());
  // factory CheckoutProductState.success(CreateOrderProductModel data) => CheckoutProductState(ApiResponse.completed(data));
  // factory CheckoutProductState.error(String message) => CheckoutProductState(ApiResponse.error(message));

  CheckoutProductState copyWith({
    ApiResponse<CreateOrderProductModel>? productResponse,
    ApiResponse<OrderProductModel>? orderProductResponse,
  }) {
    return CheckoutProductState(
      productResponse ?? this.productResponse,
      orderProductResponse ?? this.orderProductResponse,
    );
  }

  @override
  List<Object?> get props => [productResponse, orderProductResponse];
}
