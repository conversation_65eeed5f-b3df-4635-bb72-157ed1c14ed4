import 'package:dio/dio.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/data/param/order_product_param.dart';
import 'package:multime_app/core/model/order/order_product_model.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';

import '../../../../../core/base/api_response/api_response.dart';
import '../../../../../core/data/param/create_order_product_param.dart';
import '../../../../../core/model/order/create_order_product.dart';

abstract class CheckoutProductReponseRemote {
  Future<ApiResponse<CreateOrderProductModel>> createOrderProduct(
      CreateOrderProductParam param);
  Future<ApiResponse<OrderProductModel>> checkoutOrderProduct(
      OrderProductParam param);
}

class CheckoutProductReponseRemoteImp implements CheckoutProductReponseRemote {
  final ApiClient _apiClient;

  CheckoutProductReponseRemoteImp(this._apiClient);

  @override
  Future<ApiResponse<CreateOrderProductModel>> createOrderProduct(
      CreateOrderProductParam param) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.createOrderProduct,
        method: ApiType.post,
        data: param.toJson(),
      );
      final data = response['data'];
      return ApiResponse.completed(
        CreateOrderProductModel.fromJson(data),
      );
    } on FormatException catch (e) {
      return ApiResponse.error('Invalid response format: ${e.message}');
    } on DioException catch (e) {
      // Xử lý chi tiết các lỗi HTTP
      final errorData = e.response?.data;
      if (errorData is Map<String, dynamic>) {
        return ApiResponse.error(
          errorData['message'] ?? 'Create order failed',
          code: e.response?.statusCode,
        );
      }
      return ApiResponse.error(
        'Network error: ${e.message}',
        code: e.response?.statusCode,
      );
    } catch (e) {
      return ApiResponse.error('Unexpected error: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<OrderProductModel>> checkoutOrderProduct(
      OrderProductParam param) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.checkoutOrderProduct,
        method: ApiType.post,
        data: param.toJson(),
      );
      final data = response['data'];
      return ApiResponse.completed(
        OrderProductModel.fromJson(data),
      );
    } on FormatException catch (e) {
      return ApiResponse.error('Invalid response format: ${e.message}');
    } on DioException catch (e) {
      // Xử lý chi tiết các lỗi HTTP
      final errorData = e.response?.data;
      if (errorData is Map<String, dynamic>) {
        return ApiResponse.error(
          errorData['message'] ?? 'Checkout failed',
          code: e.response?.statusCode,
        );
      }
      return ApiResponse.error(
        'Network error: ${e.message}',
        code: e.response?.statusCode,
      );
    } catch (e) {
      return ApiResponse.error('Unexpected error: ${e.toString()}');
    }
  }
}
