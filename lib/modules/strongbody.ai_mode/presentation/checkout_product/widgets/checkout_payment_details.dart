import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/offer/presentation/widgets/voucher_picker.dart';
import 'package:multime_app/core/themes/theme.dart';

class CheckoutPaymentDetails extends StatefulWidget {
  const CheckoutPaymentDetails({
    super.key,
    required this.price,
    required this.service,
    required this.total,
    this.onVoucherApplied,
  });

  final double price;
  final double service;
  final double total;
  final Function(String voucherCode, double discount)? onVoucherApplied;

  @override
  State<CheckoutPaymentDetails> createState() => _CheckoutPaymentDetailsState();
}

class _CheckoutPaymentDetailsState extends State<CheckoutPaymentDetails> {
  double _discount = 0.0;
  String? _appliedVoucher;

  @override
  void dispose() {
    super.dispose();
  }

  void _applyVoucher(String code, double discount) {
    setState(() {
      _discount = discount;
      _appliedVoucher = code;
    });
    widget.onVoucherApplied?.call(code, discount);
  }

  void _removeVoucher() {
    setState(() {
      _discount = 0.0;
      _appliedVoucher = null;
    });
    widget.onVoucherApplied?.call('', 0.0);
  }

  double get finalTotal => widget.total - _discount;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).lightGrey(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.paymentDetailsTitle.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          Gap(16.h),
          _buildDetailRow(LocaleKeys.productPrice.tr(),
              "\$${widget.price.toStringAsFixed(2)}"),
          _buildDetailRow(LocaleKeys.serviceFee.tr(),
              "\$${widget.service.toStringAsFixed(2)}"),

          // Voucher Section
          Gap(8.h),
          _buildVoucherButton(),

          if (_discount > 0) ...[
            Gap(8.h),
            _buildDetailRow(
              'Giảm giá voucher',
              '-\$${_discount.toStringAsFixed(2)}',
              color: Colors.green,
            ),
          ],

          const Divider(thickness: 1, height: 24),
          _buildTotalCostRow(finalTotal, context),
          Gap(16.h),
          _buildAgreementText(),
        ],
      ),
    );
  }

  Widget _buildVoucherButton() {
    return ElevatedButton(
      onPressed: () async {
        final result = await showModalBottomSheet(
          context: context,
          backgroundColor: Theme.of(context).lightGrey(context),
          isScrollControlled: true,
          builder: (context) {
            return const VoucherPicker();
          },
        );
        if (result is Map && result['id'] != null) {
          // Mock tính giảm theo id voucher (có thể thay bằng BE)
          final String code = result['id'];
          double discount = 0.0;
          switch (code) {
            case 'voucher_1':
              discount = widget.total * 0.12;
              break;
            case 'voucher_2':
              discount = widget.total * 0.10;
              break;
            case 'voucher_3':
              discount = widget.total * 0.05;
              break;
            case 'voucher_4':
              discount = widget.total * 0.15;
              break;
            case 'voucher_5':
              discount = widget.total * 0.20;
              break;
            case 'voucher_6':
              discount = widget.total * 0.25;
              break;
          }
          _applyVoucher(code, discount);
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).transparent(context),
        foregroundColor: Theme.of(context).textPrimary(context),
        overlayColor:
            Theme.of(context).blackPrimary(context).withOpacity(0.005),
        minimumSize: Size(double.infinity, 40.h),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            color: Theme.of(context).textPrimary(context),
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
      ).copyWith(
        elevation: WidgetStateProperty.all(0),
      ),
      child: Text(_appliedVoucher == null
          ? 'Select a voucher'
          : 'Voucher: $_appliedVoucher'),
    );
  }

  Widget _buildDetailRow(String title, String value, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: GoogleFonts.plusJakartaSans(
              fontSize: 14.sp,
              fontWeight: FontWeight.normal,
              color: color,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.plusJakartaSans(
              fontSize: 14.sp,
              fontWeight: FontWeight.normal,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalCostRow(double total, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.totalCost.tr(),
          style: GoogleFonts.plusJakartaSans(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        Text(
          "\$${total.toStringAsFixed(2)}",
          style: GoogleFonts.plusJakartaSans(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).errorBase(context),
          ),
        ),
      ],
    );
  }

  Widget _buildAgreementText() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(color: Colors.black, fontSize: 12.sp),
              children: [
                TextSpan(text: LocaleKeys.termsAndConditions.tr()),
                TextSpan(
                  text: LocaleKeys.termsAndConditionsLink.tr(),
                  style: const TextStyle(color: Colors.blue),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
