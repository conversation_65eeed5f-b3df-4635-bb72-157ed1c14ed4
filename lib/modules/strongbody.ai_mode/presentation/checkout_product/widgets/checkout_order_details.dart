import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/constants/app_assets.dart';

import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field_note.dart';

class CheckoutOrderDetails extends StatelessWidget {
  const CheckoutOrderDetails({
    super.key,
    required this.title,
    required this.quantity,
    required this.price,
    required this.shopName,
    required this.userId,
    required this.mediaUrls,
  });

  final String? title;
  final int quantity;
  final double? price;
  final List<String> mediaUrls;
  final String shopName;
  final int userId;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding:  EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).lightGrey(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildShopName(context),
          Gap(6.h),
          Divider(
            color: Colors.grey.shade300,
            thickness: 1,
          ),
          Gap(12.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              mediaUrls.isNotEmpty
                  ? Container(
                      height: 46.h,
                      width: 46.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: NetworkImage(mediaUrls.first),
                          fit: BoxFit.cover,
                        ),
                      ),
                    )
                  : Container(
                      height: 80.h,
                      width: 80.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[200],
                      ),
                      child: Icon(
                        Icons.image,
                        size: 25.sp,
                        color: Theme.of(context).greyScale700(context),
                      ),
                    ),
              Gap(12.w),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Product title with ellipsis
                    Expanded(
                      child: Text(
                        title ?? 'Product Title',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: GoogleFonts.plusJakartaSans(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Gap(8.w),
                    Text(
                      "x$quantity",
                      style: GoogleFonts.plusJakartaSans(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Gap(12.w),
                    Text(
                      "\$${price?.toStringAsFixed(2) ?? '0.00'}",
                      style: GoogleFonts.plusJakartaSans(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Gap(16.h),
          _buildNoteForSeller(context),
        ],
      ),
    );
  }

  Widget _buildShopName(BuildContext context) {
    return Row(
      children: [
        Text(
          shopName,
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w700,
              ),
        ),
        Gap(8.w),
        GestureDetector(
          onTap: () {},
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Theme.of(context).errorBase(context),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Chat',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).errorBase(context),
                      ),
                ),
                Gap(4.w),
                SvgPicture.asset(
                  AppAssets.iconChatSvg,
                  colorFilter: ColorFilter.mode(
                    Theme.of(context).errorBase(context),
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }

  // Note for Seller
  Widget _buildNoteForSeller(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        'Message for Sellers:',
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
      ),
      Gap(8.h),
      StrongBodyTextFieldNote(
        noteText: '',
        labelText: 'Leave your comment here',
      )
    ]);
  }
}
