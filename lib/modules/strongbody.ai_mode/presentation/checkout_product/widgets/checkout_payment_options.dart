import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/constants/app_assets.dart';

enum PaymentMethod { card, cash, bankTransfer }

class CheckoutPaymentOptions extends StatefulWidget {
  const CheckoutPaymentOptions({
    super.key,
    required this.selectedMethod,
    required this.onPaymentMethodChanged,
  });

  final PaymentMethod selectedMethod;
  final Function(PaymentMethod) onPaymentMethodChanged;

  @override
  State<CheckoutPaymentOptions> createState() => _CheckoutPaymentOptionsState();
}

class _CheckoutPaymentOptionsState extends State<CheckoutPaymentOptions> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).lightGrey(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.paymentOptionsTitle.tr(),
            style: GoogleFonts.plusJakartaSans(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          Gap(16.h),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 1,
            separatorBuilder: (context, index) => Divider(
              color: Theme.of(context).greyScale100(context),
            ),
            itemBuilder: (context, index) {
              return RadioListTile<PaymentMethod>(
                onChanged: (value) {
                  if (value != null) widget.onPaymentMethodChanged(value);
                },
                value: PaymentMethod.card,
                groupValue: widget.selectedMethod,
                activeColor: Theme.of(context).informationBase(context),
                contentPadding: EdgeInsets.zero,
                visualDensity: VisualDensity.compact,
                title: Row(
                  children: [
                    Text(
                      'Stripe',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    Gap(4.w),
                    SvgPicture.asset(
                      AppAssets.stripe,
                      height: 28.h,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Helper method to get payment method string for API calls
  static String getPaymentMethodString(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.card:
        return 'card';
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
    }
  }
}
