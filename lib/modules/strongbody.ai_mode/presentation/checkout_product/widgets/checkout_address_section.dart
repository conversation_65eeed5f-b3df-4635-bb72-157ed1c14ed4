import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';

class CheckoutAddressSection extends StatelessWidget {
  const CheckoutAddressSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddressBloc, AddressState>(
      builder: (context, state) {
        if (state.addresses?.data?.isNotEmpty ?? false) {
          List<Address> address = [];
          address = state.addresses!.data!
              .where((add) => add.isDefault == true)
              .toList();
          if (address.isEmpty) {
            address.add(state.addresses!.data!.first);
          }

          return GestureDetector(
            onTap: () async {
              await (context).push(RouteName.addressMarketplace);
              if (context.mounted) {
                context.read<AddressBloc>().add(const GetAddressEvent());
              }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Theme.of(context).lightGrey(context),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Theme.of(context).errorBase(context),
                    ),
                    Gap(8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                "${address.first.street}",
                                style: GoogleFonts.plusJakartaSans(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Gap(8.w),
                              Text(
                                "${address.first.phone}",
                                style: GoogleFonts.plusJakartaSans(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                          Gap(4.h),
                          Text(
                            "${address.first.city}, ${address.first.state}, ${address.first.street}",
                            style: GoogleFonts.plusJakartaSans(
                              fontSize: 12.sp,
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.arrow_forward_ios,
                        size: 16.sp, color: Colors.black),
                  ],
                ),
              ),
            ),
          );
        } else {
          return GestureDetector(
            onTap: () async {
              await (context).push(RouteName.addressMarketplace);
              if (context.mounted) {
                context.read<AddressBloc>().add(const GetAddressEvent());
              }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Theme.of(context).lightGrey(context),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Theme.of(context).errorBase(context),
                    ),
                    Gap(8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Select address",
                            style: GoogleFonts.plusJakartaSans(
                              fontSize: 12.sp,
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.arrow_forward_ios,
                        size: 16.sp, color: Colors.black),
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }
}
