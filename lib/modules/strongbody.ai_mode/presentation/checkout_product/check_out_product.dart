import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/data/param/order_product_param.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/order/order_product_model.dart';
import 'package:multime_app/core/services/stripe_service.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/bloc/check_out_product_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_app_bar.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_body.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_payment_options.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/service/pusher_service.dart';
import '../../../../app/routers/routers_name.dart';
import '../../../../core/model/payment_result_data.dart';
import '../../../../shared/widgets/sames/build_bottom_total.dart';

class CheckoutProductPage extends StatefulWidget {
  CheckoutProductPage(
      {super.key,
      required this.productModel,
      required this.quantity,
      required this.unit,
      required this.total});
  final ProductModel productModel;
  final int quantity;
  final String unit;
  final double total;

  @override
  State<CheckoutProductPage> createState() => _CheckoutProductPageState();
}

class _CheckoutProductPageState extends State<CheckoutProductPage> {
  final double _mockPlatformFee = 29.99;
  double _voucherDiscount = 0.0;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.card;

  int? customerId;
  Timer? _timeoutTimer;
  bool _isPaymentInProgress = false;
  String? _lastProcessedClientSecret;
  bool _isSubscribedToPusher = false;

  @override
  void initState() {
    super.initState();
    customerId = gs.uid;
  }

  @override
  void dispose() {
    super.dispose();
    _timeoutTimer?.cancel();
    // Unsubscribe from Pusher channel if needed
    if (customerId != null) {
      PusherService.unsubscribeFromOrderChannel(customerId!);
    }
  }

  void _onVoucherApplied(String voucherCode, double discount) {
    setState(() {
      _voucherDiscount = discount;
    });
  }

  void _onPaymentMethodChanged(PaymentMethod method) {
    setState(() {
      _selectedPaymentMethod = method;
    });
  }

  String _getPaymentMethodString(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.card:
        return 'card';
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
    }
  }

  double get finalTotal => widget.total + _mockPlatformFee - _voucherDiscount;
  double get productPrice => widget.productModel.pricingTiers
      .firstWhere(
        (tier) => tier.unit == widget.unit,
        orElse: () => widget.productModel.pricingTiers.first,
      )
      .price
      .toDouble();

  @override
  Widget build(BuildContext context) {
    return BlocSelector<AddressBloc, AddressState, Address?>(
        selector: (addrState) {
      final list = addrState.addresses?.data ?? [];
      if (list.isEmpty) return null;
      return list.firstWhere(
        (add) => add.isDefault == true,
        orElse: () => list.first,
      );
    }, builder: (context, selectedAddress) {
      return MultiBlocListener(
        listeners: [
          BlocListener<CheckoutProductBloc, CheckoutProductState>(
            listener: (context, state) async {
              if (state.orderProductResponse.status == Status.error) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        state.orderProductResponse.message ?? 'Có lỗi xảy ra'),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                  ),
                );
              } else if (state.orderProductResponse.status ==
                      Status.completed &&
                  state.orderProductResponse.data?.client_secret != null &&
                  state.orderProductResponse.data!.client_secret.isNotEmpty) {
                final addrState = context.read<AddressBloc>().state;
                final list = addrState.addresses?.data ?? [];
                final Address? selectedAddress = list.isEmpty
                    ? null
                    : list.firstWhere(
                        (add) => add.isDefault == true,
                        orElse: () => list.first,
                      );
                if (selectedAddress == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        content: Text('Vui lòng chọn địa chỉ giao hàng!'),
                        backgroundColor: Colors.red),
                  );
                  return;
                }

                final orderData = context
                    .read<CheckoutProductBloc>()
                    .state
                    .orderProductResponse
                    .data;
                if (orderData?.order_id != null &&
                    customerId != null &&
                    !_isSubscribedToPusher) {
                  _setupOrderStatusListener(customerId!);
                  _isSubscribedToPusher = true;
                }

                final newSecret =
                    state.orderProductResponse.data!.client_secret;
                if (_isPaymentInProgress &&
                    _lastProcessedClientSecret == newSecret) {
                  return;
                }
                _isPaymentInProgress = true;
                _lastProcessedClientSecret = newSecret;

                final result = await StripePaymentService.processPayment(
                  paymentIntentClientSecret: newSecret,
                  amount: finalTotal,
                  currency: 'USD',
                );

                _isPaymentInProgress = false;
                final orderData2 = context
                    .read<CheckoutProductBloc>()
                    .state
                    .orderProductResponse
                    .data;
                if (result['success'] == true) {
                  _showPaymentProcessingDialog();
                  _startTimeoutTimer();
                } else {
                  _cleanupOrderTracking();
                  if (result['errorCode'] == 'user_cancelled') {
                    // do nothing
                  } else {
                    _navigateToFailedResult(orderData2, selectedAddress);
                  }
                }
              }
            },
          ),
          // Removed second BlocListener: Stripe handled directly like Offer
        ],
        child: BlocBuilder<CheckoutProductBloc, CheckoutProductState>(
          builder: (context, state) {
            return Scaffold(
              appBar: const CheckoutAppBar(),
              backgroundColor: Colors.white,
              body: CheckoutBody(
                productModel: widget.productModel,
                quantity: widget.quantity,
                unit: widget.unit,
                platformFee: _mockPlatformFee,
                totalAmount: widget.total,
                onVoucherApplied: _onVoucherApplied,
                onPaymentMethodChanged: _onPaymentMethodChanged,
                selectedPaymentMethod: _selectedPaymentMethod,
              ),
              bottomNavigationBar: BuildBottomTotal(
                total: '${finalTotal.toStringAsFixed(2)}',
                onPressed: () {
                  if (selectedAddress == null) {
                    // Show error: Chưa chọn địa chỉ
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text('Vui lòng chọn địa chỉ giao hàng!'),
                          backgroundColor: Colors.red),
                    );
                    return;
                  }

                  // Xử lý theo từng phương thức thanh toán
                  if (_selectedPaymentMethod == PaymentMethod.card) {
                    _handleCardPayment(selectedAddress);
                  } else {
                    // xử lý các phương thức thanh toán khác
                  }
                },
              ),
            );
          },
        ),
      );
    });
  }

  void _handleCardPayment(Address selectedAddress) {
    context.read<CheckoutProductBloc>().add(CheckoutProductOrderEvent(
          OrderProductParam(
            customer_id: gs.uid,
            shop_id: widget.productModel.userId,
            method: _getPaymentMethodString(_selectedPaymentMethod),
            address_id: selectedAddress.id!,
            products: [
              OrderProduct(
                productId: widget.productModel.id,
                unit: widget.unit,
                quantity: widget.quantity,
              ),
            ],
          ),
        ));
  }

  void _setupOrderStatusListener(int customerId) {
    print('🔗 Setting up Pusher listener for User ID: $customerId');

    PusherService.subscribeToOrderChannel(customerId, (orderUpdate) {
      if (orderUpdate.isPaymentSuccessful) {
        _handlePaymentSuccess(orderUpdate);
      } else if (orderUpdate.isPaymentFailed) {
        _handlePaymentFailed(orderUpdate);
      }
    });
  }

  void _handlePaymentSuccess(OrderModelUpdate update) {
    _cleanupOrderTracking();
    _dismissProcessingDialog();
    final successData = PaymentResultData.success(
      amount: widget.total,
      itemName: widget.productModel.name,
      quantity: widget.quantity,
      itemPrice: productPrice,
      unit: widget.unit,
      imageUrl: widget.productModel.image,
      shopName: widget.productModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: update.order_no,
      orderTime: DateTime.now(),
      orderStatus: update.order_status,
      phone: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.phone;
      })(),
      recipientName: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.recipientName;
      })(),
      street: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.street;
      })(),
      city: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.city;
      })(),
      userId: widget.productModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': successData,
      'onPrimaryAction': () {
        // Navigate to order history
      },
      'onSecondaryAction': () {
        // Continue shopping
      },
    });
  }

  void _showPaymentProcessingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false, // Prevent back button
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  void _cleanupOrderTracking() {
    _timeoutTimer?.cancel();
    if (customerId != null) {
      PusherService.unsubscribeFromOrderChannel(customerId!);
    }
    _isSubscribedToPusher = false;
  }

  void _dismissProcessingDialog() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context); // Close processing dialog
    }
  }

  void _handlePaymentFailed(OrderModelUpdate update) {
    // Clean up order tracking and dismiss dialog
    _cleanupOrderTracking();
    _dismissProcessingDialog();

    // Navigate to failed page with retry option
    final failedData = PaymentResultData.failed(
      amount: widget.total,
      itemName: widget.productModel.name,
      quantity: widget.quantity,
      itemPrice: productPrice,
      unit: widget.unit,
      imageUrl: widget.productModel.image,
      shopName: widget.productModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: update.order_no,
      orderTime: DateTime.now(),
      orderStatus: update.order_status,
      phone: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.phone;
      })(),
      recipientName: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.recipientName;
      })(),
      street: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.street;
      })(),
      city: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.city;
      })(),
      userId: widget.productModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': failedData,
      'onPrimaryAction': () {
        // Retry payment
        context.pop();
      },
      'onSecondaryAction': () {
        // Contact support
        context.pop();
      },
    });
  }

  void _startTimeoutTimer() {
    _timeoutTimer = Timer(Duration(seconds: 10), () {
      _cleanupOrderTracking();
      _dismissProcessingDialog();
      _handlePaymentTimeout();
    });
  }

  void _handlePaymentTimeout() {
    final timeoutData = PaymentResultData.failed(
      amount: finalTotal,
      itemName: widget.productModel.name,
      quantity: widget.quantity,
      itemPrice: productPrice,
      unit: widget.unit,
      imageUrl: widget.productModel.image,
      shopName: widget.productModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: 'Processing...',
      orderTime: DateTime.now(),
      orderStatus: 'Pending',
      phone: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.phone;
      })(),
      recipientName: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.recipientName;
      })(),
      street: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.street;
      })(),
      city: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.city;
      })(),
      userId: widget.productModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': timeoutData,
      'onPrimaryAction': () {
        // Check order history - navigate back to home
        context.pop();
      },
      'onSecondaryAction': () {
        // Continue shopping
        context.pop();
      },
    });
  }

  void _navigateToFailedResult(
      OrderProductModel? orderData, Address selectedAddress) {
    final failedData = PaymentResultData.failed(
      amount: widget.total,
      itemName: widget.productModel.name,
      quantity: widget.quantity,
      itemPrice: productPrice,
      unit: widget.unit,
      imageUrl: widget.productModel.image,
      shopName: widget.productModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: orderData?.order_no ?? 'FAILED',
      orderTime: DateTime.now(),
      orderStatus: 'Awaiting payment',
      phone: selectedAddress.phone,
      recipientName: selectedAddress.recipientName,
      street: selectedAddress.street,
      city: selectedAddress.city,
      userId: widget.productModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': failedData,
      'onPrimaryAction': () {
        context.pop();
      },
      'onSecondaryAction': () {
        context.pop();
      },
    });
  }
}
