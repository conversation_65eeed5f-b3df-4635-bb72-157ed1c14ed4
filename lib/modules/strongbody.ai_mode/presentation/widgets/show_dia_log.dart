import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/news/report_user.dart';

Future<void> showCommonDialog(BuildContext context, String title,
    String message, String description, String slogan) async {
  return showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        content: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 18.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black),
                ),
              ),
              Gap(10.h),
              RichText(
                textAlign: TextAlign.start,
                text: TextSpan(
                  text: message,
                  style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: Colors.black),
                  children: <TextSpan>[
                    TextSpan(
                      text: description,
                      style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.black),
                    ),
                  ],
                ),
              ),
              Gap(10.h),
              Text(
                slogan,
                textAlign: TextAlign.start,
                style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: Colors.black),
              ),
              Gap(10.h),
              StrongBodyButton(
                label: 'Get started',
                onPressed: () {},
                textColor: Colors.white,
                backgroundColor: Colors.red,
              )
            ],
          ),
        ),
      );
    },
  );
}

Future<void> buildReport(BuildContext context) {
  return showDialog(
    context: context,
    barrierColor: Theme.of(context).greyScale700(context),
    builder: (context) => AlertDialog(
        contentPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.radius16),
        ),
        content: ReportUser()),
  );
}
