import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/themes/theme.dart';

// ignore: must_be_immutable
class ButtonIconWidget extends StatelessWidget {
  Function() onTap;
  String text;
  Color? color;
  String iconSvg;
  final double radius;
  final double height;

  ButtonIconWidget({
    super.key,
    required this.onTap,
    required this.text,
    required this.iconSvg,
    this.color,
    this.radius = 100,
    this.height = 50,
  });

  @override
  Widget build(BuildContext context) {
    final buttonColor = color ?? Theme.of(context).backgroundRed(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: height,
        decoration: BoxDecoration(
          color: buttonColor,
          borderRadius: BorderRadius.circular(radius),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                text,
                style: TextStyle(
                  color: Theme.of(context).whitePrimary(context),
                  fontSize: 15,
                  fontWeight: FontWeight.w800,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
              const SizedBox(width: 10),
              SizedBox(
                width: 23,
                height: 23,
                child: SvgPicture.asset(iconSvg),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
