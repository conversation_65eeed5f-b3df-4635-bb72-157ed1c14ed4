import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardProductStrongBody extends StatelessWidget {
  const CardProductStrongBody(
      {super.key, required this.image, required this.job, required this.id});
  final String image, job, id;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 174.w,
      height: 200.h,
      padding: EdgeInsets.only(left: 6.w, right: 6.w, top: 6.w, bottom: 8.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Theme.of(context).whitePrimary(context),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).blackPrimary(context).withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 8,
            offset: Offset(1, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: CustomImage(
              path: image,
              imageType: ImageType.network,
              fit: BoxFit.cover,
              width: 164.w,
              height: 116.h,
            ),
          ),
          Gap(10.h),
          Center(
            child: Text(
              textAlign: TextAlign.center,
              job,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
                color: Colors.black,
              ),
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}
