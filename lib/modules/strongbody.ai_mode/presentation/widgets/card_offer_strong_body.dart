import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardOfferStrongBody extends StatelessWidget {
  const CardOfferStrongBody(
      {super.key,
      required this.image,
      required this.auth,
      required this.avatar,
      required this.description,
      required this.price,
      required this.days,
      required this.title});

  final String image, auth, avatar, description, title;
  final num price;
  final String days;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 14.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3), // changes position of shadow
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(6.r),
                child: CustomImage(
                  path: image,
                  imageType: ImageType.network,
                  fit: BoxFit.cover,
                  width: 150.w,
                  height: 104.h,
                ),
              ),
              Gap(14.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(60.r),
                          child: CustomImage(
                            path: image,
                            imageType: ImageType.network,
                            fit: BoxFit.cover,
                            width: 30.h,
                            height: 30.h,
                          ),
                        ),
                        Gap(10.w),
                        Text(
                          auth,
                          style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.black),
                        ),
                      ],
                    ),
                    Text(
                      title,
                      style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.black),
                    ),
                  ],
                ),
              )
            ],
          ),
          Gap(22.h),
          Text(
            "US \$$price - $days delivery days ",
            style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black),
          ),
          Gap(6.h),
          Text(
            description,
            style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black),
          ),
          Gap(22.h),
          Row(
            children: [
              Expanded(
                  child: StrongBodyButton(
                      label: 'Open in Chat',
                      onPressed: () {},
                      backgroundColor: Colors.white,
                      textColor: Colors.black,
                      borderColor: Theme.of(context).secondary(context))),
              Gap(14.w),
              Expanded(
                child: StrongBodyButton(
                  label: 'Review',
                  onPressed: () {},
                  backgroundColor: Colors.red,
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
