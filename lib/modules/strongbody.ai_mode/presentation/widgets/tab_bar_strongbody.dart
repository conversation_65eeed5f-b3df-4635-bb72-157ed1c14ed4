import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';

class TabBarStrongBody extends StatelessWidget {
  const TabBarStrongBody({super.key, required this.controller});
  final TabController controller;

  @override
  Widget build(BuildContext context) {
    return  TabBar(
      unselectedLabelColor: Theme.of(context).textSecondary(context),
      controller: controller,
      tabAlignment: TabAlignment.start,
      isScrollable: true,
      indicatorColor: Theme.of(context).primary(context),
      dividerColor: Colors.transparent,
      labelColor: Theme.of(context).primary(context),
      labelStyle:  TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
      ),

      tabs: const [
        Tab(
          text: 'All',
        ),
        Tab(
          text: 'Medical',
        ),
        Tab(
          text: 'Nutrition',
        ),
        Tab(
          text: 'Fitness',
        ),
        Tab(
          text: 'Mental',
        ),
        Tab(
          text: 'Pharmacy',
        ),
        Tab(
          text: 'Beauty',
        ),
        Tab(
          text: 'Baby&Kids',
        ),
        Tab(
          text: 'Family',
        ),
        <PERSON>b(
          text: 'Elderly',
        ),
        Tab(
          text: 'Travel',
        ),
        Tab(
          text: 'MedSupport',
        ),
        Tab(
          text: 'Digital works',
        ),
        Tab(
          text: 'Creative works',
        ),
        Tab(
          text: 'Consulting works',
        ),
        Tab(
          text: 'Technical works',
        ),
        Tab(
          text: 'Physical Work',
        ),
        Tab(
          text: 'Administrative works',
        ),
      ],
    );
  }
}
