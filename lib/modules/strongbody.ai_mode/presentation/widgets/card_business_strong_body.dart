
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

import '../../../../core/constants/app_assets.dart';

class CardBusinessStrongBody extends StatelessWidget {
  const CardBusinessStrongBody({super.key, required this.image, required this.label, required this.description});
  final String image, label, description;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200.h,
      width:496.w,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          image:  const DecorationImage(
              image: AssetImage(AppAssets.bgCard),
              fit: BoxFit.contain
          )
      ),
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          Padding(
            padding: EdgeInsets.only( top: 32.h,left: 10.w),
            child: SizedBox(
              width: 208.w,
              height: 165.h,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                   label,
                    style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.black
                    ),
                  ),
                  Gap(8.h),
                  Text(
                    description,
                    maxLines: 3,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                  ),
                  Gap(12.h),
                  ElevatedButton(
                    onPressed: (){},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      minimumSize: Size(108.w, 46.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Invite Friends",
                          style: TextStyle(
                            color:  Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Gap(4.w),
                        SvgPicture.asset(AppAssets.icArrowRight)
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            right:10.w ,
            child: Container(
              width: 122.w,
              height: 153.h,
              decoration:const BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8)
                  ),
                  image:   DecorationImage(
                      image:AssetImage("assets/image/freepik__bg.png"),
                      fit: BoxFit.cover
                  )
              ),
            ),

          )
        ],

      ),
    );
  }
}
