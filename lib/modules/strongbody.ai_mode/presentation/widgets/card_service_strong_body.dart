import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardServiceStrongBody extends StatelessWidget {
  const CardServiceStrongBody(
      {super.key,
      required this.image,
      required this.avatar,
      required this.description,
      required this.auth,
      required this.price});

  final String image, avatar, description, auth;
  final int price;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
          child: CustomImage(
            height: 230.h,
            width: 396.w,
            path: image,
            fit: BoxFit.cover,
            imageType: ImageType.network,
          ),
        ),
        Gap(10.h),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(50.r),
              child: CustomImage(
                path: avatar,
                fit: BoxFit.cover,
                imageType: ImageType.network,
                height: 32.h,
                width: 32.h,
              ),
            ),
            Gap(6.w),
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  description,
                  style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black),
                  maxLines: 2,
                ),
                Gap(6.h),
                Text(
                  auth,
                  style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textSecondary(context)),
                )
              ],
            ))
          ],
        ),
        Gap(5.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            RichText(
              text: TextSpan(
                  text: 'From',
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: Theme.of(context).textSecondary(context)),
                  children: [
                    TextSpan(
                      text: ' \$ $price',
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.black),
                    ),
                  ]),
            )
          ],
        ),
      ],
    );
  }
}
