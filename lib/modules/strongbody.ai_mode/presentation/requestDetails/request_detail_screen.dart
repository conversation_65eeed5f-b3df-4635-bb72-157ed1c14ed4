import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/requestDetails/bloc/request_detail_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/requestDetails/widgets/card_comment_item.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class RequestDetailScreen extends StatelessWidget {
  const RequestDetailScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: BlocConsumer<RequestDetailBloc, RequestDetailState>(
        listener: (context, state) {
          if (state.reqDetails.status == Status.loading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
        },
        builder: (context, state) {
          var requestDetail = state.reqDetails.data;
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.padding16,
                  vertical: AppSpacing.padding12h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Posting time',
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular,
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSpacing.padding16,
                          vertical: AppSpacing.padding6h,
                        ),
                        decoration: BoxDecoration(
                          // color: _getStatusColor(requestDetail.status),
                          color: _getStatusBgColor(context, 'Pending'),

                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          // requestDetail.productRequestStatusText,
                          'Pending',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodySmallBold
                              .copyWith(
                                // color: _getStatusTextColor(requestDetail.status),
                                color: _getStatusTextColor(context, 'Pending'),
                              ),
                        ),
                      )
                    ],
                  ),
                  Gap(15.h),
                  Text(
                    '${requestDetail?.title}',
                    style: Theme.of(context).textTheme.lightBodyXLargeSemiBold,
                  ),
                  Gap(20.h),
                  Text('Description',
                      style: Theme.of(context).textTheme.lightBodyLargeRegular),
                  Gap(10.h),
                  // Text(
                  //   '${requestDetail}',
                  //   style: Theme.of(context).textTheme.lightBodyLargeRegular,
                  // ),
                  Text('${requestDetail?.description}',
                      style: Theme.of(context).textTheme.lightBodyLargeRegular),
                  // ListView.builder(
                  //     itemCount: requestDetail.services!.length,
                  //     shrinkWrap: true,
                  //     physics: const NeverScrollableScrollPhysics(),
                  //     itemBuilder: (_, index) {
                  //       var service = requestDetail.services![index];
                  //       return Column(
                  //         crossAxisAlignment: CrossAxisAlignment.start,
                  //         children: [
                  //           Text(
                  //             service,
                  //             style: Theme.of(context)
                  //                 .textTheme
                  //                 .lightBodyLargeRegular,
                  //           ),
                  //           Gap(5.h),
                  //         ],
                  //       );
                  //     }),
                  // Gap(10.h),
                  // Column(
                  //   children: requestDetail.highlights!
                  //       .map<Widget>((e) => Text(
                  //             e,
                  //             style: Theme.of(context)
                  //                 .textTheme
                  //                 .lightBodyLargeRegular,
                  //           ))
                  //       .toList(),
                  // ),
                  // Gap(10.h),
                  // Text(
                  //   '${requestDetail.closingMessage}',
                  //   style: Theme.of(context).textTheme.lightBodyLargeRegular,
                  // ),
                  Gap(20.h),
                  Text('Category',
                      style: Theme.of(context).textTheme.lightBodyLargeRegular),
                  Gap(10.h),
                  // Wrap(
                  //   runSpacing: AppSpacing.padding16,
                  //   spacing: AppSpacing.padding16,
                  //   crossAxisAlignment: WrapCrossAlignment.center,
                  //   children: requestDetail.category!
                  //       .map<Widget>((e) =>
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.padding12,
                      vertical: AppSpacing.padding6h,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).greyScale200(context),
                      borderRadius: BorderRadius.circular(
                        AppRadius.radius6,
                      ),
                    ),
                    child: Text(
                      // e,
                      '${requestDetail?.categoryName}',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeRegular
                          .copyWith(
                            color: Theme.of(context).textSecondary(context),
                          ),
                    ),
                    // ))
                    // .toList(),
                  ),
                  Gap(20.h),
                  Text('Supporting file',
                      style: Theme.of(context).textTheme.lightBodyLargeRegular),
                  Gap(10.h),
                  SizedBox(
                    height: 175,
                    child: ListView.separated(
                      itemCount: requestDetail?.mediaUrls?.length ?? 0,
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) {
                        final image = requestDetail?.mediaUrls?[index];
                        return ClipRRect(
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius4),
                          child: CustomImage(
                            path: image ?? '',
                            imageType: ImageType.network,
                          ),
                        );
                      },
                      separatorBuilder: (context, index) => Gap(20.w),
                    ),
                  ),
                  Gap(20.h),
                  Text('Respond or Offer',
                      style: Theme.of(context).textTheme.lightBodyLargeRegular),
                  Gap(20.h),
                  ListView.separated(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        final comment = requestDetail?.comments?[index];
                        return CardCommentItem(
                          image: comment?.seller?.profileImage ?? '',
                          name: comment?.seller?.username ?? 'N/A',
                          country: comment?.seller?.country ?? 'N/A',
                          content: comment?.content ?? '',
                        );
                      },
                      separatorBuilder: (context, index) => Gap(20.h),
                      itemCount: requestDetail?.comments?.length ?? 0),
                  Gap(15.h),
                  (requestDetail?.comments?.length ?? 0) > 3
                      ? Center(
                          child: ElevatedButton(
                            onPressed: () {},
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(
                                horizontal: AppSpacing.padding16,
                                vertical: AppSpacing.padding12h,
                              ),
                              backgroundColor:
                                  Theme.of(context).whitePrimary(context),
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius10),
                              ),
                              side: BorderSide(
                                color: Theme.of(context).greyScale200(context),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'View more ${requestDetail!.comments!.length - 3} comments',
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyMediumMedium,
                                ),
                                Gap(10.w),
                                SvgPicture.asset(
                                  AppAssets.arrowDownSvg,
                                ),
                              ],
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                  Gap(10.h)
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Color _getStatusTextColor(BuildContext context, String status) {
    switch (status) {
      case 'Pending':
        return Theme.of(context).warningBase(context);

      case 'Completed':
        return Theme.of(context).successBase(context);
      default:
        return Theme.of(context).warningLight(context);
    }
  }

  Color _getStatusBgColor(BuildContext context, String status) {
    switch (status) {
      case 'Pending':
        return Theme.of(context).warningLight(context);

      case 'Completed':
        return Theme.of(context).successLight(context);
      default:
        return Theme.of(context).warningLight(context);
    }
  }
}
