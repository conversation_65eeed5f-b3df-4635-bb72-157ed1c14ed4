import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';
import 'package:readmore/readmore.dart';

import '../../../../../core/constants/app_assets.dart';

class CardCommentItem extends StatelessWidget {
  const CardCommentItem(
      {super.key,
      required this.image,
      required this.name,
      required this.country,
      required this.content});
  final String image;
  final String name;
  final String country;
  final String content;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: CustomImage(
                path: image,
                width: 40.h,
                height: 40.h,
                fit: BoxFit.cover,
                imageType: ImageType.network,
              ),
            ),
            Gap(10.h),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.lightBodyMediumBold,
                ),
                Text(country),
              ],
            ),
            const Spacer(),
            ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.padding12,
                        vertical: AppSpacing.padding10h),
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: const BorderSide(color: Colors.black))),
                child: Row(
                  children: [
                    SvgPicture.asset(AppAssets.message),
                    const Gap(10),
                    Text(
                      'Chat',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyMediumMedium
                          .copyWith(color: Colors.black),
                    )
                  ],
                )),
            IconButton(
                onPressed: () {},
                icon: const Icon(
                  Icons.more_horiz,
                  color: Colors.black,
                )),
          ],
        ),
        Gap(10.h),
        Container(
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).greyScale100(context),
              width: 1,
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: ReadMoreText(
            content,
            trimLines: 4,
            colorClickableText: Colors.red,
            trimMode: TrimMode.Line,
            trimCollapsedText: 'More',
            trimExpandedText: 'Less',
            style: Theme.of(context).textTheme.lightBodyLargeRegular,
          ),
        ),
      ],
    );
  }
}
