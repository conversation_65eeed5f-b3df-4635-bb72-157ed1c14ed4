part of 'request_detail_bloc.dart';

class RequestDetailState extends Equatable {
  final ApiResponse<BuyerRequest> reqDetails;

  const RequestDetailState({
    this.reqDetails = const ApiResponse.loading(),
  });

  factory RequestDetailState.initial() {
    return const RequestDetailState(
      reqDetails: ApiResponse.loading(),
    );
  }

  RequestDetailState copyWith({
    ApiResponse<BuyerRequest>? reqDetails,
  }) {
    return RequestDetailState(
      reqDetails: reqDetails ?? this.reqDetails,
    );
  }

  @override
  List<Object?> get props => [reqDetails];
}
