import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/buyer_requests.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/buyer_request_repository.dart';
part 'request_detail_event.dart';
part 'request_detail_state.dart';

class RequestDetailBloc extends Bloc<RequestDetailEvent, RequestDetailState> {
  final BuyerRequestRepository _buyerRequestRepository;

  RequestDetailBloc(this._buyerRequestRepository)
      : super(RequestDetailState.initial()) {
    on<GetRequestDetailEvent>(_onGetRequestDetailEvent);
  }

  Future<void> _onGetRequestDetailEvent(
    GetRequestDetailEvent event,
    Emitter<RequestDetailState> emit,
  ) async {
    try {
      emit(state.copyWith(reqDetails: const ApiResponse.loading()));
      final service = await _buyerRequestRepository.getRequestById(event.reqId);
      emit(state.copyWith(reqDetails: ApiResponse.completed(service)));
    } on NetworkException catch (e) {
      emit(state.copyWith(reqDetails: ApiResponse.error(e.message)));
    }
  }
}
