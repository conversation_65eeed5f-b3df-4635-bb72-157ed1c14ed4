import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class InviteFriends extends StatelessWidget {
  const InviteFriends({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background SVG
        Positioned.fill(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.0),
            child: SvgPicture.asset(
              AppAssets.bannerInviteSvg,
              fit: BoxFit.cover,
            ),
          ),
        ),
        // Content
        Row(
          children: [
            Gap(10.w),
            SvgPicture.asset(
              AppAssets.lionStrongbodyAi,
              width: 190,
              height: 190,
            ),
            Gap(40),
            Expanded(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 25.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('Invite friends for unlock',
                        style: Theme.of(context).textTheme.lightBodyLargeBold),
                    SizedBox(height: 12),
                    Text(
                      'If you connect with 15 people and they agree, you\'ll enjoy free global voice chats for one month.',
                      style: Theme.of(context).textTheme.lightBodySmallRegular,
                    ),
                    Gap(20),
                    ElevatedButton(
                      onPressed: () {
                        // Handle invite friends action
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xFFE53E3E),
                        foregroundColor: Colors.white,
                        padding:
                            EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        elevation: 0,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text('Invite Friends',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumSemiBold
                                  .copyWith(
                                      color: Theme.of(context)
                                          .whitePrimary(context))),
                          SizedBox(width: 8),
                          Icon(
                            Icons.arrow_forward,
                            size: 18,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
