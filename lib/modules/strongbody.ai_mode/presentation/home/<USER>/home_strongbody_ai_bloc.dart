import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/model/blog_model.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/modules/seller/models/seller_profile.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

part 'home_strongbody_ai_event.dart';
part 'home_strongbody_ai_state.dart';

class HomeStrongbodyAiBloc
    extends Bloc<HomeStrongbodyAiEvent, HomeStrongbodyAiState> {
  final HomeStrongbodyAiRepository servicesRepository;
  final GlobalStorage globalStorage;

  HomeStrongbodyAiBloc({
    required this.servicesRepository,
    required this.globalStorage,
  }) : super(HomeStrongbodyAiState()) {
    on<FetchSuggestServiceEvent>(_onFetchServiceEvent);
    on<FetchFeaturedProductEvent>(_onFetchFeaturedProductEvent);
    on<FetchExpertEvent>(_onFetchExpertEvent);
    on<FetchBlogEvent>(_onFetchBlogEvent);
    on<SearchServiceEvent>(_onSearchServiceEvent);
    on<SearchProductEvent>(_onSearchProductEvent);
    on<SearchBlogEvent>(_onSearchBlogEvent);
    on<SearchExpertEvent>(_onSearchExpertEvent);
    on<FilterEvent>(_onFilterEvent);
    on<SetServiceTypeEvent>(_onSetServiceTypeEvent);
    on<SelectServiceCategoryEvent>(_onSelectServiceCategoryEvent);
    on<SelectProductCategoryEvent>(_onSelectProductCategoryEvent);
    on<SelectBlogCategoryEvent>(_onSelectBlogCategoryEvent);
    on<SelectExpertOccupationEvent>(_onSelectExpertOccupationEvent);
    on<SelectBlogSortEvent>(_onSelectBlogSortEvent);
    on<SelectProductSortEvent>(_onSelectProductSortEvent);
    on<SelectProductSortPriceEvent>(_onSelectProductSortPriceEvent);
    on<SelectServiceSortEvent>(_onSelectServiceSortEvent);
    on<SelectServiceByCountryEvent>(_onSelectServiceByCountryEvent);
    on<SelectSortProductByPriceEvent>(_onSelectSortProductByPriceEvent);
    on<SearchCountryEvent>(_onSearchCountryEvent);
    on<HomeStrongbodyAiEventRefresh>(_onRefresh);
  }

  // Các hàm xử lý event
  Future<void> _onFetchServiceEvent(FetchSuggestServiceEvent event,
      Emitter<HomeStrongbodyAiState> emit) async {
    if (event.isLoadMore && (state.isLoadingMore || !state.hasMore)) {
      return;
    }
    try {
      if (event.isLoadMore) {
        emit(state.copyWith(isLoadingMore: true));
      } else {
        // Set loading riêng cho service
        emit(state.copyWith(
            isServiceLoading: true,
            isServiceSuccess: false,
            currentServicePage: 1));
      }
      final typeService = AppConstants.service;
      final currentPage = event.isLoadMore ? state.currentServicePage + 1 : 1;
      final params = {
        'page': currentPage,
        'limit': 10,
        '_t': DateTime.now().millisecondsSinceEpoch,
      };

      final serviceResponse = await servicesRepository.getServiceByQuery(
          typeService,
          resourceType: AppConstants.service,
          param: params);

      if (serviceResponse.status == Status.completed) {
        try {
          List<ServiceModel> newServices = [];
          final dataList = serviceResponse.data?.data?.list;
        // List<ServiceModel> newServices = [];

        try {
          newServices = serviceResponse.data!.data.list.map((e) {
            return ServiceModel.fromJson(e);
          }).toList();
          debugPrint("✅ Successfully parsed ${newServices.length} services");
        } catch (parseError) {
          emit(state.copyWith(
              isServiceLoading: false,
              isLoadingMore: false,
              isServiceSuccess: false,
              errorMessage: "Parse error: $parseError"));
          return;
        }

          if (dataList != null && dataList is List) {
            for (var item in dataList) {
              if (item is Map<String, dynamic>) {
                try {
                  final service = ServiceModel.fromJson(item);
                  newServices.add(service);
                } catch (e) {
                  print('💥 Error parsing service item: $e');
                  print('💥 Item data: $item');
                }
              } else {
                print(
                    '💥 Item is not Map<String, dynamic>: ${item.runtimeType}');
                print('💥 Item value: $item');
              }
            }
          } else {
            print(
                '💥 Data list is null or not a List: ${dataList.runtimeType}');
          }

          List<ServiceModel> allServices;
          if (event.isLoadMore) {
            allServices = [...state.suggestServices, ...newServices];
          } else {
            allServices = newServices;
          }

          final hasMore = newServices.length >= 10;

          emit(state.copyWith(
              isServiceLoading: false,
              isLoadingMore: false,
              isServiceSuccess: true,
              suggestServices: allServices,
              searchServiceResults: allServices,
              hasMore: hasMore,
              filteredCountries: globalStorage.countries,
              currentServicePage: currentPage));
        } catch (e) {
          print('💥 Service parsing error: $e');
          emit(state.copyWith(
              isServiceLoading: false,
              isLoadingMore: false,
              isServiceSuccess: false,
              errorMessage: e.toString()));
        }
      } else {
        debugPrint("❌ Service fetch failed - ${serviceResponse.message}");
        emit(state.copyWith(
            isServiceLoading: false,
            isLoadingMore: false,
            isServiceSuccess: false,
            errorMessage: serviceResponse.message));
      }
    } catch (e) {
      debugPrint("💥 Service fetch exception - ${e.toString()}");
      emit(state.copyWith(
          isServiceLoading: false,
          isLoadingMore: false,
          isServiceSuccess: false,
          errorMessage: e.toString()));
    }
  }

  Future<void> _onFetchFeaturedProductEvent(FetchFeaturedProductEvent event,
      Emitter<HomeStrongbodyAiState> emit) async {
    try {
      if (event.isLoadMore) {
        emit(state.copyWith(isLoadingMore: true));
      } else {
        // Set loading riêng cho product
        emit(state.copyWith(
            isProductLoading: true,
            isProductSuccess: false,
            currentProductPage: 1));
      }
      final typeService = AppConstants.product;
      final currentPage = event.isLoadMore ? state.currentProductPage + 1 : 1;
      final params = {
        'page': currentPage,
        'limit': 10,
        '_t': DateTime.now().millisecondsSinceEpoch, // Cache buster
      };

      final productResponse = await servicesRepository.getServiceByQuery(
        typeService,
        resourceType: AppConstants.product,
        param: params,
      );
      if (productResponse.status == Status.completed) {
        try {
          List<ProductModel> listProduct =
              productResponse.data!.data.list.map((e) {
            try {
              return ProductModel.fromJson(e);
            } catch (parseError) {
              debugPrint("🔍 Product JSON that failed: ${e.toString()}");
              rethrow;
            }
          }).toList();

          List<ProductModel> allProducts;
          if (event.isLoadMore) {
            allProducts = [...state.featureProducts, ...listProduct];
          } else {
            allProducts = listProduct;
          }

          emit(state.copyWith(
              isLoading: event.isLoadMore ? state.isLoading : false,
              isLoadingMore: false,
              isProductLoading: false,
              isProductSuccess: true,
              featureProducts: allProducts,
              hasMore: listProduct.length >= 10,
              searchProductsResults: allProducts,
              currentProductPage: currentPage));
        } catch (parseError) {
          debugPrint("💥 Product parse error - ${parseError.toString()}");
          emit(state.copyWith(
              isLoading: false,
              isProductLoading: false,
              isProductSuccess: false,
              hasMore: false,
              isLoadingMore: false,
              errorMessage: "Parse error: $parseError"));
        }
      } else {
        debugPrint("❌ Product fetch failed - ${productResponse.message}");
        emit(state.copyWith(
            isLoading: false,
            isProductLoading: false,
            isProductSuccess: false,
            errorMessage: productResponse.message));
      }
    } on NetworkException catch (e) {
      debugPrint("🌐 Product network exception - ${e.message}");
      emit(state.copyWith(
          isLoading: false,
          isProductLoading: false,
          isProductSuccess: false,
          errorMessage: e.message));
    } catch (e) {
      debugPrint("💥 Product fetch exception - ${e.toString()}");
      emit(state.copyWith(
          isLoading: false,
          isProductLoading: false,
          isProductSuccess: false,
          errorMessage: e.toString()));
    }
  }

  Future<void> _onFetchExpertEvent(
      FetchExpertEvent event, Emitter<HomeStrongbodyAiState> emit) async {
    try {
      if (event.isLoadMore) {
        emit(state.copyWith(isLoadingMore: true));
      } else {
        // Set loading riêng cho expert
        emit(state.copyWith(
            isExpertLoading: true,
            isExpertSuccess: false,
            currentExpertPage: 1));
      }
      final typeService = AppConstants.expert;
      final currentPage = event.isLoadMore ? state.currentExpertPage + 1 : 1;
      final params = {
        'page': currentPage,
        'limit': 10,
        '_t': DateTime.now().millisecondsSinceEpoch, // Cache buster
      };

      debugPrint("📡 Making expert API call - page: $currentPage");
      final expertResponse = await servicesRepository.getServiceByQuery(
          typeService,
          resourceType: AppConstants.expert,
          param: params);

      if (expertResponse.status == Status.completed) {
        List<SellerProfileModel> listSeller = [];
        try {
          final rawList = expertResponse.data!.data.list;

          for (int i = 0; i < rawList.length; i++) {
            try {
              final expertJson = rawList[i];

              // Log the full JSON for debugging

              final seller = SellerProfileModel.fromJson(expertJson);
              listSeller.add(seller);
            } catch (parseError) {
             
              continue;
            }
          }

          
        } catch (parseError) {
          
          emit(state.copyWith(
              isLoading: false,
              isExpertLoading: false,
              isExpertSuccess: false,
              errorMessage: "Expert parse error: $parseError"));
          return;
        }

        List<SellerProfileModel> allExperts;
        if (event.isLoadMore) {
          allExperts = [...state.seller, ...listSeller];
        } else {
          allExperts = listSeller;
        }

        emit(state.copyWith(
            isLoading: event.isLoadMore ? state.isLoading : false,
            isExpertLoading: false,
            isExpertSuccess: true,
            seller: allExperts,
            hasMore: listSeller.length >= 10,
            searchExpertsResults: allExperts,
            currentExpertPage: currentPage));
      } else {
        debugPrint("❌ Expert fetch failed - ${expertResponse.message}");
        emit(state.copyWith(
            isLoading: false,
            isExpertLoading: false,
            isExpertSuccess: false,
            errorMessage: expertResponse.message));
      }
    } on NetworkException catch (e) {
      debugPrint("🌐 Expert network exception - ${e.message}");
      emit(state.copyWith(
          isLoading: false,
          isExpertLoading: false,
          isExpertSuccess: false,
          errorMessage: e.message));
    } catch (e) {
      debugPrint("💥 Expert fetch exception - ${e.toString()}");
      emit(state.copyWith(
          isLoading: false,
          isExpertLoading: false,
          isExpertSuccess: false,
          errorMessage: e.toString()));
    }
  }

  Future<void> _onFetchBlogEvent(
      FetchBlogEvent event, Emitter<HomeStrongbodyAiState> emit) async {
    if (event.isLoadMore) {
      emit(state.copyWith(isLoadingMore: true));
    } else {
      // Set loading riêng cho blog
      emit(state.copyWith(
          isBlogLoading: true, isBlogSuccess: false, currentBlogPage: 1));
    }
    try {
      final typeService = AppConstants.post;
      final currentPage = event.isLoadMore ? state.currentBlogPage + 1 : 1;
      final params = {
        'page': currentPage,
        'limit': 10,
        '_t': DateTime.now().millisecondsSinceEpoch,
      };

      debugPrint("📡 Making blog API call - page: $currentPage");
      final blogResponse = await servicesRepository.getServiceByQuery(
        typeService,
        resourceType: AppConstants.post,
        param: params,
      );

      if (blogResponse.status == Status.completed) {
        List<BlogModel> listBlog = blogResponse.data!.data.list
            .map((e) => BlogModel.fromJson(e))
            .toList();
        List<BlogModel> allBlogs;
        if (event.isLoadMore) {
          allBlogs = [...state.blogs, ...listBlog];
        } else {
          allBlogs = listBlog;
        }

        emit(state.copyWith(
            isLoading: event.isLoadMore ? state.isLoading : false,
            isBlogLoading: false,
            isBlogSuccess: true,
            blogs: allBlogs,
            hasMore: allBlogs.length >= 10,
            searchBlogsResults: allBlogs,
            currentBlogPage: currentPage));
      } else {
        debugPrint("❌ Blog fetch failed - ${blogResponse.message}");
        emit(state.copyWith(
            isLoading: false,
            isBlogLoading: false,
            isBlogSuccess: false,
            errorMessage: blogResponse.message));
      }
    } on NetworkException catch (e) {
      debugPrint("🌐 Blog network exception - ${e.message}");
      emit(state.copyWith(
          isLoading: false,
          isBlogLoading: false,
          isBlogSuccess: false,
          errorMessage: e.message));
    } catch (e) {
      debugPrint("💥 Blog fetch exception - ${e.toString()}");
      emit(state.copyWith(
          isLoading: false,
          isBlogLoading: false,
          isBlogSuccess: false,
          errorMessage: e.toString()));
    }
  }

  void _onSearchServiceEvent(
      SearchServiceEvent event, Emitter<HomeStrongbodyAiState> emit) async {
    final query = event.query.toLowerCase().trim();

    if (query.isEmpty) {
      emit(state.copyWith(
        searchServiceResults: state.suggestServices,
        searchQuery: query,
        isLoading: false,
        hasMore: state.suggestServices.length >= 10,
      ));
    } else {
      final filteredServices = state.suggestServices.where((service) {
        return service.title.toLowerCase().contains(query);
      }).toList();

      emit(state.copyWith(
        searchServiceResults: filteredServices,
        searchQuery: query,
        isLoading: false,
        hasMore: false,
      ));
    }
  }

  void _onSearchProductEvent(
      SearchProductEvent event, Emitter<HomeStrongbodyAiState> emit) async {
    final query = event.query.toLowerCase().trim();

    if (query.isEmpty) {
      emit(state.copyWith(
        isLoading: false,
        searchProductsResults: state.featureProducts,
        searchQuery: query,
        hasMore: state.featureProducts.length >= 10,
      ));
    } else {
      final filteredProducts = state.featureProducts.where((product) {
        return product.name.toLowerCase().contains(query);
      }).toList();

      emit(state.copyWith(
        isLoading: false,
        searchProductsResults: filteredProducts,
        searchQuery: query,
        hasMore: false,
      ));
    }
  }

  void _onSearchBlogEvent(
      SearchBlogEvent event, Emitter<HomeStrongbodyAiState> emit) async {
    final query = event.query.toLowerCase().trim();

    if (query.isEmpty) {
      emit(state.copyWith(
        isLoading: false,
        searchBlogsResults: state.blogs,
        hasMore: state.blogs.length >= 10,
      ));
    } else {
      final filteredBlogs = state.blogs.where((blog) {
        return blog.title.toLowerCase().contains(query);
      }).toList();
      emit(state.copyWith(
        isLoading: false,
        searchBlogsResults: filteredBlogs,
        hasMore: false,
      ));
    }
  }

  void _onSearchExpertEvent(
      SearchExpertEvent event, Emitter<HomeStrongbodyAiState> emit) async {
    final query = event.query.toLowerCase().trim();

    if (query.isEmpty) {
      emit(state.copyWith(
        isLoading: false,
        searchExpertsResults: [],
        searchQuery: '',
        hasMore: false,
      ));
    } else {
      final filteredExperts = state.seller.where((expert) {
        return expert.user.displayName.toLowerCase().contains(query);
      }).toList();
      emit(state.copyWith(
        isLoading: false,
        searchExpertsResults: filteredExperts,
        searchQuery: query,
        hasMore: false,
      ));
    }
  }

  void _onFilterEvent(FilterEvent event, Emitter<HomeStrongbodyAiState> emit) {
    switch (event.filterType) {
      case FilterType.service:
        _handleServiceFilter(event, emit);
        break;
      case FilterType.product:
        _handleProductFilter(event, emit);
        break;
      case FilterType.blog:
        _handleBlogFilter(event, emit);
        break;
      case FilterType.expert:
        _handleExpertFilter(event, emit);
        break;
    }
  }

  void _handleExpertFilter(
      FilterEvent event, Emitter<HomeStrongbodyAiState> emit) {
    if (event.typeReset == 'reset') {
      emit(state.copyWith(
        searchExpertsResults: state.seller,
        isLoading: false,
        searchQuery: '',
        selectedExpertsOccupations: [],
        hasMore: state.blogs.length >= 10,
        selectedServiceByCountryId: 0,
      ));
      return;
    } else if (event.typeReset == 'apply') {
      final filteredExperts = state.seller.where((expert) {
        bool matchesCountry = true;
        bool matchesOccupation = true;

        if (event.countryId != null && event.countryId! > 0) {
          matchesCountry = expert.user.countryId == event.countryId;
        }

        if (event.occupations != null && event.occupations!.isNotEmpty) {
          matchesOccupation = event.occupations!.any((selectedOccupation) {
            return expert.profession.toLowerCase() ==
                selectedOccupation.toLowerCase();
          });
        }

        return matchesCountry && matchesOccupation;
      }).toList();
      debugPrint("Filtered experts count: ${filteredExperts}");
      emit(state.copyWith(
        searchExpertsResults: filteredExperts,
        isLoading: false,
        hasMore: false,
      ));
    }
  }

  void _handleServiceFilter(
      FilterEvent event, Emitter<HomeStrongbodyAiState> emit) {
    if (event.typeReset == 'reset') {
      emit(state.copyWith(
        searchServiceResults: state.suggestServices,
        selectedCategoryIds: [],
        searchQuery: '',
        isLoading: false,
        hasMore: state.blogs.length >= 10,
        selectedServiceByCountryId: 0,
        selectedSortServiceIndex: 0,
      ));
      return;
    } else if (event.typeReset == 'apply') {
      var filteredServices = state.searchServiceResults.where((service) {
        bool matchesServiceType = true;
        bool matchesPrice = true;
        bool matchesCategory = true;

        if (event.serviceType != null && event.serviceType!.isNotEmpty) {
          String dbServiceType = event.serviceType!.toLowerCase();
          matchesServiceType =
              service.type.toLowerCase() == dbServiceType.toLowerCase();
        }

        if (event.price != null && event.price! > 0) {
          matchesPrice = service.price <= event.price!;
        }

        if (event.categories != null && event.categories!.isNotEmpty) {
          matchesCategory = event.categories!.any((selectedCategory) {
            return service.category != null &&
                service.category!.name.toLowerCase() ==
                    selectedCategory.toLowerCase();
          });
        }

        return matchesServiceType && matchesPrice && matchesCategory;
      }).toList();

      switch (state.selectedSortServiceIndex) {
        case 0: // Sort by name ascending
          filteredServices.sort((a, b) => a.price.compareTo(b.price));
          break;
        case 1: // Giá từ cao xuống thấp
          filteredServices.sort((a, b) => b.price.compareTo(a.price));
          break;
        case 2: // Giá từ thấp lên cao
          filteredServices.sort((a, b) => a.price.compareTo(b.price));
          break;
        default:
          break;
      }

      if (state.selectedServiceByCountryId != null) {
        filteredServices = filteredServices
            .where((service) =>
                service.user != null &&
                service.user!.countryId != null &&
                service.user!.countryId == state.selectedServiceByCountryId)
            .toList();
      }

      emit(state.copyWith(
        searchServiceResults: filteredServices,
        isLoading: false,
        hasMore: false,
      ));
    }
  }

  void _handleProductFilter(
      FilterEvent event, Emitter<HomeStrongbodyAiState> emit) {
    if (event.typeReset == 'reset') {
      emit(state.copyWith(
        searchProductsResults: state.featureProducts,
        isLoading: false,
        searchQuery: '',
        selectedCategoryIdProducts: [],
        hasMore: state.blogs.length >= 10,
      ));
      return;
    } else if (event.typeReset == 'apply') {
      final filteredProducts = state.searchProductsResults.where((product) {
        bool matchesPrice = true;
        bool matchesCategory = true;

        // Lọc theo giá tối đa
        if (event.price != null && event.price! > 0) {
          matchesPrice =
              product.pricingTiers.any((tier) => tier.price <= event.price!);
        }

        // Lọc theo danh mục
        if (event.categories != null && event.categories!.isNotEmpty) {
          matchesCategory = event.categories!.any((selectedCategory) {
            return product.category != null &&
                product.category!.name.toLowerCase() ==
                    selectedCategory.toLowerCase();
          });
        }

        return matchesPrice && matchesCategory;
      }).toList();

      // Sắp xếp theo giá trong pricingTiers
      switch (state.selectedSortPriceProduct) {
        case 'min':
          filteredProducts.sort((a, b) {
            final aMinPrice = a.pricingTiers.isNotEmpty
                ? a.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 < v2 ? v1 : v2)
                : double.infinity;

            final bMinPrice = b.pricingTiers.isNotEmpty
                ? b.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 < v2 ? v1 : v2)
                : double.infinity;

            return aMinPrice.compareTo(bMinPrice);
          });
          break;

        case 'max':
          filteredProducts.sort((a, b) {
            final aMaxPrice = a.pricingTiers.isNotEmpty
                ? a.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 > v2 ? v1 : v2)
                : -double.infinity;

            final bMaxPrice = b.pricingTiers.isNotEmpty
                ? b.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 > v2 ? v1 : v2)
                : -double.infinity;

            return bMaxPrice.compareTo(aMaxPrice);
          });
          break;

        default:
          break;
      }

      switch (state.selectedSortProductIndex) {
        case 0: // Sort by name ascending
          break;
        case 1: // Sort by name descending
          filteredProducts.sort((a, b) {
            final aMaxPrice = a.pricingTiers.isNotEmpty
                ? a.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 > v2 ? v1 : v2)
                : -double.infinity;

            final bMaxPrice = b.pricingTiers.isNotEmpty
                ? b.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 > v2 ? v1 : v2)
                : -double.infinity;

            return bMaxPrice.compareTo(aMaxPrice);
          });
        case 2: // Sort by name descending
          filteredProducts.sort((a, b) {
            final aMinPrice = a.pricingTiers.isNotEmpty
                ? a.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 < v2 ? v1 : v2)
                : double.infinity;

            final bMinPrice = b.pricingTiers.isNotEmpty
                ? b.pricingTiers
                    .map((e) => e.price)
                    .reduce((v1, v2) => v1 < v2 ? v1 : v2)
                : double.infinity;

            return aMinPrice.compareTo(bMinPrice);
          });
          break;
        default:
          break;
      }

      emit(state.copyWith(
        searchProductsResults: filteredProducts,
        isLoading: false,
        hasMore: false,
      ));
    }
  }

  void _handleBlogFilter(
      FilterEvent event, Emitter<HomeStrongbodyAiState> emit) {
    if (event.typeReset == 'reset') {
      emit(state.copyWith(
        searchBlogsResults: state.blogs,
        selectedCategoryIdBlogs: [],
        searchQuery: '',
        isLoading: false,
      ));
      return;
    } else if (event.typeReset == 'apply') {
      var filteredBlogs = state.searchBlogsResults.where((blog) {
        if (event.categories != null && event.categories!.isNotEmpty) {
          return event.categories!.any((selectedCategory) {
            return blog.category != null &&
                blog.category!.name.toLowerCase() ==
                    selectedCategory.toLowerCase();
          });
        }
        return true;
      }).toList();

      switch (state.selectedSortIndexBlogs) {
        case 0:
          filteredBlogs.sort((a, b) {
            if (a.createdAt != null && b.createdAt != null) {
              return a.createdAt!.compareTo(b.createdAt!);
            }
            return 0;
          });
          break;
        case 1:
          print("Sorting blogs by date descending");
          filteredBlogs.sort((a, b) {
            if (a.createdAt != null && b.createdAt != null) {
              return b.createdAt!.compareTo(a.createdAt!);
            }
            return 0;
          });
          break;
        default:
          break;
      }
      emit(state.copyWith(
        searchBlogsResults: filteredBlogs,
        isLoading: false,
        hasMore: false,
      ));
    }
  }

  void _onSetServiceTypeEvent(
      SetServiceTypeEvent event, Emitter<HomeStrongbodyAiState> emit) {
    emit(state.copyWith(
      serviceTypeIndex: event.serviceTypeIndex,
    ));
  }

  void _onSelectServiceCategoryEvent(
      SelectServiceCategoryEvent event, Emitter<HomeStrongbodyAiState> emit) {
    List<int> updatedCategoryIds = List.from(state.selectedCategoryIds);

    if (event.isSelected) {
      if (!updatedCategoryIds.contains(event.categoryId)) {
        updatedCategoryIds.add(event.categoryId);
      }
    } else {
      updatedCategoryIds.remove(event.categoryId);
    }

    emit(state.copyWith(
      selectedCategoryIds: updatedCategoryIds,
    ));
  }

  void _onSelectProductCategoryEvent(
      SelectProductCategoryEvent event, Emitter<HomeStrongbodyAiState> emit) {
    List<int> updatedCategoryIds = List.from(state.selectedCategoryIdProducts);

    if (event.isSelected) {
      if (!updatedCategoryIds.contains(event.categoryId)) {
        updatedCategoryIds.add(event.categoryId);
      }
    } else {
      updatedCategoryIds.remove(event.categoryId);
    }

    emit(state.copyWith(
      selectedCategoryIdProducts: updatedCategoryIds,
    ));
  }

  void _onSelectBlogCategoryEvent(
      SelectBlogCategoryEvent event, Emitter<HomeStrongbodyAiState> emit) {
    List<int> updatedCategories = List.from(state.selectedCategoryIdBlogs);

    if (event.isSelected) {
      if (!updatedCategories.contains(event.categoryId)) {
        updatedCategories.add(event.categoryId);
      }
    } else {
      updatedCategories.remove(event.categoryId);
    }

    emit(state.copyWith(
      selectedCategoryIdBlogs: updatedCategories,
    ));
  }

  void _onSelectExpertOccupationEvent(
      SelectExpertOccupationEvent event, Emitter<HomeStrongbodyAiState> emit) {
    List<int> updatedOccupations = List.from(state.selectedExpertsOccupations);

    if (event.isSelected) {
      if (!updatedOccupations.contains(event.occupationId)) {
        updatedOccupations.add(event.occupationId);
      }
    } else {
      updatedOccupations.remove(event.occupationId);
    }

    emit(state.copyWith(
      selectedExpertsOccupations: updatedOccupations,
    ));
  }

  void _onSelectBlogSortEvent(
      SelectBlogSortEvent event, Emitter<HomeStrongbodyAiState> emit) {
    emit(state.copyWith(
      selectedSortIndexBlogs: event.sortByIndex,
    ));
  }

  void _onSelectProductSortEvent(
      SelectProductSortEvent event, Emitter<HomeStrongbodyAiState> emit) {
    emit(state.copyWith(
      selectedSortIndexProduct: event.sortIndex,
    ));
  }

  void _onSelectProductSortPriceEvent(
      SelectProductSortPriceEvent event, Emitter<HomeStrongbodyAiState> emit) {
    emit(state.copyWith(
      selectedSortPriceProduct: event.sortPrice,
    ));
  }

  void _onSelectServiceSortEvent(
      SelectServiceSortEvent event, Emitter<HomeStrongbodyAiState> emit) {
    emit(state.copyWith(
      selectedSortServiceIndex: event.sortIndex,
    ));
  }

  void _onSelectServiceByCountryEvent(
      SelectServiceByCountryEvent event, Emitter<HomeStrongbodyAiState> emit) {
    debugPrint("Selected country ID: ${event.countryId}");
    emit(state.copyWith(
      selectedServiceByCountryId: event.countryId,
    ));
  }

  void _onSelectSortProductByPriceEvent(SelectSortProductByPriceEvent event,
      Emitter<HomeStrongbodyAiState> emit) {
    emit(state.copyWith(
      selectedSortProductIndex: event.sortPrice,
    ));
  }

  void _onSearchCountryEvent(
      SearchCountryEvent event, Emitter<HomeStrongbodyAiState> emit) {
    if (event.query.isEmpty) {
      emit(state.copyWith(
        filteredCountries: globalStorage.countries,
      ));
      return;
    } else {
      final filteredCountries = globalStorage.countries!
          .where((country) =>
              country.title!.toLowerCase().contains(event.query.toLowerCase()))
          .toList();

      emit(state.copyWith(
        filteredCountries: filteredCountries,
      ));
    }
  }

  Future<void> _onRefresh(HomeStrongbodyAiEventRefresh event,
      Emitter<HomeStrongbodyAiState> emit) async {
    emit(state.copyWith(
      isServiceLoading: true,
      isProductLoading: true,
      isExpertLoading: true,
      isBlogLoading: true,
      isServiceSuccess: false,
      isProductSuccess: false,
      isExpertSuccess: false,
      isBlogSuccess: false,
      seller: [],
      featureProducts: [],
      suggestServices: [],
      blogs: [],
    ));
    try {
      await _onFetchServiceEvent(
          FetchSuggestServiceEvent(isLoadMore: false), emit);

      await _onFetchFeaturedProductEvent(
          FetchFeaturedProductEvent(isLoadMore: false), emit);

      await _onFetchExpertEvent(FetchExpertEvent(isLoadMore: false), emit);

      await _onFetchBlogEvent(FetchBlogEvent(isLoadMore: false), emit);

      // Final state update
      emit(state.copyWith(
        isLoading: false,
        isSuccess: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        isSuccess: false,
        errorMessage: 'Refresh failed: ${e.toString()}',
      ));
    }
  }
}
