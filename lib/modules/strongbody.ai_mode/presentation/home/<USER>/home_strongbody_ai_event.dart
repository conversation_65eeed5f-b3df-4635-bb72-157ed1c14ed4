part of 'home_strongbody_ai_bloc.dart';

class HomeStrongbodyAiEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class FetchSuggestServiceEvent extends HomeStrongbodyAiEvent {
  final bool isLoadMore;
  FetchSuggestServiceEvent({this.isLoadMore = false});

  @override
  List<Object?> get props => [isLoadMore];
}

class FetchFeaturedProductEvent extends HomeStrongbodyAiEvent {
  final bool isLoadMore;
  FetchFeaturedProductEvent({this.isLoadMore = false});

  @override
  List<Object?> get props => [isLoadMore];
}

class FetchExpertEvent extends HomeStrongbodyAiEvent {
  final bool isLoadMore;
  FetchExpertEvent({this.isLoadMore = false});

  @override
  List<Object?> get props => [isLoadMore];
}

class FetchBlogEvent extends HomeStrongbodyAiEvent {
  final bool isLoadMore;
  FetchBlogEvent({this.isLoadMore = false});

  @override
  List<Object?> get props => [isLoadMore];
}

class SearchServiceEvent extends HomeStrongbodyAiEvent {
  final String query;
  SearchServiceEvent({required this.query});

  @override
  List<Object?> get props => [query];
}

class SearchProductEvent extends HomeStrongbodyAiEvent {
  final String query;
  SearchProductEvent({required this.query});

  @override
  List<Object?> get props => [query];
}

class SearchBlogEvent extends HomeStrongbodyAiEvent {
  final String query;
  SearchBlogEvent({required this.query});

  @override
  List<Object?> get props => [query];
}

class SearchExpertEvent extends HomeStrongbodyAiEvent {
  final String query;
  SearchExpertEvent({required this.query});

  @override
  List<Object?> get props => [query];
}

class SetServiceTypeEvent extends HomeStrongbodyAiEvent {
  final int serviceTypeIndex;
  SetServiceTypeEvent({required this.serviceTypeIndex});

  @override
  List<Object?> get props => [serviceTypeIndex];
}

enum FilterType { service, product, blog, expert }

class FilterEvent extends HomeStrongbodyAiEvent {
  final FilterType filterType;
  final int? countryId;
  final String? serviceType;
  final int? price;
  final String? typeReset;
  final List<String>? categories;
  final List<String>? occupations;
  final List<int>? sortBy;

  FilterEvent({
    required this.filterType,
    this.countryId,
    this.serviceType,
    this.price,
    this.typeReset,
    this.categories,
    this.occupations,
    this.sortBy,
  });

  @override
  List<Object?> get props => [
        filterType,
        countryId,
        serviceType,
        price,
        typeReset,
        categories,
        occupations,
        sortBy,
      ];
}

class SelectServiceCategoryEvent extends HomeStrongbodyAiEvent {
  final int categoryId;
  final bool isSelected;

  SelectServiceCategoryEvent({
    required this.categoryId,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [categoryId, isSelected];
}

class SelectProductCategoryEvent extends HomeStrongbodyAiEvent {
  final int categoryId;
  final bool isSelected;

  SelectProductCategoryEvent({
    required this.categoryId,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [categoryId, isSelected];
}

class SelectBlogCategoryEvent extends HomeStrongbodyAiEvent {
  final int categoryId;
  final bool isSelected;

  SelectBlogCategoryEvent({
    required this.categoryId,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [categoryId, isSelected];
}

class SelectExpertCategoryEvent extends HomeStrongbodyAiEvent {
  final int categoryId;
  final bool isSelected;

  SelectExpertCategoryEvent({
    required this.categoryId,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [categoryId, isSelected];
}

class SelectExpertOccupationEvent extends HomeStrongbodyAiEvent {
  final int occupationId;
  final bool isSelected;

  SelectExpertOccupationEvent({
    required this.occupationId,
    required this.isSelected,
  });

  @override
  List<Object?> get props => [occupationId, isSelected];
}

class SelectBlogSortEvent extends HomeStrongbodyAiEvent {
  final int sortByIndex;

  SelectBlogSortEvent({
    required this.sortByIndex,
  });

  @override
  List<Object?> get props => [sortByIndex];
}

class SelectProductSortEvent extends HomeStrongbodyAiEvent {
  final int sortIndex;

  SelectProductSortEvent({
    required this.sortIndex,
  });

  @override
  List<Object?> get props => [
        sortIndex,
      ];
}

class SelectProductSortPriceEvent extends HomeStrongbodyAiEvent {
  final String sortPrice;

  SelectProductSortPriceEvent({
    required this.sortPrice,
  });

  @override
  List<Object?> get props => [
        sortPrice,
      ];
}

class SelectServiceSortEvent extends HomeStrongbodyAiEvent {
  final int sortIndex;

  SelectServiceSortEvent({
    required this.sortIndex,
  });

  @override
  List<Object?> get props => [
        sortIndex,
      ];
}

class SelectServiceByCountryEvent extends HomeStrongbodyAiEvent {
  final int countryId;

  SelectServiceByCountryEvent({
    required this.countryId,
  });

  @override
  List<Object?> get props => [
        countryId,
      ];
}

class SelectSortProductByPriceEvent extends HomeStrongbodyAiEvent {
  final int sortPrice;

  SelectSortProductByPriceEvent({
    required this.sortPrice,
  });

  @override
  List<Object?> get props => [
        sortPrice,
      ];
}

class SearchCountryEvent extends HomeStrongbodyAiEvent {
  final String query;
  SearchCountryEvent(this.query);
  @override
  List<Object?> get props => [query];
}

class HomeStrongbodyAiEventRefresh extends HomeStrongbodyAiEvent {
  HomeStrongbodyAiEventRefresh();

  @override
  List<Object?> get props => [];
}
