import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../app/routers/routers_name.dart';

class ShortcutMethod extends StatelessWidget {
  final VoidCallback onTapService;
  final VoidCallback onTapProduct;
  const ShortcutMethod({
    super.key,
    required this.onTapService,
    required this.onTapProduct,
  });

  @override
  Widget build(BuildContext context) {
    Widget buildShortcut(String icon, String label, VoidCallback onTap,
        {Color? color}) {
      return GestureDetector(
        onTap: onTap,
        child: Container(
          width: 105.w,
          height: 70.w,
          margin: EdgeInsets.only(left: AppSpacing.padding6h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).greyScale300(context),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                width: 25.h,
                height: 25.h,
                fit: BoxFit.contain,
                icon,
                // ignore: deprecated_member_use
                color: color,
              ),
              Gap(5),
              Text(label,
                  style: Theme.of(context).textTheme.lightBodySmallRegular),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          buildShortcut(AppAssets.globalSvg, 'Services', () {
            onTapService();
          }, color: Theme.of(context).informationBase(context)),
          buildShortcut(AppAssets.noteFavoriteSvg, 'Post Request',
              () => context.push(RouteName.postBuyerRequest),
              color: Theme.of(context).primary(context)),
          buildShortcut(AppAssets.box1, 'Products', () {
            onTapProduct();
          }, color: Theme.of(context).successBase(context)),
        ],
      ),
    );
  }
}
