import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/bottom_filter_drawer.dart';

class FilterDrawerBlog extends StatefulWidget {
  const FilterDrawerBlog({super.key});

  @override
  State<FilterDrawerBlog> createState() => _FilterDrawerBlogState();
}

class _FilterDrawerBlogState extends State<FilterDrawerBlog> {
  // Service Type selections
  int? selectedCountryId;
  final controllerServiceType = TextEditingController();
  final controllerCountry = TextEditingController();
  final controllerPrice = TextEditingController();

  bool showMoreCategories = false;

  final List<String> categories = [
    'Preventive Care',
    'Health Conditions',
    'Neurology',
    'Renal Diet Management',
    'Orthopedic Trauma Surgery',
  ];
  final List<String> sortBy = [
    'Featured',
    'Newest First',
  ];

  final globalStorage = getIt<GlobalStorage>();
  late List<Country> countries;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
      builder: (context, state) {
        return Drawer(
            width: MediaQuery.of(context).size.width,
            backgroundColor: Colors.white,
            child: SafeArea(
              child: Scaffold(
                  bottomNavigationBar: BottomFilterDrawer(
                    categories: categories,
                    state: state,
                    onReset: () {
                      context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                            filterType: FilterType.blog,
                            typeReset: 'reset',
                          ));
                      context.pop();
                      // Clear all filters
                    },
                    onApply: () {
                      List<String> selectedCategories = [];
                      for (int categoryId in state.selectedCategoryIdBlogs) {
                        if (categoryId >= 0 && categoryId < categories.length) {
                          selectedCategories.add(categories[categoryId]);
                        }
                      }
                      // List<String> selectedSortBy = [];
                      // for (int sortIndex in state.selectedSortIndexBlogs) {
                      //   if (sortIndex >= 0 && sortIndex < sortBy.length) {
                      //     selectedSortBy.add(sortBy[sortIndex]);
                      //   }
                      // }

                      context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                          filterType: FilterType.blog,
                          categories: selectedCategories,
                          // sortBy: selectedSortBy,
                          typeReset: 'apply'));
                      context.pop();
                    },
                  ),
                  body: Column(children: [
                    // Header
                    Container(
                      height: 60.h,
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                          bottom: BorderSide(
                            color: Theme.of(context).greyScale100(context),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.close,
                              size: 24.sp,
                              color: Theme.of(context).greyScale900(context),
                            ),
                          ),
                          Expanded(
                            child: Text(
                              'Filter',
                              textAlign: TextAlign.center,
                              style: Theme.of(context)
                                  .textTheme
                                  .lightHeadingMedium,
                            ),
                          ),
                          SizedBox(width: 40.w), // Balance the close button
                        ],
                      ),
                    ),

                    // Content
                    Expanded(
                      child: Container(
                        color: Colors.white,
                        child: ListView(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          children: [
                            Gap(24.h),

                            // Category Section
                            _buildSectionTitle('Category'),

                            Gap(16.h),

                            _buildCheckboxCategory(
                              categories,
                              (value, isSelected) {
                                // Xử lý việc chọn category
                                context.read<HomeStrongbodyAiBloc>().add(
                                      SelectBlogCategoryEvent(
                                        categoryId: value,
                                        isSelected: isSelected,
                                      ),
                                    );
                              },
                              state.selectedCategoryIdBlogs,
                            ),

                            // Show more button
                            Gap(8.h),
                            Align(
                              alignment: Alignment.center,
                              child: GestureDetector(
                                onTap: () {
                                  setState(() =>
                                      showMoreCategories = !showMoreCategories);
                                },
                                child: Row(
                                  children: [
                                    Icon(
                                      showMoreCategories
                                          ? Icons.keyboard_arrow_up
                                          : Icons.keyboard_arrow_down,
                                      color: Theme.of(context)
                                          .greyScale500(context),
                                      size: 20.sp,
                                    ),
                                    Gap(8.w),
                                    Text(
                                      showMoreCategories
                                          ? 'Show less'
                                          : 'Show more',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumRegular
                                          .copyWith(
                                            color: Theme.of(context)
                                                .greyScale500(context),
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Gap(16.h),
                            _buildSectionTitle('Sort by'),
                            Gap(16.h),
                            _buildCheckboxSort(
                              sortBy,
                              (value, isSelected) {
                                context.read<HomeStrongbodyAiBloc>().add(
                                      SelectBlogSortEvent(
                                        sortByIndex: value,
                                      ),
                                    );
                              },
                              state.selectedSortIndexBlogs,
                            ),
                          ],
                        ),
                      ),
                    )
                  ])),
            ));
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
            color: Theme.of(context).greyScale600(context),
          ),
    );
  }

  Widget _buildCheckboxTile(List<String> serviceTypes,
      ValueChanged<int> onChanged, int selectedIndex) {
    return Column(
      children: serviceTypes.asMap().entries.map((entry) {
        int index = entry.key;
        String type = entry.value;

        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: selectedIndex == index,
                  onChanged: (bool? value) {
                    if (value == true) {
                      onChanged(index);
                      controllerServiceType.text = type;
                    }
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: selectedIndex == index
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  type,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCheckboxCategory(List<String> categories,
      Function(int, bool) onChanged, List<int> selectedCategoryIds) {
    return Column(
      children: categories.asMap().entries.map((entry) {
        int index = entry.key;
        String category = entry.value;
        bool isSelected = selectedCategoryIds.contains(index);

        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (bool? value) {
                    onChanged(index, value ?? false);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  category,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCheckboxSort(List<String> categories,
      Function(int, bool) onChanged, int selectedIndex) {
    return Column(
      children: categories.asMap().entries.map((entry) {
        int index = entry.key;
        String category = entry.value;
        bool isSelected = selectedIndex == index;
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (bool? value) {
                    onChanged(index, value ?? false);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  category,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
