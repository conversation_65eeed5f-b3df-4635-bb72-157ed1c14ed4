import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/validation_utils.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/bottom_filter_drawer.dart';

class FilterDrawerExpert extends StatefulWidget {
  const FilterDrawerExpert({super.key});

  @override
  State<FilterDrawerExpert> createState() => _FilterDrawerExpertState();
}

class _FilterDrawerExpertState extends State<FilterDrawerExpert> {
  // Service Type selections
  int? selectedCountryId;
  final controllerServiceType = TextEditingController();
  final controllerPrice = TextEditingController();
  final _countryController = TextEditingController();

  bool showMoreCategories = false;

  final List<String> occupations = [
    'Dentist',
    'Pharmacist',
    'Surgeon',
    'Physiotherapist',
    'Medical Laboratory Technician',
  ];

  late List<Country> countries;

  @override
  void initState() {
    super.initState();
    countries = gs.countries!;
  }

  // Price range
  TextEditingController minPriceController = TextEditingController();
  TextEditingController maxPriceController = TextEditingController();

  // Country

  @override
  void dispose() {
    minPriceController.dispose();
    maxPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
      builder: (context, state) {
        return Drawer(
            width: MediaQuery.of(context).size.width,
            backgroundColor: Colors.white,
            child: SafeArea(
                child: Scaffold(
              bottomNavigationBar: BottomFilterDrawer(
                categories: occupations,
                state: state,
                onReset: () {
                  context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                        filterType: FilterType.expert,
                        typeReset: 'reset',
                      ));
                  context.pop();
                  // Clear all filters
                },
                onApply: () {
                  List<String> selectedOccupations = [];
                  for (int categoryId in state.selectedExpertsOccupations) {
                    if (categoryId >= 0 && categoryId < occupations.length) {
                      selectedOccupations.add(occupations[categoryId]);
                    }
                  }
                  debugPrint(
                      "Applying filter with occupations: $selectedOccupations, countryId: ${state.selectedServiceByCountryId}");

                  context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                      filterType: FilterType.expert,
                      occupations: selectedOccupations,
                      countryId: state.selectedServiceByCountryId,
                      typeReset: 'apply'));
                  context.pop();
                },
              ),
              body: Column(children: [
                // Header
                Container(
                  height: 60.h,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).greyScale100(context),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.close,
                          size: 24.sp,
                          color: Theme.of(context).greyScale900(context),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Filter',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.lightHeadingMedium,
                        ),
                      ),
                      SizedBox(width: 40.w),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: Container(
                    color: Colors.white,
                    child: ListView(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      children: [
                        Gap(24.h),

                        // Category Section
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildSectionTitle('Occupation'),
                            Icon(
                              Icons.chevron_right,
                              color: Theme.of(context).greyScale500(context),
                              size: 20.sp,
                            ),
                          ],
                        ),
                        Gap(16.h),

                        _buildCheckboxCategory(
                          occupations,
                          (value, isSelected) {
                            // Xử lý việc chọn category
                            context.read<HomeStrongbodyAiBloc>().add(
                                  SelectExpertOccupationEvent(
                                    occupationId: value,
                                    isSelected: isSelected,
                                  ),
                                );
                          },
                          state.selectedExpertsOccupations,
                        ),

                        Gap(32.h),
                        _buildSectionTitle('Country'),
                        Gap(16.h),
                        FormField<int>(
                          initialValue: state.selectedServiceByCountryId,
                          validator: (value) =>
                              ValidateForm.validateCountrySelection(value),
                          builder: (FormFieldState<int> field) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Theme(
                                  data: Theme.of(context).copyWith(
                                    dropdownMenuTheme: DropdownMenuThemeData(
                                      textStyle: TextStyle(color: Colors.black),
                                      menuStyle: MenuStyle(
                                        backgroundColor:
                                            WidgetStateProperty.all(
                                                Colors.white),
                                      ),
                                      inputDecorationTheme:
                                          InputDecorationTheme(
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: field.hasError
                                                ? Theme.of(context)
                                                    .colorScheme
                                                    .error
                                                : Theme.of(context)
                                                    .greyScale300(context),
                                            width: 1,
                                          ),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: field.hasError
                                                ? Theme.of(context)
                                                    .colorScheme
                                                    .error
                                                : Theme.of(context)
                                                    .primary(context),
                                            width: 2,
                                          ),
                                        ),
                                        errorBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .error,
                                            width: 1.5,
                                          ),
                                        ),
                                        focusedErrorBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .error,
                                            width: 2,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  child: DropdownMenu<int>(
                                    menuHeight: 200,
                                    width: double.infinity,
                                    controller: _countryController,
                                    dropdownMenuEntries:
                                        state.filteredCountries.map((country) {
                                      return DropdownMenuEntry<int>(
                                        value: country.id!,
                                        label: country.title!,
                                      );
                                    }).toList(),
                                    onSelected: (int? value) {
                                      context.read<HomeStrongbodyAiBloc>().add(
                                          SelectServiceByCountryEvent(
                                              countryId: value!));
                                    },
                                    initialSelection:
                                        state.selectedServiceByCountryId,
                                    label: Text(
                                      LocaleKeys.country.tr(),
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyLargeMedium
                                          .copyWith(
                                              color: field.hasError
                                                  ? Theme.of(context)
                                                      .colorScheme
                                                      .error
                                                  : Theme.of(context)
                                                      .greyScale400(context)),
                                    ),
                                  ),
                                ),
                                if (field.hasError)
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        top: 8.0, left: 12.0),
                                    child: Text(
                                      field.errorText!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .error),
                                    ),
                                  ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                )
              ]),
            )));
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
            color: Theme.of(context).greyScale600(context),
          ),
    );
  }

  Widget _buildCheckboxTile(List<String> serviceTypes,
      ValueChanged<int> onChanged, int selectedIndex) {
    return Column(
      children: serviceTypes.asMap().entries.map((entry) {
        int index = entry.key;
        String type = entry.value;

        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: selectedIndex == index,
                  onChanged: (bool? value) {
                    if (value == true) {
                      onChanged(index);
                      controllerServiceType.text = type;
                    }
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: selectedIndex == index
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  type,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCheckboxCategory(List<String> categories,
      Function(int, bool) onChanged, List<int> selectedCategoryIds) {
    return Column(
      children: categories.asMap().entries.map((entry) {
        int index = entry.key;
        String category = entry.value;
        bool isSelected = selectedCategoryIds.contains(index);

        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (bool? value) {
                    onChanged(index, value ?? false);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  category,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
