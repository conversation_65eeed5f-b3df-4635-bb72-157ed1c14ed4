import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../app/routers/routers_name.dart';

class BannerHome extends StatelessWidget {
  const BannerHome({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.2,
      child: Stack(clipBehavior: Clip.none, fit: StackFit.expand, children: [
        SvgPicture.asset(
          AppAssets.backgroundHomeSvg,
          fit: BoxFit.fill,
        ),
        Positioned(
          left: 0,
          right: 0,
          top: 0,
          child: Column(
            children: [
              Gap(62.h),
              Row(
                children: [
                  // IconButton(
                  //     onPressed: () {
                  //       context.pop();
                  //     },
                  //     icon: SvgPicture.asset(AppAssets.arrowLeftSvg)),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        context.push(RouteName.searchFilterStrongBody);
                      },
                      child: Hero(
                        tag: 'search-box',
                        child: Material(
                          color: Colors.transparent,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            margin: EdgeInsets.symmetric(horizontal: 16.w),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: Theme.of(context).greyScale300(context),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text("Search "),
                                Icon(Icons.search),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        // Positioned(
        //   left: 0,
        //   right: 0,
        //   top: 100.h,
        //   child: ShortcutMethod(),
        // ),
      ]),
    );
  }
}
