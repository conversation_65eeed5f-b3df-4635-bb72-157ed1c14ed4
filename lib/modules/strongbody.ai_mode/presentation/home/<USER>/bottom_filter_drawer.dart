import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';

class BottomFilterDrawer extends StatelessWidget {
  const BottomFilterDrawer({
    super.key,
    required this.categories,
    required this.state,
    this.onReset,
    this.onApply,
  });

  final List<String> categories;
  final HomeStrongbodyAiState state;
  final VoidCallback? onReset;
  final VoidCallback? onApply;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).greyScale100(context),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: onReset,
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: AppSpacing.padding12h),
                side: BorderSide(
                  color: Theme.of(context).secondary(context),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                'Reset',
                style:
                    Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                          color: Theme.of(context).greyScale700(context),
                        ),
              ),
            ),
          ),
          Gap(12.w),
          Expanded(
            child: ElevatedButton(
              onPressed: onApply,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).secondary(context),
                padding: EdgeInsets.symmetric(vertical: AppSpacing.padding12h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                'Apply',
                style:
                    Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                          color: Colors.white,
                        ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
