import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/seller/models/seller_profile.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_exprert.dart';
import 'package:multime_app/shared/widgets/app_loader/loading_card_shimmer.dart';
import 'package:multime_app/shared/widgets/layout/listview_layout.dart';
import '../../../../../core/components/section_icon.dart';

class ListCardHomeExpert extends StatelessWidget {
  const ListCardHomeExpert({
    super.key,
    required this.experts,
    required this.title,
    required this.state,
  });
  final List<SellerProfileModel> experts;
  final String title;
  final HomeStrongbodyAiState state;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
          child: SectionIcon(
              title: title,
              titleStyle: Theme.of(context).textTheme.lightBodyXLargeBold,
              onPressed: () {
                context
                    .push(
                      RouteName.allExperts,
                    )
                    .then(
                      (_) =>
                          context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                                filterType: FilterType.expert,
                                typeReset: 'reset',
                              )),
                    );
              }),
        ),
        state.isExpertLoading && experts.isEmpty
            ? LoadingCardShimmer()
            : state.isExpertSuccess && experts.isNotEmpty
                ? ListviewLayout(
                    scrollDirection: Axis.vertical,
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: experts.length > 3 ? 3 : experts.length,
                    itemBuilder: (context, i) {
                      final expert = experts[i];
                      return CardExpert(expert: expert);
                    },
                  )
                : Center(
                    child: Text('Data not available',
                        style:
                            Theme.of(context).textTheme.lightBodySmallRegular),
                  ),
      ],
    );
  }
}
