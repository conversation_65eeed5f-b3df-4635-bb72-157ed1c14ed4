import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/application/bottom_bar/application_page.dart';
import 'package:multime_app/modules/application/bottom_bar/bottomSheet_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>';
import '../../../../app/routers/routers_name.dart';
import '../../../../core/di/locator.dart';
import '../../../../core/domain/storages/global_storage.dart';

// ignore: depend_on_referenced_packages

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final userRole = getIt<GlobalStorage>().userRole;
    return ApplicationPage(
      page: const HomePageStrongBody(),
      bottomSheetItems: [
        if (userRole == "Seller mobile" || userRole == "Seller")
          BottomSheetItem(
            name: "Post Service",
            iconLead: AppAssets.post_service_icon,
            onTap: () => (context).push(RouteName.createService),
          ),
        BottomSheetItem(
          name: "Post Request",
          iconLead: AppAssets.post_request_icon,
          onTap: () => context.push(RouteName.postBuyerRequest),
        ),
      ],
    );
  }
}
