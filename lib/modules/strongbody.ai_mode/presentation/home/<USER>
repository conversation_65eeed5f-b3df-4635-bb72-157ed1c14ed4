import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/model/blog_model.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_blog.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_feature_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_suggest_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/list_card_home_expert.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/promo_slider.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/shortcut_method.dart';
import 'package:multime_app/shared/widgets/list_card/List_view_generic.dart';

import 'widgets/banner_home.dart';
part 'home_page.dart';
