part of "home_page_import.dart";

class HomePageStrongBody extends StatefulWidget implements PreferredSizeWidget {
  const HomePageStrongBody({super.key});

  @override
  State<HomePageStrongBody> createState() => _HomePageStrongBodyState();

  @override
  Size get preferredSize => const Size.fromHeight(250);
}

class _HomePageStrongBodyState extends State<HomePageStrongBody> {
  bool _showShortcut = true;
  double _lastOffset = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final offset = _scrollController.offset;
    if (offset > _lastOffset + 30) {
      if (_showShortcut) {
        setState(() {
          _showShortcut = false;
        });
      }
    } else if (offset < _lastOffset - 30) {
      // cuộn lên
      if (!_showShortcut) {
        setState(() {
          _showShortcut = true;
        });
      }
    }
    _lastOffset = offset;
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      backgroundColor: Theme.of(context).whitePrimary(context),
      color: Theme.of(context).primaryColor,
      onRefresh: () async {
        context
            .read<HomeStrongbodyAiBloc>()
            .add(HomeStrongbodyAiEventRefresh());
      },
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize:
              Size.fromHeight(MediaQuery.of(context).size.height * 0.1),
          child: BannerHome(),
        ),
        body: BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
          builder: (context, state) {
            final services = state.suggestServices;
            final products = state.featureProducts;
            final experts = state.seller;
            final blogs = state.blogs;

            return NotificationListener<ScrollNotification>(
              onNotification: (_) => false,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Gap(16.h),
                    AnimatedSlide(
                      offset: _showShortcut ? Offset.zero : const Offset(0, -1),
                      duration: const Duration(milliseconds: 300),
                      child: ShortcutMethod(
                        onTapService: () => context
                            .push(
                              RouteName.allSuggestService,
                            )
                            .then(
                              (_) => context.read<HomeStrongbodyAiBloc>().add(
                                  FilterEvent(
                                      filterType: FilterType.service,
                                      typeReset: 'reset')),
                            ),
                        onTapProduct: () => context
                            .push(
                              RouteName.allFeaturedProducts,
                            )
                            .then(
                              (_) => context.read<HomeStrongbodyAiBloc>().add(
                                  FilterEvent(
                                      filterType: FilterType.product,
                                      typeReset: 'reset')),
                            ),
                      ),
                    ),
                    Gap(20.h),
                    ListViewGeneric<ServiceModel>(
                        isLoading: state.isServiceLoading,
                        isSuccess: state.isServiceSuccess,
                        isItemAvailable: state.suggestServices.isNotEmpty,
                        items: services,
                        onItemTap: (service) => context
                            .push(RouteName.serviceDetail, extra: service),
                        showMore: false,
                        title: 'Recently Suggest Services',
                        itemBuilder: (context, service, index) =>
                            CardSuggestService(service: service),
                        isShowButton: true,
                        isListHome: true,
                        filterType: FilterType.service,
                        onPressed: () => context
                            .push(
                              RouteName.allSuggestService,
                            )
                            .then(
                              (_) => context.read<HomeStrongbodyAiBloc>().add(
                                  FilterEvent(
                                      filterType: FilterType.service,
                                      typeReset: 'reset')),
                            )),
                    Gap(16.h),
                    SBPromoSlider(),
                    Gap(16.h),
                    ListViewGeneric<ProductModel>(
                      isLoading: state.isProductLoading,
                      isSuccess: state.isProductSuccess,
                      isItemAvailable: state.featureProducts.isNotEmpty,
                      items: products,
                      onItemTap: (product) => context
                          .push(RouteName.detailProductPage, extra: product),
                      showMore: false,
                      title: 'Featured Products',
                      itemBuilder: (context, product, index) =>
                          CardFeatureProduct(product: product),
                      isShowButton: true,
                      isListHome: true,
                      filterType: FilterType.product,
                      onPressed: () => context
                          .push(
                            RouteName.allFeaturedProducts,
                          )
                          .then(
                            (_) => context
                                .read<HomeStrongbodyAiBloc>()
                                .add(FilterEvent(
                                  filterType: FilterType.product,
                                  typeReset: 'reset',
                                )),
                          ),
                    ),
                    ListCardHomeExpert(
                      experts: experts,
                      title: 'Top Experts',
                      state: state,
                    ),
                    Gap(10.h),
                    ListViewGeneric<BlogModel>(
                      isLoading: state.isBlogLoading,
                      isSuccess: state.isBlogSuccess,
                      isItemAvailable: state.blogs.isNotEmpty,
                      items: blogs,
                      onItemTap: (blog) =>
                          context.push(RouteName.detailBlogPage, extra: blog),
                      showMore: false,
                      title: 'Blog – Insights & Updates',
                      itemBuilder: (context, blog, index) =>
                          CardBlog(blog: blog),
                      isShowButton: true,
                      isListHome: true,
                      filterType: FilterType.blog,
                      onPressed: () => context
                          .push(
                            RouteName.allBlogs,
                          )
                          .then(
                            (_) => context
                                .read<HomeStrongbodyAiBloc>()
                                .add(FilterEvent(
                                  filterType: FilterType.blog,
                                  typeReset: 'reset',
                                )),
                          ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
