part of 'search_home_strong_bloc.dart';


sealed class SearchHomeStrongEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class SearchQueryChanged extends SearchHomeStrongEvent {
  final String query;

  SearchQueryChanged(this.query);

  @override
  List<Object> get props => [query];
}


class AddToSearchHomeHistoryEvent extends SearchHomeStrongEvent {
  final SearchHomeItem selectedItem;

   AddToSearchHomeHistoryEvent(this.selectedItem);

  @override
  List<Object> get props => [selectedItem];
}

class LoadRecentSearchesEvent extends SearchHomeStrongEvent {
  LoadRecentSearchesEvent();
}

class ClearRecentSearchesEvent extends SearchHomeStrongEvent {
  ClearRecentSearchesEvent();
}
class RemoveRecentSearchEvent extends SearchHomeStrongEvent {
  final int index;

  RemoveRecentSearchEvent(this.index);

  @override
  List<Object> get props => [index];
}