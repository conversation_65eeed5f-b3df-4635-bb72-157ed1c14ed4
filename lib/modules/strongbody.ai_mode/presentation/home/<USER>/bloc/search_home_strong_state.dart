part of 'search_home_strong_bloc.dart';

class SearchHomeStrongState extends Equatable {
  final int selectedIndex;
  final String query;
  final List<SearchHomeItem> recentSearches;
  final List<SearchHomeItem> result;
  final bool isLoading;
  final bool isSuccess;
  final String? errorMessage;

  const SearchHomeStrongState({
    this.selectedIndex = 0,
    this.query = '',
    this.recentSearches = const [],
    this.result = const [],
    this.isLoading = false,
    this.isSuccess = false,
    this.errorMessage,
  });

  SearchHomeStrongState copyWith({
    int? selectedIndex,
    String? query,
    List<SearchHomeItem>? recentSearches,
    List<SearchHomeItem>? result,
    bool? isLoading,
    bool? isSuccess,
    String? errorMessage,
  }) {
    return SearchHomeStrongState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
      query: query ?? this.query,
      recentSearches: recentSearches ?? this.recentSearches,
      result: result ?? this.result,
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object> get props => [
        selectedIndex,
        query,
        recentSearches,
        result ?? [],
        isLoading,
        isSuccess,
        errorMessage ?? '',
      ];
}
