import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/search/search_item.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

import '../../../../../../core/domain/global_dependencies.dart';
import '../../../../../../core/domain/storages/global_storage.dart';
import '../../../../../../core/utils/debounce.dart';

part 'search_home_strong_event.dart';

part 'search_home_strong_state.dart';

class SearHomeStrongBloc
    extends Bloc<SearchHomeStrongEvent, SearchHomeStrongState> {
  final Debouncer _debouncer = Debouncer(
    delay: const Duration(milliseconds: 500),
  );

  final HomeStrongbodyAiRepository servicesRepository;

  SearHomeStrongBloc({
    required this.servicesRepository,
  }) : super(const SearchHomeStrongState()) {
    on<SearchQueryChanged>(_onSearchQueryChanged);
    on<LoadRecentSearchesEvent>(_loadRecentSearchesEvent);
    on<AddToSearchHomeHistoryEvent>(_addToSearchHomeHistoryEvent);
    on<ClearRecentSearchesEvent>(_onClearRecentSearchesEvent);
    on<RemoveRecentSearchEvent>(_onRemoveRecentSearchEvent);
    add(LoadRecentSearchesEvent());
  }

  void _onSearchQueryChanged(
    SearchQueryChanged event,
    Emitter<SearchHomeStrongState> emit,
  ) async {
    final searchQuery = event.query;
    return Future(() {
      return _debouncer.asynchronousDebounce(() async {
        try {
          if (!emit.isDone) {
            emit(
              state.copyWith(
                query: searchQuery,
                isLoading: true,
                isSuccess: false,
              ),
            );
          }

          final response = await servicesRepository.searchHomeApi(
            {
              "keyword": searchQuery,
            },
          );
          print("Response: $response");

          if (!emit.isDone) {
            emit(
              state.copyWith(
                query: searchQuery,
                result: response.items,
                // recentSearches: recentSearches,
                isLoading: false,
                isSuccess: true,
              ),
            );
            add(LoadRecentSearchesEvent());
          }
        } catch (e) {
          print('Error in Search Bloc: $e');
          if (!emit.isDone) {
            emit(
              state.copyWith(
                errorMessage: e.toString(),
                isLoading: false,
                isSuccess: false,
              ),
            );
          }
        }
      });
    });
  }

  Future<void> _loadRecentSearchesEvent(
    LoadRecentSearchesEvent event,
    Emitter<SearchHomeStrongState> emit,
  ) async {
    try {
      final recentSearchItems = gs.getSearchHistoryItems(
        GlobalStorageKey.searchServiceHistory,
      );

      // Convert Map to SearchItem objects
      final recentSearches = recentSearchItems
          .map((item) => SearchHomeItem.fromJson(item))
          .toList();

      emit(
        state.copyWith(
          recentSearches: recentSearches,
        ),
      );
    } catch (e) {
      print('Error loading recent searches: $e');
    }
  }

  Future<void> _addToSearchHomeHistoryEvent(
    AddToSearchHomeHistoryEvent event,
    Emitter<SearchHomeStrongState> emit,
  ) async {
    try {
      final selectedItem = event.selectedItem;

      // Lưu toàn bộ thông tin của item
      await gs.addSearchHistoryItem(
        selectedItem.toJson(),
        GlobalStorageKey.searchServiceHistory,
      );

      add(LoadRecentSearchesEvent());
    } catch (e) {
      print('Error adding to search history: $e');
    }
  }

  Future<void> _onClearRecentSearchesEvent(
    ClearRecentSearchesEvent event,
    Emitter<SearchHomeStrongState> emit,
  ) async {
    try {
      await gs.clearSearchHistoriesItems(key: GlobalStorageKey.searchServiceHistory);
      emit(state.copyWith(recentSearches: []));
    } catch (e) {
      print('Error clearing recent searches: $e');
    }
  }

  Future<void> _onRemoveRecentSearchEvent(
      RemoveRecentSearchEvent event,
      Emitter<SearchHomeStrongState> emit,
      ) async {
    try {
      await gs.removeSearchHistoryItem(
        event.index,
        key: GlobalStorageKey.searchServiceHistory,
      );
      final recentSearches = gs.getSearchHistoryItems(
        GlobalStorageKey.searchServiceHistory,
      );
      emit(
          state.copyWith(
            recentSearches: recentSearches
                .map((item) => SearchHomeItem.fromJson(item))
                .toList(),
          ));
    } catch (e) {
      print('Error removing recent search: $e');
    }
  }
}
