import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class BuildCheckboxCategory extends StatelessWidget {
  const BuildCheckboxCategory({
    super.key,
    required this.categories,
    required this.onChanged,
    required this.selectedCategoryIds,
  });

  final List<String> categories;
  final Function(int, bool) onChanged;
  final List<int> selectedCategoryIds;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: categories.asMap().entries.map((entry) {
        int index = entry.key;
        String category = entry.value;
        bool isSelected = selectedCategoryIds.contains(index);

        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (bool? value) {
                    onChanged(index, value ?? false);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  category,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
