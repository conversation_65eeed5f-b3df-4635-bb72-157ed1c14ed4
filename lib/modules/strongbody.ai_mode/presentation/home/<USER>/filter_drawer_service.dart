import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/validation_utils.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/bottom_filter_drawer.dart';

class FilterDrawerService extends StatefulWidget {
  const FilterDrawerService({super.key});

  @override
  State<FilterDrawerService> createState() => _FilterDrawerServiceState();
}

class _FilterDrawerServiceState extends State<FilterDrawerService> {
  // Service Type selections
  int? selectedCountryId;
  final controllerServiceType = TextEditingController();
  final controllerCountry = TextEditingController();
  final controllerPrice = TextEditingController();

  bool showMoreCategories = false;

  final List<String> serviceTypes = [
    'Online',
    'Offline',
    'Hybrid (both online and offline)',
  ];

  final List<String> categories = [
    'Medical Professional',
    'Nutrition Consultant',
    'Physical Care',
    'Mental Services',
    'Pharmacy',
  ];

  final List<String> sortBy = [
    'Featured',
    'Price (High to Low)',
    'Price (Low to High)',
  ];
  final globalStorage = getIt<GlobalStorage>();
  late List<Country> countries;

  @override
  void initState() {
    super.initState();
    // Initialize selected country ID from global storage if available
    countries = globalStorage.countries!;

    // Set default service type based on bloc state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentState = context.read<HomeStrongbodyAiBloc>().state;
      if (currentState.serviceTypeIndex >= 0 &&
          currentState.serviceTypeIndex < serviceTypes.length) {
        controllerServiceType.text =
            serviceTypes[currentState.serviceTypeIndex];
      }
    });
  }

  // Price range
  TextEditingController minPriceController = TextEditingController();
  TextEditingController maxPriceController = TextEditingController();
  final _countryController = TextEditingController();

  // Country

  @override
  void dispose() {
    minPriceController.dispose();
    maxPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
      builder: (context, state) {
        return Drawer(
            width: MediaQuery.of(context).size.width,
            backgroundColor: Colors.white,
            child: SafeArea(
                child: Scaffold(
              bottomNavigationBar: BottomFilterDrawer(
                categories: categories,
                state: state,
                onReset: () {
                  context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                        filterType: FilterType.service,
                        typeReset: 'reset',
                      ));
                  context.pop();
                  // Clear all filters

                  // Reset service type in bloc
                  context
                      .read<HomeStrongbodyAiBloc>()
                      .add(SetServiceTypeEvent(serviceTypeIndex: 1));
                },
                onApply: () {
                  int priceValue = 0;
                  try {
                    priceValue = int.tryParse(controllerPrice.text) ?? 0;
                  } catch (e) {
                    priceValue = 0;
                  }

                  List<String> selectedCategories = [];
                  for (int categoryId in state.selectedCategoryIds) {
                    if (categoryId >= 0 && categoryId < categories.length) {
                      selectedCategories.add(categories[categoryId]);
                    }
                  }

                  context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                      filterType: FilterType.service,
                      countryId: selectedCountryId ?? 1,
                      serviceType: controllerServiceType.text,
                      price: priceValue,
                      categories: selectedCategories,
                      typeReset: 'apply'));
                  context.pop();
                },
              ),
              body: Column(children: [
                // Header
                Container(
                  height: 60.h,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).greyScale100(context),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.close,
                          size: 24.sp,
                          color: Theme.of(context).greyScale900(context),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Filter',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.lightHeadingMedium,
                        ),
                      ),
                      SizedBox(width: 40.w), // Balance the close button
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: Container(
                    color: Colors.white,
                    child: ListView(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      children: [
                        Gap(24.h),
                        _buildSectionTitle('Price'),
                        Gap(16.h),
                        Row(
                          children: [
                            Expanded(
                                child: TextButton(
                                    onPressed: () {
                                      context.read<HomeStrongbodyAiBloc>().add(
                                          SelectProductSortPriceEvent(
                                              sortPrice: 'min'));
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10.h, horizontal: 16.w),
                                      side: state.selectedSortPriceProduct ==
                                              'min'
                                          ? BorderSide(
                                              color: Theme.of(context)
                                                  .alertInformationBase(
                                                      context),
                                              width: 1,
                                            )
                                          : BorderSide(
                                              color: Theme.of(context)
                                                  .greyScale300(context),
                                              width: 1,
                                            ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.r),
                                      ),
                                    ),
                                    child: Text(
                                      'Min',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyLargeRegular
                                          .copyWith(
                                              color:
                                                  state.selectedSortPriceProduct ==
                                                          'min'
                                                      ? Theme.of(context)
                                                          .alertInformationBase(
                                                              context)
                                                      : Theme.of(context)
                                                          .greyScale500(
                                                              context)),
                                    ))),
                            Gap(10.w),
                            Expanded(
                                child: TextButton(
                                    onPressed: () {
                                      context.read<HomeStrongbodyAiBloc>().add(
                                          SelectProductSortPriceEvent(
                                              sortPrice: 'max'));
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10.h, horizontal: 16.w),
                                      side: BorderSide(
                                        color: state.selectedSortPriceProduct ==
                                                'max'
                                            ? Theme.of(context)
                                                .alertInformationBase(context)
                                            : Theme.of(context)
                                                .greyScale300(context),
                                        width: 1,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.r),
                                      ),
                                    ),
                                    child: Text(
                                      'Max',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyLargeRegular
                                          .copyWith(
                                              color:
                                                  state.selectedSortPriceProduct ==
                                                          'max'
                                                      ? Theme.of(context)
                                                          .alertInformationBase(
                                                              context)
                                                      : Theme.of(context)
                                                          .greyScale500(
                                                              context)),
                                    ))),
                          ],
                        ),
                        Gap(16.h),
                        // Service Type Section

                        // Category Section
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildSectionTitle('Category'),
                            Icon(
                              Icons.chevron_right,
                              color: Theme.of(context).greyScale500(context),
                              size: 20.sp,
                            ),
                          ],
                        ),
                        Gap(16.h),

                        _buildCheckboxCategory(
                          categories,
                          (value, isSelected) {
                            // Xử lý việc chọn category
                            context.read<HomeStrongbodyAiBloc>().add(
                                  SelectServiceCategoryEvent(
                                    categoryId: value,
                                    isSelected: isSelected,
                                  ),
                                );
                          },
                          state.selectedCategoryIds,
                        ),
                        // Show more button
                        Gap(8.h),
                        GestureDetector(
                          onTap: () {
                            setState(
                                () => showMoreCategories = !showMoreCategories);
                          },
                          child: Row(
                            children: [
                              Icon(
                                showMoreCategories
                                    ? Icons.keyboard_arrow_up
                                    : Icons.keyboard_arrow_down,
                                color: Theme.of(context).greyScale500(context),
                                size: 20.sp,
                              ),
                              Gap(8.w),
                              Text(
                                showMoreCategories ? 'Show less' : 'Show more',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyMediumRegular
                                    .copyWith(
                                      color: Theme.of(context)
                                          .greyScale500(context),
                                    ),
                              ),
                            ],
                          ),
                        ),

                        Gap(16.h),

                        // Country Section
                        _buildSectionTitle('Country'),
                        Gap(16.h),
                        FormField<int>(
                          initialValue: state.selectedServiceByCountryId,
                          validator: (value) =>
                              ValidateForm.validateCountrySelection(value),
                          builder: (FormFieldState<int> field) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Theme(
                                  data: Theme.of(context).copyWith(
                                    dropdownMenuTheme: DropdownMenuThemeData(
                                      textStyle: TextStyle(color: Colors.black),
                                      menuStyle: MenuStyle(
                                        backgroundColor:
                                            WidgetStateProperty.all(
                                                Colors.white),
                                      ),
                                      inputDecorationTheme:
                                          InputDecorationTheme(
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: field.hasError
                                                ? Theme.of(context)
                                                    .colorScheme
                                                    .error
                                                : Theme.of(context)
                                                    .greyScale300(context),
                                            width: 1,
                                          ),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: field.hasError
                                                ? Theme.of(context)
                                                    .colorScheme
                                                    .error
                                                : Theme.of(context)
                                                    .primary(context),
                                            width: 2,
                                          ),
                                        ),
                                        errorBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .error,
                                            width: 1.5,
                                          ),
                                        ),
                                        focusedErrorBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          borderSide: BorderSide(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .error,
                                            width: 2,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  child: DropdownMenu<int>(
                                    menuHeight: 200,
                                    width: double.infinity,
                                    controller: _countryController,
                                    dropdownMenuEntries:
                                        countries.map((country) {
                                      return DropdownMenuEntry<int>(
                                        value: country.id!,
                                        label: country.title!,
                                      );
                                    }).toList(),
                                    onSelected: (int? value) {
                                      context.read<HomeStrongbodyAiBloc>().add(
                                          SelectServiceByCountryEvent(
                                              countryId: value!));
                                    },
                                    initialSelection:
                                        state.selectedServiceByCountryId,
                                    label: Text(
                                      LocaleKeys.country.tr(),
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyLargeMedium
                                          .copyWith(
                                              color: field.hasError
                                                  ? Theme.of(context)
                                                      .colorScheme
                                                      .error
                                                  : Theme.of(context)
                                                      .greyScale400(context)),
                                    ),
                                  ),
                                ),
                                if (field.hasError)
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        top: 8.0, left: 12.0),
                                    child: Text(
                                      field.errorText!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .error),
                                    ),
                                  ),
                              ],
                            );
                          },
                        ),
                        Gap(16.h),
                        _buildSectionTitle('Service Type'),
                        Gap(16.h),
                        _buildCheckboxTile(
                          serviceTypes,
                          (value) {
                            // Set controller text khi user chọn service type
                            controllerServiceType.text = serviceTypes[value];
                            context.read<HomeStrongbodyAiBloc>().add(
                                SetServiceTypeEvent(serviceTypeIndex: value));
                          },
                          state.serviceTypeIndex,
                        ),

                        _buildSectionTitle('Sort by'),
                        Gap(16.h),
                        _buildCheckboxSort(sortBy, (value, isSelected) {
                          context
                              .read<HomeStrongbodyAiBloc>()
                              .add(SelectServiceSortEvent(
                                sortIndex: value,
                              ));
                        }, state.selectedSortServiceIndex),
                      ],
                    ),
                  ),
                )
              ]),
            )));
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
            color: Theme.of(context).greyScale600(context),
          ),
    );
  }

  Widget _buildCheckboxTile(List<String> serviceTypes,
      ValueChanged<int> onChanged, int selectedIndex) {
    return Column(
      children: serviceTypes.asMap().entries.map((entry) {
        int index = entry.key;
        String type = entry.value;

        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: selectedIndex == index,
                  onChanged: (bool? value) {
                    if (value == true) {
                      onChanged(index);
                      controllerServiceType.text = type;
                    }
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: selectedIndex == index
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  type,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCheckboxCategory(List<String> categories,
      Function(int, bool) onChanged, List<int> selectedCategoryIds) {
    return Column(
      children: categories.asMap().entries.map((entry) {
        int index = entry.key;
        String category = entry.value;
        bool isSelected = selectedCategoryIds.contains(index);

        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (bool? value) {
                    onChanged(index, value ?? false);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  category,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCheckboxSort(List<String> categories,
      Function(int, bool) onChanged, int selectedIndex) {
    return Column(
      children: categories.asMap().entries.map((entry) {
        int index = entry.key;
        String category = entry.value;
        bool isSelected = selectedIndex == index;
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (bool? value) {
                    onChanged(index, value ?? false);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  category,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
