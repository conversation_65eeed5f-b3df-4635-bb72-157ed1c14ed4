import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/bottom_filter_drawer.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/build_checkbox_category.dart';

class FilterDrawerProduct extends StatefulWidget {
  const FilterDrawerProduct({super.key});

  @override
  State<FilterDrawerProduct> createState() => _FilterDrawerProductState();
}

class _FilterDrawerProductState extends State<FilterDrawerProduct> {
  // Service Type selections
  int? selectedCountryId;
  final controllerServiceType = TextEditingController();
  final controllerPrice = TextEditingController();

  // Category selections

  bool showMoreCategories = false;

  final List<String> categories = [
    'Health & Wellness',
    'Medical Supplies & Equipment',
    'Food & Beverage',
    'Personal Care & Hygiene',
    'Dietary & Nutritional Supplements',
  ];

  final globalStorage = getIt<GlobalStorage>();
  late List<Country> countries;
  final List<String> sortBy = [
    'Featured',
    'Price (High to Low)',
    'Price (Low to High)',
  ];
  @override
  void initState() {
    super.initState();
    // Initialize selected country ID from global storage if available
    countries = globalStorage.countries!;
  }

  // Country

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
      builder: (context, state) {
        return Drawer(
            width: MediaQuery.of(context).size.width,
            backgroundColor: Colors.white,
            child: SafeArea(
                child: Scaffold(
              bottomNavigationBar: BottomFilterDrawer(
                categories: categories,
                state: state,
                onReset: () {
                  context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                        filterType: FilterType.product,
                        typeReset: 'reset',
                      ));
                  context.pop();
                  // Clear all filters
                },
                onApply: () {
                  int priceValue = 0;
                  try {
                    priceValue = int.tryParse(controllerPrice.text) ?? 0;
                  } catch (e) {
                    priceValue = 0;
                  }

                  final List<String> selectedCategories = [];
                  for (int categoryId in state.selectedCategoryIdProducts) {
                    if (categoryId < categories.length) {
                      selectedCategories.add(categories[categoryId]);
                    }
                  }

                  context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
                      filterType: FilterType.product,
                      categories: selectedCategories,
                      price: priceValue,
                      typeReset: 'apply'));
                  context.pop();
                },
              ),
              body: Column(children: [
                // Header
                Container(
                  height: 60.h,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(
                        color: Theme.of(context).greyScale100(context),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.close,
                          size: 24.sp,
                          color: Theme.of(context).greyScale900(context),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Filter',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.lightHeadingMedium,
                        ),
                      ),
                      SizedBox(width: 40.w), // Balance the close button
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: Container(
                    color: Colors.white,
                    child: ListView(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      children: [
                        Gap(32.h),

                        // Price Section
                        _buildSectionTitle('Price'),
                        Gap(16.h),
                        Row(
                          children: [
                            Expanded(
                                child: TextButton(
                                    onPressed: () {
                                      context.read<HomeStrongbodyAiBloc>().add(
                                          SelectProductSortPriceEvent(
                                              sortPrice: 'min'));
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10.h, horizontal: 16.w),
                                      side: state.selectedSortPriceProduct ==
                                              'min'
                                          ? BorderSide(
                                              color: Theme.of(context)
                                                  .alertInformationBase(
                                                      context),
                                              width: 1,
                                            )
                                          : BorderSide(
                                              color: Theme.of(context)
                                                  .greyScale300(context),
                                              width: 1,
                                            ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.r),
                                      ),
                                    ),
                                    child: Text(
                                      'Min',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyLargeRegular
                                          .copyWith(
                                              color:
                                                  state.selectedSortPriceProduct ==
                                                          'min'
                                                      ? Theme.of(context)
                                                          .alertInformationBase(
                                                              context)
                                                      : Theme.of(context)
                                                          .greyScale500(
                                                              context)),
                                    ))),
                            Gap(10.w),
                            Expanded(
                                child: TextButton(
                                    onPressed: () {
                                      context.read<HomeStrongbodyAiBloc>().add(
                                          SelectProductSortPriceEvent(
                                              sortPrice: 'max'));
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10.h, horizontal: 16.w),
                                      side: BorderSide(
                                        color: state.selectedSortPriceProduct ==
                                                'max'
                                            ? Theme.of(context)
                                                .alertInformationBase(context)
                                            : Theme.of(context)
                                                .greyScale300(context),
                                        width: 1,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.r),
                                      ),
                                    ),
                                    child: Text(
                                      'Max',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyLargeRegular
                                          .copyWith(
                                              color:
                                                  state.selectedSortPriceProduct ==
                                                          'max'
                                                      ? Theme.of(context)
                                                          .alertInformationBase(
                                                              context)
                                                      : Theme.of(context)
                                                          .greyScale500(
                                                              context)),
                                    ))),
                          ],
                        ),
                        Gap(16.h),

                        // Category Section
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildSectionTitle('Category'),
                            Icon(
                              Icons.chevron_right,
                              color: Theme.of(context).greyScale500(context),
                              size: 20.sp,
                            ),
                          ],
                        ),
                        Gap(16.h),
                        BuildCheckboxCategory(
                            categories: categories,
                            onChanged: (value, isSelected) {
                              context.read<HomeStrongbodyAiBloc>().add(
                                  SelectProductCategoryEvent(
                                      categoryId: value,
                                      isSelected: isSelected));
                            },
                            selectedCategoryIds:
                                state.selectedCategoryIdProducts),

                        Gap(10.h),

                        // Country Section
                        _buildSectionTitle('Sort by'),
                        Gap(16.h),
                        _buildCheckboxSort(sortBy, (value, isSelected) {
                          context
                              .read<HomeStrongbodyAiBloc>()
                              .add(SelectSortProductByPriceEvent(
                                sortPrice: value,
                              ));
                        }, state.selectedSortProductIndex),
                      ],
                    ),
                  ),
                )
              ]),
            )));
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
            color: Theme.of(context).greyScale600(context),
          ),
    );
  }

  Widget _buildCheckboxSort(List<String> categories,
      Function(int, bool) onChanged, int selectedIndex) {
    return Column(
      children: categories.asMap().entries.map((entry) {
        int index = entry.key;
        String category = entry.value;
        bool isSelected = selectedIndex == index;
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (bool? value) {
                    onChanged(index, value ?? false);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeColor: Theme.of(context).primary(context),
                  checkColor: Colors.white,
                  side: BorderSide(
                    color: isSelected
                        ? Theme.of(context).primary(context)
                        : Theme.of(context).greyScale300(context),
                    width: 1.5,
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Text(
                  category,
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale900(context),
                      ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
