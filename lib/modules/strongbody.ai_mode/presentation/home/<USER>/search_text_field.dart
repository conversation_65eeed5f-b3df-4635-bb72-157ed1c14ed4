import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../../app/routers/routers_name.dart';
import '../../../../../shared/widgets/image_widget/image_widget.dart';
import 'bloc/search_home_strong_bloc.dart';

class SearchTextFileStrongBodyPage extends StatefulWidget {
  const SearchTextFileStrongBodyPage({super.key});

  @override
  State<SearchTextFileStrongBodyPage> createState() =>
      _SearchTextFileStrongBodyPageState();
}

class _SearchTextFileStrongBodyPageState
    extends State<SearchTextFileStrongBodyPage> {
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    searchController.addListener(
      () {
        context
            .read<SearHomeStrongBloc>()
            .add(SearchQueryChanged(searchController.text.trim()));
      },
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    searchController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<SearHomeStrongBloc, SearchHomeStrongState>(
          builder: (context, state) {
        return SingleChildScrollView(
            child: Column(
          children: [
            Gap(40.h),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Hero(
                tag: 'search-box',
                child: Material(
                  elevation: 8,
                  borderRadius: BorderRadius.circular(16),
                  shadowColor: Colors.black.withOpacity(0.1),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          spreadRadius: 0,
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                        BoxShadow(
                          color: Colors.black.withOpacity(0.04),
                          spreadRadius: 0,
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      onChanged: (value) {
                        context
                            .read<SearHomeStrongBloc>()
                            .add(SearchQueryChanged(value));
                      },
                      controller: searchController,
                      decoration: InputDecoration(
                        hintText: "Search for places...",
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 16.sp,
                        ),
                        suffixIcon: IconButton(
                          onPressed: () {
                            searchController.clear();
                            context
                                .read<SearHomeStrongBloc>()
                                .add(SearchQueryChanged(''));
                          },
                          icon: Icon(
                            Icons.clear,
                            color: Colors.grey[600],
                            size: 20.w,
                          ),
                        ),
                        prefixIcon: IconButton(
                          onPressed: () {
                            context.pop();
                          },
                          icon: Icon(
                            Icons.arrow_back_outlined,
                            color: Colors.grey[600],
                            size: 20.w,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 16.h,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            _buildCardSearchResult(context, state)
          ],
        ));
      }),
    );
  }

  Widget _buildCardSearchResult(
      BuildContext context, SearchHomeStrongState state) {
    final services = state.result;

    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (services.isEmpty) {
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Center(
          child: Text(
            'No results found for "${searchController.text.trim()}"',
            style: Theme.of(context).textTheme.lightBodyLargeMedium,
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: services.length,
        itemBuilder: (context, index) {
          final item = services[index];
          return InkWell(
            onTap: () {
              context
                  .read<SearHomeStrongBloc>()
                  .add(AddToSearchHomeHistoryEvent(item));
              // xử lý chuyển hướng đến trang chi tiết dịch vụ
              if (item.type == 'service') {
                context.push(RouteName.serviceDetailSeller, extra: item.id);
              } else if (item.type == 'product') {
                context.push(RouteName.productDetailSeller, extra: item.id);
              } else if (item.type == 'seller') {
                context.push(RouteName.profileView, extra: {
                  'sellerId': item.id,
                });
              }
            },
            child: Container(
              margin: EdgeInsets.only(bottom: 12.h),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16.r),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 0,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: Colors.grey.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12.r),
                    child: Container(
                      width: 80.w,
                      height: 80.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                        color: Colors.grey[100],
                        border: Border.all(
                          color: Colors.grey.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: CustomImage(
                        path: item.images.isNotEmpty
                            ? item.images
                            : 'https://d1hjkbq40fs2x4.cloudfront.net/2016-01-31/files/1045-5.jpg',
                        imageType: ImageType.network,
                        width: 80.w,
                        height: 80.h,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.name,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeMedium
                              .copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 16.sp,
                              ),
                        ),
                        SizedBox(height: 8.h),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 8.w, vertical: 4.h),
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          child: Text(
                            item.type,
                            style: Theme.of(context)
                                .textTheme
                                .lightBodySmallMedium
                                .copyWith(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.w,
                    color: Colors.grey[400],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
