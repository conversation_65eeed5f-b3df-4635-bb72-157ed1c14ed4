import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/seller/models/seller_profile.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardExpert extends StatelessWidget {
  const CardExpert({
    super.key,
    this.isLoading = false,
    required this.expert,
  });
  final SellerProfileModel expert;
  final bool isLoading;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Gap(10.h),
          Row(
            children: [
              Container(
                width: 90.h,
                height: 70.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12.0),
                  child: CustomImage(
                    path: expert.user.profilePicture?.isNotEmpty ?? false
                        ? expert.user.profilePicture!
                        : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ5G6g4jFFhYZXVzilPjT1FZ4ptm8L6iV4XAZ-NjMoanZT_-qr8IMV9tJQX4-y6X--GmF4&usqp=CAU',
                    imageType: ImageType.network,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                ),
              ),
              Gap(10.0.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${expert.user.lastName} ${expert.user.firstName}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeBold
                          .copyWith(
                            fontSize: 14.sp,
                          ),
                    ),
                    const SizedBox(height: 4.0),
                    Text(
                      expert.user.email!,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallMedium
                          .copyWith(
                            fontSize: 11.sp,
                          ),
                    ),
                  ],
                ),
              ),
              Gap(10.0.w),
              GestureDetector(
                onTap: () {
                  context.push(
                    RouteName.chatPage,
                    extra: {
                      "uid": expert.user.id ?? 0,
                      "name": expert.user.fullName,
                      "avatar": expert.user.profilePicture,
                      "isOnline": false,
                    },
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 10.0),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey[600]!,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(AppRadius.radius10),
                  ),
                  child: Text('Contact',
                      style: Theme.of(context).textTheme.lightBodyMediumMedium),
                ),
              ),
            ],
          ),
          Gap(10.h),
          Divider(
            color: Colors.grey[300]!,
            thickness: 0.5,
          ),
        ],
      ),
    );
  }
}
