import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/model/blog_model.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardBlog extends StatelessWidget {
  const CardBlog({
    super.key,
    required this.blog,
  });
  final BlogModel blog;

  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    return Container(
      width: sizeWidth * 0.5,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                ),
                child: CustomImage(
                  path: blog.images.isNotEmpty && blog.images[0].isNotEmpty
                      ? blog.images[0]
                      : AppAssets.servicePlaceholder,
                  width: double.infinity,
                  height: 150.h,
                  fit: BoxFit.cover,
                  imageType: blog.images.isNotEmpty && blog.images[0].isNotEmpty
                      ? ImageType.network
                      : ImageType.assetImage,
                ),
              ),
              Positioned(
                top: 0,
                right: 5,
                child: IconButton(
                  icon: SvgPicture.asset(
                    AppAssets.heart_icon,
                    color: Theme.of(context).whitePrimary(context),
                  ),
                  onPressed: () {},
                ),
              )
            ],
          ),
          Container(
            width: sizeWidth,
            height: 110.h,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8.r),
                bottomRight: Radius.circular(8.r),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  RichText(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(children: [
                      TextSpan(
                        text: 'by ',
                        style: Theme.of(context).textTheme.lightBodySmallMedium,
                      ),
                      TextSpan(
                        text: blog.authorName,
                        style:
                            Theme.of(context).textTheme.lightBodySmallSemiBold,
                      ),
                    ]),
                  ),
                  SizedBox(height: 8),
                  Text(
                    blog.title,
                    style: Theme.of(context).textTheme.lightBodyMediumBold,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
