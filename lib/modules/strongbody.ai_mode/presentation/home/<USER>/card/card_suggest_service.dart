import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';

import '../../../../../../core/constants/app_assets.dart';
import '../../../../../../shared/widgets/image_widget/image_widget.dart';

class CardSuggestService extends StatelessWidget {
  const CardSuggestService({super.key, required this.service});

  final ServiceModel service;
  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    return IntrinsicHeight(
      child: Container(
        width: sizeWidth * 0.5,
        margin: EdgeInsets.symmetric(vertical: 1.h),
        decoration: BoxDecoration(
          color: Theme.of(context).whitePrimary(context),
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.r),
                    topRight: Radius.circular(8.r),
                  ),
                  child: CustomImage(
                    path: service.image.isNotEmpty
                        ? service.image[0]
                        : 'https://d1hjkbq40fs2x4.cloudfront.net/2016-01-31/files/1045-5.jpg',
                    imageType: ImageType.network,
                    width: double.infinity,
                    height: 150.h,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 5,
                  child: IconButton(
                    icon: SvgPicture.asset(
                      AppAssets.heart_icon,
                      color: Theme.of(context).whitePrimary(context),
                    ),
                    onPressed: () {},
                  ),
                )
              ],
            ),
            // Content section
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 6.w,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(8.r),
                    bottomRight: Radius.circular(8.r),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: RichText(
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'by ',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodySmallMedium,
                                    ),
                                    TextSpan(
                                      text: service.user?.firstName ?? 'user',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodySmallBold,
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16.w,
                            ),
                            Gap(2.w),
                            Text(
                              '${(Random().nextDouble() * 2 + 3).toStringAsFixed(1)}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    color: Color(0xFF687588),
                                  ),
                            ),
                          ],
                        ),
                        Gap(10.h),
                        SizedBox(
                          width: double.infinity,
                          height: 40.h,
                          child: Text(
                            service.title,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style:
                                Theme.of(context).textTheme.lightBodyMediumBold,
                          ),
                        ),
                        Gap(10.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text("From"),
                            Text(
                              ' US \$${service.price}',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumMedium
                                  .copyWith(
                                    color: Colors.red.shade700,
                                    fontWeight: FontWeight.w700,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
