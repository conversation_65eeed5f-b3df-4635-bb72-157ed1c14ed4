import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/seller/models/seller_profile.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardExpertList extends StatelessWidget {
  const CardExpertList({super.key, required this.sellerModel});

  final SellerProfileModel sellerModel;

  @override
  Widget build(BuildContext context) {
    final skills = [
      'Fitness',
      'Nutrition',
      'Yoga',
    ];
    final sizeWidth = MediaQuery.of(context).size.width;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6.r),
                  topRight: Radius.circular(6.r),
                ),
                child: CustomImage(
                  path: sellerModel.user.profilePicture!.isNotEmpty
                      ? sellerModel.user.profilePicture!
                      : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ5G6g4jFFhYZXVzilPjT1FZ4ptm8L6iV4XAZ-NjMoanZT_-qr8IMV9tJQX4-y6X--GmF4&usqp=CAU',
                  fit: BoxFit.cover,
                  height: 132.h,
                  width: double.infinity,
                  imageType: ImageType.network,
                ),
              ),
              Positioned(
                top: 0,
                right: 5,
                child: IconButton(
                  icon: SvgPicture.asset(
                    AppAssets.heart_icon,
                    color: Theme.of(context).whitePrimary(context),
                  ),
                  onPressed: () {},
                ),
              )
            ],
          ),
          Container(
            width: sizeWidth,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(6.r),
                bottomRight: Radius.circular(6.r),
              ),
            ),
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Text(
                        sellerModel.user.displayName,
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumBold
                            .copyWith(fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Gap(5.w),
                    SvgPicture.asset(
                      AppAssets.tickSvg,
                    ),
                  ],
                ),
                Gap(10.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        sellerModel.user.email!,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.padding10,
                        vertical: AppSpacing.padding2h,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .successBase(context)
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            AppAssets.dotSvg,
                          ),
                          Gap(4.w),
                          Text('Online',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyXSmallMedium
                                  .copyWith(
                                    color:
                                        Theme.of(context).successBase(context),
                                  )),
                        ],
                      ),
                    )
                  ],
                ),
                Gap(8.h),
                Wrap(
                  spacing: 4.w,
                  runSpacing: 4.h,
                  children: skills.map((skill) {
                    return Chip(
                      label: Text(
                        skill,
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyXSmallSemiBold
                            .copyWith(
                                color: Theme.of(context).greyScale500(context)),
                      ),
                      backgroundColor: Theme.of(context).greyScale100(context),
                      side: BorderSide.none,
                    );
                  }).toList(),
                ),
                Gap(5.h),
                Align(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    onTap: () {},
                    child: Text(
                      'chat',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallRegular
                          .copyWith(
                              color: Theme.of(context)
                                  .alertInformationBase(context)),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
