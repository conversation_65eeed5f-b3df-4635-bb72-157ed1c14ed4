import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';

import '../../../../../../core/constants/app_assets.dart';
import '../../../../../../shared/widgets/image_widget/image_widget.dart';

class CardFeatureProduct extends StatelessWidget {
  const CardFeatureProduct({
    super.key,
    required this.product,
  });

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    final pricingTier = product.pricingTiers
        .where((e) => e.productId == product.id)
        .firstOrNull;
    final sizeWidth = MediaQuery.of(context).size.width;
    return Container(
      width: sizeWidth * 0.5,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6.r),
                  topRight: Radius.circular(6.r),
                ),
                child: CustomImage(
                  path: product.image.isNotEmpty
                      ? product.image[0]
                      : 'https://via.placeholder.com/176x132?text=No+Image',
                  fit: BoxFit.cover,
                  height: 132.h,
                  width: double.infinity,
                  imageType: ImageType.network,
                ),
              ),
              Positioned(
                top: 0,
                right: 5,
                child: IconButton(
                  icon: SvgPicture.asset(
                    AppAssets.heart_icon,
                    color: Theme.of(context).whitePrimary(context),
                  ),
                  onPressed: () {},
                ),
              )
            ],
          ),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(6.r),
                bottomRight: Radius.circular(6.r),
              ),
            ),
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge
                      ?.copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Gap(4.h),
                Text(
                  product.description,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Gap(8.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      pricingTier != null
                          ? '\$${pricingTier.price.toStringAsFixed(0)}'
                          : 'Price not available',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontSize: 14,
                            color: Colors.red.shade700,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 16.w,
                        ),
                        Gap(2.w),
                        Text(
                          '${(Random().nextDouble() * 2 + 3).toStringAsFixed(1)}',
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    color: Color(0xFF687588),
                                  ),
                        ),
                      ],
                    ),
                  ],
                ),
                Gap(8.h),
                Text(
                  pricingTier != null
                      ? 'from ${pricingTier.price.toStringAsFixed(0)} ${pricingTier.unit}'
                      : 'Price not available',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                        fontSize: 12.sp,
                      ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
