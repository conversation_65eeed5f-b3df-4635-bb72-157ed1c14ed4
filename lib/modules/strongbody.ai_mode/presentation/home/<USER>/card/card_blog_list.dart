import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/model/blog_model.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardBlogList extends StatelessWidget {
  const CardBlogList({super.key, required this.blog});

  final BlogModel blog;

  @override
  Widget build(BuildContext context) {
    final sizeWidth = MediaQuery.of(context).size.width;
    final sizeHeight = MediaQuery.of(context).size.height;
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.padding8, vertical: AppSpacing.padding16h),
      decoration: BoxDecoration(
        border: Border(
            bottom: BorderSide(
                color: Theme.of(context).greyScale200(context), width: 1)),
      ),
      child: Row(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.r),
                child: CustomImage(
                  path: blog.images.isNotEmpty && blog.images[0].isNotEmpty
                      ? blog.images[0]
                      : AppAssets.servicePlaceholder,
                  width: sizeWidth * 0.4,
                  height: sizeHeight * 0.14,
                  fit: BoxFit.cover,
                  imageType: blog.images.isNotEmpty && blog.images[0].isNotEmpty
                      ? ImageType.network
                      : ImageType.assetImage,
                ),
              ),
              Positioned(
                  top: 2,
                  right: 6,
                  child: SvgPicture.asset(
                    AppAssets.heart_icon,
                    color: Theme.of(context).whitePrimary(context),
                  ))
            ],
          ),
          Gap(10.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                    text: TextSpan(children: [
                  TextSpan(
                    text: 'by ',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodySmallMedium
                        .copyWith(
                          color: Theme.of(context).greyScale500(context),
                        ),
                  ),
                  TextSpan(
                      text: blog.authorName,
                      style: Theme.of(context).textTheme.lightBodyLargeMedium),
                ])),
                Gap(10.h),
                Text(
                  blog.title,
                  style: Theme.of(context).textTheme.lightBodyLargeBold,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                Gap(10.h),
                Text(
                  "${TFormatter.formatDate(blog.createdAt)}",
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(
                        color: Theme.of(context).greyScale500(context),
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
