import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

import '../../../../../core/components/round_image.dart';

class SBPromoSlider extends StatefulWidget {
  const SBPromoSlider({super.key});

  @override
  State<SBPromoSlider> createState() => _SBPromoSliderState();
}

class _SBPromoSliderState extends State<SBPromoSlider> {
  int _current = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            viewportFraction: 1,
            autoPlay: true,
            autoPlayInterval: Duration(seconds: 2),
            onPageChanged: (index, _) {
              setState(() {
                _current = index;
              });
            },
          ),
          items: banners
              .map(
                (banner) => SBRoundedImage(
                  isNetworkImage: true,
                  imageUrl: banner['imageUrl'],
                  onPressed: () {},
                ),
              )
              .toList(),
        ),
        Gap(8.h),
        Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              for (int i = 0; i < banners.length; i++)
                Container(
                  width: 20,
                  height: 4,
                  margin: const EdgeInsets.only(right: 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(400),
                    color: i == _current ? Colors.blue : Colors.grey,
                  ),
                )
            ],
          ),
        ),
      ],
    );
  }
}

List banners = [
  {
    'imageUrl':
        "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRVIj5UWYf8hqc0jkPATThpX5Pq1GxNbwMTrMP27OtxNwYCk6fyU5Ap5EcTFjjQVWv_Rqc&usqp=CAU",
    'targetScreen': '/screen1',
  },
  {
    'imageUrl':
        "https://img.freepik.com/premium-vector/bio-healthy-food-banner_23-**********.jpg",
    'targetScreen': '/screen2',
  },
  {
    'imageUrl':
        "https://img.freepik.com/free-vector/bio-healthy-food-banner_23-**********.jpg",
    'targetScreen': '/screen3',
  },
  // {
  //   'imageUrl': "${AppAssets.banner4Svg}",
  //   'targetScreen': '/screen4',
  // },
  // {
  //   'imageUrl': "${AppAssets.banner5Svg}",
  //   'targetScreen': '/screen5',
  // }
];
