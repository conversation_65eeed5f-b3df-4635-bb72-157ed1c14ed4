import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

import '../../../data/models/search/search_item.dart';
import '../search/bloc/search_home_strong_bloc.dart';

class SearchFilterStrongBodyPage extends StatelessWidget {
  const SearchFilterStrongBodyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
        title: const Text("Search"),
        backgroundColor: Colors.transparent,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Positioned.fill(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                color: Colors.black.withOpacity(0.15),
              ),
            ),
          ),

          // Search Card
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 100, left: 16, right: 16),
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.7,
                ),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Search Box
                      GestureDetector(
                        onTap: () =>
                            context.push(RouteName.searchHomeStrongBody),
                        child: Hero(
                          tag: 'search-box',
                          child: Material(
                            color: Colors.transparent,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 14),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Row(
                                children: const [
                                  Icon(Icons.search, color: Colors.black54),
                                  SizedBox(width: 10),
                                  Text(
                                    "Places, attractions, activities",
                                    style: TextStyle(color: Colors.black54),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Search History Section
                      BlocBuilder<SearHomeStrongBloc, SearchHomeStrongState>(
                        builder: (context, state) {
                          if (state.recentSearches.isEmpty) {
                            return const SizedBox.shrink();
                          }

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Divider(),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Recent Searches',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16.sp,
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        // Xóa tất cả lịch sử tìm kiếm
                                        context
                                            .read<SearHomeStrongBloc>()
                                            .add(ClearRecentSearchesEvent());
                                      },
                                      child: Text(
                                        'Clear All',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // History List
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: state.recentSearches.length > 5
                                    ? 5 // Giới hạn 5 items trong filter
                                    : state.recentSearches.length,
                                itemBuilder: (context, index) {
                                  final item = state.recentSearches[index];
                                  return _buildHistoryItem(context, item, index,(int index){
                                    context.read<SearHomeStrongBloc>().add(RemoveRecentSearchEvent(index));
                                  });
                                },
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(BuildContext context, SearchHomeItem item,int index, void Function(int) onDelete) {
    return Dismissible(
      key: ValueKey(item.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      onDismissed: (direction) {
       onDelete(index);
      },
      child: InkWell(
        onTap: () {
          // Khi tap vào item lịch sử, chuyển đến trang chi tiết
          if (item.type == 'service') {
            context.push(RouteName.serviceDetailSeller, extra: item.id);
          } else if (item.type == 'product') {
            context.push(RouteName.productDetailSeller, extra: item.id);
          } else {
            context.push(RouteName.profileView, extra: {'sellerId': item.id});
          }
        },
        child: Container(
          margin: EdgeInsets.only(bottom: 8.h),
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            color: Colors.grey[50],
          ),
          child: Row(
            children: [
              // Image
              ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: CustomImage(
                  path: item.images.isNotEmpty
                      ? item.images
                      : 'https://d1hjkbq40fs2x4.cloudfront.net/2016-01-31/files/1045-5.jpg',
                  imageType: ImageType.network,
                  width: 40.w,
                  height: 40.h,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(width: 12.w),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14.sp,
                      ),
                    ),
                    if (item.type.isNotEmpty) ...[
                      SizedBox(height: 2.h),
                      Text(
                        item.type,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12.sp,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // History Icon
              Icon(
                Icons.history,
                size: 16.w,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
