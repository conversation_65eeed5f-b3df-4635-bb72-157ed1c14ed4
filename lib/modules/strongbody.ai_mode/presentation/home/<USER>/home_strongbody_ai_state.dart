part of 'home_strongbody_ai_bloc.dart';

class HomeStrongbodyAiState extends Equatable {
  final bool isLoading;
  final String? errorMessage;
  final bool isSuccess;
  final bool isCancelled;
  final bool isError;

  // Loading states riêng cho từng API
  final bool isServiceLoading;
  final bool isServiceSuccess;
  final bool isProductLoading;
  final bool isProductSuccess;
  final bool isExpertLoading;
  final bool isExpertSuccess;
  final bool isBlogLoading;
  final bool isBlogSuccess;

  final List<ServiceModel> suggestServices;
  final List<ServiceModel> searchServiceResults;
  final List<ServiceModel> filterServiceResults;
  final String searchQuery;
  final List<int> selectedCategoryIds;
  final int selectedSortServiceIndex;
  final int? selectedServiceByCountryId;

  final List<ProductModel> featureProducts;
  final List<ProductModel> searchProductsResults;
  final List<int> selectedCategoryIdProducts;
  final int selectedSortProductIndex;

  final List<SellerProfileModel> seller;
  final List<SellerProfileModel> searchExpertsResults;
  final List<int> selectedExpertsOccupations;

  final List<BlogModel> blogs;
  final List<BlogModel> searchBlogsResults;
  final List<int> selectedCategoryIdBlogs;
  final int selectedSortIndexBlogs;

  final int selectedSortIndexProduct;
  final String selectedSortPriceProduct;

  final List<Country> filteredCountries;

  final bool isLoadingMore;
  final bool hasMore;
  final int currentServicePage;
  final int currentProductPage;
  final int currentExpertPage;
  final int currentBlogPage;
  final int serviceTypeIndex;
  final Set<int> loadedPages;

  const HomeStrongbodyAiState({
    this.isLoading = false,
    this.errorMessage,
    this.isSuccess = false,
    this.isCancelled = false,
    this.isError = false,

    // Loading states riêng cho từng API
    this.isServiceLoading = false,
    this.isServiceSuccess = false,
    this.isProductLoading = false,
    this.isProductSuccess = false,
    this.isExpertLoading = false,
    this.isExpertSuccess = false,
    this.isBlogLoading = false,
    this.isBlogSuccess = false,
    this.suggestServices = const [],
    this.featureProducts = const [],
    this.seller = const [],
    this.blogs = const [],
    this.isLoadingMore = false,
    this.hasMore = true,
    this.currentServicePage = 1,
    this.currentProductPage = 1,
    this.currentExpertPage = 1,
    this.currentBlogPage = 1,
    this.loadedPages = const {},
    this.serviceTypeIndex = 0,
    this.searchServiceResults = const [],
    this.searchQuery = '',
    this.filterServiceResults = const [],
    this.searchProductsResults = const [],
    this.selectedCategoryIds = const [],
    this.selectedCategoryIdProducts = const [],
    this.searchExpertsResults = const [],
    this.searchBlogsResults = const [],
    this.selectedCategoryIdBlogs = const [],
    this.selectedExpertsOccupations = const [],
    this.selectedSortIndexBlogs = 0,
    this.selectedSortIndexProduct = 0,
    this.selectedSortPriceProduct = '',
    this.selectedSortServiceIndex = 0,
    this.selectedServiceByCountryId = null,
    this.selectedSortProductIndex = 0,
    this.filteredCountries = const [],
  });

  HomeStrongbodyAiState copyWith({
    bool? isLoading,
    String? errorMessage,
    bool? isSuccess,
    bool? isCancelled,
    bool? isError,

    // Loading states riêng cho từng API
    bool? isServiceLoading,
    bool? isServiceSuccess,
    bool? isProductLoading,
    bool? isProductSuccess,
    bool? isExpertLoading,
    bool? isExpertSuccess,
    bool? isBlogLoading,
    bool? isBlogSuccess,
    List<ServiceModel>? suggestServices,
    List<ServiceModel>? searchServiceResults,
    String? searchQuery,
    List<ProductModel>? featureProducts,
    List<SellerProfileModel>? seller,
    List<BlogModel>? blogs,
    bool? isLoadingMore,
    bool? hasMore,
    int? currentServicePage,
    int? currentProductPage,
    int? currentExpertPage,
    int? currentBlogPage,
    Set<int>? loadedPages,
    int? serviceTypeIndex,
    List<ServiceModel>? filterServiceResults,
    List<ProductModel>? searchProductsResults,
    List<int>? selectedCategoryIds,
    List<int>? selectedCategoryIdProducts,
    List<SellerProfileModel>? searchExpertsResults,
    List<BlogModel>? searchBlogsResults,
    List<int>? selectedCategoryIdBlogs,
    List<int>? selectedExpertsOccupations,
    int? selectedSortIndexBlogs,
    int? selectedSortIndexProduct,
    String? selectedSortPriceProduct,
    int? selectedSortServiceIndex,
    int? selectedServiceByCountryId,
    int? selectedSortProductIndex,
    List<Country>? filteredCountries,
  }) {
    return HomeStrongbodyAiState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
      isCancelled: isCancelled ?? this.isCancelled,
      isError: isError ?? this.isError,

      // Loading states riêng cho từng API
      isServiceLoading: isServiceLoading ?? this.isServiceLoading,
      isServiceSuccess: isServiceSuccess ?? this.isServiceSuccess,
      isProductLoading: isProductLoading ?? this.isProductLoading,
      isProductSuccess: isProductSuccess ?? this.isProductSuccess,
      isExpertLoading: isExpertLoading ?? this.isExpertLoading,
      isExpertSuccess: isExpertSuccess ?? this.isExpertSuccess,
      isBlogLoading: isBlogLoading ?? this.isBlogLoading,
      isBlogSuccess: isBlogSuccess ?? this.isBlogSuccess,

      suggestServices: suggestServices ?? this.suggestServices,
      featureProducts: featureProducts ?? this.featureProducts,
      seller: seller ?? this.seller,
      blogs: blogs ?? this.blogs,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      currentServicePage: currentServicePage ?? this.currentServicePage,
      currentProductPage: currentProductPage ?? this.currentProductPage,
      currentExpertPage: currentExpertPage ?? this.currentExpertPage,
      currentBlogPage: currentBlogPage ?? this.currentBlogPage,
      loadedPages: loadedPages ?? this.loadedPages,
      serviceTypeIndex: serviceTypeIndex ?? this.serviceTypeIndex,
      searchServiceResults: searchServiceResults ?? this.searchServiceResults,
      searchQuery: searchQuery ?? this.searchQuery,
      filterServiceResults: filterServiceResults ?? this.filterServiceResults,
      searchProductsResults:
          searchProductsResults ?? this.searchProductsResults,
      selectedCategoryIds: selectedCategoryIds ?? this.selectedCategoryIds,
      selectedCategoryIdProducts:
          selectedCategoryIdProducts ?? this.selectedCategoryIdProducts,
      searchExpertsResults: searchExpertsResults ?? this.searchExpertsResults,
      searchBlogsResults: searchBlogsResults ?? this.searchBlogsResults,
      selectedCategoryIdBlogs:
          selectedCategoryIdBlogs ?? this.selectedCategoryIdBlogs,
      selectedExpertsOccupations:
          selectedExpertsOccupations ?? this.selectedExpertsOccupations,
      selectedSortIndexBlogs:
          selectedSortIndexBlogs ?? this.selectedSortIndexBlogs,
      selectedSortIndexProduct:
          selectedSortIndexProduct ?? this.selectedSortIndexProduct,
      selectedSortPriceProduct:
          selectedSortPriceProduct ?? this.selectedSortPriceProduct,
      selectedSortServiceIndex:
          selectedSortServiceIndex ?? this.selectedSortServiceIndex,
      selectedServiceByCountryId:
          selectedServiceByCountryId ?? this.selectedServiceByCountryId,
      selectedSortProductIndex:
          selectedSortProductIndex ?? this.selectedSortProductIndex,
      filteredCountries: filteredCountries ?? this.filteredCountries,
    );
  }

  @override
  // TODO: implement props
  List<Object?> get props => [
        isLoading,
        errorMessage,
        isSuccess,
        isCancelled,
        isError,

        // Loading states riêng cho từng API
        isServiceLoading,
        isServiceSuccess,
        isProductLoading,
        isProductSuccess,
        isExpertLoading,
        isExpertSuccess,
        isBlogLoading,
        isBlogSuccess,

        suggestServices,
        featureProducts,
        seller,
        blogs,
        isLoadingMore,
        hasMore,
        currentServicePage,
        currentProductPage,
        currentExpertPage,
        currentBlogPage,
        loadedPages,
        serviceTypeIndex,
        searchServiceResults,
        searchQuery,
        filterServiceResults,
        searchProductsResults,
        selectedCategoryIds,
        selectedCategoryIdProducts,
        searchExpertsResults,
        searchBlogsResults,
        selectedCategoryIdBlogs,
        selectedExpertsOccupations,
        selectedSortIndexBlogs,
        selectedSortIndexProduct,
        selectedSortPriceProduct,
        selectedSortServiceIndex,
        selectedServiceByCountryId,
        selectedSortProductIndex,
        filteredCountries,
      ];
}
