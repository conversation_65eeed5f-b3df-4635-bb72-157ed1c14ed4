// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:gap/gap.dart';
// import 'package:go_router/go_router.dart';
// import 'package:multime_app/app/routers/routers_name.dart';
// import 'package:multime_app/core/base/api_response/status.dart';
// import 'package:multime_app/core/constants/app_assets.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/strongbodyHome/bloc/services_home_bloc.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/strongbodyHome/bloc/services_home_state.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/strongbodyHome/widgets/carousel_slider_card.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_business_strong_body.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_connect_message.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_product_strong_body.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_service_strong_body.dart';
// import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
// import 'package:multime_app/shared/widgets/app_loader/loading_strongbody.dart';
// import 'package:shimmer/shimmer.dart';
// import '../../../../../core/di/locator.dart';
// import '../../../../../core/domain/storages/global_storage.dart';
// import '../../../../../shared/widgets/app_loader/404_strongbody.dart';
// import '../../../../../shared/widgets/app_loader/loading_simmer_widget.dart';
// import '../../../../news_mode/presentation/widgets/home_page/session_heading.dart';
// import '../../widgets/card_offer_strong_body.dart';

// class TabBarAllFake extends StatefulWidget {
//   const TabBarAllFake({super.key});

//   @override
//   State<TabBarAllFake> createState() => _TabBarAllFakeState();
// }

// class _TabBarAllFakeState extends State<TabBarAllFake> {
//   static const String _defaultAvatar =
//       "https://www.vinmec.com/static/uploads/small_20240102_081908_598785_bac_si_max_1800x1800_jpg_98dcc6cbf1.jpg";
//   static const String _placeholderImage =
//       'https://cdn.24h.com.vn/upload/2-2024/images/2024-05-17/2-1715936039-580-width740height480.png';

//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (getIt<GlobalStorage>().accessToken != null) ...[
//             _buildOfferCard1()
//           ],
//           // _buildSuggestedSection(state),
//           _buildSectionHeader(
//             'Recently Suggest Services',
//             onTap: () => context.push(RouteName.viewAllBuyerRequest),
//           ),
//           _buildSuggestedServicesList1(),
//           Gap(14.h),
//           _buildSectionHeader(
//             'Main services of the category',
//             onTap: () => context.push(RouteName.viewAllBuyerRequest),
//           ),
//           _buildServiceCategoriesSection1(),
//           Gap(8.h),
//           _buildTopExpertsSection(),
//           Gap(30.h),
//         ],
//       ),
//     );
//   }



//   Widget _buildOfferCard1() {
//     return CardOfferStrongBody(
//       title: 'microservices architecture for scalable applications',
//       description: 'microservices architecture for scalable applications',
//       image:
//       'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//       avatar:
//       'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//       auth: 'Alexander Smith',
//       days: '10',
//       price: 200,
//     );
//   }



//   Widget _buildSectionHeader(String title, {VoidCallback? onTap}) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 14.w),
//         child: TSectionHeading(
//           title: title,
//           showActionButton: true,
//           textColor: Colors.red,
//           buttonTitle: 'View All',
//         ),
//       ),
//     );
//   }

//   Widget _buildSuggestedServicesList(HomeStrongBodyState state) {
//     final items = state.suggestedSectionResponse.data?.items ?? [];

//     return ListView.separated(
//       itemCount: items.length,
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       padding: EdgeInsets.symmetric(horizontal: 14.w),
//       itemBuilder: (context, index) {
//         final item = items[index];
//         return GestureDetector(
//           onTap: () => context.push(RouteName.serviceDetail, extra: item),
//           child: CardServiceStrongBody(
//             image: item.imageUrl ?? '',
//             avatar: item.providerPhoto ?? '',
//             description: item.title ?? '',
//             auth: item.providerName ?? '',
//             price: item.price?.toInt() ?? 0,
//           ),
//         );
//       },
//       separatorBuilder: (context, index) => Gap(15.h),
//     );
//   }

//   Widget _buildSuggestedServicesList1() {
//     return ListView.separated(
//       itemCount: 5,
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       padding: EdgeInsets.symmetric(horizontal: 14.w),
//       itemBuilder: (context, index) {
//         return GestureDetector(
//           onTap: () => context.push(RouteName.serviceDetail, extra: ''),
//           child: CardServiceStrongBody(
//             image:
//             'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//             avatar:
//             'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//             description:
//             'I will be your diet manager to ensure proper nutrition for each meal',
//             auth: 'MarshalDev',
//             price: 200,
//           ),
//         );
//       },
//       separatorBuilder: (context, index) => Gap(15.h),
//     );
//   }

//   Widget _buildServiceCategoriesSection1() {
//     return CarouselSliderCard(
//       items: [
//         CardProductStrongBody(
//           image:
//           'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//           job: 'MarshalDev',
//           id: '1',
//         ),
//         CardProductStrongBody(
//           image:
//           'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//           job: 'MarshalDev',
//           id: '1',
//         ),
//         CardProductStrongBody(
//           image:
//           'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//           job: 'MarshalDev',
//           id: '1',
//         ),
//         CardProductStrongBody(
//           image:
//           'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//           job: 'MarshalDev',
//           id: '1',
//         ),
//         CardProductStrongBody(
//           image:
//           'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata',
//           job: 'MarshalDev',
//           id: '1',
//         ),
//       ],
//     );
//   }

//   Widget _buildBusinessCard() {
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 14.w),
//       child: const CardBusinessStrongBody(
//         image: AppAssets.bgCard,
//         label: "Invite friends for unlock",
//         description:
//         "If you connect with 15 people and they agree, you'll enjoy free global voice chats for one month.",
//       ),
//     );
//   }

//   Widget _buildTopExpertsSection() {
//     final hasExperts = 5 > 0;

//     if (!hasExperts) {
//       return const SizedBox.shrink();
//     }

//     return Column(
//       children: [
//         _buildBusinessCard(),
//         Gap(8.h),
//         _buildExpertsHeader(),
//         Gap(8.h),
//         _buildExpertsList(),
//       ],
//     );
//   }

//   Widget _buildExpertsHeader() {
//     return GestureDetector(
//       // onTap: () => _navigateToTopExperts(context, state),
//       child: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 14.w),
//         child: const TSectionHeading(
//           title: 'Top experts',
//           buttonTitle: "View all",
//           textColor: Colors.red,
//         ),
//       ),
//     );
//   }

//   Widget _buildExpertsList() {
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 14.w),
//       child: ListView.builder(
//         shrinkWrap: true,
//         physics: const NeverScrollableScrollPhysics(),
//         itemCount: 5,
//         itemBuilder: (context, index) {
//           return GestureDetector(
//             onTap: () => context.push(RouteName.profileView),
//             child: CardConnectMessage(
//               image: _getValidImageUrl("https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata"),
//               auth: 'MarshalDev',
//               job: 'MarshalDev',
//             ),
//           );
//         },
//       ),
//     );
//   }

//   // Helper methods
//   String _getValidImageUrl(String? imageUrl) {
//     if (imageUrl == null ||
//         imageUrl == 'No Image Found' ||
//         imageUrl == '<uri>' ||
//         imageUrl.isEmpty) {
//       return _defaultAvatar;
//     }
//     return imageUrl;
//   }

//   void _navigateToBuyerRequestDetail(
//       BuildContext context, HomeStrongBodyState state) {
//     context.push(
//       RouteName.buyerRequestDetail,
//       extra: state.buyerRequests?.items?.first.id,
//     );
//   }

//   void _navigateToSuggestedServices(
//       BuildContext context, HomeStrongBodyState state) {
//     context.push(RouteName.viewAllService, extra: {
//       "sec_id": state.suggestedSectionResponse.data?.sectionId ?? 'suggested',
//       "title": state.suggestedSectionResponse.data?.title,
//     });
//   }

//   void _navigateToTopExperts(BuildContext context, HomeStrongBodyState state) {
//     context.push(RouteName.viewAllService, extra: {
//       "sec_id": state.topSellersSection?.sectionId ?? 'top_sellers',
//       "title": state.topSellersSection?.title,
//     });
//   }
// }
