// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:gap/gap.dart';
// import 'package:go_router/go_router.dart';
// import 'package:multime_app/app/routers/routers_name.dart';
// import 'package:multime_app/core/base/api_response/status.dart';
// import 'package:multime_app/core/constants/app_assets.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/strongbodyHome/bloc/services_home_bloc.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/strongbodyHome/bloc/services_home_state.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/strongbodyHome/widgets/carousel_slider_card.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_business_strong_body.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_connect_message.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_product_strong_body.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/presentation/widgets/card_service_strong_body.dart';
// import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
// import 'package:multime_app/shared/widgets/app_loader/loading_strongbody.dart';
// import 'package:shimmer/shimmer.dart';
// import '../../../../../shared/widgets/app_loader/404_strongbody.dart';
// import '../../../../../shared/widgets/app_loader/loading_simmer_widget.dart';
// import '../../../../news_mode/presentation/widgets/home_page/session_heading.dart';
// import '../../widgets/card_offer_strong_body.dart';

// class TabBarAll extends StatefulWidget {
//   const TabBarAll({super.key});

//   @override
//   State<TabBarAll> createState() => _TabBarAllState();
// }

// class _TabBarAllState extends State<TabBarAll> {
//   static const String _defaultAvatar =
//       "https://www.vinmec.com/static/uploads/small_20240102_081908_598785_bac_si_max_1800x1800_jpg_98dcc6cbf1.jpg";
//   static const String _placeholderImage =
//       'https://cdn.24h.com.vn/upload/2-2024/images/2024-05-17/2-1715936039-580-width740height480.png';

//   @override
//   Widget build(BuildContext context) {
//     return BlocConsumer<HomeStrongBodyBloc, HomeStrongBodyState>(
//       listener: _handleStateListener,
//       builder: (context, state) {
//         return SingleChildScrollView(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               _buildBuyerRequestsSection(state),
//               _buildSuggestedSection(state),
//               Gap(14.h),
//               _buildServiceCategoriesSection(state),
//               Gap(8.h),
//               _buildTopExpertsSection(state),
//               Gap(24.h),
//               _buildFreelanceSection(state),
//               Gap(30.h),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   void _handleStateListener(BuildContext context, HomeStrongBodyState state) {
//     if (state.serviceHomeModel.status == Status.loading) {
//       AppLoader.show(context);
//     } else {
//       AppLoader.hide();
//     }
//   }

//   Widget _buildBuyerRequestsSection(HomeStrongBodyState state) {
//     final hasBuyerRequests = (state.buyerRequests?.items?.length ?? 0) > 0;

//     if (!hasBuyerRequests) {
//       return const SizedBox.shrink();
//     }

//     return Column(
//       children: [
//         Gap(12.h),
//         Padding(
//           padding: EdgeInsets.symmetric(horizontal: 14.w),
//           child: GestureDetector(
//             onTap: () => _navigateToBuyerRequestDetail(context, state),
//             child: _buildOfferCard(state),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildOfferCard(HomeStrongBodyState state) {
//     final firstRequest = state.buyerRequests?.items?.first;

//     return CardOfferStrongBody(
//       title: firstRequest?.title ?? '',
//       description: firstRequest?.description ?? '',
//       image: _placeholderImage,
//       avatar: _placeholderImage,
//       auth: 'Alexander Smith',
//       days: '10',
//       price: firstRequest?.budget ?? 0,
//     );
//   }

//   Widget _buildSuggestedSection(HomeStrongBodyState state) {
//     switch (state.suggestedSectionResponse.status) {
//       case Status.error:
//         return const PageNotFound();
//       case Status.loading:
//         return Padding(
//             padding: EdgeInsets.all(10.w),
//             child: Column(
//               children: [
//                 LoadingSimmerSB(
//                   height: 250.h,
//                   width: double.infinity,
//                 ),
//                 LoadingSimmerSB(
//                   height: 250.h,
//                   width: double.infinity,
//                 ),
//                 LoadingSimmerSB(
//                   height: 250.h,
//                   width: double.infinity,
//                 ),
//               ],
//             ));
//       case Status.completed:
//         return _buildSuggestedContent(state);
//       default:
//         return const SizedBox.shrink();
//     }
//   }

//   Widget _buildSuggestedContent(HomeStrongBodyState state) {
//     return Column(
//       children: [
//         _buildSectionHeader(
//           'My Requests',
//           onTap: () => context.push(RouteName.viewAllBuyerRequest),
//         ),
//         _buildSectionHeader(
//           'Recently Suggest Services',
//           onTap: () => _navigateToSuggestedServices(context, state),
//         ),
//         Gap(12.h),
//         _buildSuggestedServicesList(state),
//       ],
//     );
//   }

//   Widget _buildSectionHeader(String title, {VoidCallback? onTap}) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 14.w),
//         child: TSectionHeading(
//           title: title,
//           showActionButton: true,
//           textColor: Colors.red,
//           buttonTitle: 'View All',
//         ),
//       ),
//     );
//   }

//   Widget _buildSuggestedServicesList(HomeStrongBodyState state) {
//     final items = state.suggestedSectionResponse.data?.items ?? [];

//     return ListView.separated(
//       itemCount: items.length,
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       padding: EdgeInsets.symmetric(horizontal: 14.w),
//       itemBuilder: (context, index) {
//         final item = items[index];
//         return GestureDetector(
//           onTap: () => context.push(RouteName.serviceDetail, extra: item),
//           child: CardServiceStrongBody(
//             image: item.imageUrl ?? '',
//             avatar: item.providerPhoto ?? '',
//             description: item.title ?? '',
//             auth: item.providerName ?? '',
//             price: item.price?.toInt() ?? 0,
//           ),
//         );
//       },
//       separatorBuilder: (context, index) => Gap(15.h),
//     );
//   }

//   Widget _buildServiceCategoriesSection(HomeStrongBodyState state) {
//     if (state.serviceHomeModel.status == Status.error) {
//       return Center(
//         child: Text(
//           state.serviceHomeModel.message ??
//               'Error loading main services of the category',
//         ),
//       );
//     }

//     if (state.serviceHomeModel.status == Status.completed &&
//         state.serviceHomeModel.data?.categories != null) {
//       return CarouselSliderCard(
//         items: state.serviceHomeModel.data!.categories!.map((item) {
//           return Padding(
//             padding: EdgeInsets.symmetric(horizontal: 8.w),
//             child: CardProductStrongBody(
//               image: item.imageUrl!,
//               job: item.name!,
//               id: item.id!,
//             ),
//           );
//         }).toList(),
//       );
//     }

//     return const SizedBox.shrink();
//   }

//   Widget _buildBusinessCard() {
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 14.w),
//       child: const CardBusinessStrongBody(
//         image: AppAssets.bgCard,
//         label: "Invite friends for unlock",
//         description:
//         "If you connect with 15 people and they agree, you'll enjoy free global voice chats for one month.",
//       ),
//     );
//   }

//   Widget _buildTopExpertsSection(HomeStrongBodyState state) {
//     final hasExperts = (state.topSellersSection?.sellers?.length ?? 0) > 0;

//     if (!hasExperts) {
//       return const SizedBox.shrink();
//     }

//     return Column(
//       children: [
//         _buildBusinessCard(),
//         Gap(8.h),
//         _buildExpertsHeader(state),
//         Gap(8.h),
//         _buildExpertsList(state),
//       ],
//     );
//   }

//   Widget _buildExpertsHeader(HomeStrongBodyState state) {
//     return GestureDetector(
//       onTap: () => _navigateToTopExperts(context, state),
//       child: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 14.w),
//         child: const TSectionHeading(
//           title: 'Top experts',
//           buttonTitle: "View all",
//           textColor: Colors.red,
//         ),
//       ),
//     );
//   }

//   Widget _buildExpertsList(HomeStrongBodyState state) {
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 14.w),
//       child: ListView.builder(
//         shrinkWrap: true,
//         physics: const NeverScrollableScrollPhysics(),
//         itemCount: state.topSellersSection?.sellers?.length ?? 0,
//         itemBuilder: (context, index) {
//           final expert = state.topSellersSection!.sellers![index];
//           return GestureDetector(
//             onTap: () => context.push(RouteName.profileView),
//             child: CardConnectMessage(
//               image: _getValidImageUrl(expert.photo),
//               auth: expert.name ?? '',
//               job: expert.categoryName ?? '',
//             ),
//           );
//         },
//       ),
//     );
//   }

//   Widget _buildFreelanceSection(HomeStrongBodyState state) {
//     if (state.freelanceSectionResponse.status == Status.error) {
//       return Center(
//         child: Text(
//           state.freelanceSectionResponse.message ??
//               'Error loading freelance section',
//         ),
//       );
//     }

//     if (state.freelanceSectionResponse.status == Status.completed) {
//       return Column(
//         children: [
//           Padding(
//             padding: EdgeInsets.symmetric(horizontal: 14.w),
//             child: const TSectionHeading(
//               title: "Freelance Works",
//               showActionButton: false,
//             ),
//           ),
//           Gap(12.h),
//           _buildFreelanceCarousel(state),
//         ],
//       );
//     }

//     return const SizedBox.shrink();
//   }

//   Widget _buildFreelanceCarousel(HomeStrongBodyState state) {
//     final items = state.freelanceSectionResponse.data?.items ?? [];

//     return CarouselSliderCard(
//       items: items.map((item) {
//         return Padding(
//           padding: EdgeInsets.symmetric(horizontal: 8.w),
//           child: CardProductStrongBody(
//             image: item.imageUrl ?? '',
//             job: item.title ?? '',
//             id: item.id!,
//           ),
//         );
//       }).toList(),
//     );
//   }

//   // Helper methods
//   String _getValidImageUrl(String? imageUrl) {
//     if (imageUrl == null ||
//         imageUrl == 'No Image Found' ||
//         imageUrl == '<uri>' ||
//         imageUrl.isEmpty) {
//       return _defaultAvatar;
//     }
//     return imageUrl;
//   }

//   void _navigateToBuyerRequestDetail(
//       BuildContext context, HomeStrongBodyState state) {
//     context.push(
//       RouteName.buyerRequestDetail,
//       extra: state.buyerRequests?.items?.first.id,
//     );
//   }

//   void _navigateToSuggestedServices(
//       BuildContext context, HomeStrongBodyState state) {
//     context.push(RouteName.viewAllService, extra: {
//       "sec_id": state.suggestedSectionResponse.data?.sectionId ?? 'suggested',
//       "title": state.suggestedSectionResponse.data?.title,
//     });
//   }

//   void _navigateToTopExperts(BuildContext context, HomeStrongBodyState state) {
//     context.push(RouteName.viewAllService, extra: {
//       "sec_id": state.topSellersSection?.sectionId ?? 'top_sellers',
//       "title": state.topSellersSection?.title,
//     });
//   }
// }