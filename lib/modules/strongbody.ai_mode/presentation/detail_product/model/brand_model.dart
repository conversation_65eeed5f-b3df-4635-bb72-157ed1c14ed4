class BrandModel {
  final int id;
  final String name;
  final String description;
  final String? logo;
  final String? website;
  final String? email;
  final String? phone;
  final String? address;
  final String? countryOfOrigin;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  BrandModel({
    required this.id,
    required this.name,
    required this.description,
    this.logo,
    this.website,
    this.email,
    this.phone,
    this.address,
    this.countryOfOrigin,
    required this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  factory BrandModel.fromJson(Map<String, dynamic> json) {
    return BrandModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      logo: json['logo'],
      website: json['website'],
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      countryOfOrigin: json['country_of_origin'],
      isActive: json['is_active'] ?? false,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logo': logo,
      'website': website,
      'email': email,
      'phone': phone,
      'address': address,
      'country_of_origin': countryOfOrigin,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  BrandModel copyWith({
    int? id,
    String? name,
    String? description,
    String? logo,
    String? website,
    String? email,
    String? phone,
    String? address,
    String? countryOfOrigin,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BrandModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      logo: logo ?? this.logo,
      website: website ?? this.website,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      countryOfOrigin: countryOfOrigin ?? this.countryOfOrigin,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'BrandModel(id: $id, name: $name, description: $description, logo: $logo, website: $website, email: $email, phone: $phone, address: $address, countryOfOrigin: $countryOfOrigin, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BrandModel &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.logo == logo &&
        other.website == website &&
        other.email == email &&
        other.phone == phone &&
        other.address == address &&
        other.countryOfOrigin == countryOfOrigin &&
        other.isActive == isActive &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      logo,
      website,
      email,
      phone,
      address,
      countryOfOrigin,
      isActive,
      createdAt,
      updatedAt,
    );
  }
}
