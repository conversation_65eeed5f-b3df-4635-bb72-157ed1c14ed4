import 'package:multime_app/core/model/category_model.dart';
import 'package:multime_app/core/model/shop/shop_model.dart';
import 'package:multime_app/shared/models/user/user_model.dart';
import 'brand_model.dart';

class ProductModel {
  final int id;
  final String name;
  final String description;
  final String sku;
  final int categoryId;
  final int userId;
  final UserModel? user;
  final double price;
  final String brandtext;
  final int brandId;
  final BrandModel? brand;
  final bool isActive;
  final DateTime? expiryDate;
  final DateTime? manufactureDate;
  final List<String> image;
  final String countryOfOrigin;
  final String licenseNo;
  final String? licenseFile;
  final List<PricingTier> pricingTiers;
  final DateTime? createdAt;
  final String? createdBy;
  final CategoryModel? category;
  final double? rating;
  final int shopId;
  final ShopModel? shop;

  ProductModel({
    required this.id,
    required this.name,
    required this.description,
    required this.sku,
    required this.categoryId,
    required this.userId,
    required this.brandtext,
    required this.brandId,
    this.brand,
    required this.isActive,
    this.expiryDate,
    this.manufactureDate,
    required this.image,
    required this.countryOfOrigin,
    required this.licenseNo,
    this.licenseFile,
    required this.pricingTiers,
    this.createdAt,
    this.createdBy,
    this.user,
    required this.price,
    this.category,
    this.rating,
    required this.shopId,
    this.shop,
  });

  static List<String> _parseImageField(dynamic imageData) {
    if (imageData == null) return [];
    if (imageData is List) {
      return List<String>.from(imageData);
    }
    if (imageData is String) {
      return imageData.isEmpty ? [] : [imageData];
    }
    return [];
  }

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    ShopModel? shop;
    try {
      shop = json['shop'] != null ? ShopModel.fromJson(json['shop']) : null;
    } catch (e) {
      print('🔥 Shop parse error: $e');
      shop = null;
    }

    return ProductModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      sku: json['sku'] ?? '',
      categoryId: json['category_id'] ?? 0,
      userId: json['user_id'] ?? 0,
      brandtext: json['brandtext'] ?? '',
      brandId: json['brand_id'] ?? 0,
      brand: json['brand'] != null ? BrandModel.fromJson(json['brand']) : null,
      isActive: json['is_active'] ?? false,
      expiryDate: json['expiry_date'] != null
          ? DateTime.tryParse(json['expiry_date'])
          : null,
      manufactureDate: json['manufacture_date'] != null
          ? DateTime.tryParse(json['manufacture_date'])
          : null,
      image: _parseImageField(json['image']),
      countryOfOrigin: json['country_of_origin'] ?? '',
      licenseNo: json['license_no'] ?? '',
      licenseFile: json['license_file'],
      pricingTiers: (json['pricing_tiers'] as List)
          .map((e) => PricingTier.fromJson(e))
          .toList(),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      createdBy: json['created_by'],
      user: UserModel.fromJson(json['user']),
      price: json['price'] != null ? (json['price'] as num).toDouble() : 0.0,
      rating:
          json['rating'] != null ? (json['rating'] as num).toDouble() : null,
      category: CategoryModel.fromJson(json['category']),
      shopId: json['shop_id'] ?? 0,
      shop: shop,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sku': sku,
      'category_id': categoryId,
      'user_id': userId,
      'brandtext': brandtext,
      'brand_id': brandId,
      'brand': brand?.toJson(),
      'is_active': isActive,
      'expiry_date': expiryDate?.toIso8601String(),
      'manufacture_date': manufactureDate?.toIso8601String(),
      'image': image,
      'country_of_origin': countryOfOrigin,
      'license_no': licenseNo,
      'license_file': licenseFile,
      'pricing_tiers': pricingTiers.map((e) => e.toJson()).toList(),
      'created_at': createdAt?.toIso8601String(),
      'created_by': createdBy,
      'user': user?.toJson(),
      'price': price,
      'category': category?.toJson(),
      'rating': rating,
      'shop_id': shopId,
      'shop': shop,
    };
  }

  ProductModel copyWith({
    int? id,
    String? name,
    String? description,
    String? sku,
    int? categoryId,
    int? userId,
    String? brandtext,
    int? brandId,
    BrandModel? brand,
    bool? isActive,
    DateTime? expiryDate,
    DateTime? manufactureDate,
    List<String>? image,
    String? countryOfOrigin,
    String? licenseNo,
    String? licenseFile,
    List<PricingTier>? pricingTiers,
    DateTime? createdAt,
    String? createdBy,
    UserModel? user,
    double? price,
    CategoryModel? category,
    double? rating,
    int? shopId,
    ShopModel? shop,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      sku: sku ?? this.sku,
      categoryId: categoryId ?? this.categoryId,
      userId: userId ?? this.userId,
      brandtext: brandtext ?? this.brandtext,
      brandId: brandId ?? this.brandId,
      brand: brand ?? this.brand,
      isActive: isActive ?? this.isActive,
      expiryDate: expiryDate ?? this.expiryDate,
      manufactureDate: manufactureDate ?? this.manufactureDate,
      image: image ?? this.image,
      countryOfOrigin: countryOfOrigin ?? this.countryOfOrigin,
      licenseNo: licenseNo ?? this.licenseNo,
      licenseFile: licenseFile ?? this.licenseFile,
      pricingTiers: pricingTiers ?? this.pricingTiers,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      user: user ?? this.user,
      price: price ?? this.price,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      shopId: shopId ?? this.shopId,
      shop: shop ?? this.shop,
    );
  }

  static ProductModel empty() {
    return ProductModel(
      id: 0,
      name: '',
      description: '',
      sku: '',
      categoryId: 0,
      userId: 0,
      brandtext: '',
      brandId: 0,
      brand: null,
      isActive: false,
      expiryDate: null,
      manufactureDate: null,
      image: [],
      countryOfOrigin: '',
      licenseNo: '',
      licenseFile: null,
      pricingTiers: [],
      createdAt: null,
      createdBy: null,
      user: null,
      price: 0,
      category: null,
      rating: 0.0,
      shopId: 0,
      shop: null,
    );
  }

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, description: $description, sku: $sku, categoryId: $categoryId, userId: $userId, brandtext: $brandtext, brandId: $brandId, brand: $brand, isActive: $isActive, expiryDate: $expiryDate, manufactureDate: $manufactureDate, image: $image, countryOfOrigin: $countryOfOrigin, licenseNo: $licenseNo, licenseFile: $licenseFile, pricingTiers: $pricingTiers, createdAt: $createdAt, createdBy: $createdBy)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.sku == sku &&
        other.categoryId == categoryId &&
        other.userId == userId &&
        other.brandtext == brandtext &&
        other.brandId == brandId &&
        other.brand == brand &&
        other.isActive == isActive &&
        other.expiryDate == expiryDate &&
        other.manufactureDate == manufactureDate &&
        other.image.toString() == image.toString() &&
        other.countryOfOrigin == countryOfOrigin &&
        other.licenseNo == licenseNo &&
        other.licenseFile == licenseFile &&
        other.pricingTiers.toString() == pricingTiers.toString() &&
        other.createdAt == createdAt &&
        other.createdBy == createdBy &&
        other.user == user &&
        other.price == price;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      sku,
      categoryId,
      userId,
      brandtext,
      brandId,
      brand,
      isActive,
      expiryDate,
      manufactureDate,
      image,
      countryOfOrigin,
      licenseNo,
      licenseFile,
      pricingTiers,
      createdAt,
      createdBy,
    );
  }
}

class PricingTier {
  final int id;
  final int productId;
  final double price;
  final String unit;
  final String description;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int stockQuantity;

  PricingTier({
    required this.id,
    required this.productId,
    required this.price,
    required this.unit,
    required this.stockQuantity,
    required this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory PricingTier.fromJson(Map<String, dynamic> json) {
    return PricingTier(
      id: json['id'] ?? 0,
      productId: json['product_id'] ?? 0,
      price: (json['price'] ?? 0).toDouble(),
      unit: json['unit'] ?? '',
      description: json['description'] ?? '',
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
      stockQuantity: json['stock_quantity'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'price': price,
      'unit': unit,
      'description': description,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'stock_quantity': stockQuantity,
    };
  }

  PricingTier copyWith({
    int? id,
    int? productId,
    double? price,
    String? unit,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? stockQuantity,
  }) {
    return PricingTier(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      price: price ?? this.price,
      unit: unit ?? this.unit,
      description: description ?? this.description,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'PricingTier(id: $id, productId: $productId, price: $price, unit: $unit, description: $description, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PricingTier &&
        other.id == id &&
        other.productId == productId &&
        other.price == price &&
        other.unit == unit &&
        other.description == description &&
        other.stockQuantity == stockQuantity &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      productId,
      price,
      unit,
      stockQuantity,
      description,
      createdAt,
      updatedAt,
    );
  }
}
