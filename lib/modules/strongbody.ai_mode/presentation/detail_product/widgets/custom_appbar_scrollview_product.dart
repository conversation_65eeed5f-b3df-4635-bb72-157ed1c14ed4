import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/appbar_detail_product.dart';

import '../model/product_model.dart';

class CustomAppbarScrollviewProduct extends StatelessWidget {
  final TabController tabController;
  final void Function(int) onTabTap;
  final bool isCollapsed;
  final double expandedHeight;
  final List<String> images;
  final int currentImage;
  final void Function(int) onCarouselChanged;
  final ProductModel? productModel;

  const CustomAppbarScrollviewProduct({
    Key? key,
    required this.tabController,
    required this.onTabTap,
    required this.isCollapsed,
    required this.expandedHeight,
    required this.images,
    required this.currentImage,
    required this.onCarouselChanged,
    this.productModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Filter valid image URLs
    final validImages = images
        .where((banner) => banner.isNotEmpty && Uri.tryParse(banner) != null)
        .toList();

    return SliverAppBar(
      pinned: true,
      floating: false,
      expandedHeight: expandedHeight.h,
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      title:
          isCollapsed ? AppbarDetailProduct(productModel: productModel) : null,
      bottom: isCollapsed
          ? PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: Container(
                color: Colors.white,
                child: TabBar(
                  controller: tabController,
                  labelColor: Theme.of(context).primary(context),
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: Theme.of(context).textTheme.lightBodyLargeBold,
                  unselectedLabelStyle:
                      Theme.of(context).textTheme.lightBodyLargeRegular,
                  indicatorColor: Theme.of(context).primary(context),
                  indicatorWeight: 3,
                  isScrollable: true,
                  tabAlignment: TabAlignment.start,
                  dividerColor: Colors.transparent,
                  padding: EdgeInsets.zero,
                  onTap: onTabTap,
                  tabs: const [
                    Tab(text: 'About Product'),
                    Tab(text: 'Product Description'),
                    Tab(text: 'Review'),
                  ],
                ),
              ),
            )
          : null,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            Positioned.fill(
              child: validImages.isEmpty
                  ? Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey,
                          size: 80,
                        ),
                      ),
                    )
                  : CarouselSlider(
                      options: CarouselOptions(
                        viewportFraction: 1,
                        height: double.infinity,
                        onPageChanged: (index, _) => onCarouselChanged(index),
                      ),
                      items: validImages
                          .map(
                            (banner) => Image.network(
                              banner,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                print(
                                    'Image loading error for URL: $banner - Error: $error');
                                return Container(
                                  color: Colors.grey[200],
                                  child: const Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey,
                                      size: 50,
                                    ),
                                  ),
                                );
                              },
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              },
                            ),
                          )
                          .toList(),
                    ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 25,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (int i = 0; i < validImages.length; i++)
                    Container(
                      width: 10,
                      height: 10,
                      margin: const EdgeInsets.only(right: 10),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: i == currentImage
                            ? Theme.of(context).alertInformationBase(context)
                            : Theme.of(context).greyScale300(context),
                      ),
                    )
                ],
              ),
            ),
            if (!isCollapsed)
              const Positioned(
                top: 0,
                left: 16,
                right: 16,
                child: AppbarDetailProduct(isBackground: true),
              ),
          ],
        ),
      ),
    );
  }
}
