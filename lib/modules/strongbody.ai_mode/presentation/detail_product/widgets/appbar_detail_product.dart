import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../model/product_model.dart';

class AppbarDetailProduct extends StatelessWidget {
  const AppbarDetailProduct({this.isBackground = false, this.productModel});
  final bool isBackground;
  final ProductModel? productModel;
  @override
  Widget build(BuildContext context) {
    Widget _buildIconButton(String assetName, VoidCallback onPressed) {
      return IconButton(
        style: IconButton.styleFrom(
          backgroundColor: isBackground
              ? Theme.of(context).secondary(context).withOpacity(0.5)
              : null,
        ),
        icon: SvgPicture.asset(assetName,
            color: isBackground
                ? Theme.of(context).whitePrimary(context)
                : Theme.of(context).blackPrimary(context)),
        onPressed: onPressed,
      );
    }

    return AppBar(
      backgroundColor: Colors.transparent,
      leading: _buildIconButton(AppAssets.arrowLeftSvg, () => context.pop()),
      actions: [
        _buildIconButton(AppAssets.messageAppbarSvg, () {
          context.push(
            RouteName.chatPage,
            extra: {
              "uid": productModel?.user?.id ?? 0,
              "name": productModel?.user?.fullName ?? "",
              "avatar": productModel?.user?.profilePicture ?? "",
              "isOnline": false,
            },
          );
        }),
        _buildIconButton(AppAssets.heart_icon, () {}),
        _buildIconButton(AppAssets.exportSvg, () {}),
        _buildIconButton(AppAssets.shoppingCart, () {}),
      ],
    );
  }
}
