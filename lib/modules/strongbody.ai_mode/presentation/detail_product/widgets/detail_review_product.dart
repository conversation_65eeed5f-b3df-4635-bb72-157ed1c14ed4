import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';

class DetailReviewProduct extends StatelessWidget {
  const DetailReviewProduct({super.key, required this.productModel});

  final ProductModel productModel;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SvgPicture.asset(
              AppAssets.noteFavoriteSvg,
            ),
            Gap(5.w),
            Text(
              'Reviews',
              style: Theme.of(context).textTheme.lightBodyLargeBold,
            ),
          ],
        ),
        Gap(16.h),
        Container(
          padding: EdgeInsets.only(bottom: AppSpacing.padding20h),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1,
                color: Theme.of(context).attentionLight(context),
              ),
            ),
          ),
          child: Row(
            children: [
              RichText(
                  text: TextSpan(
                text: '${productModel.rating!.toStringAsFixed(1)}',
                style: Theme.of(context)
                    .textTheme
                    .lightHeadingMediumLarge
                    .copyWith(
                      color: Theme.of(context).alertAttentionBase(context),
                    ),
                children: [
                  TextSpan(
                    text: '/5',
                    style: Theme.of(context).textTheme.lightBodySmallRegular,
                  ),
                ],
              )),
              Gap(10.w),
              Column(
                children: [
                  Text('Satisfied',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallSemiBold
                          .copyWith(
                            color:
                                Theme.of(context).alertAttentionBase(context),
                          )),
                  Text('${Random().nextInt(30) + 50} reviews',
                      style:
                          Theme.of(context).textTheme.lightBodyXSmallRegular),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}
