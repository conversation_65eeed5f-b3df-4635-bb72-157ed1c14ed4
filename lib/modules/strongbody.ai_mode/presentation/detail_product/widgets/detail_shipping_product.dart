import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class DetailShippingProduct extends StatelessWidget {
  const DetailShippingProduct({super.key});

  @override
  Widget build(BuildContext context) {
    Widget _buildRichText(String label, String value) {
      return RichText(
        text: TextSpan(
          text: '$label ',
          style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                color: Theme.of(context).greyScale500(context),
              ),
          children: [
            TextSpan(
              text: '  $value',
              style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(AppAssets.truckTimeSvg),
            Gap(5.w),
            Text(
              'Shipping',
              style: Theme.of(context).textTheme.lightBodyLargeBold,
            ),
          ],
        ),
        Gap(10.h),
        _buildRichText('Shipping total', '\$10-\$35'),
        Gap(10.h),
        _buildRichText('Delivery time', '5-7 days'),
        Gap(10.h),
      ],
    );
  }
}
