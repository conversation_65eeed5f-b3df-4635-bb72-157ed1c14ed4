import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;

import '../model/product_model.dart';

class DetailDescriptionProduct extends StatelessWidget {
  const DetailDescriptionProduct({super.key, required this.productModel});

  final ProductModel productModel;

  @override
  Widget build(BuildContext context) {
    String htmlToPlainText(String htmlString) {
      // Parse HTML
      dom.Document document = html_parser.parse(htmlString);

      // Extract text content
      String plainText = document.body?.text ?? '';

      // Clean up extra whitespace
      return plainText.replaceAll(RegExp(r'\s+'), ' ').trim();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(AppAssets.boxSearchSvg),
            SizedBox(width: 8.0),
            Text(
              'Product description',
              style: Theme.of(context).textTheme.lightBodyLargeBold,
            ),
          ],
        ),
        Gap(16.h),
        Text(
          htmlToPlainText(productModel.description),
          style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
        ),
      ],
    );
  }
}
