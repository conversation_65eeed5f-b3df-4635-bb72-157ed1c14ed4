import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/bloc/detail_service_bloc.dart';

class DetailServicePackage extends StatelessWidget {
  const DetailServicePackage({super.key});

  @override
  Widget build(BuildContext context) {
    List<String> detailSchedules = [
      'Staff Contact',
      'Preliminary Interview',
      'Comprehensive Examination',
      'Treatment',
      'Prescription',
    ];

    Widget _buildExpandedSection(String title, String content) {
      return Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1,
              color: Theme.of(context).attentionLight(context),
            ),
          ),
        ),
        child: ExpansionTile(
          title: Text(
            title,
            style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
          ),
          trailing: SvgPicture.asset(
            AppAssets.arrowRightSvg,
            color: Theme.of(context).blackPrimary(context),
          ),
          tilePadding: EdgeInsets.zero,
          childrenPadding: EdgeInsets.zero,
          children: [
            Text(
              content,
              style: Theme.of(context).textTheme.lightBodyLargeRegular,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(
              AppAssets.briefcaseSvg,
            ),
            Gap(5.w),
            Text(
              'Service Packages',
              style: Theme.of(context).textTheme.lightBodyLargeBold,
            ),
          ],
        ),
        Gap(16.h),
        BlocBuilder<DetailServiceBloc, DetailServiceState>(
          builder: (context, state) {
            final selectedIndex = state.selectedIndex;
            return SizedBox(
              height: 86.h,
              child: ListView.separated(
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        context.read<DetailServiceBloc>().add(
                              IsSelectedEvent(selectedIndex: index),
                            );
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).whitePrimary(context),
                          border: Border.all(
                              color: selectedIndex == index
                                  ? Theme.of(context)
                                      .alertInformationBase(context)
                                  : Theme.of(context).blackPrimary(context),
                              width: 2),
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius10),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(width: 8),
                            Text(
                              'Service Package ${index + 1}',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeSemiBold,
                            ),
                            Gap(5.h),
                            Text(
                              '\$345',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeSemiBold,
                            )
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) => Gap(10.h),
                  itemCount: 5),
            );
          },
        ),
        Gap(32.h),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).pinkScale50(context),
            borderRadius: BorderRadius.circular(AppRadius.radius10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'View detailed schedule',
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
              ),
              Gap(16.h),
              ...List.generate(
                detailSchedules.length,
                (index) => Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 24,
                      child: Text(
                        '${index + 1}.',
                        style:
                            Theme.of(context).textTheme.lightBodyLargeRegular,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        detailSchedules[index],
                        style:
                            Theme.of(context).textTheme.lightBodyLargeRegular,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        _buildExpandedSection('Before you book', 'Content'),
        _buildExpandedSection('Terms & Conditions', 'Content'),
        _buildExpandedSection('How to use', 'Content'),
      ],
    );
  }
}
