// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:gap/gap.dart';
// import 'package:multime_app/core/components/section_icon.dart';
// import 'package:multime_app/core/constants/app_assets.dart';
// import 'package:multime_app/core/themes/app_text_style.dart';
// import 'package:multime_app/core/themes/theme.dart';
// import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
// import 'package:readmore/readmore.dart';
// import 'package:html/parser.dart' as html_parser;
// import 'package:html/dom.dart' as dom;

// class DetailAboutService extends StatelessWidget {
//   const DetailAboutService({super.key, required this.serviceModel});

//   final ServiceModel serviceModel;

//   @override
//   Widget build(BuildContext context) {
//     String htmlToPlainText(String htmlString) {
//       // Parse HTML
//       dom.Document document = html_parser.parse(htmlString);

//       // Extract text content
//       String plainText = document.body?.text ?? '';

//       // Clean up extra whitespace
//       return plainText.replaceAll(RegExp(r'\s+'), ' ').trim();
//     }

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Row(
//           children: [
//             SvgPicture.asset(AppAssets.boxSearchSvg),
//             SizedBox(width: 8.0),
//             Text(
//               'About Service',
//               style: Theme.of(context).textTheme.lightBodyLargeBold,
//             ),
//           ],
//         ),
//         Gap(16.h),
//         Text(
//           'Description',
//           style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
//                 color: Theme.of(context).modeLightText(context),
//               ),
//         ),
//         Gap(10.h),
//         ReadMoreText(
//           htmlToPlainText(serviceModel.description),
//           trimLines: 7,
//           colorClickableText: Theme.of(context).alertInformationBase(context),
//           trimMode: TrimMode.Line,
//           trimCollapsedText: 'more',
//           trimExpandedText: 'less',
//           style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
//         ),
//         // Gap(16.h),
//         // Text(
//         //   'Service certifications',
//         //   style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
//         //         color: Theme.of(context).modeLightText(context),
//         //       ),
//         // ),
//         // Gap(10.h),
//         // Row(
//         //   children: [
//         //     Text('Picture.png'),
//         //     Gap(10.w),
//         //     Text('File.pdf'),
//         //   ],
//         // )
//       ],
//     );
//   }
// }
