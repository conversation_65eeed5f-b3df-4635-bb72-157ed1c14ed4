import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/components/section_title.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

class DetailDocumentProduct extends StatelessWidget {
  const DetailDocumentProduct({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
              width: 1, color: Theme.of(context).attentionLight(context)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SectionTitle(
              image: AppAssets.boxSearchSvg, title: 'Product Documentation'),
          Gap(16.h),
          Row(
            children: [
              _buildDocumentItem(
                context,
                AppAssets.imageDocumentSvg,
                'assets.doc',
                '5.3MB',
              ),
              Gap(10.w),
              _buildDocumentItem(
                context,
                AppAssets.imageSheetSvg,
                'assets.xls',
                '5.3MB',
              ),
            ],
          ),
          Gap(16.h),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(
      BuildContext context, String image, String name, String size) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(image),
        Gap(8.h),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 14.sp,
                  ),
            ),
            Gap(2.h),
            Text(
              size,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                    fontSize: 12.sp,
                  ),
            ),
          ],
        ),
      ],
    );
  }
}
