import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

import '../model/product_model.dart';

class BottomDetailProduct extends StatelessWidget {
  const BottomDetailProduct({super.key, required this.productModel});
  final ProductModel productModel;

  @override
  Widget build(BuildContext context) {
    Widget _buildButton(String text, VoidCallback onPressed, Color color) {
      return ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
          backgroundColor: color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
          ),
        ),
        child: Text(
          text,
          style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                color: Theme.of(context).whitePrimary(context),
              ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.0.h),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 4.0,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Text(
              //   '\$15:',
              //   style: Theme.of(context).textTheme.lightBodyXLargeBold,
              // ),
              // Spacer(),

              _buildButton(
                'Buy Now',
                () async {
                  debugPrint('Buy Now pressed');
                  final result =
                      await showUnitQuantityBottomSheet(context, productModel);
                  debugPrint('Bottom sheet result: $result');
                  if (result != null) {
                    debugPrint('Navigating to checkout with: $result');
                    context.pushNamed(
                      RouteName.checkoutProductPage,
                      extra: {
                        'product': productModel,
                        'quantity': result['quantity'],
                        'unit': result['unit'],
                        'total': result['total'],
                      },
                    );
                  } else {
                    debugPrint('Result is null, not navigating');
                  }
                },
                Theme.of(context).colorScheme.primary,
              )
            ],
          ),
          Gap(16.h),
        ],
      ),
    );
  }

  Future<Map<String, dynamic>?> showUnitQuantityBottomSheet(
    BuildContext context,
    ProductModel productModel,
  ) {
    final pricingTiers = productModel.pricingTiers;
    final units = pricingTiers.map((e) => e.unit).toSet().toList();

    final stockQuantity =
        pricingTiers.firstWhere((e) => e.unit == units.first).stockQuantity;

    if (units.isEmpty || stockQuantity == 0) return Future.value(null);

    String selectedUnit = units.first;
    int quantity = 1;
    final quantityController = TextEditingController(text: '1');

    return showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            final currentPricing = pricingTiers.firstWhere(
              (tier) => tier.unit == selectedUnit,
              orElse: () => pricingTiers.first,
            );

            double unitPrice = currentPricing.price;
            double total = unitPrice * quantity;

            void updateQuantity(String value) {
              final parsed = int.tryParse(value);
              if (parsed == null || parsed <= 0) {
                quantityController.text = '1';
                quantity = 1;
              } else if (parsed > stockQuantity) {
                quantityController.text = stockQuantity.toString();
                quantity = stockQuantity;
              } else {
                quantity = parsed;
              }
              setState(() {});
            }

            return Padding(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
                top: 16,
                bottom: MediaQuery.of(context).viewInsets.bottom + 60,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Select Unit & Quantity',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Product info
                  Row(
                    children: [
                      Image.network(
                        productModel.image[0],
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              productModel.name,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '\$${unitPrice.toStringAsFixed(2)} / $selectedUnit',
                              style: const TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Unit selection
                  Align(
                    alignment: Alignment.centerLeft,
                    child:
                        Text('Unit', style: TextStyle(color: Colors.grey[700])),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 48.0,
                    child: ListView.separated(
                      physics: const NeverScrollableScrollPhysics(),
                      scrollDirection: Axis.horizontal,
                      itemCount: units.length,
                      separatorBuilder: (_, __) => const SizedBox(width: 8),
                      itemBuilder: (context, index) {
                        final unit = units[index];
                        final isSelected = unit == selectedUnit;

                        return GestureDetector(
                          onTap: () {
                            selectedUnit = unit;
                            setState(() {});
                          },
                          child: Container(
                            width: 90.w,
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 10),
                            decoration: BoxDecoration(
                              color:
                                  isSelected ? Colors.red : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isSelected ? Colors.red : Colors.grey,
                                width: 1.2,
                              ),
                            ),
                            child: Text(
                              unit,
                              style: TextStyle(
                                color: isSelected ? Colors.white : Colors.black,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Quantity input
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text('Quantity (Max: $stockQuantity)',
                        style: TextStyle(color: Colors.grey[700])),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: quantity > 1
                            ? () {
                                quantity--;
                                quantityController.text = quantity.toString();
                                setState(() {});
                              }
                            : null,
                        icon: const Icon(Icons.remove),
                      ),
                      SizedBox(
                        width: 80,
                        child: TextField(
                          controller: quantityController,
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          onChanged: updateQuantity,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: quantity < stockQuantity
                            ? () {
                                quantity++;
                                quantityController.text = quantity.toString();
                                setState(() {});
                              }
                            : null,
                        icon: const Icon(Icons.add),
                      ),
                    ],
                  ),

                  const Divider(height: 32),

                  // Total and Button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Total',
                              style: TextStyle(color: Colors.grey[600])),
                          const SizedBox(height: 4),
                          Text(
                            '\$${total.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Expanded(
                        child: StrongBodyButton(
                          label: 'Buy Now',
                          onPressed: () {
                            Navigator.pop(context);
                            context.pushNamed(
                              RouteName.checkoutProductPage,
                              extra: {
                                'product': productModel,
                                'quantity': quantity,
                                'unit': selectedUnit,
                                'total': total,
                              },
                            );
                          },
                        ),
                      )
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
