import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/user_avatar.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';

class DetailContactProduct extends StatelessWidget {
  const DetailContactProduct({super.key, required this.productModel});

  final ProductModel productModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.w),
      color: Theme.of(context).pinkScale50(context),
      child: Row(
        children: [
          UserAvatar(
            size: 40,
            imageURL: (productModel.user?.profilePicture != null &&
                    productModel.user!.profilePicture!.isNotEmpty)
                ? productModel.user!.profilePicture!
                : 'https://img.tripi.vn/cdn-cgi/image/width=700,height=700/https://gcs.tripi.vn/public-tripi/tripi-feed/img/477744gWT/anh-mo-ta.png',
            isOnline: false,
          ),
          Gap(8.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 120.w,
                child: Text(
                  productModel.user!.fullName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style:
                      Theme.of(context).textTheme.lightBodyLargeBold.copyWith(
                            fontSize: 13,
                          ),
                ),
              ),
              Gap(5.h),
              Text('Active 3 min ago',
                  style: Theme.of(context).textTheme.lightBodySmallRegular),
              Text('United States',
                  style: Theme.of(context).textTheme.lightBodySmallRegular),
            ],
          ),
          const Spacer(),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              side: BorderSide(
                color: Theme.of(context).primary(context),
              ),
              backgroundColor: Theme.of(context).whitePrimary(context),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              context.push(
                RouteName.chatPage,
                extra: {
                  "uid": productModel.user?.id ?? 0,
                  "name": productModel.user?.fullName ?? "",
                  "avatar": productModel.user?.profilePicture ?? "",
                  "isOnline": false,
                },
              );
            },
            child: Row(
              children: [
                Text(
                  'Contact',
                  style:
                      Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                            color: Theme.of(context).primary(context),
                          ),
                ),
                Gap(5.w),
                SvgPicture.asset(
                  AppAssets.shopSvg,
                  width: 22.w,
                  height: 22.h,
                  color: Theme.of(context).primary(context),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
