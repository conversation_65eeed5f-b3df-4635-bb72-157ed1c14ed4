import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/widgets/listview_blog_trending.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/bloc/detail_product_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/bottom_detail_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/custom_appbar_scrollview_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/detail_contact_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/detail_description_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/detail_info_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/detail_review_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_feature_product.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

part 'detail_product_page.dart';
