part of 'detail_product_import.dart';

class DetailProductPage extends StatefulWidget {
  const DetailProductPage({super.key, required this.productModel});
  final ProductModel productModel;

  @override
  State<DetailProductPage> createState() => _DetailProductPageState();
}

class _DetailProductPageState extends State<DetailProductPage>
    with SingleTickerProviderStateMixin {
  int _current = 0;
  bool isCollapsed = false;
  late AutoScrollController scrollController;
  late TabController tabController;
  bool pauseRectGetterIndex = false;
  final double expandedHeight = 270.0;

  static const int sectionCount = 3;
  final List<GlobalKey> sectionKeys = List.generate(3, (_) => GlobalKey());

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: sectionCount, vsync: this);
    scrollController = AutoScrollController();
    scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    final double threshold = expandedHeight -
        kToolbarHeight -
        MediaQuery.of(context).padding.top -
        10;
    bool shouldCollapse =
        scrollController.hasClients && scrollController.offset > threshold;
    if (isCollapsed != shouldCollapse) {
      setState(() {
        isCollapsed = shouldCollapse;
      });
    }

    if (!pauseRectGetterIndex) {
      for (int i = sectionKeys.length - 1; i >= 0; i--) {
        final keyContext = sectionKeys[i].currentContext;
        if (keyContext != null) {
          final box = keyContext.findRenderObject();
          if (box is RenderBox) {
            final offset = box.localToGlobal(Offset.zero,
                ancestor: context.findRenderObject());
            if (offset.dy <= kToolbarHeight + 60) {
              if (tabController.index != i) {
                tabController.animateTo(i);
              }
              break;
            }
          }
        }
      }
    }
  }

  void animateAndScrollTo(int index) async {
    pauseRectGetterIndex = true;
    tabController.animateTo(index);
    await scrollController.scrollToIndex(index,
        preferPosition: AutoScrollPosition.begin);
    pauseRectGetterIndex = false;
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Extract actual PricingTier objects that match the productId
    final matchingPricingTiers = widget.productModel.pricingTiers
        .where((tier) => tier.productId == widget.productModel.id)
        .toList();

    // Get the first matching pricing tier for display
    final primaryPricingTier =
        matchingPricingTiers.isNotEmpty ? matchingPricingTiers.first : null;

    // For debugging - print the extracted pricing tiers
    print('Matching pricing tiers: ${matchingPricingTiers.length}');
    if (primaryPricingTier != null) {
      print('Primary pricing tier: ${primaryPricingTier.toString()}');
    }
    return BlocBuilder<DetailProductBloc, DetailProductState>(
        builder: (context, state) {
      final productLike = state.productLike
          .where((product) => product.id != widget.productModel.id)
          .toList();
      final productTrending = state.productTrending
          .where((product) => product.id != widget.productModel.id)
          .toList();
      return Scaffold(
        bottomNavigationBar: BottomDetailProduct(
          productModel: widget.productModel,
        ),
        body: CustomScrollView(
          controller: scrollController,
          slivers: [
            CustomAppbarScrollviewProduct(
              productModel: widget.productModel,
              tabController: tabController,
              onTabTap: animateAndScrollTo,
              isCollapsed: isCollapsed,
              expandedHeight: expandedHeight,
              images: widget.productModel.image,
              currentImage: _current,
              onCarouselChanged: (index) {
                setState(() {
                  _current = index;
                });
              },
            ),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.padding16,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap(16.h),
                        AutoScrollTag(
                          key: ValueKey(0),
                          controller: scrollController,
                          index: 0,
                          child: Container(
                            key: sectionKeys[0],
                            child: DetailInfoProduct(
                                productModel: widget.productModel),
                          ),
                        ),
                        Gap(16.h),
                        DetailContactProduct(productModel: widget.productModel),
                        Gap(16.h),
                        AutoScrollTag(
                            key: ValueKey(1),
                            controller: scrollController,
                            index: 1,
                            child: Container(
                              key: sectionKeys[1],
                              child: DetailDescriptionProduct(
                                  productModel: widget.productModel),
                            )),
                        Gap(16.h),
                        AutoScrollTag(
                          key: ValueKey(2),
                          controller: scrollController,
                          index: 2,
                          child: Container(
                            key: sectionKeys[2],
                            child: DetailReviewProduct(
                                productModel: widget.productModel),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(16.h),
                  ListViewGeneric<ProductModel>(
                    isLoading: state.isLoading,
                    items: productLike,
                    onItemTap: (product) => context
                        .push(RouteName.detailProductPage, extra: product),
                    showMore: true,
                    title: 'Maybe you like',
                    itemBuilder: (context, product, index) =>
                        CardFeatureProduct(product: product),
                  ),
                  Gap(16.h),
                  ListViewGeneric<ProductModel>(
                    isLoading: state.isLoading,
                    items: productTrending,
                    onItemTap: (product) => context
                        .push(RouteName.detailProductPage, extra: product),
                    showMore: true,
                    title: 'Trending',
                    itemBuilder: (context, product, index) =>
                        CardFeatureProduct(product: product),
                  ),
                  Gap(32.h),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
