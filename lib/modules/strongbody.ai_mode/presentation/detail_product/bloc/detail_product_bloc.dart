import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

part 'detail_product_event.dart';
part 'detail_product_state.dart';

class DetailProductBloc extends Bloc<DetailProductEvent, DetailProductState> {
  final HomeStrongbodyAiRepository homeStrongbodyAiRepository;
  DetailProductBloc({required this.homeStrongbodyAiRepository})
      : super(DetailProductState.initial()) {
    on<FetchProductLikeEvent>(_onFetchProductLikeEvent);
    on<FetchProductTrendingEvent>(_onFetchProductTrendingEvent);
    on<GetProductDetailEvent>(_onGetProductDetailEvent);
  }
  void _onFetchProductLikeEvent(
      FetchProductLikeEvent event, Emitter<DetailProductState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, isSuccess: false));
      final param = {
        'category_id': event.categoryId,
      };
      final response = await homeStrongbodyAiRepository.getServiceByQuery(
          param: param, AppConstants.like, resourceType: AppConstants.product);
      if (response.status == Status.completed) {
        emit(state.copyWith(
          isLoading: false,
          isSuccess: true,
          productLike: response.data!.data.list
              .map((e) => ProductModel.fromJson(e))
              .toList(),
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          isSuccess: false,
          errorMessage: response.message,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        isSuccess: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void _onFetchProductTrendingEvent(
      FetchProductTrendingEvent event, Emitter<DetailProductState> emit) async {
    try {
      emit(state.copyWith(isLoading: true, isSuccess: false));
      final response = await homeStrongbodyAiRepository.getServiceByQuery(
          AppConstants.trending,
          resourceType: AppConstants.product);
      if (response.status == Status.completed) {
        emit(state.copyWith(
          isLoading: false,
          isSuccess: true,
          productTrending: response.data!.data.list
              .map((e) => ProductModel.fromJson(e))
              .toList(),
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          isSuccess: false,
          errorMessage: response.message,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        isSuccess: false,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onGetProductDetailEvent(
      GetProductDetailEvent event, Emitter<DetailProductState> emit) async {
    emit(state.copyWith(
      isLoading: true,
    ));
    try {
      final product =
          await homeStrongbodyAiRepository.getProduct(event.productId);
      emit(state.copyWith(isLoading: false, isSuccess: true, product: product));
      add(FetchProductLikeEvent(categoryId: product.categoryId));
      add(FetchProductTrendingEvent());
    } catch (e) {
      emit(state.copyWith(
        errorMessage: e.toString(),
        isLoading: false,
      ));
    }
  }
}
