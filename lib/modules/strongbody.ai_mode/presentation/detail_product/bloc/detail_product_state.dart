part of 'detail_product_bloc.dart';

class DetailProductState extends Equatable {
  final bool isLoading;
  final String? errorMessage;
  final bool isSuccess;
  final bool isCancelled;
  final bool isError;
  final List<ProductModel> productLike;
  final List<ProductModel> productTrending;
  final ProductModel? product;

  const DetailProductState({
    required this.isLoading,
    this.errorMessage,
    required this.isSuccess,
    required this.isCancelled,
    required this.isError,
    required this.productLike,
    required this.productTrending,
    this.product,
  });

  factory DetailProductState.initial() {
    return DetailProductState(
      isLoading: false,
      errorMessage: null,
      isSuccess: false,
      isCancelled: false,
      isError: false,
      productLike: [],
      productTrending: [],
      // product: ProductModel.empty(),
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        errorMessage,
        isSuccess,
        isCancelled,
        isError,
        productLike,
        productTrending,
        product,
      ];
  DetailProductState copyWith({
    bool? isLoading,
    String? errorMessage,
    bool? isSuccess,
    bool? isCancelled,
    bool? isError,
    List<ProductModel>? productLike,
    List<ProductModel>? productTrending,
    ProductModel? product,
  }) {
    return DetailProductState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
      isCancelled: isCancelled ?? this.isCancelled,
      isError: isError ?? this.isError,
      productLike: productLike ?? this.productLike,
      productTrending: productTrending ?? this.productTrending,
      product: product ?? this.product,
    );
  }
}
