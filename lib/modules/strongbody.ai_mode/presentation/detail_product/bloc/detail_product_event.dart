part of 'detail_product_bloc.dart';

class DetailProductEvent extends Equatable {
  const DetailProductEvent();
  @override
  List<Object?> get props => [];
}

class FetchDetailProductEvent extends DetailProductEvent {
  final String productId;

  const FetchDetailProductEvent({required this.productId});

  @override
  List<Object?> get props => [productId];
}

class FetchProductLikeEvent extends DetailProductEvent {
  final int categoryId;
  const FetchProductLikeEvent({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class FetchProductTrendingEvent extends DetailProductEvent {
  const FetchProductTrendingEvent();

  @override
  List<Object?> get props => [];
}

class GetProductDetailEvent extends DetailProductEvent {
  final int productId;
  const GetProductDetailEvent({required this.productId});

  @override
  List<Object?> get props => [productId];
}
