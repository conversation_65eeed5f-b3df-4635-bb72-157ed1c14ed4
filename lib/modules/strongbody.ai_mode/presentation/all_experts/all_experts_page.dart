import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_expert_list.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/filter_drawer_expert.dart';
import 'package:multime_app/shared/widgets/app_loader/vertical_product_shimmer.dart';
import 'package:multime_app/shared/widgets/layout/grid_layout.dart';

class AllExpertsPage extends StatefulWidget {
  AllExpertsPage();

  @override
  State<AllExpertsPage> createState() => _AllExpertsPageState();
}

class _AllExpertsPageState extends State<AllExpertsPage> {
  final ScrollController _scrollController = ScrollController();
  final globalKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        context
            .read<HomeStrongbodyAiBloc>()
            .add(FetchExpertEvent(isLoadMore: true));
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        context.read<HomeStrongbodyAiBloc>().add(FilterEvent(
              filterType: FilterType.expert,
              typeReset: 'reset',
            ));
        return true;
      },
      child: BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
          builder: (context, state) {
        final experts = state.searchExpertsResults;
        return Scaffold(
            key: globalKey,
            appBar: AppBar(
              automaticallyImplyLeading: false,
              leading: IconButton(
                icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
                onPressed: () => context.pop(),
              ),
              title: Text(
                'Our Experts',
                style: Theme.of(context).textTheme.lightHeadingMedium,
              ),
              actions: [
                Visibility(
                  visible: false,
                  child: IconButton(
                    icon: Icon(Icons.menu),
                    onPressed: () => globalKey.currentState?.openEndDrawer(),
                  ),
                ),
              ],
            ),
            endDrawer: FilterDrawerExpert(),
            endDrawerEnableOpenDragGesture: false,
            body: Column(
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(children: [
                    Expanded(
                      child: TextFormField(
                        onChanged: (value) {
                          context
                              .read<HomeStrongbodyAiBloc>()
                              .add(SearchExpertEvent(query: value));
                        },
                        decoration: InputDecoration(
                          labelText: 'Search',
                          suffixIcon: Icon(
                            Icons.search,
                            color: Theme.of(context).greyScale500(context),
                          ),
                          hintStyle:
                              Theme.of(context).textTheme.lightBodyLargeRegular,
                          contentPadding: EdgeInsets.symmetric(horizontal: 10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: Theme.of(context).greyScale100(context),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Gap(10.w),
                    IconButton(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.padding14,
                        vertical: AppSpacing.padding12h,
                      ),
                      style: IconButton.styleFrom(
                        side: BorderSide(
                          color: Theme.of(context).greyScale200(context),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      icon: SvgPicture.asset(AppAssets.setting5Svg,
                          color: Theme.of(context).greyScale500(context)),
                      onPressed: () {
                        globalKey.currentState?.openEndDrawer();
                      },
                    ),
                  ]),
                ),
                Expanded(
                  child: state.isExpertLoading
                      ? TVerticalProductShimmer()
                      : experts.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.search_off,
                                    size: 64,
                                    color:
                                        Theme.of(context).greyScale300(context),
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    'Expert not found',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodyLargeRegular
                                        .copyWith(
                                          color: Theme.of(context)
                                              .greyScale500(context),
                                        ),
                                  ),
                                ],
                              ),
                            )
                          : TGridLayout(
                              controller: _scrollController,
                              mainAxisExtent: 360,
                              itemCount: state.searchQuery.isNotEmpty
                                  ? experts.length
                                  : (state.hasMore
                                      ? experts.length + 2
                                      : experts.length),
                              itemBuilder: (context, index) {
                                if (state.searchQuery.isEmpty &&
                                    index >= experts.length) {
                                  return TVerticalProductShimmer();
                                }
                                if (index >= experts.length) {
                                  return SizedBox.shrink();
                                }
                                final expertItem = experts[index];
                                return GestureDetector(
                                  onTap: () {
                                    // context.push(RouteName.detailExpertPage,
                                    //     extra: expertItem);
                                  },
                                  child:
                                      CardExpertList(sellerModel: expertItem),
                                );
                              },
                            ),
                ),
              ],
            ));
      }),
    );
  }
}
