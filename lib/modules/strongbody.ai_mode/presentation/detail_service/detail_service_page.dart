part of "detail_service_import.dart";

class DetailServicePage extends StatefulWidget {
  const DetailServicePage({
    super.key,
    required this.serviceModel,
  });
  final ServiceModel serviceModel;

  @override
  State<DetailServicePage> createState() => _DetailServicePageState();
}

class _DetailServicePageState extends State<DetailServicePage>
    with SingleTickerProviderStateMixin {
  int _current = 0;
  bool isCollapsed = false;
  late AutoScrollController scrollController;
  late TabController tabController;
  bool pauseRectGetterIndex = false;
  final double expandedHeight = 270.0;
  late int randomViews;

  static const int sectionCount = 2;
  final List<GlobalKey> sectionKeys = List.generate(2, (_) => GlobalKey());

  @override
  void initState() {
    super.initState();
    randomViews = Random().nextInt(1000) + 50;
    tabController = TabController(length: sectionCount, vsync: this);
    scrollController = AutoScrollController();
    scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    final double threshold = expandedHeight -
        kToolbarHeight -
        MediaQuery.of(context).padding.top -
        10;
    bool shouldCollapse =
        scrollController.hasClients && scrollController.offset > threshold;
    if (isCollapsed != shouldCollapse) {
      setState(() {
        isCollapsed = shouldCollapse;
      });
    }

    if (!pauseRectGetterIndex) {
      for (int i = sectionKeys.length - 1; i >= 0; i--) {
        final keyContext = sectionKeys[i].currentContext;
        if (keyContext != null) {
          final box = keyContext.findRenderObject();
          if (box is RenderBox) {
            final offset = box.localToGlobal(Offset.zero,
                ancestor: context.findRenderObject());
            if (offset.dy <= kToolbarHeight + 60) {
              if (tabController.index != i) {
                tabController.animateTo(i);
              }
              break;
            }
          }
        }
      }
    }
  }

  void animateAndScrollTo(int index) async {
    pauseRectGetterIndex = true;
    tabController.animateTo(index);
    await scrollController.scrollToIndex(index,
        preferPosition: AutoScrollPosition.begin);
    pauseRectGetterIndex = false;
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DetailServiceBloc, DetailServiceState>(
      builder: (context, state) {
        final serviceLike = state.serviceLike
            .where((service) => service.id != widget.serviceModel.id)
            .toList();
        final serviceTrending = state.serviceTrending
            .where((service) => service.id != widget.serviceModel.id)
            .toList();
        return Scaffold(
          bottomNavigationBar: BottomDetailService(
            serviceModel: widget.serviceModel,
          ),
          body: CustomScrollView(
            controller: scrollController,
            slivers: [
              CustomAppbarScrollview(
                sellerId: widget.serviceModel.user?.id ?? 0,
                sellerName: widget.serviceModel.user?.fullName ?? "",
                sellerAvatar: widget.serviceModel.user?.profilePicture ?? "",
                sellerIsOnline: false,
                tabController: tabController,
                onTabTap: animateAndScrollTo,
                isCollapsed: isCollapsed,
                expandedHeight: expandedHeight,
                images: widget.serviceModel.image,
                currentImage: _current,
                onCarouselChanged: (index) {
                  setState(() {
                    _current = index;
                  });
                },
              ),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('${widget.serviceModel.title}',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightHeadingMedium),
                          Gap(10.h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              IconLabel(AppAssets.eyeSvg, '$randomViews views'),
                              IconLabel(AppAssets.share_newSvg, 'Copy link'),
                              IconLabel(AppAssets.heart_icon, 'Like'),
                            ],
                          ),
                          Gap(16.h),
                          DetailInfoService(serviceModel: widget.serviceModel),
                          Gap(16.h),
                          DetailContactService(
                              serviceModel: widget.serviceModel),
                          // Gap(10.h),
                          // ReadMoreText(
                          //   TFormatter.htmlToFormattedText(
                          //       widget.serviceModel.description),
                          //   trimLines: 3,
                          //   colorClickableText:
                          //       Theme.of(context).alertInformationBase(context),
                          //   trimMode: TrimMode.Line,
                          //   trimCollapsedText: 'more',
                          //   trimExpandedText: 'less',
                          //   style: Theme.of(context)
                          //       .textTheme
                          //       .lightBodyLargeSemiBold,
                          // ),
                          // Gap(16.h),
                          // DetailContactService(
                          //     serviceModel: widget.serviceModel),
                          // Gap(16.h),
                          // AutoScrollTag(
                          //   key: ValueKey(0),
                          //   controller: scrollController,
                          //   index: 0,
                          //   child: Container(
                          //     key: sectionKeys[0],
                          //     child: DetailServicePackage(),
                          //   ),
                          // ),
                          Gap(16.h),
                          AutoScrollTag(
                            key: ValueKey(0),
                            controller: scrollController,
                            index: 0,
                            child: Container(
                              key: sectionKeys[0],
                              child: DetailDescriptionService(
                                  serviceModel: widget.serviceModel),
                            ),
                          ),
                          Gap(16.h),
                          AutoScrollTag(
                            key: ValueKey(1),
                            controller: scrollController,
                            index: 1,
                            child: Container(
                              key: sectionKeys[1],
                              child: DetailReviewService(
                                  serviceModel: widget.serviceModel),
                            ),
                          ),
                          // Gap(16.h),
                          // AutoScrollTag(
                          //   key: ValueKey(3),
                          //   controller: scrollController,
                          //   index: 3,
                          //   child: Container(
                          //     key: sectionKeys[3],
                          //     child: DetailFAQService(),
                          //   ),
                          // ),
                          // Gap(16.h),
                          // DetailCustomerReview(),
                        ],
                      ),
                    ),
                    Gap(16.h),
                    ListViewGeneric<ServiceModel>(
                      isLoading: state.isLoading,
                      isSuccess: state.isSuccess,
                      items: serviceLike,
                      onItemTap: (service) =>
                          context.push(RouteName.serviceDetail, extra: service),
                      showMore: true,
                      title: 'May you Like',
                      itemBuilder: (context, service, index) =>
                          CardSuggestService(service: service),
                      isShowButton: false,
                      isListHome: false,
                    ),
                    Gap(16.h),
                    ListViewGeneric<ServiceModel>(
                      isLoading: state.isLoading,
                      isSuccess: state.isSuccess,
                      items: serviceTrending,
                      onItemTap: (service) =>
                          context.push(RouteName.serviceDetail, extra: service),
                      showMore: true,
                      title: 'Trending',
                      itemBuilder: (context, service, index) =>
                          CardSuggestService(service: service),
                      isShowButton: false,
                      isListHome: false,
                    ),
                    Gap(32.h),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget IconLabel(String icon, String label) {
    return Row(
      children: [
        SvgPicture.asset(icon, color: Theme.of(context).greyScale600(context)),
        Gap(10.w),
        Text(label,
            style: Theme.of(context)
                .textTheme
                .lightBodyMediumMedium
                .copyWith(color: Theme.of(context).greyScale600(context))),
      ],
    );
  }
}
