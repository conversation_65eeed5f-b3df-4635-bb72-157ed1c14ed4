part of 'detail_service_bloc.dart';

class DetailServiceEvent extends Equatable {
  const DetailServiceEvent();

  @override
  List<Object?> get props => [];
}

class IsSelectedEvent extends DetailServiceEvent {
  final int selectedIndex;
  const IsSelectedEvent({required this.selectedIndex});

  @override
  List<Object?> get props => [selectedIndex];
}

class FetchServiceLikeEvent extends DetailServiceEvent {
  final int categoryId;
  const FetchServiceLikeEvent({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class FetchServiceTrendingEvent extends DetailServiceEvent {
  const FetchServiceTrendingEvent();

  @override
  List<Object?> get props => [];
}


class GetServiceDetailEvent extends DetailServiceEvent {
  final int serviceId;
  const GetServiceDetailEvent({required this.serviceId});

  @override
  List<Object?> get props => [serviceId];
}
