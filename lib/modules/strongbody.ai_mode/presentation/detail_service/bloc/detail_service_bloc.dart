import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

part 'detail_service_event.dart';
part 'detail_service_state.dart';

class DetailServiceBloc extends Bloc<DetailServiceEvent, DetailServiceState> {
  final HomeStrongbodyAiRepository servicesRepository;
  DetailServiceBloc({required this.servicesRepository})
      : super(DetailServiceState.initial()) {
    on<IsSelectedEvent>(_onIsSelectedEvent);
    on<FetchServiceLikeEvent>(_onFetchServiceLikeEvent);
    on<FetchServiceTrendingEvent>(_onFetchServiceTrendingEvent);

    on<GetServiceDetailEvent>(_onGetServiceDetailEvent);
  }

  void _onIsSelectedEvent(
      IsSelectedEvent event, Emitter<DetailServiceState> emit) {
    emit(state.copyWith(selectedIndex: event.selectedIndex));
  }

  void _onFetchServiceLikeEvent(
      FetchServiceLikeEvent event, Emitter<DetailServiceState> emit) async {
    emit(state.copyWith(isLoadingServiceLike: true));
    try {
      final param = {
        'category_id': event.categoryId,
      };
      final services = await servicesRepository.getServiceByQuery(
        param: param,
        AppConstants.like,
        resourceType: AppConstants.service,
      );
      emit(state.copyWith(
        isLoadingServiceLike: false,
        isSuccess: true,
        serviceLike: services.data!.data.list
            .map((e) => ServiceModel.fromJson(e))
            .toList(),
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingServiceLike: false,
        errorMessage: e.toString(),
      ));
    }
  }

  void _onFetchServiceTrendingEvent(
      FetchServiceTrendingEvent event, Emitter<DetailServiceState> emit) async {
    emit(state.copyWith(isLoadingServiceTrending: true));
    try {
      final services = await servicesRepository.getServiceByQuery(
        AppConstants.trending,
        resourceType: AppConstants.service,
      );
      emit(state.copyWith(
        isSuccess: true,
        isLoadingServiceTrending: false,
        serviceTrending: services.data!.data.list
            .map((e) => ServiceModel.fromJson(e))
            .toList(),
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoadingServiceTrending: false,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onGetServiceDetailEvent(
      GetServiceDetailEvent event, Emitter<DetailServiceState> emit) async {
    emit(state.copyWith(isLoadingDetail: true));
    try {
      final service = await servicesRepository.getService(event.serviceId);
      emit(state.copyWith(
        isLoadingDetail: false,
        isSuccess: true,
        service: service,
      ));
      add(FetchServiceLikeEvent(categoryId: service.categoryId));
      add(FetchServiceTrendingEvent());
    } catch (e) {
      emit(state.copyWith(
        isLoadingDetail: false,
        isSuccess: false,
        errorMessage: e.toString(),
      ));
    }
  }
}
