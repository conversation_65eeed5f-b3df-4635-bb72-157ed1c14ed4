part of 'detail_service_bloc.dart';

class DetailServiceState extends Equatable {
  final bool isLoadingDetail;
  final bool isLoadingServiceLike;
  final bool isLoadingServiceTrending;
  final bool isLoading;

  final String? errorMessage;
  final String? successMessage;
  final bool isSuccess;
  final int selectedIndex;

  final List<ServiceModel> serviceLike;
  final List<ServiceModel> serviceTrending;
  final ServiceModel? service;

  const DetailServiceState({
    this.service,
    required this.isLoadingDetail,
    required this.isLoadingServiceLike,
    required this.isLoadingServiceTrending,
    required this.isSuccess,
    required this.selectedIndex,
    required this.serviceLike,
    required this.serviceTrending,
    this.errorMessage,
    this.successMessage,
    required this.isLoading,
  });

  factory DetailServiceState.initial() => const DetailServiceState(
        isLoadingDetail: false,
        isLoadingServiceLike: false,
        isLoadingServiceTrending: false,
        isSuccess: false,
        selectedIndex: 0,
        serviceLike: [],
        serviceTrending: [],
        errorMessage: null,
        successMessage: null,
        isLoading: false,
      );

  DetailServiceState copyWith({
    bool? isLoadingDetail,
    bool? isLoadingServiceLike,
    bool? isLoadingServiceTrending,
    String? errorMessage,
    String? successMessage,
    bool? isSuccess,
    int? selectedIndex,
    List<ServiceModel>? serviceLike,
    List<ServiceModel>? serviceTrending,
    ServiceModel? service,
    bool? isLoading,
  }) {
    return DetailServiceState(
      isLoadingDetail: isLoadingDetail ?? this.isLoadingDetail,
      isLoadingServiceLike: isLoadingServiceLike ?? this.isLoadingServiceLike,
      isLoadingServiceTrending:
          isLoadingServiceTrending ?? this.isLoadingServiceTrending,
      service: service ?? this.service,
      isSuccess: isSuccess ?? this.isSuccess,
      selectedIndex: selectedIndex ?? this.selectedIndex,
      serviceLike: serviceLike ?? this.serviceLike,
      serviceTrending: serviceTrending ?? this.serviceTrending,
      errorMessage: errorMessage,
      successMessage: successMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [
        isLoadingDetail,
        isLoadingServiceLike,
        isLoadingServiceTrending,
        service,
        serviceLike,
        serviceTrending,
        isSuccess,
        selectedIndex,
        errorMessage,
        successMessage,
        isLoading,
      ];
}
