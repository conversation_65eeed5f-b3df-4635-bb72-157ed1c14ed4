import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class DetailFAQService extends StatelessWidget {
  const DetailFAQService({super.key});

  @override
  Widget build(BuildContext context) {
    Widget _buildExpandedSection(String title, String content) {
      return Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1,
              color: Theme.of(context).attentionLight(context),
            ),
          ),
        ),
        child: ExpansionTile(
          title: Text(
            title,
            style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
          ),
          trailing: SvgPicture.asset(
            AppAssets.arrowRightSvg,
            color: Theme.of(context).blackPrimary(context),
          ),
          tilePadding: EdgeInsets.zero,
          childrenPadding: EdgeInsets.zero,
          children: [
            Text(
              content,
              style: Theme.of(context).textTheme.lightBodyLargeRegular,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service FAQs',
          style: Theme.of(context).textTheme.lightBodyLargeBold,
        ),
        Gap(16.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
            border: Border.all(
              width: 1,
              color: Theme.of(context).attentionLight(context),
            ),
          ),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      width: 1,
                      color: Theme.of(context).attentionLight(context),
                    ),
                  ),
                ),
                child: _buildExpandedSection(
                  'Does the service offer home support?',
                  'Content',
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      width: 1,
                      color: Theme.of(context).attentionLight(context),
                    ),
                  ),
                ),
                child: _buildExpandedSection(
                  'What are the service uptimes?',
                  'Content',
                ),
              ),
              Gap(16.h)
            ],
          ),
        ),
      ],
    );
  }
}
