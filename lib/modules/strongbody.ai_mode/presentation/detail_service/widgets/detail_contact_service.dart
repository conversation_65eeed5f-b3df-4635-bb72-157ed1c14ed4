import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/user_avatar.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';

class DetailContactService extends StatelessWidget {
  const DetailContactService({super.key, required this.serviceModel});

  final ServiceModel serviceModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Theme.of(context).pinkScale50(context),
      child: Row(
        children: [
          UserAvatar(
            size: 40,
            imageURL: (serviceModel.user?.profilePicture != null &&
                    serviceModel.user!.profilePicture!.isNotEmpty)
                ? serviceModel.user!.profilePicture!
                : 'https://img.tripi.vn/cdn-cgi/image/width=700,height=700/https://gcs.tripi.vn/public-tripi/tripi-feed/img/477744gWT/anh-mo-ta.png',
            isOnline: false,
          ),
          Gap(8.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(serviceModel.user!.fullName,
                  style: Theme.of(context).textTheme.lightBodyLargeBold),
              Gap(5.h),
              Text('Active 3 min ago',
                  style: Theme.of(context).textTheme.lightBodySmallRegular),
              Text(serviceModel.user!.countryName ?? "Universal",
                  style: Theme.of(context).textTheme.lightBodySmallRegular),
            ],
          ),
          const Spacer(),
          ElevatedButton(
              style: ElevatedButton.styleFrom(
                side: BorderSide(
                  color: Theme.of(context).primary(context),
                ),
                backgroundColor: Theme.of(context).whitePrimary(context),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                context.push(
                  RouteName.chatPage,
                  extra: {
                    "uid": serviceModel.user?.id ?? 0,
                    "name": serviceModel.user?.fullName ?? "",
                    "avatar": serviceModel.user?.profilePicture ?? "",
                    "isOnline": false,
                  },
                );
              },
              child: Row(
                children: [
                  Text(
                    'Contact',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeMedium
                        .copyWith(
                          color: Theme.of(context).primary(context),
                        ),
                  ),
                  Gap(5.w),
                  SvgPicture.asset(
                    AppAssets.shopSvg,
                    width: 24.w,
                    height: 24.h,
                    color: Theme.of(context).primary(context),
                  ),
                ],
              ))
        ],
      ),
    );
  }
}
