import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';

class DetailInfoService extends StatelessWidget {
  const DetailInfoService({super.key, required this.serviceModel});
  final ServiceModel serviceModel;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '\$ ${serviceModel.price}',
          style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                color: Colors.red.shade700,
              ),
        ),
        Gap(16.h),
        RichText(
          text: TextSpan(
            style: DefaultTextStyle.of(context).style,
            children: <TextSpan>[
              TextSpan(
                  text: 'Category  ',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeRegular
                      .copyWith(
                        color: Theme.of(context).greyScale600(context),
                      )),
              TextSpan(
                  text: '${serviceModel.category?.name}',
                  style: Theme.of(context).textTheme.lightBodyLargeSemiBold),
            ],
          ),
        ),
        Gap(10.h),
        Text('Product certifications',
            style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                  color: Theme.of(context).greyScale600(context),
                )),
        Gap(10.h),
        Row(
          children: [
            Column(
              children: [
                SvgPicture.asset(
                  AppAssets.isoSvg,
                ),
                Text('Picture.png',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeRegular
                        .copyWith(
                          color: Theme.of(context).greyScale600(context),
                        )),
              ],
            ),
            Gap(10.w),
            Column(
              children: [
                SvgPicture.asset(
                  AppAssets.bsciSvg,
                ),
                Text('file.pdf',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeRegular
                        .copyWith(
                          color: Theme.of(context).greyScale600(context),
                        )),
              ],
            )
          ],
        ),
        Gap(16.h),
        Row(
          children: [
            SvgPicture.asset(AppAssets.ticketExpiredSvg),
            SizedBox(width: 8.0),
            Text(
              'Online working format',
              style: Theme.of(context).textTheme.lightBodyLargeMedium,
            ),
          ],
        ),
        Gap(16.h),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).greyScale50(context),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Theme.of(context).greyScale300(context),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  SvgPicture.asset(AppAssets.clockMessageSvg),
                  Gap(10.w),
                  Text(
                    'Service Validity Period',
                    style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                  ),
                ],
              ),
              Gap(10.h),
              Text(
                'This service is valid for 30 Days 6 hours after purchase. ',
                style: Theme.of(context).textTheme.lightBodyLargeMedium,
              ),
            ],
          ),
        )
      ],
    );
  }
}
