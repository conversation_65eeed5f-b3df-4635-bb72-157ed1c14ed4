// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../app/routers/routers_name.dart';
import '../../../data/models/services/service_model.dart';

// ignore: must_be_immutable
class BottomDetailService extends StatelessWidget {
  ServiceModel? serviceModel;
  BottomDetailService({
    Key? key,
    this.serviceModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget _buildButton(String text, VoidCallback onPressed, Color color) {
      return ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
          backgroundColor: color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.radius10),
          ),
        ),
        child: Text(
          text,
          style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                color: Theme.of(context).whitePrimary(context),
              ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 4.0,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Text(
          //   '\$15:',
          //   style: Theme.of(context).textTheme.lightBodyXLargeBold,
          // ),
          // Spacer(),
          // _buildButton('Add to Cart', () {
          //   // Handle button press
          // }, Theme.of(context).secondary(context)),
          Gap(10.w),
          _buildButton(
              'Buy Now',
              () => context.push(
                    RouteName.checkoutService,
                    extra: serviceModel,
                    // Example extra data
                  )
              // Handle button press
              ,
              Theme.of(context).primary(context))
        ],
      ),
    );
  }
}
