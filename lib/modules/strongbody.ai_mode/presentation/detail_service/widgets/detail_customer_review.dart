import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/layout/listview_layout.dart';

class DetailCustomerReview extends StatelessWidget {
  const DetailCustomerReview({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.height;
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 1,
            color: Theme.of(context).attentionLight(context),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Our Customer Reviews',
            style: Theme.of(context).textTheme.lightBodyLargeBold,
          ),
          Gap(16.h),
          SizedBox(
            height: size * 0.2,
            child: ListviewLayout(
              itemBuilder: (context, index) {
                return SvgPicture.asset(
                  AppAssets.imageReviewSvg,
                  color: Theme.of(context).alertAttentionBase(context),
                );
              },
              itemCount: 5,
            ),
          ),
          Center(
              child: Text(
            'Read all reviews ( 18 )',
            style: Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                  color: Theme.of(context).informationBase(context),
                  decoration: TextDecoration.underline,
                  decorationColor: Theme.of(context).informationBase(context),
                ),
          )),
          Gap(16.h),
        ],
      ),
    );
  }
}
