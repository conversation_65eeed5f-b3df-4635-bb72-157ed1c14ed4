import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/appbar_detail_service.dart';

class CustomAppbarScrollview extends StatelessWidget {
  final TabController tabController;
  final void Function(int) onTabTap;
  final bool isCollapsed;
  final double expandedHeight;
  final List<String> images;
  final int currentImage;
  final void Function(int) onCarouselChanged;
  final int? sellerId;
  final String? sellerName;
  final String? sellerAvatar;
  final bool? sellerIsOnline;

  const CustomAppbarScrollview({
    Key? key,
    required this.tabController,
    required this.onTabTap,
    required this.isCollapsed,
    required this.expandedHeight,
    required this.images,
    required this.currentImage,
    required this.onCarouselChanged,
    required this.sellerId,
    required this.sellerName,
    required this.sellerAvatar,
    required this.sellerIsOnline,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Filter valid image URLs
    final validImages = images
        .where((url) => url.isNotEmpty && Uri.tryParse(url) != null)
        .toList();

    return SliverAppBar(
      pinned: true,
      floating: false,
      expandedHeight: expandedHeight.h,
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      title: isCollapsed
          ? AppbarDetailService(
              sellerId: sellerId,
              sellerName: sellerName,
              sellerAvatar: sellerAvatar,
              sellerIsOnline: sellerIsOnline,
            )
          : null,
      bottom: isCollapsed
          ? PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: Container(
                color: Colors.white,
                child: TabBar(
                  controller: tabController,
                  labelColor: Theme.of(context).primary(context),
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: Theme.of(context).textTheme.lightBodyLargeBold,
                  unselectedLabelStyle:
                      Theme.of(context).textTheme.lightBodyLargeRegular,
                  indicatorColor: Theme.of(context).primary(context),
                  indicatorWeight: 3,
                  isScrollable: true,
                  tabAlignment: TabAlignment.start,
                  dividerColor: Colors.transparent,
                  padding: EdgeInsets.zero,
                  onTap: onTabTap,
                  tabs: const [
                    // Tab(text: 'Service Packages'),
                    Tab(text: 'Service Description'),
                    Tab(text: 'Review'),

                    // Tab(text: 'FAQ'),
                  ],
                ),
              ),
            )
          : null,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            Positioned.fill(
              child: validImages.isEmpty
                  ? Container(
                      color: Colors.grey[100],
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.image_outlined,
                              size: 80,
                              color: Colors.grey[400],
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No image available',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : CarouselSlider(
                      options: CarouselOptions(
                        viewportFraction: 1,
                        height: double.infinity,
                        onPageChanged: (index, _) => onCarouselChanged(index),
                      ),
                      items: validImages
                          .map(
                            (banner) => Image.network(
                              banner,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  color: Colors.grey[100],
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      value:
                                          loadingProgress.expectedTotalBytes !=
                                                  null
                                              ? loadingProgress
                                                      .cumulativeBytesLoaded /
                                                  loadingProgress
                                                      .expectedTotalBytes!
                                              : null,
                                    ),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                color: Colors.grey[100],
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.broken_image_outlined,
                                        size: 60,
                                        color: Colors.grey[400],
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Failed to load image',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 25,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (validImages.isNotEmpty)
                    for (int i = 0; i < validImages.length; i++)
                      Container(
                        width: 10,
                        height: 10,
                        margin: const EdgeInsets.only(right: 10),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: i == currentImage
                              ? Theme.of(context).alertInformationBase(context)
                              : Theme.of(context).greyScale300(context),
                        ),
                      )
                ],
              ),
            ),
            if (!isCollapsed)
              Positioned(
                top: 0,
                left: 16,
                right: 16,
                child: AppbarDetailService(
                  isBackground: true,
                  sellerId: sellerId,
                  sellerName: sellerName,
                  sellerAvatar: sellerAvatar,
                  sellerIsOnline: sellerIsOnline,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
