import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';

class DetailDescriptionService extends StatelessWidget {
  const DetailDescriptionService({super.key, required this.serviceModel});

  final ServiceModel serviceModel;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SvgPicture.asset(AppAssets.boxSearchSvg),
            SizedBox(width: 8.0),
            Text(
              'Service description',
              style: Theme.of(context).textTheme.lightBodyLargeBold,
            ),
          ],
        ),
        Gap(16.h),
        Text(
          serviceModel.cleanDescription, // Sử dụng cleanDescription getter
          style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
        ),
      ],
    );
  }
}
