import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/bloc/detail_service_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/bottom_detail_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/custom_appbar_scrollview.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_description_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_contact_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_info_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_review_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_suggest_service.dart';
import 'package:multime_app/shared/widgets/list_card/List_view_generic.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

part 'detail_service_page.dart';
