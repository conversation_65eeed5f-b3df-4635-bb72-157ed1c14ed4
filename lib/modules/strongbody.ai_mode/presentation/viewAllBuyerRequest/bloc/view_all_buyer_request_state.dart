part of 'view_all_buyer_request_bloc.dart';

 class ViewAllBuyerRequestState extends Equatable {
final ApiResponse<ViewAllBuyerRequests> requests;

  const ViewAllBuyerRequestState({
    this.requests = const ApiResponse.loading(),
  });

  factory ViewAllBuyerRequestState.initial() {
    return const ViewAllBuyerRequestState(
      requests: ApiResponse.loading(),
    );
  }

  ViewAllBuyerRequestState copyWith({
    ApiResponse<ViewAllBuyerRequests>? requests,
  }) {
    return ViewAllBuyerRequestState(
      requests: requests ?? this.requests,
    );
  }

  @override
  List<Object?> get props => [requests];
}
