import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/view_all_buyer_requests.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/buyer_request_repository.dart';

part 'view_all_buyer_request_event.dart';
part 'view_all_buyer_request_state.dart';

class ViewAllBuyerRequestBloc
    extends Bloc<ViewAllBuyerRequestEvent, ViewAllBuyerRequestState> {
  final BuyerRequestRepository _buyerRequestRepository;

  ViewAllBuyerRequestBloc(this._buyerRequestRepository)
      : super(ViewAllBuyerRequestState.initial()) {
    on<GetAllBuyerRequestEvent>(_onGetAllBuyerRequestEvent);
  }

  Future<void> _onGetAllBuyerRequestEvent(
    GetAllBuyerRequestEvent event,
    Emitter<ViewAllBuyerRequestState> emit,
  ) async {
    try {
      emit(state.copyWith(requests: const ApiResponse.loading()));
      final requests = await _buyerRequestRepository.viewAllBuyerRequests();
      emit(state.copyWith(requests: ApiResponse.completed(requests)));
    } on NetworkException catch (e) {
      emit(state.copyWith(requests: ApiResponse.error(e.message)));
    }
  }
}
