import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/date_time_utils.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/buyer_requests.dart';

class CardRequest extends StatelessWidget {
  const CardRequest({super.key, this.buyerRequest});

  final BuyerRequest? buyerRequest;

  @override
  Widget build(BuildContext context) {
    // int maxVisibleCategories = 3;
    // int remainingCategories =
    //     ProductRequestData.data.length - maxVisibleCategories;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 1),
        borderRadius: BorderRadius.circular(14),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Posted ${getTimeAgo(buyerRequest!.createdAt!, context.locale)}',
                style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400,
                    color: Theme.of(context).greyScale600(context)),
              ),
              Container(
                alignment: Alignment.center,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color:
                      // buyerRequest!.status ==
                      //         ProductRequestStatus.completed
                      // ? Theme.of(context).successLight(context)
                      // :
                      Theme.of(context).warningLight(context),
                ),
                child: Text(
                  // productRequestModel!.status == ProductRequestStatus.completed
                  //     ? 'Completed'.tr()
                  //     :
                  'Pending'.tr(),
                  style: TextStyle(
                    color:
                        // productRequestModel!.status ==
                        //         ProductRequestStatus.completed
                        //     ? Theme.of(context).successBase(context)
                        //     :
                        Theme.of(context).warningBase(context),
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              )
            ],
          ),
          Gap(8.h),
          Text(
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            buyerRequest?.title ?? '',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          Gap(8.h),
          Text(
            'Public',
            style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
                color: Theme.of(context).greyScale600(context)),
          ),
          Gap(14.h),
          // SizedBox(
          //   height: 34.h,
          //   child: ListView.separated(
          //     separatorBuilder: (_, __) => const Gap(8),
          //     shrinkWrap: true,
          //     scrollDirection: Axis.horizontal,
          //     itemCount: productRequestModel!.category!.length > 3
          //         ? 4
          //         : productRequestModel!.category!.length,
          //     itemBuilder: (context, i) {
          // if (i < 3) {
          // Hiển thị 3 phần tử đầu tiên
          // return
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).greyScale200(context),
            ),
            child: Text(
              // productRequestModel!.category![i],
              buyerRequest?.categoryName ?? '',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textSecondary(context),
              ),
            ),
          ),
          // } else {
          //   // Hiển thị phần tử cuối cùng với số lượng còn lại
          //   return Container(
          //     width: 36,
          //     height: 24,
          //     padding:
          //         const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(8),
          //       color: Theme.of(context).greyScale200(context),
          //     ),
          //     child: Text(
          //       '+${productRequestModel!.category!.length - 3}', // Hiển thị số lượng danh mục còn lại
          //       textAlign: TextAlign.center,
          //       style: TextStyle(
          //         fontSize: 12.sp,
          //         fontWeight: FontWeight.w600,
          //         color: Theme.of(context).textSecondary(context),
          //       ),
          //     ),
          //   );
          //       }
          //     },
          //   ),
          // ),
          Gap(14.h),
          Text(
            textAlign: TextAlign.justify,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            buyerRequest!.description!,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Theme.of(context).greyScale600(context),
            ),
          )
        ],
      ),
    );
  }
}
