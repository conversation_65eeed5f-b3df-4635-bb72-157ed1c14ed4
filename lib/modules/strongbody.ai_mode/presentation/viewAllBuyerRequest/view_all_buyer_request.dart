import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/viewAllBuyerRequest/bloc/view_all_buyer_request_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/viewAllBuyerRequest/widgets/card_request.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';

class ViewAllBuyerRequestScreen extends StatelessWidget {
  const ViewAllBuyerRequestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Requests'),
        centerTitle: true,
      ),
      body: BlocConsumer<ViewAllBuyerRequestBloc, ViewAllBuyerRequestState>(
        listener: (context, state) {
          if (state.requests.status == Status.loading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
        },
        builder: (context, state) {
          if (state.requests.status == Status.completed) {
            return ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              separatorBuilder: (_, __) => Gap(12.h),
              shrinkWrap: true,
              itemCount: state.requests.data?.items?.length ?? 0,
              itemBuilder: (context, i) {
                var request = state.requests.data?.items?[i];
                return GestureDetector(
                  onTap: () {
                    context.push(RouteName.buyerRequestDetail,
                        extra: request!.id);
                  },
                  child: CardRequest(
                    buyerRequest: request,
                  ),
                );
              },
            );
          } else {
            return Text(state.requests.message ?? "");
          }
        },
      ),
    );
  }
}
