part of 'checkout_service_bloc.dart';

class CheckoutServiceEvent extends Equatable {
  const CheckoutServiceEvent();

  @override
  List<Object?> get props => [];
}

class CheckoutServiceOrderEvent extends CheckoutServiceEvent {
  final CreateOrderServiceParam param;

  const CheckoutServiceOrderEvent(this.param);

  @override
  List<Object?> get props => [param];
}

class CheckoutServiceInitialEvent extends CheckoutServiceEvent {
  final CreateOrderServiceModel serviceModel;
  const CheckoutServiceInitialEvent(
    this.serviceModel,
  );

  @override
  List<Object?> get props => [serviceModel];
}

class CheckoutServicePaymentEvent extends CheckoutServiceEvent {
  final int orderId;

  const CheckoutServicePaymentEvent(this.orderId);

  @override
  List<Object?> get props => [orderId];
}
