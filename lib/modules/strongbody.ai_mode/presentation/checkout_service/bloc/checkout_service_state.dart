part of 'checkout_service_bloc.dart';

class CheckoutServiceState extends Equatable {
  final ApiResponse<OrderServiceModel> serviceResponse;
  final ApiResponse<OrderOfferModel> paymentResponse;

  const CheckoutServiceState(this.serviceResponse, this.paymentResponse);

  factory CheckoutServiceState.initial() =>
      const CheckoutServiceState(ApiResponse.loading(), ApiResponse.loading());

  CheckoutServiceState copyWith({
    ApiResponse<OrderServiceModel>? serviceResponse,
    final ApiResponse<OrderOfferModel>? paymentResponse,
  }) {
    return CheckoutServiceState(
      serviceResponse ?? this.serviceResponse,
      paymentResponse ?? this.paymentResponse,
    );
  }

  @override
  List<Object?> get props => [serviceResponse, paymentResponse];
}
