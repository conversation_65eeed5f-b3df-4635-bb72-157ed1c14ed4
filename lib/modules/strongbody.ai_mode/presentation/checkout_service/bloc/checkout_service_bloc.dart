import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/model/order/order_offer_model.dart';
import '../../../../../core/base/api_response/api_response.dart';
import '../../../../../core/data/param/create_order_service_param.dart';
import '../../../../../core/model/order/create_order_service.dart';
import '../../../../../core/model/order/order_service_model.dart';
import '../response/checkout_service_respose.dart';

part 'checkout_service_event.dart';
part 'checkout_service_state.dart';

class CheckoutServiceBloc
    extends Bloc<CheckoutServiceEvent, CheckoutServiceState> {
  final CheckoutServiceReponseRemote checkoutServiceReponseRemote;

  CheckoutServiceBloc(this.checkoutServiceReponseRemote)
      : super(CheckoutServiceState.initial()) {
    on<CheckoutServiceOrderEvent>(_onCheckoutServiceOrderEvent);
    on<CheckoutServicePaymentEvent>(_onCheckoutServicePaymentEvent);
    on<CheckoutServiceInitialEvent>(_onCheckoutServiceInitialEvent);
  }

  Future<void> _onCheckoutServiceInitialEvent(CheckoutServiceInitialEvent event,
      Emitter<CheckoutServiceState> emit) async {
    emit(state.copyWith(
      serviceResponse: ApiResponse.loading(),
    ));
    try {
      final response = await checkoutServiceReponseRemote
          .createOrderService(event.serviceModel);
      emit(state.copyWith(
        serviceResponse: ApiResponse.completed(response.data!),
      ));
    } catch (e) {
      emit(state.copyWith(
        serviceResponse: ApiResponse.error(e.toString()),
      ));
    }
  }

  Future<void> _onCheckoutServiceOrderEvent(CheckoutServiceOrderEvent event,
      Emitter<CheckoutServiceState> emit) async {
    emit(state.copyWith(
      serviceResponse: ApiResponse.loading(),
    ));
    try {
      // Note: Cần implement method để handle CreateOrderServiceParam
      // Hiện tại remote chỉ có method nhận CreateOrderServiceModel
      // TODO: Implement checkoutOrderService method hoặc convert param

      print('CheckoutServiceOrderEvent received with param: ${event.param}');

      // Tạm thời emit error để biết event được trigger
      emit(state.copyWith(
        serviceResponse:
            ApiResponse.error('CheckoutServiceOrderEvent not implemented yet'),
      ));
    } catch (e) {
      emit(state.copyWith(
        serviceResponse: ApiResponse.error(e.toString()),
      ));
    }
  }

  Future<void> _onCheckoutServicePaymentEvent(CheckoutServicePaymentEvent event,
      Emitter<CheckoutServiceState> emit) async {
    emit(state.copyWith(
      paymentResponse: ApiResponse.loading(),
    ));
    try {
      final response =
          await checkoutServiceReponseRemote.checkoutPayment(event.orderId);
      emit(state.copyWith(
        paymentResponse: ApiResponse.completed(response.data!),
      ));
    } catch (e) {
      emit(state.copyWith(
        paymentResponse: ApiResponse.error(e.toString()),
      ));
    }
  }
}
