import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_payment_options.dart';
import '../../../../app/routers/routers_name.dart';
import '../../../../core/base/api_response/status.dart';
import 'package:multime_app/core/services/stripe_service.dart';
import '../../../../core/domain/global_dependencies.dart';
import '../../../../core/model/order/create_order_service.dart';
import '../../../../core/model/order/order_service_model.dart';
import '../../../../core/model/payment_result_data.dart';
import '../../../../service/pusher_service.dart';
import '../../../../shared/widgets/sames/build_bottom_total.dart';
import '../../../marketplace_mode/data/models/address.dart';
import '../../../marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';
import '../../data/models/services/service_model.dart';
import '../checkout_product/widgets/checkout_app_bar.dart';
import 'bloc/checkout_service_bloc.dart';
import 'widgets/checkout_service_body.dart';

class CheckoutServicePage extends StatefulWidget {
  CheckoutServicePage(
      {super.key,
      required this.serviceModel,
      required this.quantity,
      required this.unit,
      required this.total});
  final ServiceModel serviceModel;
  final int quantity;
  final String unit;
  final double total;

  @override
  State<CheckoutServicePage> createState() => _CheckoutServicePageState();
}

class _CheckoutServicePageState extends State<CheckoutServicePage> {
  final double _mockPlatformFee = 30;
  double _voucherDiscount = 0.0;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.card;

  int? customerId;
  Timer? _timeoutTimer;

  @override
  void initState() {
    super.initState();
    customerId = gs.uid; // Assuming gs.uid is the customer ID
  }

  @override
  void dispose() {
    super.dispose();
    _timeoutTimer?.cancel();
    // Unsubscribe from Pusher channel if needed
    if (customerId != null) {
      PusherService.unsubscribeFromOrderChannel(customerId!);
    }
  }

  void _onVoucherApplied(String voucherCode, double discount) {
    setState(() {
      _voucherDiscount = discount;
    });
  }

  void _onPaymentMethodChanged(PaymentMethod method) {
    setState(() {
      _selectedPaymentMethod = method;
    });
  }

  String _getPaymentMethodString(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.card:
        return 'card';
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
    }
  }

  double get finalTotal => widget.total + _mockPlatformFee - _voucherDiscount;
  double get servicePrice => widget.serviceModel.price.toDouble();

  @override
  Widget build(BuildContext context) {
    return BlocListener<CheckoutServiceBloc, CheckoutServiceState>(
      listener: (context, state) async {
        if (state.serviceResponse.status == Status.error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.serviceResponse.message ?? 'Có lỗi xảy ra'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }

        // Chỉ mở payment modal khi THÀNH CÔNG và có clientSecret hợp lệ
        else if (state.serviceResponse.status == Status.completed &&
            state.serviceResponse.data?.clientSecret != null &&
            state.serviceResponse.data!.clientSecret.isNotEmpty) {
          final addrState = context.read<AddressBloc>().state;
          final addrList = addrState.addresses?.data ?? [];
          final Address? selectedAddress = addrList.isEmpty
              ? null
              : addrList.firstWhere(
                  (add) => add.isDefault == true,
                  orElse: () => addrList.first,
                );
          if (selectedAddress == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Vui lòng chọn địa chỉ giao hàng!'),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }

          final orderData =
              context.read<CheckoutServiceBloc>().state.serviceResponse.data;
          if (orderData?.offerId != null && customerId != null) {
            _setupOrderStatusListener(customerId!);
          }

          final clientSecret = state.serviceResponse.data!.clientSecret;
          final result = await StripePaymentService.processPayment(
            paymentIntentClientSecret: clientSecret,
            amount: finalTotal,
            currency: 'USD',
          );

          if (result['success'] == true) {
            _showPaymentProcessingDialog();
            _startTimeoutTimer();
          } else {
            _cleanupOrderTracking();
            if (result['errorCode'] != 'user_cancelled') {
              _navigateToFailedResult(orderData, selectedAddress);
            }
          }
        }
      },
      child: BlocSelector<AddressBloc, AddressState, Address?>(
        selector: (addrState) {
          final list = addrState.addresses?.data ?? [];
          if (list.isEmpty) return null;
          return list.firstWhere(
            (add) => add.isDefault == true,
            orElse: () => list.first,
          );
        },
        builder: (context, selectedAddress) {
          return BlocBuilder<CheckoutServiceBloc, CheckoutServiceState>(
            builder: (context, state) {
              return Scaffold(
                appBar: const CheckoutAppBar(),
                backgroundColor: Colors.white,
                body: CheckoutServiceBody(
                  serviceModel: widget.serviceModel,
                  quantity: 1,
                  unit: widget.unit,
                  platformFee: _mockPlatformFee,
                  totalAmount: widget.total,
                  onVoucherApplied: _onVoucherApplied,
                  onPaymentMethodChanged: _onPaymentMethodChanged,
                  selectedPaymentMethod: _selectedPaymentMethod,
                ),
                bottomNavigationBar: BuildBottomTotal(
                  total: '${finalTotal.toStringAsFixed(2)}',
                  onPressed: () {
                    if (selectedAddress == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                            content: Text('Vui lòng chọn địa chỉ giao hàng!'),
                            backgroundColor: Colors.red),
                      );
                      return;
                    }

                    if (_selectedPaymentMethod == PaymentMethod.card) {
                      if (state.serviceResponse.data?.clientSecret == null) {
                        _handleCardPayment(selectedAddress);
                      } else {
                        final clientSecret =
                            state.serviceResponse.data!.clientSecret;
                        () async {
                          final orderData = context
                              .read<CheckoutServiceBloc>()
                              .state
                              .serviceResponse
                              .data;
                          if (orderData?.offerId != null &&
                              customerId != null) {
                            _setupOrderStatusListener(customerId!);
                          }
                          final result =
                              await StripePaymentService.processPayment(
                            paymentIntentClientSecret: clientSecret,
                            amount: finalTotal,
                            currency: 'USD',
                          );
                          if (result['success'] == true) {
                            _showPaymentProcessingDialog();
                            _startTimeoutTimer();
                          } else {
                            _cleanupOrderTracking();
                            if (result['errorCode'] != 'user_cancelled') {
                              _navigateToFailedResult(
                                  orderData, selectedAddress);
                            }
                          }
                        }();
                      }
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              'Phương thức thanh toán này chưa được hỗ trợ'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    }
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }

  void _handleCardPayment(Address selectedAddress) {
    context
        .read<CheckoutServiceBloc>()
        .add(CheckoutServiceInitialEvent(CreateOrderServiceModel(
          serviceId: widget.serviceModel.id.toString(),
          sellerId: widget.serviceModel.userId.toString(),
          customerId: gs.uid.toString(),
          title: widget.serviceModel.title,
          description: widget.serviceModel.description,
          price: finalTotal,
          files: widget.serviceModel.image,
          notes: '',
        )));
  }

  // Đã bỏ bottom sheet StripeCardPayment, dùng StripePaymentService trực tiếp

  void _setupOrderStatusListener(int orderId) {
    print('🔗 Setting up Pusher listener for Order ID: $orderId');

    PusherService.subscribeToOrderChannel(orderId, (orderUpdate) {
      if (orderUpdate.isPaymentSuccessful) {
        print('✅ Processing as successful payment...');
        _handlePaymentSuccess(orderUpdate);
      } else if (orderUpdate.isPaymentFailed) {
        print('❌ Processing as failed payment...');
        _handlePaymentFailed(orderUpdate);
      } else {
        print('⏳ Payment status pending or unknown...');
      }
    });
  }

  void _handlePaymentSuccess(OrderServiceModelUpdate update) {
    _cleanupOrderTracking();
    _dismissProcessingDialog();
    // Navigate to success page
    final successData = PaymentResultData.success(
      amount: finalTotal,
      itemName: widget.serviceModel.title,
      quantity: 1,
      itemPrice: finalTotal,
      unit: widget.unit,
      imageUrl: widget.serviceModel.image,
      shopName: widget.serviceModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: update.order_no,
      orderTime: DateTime.now(),
      orderStatus: update.order_status,
      phone: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.phone;
      })(),
      recipientName: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.recipientName;
      })(),
      street: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.street;
      })(),
      city: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.city;
      })(),
      userId: widget.serviceModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': successData,
      'onPrimaryAction': () {
        // Navigate to order history
      },
      'onSecondaryAction': () {
        // Continue shopping
      },
    });
  }

  void _showPaymentProcessingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false, // Prevent back button
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Processing Payment...',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('Please wait while we confirm your order.',
                  textAlign: TextAlign.center),
              SizedBox(height: 8),
              Text('This may take up to 30 seconds.',
                  style: TextStyle(color: Colors.grey, fontSize: 12)),
            ],
          ),
        ),
      ),
    );
  }

  void _cleanupOrderTracking() {
    _timeoutTimer?.cancel();
    if (customerId != null) {
      PusherService.unsubscribeFromOrderChannel(customerId!);
    }
  }

  void _dismissProcessingDialog() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context); // Close processing dialog
    }
  }

  void _handlePaymentFailed(OrderServiceModelUpdate update) {
    // Clean up order tracking and dismiss dialog
    _cleanupOrderTracking();
    _dismissProcessingDialog();

    // Navigate to failed page with retry option
    final failedData = PaymentResultData.failed(
      amount: finalTotal,
      itemName: widget.serviceModel.title,
      quantity: 1,
      itemPrice: finalTotal,
      unit: widget.unit,
      imageUrl: widget.serviceModel.image,
      shopName: widget.serviceModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: update.order_no,
      orderTime: DateTime.now(),
      orderStatus: update.order_status,
      phone: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.phone;
      })(),
      recipientName: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.recipientName;
      })(),
      street: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.street;
      })(),
      city: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.city;
      })(),
      userId: widget.serviceModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': failedData,
      'onPrimaryAction': () {
        // Retry payment
        context.pop();
      },
      'onSecondaryAction': () {
        // Contact support
        context.pop();
      },
    });
  }

  void _startTimeoutTimer() {
    _timeoutTimer = Timer(Duration(seconds: 30), () {
      _cleanupOrderTracking();
      _dismissProcessingDialog();
      _handlePaymentTimeout();
    });
  }

  void _handlePaymentTimeout() {
    // Payment successful but backend confirmation timed out
    final timeoutData = PaymentResultData.failed(
      amount: finalTotal,
      itemName: widget.serviceModel.title,
      quantity: 1,
      itemPrice: finalTotal,
      unit: widget.unit,
      imageUrl: widget.serviceModel.image,
      shopName: widget.serviceModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: 'Processing...',
      orderTime: DateTime.now(),
      orderStatus: 'Pending',
      phone: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.phone;
      })(),
      recipientName: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.recipientName;
      })(),
      street: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.street;
      })(),
      city: (() {
        final s = context.read<AddressBloc>().state;
        final list = s.addresses?.data ?? [];
        if (list.isEmpty) return null;
        final a = list.firstWhere(
          (add) => add.isDefault == true,
          orElse: () => list.first,
        );
        return a.city;
      })(),
      userId: widget.serviceModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': timeoutData,
      'onPrimaryAction': () {
        // Check order history - navigate back to home
        context.pop();
      },
      'onSecondaryAction': () {
        // Continue shopping
        context.pop();
      },
    });
  }

  void _navigateToFailedResult(
      OrderServiceModel? orderData, Address selectedAddress) {
    final failedData = PaymentResultData.failed(
      amount: widget.total,
      itemName: widget.serviceModel.title,
      quantity: 1,
      itemPrice: finalTotal,
      unit: widget.unit,
      imageUrl: widget.serviceModel.image,
      shopName: widget.serviceModel.shop?.shopName ?? 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: orderData?.orderNo ?? 'FAILED',
      orderTime: DateTime.now(),
      orderStatus: 'Awaiting payment',
      phone: selectedAddress.phone,
      recipientName: selectedAddress.recipientName,
      street: selectedAddress.street,
      city: selectedAddress.city,
      userId: widget.serviceModel.userId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': failedData,
      'onPrimaryAction': () {
        context.pop();
      },
      'onSecondaryAction': () {
        context.pop();
      },
    });
  }
}
