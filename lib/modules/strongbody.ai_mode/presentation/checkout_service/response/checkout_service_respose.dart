import 'package:flutter/material.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/order/order_offer_model.dart';
import 'package:multime_app/core/model/order/order_service_model.dart';

import '../../../../../core/base/api_response/api_response.dart';
import '../../../../../core/constants/api_endpoints.dart';
import '../../../../../core/model/order/create_order_service.dart';
import '../../../../../core/network/api.dart';
import '../../../../../core/network/api_type.dart';

abstract class CheckoutServiceReponseRemote {
  Future<ApiResponse<OrderServiceModel>> createOrderService(
      CreateOrderServiceModel param);
  Future<ApiResponse<OrderOfferModel>> checkoutPayment(int orderId);
  // Future<ApiResponse<OrderServiceModel>> checkoutOrderService(
  //     OrderServiceParam param);
}

class CheckoutServiceReponseRemoteImp implements CheckoutServiceReponseRemote {
  final ApiClient _apiClient;

  CheckoutServiceReponseRemoteImp(this._apiClient);

  @override
  Future<ApiResponse<OrderServiceModel>> createOrderService(
      CreateOrderServiceModel param) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.createOrderService,
        method: ApiType.post,
        data: param.toJson(),
      );
      final data = response['data'];

      debugPrint('Create Order Service Response: $data');
      return ApiResponse.completed(
        OrderServiceModel.fromJson(data),
      );
    } on FormatException catch (e) {
      return ApiResponse.error(e.message);
    } catch (e) {
      return ApiResponse.error('An unexpected error occurred');
    }
  }

  @override
  Future<ApiResponse<OrderOfferModel>> checkoutPayment(int orderId) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.paymentServiceBuyer}/$orderId/payment-intent',
          method: ApiType.post,
          headers: {
            "Authorization": "Bearer ${gs.accessToken}",
            "Scope": AppConstants.multi_me
          });

      // Parse response data to OrderOfferModel
      final orderOfferModel = OrderOfferModel.fromJson(response['data']);
      return ApiResponse.completed(orderOfferModel);
    } catch (e) {
      return ApiResponse.error('An unexpected error occurred: ${e.toString()}');
    }
  }

  // @override
  // Future<ApiResponse<OrderServiceModel>> checkoutOrderService(
  //     OrderServiceParam param) async {

  // try {
  //   final response = await _apiClient.request(
  //     path: ApiConst.checkoutService,
  //     method: ApiType.post,
  //     data: param.toJson(),
  //   );
  //   final data = response['data'];
  //   return ApiResponse.completed(
  //     OrderServiceModel.fromJson(data),
  //   );
  // } on FormatException catch (e) {
  //   return ApiResponse.error(e.message);
  // } catch (e) {
  //   return ApiResponse.error('An unexpected error occurred');
  // }
  // }
}
