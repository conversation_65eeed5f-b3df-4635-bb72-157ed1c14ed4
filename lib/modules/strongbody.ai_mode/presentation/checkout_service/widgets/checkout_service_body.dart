import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_address_section.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_order_details.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_payment_details.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_payment_options.dart';

class CheckoutServiceBody extends StatefulWidget {
  const CheckoutServiceBody({
    super.key,
    required this.serviceModel,
    required this.quantity,
    required this.unit,
    required this.platformFee,
    required this.totalAmount,
    this.onVoucherApplied,
    this.onPaymentMethodChanged,
    this.selectedPaymentMethod = PaymentMethod.card,
  });

  final ServiceModel serviceModel;
  final int quantity;
  final String unit;
  final double platformFee;
  final double totalAmount;
  final Function(String voucherCode, double discount)? onVoucherApplied;
  final Function(PaymentMethod)? onPaymentMethodChanged;
  final PaymentMethod selectedPaymentMethod;

  @override
  State<CheckoutServiceBody> createState() => _CheckoutServiceBodyState();
}

class _CheckoutServiceBodyState extends State<CheckoutServiceBody> {
  @override
  Widget build(BuildContext context) {
    final double servicePrice = widget.serviceModel.price > 0.0
        ? widget.serviceModel.price.toDouble()
        : 0.0;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Address Section
          const CheckoutAddressSection(),
          Gap(24.h),

          // Order Details Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: CheckoutOrderDetails(
              title: widget.serviceModel.title,
              quantity: 1,
              price: servicePrice,
              mediaUrls: widget.serviceModel.image,
              shopName: widget.serviceModel.shop?.shopName ?? 'Unknown Shop',
              userId: widget.serviceModel.userId,
            ),
          ),
          Gap(24.h),

          // Payment Options Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: CheckoutPaymentOptions(
              selectedMethod: widget.selectedPaymentMethod,
              onPaymentMethodChanged: (PaymentMethod method) {
                widget.onPaymentMethodChanged?.call(method);
              },
            ),
          ),
          Gap(16.h),

          // Payment Details Section with Voucher
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: CheckoutPaymentDetails(
              price: widget.totalAmount,
              service: widget.platformFee,
              total: widget.totalAmount + widget.platformFee,
              onVoucherApplied: widget.onVoucherApplied,
            ),
          ),
          Gap(50.h),
        ],
      ),
    );
  }
}
