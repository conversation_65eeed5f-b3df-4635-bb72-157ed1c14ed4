import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/model/blog_model.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/bloc/detail_blog_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/widgets/custome_appbar_scrollview_blog.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_blog.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:html/parser.dart' as html_parser;

import '../../../../shared/widgets/list_card/List_view_generic.dart';

class DetailBlogPage extends StatefulWidget {
  const DetailBlogPage({super.key, required this.blog});
  final BlogModel blog;
  @override
  State<DetailBlogPage> createState() => _DetailBlogPageState();
}

class _DetailBlogPageState extends State<DetailBlogPage>
    with SingleTickerProviderStateMixin {
  int _current = 0;
  bool isCollapsed = false;
  late AutoScrollController scrollController;
  late TabController tabController;
  bool pauseRectGetterIndex = false;
  final double expandedHeight = 5;

  static const int sectionCount = 4;
  final List<GlobalKey> sectionKeys = List.generate(4, (_) => GlobalKey());

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: sectionCount, vsync: this);
    scrollController = AutoScrollController();
    scrollController.addListener(_scrollListener);
  }

  final List<String> hashtags = [
    'Health',
    'Nutrition',
    'Wellness',
    'Fitness',
  ];

  Widget buildIconButton(String iconPath, VoidCallback onPressed,
      {double size = 24.0}) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Theme.of(context).greyScale200(context),
      ),
      child: IconButton(
        icon: SvgPicture.asset(iconPath, width: size, height: size),
        onPressed: onPressed,
      ),
    );
  }

  List<String> extractHeadings(String html) {
    final document = html_parser.parse(html);
    return document
        .getElementsByTagName('h2')
        .map((e) => e.text.trim())
        .toList();
  }

  void _scrollListener() {
    final double threshold = expandedHeight -
        kToolbarHeight -
        MediaQuery.of(context).padding.top -
        10;
    bool shouldCollapse =
        scrollController.hasClients && scrollController.offset > threshold;
    if (isCollapsed != shouldCollapse) {
      setState(() {
        isCollapsed = shouldCollapse;
      });
    }

    if (!pauseRectGetterIndex) {
      for (int i = sectionKeys.length - 1; i >= 0; i--) {
        final keyContext = sectionKeys[i].currentContext;
        if (keyContext != null) {
          final box = keyContext.findRenderObject();
          if (box is RenderBox) {
            final offset = box.localToGlobal(Offset.zero,
                ancestor: context.findRenderObject());
            if (offset.dy <= kToolbarHeight + 60) {
              if (tabController.index != i) {
                tabController.animateTo(i);
              }
              break;
            }
          }
        }
      }
    }
  }

  void animateAndScrollTo(int index) async {
    pauseRectGetterIndex = true;
    tabController.animateTo(index);
    await scrollController.scrollToIndex(index,
        preferPosition: AutoScrollPosition.begin);
    pauseRectGetterIndex = false;
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tags = [
      {
        "label": "Medical Professional",
        "color": const Color(0xFFE7F8EC),
        "textColor": const Color(0xFF19B87A)
      },
      {
        "label": "Nutrition Consultant",
        "color": const Color(0xFFFFF5E4),
        "textColor": const Color(0xFFFFA629)
      },
    ];
    return BlocBuilder<DetailBlogBloc, DetailBlogState>(
      builder: (context, state) {
        final blogLike =
            state.blogLike.where((blog) => blog.id != widget.blog.id).toList();
        final blogTrending = state.blogTrending
            .where((blog) => blog.id != widget.blog.id)
            .toList();
        return Scaffold(
          body: CustomScrollView(
            controller: scrollController,
            slivers: [
              CustomeAppbarScrollviewBlog(
                  tabController: tabController,
                  onTabTap: animateAndScrollTo,
                  isCollapsed: isCollapsed,
                  expandedHeight: expandedHeight,
                  currentImage: _current,
                  onCarouselChanged: (index) {
                    setState(() {
                      _current = index;
                    });
                  }),
              SliverToBoxAdapter(
                  child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Wrap(
                          spacing: 8.0,
                          children: tags.map((tag) {
                            return Chip(
                              label: Text('${tag["label"]}'),
                              backgroundColor: tag["color"] as Color,
                              labelStyle: Theme.of(context)
                                  .textTheme
                                  .lightBodySmallBold
                                  .copyWith(
                                    color: tag["textColor"] as Color,
                                  ),
                            );
                          }).toList(),
                        ),
                        Gap(12.h),
                        Text(widget.blog.title,
                            style:
                                Theme.of(context).textTheme.lightHeadingSmall),
                        Gap(8.h),
                        Text(
                          TFormatter.formatDate(widget.blog.createdAt),
                          style:
                              Theme.of(context).textTheme.lightBodyLargeRegular,
                        ),
                        Gap(16.h),
                        widget.blog.images.isNotEmpty &&
                                widget.blog.images[0].isNotEmpty
                            ? Image.network(
                                widget.blog.images[0],
                                errorBuilder: (context, error, stackTrace) {
                                  return Image.asset(
                                      AppAssets.servicePlaceholder);
                                },
                              )
                            : Image.asset(AppAssets.servicePlaceholder),
                        Gap(16.h),
                        HtmlWidget(
                          widget.blog.content,
                          textStyle:
                              Theme.of(context).textTheme.lightBodyLargeRegular,
                        ),
                        Wrap(
                          children: hashtags.map((hashtag) {
                            return Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: Chip(
                                  side: BorderSide.none,
                                  label: Text('#$hashtag'),
                                  backgroundColor:
                                      Theme.of(context).greyScale300(context),
                                  labelStyle: Theme.of(context)
                                      .textTheme
                                      .lightBodySmallBold
                                      .copyWith(
                                        color: Theme.of(context)
                                            .greyScale600(context),
                                      ),
                                ));
                          }).toList(),
                        ),
                        Gap(16.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            buildIconButton(AppAssets.iconFacebookSvg, () {}),
                            buildIconButton(AppAssets.iconTwitterSvg, () {}),
                            buildIconButton(AppAssets.iconLinkSvg, () {}),
                            buildIconButton(AppAssets.iconSaveSvg, () {}),
                            buildIconButton(AppAssets.iconprinterSvg, () {}),
                          ],
                        )
                      ],
                    ),
                  ),
                  ListViewGeneric<BlogModel>(
                    isLoading: state.isLoading,
                    isSuccess: state.isSuccess,
                    items: blogLike,
                    onItemTap: (blog) =>
                        context.push(RouteName.detailBlogPage, extra: blog),
                    showMore: true,
                    title: 'May you Like',
                    itemBuilder: (context, blog, index) => CardBlog(blog: blog),
                    isShowButton: false,
                    isListHome: true,
                  ),
                  Gap(16.h),
                  ListViewGeneric<BlogModel>(
                    isLoading: state.isLoading,
                    isSuccess: state.isSuccess,
                    items: blogTrending,
                    onItemTap: (blog) =>
                        context.push(RouteName.detailBlogPage, extra: blog),
                    showMore: true,
                    title: 'Trending',
                    itemBuilder: (context, blog, index) => CardBlog(blog: blog),
                    isShowButton: false,
                    isListHome: true,
                  )
                ],
              ))
            ],
          ),
        );
      },
    );
  }
}
