import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/app_loader/horizontal_product_shimmer.dart';
import 'package:multime_app/shared/widgets/layout/listview_layout.dart';

class ListViewGeneric<T> extends StatelessWidget {
  const ListViewGeneric({
    super.key,
    required this.items,
    required this.isLoading,
    required this.itemBuilder,
    this.title = 'Trending',
    this.showMore = true,
    this.onItemTap,
    this.onShowMoreTap,
    this.emptyMessage = 'No items available',
  });

  final List<T> items;
  final bool isLoading;
  final String title;
  final bool showMore;
  final String emptyMessage;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final void Function(T item)? onItemTap;
  final VoidCallback? onShowMoreTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
          child: Text(
            title,
            style: Theme.of(context).textTheme.lightBodyLargeBold,
          ),
        ),
        Gap(16.h),
        isLoading
            ? THorizontalProductShimmer()
            : items.isEmpty
                ? Center(
                    child: Text(
                      emptyMessage,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                    ),
                  )
                : SizedBox(
                    height: 270.h,
                    child: ListviewLayout(
                      itemCount: items.length > 5 ? 5 : items.length,
                      itemBuilder: (context, i) {
                        final item = items[i];
                        return Padding(
                          padding: const EdgeInsets.only(left: 16),
                          child: GestureDetector(
                            onTap: () => onItemTap?.call(item),
                            child: itemBuilder(context, item, i),
                          ),
                        );
                      },
                    ),
                  ),
        if (showMore) ...[
          Gap(16.h),
          Center(
            child: GestureDetector(
              onTap: onShowMoreTap,
              child: Text(
                'Show more',
                style:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Theme.of(context).informationBase(context),
                          decoration: TextDecoration.underline,
                          decorationColor:
                              Theme.of(context).informationBase(context),
                        ),
              ),
            ),
          )
        ]
      ],
    );
  }
}
