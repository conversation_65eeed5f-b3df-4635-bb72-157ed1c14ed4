import 'package:flutter/material.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/widgets/appbar_detail_blog.dart';

class CustomeAppbarScrollviewBlog extends StatelessWidget {
  final TabController tabController;
  final void Function(int) onTabTap;
  final bool isCollapsed;
  final double expandedHeight;
  final int currentImage;
  final void Function(int) onCarouselChanged;

  const CustomeAppbarScrollviewBlog({
    Key? key,
    required this.tabController,
    required this.onTabTap,
    required this.isCollapsed,
    required this.expandedHeight,
    required this.currentImage,
    required this.onCarouselChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      pinned: true,
      floating: false,
      snap: false,
      expandedHeight: expandedHeight,
      backgroundColor: Colors.white,
      automaticallyImplyLeading: false,
      title: isCollapsed ? const AppbarDetailBlog(isBackground: false) : null,
      bottom: isCollapsed
          ? PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: Container(
                color: Colors.white,
                child: TabBar(
                  controller: tabController,
                  labelColor: Theme.of(context).primary(context),
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: Theme.of(context).textTheme.lightBodyLargeBold,
                  unselectedLabelStyle:
                      Theme.of(context).textTheme.lightBodyLargeRegular,
                  indicatorColor: Theme.of(context).primary(context),
                  indicatorWeight: 3,
                  isScrollable: true,
                  tabAlignment: TabAlignment.start,
                  dividerColor: Colors.transparent,
                  padding: EdgeInsets.zero,
                  onTap: onTabTap,
                  tabs: const [
                    Tab(text: 'Service Packages'),
                    Tab(text: 'Review'),
                    Tab(text: 'About Service'),
                    Tab(text: 'FAQ'),
                  ],
                ),
              ),
            )
          : null,
      flexibleSpace: !isCollapsed
          ? Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
              child: AppbarDetailBlog(isBackground: true),
            )
          : null,
    );
  }
}
