import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

class AppbarDetailBlog extends StatelessWidget {
  const AppbarDetailBlog({super.key, required this.isBackground});

  final bool isBackground;
  @override
  Widget build(BuildContext context) {
    Widget _buildIconButton(String assetName, VoidCallback onPressed) {
      return IconButton(
        style: IconButton.styleFrom(
          backgroundColor: isBackground
              ? Theme.of(context).secondary(context).withOpacity(0.5)
              : null,
        ),
        icon: SvgPicture.asset(assetName,
            color: isBackground
                ? Theme.of(context).whitePrimary(context)
                : Theme.of(context).blackPrimary(context)),
        onPressed: onPressed,
      );
    }

    return AppBar(
      backgroundColor: Colors.transparent,
      leading: _buildIconButton(AppAssets.arrowLeftSvg, () => context.pop()),
      actions: [
        _buildIconButton(AppAssets.messageAppbarSvg, () {}),
        _buildIconButton(AppAssets.heart_icon, () {}),
        _buildIconButton(AppAssets.exportSvg, () {}),
      ],
    );
  }
}
