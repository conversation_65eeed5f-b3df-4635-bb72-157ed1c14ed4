part of 'detail_blog_bloc.dart';

class DetailBlogEvent extends Equatable {
  const DetailBlogEvent();

  @override
  List<Object?> get props => [];
}

class FetchDetailBlogLikeEvent extends DetailBlogEvent {
  final int categoryId;
  const FetchDetailBlogLikeEvent({
    required this.categoryId,
  });

  @override
  List<Object?> get props => [categoryId];
}

class FetchDetailBlogTrendingEvent extends DetailBlogEvent {
  const FetchDetailBlogTrendingEvent();

  @override
  List<Object?> get props => [];
}
