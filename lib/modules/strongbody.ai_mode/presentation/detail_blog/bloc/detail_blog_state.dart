part of 'detail_blog_bloc.dart';

class DetailBlogState extends Equatable {
  final bool isLoading;
  final String errorMessage;
  final bool isSuccess;
  final bool isError;
  final List<BlogModel> blogLike;
  final List<BlogModel> blogTrending;

  DetailBlogState({
    this.isLoading = false,
    this.errorMessage = '',
    this.isSuccess = false,
    this.isError = false,
    this.blogLike = const [],
    this.blogTrending = const [],
  });

  @override
  List<Object?> get props =>
      [isLoading, errorMessage, isSuccess, isError, blogLike, blogTrending];

  DetailBlogState copyWith({
    bool? isLoading,
    String? errorMessage,
    bool? isSuccess,
    bool? isError,
    List<BlogModel>? blogLike,
    List<BlogModel>? blogTrending,
  }) {
    return DetailBlogState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
      isError: isError ?? this.isError,
      blogLike: blogLike ?? this.blogLike,
      blogTrending: blogTrending ?? this.blogTrending,
    );
  }
}
