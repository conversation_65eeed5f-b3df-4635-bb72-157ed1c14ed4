
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/model/blog_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

part 'detail_blog_event.dart';
part 'detail_blog_state.dart';

class DetailBlogBloc extends Bloc<DetailBlogEvent, DetailBlogState> {
  final HomeStrongbodyAiRepository homeStrongbodyAiRepository;
  DetailBlogBloc({required this.homeStrongbodyAiRepository})
      : super(DetailBlogState()) {
    on<FetchDetailBlogLikeEvent>(_onFetchDetailBlogLikeEvent);
    on<FetchDetailBlogTrendingEvent>(_onFetchDetailBlogTrendingEvent);
  }

  void _onFetchDetailBlogLikeEvent(
      FetchDetailBlogLikeEvent event, Emitter<DetailBlogState> emit) async {
    emit(state.copyWith(isLoading: true, isSuccess: false));
    try {
      final typeService = AppConstants.like;
      final param = {
        'category_id': event.categoryId,
      };
      final blogLike = await homeStrongbodyAiRepository.getServiceByQuery(
          typeService,
          resourceType: AppConstants.post,
          param: param);
      emit(state.copyWith(
          isLoading: false,
          isSuccess: true,
          blogLike: blogLike.data!.data.list
              .map((e) => BlogModel.fromJson(e))
              .toList()));
    } catch (e) {
      emit(state.copyWith(
          isLoading: false, isError: true, errorMessage: e.toString()));
    }
  }

  void _onFetchDetailBlogTrendingEvent(
      FetchDetailBlogTrendingEvent event, Emitter<DetailBlogState> emit) async {
    emit(state.copyWith(isLoading: true, isSuccess: false));
    try {
      final blogTrending = await homeStrongbodyAiRepository.getServiceByQuery(
          AppConstants.trending,
          resourceType: AppConstants.post);
      emit(state.copyWith(
          isLoading: false,
          isSuccess: true,
          blogTrending: blogTrending.data!.data.list
              .map((e) => BlogModel.fromJson(e))
              .toList()));
    } catch (e) {
      emit(state.copyWith(
          isLoading: false, isError: true, errorMessage: e.toString()));
    }
  }
}
