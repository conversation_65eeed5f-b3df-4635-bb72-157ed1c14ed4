
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../../../core/constants/app_assets.dart';

class AppBarPostRequest extends StatefulWidget implements PreferredSizeWidget {
  const AppBarPostRequest({
    super.key,
  });

  @override
  State<AppBarPostRequest> createState() => _AppBarPostRequestState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _AppBarPostRequestState extends State<AppBarPostRequest> {
  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(kToolbarHeight),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.black,
              Color(0XFF000040),
            ],
            begin: Alignment.topLeft,
            end: Alignment.topRight,
          ),
        ),
        child: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            'Request for Offer',
            style: Theme.of(context)
                .textTheme
                .headlineMedium!
                .copyWith(color: Colors.white),
          ),
          leading: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              width: 50,
              height: 50,
              child: GestureDetector(
                child: SvgPicture.asset(
                  AppAssets.arrowLeftSvg,
                  fit: BoxFit.contain,
                  // ignore: deprecated_member_use
                  color: Colors.white,
                ),
                onTap: () {
                  context.pop();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
