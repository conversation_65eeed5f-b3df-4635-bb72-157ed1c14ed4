import 'dart:io';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../bloc/post_buyer_request_bloc.dart';
import '../bloc/post_buyer_request_event.dart';
import '../bloc/post_buyer_request_state.dart';

class UploadFileBuyerRequest extends StatefulWidget {
  const UploadFileBuyerRequest({super.key});

  @override
  State<UploadFileBuyerRequest> createState() => _UploadFileBuyerRequestState();
}

class _UploadFileBuyerRequestState extends State<UploadFileBuyerRequest> {
  final ImagePicker _picker = ImagePicker();

  Future<void> _selectFile() async {
    try {
      final bloc = context.read<PostBuyerRequestBloc>();

      // If file already exists, show replace confirmation
      if (bloc.state.mediaFiles.isNotEmpty) {
        final shouldReplace = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Replace File'),
            content: const Text(
                'You can only select one file. Do you want to replace the current file?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Replace'),
              ),
            ],
          ),
        );

        if (shouldReplace != true) return;
      }

      // Show file selection options
      await _showFileSelectionOptions();
    } catch (e) {
      print('File selection error: $e');
      _showError('An error occurred while selecting file. Please try again.');
    }
  }

  Future<void> _showFileSelectionOptions() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (context) => SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: Icon(
                  Icons.image,
                  color: Theme.of(context).primaryColor,
                ),
                title: const Text(
                  'Select Image',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _selectImage();
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.picture_as_pdf,
                  color: Colors.red[600],
                ),
                title: const Text(
                  'Select PDF',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _selectPDF();
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectImage() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxHeight: 1080,
        maxWidth: 1080,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        await _processSelectedFile(File(pickedFile.path), 'Image');
      }
    } catch (e) {
      print('Image selection error: $e');
      _showError('Failed to select image. Please try again.');
    }
  }

  Future<void> _selectPDF() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        // Check file size (max 10MB)
        final fileSize = await file.length();
        if (fileSize > 10 * 1024 * 1024) {
          _showError('PDF file size must be less than 10MB');
          return;
        }

        await _processSelectedFile(file, 'PDF');
      }
    } catch (e) {
      print('PDF selection error: $e');
      _showError('Failed to select PDF. Please try again.');
    }
  }

  Future<void> _processSelectedFile(File file, String fileType) async {
    try {
      final bloc = context.read<PostBuyerRequestBloc>();

      // Check if file exists and is readable
      if (await file.exists()) {
        final fileSize = await file.length();
        if (fileSize > 0) {
          // Replace the current file with the new one
          bloc.add(UpdateBuyerRequestMedia([file]));

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('$fileType added successfully'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 1),
              ),
            );
          }
          return;
        }
      }

      _showError('Selected file is not valid');
    } catch (e) {
      print('File processing error: $e');
      _showError('Failed to process selected file. Please try again.');
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _removeFile() async {
    // Show confirmation dialog
    final shouldRemove = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove File'),
        content: const Text('Are you sure you want to remove this file?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (shouldRemove == true) {
      try {
        // Remove file from state
        context.read<PostBuyerRequestBloc>().add(UpdateBuyerRequestMedia([]));

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text('File removed successfully'),
                ],
              ),
              backgroundColor: Colors.orange[600],
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } catch (e) {
        print('Error removing file: $e');
        _showError('Failed to remove file. Please try again.');
      }
    }
  }

  String _getFileExtension(String path) {
    return path.split('.').last.toLowerCase();
  }

  bool _isImageFile(String path) {
    final extension = _getFileExtension(path);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  bool _isPdfFile(String path) {
    return _getFileExtension(path) == 'pdf';
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PostBuyerRequestBloc, PostBuyerRequestState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: SizeTransition(
                    sizeFactor: animation,
                    child: child,
                  ),
                );
              },
              child: state.mediaFiles.isNotEmpty
                  ? Column(
                      key: const ValueKey('file_selected'),
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Selected File (1/1)',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                            ),
                          ],
                        ),
                        Gap(8.h),
                        _buildFileItem(state.mediaFiles.first),
                      ],
                    )
                  : Container(
                      key: const ValueKey('upload_prompt'),
                      child: _buildUploadPrompt(),
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFileItem(File file) {
    final fileName = file.path.split('/').last;
    final isImage = _isImageFile(file.path);
    final isPdf = _isPdfFile(file.path);

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // File preview
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              // color: Colors.grey[100],
            ),
            child: isImage
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      file,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.broken_image,
                          color: Colors.grey[400],
                          size: 30,
                        );
                      },
                    ),
                  )
                : isPdf
                    ? Icon(
                        Icons.picture_as_pdf,
                        color: Colors.red[400],
                        size: 30,
                      )
                    : Icon(
                        Icons.insert_drive_file,
                        color: Colors.grey[400],
                        size: 30,
                      ),
          ),
          Gap(12.w),
          // File info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Gap(4.h),
                FutureBuilder<int>(
                  future: file.length(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      final sizeInKB = snapshot.data! / 1024;
                      final sizeText = sizeInKB > 1024
                          ? '${(sizeInKB / 1024).toStringAsFixed(1)} MB'
                          : '${sizeInKB.toStringAsFixed(1)} KB';
                      return Text(
                        '$sizeText • ${isImage ? 'Image' : isPdf ? 'PDF' : 'File'}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).textSecondary(context),
                            ),
                      );
                    }
                    return Text(
                      isImage
                          ? 'Image'
                          : isPdf
                              ? 'PDF'
                              : 'File',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).textSecondary(context),
                          ),
                    );
                  },
                ),
              ],
            ),
          ),
          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _selectFile,
                icon: Icon(
                  Icons.edit,
                  color: Theme.of(context).backgroundRed(context),
                  size: 20,
                ),
                tooltip: 'Replace file',
              ),
              IconButton(
                onPressed: _removeFile,
                icon: const Icon(
                  Icons.delete,
                  color: Colors.red,
                  size: 20,
                ),
                tooltip: 'Remove file',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUploadPrompt() {
    return GestureDetector(
      onTap: _selectFile,
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(10),
        dashPattern: const [5, 3],
        color: Theme.of(context).textSecondary(context),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                Gap(18.h),
                SvgPicture.asset(
                  AppAssets.uploadImageSvg,
                ),
                Gap(8.h),
                Text(
                  'Click here to upload file',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Gap(8.h),
                Text.rich(
                  TextSpan(
                    text:
                        "Supports image formats (JPG, PNG) and PDF files up to ",
                    style: Theme.of(context).textTheme.labelSmall!.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                    children: [
                      TextSpan(
                        text: "10MB",
                        style: TextStyle(
                          color: Theme.of(context).textPrimary(context),
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
                Gap(18.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
// 