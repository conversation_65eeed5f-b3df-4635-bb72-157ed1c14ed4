import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import '../../../../../core/constants/app_assets.dart';

class PostRequestCheck extends StatelessWidget {
  final bool checkSuccess;
  const PostRequestCheck({super.key, required this.checkSuccess});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text('Post buying Request',
              style: Theme.of(context).textTheme.headlineMedium!)),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            checkSuccess
                ? SvgPicture.asset(AppAssets.post_request_success)
                : SvgPicture.asset(AppAssets.post_service_failed),
            Gap(20),
            Text(
              checkSuccess
                  ? 'Your request has been submitted successfully!'
                  : 'Your request has failed. Please try again.',
              style: Theme.of(context).textTheme.headlineSmall!,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              context.pop();
            },
            child: Container(
              width: double.infinity,
              height: 50.h,
              margin: const EdgeInsets.symmetric(horizontal: 15),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondary,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.secondary,
                  width: 1,
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                'Back to Home',
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      color: Colors.white,
                    ),
              ),
            ),
          ),
          Gap(20),
          GestureDetector(
            onTap: () {},
            child: Container(
              width: double.infinity,
              height: 50.h,
              margin: const EdgeInsets.symmetric(horizontal: 15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.secondary,
                  width: 1,
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                'Manage my Request',
                style: Theme.of(context).textTheme.bodyLarge!,
              ),
            ),
          ),
          Gap(30),
        ],
      ),
    );
  }
}
