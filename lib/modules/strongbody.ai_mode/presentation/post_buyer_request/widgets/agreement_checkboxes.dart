import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class AgreementCheckboxes extends StatelessWidget {
  final bool agreeToShareBusinessCard;
  final bool agreeToPostingRules;
  final ValueChanged<bool> onBusinessCardChanged;
  final ValueChanged<bool> onPostingRulesChanged;

  const AgreementCheckboxes({
    super.key,
    required this.agreeToShareBusinessCard,
    required this.agreeToPostingRules,
    required this.onBusinessCardChanged,
    required this.onPostingRulesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Checkbox 1: Business Card
        _buildCheckboxRow(
          context: context,
          value: agreeToShareBusinessCard,
          onChanged: onBusinessCardChanged,
          children: [
            TextSpan(text: "I agree to share my "),
            TextSpan(
              text: "Business Card",
              style: TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextSpan(text: " with quoted suppliers."),
          ],
        ),

        Gap(12.h),

        // Checkbox 2: Posting Rules
        _buildCheckboxRow(
          context: context,
          value: agreeToPostingRules,
          onChanged: onPostingRulesChanged,
          children: [
            TextSpan(
                text: "I have read, understood and agreed to abide by the "),
            TextSpan(
              text: "Buying Request Posting Rules",
              style: TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCheckboxRow({
    required BuildContext context,
    required bool value,
    required ValueChanged<bool> onChanged,
    required List<TextSpan> children,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Checkbox(
            value: value,
            onChanged: (newValue) => onChanged(newValue ?? false),
            activeColor: Colors.red,
            checkColor: Colors.white,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(top: 10.h, left: 8.w),
            child: RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 13.sp,
                      color: Colors.black87,
                    ),
                children: children,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
