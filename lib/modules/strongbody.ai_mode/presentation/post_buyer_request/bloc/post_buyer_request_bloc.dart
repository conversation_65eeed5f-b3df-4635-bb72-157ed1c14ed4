import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/category.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/category_repository.dart';
import 'package:dio/dio.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/buyer_request_repository.dart';
import 'post_buyer_request_event.dart';
import 'post_buyer_request_state.dart';
import 'package:http_parser/http_parser.dart';

class PostBuyerRequestBloc
    extends Bloc<PostBuyerRequestEvent, PostBuyerRequestState> {
  final BuyerRequestRepository _repository;
  final CategoryRepositoryRemote _categoryRepositoryRemote;

  PostBuyerRequestBloc(this._repository, this._categoryRepositoryRemote)
      : super(const PostBuyerRequestState()) {
    on<UpdateBuyerRequestTitle>(_onUpdateTitle);
    on<UpdateBuyerRequestDescription>(_onUpdateDescription);
    on<UpdateBuyerRequestMedia>(_onUpdateMedia);
    on<RemoveBuyerRequestMedia>(_onRemoveMedia);
    on<SubmitBuyerRequest>(_onSubmitBuyerRequest);
    on<UpdateBuyerCategory>(_onUpdateBuyerCategory);
    on<GetCategoriesBuyerRequest>(_onGetCategoriesEvent);
    on<SubmitUserRequestWithFiles>(_onSubmitUserRequestWithFiles);
  }

  void _onUpdateTitle(
      UpdateBuyerRequestTitle event, Emitter<PostBuyerRequestState> emit) {
    emit(state.copyWith(title: event.title));
  }

  void _onUpdateDescription(UpdateBuyerRequestDescription event,
      Emitter<PostBuyerRequestState> emit) {
    emit(state.copyWith(description: event.description));
  }

  void _onUpdateMedia(
      UpdateBuyerRequestMedia event, Emitter<PostBuyerRequestState> emit) {
    // Replace the entire mediaFiles list with the new one
    emit(state.copyWith(mediaFiles: event.mediaFiles));
  }

  void _onRemoveMedia(
      RemoveBuyerRequestMedia event, Emitter<PostBuyerRequestState> emit) {
    final updatedMediaFiles = List<File>.from(state.mediaFiles)
      ..removeAt(event.index);
    emit(state.copyWith(mediaFiles: updatedMediaFiles));
  }

  void _onUpdateBuyerCategory(
      UpdateBuyerCategory event, Emitter<PostBuyerRequestState> emit) {
    emit(state.copyWith(category: event.category));
  }

  void _onGetCategoriesEvent(GetCategoriesBuyerRequest event,
      Emitter<PostBuyerRequestState> emit) async {
    try {
      // Prevent duplicate API calls if categories are already loaded or loading
      if (state.categories?.status == Status.loading) {
        return;
      }

      if (state.categories?.status == Status.completed &&
          state.categories?.data?.items != null &&
          state.categories!.data!.items!.isNotEmpty) {
        return;
      }

      emit(state.copyWith(categories: const ApiResponse.loading()));
      CategoryList products = await _categoryRepositoryRemote.getCategoryList();
      emit(state.copyWith(categories: ApiResponse.completed(products)));
    } on NetworkException catch (e) {
      print('Network error fetching categories: ${e.message}');
      emit(state.copyWith(categories: ApiResponse.error(e.message)));
    } catch (e) {
      print('General error fetching categories: $e');
      emit(state.copyWith(categories: ApiResponse.error(e.toString())));
    }
  }

  Future<void> _onSubmitBuyerRequest(
      SubmitBuyerRequest event, Emitter<PostBuyerRequestState> emit) async {
    try {
      emit(state.copyWith(createBuyerRequest: const ApiResponse.loading()));
      var files = await Future.wait(event.mediaFiles.map((img) async {
        var file = await MultipartFile.fromFile(
          img.path,
          contentType: MediaType('image',
              img.path.split('.').last), // Or whatever the image type is
        );
        return file;
      }).toList());
      // Create FormData for multipart request
      final formData = FormData.fromMap({
        'title': event.data['title'],
        'description': event.data['description'],
        'category_id': state.category,
        'files': files,
      });

      final response = await _repository.createBuyerRequest(formData);

      emit(state.copyWith(
        createBuyerRequest: ApiResponse.completed(response),
      ));
    } catch (e) {
      emit(state.copyWith(
        createBuyerRequest: ApiResponse.error(e.toString()),
      ));
    }
  }

  Future<void> _onSubmitUserRequestWithFiles(SubmitUserRequestWithFiles event,
      Emitter<PostBuyerRequestState> emit) async {
    try {
      emit(state.copyWith(createUserRequest: const ApiResponse.loading()));

      final response =
          await _repository.createUserRequestWithFiles(event.formData);

      emit(state.copyWith(
        createUserRequest: ApiResponse.completed(response),
      ));
    } catch (e) {
      emit(state.copyWith(
        createUserRequest: ApiResponse.error(e.toString()),
      ));
    }
  }
}
