import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:dio/dio.dart';

abstract class PostBuyerRequestEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class UpdateBuyerRequestTitle extends PostBuyerRequestEvent {
  final String title;
  UpdateBuyerRequestTitle(this.title);

  @override
  List<Object> get props => [title];
}

class UpdateBuyerRequestDescription extends PostBuyerRequestEvent {
  final String description;
  UpdateBuyerRequestDescription(this.description);

  @override
  List<Object> get props => [description];
}

class UpdateBuyerRequestBudget extends PostBuyerRequestEvent {
  final int budget;
  UpdateBuyerRequestBudget(this.budget);

  @override
  List<Object> get props => [budget];
}

class UpdateBuyerRequestMedia extends PostBuyerRequestEvent {
  final List<File> mediaFiles;
  UpdateBuyerRequestMedia(this.mediaFiles);

  @override
  List<Object> get props => [mediaFiles];
}

class UpdateBuyerCategory extends PostBuyerRequestEvent {
  final String category;
  UpdateBuyerCategory(this.category);

  @override
  List<Object> get props => [category];
}

class RemoveBuyerRequestMedia extends PostBuyerRequestEvent {
  final int index;
  RemoveBuyerRequestMedia(this.index);

  @override
  List<Object> get props => [index];
}

class SubmitBuyerRequest extends PostBuyerRequestEvent {
  final Map<String, dynamic> data;
  final List<File> mediaFiles;

  SubmitBuyerRequest({
    required this.data,
    required this.mediaFiles,
  });

  @override
  List<Object> get props => [data, mediaFiles];
}

class GetCategoriesBuyerRequest extends PostBuyerRequestEvent {
  GetCategoriesBuyerRequest();
}

class SubmitUserRequestWithFiles extends PostBuyerRequestEvent {
  final FormData formData;

  SubmitUserRequestWithFiles({required this.formData});

  @override
  List<Object> get props => [formData];
}
