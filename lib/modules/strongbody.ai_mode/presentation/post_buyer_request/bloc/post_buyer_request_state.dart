import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/category.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/buyer_requests.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/user_request/user_request_model.dart';

class PostBuyerRequestState extends Equatable {
  final ApiResponse<BuyerRequest>? createBuyerRequest;
  final ApiResponse<UserRequestResponse>? createUserRequest;
  final ApiResponse<CategoryList>? categories;
  final String category;

  final List<File> mediaFiles;
  final String title;
  final String description;

  const PostBuyerRequestState({
    this.createBuyerRequest,
    this.createUserRequest,
    this.categories,
    this.mediaFiles = const [],
    this.title = '',
    this.description = '',
    this.category = '',
  });

  PostBuyerRequestState copyWith({
    ApiResponse<BuyerRequest>? createBuyerRequest,
    ApiResponse<UserRequestResponse>? createUserRequest,
    List<File>? mediaFiles,
    String? title,
    String? description,
    ApiResponse<CategoryList>? categories,
    String? category,
  }) {
    return PostBuyerRequestState(
      createBuyerRequest: createBuyerRequest ?? this.createBuyerRequest,
      createUserRequest: createUserRequest ?? this.createUserRequest,
      mediaFiles: mediaFiles ?? this.mediaFiles,
      title: title ?? this.title,
      description: description ?? this.description,
      categories: categories ?? this.categories,
      category: category ?? this.category,
    );
  }

  @override
  List<Object?> get props => [
        createBuyerRequest,
        createUserRequest,
        mediaFiles,
        title,
        description,
        categories,
        category,
      ];
}
