import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_title.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm.dart';
import 'package:multime_app/shared/widgets/sames/custom_dropdown_button.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/post_buyer_request/widgets/upload_file_buyer_request.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/post_buyer_request/widgets/agreement_checkboxes.dart';
import 'package:dio/dio.dart';
import '../../../../app/routers/routers_name.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/domain/global_dependencies.dart';
import '../../../auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'bloc/post_buyer_request_bloc.dart';
import 'bloc/post_buyer_request_event.dart';
import 'bloc/post_buyer_request_state.dart';
import 'widgets/app_bar_request.dart';

class PostRequestPage extends StatefulWidget {
  const PostRequestPage({super.key});

  @override
  State<PostRequestPage> createState() => _PostRequestPageState();
}

class _PostRequestPageState extends State<PostRequestPage> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _budgetController = TextEditingController();

  bool _agreeToShareBusinessCard = false;
  bool _agreeToPostingRules = false;

  bool _isSubmitting = false;
  DateTime? _lastSubmitTime;
  static const _submitCooldown = Duration(seconds: 2); // Cooldown 2 giây

  // Save bloc reference for safe disposal
  late PostBuyerRequestBloc _bloc;

  @override
  void initState() {
    super.initState();
    // Save bloc reference for safe disposal
    _bloc = context.read<PostBuyerRequestBloc>();

    // Check if categories are not already loaded before making API call
    if (_bloc.state.categories == null ||
        _bloc.state.categories?.status == Status.error) {
      print('Loading categories from initState...');
      _bloc.add(GetCategoriesBuyerRequest());
    } else {
      print('Categories already available, skipping API call');
    }
  }

  void _submitRequest() async {
    final now = DateTime.now();
    final currentState = context.read<PostBuyerRequestBloc>().state;

// Coldown test -If you just submit within 2 seconds, do not give submit
    if (_lastSubmitTime != null &&
        now.difference(_lastSubmitTime!) < _submitCooldown) {
      print('Submit blocked: Still in cooldown period');
      return;
    }

// Check if you are submitting or loading, do not give submit
    if (_isSubmitting ||
        currentState.createUserRequest?.status == Status.loading) {
      print('Submit blocked: Already submitting or loading');
      return;
    }

    // Basic validation
    if (_descriptionController.text.trim().isEmpty) {
//can show snackbar or toast message
      print('Submit blocked: Description is empty');
      return;
    }

// Set timestamp and flag to avoid duplicate submit
    _lastSubmitTime = now;
    setState(() {
      _isSubmitting = true;
    });

    print('Submit started at: $now');

    try {
// Create formData to send files
      final formData = FormData.fromMap({
        'title': _titleController.text,
        'description': _descriptionController.text,
        'status': 'pending',
        'category_id': int.tryParse(currentState.category) ?? 1,
        'user_id': gs.uid ?? 1,
        'notes': 'new',
      });

      // add files  FormData
      for (int i = 0; i < currentState.mediaFiles.length; i++) {
        final file = currentState.mediaFiles[i];
        formData.files.add(MapEntry(
          'attachments',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
          ),
        ));
      }

      context.read<PostBuyerRequestBloc>().add(
            SubmitUserRequestWithFiles(formData: formData),
          );
    } catch (e) {
      print('Submit error: $e');
      // Reset flag if error
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PostBuyerRequestBloc, PostBuyerRequestState>(
      listener: (context, state) {
        // Handle user request
        if (state.createUserRequest?.status == Status.loading) {
          print('Bloc state: Loading');
        }
        if (state.createUserRequest?.status == Status.error) {
          print('Bloc state: Error - resetting flags');
          // Reset submitting flag if error
          setState(() {
            _isSubmitting = false;
          });
          context.push(RouteName.postBuyerRequestCheck, extra: false);
        }

        if (state.createUserRequest?.status == Status.completed) {
          print('Bloc state: Completed - resetting flags');
          // Reset submitting flag if completed
          setState(() {
            _isSubmitting = false;
          });
          context.push(RouteName.postBuyerRequestCheck, extra: true);
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: AppBarPostRequest(),
          body: SingleChildScrollView(
            physics: ClampingScrollPhysics(),
            child: Container(
              width: double.infinity,
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height +
                    100.h, // Ensure enough height for content
              ),
              child: Stack(
                children: [
                  // Header gradient container (background)
                  Container(
                    width: double.infinity,
                    height: MediaQuery.of(context).size.height * 0.28,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.black,
                          Color(0XFF000040),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Text(
                              "Get quotes for your custom request",
                              style: Theme.of(context)
                                  .textTheme
                                  .labelLarge!
                                  .copyWith(color: Colors.white),
                            ),
                            Text(
                              "Accurate supplier matching, fast price comparison",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(color: Colors.grey[300]),
                            ),
                            Row(
                              children: [
                                SvgPicture.asset(
                                  AppAssets.play_circle,
                                  width: 25.w,
                                  height: 25.h,
                                  fit: BoxFit.contain,
                                  colorFilter: ColorFilter.mode(
                                    Colors.white,
                                    BlendMode.srcIn,
                                  ),
                                ),
                                Gap(10.w),
                                Text(
                                  "Learn about RFO",
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelLarge!
                                      .copyWith(
                                        color: Colors.white,
                                        decoration: TextDecoration.underline,
                                      ),
                                ),
                              ],
                            ),
                            Gap(1),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Content area with white background - positioned to overlap
                  Positioned(
                    top: MediaQuery.of(context).size.height * 0.22,
                    left: 0,
                    right: 0,
                    child: Container(
                      width: double.infinity,
                      constraints: BoxConstraints(
                        minHeight: MediaQuery.of(context).size.height * 0.9,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(24.r),
                          topRight: Radius.circular(24.r),
                        ),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Gap(30.h), // Top padding for content
                          StrongBodyTextField(
                            labalText: LocaleKeys.description.tr(),
                            hintText: LocaleKeys.DescriptionOfRequest.tr(),
                            isLarge: true,
                            controller: _descriptionController,
                            onChanged: (value) {
                              context.read<PostBuyerRequestBloc>().add(
                                    UpdateBuyerRequestDescription(value),
                                  );
                            },
                            maxLines: 5,
                          ),
                          Gap(18.h),
                          // Category dropdown with loading state handling
                          state.categories?.status == Status.loading
                              ? Container(
                                  height: 50.h,
                                  child: CustomDropDownButton(
                                    arr: [],
                                    hintText: LocaleKeys.CategoryServices.tr(),
                                  ))
                              : state.categories?.status == Status.error
                                  ? Container(
                                      height: 50.h,
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.red),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Center(
                                        child: Text(
                                          'Error loading categories: ${state.categories?.message ?? 'Unknown error'}',
                                          style: TextStyle(color: Colors.red),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    )
                                  : CustomDropDownButton(
                                      arr: state.categories?.data?.items
                                              ?.where((category) =>
                                                  category.name != null &&
                                                  category.name!
                                                      .trim()
                                                      .isNotEmpty)
                                              .map((category) => category.name!)
                                              .toList() ??
                                          [],
                                      hintText:
                                          LocaleKeys.CategoryServices.tr(),
                                      onChanged: (selectedCategory) {
                                        if (state.categories?.data?.items !=
                                            null) {
                                          final category = state
                                              .categories!.data!.items!
                                              .firstWhere((cat) =>
                                                  cat.name == selectedCategory);
                                          context
                                              .read<PostBuyerRequestBloc>()
                                              .add(UpdateBuyerCategory(
                                                  category.id ?? ''));
                                        }
                                      },
                                    ),
                          Gap(18.h),
                          ServiceTitle(
                            title: LocaleKeys.service_image.tr(),
                            onTap: () {},
                            isHelp: false,
                          ),
                          Gap(15.h),
                          const UploadFileBuyerRequest(),
                          Gap(20.h),

                          // Agreement Checkboxes
                          AgreementCheckboxes(
                            agreeToShareBusinessCard: _agreeToShareBusinessCard,
                            agreeToPostingRules: _agreeToPostingRules,
                            onBusinessCardChanged: (value) {
                              setState(() {
                                _agreeToShareBusinessCard = value;
                              });
                            },
                            onPostingRulesChanged: (value) {
                              setState(() {
                                _agreeToPostingRules = value;
                              });
                            },
                          ),
                          Gap(40.h),
                          ButtonSB(
                            onTap: (_isSubmitting ||
                                    state.createUserRequest?.status ==
                                        Status.loading)
                                ? () {
                                    print(
                                        'Button clicked but blocked - already submitting');
                                  } // Empty function when submitting or loading
                                : () {
                                    print('Button clicked - initiating submit');
                                    _submitRequest();
                                  },
                            text: (_isSubmitting ||
                                    state.createUserRequest?.status ==
                                        Status.loading)
                                ? "Submitting..."
                                : "Post request",
                            color: (_isSubmitting ||
                                    state.createUserRequest?.status ==
                                        Status.loading)
                                ? Colors.grey
                                : Color(0xff000040),
                          ),
                          Gap(30.h), // Extra space for bottom padding
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    // Dispose text controllers
    _titleController.dispose();
    _descriptionController.dispose();
    _budgetController.dispose();

    // Clear any pending bloc operations or reset state if needed
    // This helps prevent memory leaks when navigating back
    // Use saved bloc reference instead of context to avoid the deactivated widget error
    _bloc.add(UpdateBuyerCategory(''));

    super.dispose();
  }
}
