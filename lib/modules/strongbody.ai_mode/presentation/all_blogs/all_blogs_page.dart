import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_blog_list.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/filter_drawer_blog.dart';
import 'package:multime_app/shared/widgets/app_loader/vertical_product_shimmer.dart';
import 'package:multime_app/shared/widgets/layout/listview_layout.dart';

class AllBlogsPage extends StatefulWidget {
  AllBlogsPage();

  @override
  State<AllBlogsPage> createState() => _AllBlogsPageState();
}

class _AllBlogsPageState extends State<AllBlogsPage> {
  final ScrollController _scrollController = ScrollController();
  final globalKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        context
            .read<HomeStrongbodyAiBloc>()
            .add(FetchBlogEvent(isLoadMore: true));
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
        builder: (context, state) {
      final blogs = state.searchBlogsResults;
      return Scaffold(
        key: globalKey,
        appBar: AppBar(
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () => context.pop(),
          ),
          title: Text(
            'All Blogs',
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          actions: [
            Visibility(
              visible: false,
              child: IconButton(
                icon: Icon(Icons.menu),
                onPressed: () => globalKey.currentState?.openEndDrawer(),
              ),
            ),
          ],
        ),
        endDrawer: FilterDrawerBlog(),
        endDrawerEnableOpenDragGesture: false,
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(children: [
                Expanded(
                  child: TextFormField(
                    onChanged: (value) {
                      context
                          .read<HomeStrongbodyAiBloc>()
                          .add(SearchBlogEvent(query: value));
                    },
                    decoration: InputDecoration(
                      labelText: 'Search',
                      suffixIcon: Icon(
                        Icons.search,
                        color: Theme.of(context).greyScale500(context),
                      ),
                      hintStyle:
                          Theme.of(context).textTheme.lightBodyLargeRegular,
                      contentPadding: EdgeInsets.symmetric(horizontal: 10),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Theme.of(context).greyScale100(context),
                        ),
                      ),
                    ),
                  ),
                ),
                Gap(10.w),
                IconButton(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.padding14,
                    vertical: AppSpacing.padding12h,
                  ),
                  style: IconButton.styleFrom(
                    side: BorderSide(
                      color: Theme.of(context).greyScale200(context),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  icon: SvgPicture.asset(AppAssets.setting5Svg,
                      color: Theme.of(context).greyScale500(context)),
                  onPressed: () {
                    globalKey.currentState?.openEndDrawer();
                  },
                ),
              ]),
            ),
            Expanded(
              child: state.isBlogLoading && blogs.isEmpty
                  ? TVerticalProductShimmer()
                  : blogs.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off,
                                size: 64,
                                color: Theme.of(context).greyScale300(context),
                              ),
                              SizedBox(height: 16),
                              Text(
                                'Blog not found',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular
                                    .copyWith(
                                      color: Theme.of(context)
                                          .greyScale500(context),
                                    ),
                              ),
                            ],
                          ),
                        )
                      : ListviewLayout(
                          controller: _scrollController,
                          scrollDirection: Axis.vertical,
                          padding: EdgeInsets.symmetric(
                            horizontal: AppSpacing.padding16,
                            vertical: AppSpacing.padding8,
                          ),
                          itemCount: state.searchQuery.isNotEmpty
                              ? blogs.length
                              : (state.hasMore
                                  ? blogs.length + 2
                                  : blogs.length),
                          itemBuilder: (context, index) {
                            if (index >= blogs.length) {
                              return TVerticalProductShimmer();
                            }
                            final blogItem = blogs[index];
                            return GestureDetector(
                              onTap: () {
                                context.push(RouteName.detailBlogPage,
                                    extra: blogItem);
                              },
                              child: CardBlogList(blog: blogItem),
                            );
                          },
                        ),
            ),
          ],
        ),
      );
    });
  }
}
