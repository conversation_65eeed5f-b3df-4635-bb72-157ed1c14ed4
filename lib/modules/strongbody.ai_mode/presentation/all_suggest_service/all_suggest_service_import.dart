import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_suggest_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/filter_drawer_service.dart';
import 'package:multime_app/shared/widgets/app_loader/vertical_product_shimmer.dart';
import 'package:multime_app/shared/widgets/layout/grid_layout.dart';

part "all_suggest_service.dart";
