part of 'all_suggest_service_import.dart';

class AllSuggestService extends StatefulWidget {
  AllSuggestService();

  @override
  State<AllSuggestService> createState() => _AllSuggestServiceState();
}

class _AllSuggestServiceState extends State<AllSuggestService> {
  final ScrollController _scrollController = ScrollController();
  final globalKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        context
            .read<HomeStrongbodyAiBloc>()
            .add(FetchSuggestServiceEvent(isLoadMore: true));
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeStrongbodyAiBloc, HomeStrongbodyAiState>(
        builder: (context, state) {
      final service = state.searchServiceResults;
      return Scaffold(
          key: globalKey,
          endDrawer: FilterDrawerService(),
          appBar: AppBar(
            leading: IconButton(
              icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
              onPressed: () => context.pop(),
            ),
            title: Text(
              'All Services',
              style: Theme.of(context).textTheme.lightHeadingMedium,
            ),
            actions: [
              Visibility(
                visible: false,
                child: IconButton(
                  icon: Icon(Icons.menu),
                  onPressed: () => globalKey.currentState?.openEndDrawer(),
                ),
              ),
            ],
          ),
          body: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(children: [
                  Expanded(
                    child: TextFormField(
                      onChanged: (value) {
                        context
                            .read<HomeStrongbodyAiBloc>()
                            .add(SearchServiceEvent(query: value));
                      },
                      decoration: InputDecoration(
                        labelText: 'Search',
                        suffixIcon: Icon(
                          Icons.search,
                          color: Theme.of(context).greyScale500(context),
                        ),
                        hintStyle:
                            Theme.of(context).textTheme.lightBodyLargeRegular,
                        contentPadding: EdgeInsets.symmetric(horizontal: 10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: Theme.of(context).greyScale100(context),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Gap(10.w),
                  IconButton(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.padding14,
                      vertical: AppSpacing.padding12h,
                    ),
                    style: IconButton.styleFrom(
                      side: BorderSide(
                        color: Theme.of(context).greyScale200(context),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    icon: SvgPicture.asset(AppAssets.setting5Svg,
                        color: Theme.of(context).greyScale500(context)),
                    onPressed: () {
                      globalKey.currentState?.openEndDrawer();
                    },
                  ),
                ]),
              ),
              Expanded(
                  child: state.isServiceLoading && service.isEmpty
                      ? TVerticalProductShimmer()
                      : state.isServiceSuccess && service.isNotEmpty
                          ? TGridLayout(
                              controller: _scrollController,
                              itemCount: state.searchQuery.isNotEmpty
                                  ? service.length
                                  : (state.hasMore
                                      ? service.length + 2
                                      : service.length),
                              itemBuilder: (context, index) {
                                if (state.searchQuery.isEmpty &&
                                    index >= service.length) {
                                  return TVerticalProductShimmer();
                                }
                                if (index >= service.length) {
                                  return SizedBox.shrink();
                                }
                                final serviceItem = service[index];
                                return GestureDetector(
                                  onTap: () {
                                    context.push(RouteName.serviceDetail,
                                        extra: serviceItem);
                                  },
                                  child:
                                      CardSuggestService(service: serviceItem),
                                );
                              },
                            )
                          : Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.search_off,
                                    size: 64,
                                    color:
                                        Theme.of(context).greyScale300(context),
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    'Service not found',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodyLargeRegular
                                        .copyWith(
                                          color: Theme.of(context)
                                              .greyScale500(context),
                                        ),
                                  ),
                                ],
                              ),
                            ))
            ],
          ));
    });
  }
}
