import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/search/search_home_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/response/home_strongbody_ai_response.dart';

class HomeStrongbodyAiServices {
  final ApiClient apiClient;
  final Map<String, ApiResponse<HomeStrongbodyAiResponse>> _cache = {};
  final Map<String, DateTime> _cacheTimestamp = {};
  final Duration _cacheTimeout = Duration(minutes: 5);

  HomeStrongbodyAiServices({required this.apiClient});

  // Hàm gọi API tổng quát, truyền đủ mọi param
  Future<ApiResponse<T>> fetchApi<T>({
    required String path,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    ApiType method = ApiType.get,
    required T Function(Map<String, dynamic> json) fromJson,
    String? cacheKey,
  }) async {
    try {
      final String key = cacheKey ?? '$path|${queryParameters.toString()}';
      if (_isCacheValid(key)) {
        return _cache[key]! as ApiResponse<T>;
      }
      final response = await apiClient.request(
        path: path,
        method: method,
        queryParameters: queryParameters,
        headers: headers ?? {'x-api-key': 'your_api_key'},
      );
      if (response['code'] != 200 &&
          response['code'] != 201 &&
          response['code'] != 0) {
        return ApiResponse.error(response['message']);
      }
      final result = fromJson(response);
      final apiResponse = ApiResponse.completed(result);
      _cache[key] = apiResponse as ApiResponse<HomeStrongbodyAiResponse>;
      _cacheTimestamp[key] = DateTime.now();
      return apiResponse;
    } catch (e) {
      print('❌ Error in fetchApi: $e');
      return ApiResponse.error(e.toString());
    }
  }

  // Ví dụ dùng cho HomeStrongbodyAiResponse
  Future<ApiResponse<HomeStrongbodyAiResponse>> getHomeStrongbodyAI({
    required String typeService,
    String resourceType = 'service',
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) {
    final path = _buildApiPath(typeService, resourceType);
    return fetchApi<HomeStrongbodyAiResponse>(
      path: path,
      queryParameters: queryParameters,
      headers: headers,
      fromJson: (json) => HomeStrongbodyAiResponse.fromJson(json),
      cacheKey: '$typeService|$path|${queryParameters.toString()}',
    );
  }

  // Ví dụ cho searchHomeApi
  Future<SearchHomeModel> searchHomeApi(Map<String, dynamic> query) async {
    final response = await apiClient.request(
      path: ApiConst.searchHome,
      method: ApiType.get,
      queryParameters: query,
      headers: {'Scope': 'multi-me', 'x-api-key': 'your_api_key'},
    );
    return SearchHomeModel.fromJson(response);
  }

  String _buildApiPath(String typeService, String resourceType) {
    switch (typeService) {
      case 'service':
      case 'product':
      case 'expert':
      case 'post':
        return '${ApiConst.getHomeService}/$typeService';

      case 'trending':
      case 'like':
        return '${ApiConst.getHomeService}/$resourceType';
      default:
        return ApiConst.getHomeService;
    }
  }

  bool _isCacheValid(String cacheKey) {
    if (!_cache.containsKey(cacheKey) ||
        !_cacheTimestamp.containsKey(cacheKey)) {
      return false;
    }
    final timestamp = _cacheTimestamp[cacheKey]!;
    return DateTime.now().difference(timestamp) < _cacheTimeout;
  }

  void clearCache() {
    _cache.clear();
    _cacheTimestamp.clear();
  }

  void clearCacheForType(String typeService, String resourceType) {
    final String path = _buildApiPath(typeService, resourceType);
    final String cacheKey = '$typeService|$path';
    _cache.remove(cacheKey);
    _cacheTimestamp.remove(cacheKey);
  }
}
