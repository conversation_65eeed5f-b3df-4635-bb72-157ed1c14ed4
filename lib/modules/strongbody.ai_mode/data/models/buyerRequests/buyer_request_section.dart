import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/buyer_request_item.dart';

class BuyerRequestsSection {
  final String? sectionId;
  final String? title;
  final String? viewAllLink;
  final List<BuyerRequestItem>? items;

  BuyerRequestsSection({
    this.sectionId,
    this.title,
    this.viewAllLink,
    this.items,
  });

  factory BuyerRequestsSection.fromJson(Map<String, dynamic> json) {
    return BuyerRequestsSection(
      sectionId: json['section_id'] as String?,
      title: json['title'] as String?,
      viewAllLink: json['view_all_link'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => BuyerRequestItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
