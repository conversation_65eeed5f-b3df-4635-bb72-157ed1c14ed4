import 'package:multime_app/modules/strongbody.ai_mode/data/models/comments/comment_model.dart';

class BuyerRequest {
  final int? budget;
  final Buyer? buyerId;
  final DateTime? createdAt;
  final String? description;
  final String? id;
  final bool? isActive;
  final List<String>? mediaUrls;
  final String? title;
  final DateTime? updatedAt;
  final String? categoryName;
  final String? expectedDeliveryDays;
  final List<CommentModel>? comments;

  BuyerRequest(
      {this.budget,
      this.buyerId,
      this.createdAt,
      this.description,
      this.id,
      this.isActive,
      this.mediaUrls,
      this.title,
      this.categoryName,
      this.expectedDeliveryDays,
      this.updatedAt,
      this.comments});

  factory BuyerRequest.fromJson(Map<String, dynamic> json) {
    return BuyerRequest(
      budget: json['budget'] as int?,
      buyerId: json['buyer'] != null ? Buyer.fromJson(json['buyer']) : null,
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      description: json['description'] as String?,
      id: json['id'] as String?,
      isActive: json['isActive'] as bool?,
      mediaUrls: (json['mediaUrls'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      title: json['title'] as String?,
      categoryName: json['categoryName'] as String?,
      expectedDeliveryDays: json['expectedDeliveryDays'] as String?,
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      comments: (json['comments'] as List<dynamic>?)
          ?.map((e) => CommentModel.fromJson(e))
          .toList(),
    );
  }
}

class Buyer {
  final String? country;
  final String? id;
  final String? profileImage;
  final String? username;

  Buyer({
    this.country,
    this.id,
    this.profileImage,
    this.username,
  });

  factory Buyer.fromJson(Map<String, dynamic> json) {
    return Buyer(
      country: json['country'] as String?,
      id: json['id'] as String?,
      profileImage: json['profileImage'] as String?,
      username: json['username'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'id': id,
      'profileImage': profileImage,
      'username': username,
    };
  }
}
