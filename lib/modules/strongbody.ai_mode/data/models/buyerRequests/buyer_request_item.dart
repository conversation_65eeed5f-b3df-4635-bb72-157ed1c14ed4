class BuyerRequestItem {
  final String? id;
  final String? buyerId;
  final String? title;
  final String? description;
  final String? categoryName;
  final num? budget;
  final bool? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  BuyerRequestItem({
    this.id,
    this.buyerId,
    this.title,
    this.description,
    this.budget,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.categoryName,
  });

  factory BuyerRequestItem.fromJson(Map<String, dynamic> json) {
    return BuyerRequestItem(
      id: json['id'] as String?,
      buyerId: json['buyer_id'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      categoryName: json['categoryName'] as String?,
      budget: json['budget'] as int?,
      isActive: json['is_active'] as bool?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }
}
