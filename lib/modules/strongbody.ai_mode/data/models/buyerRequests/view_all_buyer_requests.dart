import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/buyer_requests.dart';

class ViewAllBuyerRequests {
  final List<BuyerRequest>? items;

  ViewAllBuyerRequests({
    this.items,
  });

  factory ViewAllBuyerRequests.fromJson(Map<String, dynamic> json) {
    return ViewAllBuyerRequests(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => BuyerRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
