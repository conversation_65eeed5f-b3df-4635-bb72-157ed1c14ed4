class ServiceOrderModel {
  final int serviceId;
  final int sellerId;
  final int customerId;
  final String title;
  final String description;
  final double price;
  final List<String> files;
  final String notes;
  final String paymentMethod;

  ServiceOrderModel({
    required this.serviceId,
    required this.sellerId,
    required this.customerId,
    required this.title,
    required this.description,
    required this.price,
    required this.files,
    required this.notes,
    required this.paymentMethod,
  });

  Map<String, dynamic> toJson() {
    return {
      'service_id': serviceId,
      'seller_id': sellerId,
      'customer_id': customerId,
      'title': title,
      'description': description,
      'price': price,
      'files': files,
      'notes': notes,
      'payment_method': paymentMethod,
    };
  }
}

class CreateCheckOutRes {
  final int offerId;
  final String orderNo;
  final String status;
  final String paymentStatus;

  CreateCheckOutRes({
    required this.offerId,
    required this.orderNo,
    required this.status,
    required this.paymentStatus,
  });

  factory CreateCheckOutRes.fromJson(Map<String, dynamic> json) {
    return CreateCheckOutRes(
      offerId: json['offer_id'],
      orderNo: json['order_no'],
      status: json['status'],
      paymentStatus: json['payment_status'],
    );
  }
}
