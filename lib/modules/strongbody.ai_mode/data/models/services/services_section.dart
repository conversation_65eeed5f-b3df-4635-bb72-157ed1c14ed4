import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/services_item.dart';

class ServicesSection {
  final String? sectionId;
  final String? title;
  final String? viewAllLink;
  final List<ServiceItem>? items;

  ServicesSection({
    this.sectionId,
    this.title,
    this.viewAllLink,
    this.items,
  });

  factory ServicesSection.fromJson(Map<String, dynamic> json) {
    return ServicesSection(
      sectionId: json['section_id'] as String?,
      title: json['title'] as String?,
      viewAllLink: json['view_all_link'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ServiceItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ServiceCategory {
  final String? id;
  final String? name;
  final String? imageUrl;

  ServiceCategory({
    this.id,
    this.name,
    this.imageUrl,
  });

  factory ServiceCategory.fromJson(Map<String, dynamic> json) {
    return ServiceCategory(
      id: json['id'] as String?,
      name: json['name'] as String?,
      imageUrl: json['image_url'] as String?,
    );
  }
}

class ServicePromotion {
  final String? id;
  final String? type;
  final String? title;
  final String? content;
  final String? actionText;
  final String? actionLink;

  ServicePromotion({
    this.id,
    this.type,
    this.title,
    this.content,
    this.actionText,
    this.actionLink,
  });

  factory ServicePromotion.fromJson(Map<String, dynamic> json) {
    return ServicePromotion(
      id: json['id'] as String?,
      type: json['type'] as String?,
      title: json['title'] as String?,
      content: json['content'] as String?,
      actionText: json['action_text'] as String?,
      actionLink: json['action_link'] as String?,
    );
  }
}
