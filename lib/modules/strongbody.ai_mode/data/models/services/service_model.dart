import 'dart:math';

import 'package:multime_app/core/model/shop/shop_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/category.dart';
import 'package:multime_app/shared/models/user/user_model.dart';

class ServiceModel {
  final int id;
  final String title;
  final String description;
  final int categoryId;
  final Category? category;
  final int userId;
  final UserModel? user;
  final String address;
  final int? views;
  final double rating;
  final List<String> image;
  final int price;
  final String type;
  final String serviceDeliveryMethod;
  final int shopId;
  final ShopModel? shop;
  final String status;
  final int saled;
  final DateTime createdAt;

  // Optional fields that might not be present in all responses
  final String? rejectNotes;
  final String? longitude;
  final String? latitude;
  final List<String>? coverImage;
  final String? deliverables;
  final int? deliveryLeadTimeDay;
  final DateTime? goLiveDate;
  final String? slug;

  ServiceModel({
    required this.id,
    required this.title,
    required this.description,
    required this.categoryId,
    this.category,
    required this.userId,
    this.user,
    required this.address,
    this.views,
    required this.rating,
    required this.image,
    required this.price,
    required this.type,
    required this.serviceDeliveryMethod,
    required this.shopId,
    this.shop,
    required this.status,
    required this.saled,
    required this.createdAt,
    this.rejectNotes,
    this.longitude,
    this.latitude,
    this.coverImage,
    this.deliverables,
    this.deliveryLeadTimeDay,
    this.goLiveDate,
    this.slug,
  });

  factory ServiceModel.fromJson(Map<String, dynamic> json) {
    try {
      // Parse basic fields
      final id = json['id'] ?? 0;
      final title = json['title'] ?? '';
      final description = json['description'] ?? '';
      final categoryId = json['category_id'] ?? 0;

      // Parse category
      Category? category;
      try {
        if (json['category'] != null) {
          category = Category.fromJson(json['category']);
        }
      } catch (e) {
        print("🔥 Category parse error: $e");
        category = null;
      }

      // Parse user
      UserModel? user;
      try {
        if (json['user'] != null) {
          user = UserModel.fromJson(json['user']);
        }
      } catch (e) {
        print("🔥 User parse error: $e");
        user = null;
      }

      // Parse shop
      ShopModel? shop;
      try {
        if (json['shop'] != null) {
          shop = ShopModel.fromJson(json['shop']);
        }
      } catch (e) {
        shop = null;
      }

      // Parse image array
      final imageList = (json['image'] as List?)
              ?.map((e) => e as String)
              .where((url) => url.isNotEmpty && Uri.tryParse(url) != null)
              .toList() ??
          [];

      return ServiceModel(
        id: id,
        title: title,
        description: description,
        categoryId: categoryId,
        category: category,
        userId: json['user_id'] ?? 0,
        user: user,
        address: json['address'] ?? '',
        views: json['views'] ?? Random().nextInt(1000) + 50,
        rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
        image: imageList,
        price: (json['price'] as num?)?.toInt() ?? 0,
        type: json['type'] ?? '',
        serviceDeliveryMethod: json['service_delivery_method'] ?? '',
        shopId: json['shop_id'] ?? 0,
        shop: shop,
        status: json['status'] ?? '',
        saled: json['saled'] ?? 0,
        createdAt: DateTime.parse(
            json['created_at'] ?? DateTime.now().toIso8601String()),
        // Optional fields
        rejectNotes: json['reject_notes'],
        longitude: json['longitude'],
        latitude: json['latitude'],
        coverImage: (json['cover_image'] as List?)
            ?.map((e) => e as String)
            .where((url) => url.isNotEmpty && Uri.tryParse(url) != null)
            .toList(),
        deliverables: json['deliverables'],
        deliveryLeadTimeDay: json['delivery_lead_time_day'],
        goLiveDate: json['go_live_date'] != null
            ? DateTime.parse(json['go_live_date'])
            : null,
        slug: json['slug'],
      );
    } catch (e) {
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category_id': categoryId,
      'category': category?.toJson(),
      'user_id': userId,
      'user': user?.toJson(),
      'address': address,
      'views': views,
      'rating': rating,
      'image': image,
      'price': price,
      'type': type,
      'service_delivery_method': serviceDeliveryMethod,
      'shop_id': shopId,
      'shop': shop?.toJson(),
      'status': status,
      'saled': saled,
      'created_at': createdAt.toIso8601String(),
      // Optional fields
      if (rejectNotes != null) 'reject_notes': rejectNotes,
      if (longitude != null) 'longitude': longitude,
      if (latitude != null) 'latitude': latitude,
      if (coverImage != null) 'cover_image': coverImage,
      if (deliverables != null) 'deliverables': deliverables,
      if (deliveryLeadTimeDay != null)
        'delivery_lead_time_day': deliveryLeadTimeDay,
      if (goLiveDate != null) 'go_live_date': goLiveDate!.toIso8601String(),
      if (slug != null) 'slug': slug,
    };
  }

  // Helper method to strip HTML tags from description
  String get cleanDescription {
    return description
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll('&nbsp;', ' ') // Replace &nbsp; with space
        .replaceAll('&amp;', '&') // Replace &amp; with &
        .replaceAll('&lt;', '<') // Replace &lt; with <
        .replaceAll('&gt;', '>') // Replace &gt; with >
        .replaceAll('&quot;', '"') // Replace &quot; with "
        .trim(); // Remove leading/trailing whitespace
  }

  static ServiceModel get empty => ServiceModel(
        id: 0,
        title: '',
        description: '',
        categoryId: 0,
        category: null,
        userId: 0,
        user: null,
        address: '',
        views: 0,
        rating: 0.0,
        image: [],
        price: 0,
        type: '',
        serviceDeliveryMethod: '',
        shopId: 0,
        shop: null,
        status: '',
        saled: 0,
        createdAt: DateTime.now(),
        rejectNotes: null,
        longitude: null,
        latitude: null,
        coverImage: null,
        deliverables: null,
        deliveryLeadTimeDay: null,
        goLiveDate: null,
        slug: null,
      );
}
