class ServiceItem {
  final String? id;
  final String? title;
  final String? sellerId;
  final String? providerName;
  final String? providerPhoto;
  final String? imageUrl;
  final String? description;
  final num? price;
  final String? priceUnit;
  final double? locationLatitude;
  final double? locationLongitude;
  final String? locationAddress;
  final String? locationCity;
  final String? locationState;
  final String? locationCountry;
  final String? locationPostalCode;
  final String? locationType;
  final String? contactAction;
  final String? categoryId;

  ServiceItem({
    this.id,
    this.title,
    this.sellerId,
    this.providerName,
    this.providerPhoto,
    this.imageUrl,
    this.description,
    this.price,
    this.priceUnit,
    this.locationLatitude,
    this.locationLongitude,
    this.locationAddress,
    this.locationCity,
    this.locationState,
    this.locationCountry,
    this.locationPostalCode,
    this.locationType,
    this.contactAction,
    this.categoryId,
  });

  const ServiceItem.empty()
      : id = null,
        title = null,
        sellerId = null,
        providerName = null,
        providerPhoto = null,
        imageUrl = null,
        description = null,
        price = null,
        priceUnit = null,
        locationLatitude = null,
        locationLongitude = null,
        locationAddress = null,
        locationCity = null,
        locationState = null,
        locationCountry = null,
        locationPostalCode = null,
        locationType = null,
        contactAction = null,
        categoryId = null;

  factory ServiceItem.fromJson(Map<String, dynamic> json) {
    final location = json['location'] as Map<String, dynamic>?;

    return ServiceItem(
      id: json['id'] as String?,
      title: json['title'] as String?,
      sellerId: json['seller_id'] as String?,
      providerName: json['provider_name'] as String?,
      providerPhoto: json['provider_photo'] as String?,
      imageUrl: json['image_url'] as String?,
      description: json['description'] as String?,
      price: json['price'],
      priceUnit: json['price_unit'] as String?,
      locationLatitude: (location?['latitude'] as num?)?.toDouble(),
      locationLongitude: (location?['longitude'] as num?)?.toDouble(),
      locationAddress: location?['address'] as String?,
      locationCity: location?['city'] as String?,
      locationState: location?['state'] as String?,
      locationCountry: location?['country'] as String?,
      locationPostalCode: location?['postal_code'] as String?,
      locationType: json['location_type'] as String?,
      contactAction: json['contact_action'] as String?,
      categoryId: json['category_id'] as String?,
    );
  }
}
