import 'package:multime_app/modules/strongbody.ai_mode/data/models/search/search_item.dart';

class SearchHomeModel {
  final int total;
  final List<SearchHomeItem> items;

  SearchHomeModel({
    required this.total,
    required this.items,
  });

  factory SearchHomeModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>? ?? {};
    final itemsList = (data['data'] as List?) ?? [];
    return SearchHomeModel(
      total: data['total'] as int? ?? 0,
      items: itemsList
          .map((e) => SearchHomeItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'items': items
          .where((e) => e != null)
          .map((e) => e.toJson())
          .toList(),
    };
  }
}