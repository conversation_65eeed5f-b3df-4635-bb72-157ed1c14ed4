import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/services_item.dart';

class SearchServiceModel {
  final int? total;
  final int? page;
  final int? pageSize;
  final List<ServiceItem>? services;

  SearchServiceModel({
    this.total,
    this.page,
    this.pageSize,
    this.services,
  });

  factory SearchServiceModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return SearchServiceModel();
    return SearchServiceModel(
      total: json['total'] as int?,
      page: json['page'] as int?,
      pageSize: json['page_size'] as int?,
      services: (json['services'] as List<dynamic>?)?.map((e) => ServiceItem.fromJson(e as Map<String, dynamic>)).toList(),
    );
  }
}

// class ProductSearchModel {
//   final String? id;
//   final String? name;
//   final String? description;
//   final double? price;
//   final String? category;

//   ProductSearchModel({
//     this.id,
//     this.name,
//     this.description,
//     this.price,
//     this.category,
//   });

//   factory ProductSearchModel.fromJson(Map<String, dynamic>? json) {
//     if (json == null) return ProductSearchModel();
//     return ProductSearchModel(
//       id: json['id'] as String?,
//       name: json['name'] as String?,
//       description: json['description'] as String?,
//       price: (json['price'] as num?)?.toDouble(),
//       category: json['category'] as String?,
//     );
//   }
// }
