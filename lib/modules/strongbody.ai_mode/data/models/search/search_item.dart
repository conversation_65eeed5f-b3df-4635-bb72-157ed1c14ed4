class SearchHomeItem {
  final int id;
  final String name;
  final String type;
  final String? slug;
  final String images;

  SearchHomeItem({
    required this.id,
    required this.name,
    required this.type,
    this.slug,
    required this.images,
  });

  factory SearchHomeItem.fromJson(Map<String, dynamic> json) {
    return SearchHomeItem(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      type: json['type'] as String? ?? '',
      slug: json['slug'] as String?,
      images: json['images'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'slug': slug,
      'images': images,
    };
  }
}