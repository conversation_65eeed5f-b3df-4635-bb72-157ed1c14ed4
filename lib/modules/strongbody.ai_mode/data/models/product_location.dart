import 'package:json_annotation/json_annotation.dart';

part 'product_location.g.dart';

@JsonSerializable(explicitToJson: true)
class ProductLocation {
  final String id;
  final String productId;
  final double locationLat;
  final double locationLong;
  final String address;
  final String city;
  final String state;
  final String country;
  final String postalCode;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductLocation({
    required this.id,
    required this.productId,
    required this.locationLat,
    required this.locationLong,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON serialization
  factory ProductLocation.fromJson(Map<String, dynamic> json) =>
      _$ProductLocationFromJson(json);

  Map<String, dynamic> toJson() => _$ProductLocationToJson(this);
}
