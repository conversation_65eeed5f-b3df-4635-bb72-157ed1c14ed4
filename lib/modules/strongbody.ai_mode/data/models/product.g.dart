// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      sellerId: json['sellerId'] as String,
      categoryId: json['categoryId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      condition: json['condition'] as String,
      quantity: (json['quantity'] as num).toInt(),
      weight: (json['weight'] as num).toDouble(),
      weightUnit: json['weightUnit'] as String,
      height: (json['height'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      length: (json['length'] as num).toDouble(),
      dimensionUnit: json['dimensionUnit'] as String,
      moderationStatus:
          $enumDecode(_$ModerationStatusEnumMap, json['moderationStatus']),
      moderationResult: json['moderationResult'] as String,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'sellerId': instance.sellerId,
      'categoryId': instance.categoryId,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'condition': instance.condition,
      'quantity': instance.quantity,
      'weight': instance.weight,
      'weightUnit': instance.weightUnit,
      'height': instance.height,
      'width': instance.width,
      'length': instance.length,
      'dimensionUnit': instance.dimensionUnit,
      'moderationStatus': _$ModerationStatusEnumMap[instance.moderationStatus]!,
      'moderationResult': instance.moderationResult,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$ModerationStatusEnumMap = {
  ModerationStatus.pending: 'pending',
  ModerationStatus.approved: 'approved',
  ModerationStatus.rejected: 'rejected',
};
