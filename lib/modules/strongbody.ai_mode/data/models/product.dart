import 'package:json_annotation/json_annotation.dart';

part 'product.g.dart';

enum ModerationStatus { pending, approved, rejected }

@JsonSerializable(explicitToJson: true)
class Product {
  final String id;
  final String sellerId;
  final String categoryId;
  final String title;
  final String description;
  final double price;
  final String condition;
  final int quantity;
  final double weight;
  final String weightUnit;
  final double height;
  final double width;
  final double length;
  final String dimensionUnit;
  final ModerationStatus moderationStatus;
  final String moderationResult;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Product({
    required this.id,
    required this.sellerId,
    required this.categoryId,
    required this.title,
    required this.description,
    required this.price,
    required this.condition,
    required this.quantity,
    required this.weight,
    required this.weightUnit,
    required this.height,
    required this.width,
    required this.length,
    required this.dimensionUnit,
    required this.moderationStatus,
    required this.moderationResult,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON serialization
  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);

  Map<String, dynamic> toJson() => _$ProductToJson(this);
}
