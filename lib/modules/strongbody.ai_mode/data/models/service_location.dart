import 'package:json_annotation/json_annotation.dart';

part 'service_location.g.dart';

@JsonSerializable(explicitToJson: true)
class ServiceLocation {
  final String id;
  final String serviceId;
  final String locationType;
  final double locationLat;
  final double locationLong;
  final String address;
  final String city;
  final String state;
  final String country;
  final String postalCode;
  final DateTime createdAt;
  final DateTime updatedAt;

  ServiceLocation({
    required this.id,
    required this.serviceId,
    required this.locationType,
    required this.locationLat,
    required this.locationLong,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON serialization
  factory ServiceLocation.fromJson(Map<String, dynamic> json) =>
      _$ServiceLocationFromJson(json);

  // Map<String, dynamic> toJson() => _$ProductLocationToJson(this);
}