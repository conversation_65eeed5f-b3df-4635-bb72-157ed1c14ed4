class Category {
  final int id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final dynamic deletedAt;
  final dynamic createdBy;
  final dynamic updatedBy;
  final String name;
  final String description;
  final String slug;
  final bool isActive;
  final String type;
  final List<String> coverImage;
  final List<String> icon;
  final String path;
  final int depth;
  final int parentId;
  final String externalID;

  Category({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    required this.name,
    required this.description,
    required this.slug,
    required this.isActive,
    required this.type,
    required this.coverImage,
    required this.icon,
    required this.path,
    required this.depth,
    required this.parentId,
    required this.externalID,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? 0,
      createdAt: DateTime.parse(
          json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(
          json['updated_at'] ?? DateTime.now().toIso8601String()),
      deletedAt: json['deleted_at'],
      createdBy: json['created_by'],
      updatedBy: json['updated_by'],
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      slug: json['slug'] ?? '',
      isActive: json['is_active'] ?? true,
      type: json['type'] ?? '',
      coverImage: (json['cover_image'] is List)
          ? (json['cover_image'] as List).map((e) => e.toString()).toList()
          : [],
      icon: (json['icon'] is List)
          ? (json['icon'] as List).map((e) => e.toString()).toList()
          : [],
      path: json['path'] ?? '',
      depth: json['depth'] ?? 0,
      parentId: json['parent_id'] ?? 0,
      externalID: json['ExternalID'] ?? '',
    );
  }
  Category copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    dynamic createdBy,
    dynamic updatedBy,
    String? name,
    String? description,
    String? slug,
    bool? isActive,
    String? type,
    List<String>? coverImage,
    List<String>? icon,
    String? path,
    int? depth,
    int? parentId,
    String? externalID,
  }) {
    return Category(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      name: name ?? this.name,
      description: description ?? this.description,
      slug: slug ?? this.slug,
      isActive: isActive ?? this.isActive,
      type: type ?? this.type,
      coverImage: coverImage ?? this.coverImage,
      icon: icon ?? this.icon,
      path: path ?? this.path,
      depth: depth ?? this.depth,
      parentId: parentId ?? this.parentId,
      externalID: externalID ?? this.externalID,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'deleted_at': deletedAt,
      'created_by': createdBy,
      'updated_by': updatedBy,
      'name': name,
      'description': description,
      'slug': slug,
      'is_active': isActive,
      'type': type,
      'cover_image': coverImage,
      'icon': icon,
      'path': path,
      'depth': depth,
      'parent_id': parentId,
      'ExternalID': externalID,
    };
  }
}
