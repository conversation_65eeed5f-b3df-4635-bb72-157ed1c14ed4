class CommentModel {
  final String? buyerRequestId;
  final String? content;
  final DateTime? createdAt;
  final String? id;
  final SellerComment? seller;
  final String? sellerId;
  final DateTime? updatedAt;

  CommentModel({
    this.buyerRequestId,
    this.content,
    this.createdAt,
    this.id,
    this.seller,
    this.sellerId,
    this.updatedAt,
  });

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      buyerRequestId: json['buyerRequestId'] as String?,
      content: json['content'] as String?,
      createdAt: json['createdAt'] != null ? DateTime.tryParse(json['createdAt']) : null,
      id: json['id'] as String?,
      seller: json['seller'] != null ? SellerComment.fromJson(json['seller']) : null,
      sellerId: json['sellerId'] as String?,
      updatedAt: json['updatedAt'] != null ? DateTime.tryParse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'buyerRequestId': buyerRequestId,
      'content': content,
      'createdAt': createdAt?.toIso8601String(),
      'id': id,
      'seller': seller?.toJson(),
      'sellerId': sellerId,
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}

class SellerComment {
  final String? country;
  final String? id;
  final String? profileImage;
  final String? username;

  SellerComment({
    this.country,
    this.id,
    this.profileImage,
    this.username,
  });

  factory SellerComment.fromJson(Map<String, dynamic> json) {
    return SellerComment(
      country: json['country'] as String?,
      id: json['id'] as String?,
      profileImage: json['profileImage'] as String?,
      username: json['username'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'id': id,
      'profileImage': profileImage,
      'username': username,
    };
  }
}
