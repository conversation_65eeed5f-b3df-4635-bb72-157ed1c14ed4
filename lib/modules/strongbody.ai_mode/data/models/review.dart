import 'package:json_annotation/json_annotation.dart';

part 'review.g.dart';

@JsonSerializable(explicitToJson: true)
class Review {
  final String id;
  final String reviewerId;
  final String? revieweeId;
  final String referenceId;
  final String referenceType;
  final int rating;
  final String comment;
  final String? reply;
  final DateTime? replyAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  Review({
    required this.id,
    required this.reviewerId,
    this.revieweeId,
    required this.referenceId,
    required this.referenceType,
    required this.rating,
    required this.comment,
    this.reply,
    this.replyAt,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON serialization
  factory Review.fromJson(Map<String, dynamic> json) => _$ReviewFromJson(json);

  Map<String, dynamic> toJson() => _$ReviewToJson(this);
}
