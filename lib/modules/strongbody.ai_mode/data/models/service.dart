import 'package:json_annotation/json_annotation.dart';

part 'service.g.dart';

enum ModerationStatus { pending, approved, rejected }

@JsonSerializable(explicitToJson: true)
class Service {
  final String id;
  final String sellerId;
  final String categoryId;
  final String title;
  final String description;
  final double price;
  final int durationMinutes;
  final bool isAvailable;
  final String locationType;
  final ModerationStatus moderationStatus;
  final String moderationResult;
  final DateTime createdAt;
  final DateTime updatedAt;

  Service({
    required this.id,
    required this.sellerId,
    required this.categoryId,
    required this.title,
    required this.description,
    required this.price,
    required this.durationMinutes,
    required this.isAvailable,
    required this.locationType,
    required this.moderationStatus,
    required this.moderationResult,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON serialization
  factory Service.fromJson(Map<String, dynamic> json) =>
      _$ServiceFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceToJson(this);
}
