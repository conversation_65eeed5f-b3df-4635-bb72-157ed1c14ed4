// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Review _$ReviewFromJson(Map<String, dynamic> json) => Review(
      id: json['id'] as String,
      reviewerId: json['reviewerId'] as String,
      revieweeId: json['revieweeId'] as String?,
      referenceId: json['referenceId'] as String,
      referenceType: json['referenceType'] as String,
      rating: (json['rating'] as num).toInt(),
      comment: json['comment'] as String,
      reply: json['reply'] as String?,
      replyAt: json['replyAt'] == null
          ? null
          : DateTime.parse(json['replyAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ReviewToJson(Review instance) => <String, dynamic>{
      'id': instance.id,
      'reviewerId': instance.reviewerId,
      'revieweeId': instance.revieweeId,
      'referenceId': instance.referenceId,
      'referenceType': instance.referenceType,
      'rating': instance.rating,
      'comment': instance.comment,
      'reply': instance.reply,
      'replyAt': instance.replyAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
