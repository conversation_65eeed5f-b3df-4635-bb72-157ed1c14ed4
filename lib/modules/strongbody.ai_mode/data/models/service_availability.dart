import 'package:json_annotation/json_annotation.dart';

part 'service_availability.g.dart';

@JsonSerializable(explicitToJson: true)
class ServiceAvailability {
  final String id;
  final String serviceId;
  final int dayOfWeek;
  final String startTime;
  final String endTime;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime updatedAt;

  ServiceAvailability({
    required this.id,
    required this.serviceId,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.isAvailable,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON serialization
  factory ServiceAvailability.fromJson(Map<String, dynamic> json) =>
      _$ServiceAvailabilityFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceAvailabilityToJson(this);
}
