// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Service _$ServiceFromJson(Map<String, dynamic> json) => Service(
      id: json['id'] as String,
      sellerId: json['sellerId'] as String,
      categoryId: json['categoryId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      durationMinutes: (json['durationMinutes'] as num).toInt(),
      isAvailable: json['isAvailable'] as bool,
      locationType: json['locationType'] as String,
      moderationStatus:
          $enumDecode(_$ModerationStatusEnumMap, json['moderationStatus']),
      moderationResult: json['moderationResult'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ServiceToJson(Service instance) => <String, dynamic>{
      'id': instance.id,
      'sellerId': instance.sellerId,
      'categoryId': instance.categoryId,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'durationMinutes': instance.durationMinutes,
      'isAvailable': instance.isAvailable,
      'locationType': instance.locationType,
      'moderationStatus': _$ModerationStatusEnumMap[instance.moderationStatus]!,
      'moderationResult': instance.moderationResult,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$ModerationStatusEnumMap = {
  ModerationStatus.pending: 'pending',
  ModerationStatus.approved: 'approved',
  ModerationStatus.rejected: 'rejected',
};
