class TopSellersItem {
  final String? id;
  final String? name;
  final String? photo;
  final String? speciality;
  final String? categoryId;
  final String? categoryName;

  TopSellersItem({
    this.id,
    this.name,
    this.photo,
    this.categoryId,
    this.categoryName,
    this.speciality,
  });

  factory TopSellersItem.fromJson(Map<String, dynamic> json) {
    return TopSellersItem(
      id: json['id'] as String?,
      name: json['name'] as String?,
      photo: json['photo'] as String?,
      speciality: json['specialty'] as String?,
      categoryId: json['category_id'] as String?,
      categoryName: json['category_name'] as String?,
    );
  }
}
