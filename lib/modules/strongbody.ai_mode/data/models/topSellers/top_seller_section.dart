import 'package:multime_app/modules/strongbody.ai_mode/data/models/topSellers/top_sellers_model.dart';

class TopSellersSection {
  final String? sectionId;
  final String? title;
  final String? viewAllLink;
  final List<TopSellersItem>? sellers;

  TopSellersSection({
    this.sectionId,
    this.title,
    this.viewAllLink,
    this.sellers,
  });

  factory TopSellersSection.fromJson(Map<String, dynamic> json) {
    return TopSellersSection(
      sectionId: json['section_id'] as String?,
      title: json['title'] as String?,
      viewAllLink: json['view_all_link'] as String?,
      sellers: (json['sellers'] as List<dynamic>?)
          ?.map((e) => TopSellersItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
