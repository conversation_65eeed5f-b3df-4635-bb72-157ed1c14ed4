class UserRequestModel {
  final String title;
  final String description;
  final String? status;
  final int categoryId;
  final int userId;
  final int? assignedToId;
  final List<List<dynamic>>? attachments;
  final String? notes;

  UserRequestModel({
    required this.title,
    required this.description,
    this.status,
    required this.categoryId,
    required this.userId,
    this.assignedToId,
    this.attachments,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'title': title,
      'description': description,
      'category_id': categoryId,
      'user_id': userId,
    };
    
    // Chỉ thêm các field không null
    if (status != null) data['status'] = status;
    if (assignedToId != null) data['assigned_to_id'] = assignedToId;
    if (attachments != null) data['attachments'] = attachments;
    if (notes != null) data['notes'] = notes;
    
    return data;
  }
}

class UserRequestResponse {
  final int code;
  final String message;
  final UserRequestData? data;

  UserRequestResponse({
    required this.code,
    required this.message,
    this.data,
  });

  factory UserRequestResponse.fromJson(Map<String, dynamic> json) {
    return UserRequestResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: json['data'] != null ? UserRequestData.fromJson(json['data']) : null,
    );
  }
}

class UserRequestData {
  final bool success;
  final String message;
  final int id;

  UserRequestData({
    required this.success,
    required this.message,
    required this.id,
  });

  factory UserRequestData.fromJson(Map<String, dynamic> json) {
    return UserRequestData(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      id: json['id'] ?? 0,
    );
  }
}
