import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/buyer_request_section.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/services_section.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/topSellers/top_seller_section.dart';

class StrongBodyHomeModel {
  final List<ServicesSection>? sections;
  final List<ServiceCategory>? categories;
  final List<ServicePromotion>? promotions;
  final BuyerRequestsSection? buyerRequestsSection;
  final TopSellersSection? topExpertsSection;

  StrongBodyHomeModel({
    this.sections,
    this.categories,
    this.promotions,
    this.buyerRequestsSection,
    this.topExpertsSection,
  });

  factory StrongBodyHomeModel.fromJson(Map<String, dynamic> json) {
    return StrongBodyHomeModel(
      sections: (json['sections'] as List<dynamic>?)
          ?.map((e) => ServicesSection.fromJson(e as Map<String, dynamic>))
          .toList(),
      buyerRequestsSection: json['buyer_requests'] != null
          ? BuyerRequestsSection.fromJson(
              json['buyer_requests'] as Map<String, dynamic>)
          : null,
      topExpertsSection: TopSellersSection.fromJson(
          json['top_sellers'] as Map<String, dynamic>),
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => ServiceCategory.fromJson(e as Map<String, dynamic>))
          .toList(),
      promotions: (json['promotions'] as List<dynamic>?)
          ?.map((e) => ServicePromotion.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}
