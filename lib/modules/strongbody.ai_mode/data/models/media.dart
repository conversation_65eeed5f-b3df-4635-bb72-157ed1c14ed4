import 'package:json_annotation/json_annotation.dart';

part 'media.g.dart';

@JsonSerializable(explicitToJson: true)
class Media {
  final String id;
  final String referenceId;
  final String referenceType;
  final String mediaType;
  final String fileName;
  final int fileSize;
  final String mimeType;
  final String url;
  final String bucket;
  final DateTime createdAt;
  final DateTime updatedAt;

  Media({
    required this.id,
    required this.referenceId,
    required this.referenceType,
    required this.mediaType,
    required this.fileName,
    required this.fileSize,
    required this.mimeType,
    required this.url,
    required this.bucket,
    required this.createdAt,
    required this.updatedAt,
  });

  // JSON serialization
  factory Media.fromJson(Map<String, dynamic> json) => _$MediaFromJson(json);

  Map<String, dynamic> toJson() => _$MediaToJson(this);
}
