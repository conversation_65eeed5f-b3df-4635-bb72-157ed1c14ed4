class HomeStrongbodyAiResponse {
  final HomeStrongbodyAiData data;
  HomeStrongbodyAiResponse({
    required this.data,
  });
  factory HomeStrongbodyAiResponse.fromJson(Map<String, dynamic> json) {
    return HomeStrongbodyAiResponse(
      data: HomeStrongbodyAiData.fromJson(json['data'] ?? {}),
    );
  }
}

class HomeStrongbodyAiData {
  final String type;
  final List<Map<String, dynamic>> list;

  HomeStrongbodyAiData({
    required this.type,
    required this.list,
  });

  factory HomeStrongbodyAiData.fromJson(Map<String, dynamic> json) {
    return HomeStrongbodyAiData(
      type: json['type'] ?? '',
      list: List<Map<String, dynamic>>.from(json['list'] ?? []),
    );
  }
}
