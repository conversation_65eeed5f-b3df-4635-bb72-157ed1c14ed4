// import 'package:flutter/material.dart';
// import 'package:multime_app/core/base/api_response/api_response.dart';
// import 'package:multime_app/core/constants/api_endpoints.dart';
// import 'package:multime_app/core/network/api.dart';
// import 'package:multime_app/core/network/api_type.dart';
// import '../../../../core/domain/global_dependencies.dart';
// import '../models/services/sevice_order_model.dart';

// abstract class CheckoutServiceRepository {
//   Future<ApiResponse<CreateCheckOutRes>> createServiceOrder(
//       CreateCheckoutServiceEvent event);
// }

// class CheckoutServiceRepositoryRemote implements CheckoutServiceRepository {
//   final ApiClient _apiClient;

//   CheckoutServiceRepositoryRemote(this._apiClient);

//   @override
//   Future<ApiResponse<CreateCheckOutRes>> createServiceOrder(
//       CreateCheckoutServiceEvent event) async {
//     try {
//       final response = await _apiClient.request(
//         path: ApiConst.checkoutService,
//         method: ApiType.post,
//         data: ServiceOrderModel(
//           serviceId: event.serviceId,
//           sellerId: event.sellerId,
//           customerId: gs.uid ?? 1,
//           title: event.title,
//           description: event.description,
//           price: event.price,
//           files: event.files,
//           notes: event.notes,
//           paymentMethod: event.paymentMethod,
//         ).toJson(),
//       );
//       debugPrint("Checkout Service Response: $response");
//       final data = response['data'];
//       return ApiResponse.completed(
//         CreateCheckOutRes.fromJson(data),
//       );
//     } catch (e) {
//       print(" checkout service error: $e");
//       rethrow;
//     }
//   }
// }
