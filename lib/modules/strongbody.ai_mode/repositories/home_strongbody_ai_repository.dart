import 'dart:core';

import 'package:dio/dio.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/search/search_home_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/services_item.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/services_section.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/strongbodyHomeModel/strongbody_home_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/topSellers/top_seller_section.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/response/home_strongbody_ai_response.dart';
import 'package:multime_app/modules/strongbody.ai_mode/services/home_strongbody_ai_services.dart';
import '../../../../core/network/api.dart';
import '../../../../core/network/api_type.dart';
import '../presentation/detail_product/model/product_model.dart';

abstract class HomeStrongbodyAiRepository {
  Future<ApiResponse<HomeStrongbodyAiResponse>> getServiceByQuery(
    String typeService, {
    Map<String, dynamic>? param,
    String? resourceType,
  });
  Future<StrongBodyHomeModel> getHomeServices({CancelToken? cancelToken});
  Future<ServiceModel> createService(Map<String, dynamic> data);
  Future<ServiceModel> createServiceWithFormData(FormData formData);
  Future<Map<String, dynamic>> addImageApi(
      {required FormData data, required String id});
  Future<Map<String, dynamic>> addCertificateApi(
      {required FormData data, required String id});
  Future<ServiceModel> getServiceById(String id);
  Future<ServicesSection> getServicesBySection(String sectionId);
  Future<List<ServiceItem>> searchApi(Map<String, dynamic> query);
  Future<SearchHomeModel> searchHomeApi(Map<String, dynamic> query);
  Future<TopSellersSection> viewAllTopSellers();
  Future<ServicesSection> getSimilarServices(String servicesId);

  //
  Future<ServiceModel> getService(int serviceId);
  Future<ProductModel> getProduct(int productId);
}

class HomeStrongbodyAiRepositoryRemote implements HomeStrongbodyAiRepository {
  final ApiClient apiClient;
  final HomeStrongbodyAiServices homeStrongbodyAiServices;

  HomeStrongbodyAiRepositoryRemote(
      {required this.apiClient, required this.homeStrongbodyAiServices});

  @override
  Future<StrongBodyHomeModel> getHomeServices(
      {CancelToken? cancelToken}) async {
    try {
      final response = await apiClient.request(
        path: ApiConst.getHomeService,
        method: ApiType.get,
        cancelToken: cancelToken,
      );
      return StrongBodyHomeModel.fromJson(response);
    } catch (e) {
      print("getHomeServices m error: $e");
      rethrow;
    }
  }

  @override
  Future<ServiceModel> createService(Map<String, dynamic> data) async {
    try {
      final response = await apiClient.request(
          path: ApiConst.services, method: ApiType.post, data: data);
      return ServiceModel.fromJson(response);
    } catch (e) {
      print("createService m error: $e");
      rethrow;
    }
  }

  @override
  Future<ServiceModel> createServiceWithFormData(FormData formData) async {
    try {
      final response = await apiClient.request(
          path: ApiConst.adminServices,
          method: ApiType.post,
          data: formData,
          headers: {
            'Content-Type': 'admin-dashboard',
          });
      return ServiceModel.fromJson(response);
    } catch (e) {
      print("createServiceWithFormData m error: $e");
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> addImageApi(
      {required FormData? data, required String id}) async {
    try {
      final response = await apiClient.request(
          path: "${ApiConst.services}/$id/media",
          method: ApiType.post,
          data: data);
      return response;
    } catch (e) {
      print("addImageApi error: $e");
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> addCertificateApi(
      {required FormData? data, required String id}) async {
    try {
      final response = await apiClient.request(
          path: "${ApiConst.services}/$id/certificates",
          method: ApiType.post,
          data: data);
      return response;
    } catch (e) {
      print("addCertificateApi error: $e");
      rethrow;
    }
  }

  @override
  Future<ServiceModel> getServiceById(String id) async {
    try {
      final response = await apiClient.request(
          path: "${ApiConst.services}/$id", method: ApiType.get);
      return ServiceModel.fromJson(response);
    } catch (e) {
      print("getServiceById error: $e");
      rethrow;
    }
  }

  @override
  Future<ServicesSection> getServicesBySection(String sectionId) async {
    try {
      final response = await apiClient.request(
          path: "${ApiConst.getHomeService}/section/$sectionId",
          method: ApiType.get);
      return ServicesSection.fromJson(response);
    } catch (e) {
      print("getServicesBySection error: $e");
      rethrow;
    }
  }

  @override
  Future<List<ServiceItem>> searchApi(Map<String, dynamic> query) async {
    try {
      final response = await apiClient.request(
        path: ApiConst.searchService,
        method: ApiType.get,
        queryParameters: query,
      );

      final services =
          (response['services'] as List?)?.cast<Map<String, dynamic>>() ?? [];
      return services.map((e) => ServiceItem.fromJson(e)).toList();
    } catch (e) {
      print("Search API error: $e");
      rethrow;
    }
  }

  @override
  Future<TopSellersSection> viewAllTopSellers() async {
    try {
      final response = await apiClient.request(
          path: "${ApiConst.getHomeService}/section/top_sellers",
          method: ApiType.get);
      return TopSellersSection.fromJson(response);
    } catch (e) {
      print("viewAllTopSellers error: $e");
      rethrow;
    }
  }

  @override
  Future<ServicesSection> getSimilarServices(String servicesId) async {
    try {
      final response = await apiClient.request(
          path: "${ApiConst.similarServices}$servicesId/similar",
          method: ApiType.get);
      return ServicesSection.fromJson(response);
    } catch (e) {
      print("GET Similar Services Error: $e");
      rethrow;
    }
  }

  @override
  Future<ApiResponse<HomeStrongbodyAiResponse>> getServiceByQuery(
      String typeService,
      {Map<String, dynamic>? param,
      String? resourceType}) async {
    return await homeStrongbodyAiServices.getHomeStrongbodyAI(
        typeService: typeService,
        resourceType: resourceType ?? 'service',
        queryParameters: param);
  }

  @override
  Future<SearchHomeModel> searchHomeApi(
    Map<String, dynamic> query,
  ) async {
    final response = await apiClient.request(
      path: ApiConst.searchHome,
      method: ApiType.get,
      queryParameters: query,
      headers: {'Scope': 'multi-me', 'x-api-key': 'your_api_key'},
    );
    return SearchHomeModel.fromJson(response);
  }

  @override
  Future<ProductModel> getProduct(int productId) async {
    try {
      final response = await apiClient.request(
        path: "${ApiConst.getProduct}$productId",
        method: ApiType.get,
      );
      final data = response['data'];

      return ProductModel.fromJson(data);
    } catch (e) {
      print("getProduct error: $e");
      rethrow;
    }
  }

  @override
  Future<ServiceModel> getService(int serviceId) async {
    try {
      final response = await apiClient.request(
        method: ApiType.get,
        path: "${ApiConst.getService}$serviceId",
      );
      final data = response['data'];
      return ServiceModel.fromJson(data);
    } catch (e) {
      print("getService error: $e");
      rethrow;
    }
  }
}
