import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/buyer_requests.dart';
import 'package:dio/dio.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/buyerRequests/view_all_buyer_requests.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/comments/comment_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/user_request/user_request_model.dart';
import '../../../../core/network/api.dart';
import '../../../../core/network/api_type.dart';

/// Abstract class defining methods for authentication API repositories.
abstract class BuyerRequestRepository {
  Future<BuyerRequest> createBuyerRequest(FormData data);
  Future<BuyerRequest> getRequestById(String id);
  Future<ViewAllBuyerRequests> viewAllBuyerRequests();
  Future<CommentModel> addComment(
      {required Map<String, dynamic>? data, required String reqId});
  Future<UserRequestResponse> createUserRequest(UserRequestModel request);
  Future<UserRequestResponse> createUserRequestWithFiles(FormData formData);
}

class BuyerRequestRepositoryRemote implements BuyerRequestRepository {
  final ApiClient _apiClient = getIt<ApiClient>();
  BuyerRequestRepositoryRemote();

  @override
  Future<BuyerRequest> createBuyerRequest(FormData data) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.buyerRequests, method: ApiType.post, data: data);
      return BuyerRequest.fromJson(response);
    } catch (e) {
      print("createBuyerRequest m error: $e");
      rethrow;
    }
  }

  @override
  Future<BuyerRequest> getRequestById(String id) async {
    try {
      final response = await _apiClient.request(
          path: "${ApiConst.buyerRequests}/$id", method: ApiType.get);
      return BuyerRequest.fromJson(response);
    } catch (e) {
      print("getRequestById error: $e");
      rethrow;
    }
  }

  @override
  Future<ViewAllBuyerRequests> viewAllBuyerRequests() async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.buyerRequests, method: ApiType.get);
      return ViewAllBuyerRequests.fromJson(response);
    } catch (e) {
      print("viewAllBuyerRequests m error: $e");
      rethrow;
    }
  }

  @override
  Future<CommentModel> addComment(
      {required Map<String, dynamic>? data, required String reqId}) async {
    try {
      final response = await _apiClient.request(
          path: "${ApiConst.buyerRequests}/$reqId/comments",
          method: ApiType.post,
          data: data);
      return CommentModel.fromJson(response);
    } catch (e) {
      print("addComment m error: $e");
      rethrow;
    }
  }

  @override
  Future<UserRequestResponse> createUserRequest(
      UserRequestModel request) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.userRequest,
          method: ApiType.post,
          data: request.toJson(),
          headers: {
            'Scope': 'multi-me',
          });
      return UserRequestResponse.fromJson(response);
    } catch (e) {
      print("createUserRequest error: $e");
      rethrow;
    }
  }

  @override
  Future<UserRequestResponse> createUserRequestWithFiles(
      FormData formData) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.userRequest,
          method: ApiType.post,
          data: formData,
          headers: {
            'Scope': 'multi-me',
          });
      return UserRequestResponse.fromJson(response);
    } catch (e) {
      print("createUserRequestWithFiles error: $e");
      rethrow;
    }
  }
}
