part of 'conversation_list_bloc.dart';

class ConversationListState extends Equatable {
  final bool isLoading;
  final bool isLoadingContacts;
  final bool isLoadingMore;
  final bool isLoadingMoreContacts;
  final bool hasReachedEnd;
  final bool hasContactsEnd;
  final String? error;
  final List<Conversations> conversations;
  final List<Contacts> contacts;

  const ConversationListState({
    this.isLoading = false,
    this.isLoadingContacts = false,
    this.isLoadingMore = false,
    this.isLoadingMoreContacts = false,
    this.hasReachedEnd = false,
    this.hasContactsEnd = false,
    this.error,
    this.conversations = const [],
    this.contacts = const [],
  });

  ConversationListState copyWith({
    bool? isLoading,
    bool? isLoadingContacts,
    bool? isLoadingMore,
    bool? isLoadingMoreContacts,
    bool? hasReachedEnd,
    bool? hasContactsEnd,
    String? error,
    List<Conversations>? conversations,
    List<Contacts>? contacts,
  }) {
    return ConversationListState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingContacts: isLoadingContacts ?? this.isLoadingContacts,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isLoadingMoreContacts:
          isLoadingMoreContacts ?? this.isLoadingMoreContacts,
      hasReachedEnd: hasReachedEnd ?? this.hasReachedEnd,
      hasContactsEnd: hasContactsEnd ?? this.hasContactsEnd,
      error: error ?? this.error,
      conversations: conversations ?? this.conversations,
      contacts: contacts ?? this.contacts,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isLoadingContacts,
        isLoadingMore,
        isLoadingMoreContacts,
        hasReachedEnd,
        hasContactsEnd,
        error,
        conversations,
        contacts,
      ];
}
