import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/model/chat/contacts.dart';
import 'package:multime_app/core/model/chat/conversations.dart';
import 'package:multime_app/modules/chat/data/repository/chat_repository.dart';

part 'conversation_list_event.dart';
part 'conversation_list_state.dart';

class ConversationListBloc
    extends Bloc<ConversationListEvent, ConversationListState> {
  final ChatRepository _repository;
  CancelToken? _conversationsCancelToken;
  CancelToken? _contactsCancelToken;
  int _conversationsCurrentPage = 1;
  int _contactsCurrentPage = 1;
  static const int _pageSize = 10;

  ConversationListBloc(this._repository)
      : super(const ConversationListState(isLoading: true)) {
    on<LoadConversations>(_onLoadConversations);
    on<LoadMoreConversations>(_onLoadMoreConversations);
    on<UpdateConversations>(_onUpdateConversations);
    on<CancelRequest>(_onCancelRequest);
    on<LoadContacts>(_onLoadContacts);
    on<LoadMoreContacts>(_onLoadMoreContacts);
    on<UpdateConversationsViaPUC>(_onUpdateConversationsViaPUC);
  }

  // ✅ Load conversations
  Future<void> _onLoadConversations(
    LoadConversations event,
    Emitter<ConversationListState> emit,
  ) async {
    _conversationsCancelToken?.cancel();
    _conversationsCancelToken = CancelToken();
    _conversationsCurrentPage = 1;

    emit(
      state.copyWith(
        isLoading: true,
        error: null,
        hasReachedEnd: false,
      ),
    );

    try {
      final conversations = await _repository.getConversations(
        page: _conversationsCurrentPage,
        limit: _pageSize,
        cancelToken: _conversationsCancelToken,
      );

      emit(
        state.copyWith(
          isLoading: false,
          conversations: conversations,
          hasReachedEnd: conversations.length < _pageSize,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          isLoading: false,
          error: error.toString(),
        ),
      );
    } finally {
      _conversationsCancelToken = null;
    }
  }

  // ✅ Load more conversations
  Future<void> _onLoadMoreConversations(
    LoadMoreConversations event,
    Emitter<ConversationListState> emit,
  ) async {
    _conversationsCancelToken?.cancel();
    _conversationsCancelToken = CancelToken();

    emit(
      state.copyWith(
        isLoadingMore: true,
      ),
    );

    try {
      _conversationsCurrentPage++;

      final conversations = await _repository.getConversations(
        page: _conversationsCurrentPage,
        limit: _pageSize,
        cancelToken: _conversationsCancelToken,
      );

      if (conversations.isEmpty) {
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasReachedEnd: true,
          ),
        );
        return;
      }

      final updated = [...state.conversations, ...conversations];

      emit(
        state.copyWith(
          isLoadingMore: false,
          conversations: updated,
          hasReachedEnd: conversations.length < _pageSize,
        ),
      );
    } catch (error) {
      _conversationsCurrentPage--;

      emit(
        state.copyWith(
          isLoadingMore: false,
          error: error.toString(),
        ),
      );
    } finally {
      _conversationsCancelToken = null;
    }
  }

  // ✅ Update conversations when a new message is sent
  Future<void> _onUpdateConversations(
    UpdateConversations event,
    Emitter<ConversationListState> emit,
  ) async {
    final conversations = List<Conversations>.from(state.conversations);
    final existing = conversations.indexWhere(
      (c) => c.channelName == event.conversation.channelName,
    );

    if (existing != -1) {
      conversations[existing] = event.conversation;
    } else {
      conversations.add(event.conversation);
    }

    conversations.sort(
      (a, b) {
        final prev = BigInt.tryParse(a.timetoken ?? '0') ?? BigInt.zero;
        final next = BigInt.tryParse(b.timetoken ?? '0') ?? BigInt.zero;
        return next.compareTo(prev);
      },
    );

    emit(
      state.copyWith(
        conversations: conversations,
      ),
    );
  }

  // ✅ Load contacts
  Future<void> _onLoadContacts(
    LoadContacts event,
    Emitter<ConversationListState> emit,
  ) async {
    _contactsCancelToken?.cancel();
    _contactsCancelToken = CancelToken();
    _contactsCurrentPage = 1;

    emit(
      state.copyWith(
        isLoadingContacts: true,
        error: null,
        hasContactsEnd: false,
      ),
    );
    try {
      final contacts = await _repository.getContacts(
        page: _contactsCurrentPage,
        limit: _pageSize,
        cancelToken: _contactsCancelToken,
      );

      emit(
        state.copyWith(
          isLoadingContacts: false,
          contacts: contacts,
          hasContactsEnd: contacts.length < _pageSize,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          isLoadingContacts: false,
          error: error.toString(),
        ),
      );
    } finally {
      _contactsCancelToken = null;
    }
  }

  // ✅ Load more contacts
  Future<void> _onLoadMoreContacts(
    LoadMoreContacts event,
    Emitter<ConversationListState> emit,
  ) async {
    _contactsCancelToken?.cancel();
    _contactsCancelToken = CancelToken();

    emit(
      state.copyWith(
        isLoadingMoreContacts: true,
      ),
    );

    try {
      _contactsCurrentPage++;

      final contacts = await _repository.getContacts(
        page: _contactsCurrentPage,
        limit: _pageSize,
        cancelToken: _contactsCancelToken,
      );

      if (contacts.isEmpty) {
        emit(
          state.copyWith(
            isLoadingMoreContacts: false,
            hasContactsEnd: true,
          ),
        );
        return;
      }

      final updated = [...state.contacts, ...contacts];

      emit(
        state.copyWith(
          isLoadingMoreContacts: false,
          contacts: updated,
          hasContactsEnd: contacts.length < _pageSize,
        ),
      );
    } catch (error) {
      _contactsCurrentPage--;

      emit(
        state.copyWith(
          isLoadingMoreContacts: false,
          error: error.toString(),
        ),
      );
    } finally {
      _contactsCancelToken = null;
    }
  }

  // ✅ Update conversations via personal update channel
  Future<void> _onUpdateConversationsViaPUC(
    UpdateConversationsViaPUC event,
    Emitter<ConversationListState> emit,
  ) async {
    try {
      final data = Map<String, dynamic>.from(event.data);
      final lastMessage = data['last_message'] is Map<String, dynamic>
          ? Map<String, dynamic>.from(data['last_message'])
          : <String, dynamic>{};
      final partner = data['partner'] is Map<String, dynamic>
          ? Map<String, dynamic>.from(data['partner'])
          : <String, dynamic>{};
      final channelID = event.channelID;

      if (channelID.isEmpty) return;
      final updated = List<Conversations>.from(state.conversations);

      Conversations _buildConversation(Conversations? current) {
        final partnerUID = partner['user_id']?.toString();
        final usernameInPayload = (data['channel_name']?.toString() ??
                partner['name']?.toString() ??
                current?.userName) ??
            '';
        final avatarInPayload = partner['avatar']?.toString();

        int? uid;
        if (partnerUID != null && partnerUID.isNotEmpty) {
          uid = int.tryParse(partnerUID);
        }
        uid ??= current?.userID;

        return Conversations(
          userID: uid,
          userName: usernameInPayload,
          avatar: current?.avatar ?? avatarInPayload,
          isOnline: current?.isOnline ?? false,
          senderID: int.tryParse(
              (lastMessage['sender_id'] ?? current?.senderID)?.toString() ??
                  ''),
          channelName: current?.channelName ?? channelID,
          message:
              (lastMessage['content']?.toString() ?? current?.message) ?? '',
          timetoken:
              (lastMessage['timetoken']?.toString() ?? current?.timetoken) ??
                  '',
          messageType:
              (lastMessage['type']?.toString() ?? current?.messageType) ?? '',
          unreadCount: data['unread_count'] ?? current?.unreadCount ?? 0,
        );
      }

      int index = updated.indexWhere(
        (channel) => channel.channelName == channelID,
      );
      if (index == -1) {
        final conversation = _buildConversation(null);
        if (conversation.userID == null) return;
        updated.insert(0, conversation);
      } else {
        updated[index] = _buildConversation(updated[index]);
      }

      updated.sort((a, b) {
        final aTimetoken = BigInt.tryParse(a.timetoken ?? '0') ?? BigInt.zero;
        final bTimetoken = BigInt.tryParse(b.timetoken ?? '0') ?? BigInt.zero;
        return bTimetoken.compareTo(aTimetoken);
      });

      emit(
        state.copyWith(
          conversations: updated,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          error: error.toString(),
        ),
      );
    }
  }

  // ✅ Cancel any ongoing requests
  void _onCancelRequest(
    CancelRequest event,
    Emitter<ConversationListState> emit,
  ) {
    _conversationsCancelToken?.cancel();
    _conversationsCancelToken = null;

    _contactsCancelToken?.cancel();
    _contactsCancelToken = null;
  }

  @override
  Future<void> close() {
    _conversationsCancelToken?.cancel();
    _contactsCancelToken?.cancel();

    return super.close();
  }
}
