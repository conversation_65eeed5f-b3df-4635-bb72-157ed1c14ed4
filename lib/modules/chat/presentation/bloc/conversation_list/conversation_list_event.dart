part of 'conversation_list_bloc.dart';

class ConversationListEvent extends Equatable {
  const ConversationListEvent();

  @override
  List<Object?> get props => [];
}

// ✅ Load conversations
class LoadConversations extends ConversationListEvent {}

// ✅ Load more conversations
class LoadMoreConversations extends ConversationListEvent {}

// ✅ Update conversations when a new message is sent
class UpdateConversations extends ConversationListEvent {
  final Conversations conversation;

  const UpdateConversations(this.conversation);

  @override
  List<Object?> get props => [conversation];
}

// ✅ Load contacts
class LoadContacts extends ConversationListEvent {}

// ✅ Load more contacts
class LoadMoreContacts extends ConversationListEvent {}

// ✅ Cancel any ongoing requests
class CancelRequest extends ConversationListEvent {}

// ✅ Update conversations via personal update channel
class UpdateConversationsViaPUC extends ConversationListEvent {
  final String channelID;
  final Map<String, dynamic> data;

  const UpdateConversationsViaPUC({
    required this.channelID,
    required this.data,
  });

  @override
  List<Object?> get props => [channelID, data];
}
