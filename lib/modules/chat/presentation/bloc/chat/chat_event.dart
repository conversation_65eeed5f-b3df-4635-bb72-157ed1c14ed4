part of 'chat_bloc.dart';

abstract class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object> get props => [];
}

// ✅ Load first batch of messages
class LoadMessages extends ChatEvent {}

// ✅ Load more messages
class LoadMoreMessages extends ChatEvent {}

// ✅ Send text message
class SendMessage extends ChatEvent {
  final String message;

  const SendMessage({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// ✅ Send reply message
class SendReply extends ChatEvent {
  final String message;
  final ChatMessage parentMessage;

  const SendReply({
    required this.message,
    required this.parentMessage,
  });

  @override
  List<Object> get props => [message, parentMessage];
}

// ✅ Send sticker
class SendSticker extends ChatEvent {
  final String sticker;

  const SendSticker(this.sticker);

  @override
  List<Object> get props => [sticker];
}

// ✅ Send image
class SendImage extends ChatEvent {
  final File image;

  const SendImage(this.image);

  @override
  List<Object> get props => [image];
}

// ✅ Send file
class SendFile extends ChatEvent {
  final File file;

  const SendFile(this.file);

  @override
  List<Object> get props => [file];
}

// ✅ Download file
class DownloadFile extends ChatEvent {
  final String fileID;
  final String fileName;

  const DownloadFile(this.fileID, this.fileName);

  @override
  List<Object> get props => [fileID, fileName];
}

// ✅ Handle messages received from PubNub stream
class StreamMessageReceived extends ChatEvent {
  final ChatMessage envelope;

  const StreamMessageReceived(this.envelope);

  @override
  List<Object> get props => [envelope];
}

// ✅ Translate speech-to-text
class TranslateSpeechToText extends ChatEvent {
  final String filePath;
  final String fileName;
  final String sourceLang;
  final String targetLang;

  const TranslateSpeechToText({
    required this.filePath,
    required this.fileName,
    required this.sourceLang,
    required this.targetLang,
  });

  @override
  List<Object> get props => [filePath, fileName, sourceLang, targetLang];
}

// ✅ Reset translation state
class ResetTranlation extends ChatEvent {}

// ✅ Send translation message
class SendTranslation extends ChatEvent {
  final String message;
  final String translated;

  const SendTranslation({
    required this.message,
    required this.translated,
  });

  @override
  List<Object> get props => [message, translated];
}

// ✅ Delete a message
class DeleteMessage extends ChatEvent {
  final String timetoken;
  final String channelName;

  const DeleteMessage({
    required this.timetoken,
    required this.channelName,
  });

  @override
  List<Object> get props => [timetoken, channelName];
}

// ✅ Send an offer message to a channel
class SendOffer extends ChatEvent {
  final String channelName;
  final CreateOfferResponse offer;

  const SendOffer({
    required this.channelName,
    required this.offer,
  });

  @override
  List<Object> get props => [channelName, offer];
}

// ✅ Cancel any ongoing requests
class CancelRequest extends ChatEvent {}

// ✅ Reset sending offer state
class ResetOfferState extends ChatEvent {}

// ✅ Mark message as read immediately
class ImmediatelyMarkRead extends ChatEvent {
  final String timetoken;

  const ImmediatelyMarkRead(this.timetoken);

  @override
  List<Object> get props => [timetoken];
}

// ✅ Mark message as read using debouncer
class DebouncingMarkRead extends ChatEvent {
  final String timetoken;

  const DebouncingMarkRead(this.timetoken);

  @override
  List<Object> get props => [timetoken];
}
