part of 'chat_bloc.dart';

class ChatState extends Equatable {
  final bool isLoading;
  final bool isLoadingMore;
  final bool hasReachedEnd;
  final String? error;
  final String? snackbarMessage;
  final List<ChatMessage> messages;
  final String? originalText;
  final String? translatedText;
  final bool isTranslating;
  final String? translationError;
  final bool isDownloading;
  final bool isOfferSent;
  final MarkReadResponse? markReadResponse;

  const ChatState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.hasReachedEnd = false,
    this.error,
    this.snackbarMessage,
    this.messages = const [],
    this.originalText,
    this.translatedText,
    this.isTranslating = false,
    this.translationError,
    this.isDownloading = false,
    this.isOfferSent = false,
    this.markReadResponse,
  });

  ChatState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasReachedEnd,
    String? error,
    String? snackbarMessage,
    ChannelAuthorization? authorization,
    List<ChatMessage>? messages,
    String? originalText,
    String? translatedText,
    bool? isTranslating,
    String? translationError,
    bool? isDownloading,
    bool? isOfferSent,
    MarkReadResponse? markReadResponse,
  }) {
    return ChatState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasReachedEnd: hasReachedEnd ?? this.hasReachedEnd,
      error: error,
      snackbarMessage: snackbarMessage,
      messages: messages ?? this.messages,
      originalText: originalText ?? this.originalText,
      translatedText: translatedText ?? this.translatedText,
      isTranslating: isTranslating ?? this.isTranslating,
      translationError: translationError ?? this.translationError,
      isDownloading: isDownloading ?? this.isDownloading,
      isOfferSent: isOfferSent ?? this.isOfferSent,
      markReadResponse: markReadResponse ?? this.markReadResponse,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isLoadingMore,
        hasReachedEnd,
        error,
        snackbarMessage,
        messages,
        originalText,
        translatedText,
        isTranslating,
        translationError,
        isDownloading,
        isOfferSent,
        markReadResponse,
      ];
}
