import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/chat/channel_authorization.dart';
import 'package:multime_app/core/model/chat/chat_message.dart';
import 'package:multime_app/core/model/chat/mark_read_response.dart';
import 'package:multime_app/core/model/offer/create_offer_response.dart';
import 'package:multime_app/modules/chat/data/repository/chat_repository.dart';
import 'package:multime_app/modules/chat/data/services/pubnub_actions_events.dart';
import 'package:multime_app/modules/chat/data/services/pubnub_services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pubnub/pubnub.dart';

part 'chat_event.dart';
part 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  PubNubServices service;
  final ChatRepository repository;
  final String channelName;
  final List<ChatMessage> _messages = [];
  StreamSubscription? _subscription;
  final Future<void> Function()? onExpired;
  final void Function(ChatMessage message)? onMessageUpdate;
  CancelToken? _cancelToken;
  late final PubNubActionsEvents _actionsEvents;

  ChatBloc({
    required this.service,
    required this.repository,
    required this.channelName,
    this.onExpired,
    this.onMessageUpdate,
  }) : super(const ChatState(isLoading: true)) {
    on<LoadMessages>(_onLoadMessages);
    on<LoadMoreMessages>(_onLoadMoreMessages);
    on<StreamMessageReceived>(_onStreamMessageReceived);
    on<SendMessage>(_onSendMessage);
    on<SendReply>(_onSendReply);
    on<SendSticker>(_onSendSticker);
    on<SendImage>(_onSendImage);
    on<SendFile>(_onSendFile);
    on<DownloadFile>(_onDownloadFile);
    on<TranslateSpeechToText>(_onTranslateSpeechToText);
    on<ResetTranlation>(_resetTranslation);
    on<SendTranslation>(_onSendTranslation);
    on<DeleteMessage>(_onDeleteMessage);
    on<SendOffer>(_onSendOffer);
    on<CancelRequest>(_onCancelRequest);
    on<ResetOfferState>(_onResetOfferState);

    // PubNub Actions and Events
    _actionsEvents = PubNubActionsEvents(
      repository: repository,
      channelID: channelName,
    );
    on<ImmediatelyMarkRead>(_onImmediatelyMarkRead);
    on<DebouncingMarkRead>(_onDebouncingMarkRead);

    // Subscribe to the channel and load first batch of messages when the BLoC is created
    _subscribe();
    add(LoadMessages());
  }

  // ✅ [ConversationListBloc] Update conversation when a new message is received
  void _updateConversation(ChatMessage message) {
    if (onMessageUpdate != null) {
      onMessageUpdate!(message);
    }
  }

  // ✅ Subscribe to the PubNub channel
  void _subscribe() {
    _subscription = service.subscribeChannel(channelName).listen(
      (message) => add(
        StreamMessageReceived(message),
      ),
      onError: (_) {
        if (onExpired != null) {
          onExpired!().then(
            (_) {
              service = PubNubServices(pubnub: service.pubnub);
              _subscription?.cancel();
              _subscribe();
            },
          ).catchError(
            (error) {
              print('Re-subscription error: $error');
            },
          );
        }
      },
    );
  }

  // ✅ Retry action if the token has expired
  Future<T> _onExpiredRetry<T>(Future<T> Function() action) async {
    try {
      return await action();
    } catch (error) {
      if (onExpired != null) {
        await onExpired!();
        // service = PubNubServices(pubnub: service.pubnub);
        await Future.delayed(
          Duration(milliseconds: 100),
        );
        return await action();
      }
      rethrow;
    }
  }

  // ✅ Load first batch of messages when the BLoC is created
  Future<void> _onLoadMessages(
    LoadMessages event,
    Emitter<ChatState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: true,
        error: null,
      ),
    );

    try {
      final history = await _onExpiredRetry(
        () => service.fetchMessages(channelName),
      );

      _messages
        ..clear()
        ..addAll(history.reversed);

      emit(
        state.copyWith(
          // isLoading: false,
          messages: List.from(_messages),
          hasReachedEnd: !service.hasMoreMessages,
        ),
      );

      if (_messages.isNotEmpty) {
        final response = await _actionsEvents.markMessageAsRead(
          _messages.first.timetoken,
          isDebouncing: false,
        );

        if (response != null) {
          emit(
            state.copyWith(
              isLoading: false,
              markReadResponse: response,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            isLoading: false,
          ),
        );
      }
    } catch (error) {
      emit(
        state.copyWith(
          isLoading: false,
          error: error.toString(),
        ),
      );
    }
  }

  // ✅ Load more messages
  Future<void> _onLoadMoreMessages(
    LoadMoreMessages event,
    Emitter<ChatState> emit,
  ) async {
    if (state.isLoadingMore || state.hasReachedEnd) return;

    emit(
      state.copyWith(
        isLoadingMore: true,
      ),
    );

    try {
      final messages = await _onExpiredRetry(
        () => service.fetchMoreMessages(channelName),
      );

      if (messages.isEmpty) {
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasReachedEnd: true,
          ),
        );
        return;
      }

      _messages.addAll(messages.reversed);

      emit(
        state.copyWith(
          isLoadingMore: false,
          messages: List.from(_messages),
          hasReachedEnd: !service.hasMoreMessages,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          isLoadingMore: false,
          error: error.toString(),
        ),
      );
    }
  }

  // ✅ Handle incoming messages from the PubNub stream
  void _onStreamMessageReceived(
    StreamMessageReceived event,
    Emitter<ChatState> emit,
  ) {
    final incoming = event.envelope;

    final isDuplicate = _messages.any(
      (message) => message.timetoken == incoming.timetoken,
    );
    if (isDuplicate) return;

    if (incoming.sticker != null && incoming.sticker!.isNotEmpty) {
      final index = _messages.indexWhere(
        (message) =>
            message.isPending &&
            message.sticker == incoming.sticker &&
            message.uuid == incoming.uuid,
      );

      if (index != -1) {
        _messages[index] = incoming;
      } else {
        _messages.insert(0, incoming);
      }
    } else if (incoming.image != null && incoming.image!.isNotEmpty) {
      final index = _messages.indexWhere(
        (message) =>
            message.isPending &&
            message.pendingImage != null &&
            message.uuid == incoming.uuid,
      );

      if (index != -1) {
        _messages[index] = incoming;
      } else {
        _messages.insert(0, incoming);
      }
    } else if (incoming.file != null && incoming.file!.isNotEmpty) {
      final index = _messages.indexWhere(
        (message) =>
            message.isPending &&
            message.pendingFile != null &&
            message.uuid == incoming.uuid,
      );

      if (index != -1) {
        _messages[index] = incoming;
      } else {
        _messages.insert(0, incoming);
      }
    } else {
      final index = _messages.indexWhere(
        (message) =>
            message.isPending &&
            message.message == incoming.message &&
            message.uuid == incoming.uuid,
      );

      if (index != -1) {
        _messages[index] = incoming;
      } else {
        _messages.insert(0, incoming);
      }
    }

    _updateConversation(incoming);

    emit(
      state.copyWith(
        messages: List.from(_messages),
      ),
    );
  }

  // ✅ Send a text message to the PubNub channel
  Future<void> _onSendMessage(
    SendMessage event,
    Emitter<ChatState> emit,
  ) async {
    final timetoken = Timetoken.fromDateTime(DateTime.now()).value.toString();
    final pendingMessage = ChatMessage(
      timetoken: timetoken,
      channel: channelName,
      uuid: gs.uid!,
      message: event.message,
      isPending: true,
    );

    _messages.insert(0, pendingMessage);
    emit(
      state.copyWith(
        messages: List.from(_messages),
      ),
    );

    try {
      await _onExpiredRetry(
        () => service.sendMessage(
          channelName,
          event.message,
        ),
      );
    } catch (error) {
      print('Error sending message: $error');
      emit(
        state.copyWith(
          snackbarMessage:
              'There was an error sending message. Please try again.',
        ),
      );
    }
  }

  // ✅ Send a text message to the PubNub channel
  Future<void> _onSendReply(
    SendReply event,
    Emitter<ChatState> emit,
  ) async {
    final timetoken = Timetoken.fromDateTime(DateTime.now()).value.toString();
    final pendingMessage = ChatMessage(
      timetoken: timetoken,
      channel: channelName,
      uuid: gs.uid!,
      message: event.message,
      parentMessage: event.parentMessage,
      isPending: true,
    );

    _messages.insert(0, pendingMessage);
    emit(
      state.copyWith(
        messages: List.from(_messages),
      ),
    );

    try {
      await _onExpiredRetry(
        () => service.sendReply(
          channelName,
          event.message,
          event.parentMessage,
        ),
      );
    } catch (error) {
      print('Error replying message: $error');
      emit(
        state.copyWith(
          snackbarMessage:
              'There was an error replying message. Please try again.',
        ),
      );
    }
  }

  // ✅ Send a sticker to the PubNub channel
  Future<void> _onSendSticker(
    SendSticker event,
    Emitter<ChatState> emit,
  ) async {
    final timetoken = Timetoken.fromDateTime(DateTime.now()).value.toString();
    final pendingSticker = ChatMessage(
      timetoken: timetoken,
      channel: channelName,
      uuid: gs.uid!,
      message: '',
      sticker: event.sticker,
      isPending: true,
    );

    _messages.insert(0, pendingSticker);
    emit(
      state.copyWith(
        messages: List.from(_messages),
      ),
    );

    try {
      await _onExpiredRetry(
        () => service.sendSticker(
          channelName,
          event.sticker,
        ),
      );
    } catch (error) {
      print('Error sending sticker: $error');
      emit(
        state.copyWith(
          snackbarMessage:
              'There was an error sending sticker. Please try again.',
        ),
      );
    }
  }

  // ✅ Send an image to the PubNub channel
  Future<void> _onSendImage(
    SendImage event,
    Emitter<ChatState> emit,
  ) async {
    final timetoken = Timetoken.fromDateTime(DateTime.now()).value.toString();
    final pendingMessage = ChatMessage(
      timetoken: timetoken,
      channel: channelName,
      uuid: gs.uid!,
      message: '',
      pendingImage: event.image.path,
      isPending: true,
    );

    _messages.insert(0, pendingMessage);
    emit(
      state.copyWith(
        messages: List.from(_messages),
      ),
    );

    try {
      await _onExpiredRetry(
        () => service.sendImage(
          channelName,
          event.image,
        ),
      );
    } catch (error) {
      print('Error sending image: $error');
      emit(
        state.copyWith(
          snackbarMessage:
              'There was an error sending image. Please try again.',
        ),
      );
    }
  }

  // ✅ Send a file to the PubNub channel
  Future<void> _onSendFile(
    SendFile event,
    Emitter<ChatState> emit,
  ) async {
    final timetoken = Timetoken.fromDateTime(DateTime.now()).value.toString();
    final pendingMessage = ChatMessage(
      timetoken: timetoken,
      channel: channelName,
      uuid: gs.uid!,
      message: '',
      pendingFile: event.file.path,
      isPending: true,
    );

    _messages.insert(0, pendingMessage);
    emit(
      state.copyWith(
        messages: List.from(_messages),
      ),
    );

    try {
      await _onExpiredRetry(
        () => service.sendFile(
          channelName,
          event.file,
        ),
      );
    } catch (error) {
      print('Error sending file: $error');
      emit(
        state.copyWith(
          snackbarMessage: 'There was an error sending file. Please try again.',
        ),
      );
    }
  }

  // ✅ Download a file from the PubNub channel
  Future<void> _onDownloadFile(
    DownloadFile event,
    Emitter<ChatState> emit,
  ) async {
    try {
      bool permissionGranted = false;

      if (Platform.isAndroid) {
        permissionGranted = await Permission.storage.request().isGranted;
        if (!permissionGranted) {
          permissionGranted =
              await Permission.manageExternalStorage.request().isGranted;
        }
      } else if (Platform.isIOS) {
        permissionGranted = true;
      }

      if (!permissionGranted) {
        emit(
          state.copyWith(
            snackbarMessage:
                'Permission denied. Please allow storage access to download files.',
          ),
        );
        return;
      }

      emit(
        state.copyWith(
          isDownloading: true,
        ),
      );

      final bytes = await service.downloadFile(
        channelName,
        event.fileID,
        event.fileName,
      );

      String path;
      File file;

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkInt = androidInfo.version.sdkInt;

        if (sdkInt >= 30) {
          path = '/storage/emulated/0/Download/${event.fileName}';
        } else {
          final dir = await getExternalStorageDirectory();
          path = '${dir!.path}/${event.fileName}';
        }
      } else if (Platform.isIOS) {
        final dir = await getApplicationDocumentsDirectory();
        path = '${dir.path}/${event.fileName}';
      } else {
        throw UnsupportedError('Unsupported platform');
      }

      file = File(path);
      await file.writeAsBytes(List<int>.from(bytes));

      emit(
        state.copyWith(
          isDownloading: false,
          snackbarMessage:
              'File \'${event.fileName}\' downloaded successfully.',
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          isDownloading: false,
          error: error.toString(),
        ),
      );
    }
  }

  // ✅ Translate speech-to-text
  Future<void> _onTranslateSpeechToText(
    TranslateSpeechToText event,
    Emitter<ChatState> emit,
  ) async {
    try {
      _cancelToken?.cancel();
      _cancelToken = CancelToken();

      emit(
        state.copyWith(
          isTranslating: true,
        ),
      );

      final result = await repository.translateMessage(
        event.filePath,
        event.fileName,
        event.sourceLang,
        event.targetLang,
        cancelToken: _cancelToken,
      );

      if (result.statusCode == 50) {
        emit(
          state.copyWith(
            isTranslating: false,
            translationError:
                'Voice could not be recognized or an unexpected error occured while translating, please try again.',
          ),
        );
        return;
      }

      emit(
        state.copyWith(
          isTranslating: false,
          originalText: result.originalText,
          translatedText: result.translatedText,
        ),
      );
    } catch (error) {
      if (error is DioException && error.type == DioExceptionType.cancel) {
        emit(
          state.copyWith(
            isTranslating: false,
          ),
        );
        return;
      }

      emit(
        state.copyWith(
          isTranslating: false,
          error: error.toString(),
        ),
      );
    } finally {
      _cancelToken = null;
    }
  }

  // ✅ Reset translation state
  Future<void> _resetTranslation(
    ResetTranlation event,
    Emitter<ChatState> emit,
  ) async {
    emit(
      state.copyWith(
        originalText: '',
        translatedText: '',
        translationError: '',
      ),
    );
  }

  // ✅ Send translation message
  Future<void> _onSendTranslation(
    SendTranslation event,
    Emitter<ChatState> emit,
  ) async {
    try {
      await _onExpiredRetry(
        () => service.sendTranslation(
          channelName,
          event.message,
          event.translated,
        ),
      );
    } catch (error) {
      print('Error sending translation: $error');
      emit(
        state.copyWith(
          snackbarMessage:
              'There was an error sending translation. Please try again.',
        ),
      );
    }
  }

  // ✅ Delete a message from the PubNub channel
  Future<void> _onDeleteMessage(
    DeleteMessage event,
    Emitter<ChatState> emit,
  ) async {
    try {
      emit(
        state.copyWith(
          snackbarMessage: null,
        ),
      );

      await service.deleteMessage(
        event.channelName,
        event.timetoken,
      );

      _messages.removeWhere(
        (message) => message.timetoken == event.timetoken,
      );
      // Update conversation list when a message is deleted
      if (_messages.isNotEmpty && onMessageUpdate != null) {
        final latest = _messages.first;
        onMessageUpdate!(latest);
      }

      emit(
        state.copyWith(
          messages: List.from(_messages),
          snackbarMessage: 'Message deleted successfully',
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          snackbarMessage:
              'There was an error deleting the message. Please try again.',
        ),
      );
      print('Error deleting message: $error');
    }
  }

  // ✅ Send an offer message to a channel
  Future<void> _onSendOffer(
    SendOffer event,
    Emitter<ChatState> emit,
  ) async {
    try {
      await _onExpiredRetry(
        () => service.sendOffer(
          channelName,
          event.offer,
        ),
      );

      emit(
        state.copyWith(
          isOfferSent: true,
        ),
      );
    } catch (error) {
      print('Error sending offer: $error');
    }
  }

  // ✅ Cancel any ongoing requests
  void _onCancelRequest(
    CancelRequest event,
    Emitter<ChatState> emit,
  ) {
    _cancelToken?.cancel();
    _cancelToken = null;
  }

  // ✅ Reset sending offer state
  void _onResetOfferState(
    ResetOfferState event,
    Emitter<ChatState> emit,
  ) {
    emit(
      state.copyWith(
        isOfferSent: false,
      ),
    );
  }

  // ✅ Mark message as read immediately
  Future<void> _onImmediatelyMarkRead(
    ImmediatelyMarkRead event,
    Emitter<ChatState> emit,
  ) async {
    try {
      final response = await _actionsEvents.markMessageAsRead(
        event.timetoken,
        isDebouncing: false,
      );

      emit(
        state.copyWith(
          markReadResponse: response,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          error: error.toString(),
        ),
      );
    }
  }

  // ✅ Mark message as read using debouncer
  Future<void> _onDebouncingMarkRead(
    DebouncingMarkRead event,
    Emitter<ChatState> emit,
  ) async {
    try {
      final response = await _actionsEvents.markMessageAsRead(
        event.timetoken,
        isDebouncing: true,
      );

      emit(
        state.copyWith(
          markReadResponse: response,
        ),
      );
    } catch (error) {
      emit(
        state.copyWith(
          error: error.toString(),
        ),
      );
    }
  }

  @override
  Future<void> close() {
    _subscription?.cancel();
    service.resetPagination();
    _cancelToken?.cancel();
    _actionsEvents.dispose();

    return super.close();
  }
}
