import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/global/global_presence/bloc/global_bloc.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/data/repository/chat_repository.dart';
import 'package:multime_app/modules/chat/data/services/pubnub_instance.dart';
import 'package:multime_app/modules/chat/presentation/bloc/conversation_list/conversation_list_bloc.dart';
import 'package:multime_app/modules/chat/presentation/widgets/custom_message_dialog.dart';
import 'package:multime_app/modules/chat/presentation/widgets/messages/adaptive_time.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/user_avatar.dart';
import 'package:multime_app/modules/chat/presentation/widgets/shimmer/contact_shimmer.dart';
import 'package:multime_app/modules/chat/presentation/widgets/shimmer/conversation_shimmer.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../core/global/global_presence/bloc/global_state.dart';
import '../../../core/streams/presence_stream_controller.dart';
import '../data/services/pubnub_services.dart';

class ChatHomepage extends StatefulWidget {
  const ChatHomepage({super.key});

  @override
  State<ChatHomepage> createState() => _ChatHomepageState();
}

class _ChatHomepageState extends State<ChatHomepage> {
  final Set<int> _activeNavigations = <int>{};

  // Determine whether to show the notification card
  bool _showNotificationCard = true;

  // Controllers for scrolling
  final ScrollController _scrollController = ScrollController();
  final ScrollController _contactsScrollController = ScrollController();

  // BLoC and PubNub Services
  ConversationListBloc? _bloc;
  PubNubServices? _services;
  StreamSubscription<Map<String, dynamic>>? _personalUpdateSubscription;

  @override
  void initState() {
    super.initState();

    _initializeGlobalChat();
    _setupPresenceListener();

    _scrollController.addListener(_scrollListener);
    _contactsScrollController.addListener(_contactsScrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();

    _contactsScrollController.removeListener(_contactsScrollListener);
    _contactsScrollController.dispose();

    _personalUpdateSubscription?.cancel();

    super.dispose();
  }

  Future<void> _initializeGlobalChat() async {
    try {
      final repository = getIt<ChatRepository>();
      final response = await repository.getPersonalUpdateToken(gs.uid!);

      debugPrint('🔄 Fetching personal update token for user: ${gs.uid}');
      debugPrint('🔑 Token response: ${response.token}');
      debugPrint('📡 Update channel: ${response.channelID}');
      debugPrint('⏰ TTL: ${response.ttl} seconds');

      final pubnub = PubNubInstance.create(response.token);
      _services = PubNubServices(pubnub: pubnub);

      _personalUpdateSubscription =
          _services!.processPersonalUpdateChannel().listen(
        (update) => _handlePersonalChannelUpdates(update),
        onError: (error) {
          debugPrint('Error updating channel: $error');
          // Refresh token if encountering a 403 error (token expired)
          if (error.toString().contains('403')) {
            _refreshPersonalUpdateToken();
          }
        },
      );
    } catch (error) {
      debugPrint('Error initializing global chat: $error');
      rethrow;
    }
  }

  Future<void> _refreshPersonalUpdateToken() async {
    try {
      // Cancel any existing subscription
      await _personalUpdateSubscription?.cancel();
      _personalUpdateSubscription = null;

      // Reinitialize the global chat with new token
      await _initializeGlobalChat();
    } catch (error) {
      debugPrint('Error refreshing token: $error');

      // Retry after a delay if there's an error
      Future.delayed(Duration(milliseconds: 5000), () {
        _refreshPersonalUpdateToken();
      });
    }
  }

  void _setupPresenceListener() {
    // Listen to PresenceSubscribed event for GlobalBloc to handle presence updates
    PresenceStreamController.stream.listen(
      (event) {
        if (event is PresenceSubscribed) {
          debugPrint(
            '📨 ChatHomepage: Receiving PresenceSubscribed event for user with UID: ${event.userId}',
          );
          debugPrint(
            '🔑 ChatHomepage: AuthKey: ${event.authKey.substring(0, 10)}...',
          );
          debugPrint(
            '✅ ChatHomepage: GlobalBloc will handle presence subscription automatically.',
          );
        }
      },
    );
  }

  void _handlePersonalChannelUpdates(Map<String, dynamic> update) {
    try {
      final type = update['type'] as String?;

      if (type == 'channel_list_update') {
        final data = update['data'] as Map<String, dynamic>;
        final channelID = data['channel_id'] as String;

        if (_bloc != null && !_bloc!.isClosed) {
          _bloc!.add(
            UpdateConversationsViaPUC(
              channelID: channelID,
              data: data,
            ),
          );
        }
      }
    } catch (error) {
      debugPrint('Error handling personal channel update: $error');
    }
  }

  void _scrollListener() {
    if (_bloc != null && !_bloc!.isClosed && _scrollController.hasClients) {
      final state = _bloc!.state;
      if (state.isLoading || state.isLoadingMore || state.hasReachedEnd) {
        return;
      }

      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _bloc?.add(LoadMoreConversations());
      }
    }
  }

  void _contactsScrollListener() {
    if (_bloc != null &&
        !_bloc!.isClosed &&
        _contactsScrollController.hasClients) {
      final state = _bloc!.state;
      if (state.isLoadingContacts ||
          state.isLoadingMoreContacts ||
          state.hasContactsEnd) {
        return;
      }

      if (_contactsScrollController.position.pixels >=
          _contactsScrollController.position.maxScrollExtent - 50) {
        _bloc?.add(LoadMoreContacts());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        _bloc = ConversationListBloc(
          getIt<ChatRepository>(),
        );
        return _bloc!
          ..add(LoadConversations())
          ..add(LoadContacts());
      },
      child: Builder(
        builder: (context) => Scaffold(
          appBar: _buildChatHomepageAppBar(context),
          body: SafeArea(
            child: BlocBuilder<ConversationListBloc, ConversationListState>(
              builder: (context, state) {
                return RefreshIndicator(
                  onRefresh: state.isLoading || state.isLoadingMore
                      ? () async {
                          return Future.delayed(Duration.zero);
                        }
                      : () async {
                          final bloc = context.read<ConversationListBloc>();
                          if (!bloc.isClosed && !bloc.state.isLoading) {
                            bloc
                              ..add(
                                LoadConversations(),
                              )
                              ..add(
                                LoadContacts(),
                              );
                            await bloc.stream.firstWhere(
                              (state) => !state.isLoading,
                            );
                          }
                        },
                  backgroundColor: Theme.of(context).whitePrimary(context),
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    physics: state.isLoading
                        ? const NeverScrollableScrollPhysics()
                        : const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        /*
                        _buildSearchBar(context),
                        */
                        if (_showNotificationCard)
                          _buildNotificationCard(context),
                        _buildContacts(),
                        _buildConversations(),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationCard(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 0.h,
        bottom: 24.h,
      ),
      padding: EdgeInsets.symmetric(
        vertical: 18.h,
        horizontal: 16.w,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).textPrimary(context),
            const Color(0xFF596D8F),
          ],
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Stay up to date!',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).whitePrimary(context),
                ),
          ),
          Gap(8.h),
          Text(
            'Turn on notification so don\'t miss any messages.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).greyScale400(context),
                  fontFamily: AppFontFamily.light,
                ),
          ),
          Gap(24.h),
          Row(
            children: [
              OutlinedButton(
                onPressed: () {
                  setState(() {
                    _showNotificationCard = false;
                  });
                },
                style: OutlinedButton.styleFrom(
                  side: BorderSide(
                    color: Theme.of(context).greyScale400(context),
                  ),
                  backgroundColor: Theme.of(context).transparent(context),
                  padding: EdgeInsets.symmetric(
                    vertical: 10.h,
                    horizontal: 16.w,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  overlayColor: Theme.of(context)
                      .blackPrimary(context)
                      .withOpacity(0.005),
                ),
                child: Text(
                  'Not now',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).greyScale400(context),
                      ),
                ),
              ),
              Gap(12.w),
              ElevatedButton(
                onPressed: () async {
                  final permission = await Permission.notification.isGranted;
                  if (!permission) {
                    showDialog(
                      context: context,
                      builder: (_) {
                        return CustomMessageDialog(
                          title: 'Notification Permission',
                          subtitle:
                              'Please allow notifications within your device settings so you never miss important messages from your contacts.',
                          confirmText: 'Turn on',
                          onConfirm: () {
                            openAppSettings();
                            context.pop();
                          },
                        );
                      },
                    );
                    return;
                  } else {
                    setState(() {
                      _showNotificationCard = false;
                    });
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).whitePrimary(context),
                  padding: EdgeInsets.symmetric(
                    vertical: 10.h,
                    horizontal: 16.w,
                  ),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  overlayColor: Theme.of(context)
                      .blackPrimary(context)
                      .withOpacity(0.005),
                ),
                child: Text(
                  'Turn on notifications',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textPrimary(context),
                      ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 8.h,
        bottom: 24.h,
      ),
      child: TextField(
        decoration: InputDecoration(
          contentPadding: PaddingConstants.padSymH16.add(
            PaddingConstants.padSymV12,
          ),
          hintText: 'Search something',
          hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).textSecondary100(context),
                fontFamily: AppFontFamily.regular,
              ),
          prefixIcon: Icon(
            Icons.search,
            color: Theme.of(context).textSecondary100(context),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(
              color: Theme.of(context).textSecondary100(context),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(
              color: Theme.of(context).textSecondary100(context),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.r),
            borderSide: BorderSide(
              color: Theme.of(context).textSecondary100(context),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContacts() {
    return BlocBuilder<ConversationListBloc, ConversationListState>(
      builder: (context, state) {
        // ✅ Situation when contacts are loading
        if (state.isLoadingContacts) {
          return SizedBox(
            height: 100.h,
            child: ListView.separated(
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
                top: 4.h,
                bottom: 0.h,
              ),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              scrollDirection: Axis.horizontal,
              itemCount: 6,
              itemBuilder: (context, index) => ContactShimmer(),
              separatorBuilder: (context, index) => Gap(16.w),
            ),
          );
        }

        // ✅ Situation when there are no contacts
        if (state.contacts.isEmpty) {
          return SizedBox.shrink();
        }

        return Align(
          alignment: Alignment.centerLeft,
          child: SizedBox(
            height: 100.h,
            child: ListView.separated(
              controller: _contactsScrollController,
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
                top: 4.h,
                bottom: 0.h,
              ),
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount:
                  state.contacts.length + (state.isLoadingMoreContacts ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == state.contacts.length &&
                    state.isLoadingMoreContacts) {
                  return ContactShimmer();
                }

                final contact = state.contacts[index];
                return InkWell(
                  onTap: () {
                    final contactID = contact.userID;
                    if (_activeNavigations.contains(contactID)) {
                      debugPrint(
                        '🚫 Navigation blocked: Already navigating to $contactID',
                      );
                      return;
                    }
                    _activeNavigations.add(contactID!);

                    context.push(
                      RouteName.chatPage,
                      extra: {
                        'bloc': context.read<ConversationListBloc>(),
                        'partner_id': contact.userID,
                        'name': contact.name,
                        'avatar': contact.avatar,
                      },
                    ).whenComplete(() {
                      _activeNavigations.remove(contactID);
                    });
                  },
                  child: SizedBox(
                    width: 74.w,
                    child: Column(
                      children: [
                        BlocBuilder<GlobalBloc, GlobalState>(
                          builder: (context, globalState) {
                            bool isOnline = false;

                            if (contact.userID != null &&
                                globalState.isPresenceSubscribed) {
                              if (globalState.globalPresence
                                  .containsKey(contact.userID!)) {
                                isOnline = globalState
                                    .globalPresence[contact.userID!]!;
                              }
                            }

                            return UserAvatar(
                              username: contact.name,
                              size: 64,
                              imageURL: contact.avatar ?? '',
                              isOnline: isOnline,
                            );
                          },
                        ),
                        Gap(8.h),
                        Text(
                          contact.name!.split(' ').last,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontFamily: AppFontFamily.bold,
                                  ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) => Gap(16.w),
            ),
          ),
        );
      },
    );
  }

  Widget _buildConversations() {
    String _getLastMessage({
      required String type,
      required String message,
      required String username,
      required bool me,
    }) {
      switch (type) {
        case 'text':
          if (me) {
            return 'You: $message';
          } else {
            return message;
          }
        case 'sticker':
          if (me) {
            return 'You sent a sticker.';
          } else {
            return '$username sent you a sticker.';
          }
        case 'file':
          if (me) {
            return 'You sent a file.';
          } else {
            return '$username sent you a file.';
          }
        case 'image':
          if (me) {
            return 'You sent an image.';
          } else {
            return '$username sent you an image.';
          }
        case 'offer':
          if (me) {
            return 'You sent an offer.';
          } else {
            return '$username sent you an offer.';
          }
        default:
          return 'You have a new message.';
      }
    }

    return BlocBuilder<ConversationListBloc, ConversationListState>(
      builder: (context, state) {
        // ✅ Situation when conversations are loading
        if (state.isLoading) {
          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(
              vertical: 8.h,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => ConversationShimmer(),
            separatorBuilder: (context, index) => Padding(
              padding: EdgeInsets.symmetric(
                vertical: 8.h,
                horizontal: 16.w,
              ),
              child: Divider(
                color: Theme.of(context).greyScale300(context),
                thickness: 1,
                height: 0.h,
              ),
            ),
          );
        }

        // ✅ Situation when there is an error
        if (state.error != null) {
          debugPrint("Chat error: ${state.error}");
          return Padding(
            padding: PaddingConstants.padAll16,
            child: Center(
              child: Text("Error: ${state.error}"),
            ),
          );
        }

        // ✅ Situation when there are no conversations
        if (state.conversations.isEmpty) {
          return SizedBox.shrink();
        }

        // ✅ Situation when there are conversations
        final valid = state.conversations
            .where((conversation) => conversation.senderID != 0)
            .toList();

        return Column(
          children: [
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.symmetric(
                vertical: 8.h,
              ),
              itemCount: valid.length,
              itemBuilder: (context, index) {
                final conversation = valid[index];

                return InkWell(
                  onTap: () async {
                    final userID = conversation.userID;
                    if (_activeNavigations.contains(userID)) {
                      debugPrint(
                        '🚫 Navigation blocked: Already navigating to $userID',
                      );
                      return;
                    }
                    _activeNavigations.add(userID!);

                    context.push(
                      RouteName.chatPage,
                      extra: {
                        'bloc': context.read<ConversationListBloc>(),
                        'partner_id': conversation.userID,
                        'name': conversation.userName,
                        'avatar': conversation.avatar,
                      },
                    ).whenComplete(() {
                      _activeNavigations.remove(userID);
                    });
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              BlocBuilder<GlobalBloc, GlobalState>(
                                builder: (context, globalState) {
                                  bool isOnline = false;

                                  if (conversation.userID != null &&
                                      globalState.isPresenceSubscribed) {
                                    if (globalState.globalPresence
                                        .containsKey(conversation.userID!)) {
                                      isOnline = globalState.globalPresence[
                                          conversation.userID!]!;
                                    }
                                  }

                                  return UserAvatar(
                                    username: conversation.userName,
                                    size: 46,
                                    imageURL: conversation.avatar ?? '',
                                    isOnline: isOnline,
                                  );
                                },
                              ),
                              Gap(12.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      conversation.userName!,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                            fontFamily: AppFontFamily.bold,
                                          ),
                                    ),
                                    Gap(4.h),
                                    Text(
                                      _getLastMessage(
                                        type: conversation.messageType!,
                                        message: conversation.message!,
                                        username: conversation.userName!,
                                        me: conversation.senderID! == gs.uid!,
                                      ),
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            fontFamily:
                                                (conversation.unreadCount > 0)
                                                    ? AppFontFamily.bold
                                                    : AppFontFamily.medium,
                                          ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    Gap(8.h),
                                    AdaptiveTimeWidget(
                                      timetoken: conversation.timetoken!,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (conversation.unreadCount > 0)
                          Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primary(context),
                              borderRadius: BorderRadius.circular(16.r),
                            ),
                            constraints: BoxConstraints(
                              minWidth: 18.w,
                              minHeight: 20.h,
                            ),
                            child: Text(
                              conversation.unreadCount.toString(),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    fontFamily: AppFontFamily.bold,
                                    color:
                                        Theme.of(context).whitePrimary(context),
                                  ),
                            ),
                          )
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) => Padding(
                padding: EdgeInsets.symmetric(
                  vertical: 8.h,
                  horizontal: 16.w,
                ),
                child: Divider(
                  color: Theme.of(context).greyScale300(context),
                  thickness: 1,
                  height: 0.h,
                ),
              ),
            ),

            // ✅ Lazy loading/infinite scrolling
            BlocBuilder<ConversationListBloc, ConversationListState>(
              builder: (context, state) {
                if (state.isLoadingMore) {
                  return Padding(
                    padding: EdgeInsets.only(top: 8.h, bottom: 24.h),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                return SizedBox.shrink();
              },
            ),
          ],
        );
      },
    );
  }

  AppBar _buildChatHomepageAppBar(BuildContext context) {
    return AppBar(
      leading: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Message',
              style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    fontFamily: AppFontFamily.medium,
                  ),
            ),
          ],
        ),
      ),
      leadingWidth: 150.w,
      actions: [
        /*
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 8.w,
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: () {},
                icon: SvgPicture.asset(
                  AppAssets.translate_icon,
                  width: 24.w,
                  height: 24.h,
                ),
              ),
              IconButton(
                onPressed: () {},
                icon: SvgPicture.asset(
                  AppAssets.add_group,
                  width: 24.w,
                  height: 24.h,
                ),
              ),
            ],
          ),
        ),
        */
      ],
    );
  }
}
