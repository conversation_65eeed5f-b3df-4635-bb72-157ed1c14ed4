import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/global/global_presence/bloc/global_bloc.dart';
import 'package:multime_app/core/global/global_presence/bloc/global_state.dart';
import 'package:multime_app/core/model/chat/channel_authorization.dart';
import 'package:multime_app/core/model/chat/chat_message.dart';
import 'package:multime_app/core/model/chat/conversations.dart';
import 'package:multime_app/modules/chat/data/repository/chat_repository.dart';
import 'package:multime_app/modules/chat/data/services/pubnub_instance.dart';
import 'package:multime_app/modules/chat/data/services/pubnub_services.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/chat/presentation/bloc/conversation_list/conversation_list_bloc.dart';
import 'package:multime_app/modules/chat/presentation/widgets/messages/messages.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/chat_appbar.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/chat_input.dart';
import 'package:multime_app/modules/offer/data/repository/offer_repository.dart';
import 'package:multime_app/modules/offer/presentation/bloc/offer_bloc.dart';

class ChatPage extends StatefulWidget {
  final int partnerID;
  final String avatar;
  final String name;

  const ChatPage({
    super.key,
    required this.partnerID,
    required this.avatar,
    required this.name,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  ChannelAuthorization? _authorization;
  PubNubServices? _service;
  bool _loading = true;

  // GlobalKey
  final GlobalKey<ChatInputState> _chatInputKey = GlobalKey<ChatInputState>();
  final GlobalKey<MessagesState> _messagesKey = GlobalKey<MessagesState>();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        await Future.delayed(Duration(milliseconds: 200));
        if (mounted) {
          _initializeChat();
        }
      },
    );
  }

  Future<void> _initializeChat() async {
    try {
      final repository = getIt<ChatRepository>();
      final authorization = await repository.createChannel(widget.partnerID);

      final existingToken = gs.channelTokens[authorization.channelName];
      String token;

      if (existingToken != null) {
        token = existingToken;
      } else {
        final authorization = await repository.createChannel(widget.partnerID);
        token = authorization.token;

        await gs.saveChannelToken(
          authorization.channelName,
          token,
        );
      }

      final pubnub = PubNubInstance.create(token);
      final service = PubNubServices(pubnub: pubnub);

      setState(() {
        _authorization = authorization;
        _service = service;
        _loading = false;
      });
    } catch (error) {
      debugPrint('Error initializing chat: $error');
    }
  }

  Future<void> _refreshToken() async {
    try {
      final repository = getIt<ChatRepository>();
      final token = await repository.refreshToken(_authorization!.channelName);

      final pubnub = PubNubInstance.create(token);
      final service = PubNubServices(pubnub: pubnub);

      setState(() {
        _service = service;
      });
    } catch (error) {
      debugPrint('Error refreshing token: $error');
    }
  }

  String _getMessageType(ChatMessage message) {
    if (message.sticker != null && message.sticker!.isNotEmpty) {
      return 'sticker';
    } else if (message.image != null && message.image!.isNotEmpty) {
      return 'image';
    } else if (message.file != null && message.file!.isNotEmpty) {
      return 'file';
    } else if (message.offerID != null && message.offerID != 0) {
      return 'offer';
    } else {
      return 'text';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ChatBloc(
            service: _service!,
            repository: getIt<ChatRepository>(),
            channelName: _authorization!.channelName,
            onExpired: _refreshToken,
            onMessageUpdate: (message) {
              final globalState = context.read<GlobalBloc>().state;
              final isOnline =
                  globalState.globalPresence[widget.partnerID] ?? false;

              final conversation = Conversations(
                channelName: _authorization!.channelName,
                userID: widget.partnerID,
                userName: widget.name,
                avatar: widget.avatar,
                message: message.message,
                messageType: _getMessageType(message),
                timetoken: message.timetoken,
                senderID: message.uuid,
                isOnline: isOnline,
                unreadCount: 0,
              );

              try {
                context.read<ConversationListBloc>().add(
                      UpdateConversations(conversation),
                    );
              } catch (error) {
                debugPrint('Error updating conversation: $error');
              }
            },
          ),
        ),
        BlocProvider(
          create: (context) => OfferBloc(
            getIt<OfferRepository>(),
          ),
        ),
      ],
      child: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          return Scaffold(
            appBar: PreferredSize(
              preferredSize: const Size.fromHeight(kToolbarHeight),
              child: BlocBuilder<GlobalBloc, GlobalState>(
                builder: (context, state) {
                  final globalState = context.read<GlobalBloc>().state;
                  final isOnline =
                      globalState.globalPresence[widget.partnerID] ?? false;

                  return ChatAppBar(
                    avatar: widget.avatar,
                    name: widget.name,
                    isOnline: isOnline,
                  );
                },
              ),
            ),
            body: SafeArea(
              child: Column(
                children: [
                  Expanded(
                    child: Messages(
                      key: _messagesKey,
                      channelName: _authorization!.channelName,
                      name: widget.name,
                      avatar: widget.avatar,
                      onReply: (isReplying, parentMessage) {
                        _chatInputKey.currentState?.setReplyState(
                          isReplying,
                          parentMessage,
                        );
                      },
                      offerBloc: context.read<OfferBloc>(),
                      chatBloc: context.read<ChatBloc>(),
                    ),
                  ),
                  ChatInput(
                    key: _chatInputKey,
                    onSendMessage: (text) {
                      context.read<ChatBloc>().add(
                            SendMessage(message: text),
                          );
                    },
                    onSendReply: (text, parentMessage) {
                      context.read<ChatBloc>().add(
                            SendReply(
                              message: text,
                              parentMessage: parentMessage,
                            ),
                          );
                    },
                    onReplySent: () {
                      _messagesKey.currentState?.clearReplyState();
                    },
                    onMessageSent: () {
                      Future.delayed(const Duration(milliseconds: 100), () {
                        _messagesKey.currentState?.scrollToBottom();
                      });
                    },
                    partnerID: widget.partnerID,
                    channelName: _authorization!.channelName,
                    chatBloc: context.read<ChatBloc>(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
