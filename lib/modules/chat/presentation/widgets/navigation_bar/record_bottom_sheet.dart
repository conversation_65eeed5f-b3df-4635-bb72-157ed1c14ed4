import 'dart:convert';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/chat/presentation/widgets/custom_message_dialog.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/language_picker.dart';
import 'package:multime_app/shared/widgets/jumping_dots.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';

class RecordBottomSheet extends StatefulWidget {
  final ChatBloc bloc;

  const RecordBottomSheet({
    super.key,
    required this.bloc,
  });

  @override
  State<RecordBottomSheet> createState() => _RecordBottomSheetState();
}

class _RecordBottomSheetState extends State<RecordBottomSheet> {
  // AudioRecorder instance
  final _record = AudioRecorder();
  bool _recording = false;
  String? _recordPath;

  // PlayerController instance
  late final PlayerController _playerController;

  // List of source/target languages from JSON
  List<dynamic> languages = [];
  Map<String, dynamic>? _selectedSourceLanguage;
  Map<String, dynamic>? _selectedTargetLanguage;

  // TextEditingController
  final TextEditingController _originalController = TextEditingController();
  final TextEditingController _translatedController = TextEditingController();

  // Pre-condition checks
  bool get _isTranslated =>
      _selectedTargetLanguage != null &&
      _selectedSourceLanguage != null &&
      widget.bloc.state.originalText?.isNotEmpty == true &&
      widget.bloc.state.translatedText?.isNotEmpty == true;

  bool get _isTranslateViable =>
      _isRecorded &&
      _selectedSourceLanguage != null &&
      _selectedTargetLanguage != null &&
      _selectedSourceLanguage!['language_code'] !=
          _selectedTargetLanguage!['language_code'];

  bool get _isRecorded =>
      _recordPath != null && _recordPath!.isNotEmpty && _recording == false;

  @override
  void initState() {
    super.initState();

    _playerController = PlayerController();
    rootBundle.loadString('assets/language/languages.json').then(
      (json) {
        setState(() {
          languages = jsonDecode(json);
        });
      },
    );
  }

  @override
  void dispose() {
    _originalController.dispose();
    _translatedController.dispose();

    _record
      ..stop()
      ..cancel()
      ..dispose();
    _playerController
      ..release()
      ..stopAllPlayers()
      ..dispose();

    widget.bloc
      ..add(
        ResetTranlation(),
      )
      ..add(
        CancelRequest(),
      );

    super.dispose();
  }

  void _onRecord(BuildContext context) async {
    if (!await Permission.microphone.request().isGranted) {
      showDialog(
        context: context,
        builder: (_) {
          return CustomMessageDialog(
            title: 'Microphone Permission',
            subtitle:
                'To use voice translation, MultiMe needs access to your microphone. Please enable it in your device settings.',
            confirmText: 'Turn on',
            onConfirm: () {
              openAppSettings();
              context.pop();
            },
          );
        },
      );
      return;
    }

    if (!_recording) {
      setState(() {
        _recording = true;
      });

      final tempDirectory = await getTemporaryDirectory();
      _recordPath =
          '${tempDirectory.path}/multime_voice_${DateTime.now().millisecondsSinceEpoch}.wav';

      debugPrint('🔴 Starting record at path: $_recordPath');
      await _record.start(
        const RecordConfig(
          encoder: AudioEncoder.wav,
        ),
        path: _recordPath!,
      );
    } else {
      setState(() {
        _recording = false;
      });

      debugPrint('🔴 Stopping record at path: $_recordPath');
      await _record.stop();
      await _playerController.preparePlayer(
        path: _recordPath!,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ChatBloc, ChatState>(
      bloc: widget.bloc,
      listener: (context, state) async {
        if (state.translationError != null &&
            state.translationError!.isNotEmpty) {
          await showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) => CustomMessageDialog(
              title: 'Voice Translation',
              subtitle: state.translationError!,
              confirmText: 'Got it!',
              onConfirm: () {
                widget.bloc.add(
                  ResetTranlation(),
                );

                context.pop();
              },
            ),
          );

          widget.bloc.add(
            ResetTranlation(),
          );
        }

        // Update UIs when translation is done
        if (state.translatedText != null && state.translatedText!.isNotEmpty) {
          setState(() {});
        }
      },
      child: Wrap(
        children: [
          Padding(
            padding: PaddingConstants.padAll16,
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: InkWell(
                    child: Container(
                      width: 28.w,
                      height: 28.h,
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: Theme.of(context).textPrimary(context),
                      ),
                    ),
                    onTap: () {
                      widget.bloc.add(
                        ResetTranlation(),
                      );

                      context.pop();
                    },
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Auto language conversion',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    Gap(24.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () async {
                              final language = await showModalBottomSheet<
                                  Map<String, dynamic>>(
                                context: context,
                                backgroundColor:
                                    Theme.of(context).whitePrimary(context),
                                builder: (_) => LanguagePicker(
                                  languages: languages,
                                ),
                              );

                              if (language != null) {
                                setState(() {
                                  _selectedSourceLanguage = language;
                                });
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).whitePrimary(context),
                                borderRadius: BorderRadius.circular(24.r),
                                border: Border.all(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 8.h,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  if (_selectedSourceLanguage != null)
                                    Image.network(
                                      _selectedSourceLanguage!['flag'],
                                      width: 24.w,
                                      height: 24.h,
                                    )
                                  else
                                    SvgPicture.asset(
                                      AppAssets.translateSvg,
                                      width: 24.w,
                                      height: 24.h,
                                    ),
                                  Gap(8.w),
                                  Flexible(
                                    child: Text(
                                      _selectedSourceLanguage?[
                                              'language_name'] ??
                                          'From',
                                      style:
                                          Theme.of(context).textTheme.bodyLarge,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Gap(10.w),
                        SvgPicture.asset(
                          AppAssets.convert,
                        ),
                        Gap(10.w),
                        Expanded(
                          child: GestureDetector(
                            onTap: () async {
                              final language = await showModalBottomSheet<
                                  Map<String, dynamic>>(
                                context: context,
                                backgroundColor:
                                    Theme.of(context).whitePrimary(context),
                                builder: (_) => LanguagePicker(
                                  languages: languages,
                                ),
                              );

                              if (language != null) {
                                setState(() {
                                  _selectedTargetLanguage = language;
                                });
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).whitePrimary(context),
                                borderRadius: BorderRadius.circular(24.r),
                                border: Border.all(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 8.h,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  if (_selectedTargetLanguage != null)
                                    Image.network(
                                      _selectedTargetLanguage!['flag'],
                                      width: 32.w,
                                      height: 24.h,
                                    )
                                  else
                                    SvgPicture.asset(
                                      AppAssets.translateSvg,
                                      width: 24.w,
                                      height: 24.h,
                                    ),
                                  Gap(8.w),
                                  Flexible(
                                    child: Text(
                                      _selectedTargetLanguage?[
                                              'language_name'] ??
                                          'To',
                                      style:
                                          Theme.of(context).textTheme.bodyLarge,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Gap(24.h),
                    BlocBuilder<ChatBloc, ChatState>(
                      bloc: widget.bloc,
                      builder: (context, state) {
                        _originalController.text = state.originalText ?? '';

                        return SizedBox(
                          height: 100.h,
                          child: TextField(
                            controller: _originalController,
                            enabled: true,
                            readOnly: true,
                            keyboardType: TextInputType.multiline,
                            maxLines: 100,
                            decoration: InputDecoration(
                              hintText: widget.bloc.state.isTranslating
                                  ? 'Translating, please wait...'
                                  : 'Press play and say something',
                              hintStyle: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                    fontFamily: AppFontFamily.regular,
                                  ),
                              filled: true,
                              fillColor:
                                  Theme.of(context).whitePrimary(context),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                                borderSide: BorderSide(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                                borderSide: BorderSide(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                                borderSide: BorderSide(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    Gap(24.h),
                    BlocBuilder<ChatBloc, ChatState>(
                      bloc: widget.bloc,
                      builder: (context, state) {
                        return state.isTranslating
                            ? SizedBox(
                                height: 24.h,
                                child: JumpingDots(
                                  color:
                                      Theme.of(context).greyScale700(context),
                                ),
                              )
                            : SvgPicture.asset(
                                AppAssets.convert,
                              );
                      },
                    ),
                    Gap(24.h),
                    BlocBuilder<ChatBloc, ChatState>(
                      bloc: widget.bloc,
                      builder: (context, state) {
                        _translatedController.text = state.translatedText ?? '';

                        return SizedBox(
                          height: 100.h,
                          child: TextField(
                            controller: _translatedController,
                            enabled: true,
                            readOnly: true,
                            keyboardType: TextInputType.multiline,
                            maxLines: 100,
                            decoration: InputDecoration(
                              hintText: widget.bloc.state.isTranslating
                                  ? 'Translating, please wait...'
                                  : 'Traduction',
                              hintStyle: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                    fontFamily: AppFontFamily.regular,
                                  ),
                              filled: true,
                              fillColor:
                                  Theme.of(context).whitePrimary(context),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                                borderSide: BorderSide(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                                borderSide: BorderSide(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                                borderSide: BorderSide(
                                  color:
                                      Theme.of(context).textSecondary(context),
                                  width: 0.5,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    Gap(18.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (_isRecorded)
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _recordPath = '';
                              });

                              widget.bloc.add(
                                ResetTranlation(),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).greyScale700(context),
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 8.h,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  'Delete',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(
                                        color: Theme.of(context)
                                            .whitePrimary(context),
                                      ),
                                ),
                                Gap(8.h),
                                Icon(
                                  Icons.delete_forever,
                                  color:
                                      Theme.of(context).whitePrimary(context),
                                )
                              ],
                            ),
                          ),
                        if (!_isRecorded) const Spacer(),
                        Row(
                          children: [
                            ElevatedButton(
                              onPressed: _isTranslateViable
                                  ? () async {
                                      if (_recordPath == null) {
                                        debugPrint(
                                            '🔴 No recording found to translate');
                                        return;
                                      }

                                      widget.bloc.add(
                                        TranslateSpeechToText(
                                          filePath: _recordPath!,
                                          fileName:
                                              _recordPath!.split('/').last,
                                          sourceLang: _selectedSourceLanguage?[
                                                  'language_code'] ??
                                              'vi',
                                          targetLang: _selectedTargetLanguage?[
                                                  'language_code'] ??
                                              'en',
                                        ),
                                      );
                                    }
                                  : null,
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 12.w,
                                  vertical: 8.h,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                              ).copyWith(
                                backgroundColor: WidgetStateProperty.all(
                                  _isTranslateViable
                                      ? Theme.of(context).greyScale700(context)
                                      : Theme.of(context)
                                          .greyScale700(context)
                                          .withOpacity(0.5),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    'Translate',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall!
                                        .copyWith(
                                          color: Theme.of(context)
                                              .whitePrimary(context),
                                        ),
                                  ),
                                  Gap(8.h),
                                  Icon(
                                    Icons.g_translate,
                                    color:
                                        Theme.of(context).whitePrimary(context),
                                  )
                                ],
                              ),
                            ),
                            Gap(10.w),
                            ElevatedButton(
                              onPressed: _isTranslated
                                  ? () {
                                      widget.bloc.add(
                                        SendTranslation(
                                          message: _originalController.text,
                                          translated:
                                              _translatedController.text,
                                        ),
                                      );

                                      widget.bloc.add(
                                        ResetTranlation(),
                                      );

                                      context.pop();
                                    }
                                  : null,
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 12.w,
                                  vertical: 8.h,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                              ).copyWith(
                                backgroundColor: WidgetStateProperty.all(
                                  _isTranslated
                                      ? Theme.of(context).greyScale700(context)
                                      : Theme.of(context)
                                          .greyScale700(context)
                                          .withOpacity(0.5),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Send',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall!
                                        .copyWith(
                                          color: Theme.of(context)
                                              .whitePrimary(context),
                                        ),
                                  ),
                                  Gap(8.h),
                                  Icon(
                                    Icons.send_rounded,
                                    color:
                                        Theme.of(context).whitePrimary(context),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Gap(24.h),
                    _isRecorded
                        ? Row(
                            children: [
                              IconButton(
                                icon: Icon(
                                  _playerController.playerState.isPlaying
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                  size: 48,
                                  color:
                                      Theme.of(context).greyScale700(context),
                                ),
                                visualDensity: VisualDensity.compact,
                                onPressed: () async {
                                  _playerController.setFinishMode(
                                    finishMode: FinishMode.loop,
                                  );

                                  if (_playerController.playerState.isPlaying) {
                                    await _playerController.pausePlayer();
                                  } else {
                                    await _playerController.startPlayer();
                                  }

                                  setState(() {});
                                },
                              ),
                              Gap(8.w),
                              Expanded(
                                child: AudioFileWaveforms(
                                  size: Size(
                                    double.infinity,
                                    100.h,
                                  ),
                                  playerController: _playerController,
                                  waveformType: WaveformType.fitWidth,
                                  enableSeekGesture: false,
                                  playerWaveStyle: PlayerWaveStyle(
                                    scaleFactor: 200,
                                    fixedWaveColor:
                                        Theme.of(context).greyScale500(context),
                                    liveWaveColor:
                                        Theme.of(context).primary(context),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : GestureDetector(
                            onTap: () => _onRecord(context),
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              curve: Curves.easeInOut,
                              width: 100.w,
                              height: 100.h,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Theme.of(context).whitePrimary(context),
                                border: Border.all(
                                  color:
                                      Theme.of(context).greyScale400(context),
                                  width: 6.w,
                                ),
                              ),
                              padding: _recording
                                  ? EdgeInsets.all(24.r)
                                  : EdgeInsets.all(6.r),
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                curve: Curves.easeInOut,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primary(context),
                                  borderRadius: BorderRadius.circular(
                                    _recording ? 8.r : 50.r,
                                  ),
                                ),
                              ),
                            ),
                          )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
