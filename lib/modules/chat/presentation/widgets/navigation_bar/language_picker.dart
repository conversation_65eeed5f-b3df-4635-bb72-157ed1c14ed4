import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/theme.dart';

class LanguagePicker extends StatefulWidget {
  final List<dynamic> languages;

  const LanguagePicker({
    super.key,
    required this.languages,
  });

  @override
  State<LanguagePicker> createState() => _LanguagePickerState();
}

class _LanguagePickerState extends State<LanguagePicker> {
  final TextEditingController _searchController = TextEditingController();
  late Map<String, List<dynamic>> _groupedLanguages;
  late List<String> _alphabeticalKeys;
  late List<dynamic> _featuredLanguages;

  @override
  void initState() {
    super.initState();

    _searchController.addListener(() {
      // Search functionality
      final query = _searchController.text.toLowerCase();
      final filtered = widget.languages.where((language) {
        final name = language['language_name']?.toString().toLowerCase() ?? '';
        return name.contains(query);
      }).toList();

      // Featured languages
      final featured =
          filtered.where((language) => language['featured'] == true).toList();

      // Group langugaes based on the first letter of their names in alphabetical order
      final Map<String, List<dynamic>> grouped = {};
      for (final language in filtered.where(
        (language) => language['featured'] != true,
      )) {
        final name = language['language_name'] ?? '';

        if (name.isEmpty) continue;

        final key = name[0].toUpperCase();
        grouped.putIfAbsent(key, () => []).add(language);
      }

      // Sort each group alphabetically by language name
      for (final list in grouped.values) {
        list.sort(
          (a, b) => a['language_name'].toLowerCase().compareTo(
                b['language_name'].toLowerCase(),
              ),
        );
      }

      setState(() {
        _groupedLanguages = grouped;
        _alphabeticalKeys = grouped.keys.toList()..sort();
        _featuredLanguages = featured;
      });
    });

    // Initial grouping and sorting of languages
    final featured = widget.languages
        .where((language) => language['featured'] == true)
        .toList();
    final Map<String, List<dynamic>> grouped = {};
    for (final language in widget.languages.where(
      (language) => language['featured'] != true,
    )) {
      final name = language['language_name'] ?? '';

      if (name.isEmpty) continue;

      final key = name[0].toUpperCase();
      grouped.putIfAbsent(key, () => []).add(language);
    }
    for (final list in grouped.values) {
      list.sort(
        (a, b) => a['language_name'].toLowerCase().compareTo(
              b['language_name'].toLowerCase(),
            ),
      );
    }
    _groupedLanguages = grouped;
    _alphabeticalKeys = grouped.keys.toList()..sort();
    _featuredLanguages = featured;
  }

  @override
  void dispose() {
    _searchController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          Padding(
            padding: PaddingConstants.padAll16,
            child: Align(
              alignment: Alignment.topRight,
              child: InkWell(
                child: Icon(
                  Icons.close,
                  size: 24,
                  color: Theme.of(context).textPrimary(context),
                ),
                onTap: () => context.pop(),
              ),
            ),
          ),
          Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
                child: Text(
                  'Select a language',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  bottom: 8.h,
                  left: 16.w,
                  right: 16.w,
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search for a language...',
                    hintStyle: Theme.of(context).textTheme.bodyMedium,
                    prefixIcon: Icon(
                      Icons.search,
                      color: Theme.of(context).textPrimary(context),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: Theme.of(context).greyScale500(context),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: Theme.of(context).greyScale500(context),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      borderSide: BorderSide(
                        color: Theme.of(context).greyScale500(context),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.only(
                    bottom: 16.h,
                    left: 16.w,
                    right: 16.w,
                  ),
                  children: [
                    // Featured languages (ListView)
                    if (_featuredLanguages.isNotEmpty) ...[
                      Padding(
                        padding: EdgeInsets.only(
                          top: 8.h,
                          bottom: 8.h,
                        ),
                        child: Text(
                          'SUGGESTED LANGUAGES',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                      ..._featuredLanguages.map((language) {
                        return ListTile(
                          leading: Image.network(
                            language['flag'],
                            width: 32.w,
                            height: 32.h,
                          ),
                          title: Text(
                            language['language_name'],
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                          onTap: () => context.pop(language),
                        );
                      }),
                    ],

                    // Alphabetical sorted languages (ListView)
                    ..._alphabeticalKeys.map((key) {
                      final items = _groupedLanguages[key]!;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Gap(16.h),
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                              vertical: 8.h,
                              horizontal: 12.w,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).lightGrey(context),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8.r),
                                topRight: Radius.circular(8.r),
                              ),
                            ),
                            child: Text(
                              key,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                          Gap(8.h),
                          ...items.map((language) {
                            return ListTile(
                              leading: Image.network(
                                language['flag'],
                                width: 32.w,
                                height: 32.h,
                              ),
                              title: Text(
                                language['language_name'],
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                              onTap: () => context.pop(language),
                            );
                          }).toList(),
                        ],
                      );
                    }).toList(),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
