import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/theme.dart';

class StickerPicker extends StatelessWidget {
  final Function(String) onSelected;

  const StickerPicker({
    super.key,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final List<String> stickers = List.generate(
      10,
      (index) => 'sticker_${index + 1}',
    );

    return Container(
      height: 240.h,
      child: Column(
        children: [
          const Divider(),
          Expanded(
            child: GridView.builder(
              padding: PaddingConstants.padSymV12,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                mainAxisSpacing: 12.h,
                crossAxisSpacing: 12.w,
              ),
              itemCount: stickers.length,
              itemBuilder: (context, index) {
                final sticker = stickers[index];

                return InkWell(
                  onTap: () => onSelected(sticker),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: Image.asset(
                      'assets/sticker/$sticker.png',
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, trace) {
                        return Container(
                          color: Theme.of(context).lightGrey(context),
                          child: Icon(
                            Icons.image_not_supported,
                            color: Theme.of(context).greyScale500(context),
                            size: 32,
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
