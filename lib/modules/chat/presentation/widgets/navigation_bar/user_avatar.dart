// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';

class UserAvatar extends StatefulWidget {
  final String? username;
  final int size;
  final String imageURL;
  final bool? isOnline;

  const UserAvatar({
    super.key,
    this.username,
    required this.size,
    required this.imageURL,
    this.isOnline,
  });

  @override
  State<UserAvatar> createState() => _UserAvatarState();
}

class _UserAvatarState extends State<UserAvatar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 160),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    if (widget.isOnline == true) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(UserAvatar previous) {
    super.didUpdateWidget(previous);

    if (previous.isOnline != widget.isOnline) {
      if (widget.isOnline == true) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();

    super.dispose();
  }

  String _getUsernameInitial(String name) {
    if (name.isEmpty) return 'U';

    final words = name.trim().split(RegExp(r'\s+'));
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return (words[0].substring(0, 1) + words[1].substring(0, 1))
          .toUpperCase();
    }
  }

  Color _getBackgroundColor(
    BuildContext context,
    String name,
  ) {
    final colors = [
      Theme.of(context).primary(context),
      Theme.of(context).secondary(context),
      Theme.of(context).successBase(context),
      Theme.of(context).warningBase(context),
      Theme.of(context).errorBase(context),
      Theme.of(context).attentionBase(context),
      Theme.of(context).informationBase(context),
      Theme.of(context).focusHighlightBase(context),
      Theme.of(context).disabledBase(context),
    ];
    final hash = name.hashCode;
    return colors[hash.abs() % colors.length];
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          width: widget.size.h,
          height: widget.size.h,
          child: CircleAvatar(
            radius: (widget.size / 2).r,
            backgroundColor: widget.imageURL.isNotEmpty
                ? null
                : _getBackgroundColor(
                    context,
                    widget.username!,
                  ),
            backgroundImage: widget.imageURL.isNotEmpty
                ? NetworkImage(widget.imageURL)
                : null,
            child: widget.imageURL.isNotEmpty
                ? null
                : Text(
                    _getUsernameInitial(widget.username ?? ''),
                    style: TextStyle(
                      color: Theme.of(context).whitePrimary(context),
                      fontFamily: AppFontFamily.semiBold,
                      fontSize: (widget.size * 0.4).sp,
                    ),
                  ),
          ),
        ),
        if (widget.isOnline != null && widget.isOnline == true)
          Positioned(
            right: -2,
            bottom: -2,
            child: FadeTransition(
              opacity: _animation,
              child: Container(
                width: widget.size.h / 3,
                height: widget.size.h / 3,
                decoration: BoxDecoration(
                  color: Theme.of(context).successBase(context),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).whitePrimary(context),
                    width: 2,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
