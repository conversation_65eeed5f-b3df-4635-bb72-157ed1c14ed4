import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/user_avatar.dart';

class ChatAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String avatar;
  final String name;
  final bool isOnline;

  const ChatAppBar({
    super.key,
    required this.avatar,
    required this.name,
    required this.isOnline,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: false,
      leading: IconButton(
        icon: SvgPicture.asset(
          AppAssets.arrowLeftSvg,
          width: 24.w,
          height: 24.h,
        ),
        onPressed: () => context.pop(),
      ),
      title: InkWell(
        onTap: () => context.push(
          RouteName.chatInfoPage,
          extra: {
            'avatar': avatar,
            'name': name,
          },
        ),
        child: Transform.translate(
          offset: const Offset(-20, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              UserAvatar(
                username: name,
                size: 32,
                imageURL: avatar,
                isOnline: isOnline,
              ),
              Gap(10.w),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            name,
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeMedium,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Gap(2.w),
                        SvgPicture.asset(
                          AppAssets.premium_icon,
                          width: 30.w,
                          height: 30.h,
                        ),
                      ],
                    ),
                    Transform.translate(
                      offset: const Offset(0, -4),
                      child: Text(
                        isOnline ? 'Online' : 'Offline',
                        style: Theme.of(context)
                            .textTheme
                            .lightBodySmallMedium
                            .copyWith(
                              color: Theme.of(context).informationBase(context),
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        /*
        _buildAction(
          AppAssets.call_icon,
          () {},
        ),
        _buildAction(
          AppAssets.translate_icon,
          () {},
        ),
        */
        _buildAction(
          AppAssets.other_icon,
          () => context.push(
            RouteName.chatInfoPage,
            extra: {
              'avatar': avatar,
              'name': name,
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAction(
    String icon,
    VoidCallback onPressed,
  ) {
    return IconButton(
      icon: SvgPicture.asset(
        icon,
        width: 24.w,
        height: 24.h,
      ),
      onPressed: onPressed,
      visualDensity: VisualDensity.compact,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
