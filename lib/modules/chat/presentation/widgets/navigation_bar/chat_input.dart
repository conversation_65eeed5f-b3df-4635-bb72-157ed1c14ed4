import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/model/chat/chat_message.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/record_bottom_sheet.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/sticker_picker.dart';

class ChatInput extends StatefulWidget {
  final Function(String) onSendMessage;
  final Function(String, ChatMessage)? onSendReply;
  final VoidCallback? onReplySent;
  final VoidCallback? onMessageSent;
  final int partnerID;
  final String channelName;
  final ChatBloc chatBloc;

  ChatInput({
    super.key,
    required this.partnerID,
    required this.onSendMessage,
    this.onSendReply,
    this.onReplySent,
    this.onMessageSent,
    required this.channelName,
    required this.chatBloc,
  });

  @override
  State<ChatInput> createState() => ChatInputState();

  static ChatInputState? of(BuildContext context) {
    return context.findAncestorStateOfType<ChatInputState>();
  }
}

class ChatInputState extends State<ChatInput> {
  // [VARIABLES] Use for Text Input
  final TextEditingController _messageController = TextEditingController();
  final int _maxLength = 2500;
  bool _isTyping = false;

  // [VARIABLES] StickerPicker
  bool _showStickerPicker = false;

  // [VARIABLES] Reply functionality
  bool _isReplying = false;
  ChatMessage? _parentMessage;

  // [VARIABLES] Current user role
  final role = getIt<GlobalStorage>().userRole;

  @override
  void initState() {
    super.initState();

    _messageController.addListener(_typing);
  }

  @override
  void dispose() {
    _messageController.removeListener(_typing);
    _messageController.dispose();

    super.dispose();
  }

  // [FUNCTIONALITY] Detect when user is typing
  void _typing() {
    setState(() {
      _isTyping = _messageController.text.trim().isNotEmpty;
    });
  }

  // [FUNCTIONALITY] Send message
  void _sendMessage() {
    if (_isTyping) {
      final message = _messageController.text.trim();

      if (_isReplying && _parentMessage != null) {
        widget.onSendReply?.call(message, _parentMessage!);
        _clearReplyState();
        widget.onReplySent?.call();
      } else {
        widget.onSendMessage(message);
      }

      _messageController.clear();
      setState(() {
        _isTyping = false;
      });

      widget.onMessageSent?.call();
    }
  }

  // [FUNCTIONALITY] Set replying message state
  void setReplyState(
    bool isReplying,
    ChatMessage? parentMessage,
  ) {
    setState(() {
      _isReplying = isReplying;
      _parentMessage = parentMessage;
    });
  }

  // [FUNCTIONALITY] Clear replying message state
  void _clearReplyState() {
    setState(() {
      _isReplying = false;
      _parentMessage = null;
    });
  }

  // [FUNCTIONALITY] Toggle on/off StickerPicker
  void _toggleStickerPicker() {
    setState(() {
      _showStickerPicker = !_showStickerPicker;
    });
  }

  // [FUNCTIONALITY] Send sticker
  void _onStickerSelected(String sticker) {
    context.read<ChatBloc>().add(
          SendSticker(sticker),
        );
    print('Sticker selected: $sticker');

    setState(() {
      _showStickerPicker = false;
    });

    widget.onMessageSent?.call();
  }

  // [FUNCTIONALITY] Send image
  void _onSendPicture(_onSendMessage) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      File imageFile = File(image.path);
      context.read<ChatBloc>().add(
            SendImage(imageFile),
          );
    }

    widget.onMessageSent?.call();
  }

  // [FUNCTIONALITY] Record, speech-to-text then translate
  void _onRecord(BuildContext context) async {
    final bloc = context.read<ChatBloc>();

    await showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).lightGrey(context),
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
      builder: (context) {
        return RecordBottomSheet(
          bloc: bloc,
        );
      },
    );
  }

  // [FUNCTIONALITY] Send file
  void _onSendFile(_onSendMessage) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
    );

    if (result != null && result.files.single.path != null) {
      File file = File(result.files.single.path!);
      final fileSize = await file.length();

      if (fileSize > 5 * 1024 * 1024) {
        if (!context.mounted) return;

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(
              "File size limit exceeded",
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            content: Text(
              "Maximum file size allowed is 5MB.",
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () => context.pop(),
                child: Text(
                  "OK",
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        color: Theme.of(context).primary(context),
                      ),
                ),
              ),
            ],
          ),
        );
        return;
      }

      context.read<ChatBloc>().add(
            SendFile(file),
          );

      widget.onMessageSent?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: PaddingConstants.padAll16.copyWith(
        top: 8.h,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextFormField(
            controller: _messageController,
            maxLength: _maxLength,
            decoration: InputDecoration(
              counterText: "",
              hintText: "Type a message",
              hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: Theme.of(context).greyScale600(context),
                    fontFamily: AppFontFamily.regular,
                  ),
              filled: true,
              fillColor: Theme.of(context).greyScale50(context),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: BorderSide.none,
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 8.h,
              ),
              suffixIcon: _isTyping
                  ? GestureDetector(
                      onTap: _sendMessage,
                      child: Padding(
                        padding: PaddingConstants.padAll08,
                        child: SvgPicture.asset(
                          AppAssets.sendMessageSvg,
                          fit: BoxFit.scaleDown,
                          width: 24.w,
                          height: 24.h,
                        ),
                      ),
                    )
                  : null,
            ),
          ),
          Gap(12.h),
          Row(
            children: [
              _buildIconButton(
                AppAssets.voice_chat_icon,
                () => _onRecord(context),
              ),
              _buildIconButton(
                AppAssets.emoji_send,
                _toggleStickerPicker,
              ),
              _buildIconButton(
                AppAssets.gallerySvg,
                () => _onSendPicture(widget.onSendMessage),
              ),
              _buildIconButton(
                AppAssets.linkSvg,
                () => _onSendFile(widget.onSendMessage),
              ),
              const Spacer(),
              if (role == 'Seller mobile' || role == 'Seller')
                ElevatedButton(
                  onPressed: () => context.push(
                    RouteName.chatOfferPage,
                    extra: {
                      'partner_id': widget.partnerID,
                      'channel_name': widget.channelName,
                      'chat_bloc': widget.chatBloc,
                    },
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).whitePrimary(context),
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 8.h,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                      side: BorderSide(
                        color: Theme.of(context).greyScale700(context),
                        width: 1,
                      ),
                    ),
                    overlayColor: Theme.of(context)
                        .blackPrimary(context)
                        .withOpacity(0.005),
                  ).copyWith(
                    elevation: WidgetStateProperty.all(0),
                  ),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        AppAssets.briefcaseSvg,
                        width: 20.w,
                        height: 20.h,
                      ),
                      Gap(8.w),
                      Text(
                        "Create an offer",
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontFamily: AppFontFamily.regular,
                            ),
                      ),
                    ],
                  ),
                )
            ],
          ),
          if (_showStickerPicker)
            StickerPicker(
              onSelected: _onStickerSelected,
            ),
        ],
      ),
    );
  }

  Widget _buildIconButton(
    String icon,
    VoidCallback onPressed,
  ) {
    return IconButton(
      icon: SvgPicture.asset(
        icon,
        width: 24.w,
        height: 24.h,
      ),
      onPressed: onPressed,
      visualDensity: VisualDensity.compact,
    );
  }
}
