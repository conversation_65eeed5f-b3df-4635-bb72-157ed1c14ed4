import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:shimmer/shimmer.dart';

class MessageShimmer extends StatelessWidget {
  final bool me;

  const MessageShimmer({
    super.key,
    this.me = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: 4.h,
      ),
      child: Row(
        mainAxisAlignment: me ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!me)
            Shimmer.fromColors(
              baseColor: Theme.of(context).greyScale300(context),
              highlightColor: Theme.of(context).greyScale100(context),
              child: CircleAvatar(
                radius: 12,
                backgroundColor: Theme.of(context).greyScale300(context),
              ),
            )
          else
            Gap(24.w),
          Gap(8.w),
          Shimmer.fromColors(
            baseColor: Theme.of(context).greyScale300(context),
            highlightColor: Theme.of(context).greyScale100(context),
            child: Container(
              height: 40.h,
              width: 1.sw * 3 / 5,
              decoration: BoxDecoration(
                color: Theme.of(context).greyScale300(context),
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
