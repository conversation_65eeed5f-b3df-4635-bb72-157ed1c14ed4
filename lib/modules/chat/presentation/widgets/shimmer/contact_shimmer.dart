import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:shimmer/shimmer.dart';

class ContactShimmer extends StatelessWidget {
  const ContactShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Theme.of(context).greyScale300(context),
      highlightColor: Theme.of(context).greyScale100(context),
      child: SizedBox(
        width: 74.w,
        child: Column(
          children: [
            Container(
              width: 64.w,
              height: 64.h,
              decoration: BoxDecoration(
                color: Theme.of(context).whitePrimary(context),
                shape: BoxShape.circle,
              ),
            ),
            Gap(8.h),
            Container(
              width: 50.w,
              height: 12.h,
              decoration: BoxDecoration(
                color: Theme.of(context).whitePrimary(context),
                borderRadius: BorderRadius.circular(6.r),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
