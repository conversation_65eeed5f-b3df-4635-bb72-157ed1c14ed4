import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/chat/chat_message.dart';
import 'package:multime_app/core/model/chat/mark_read_response.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/date_time_utils.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/chat/presentation/widgets/custom_message_dialog.dart';
import 'package:multime_app/modules/chat/presentation/widgets/messages/message_action_menu.dart';
import 'package:multime_app/modules/chat/presentation/widgets/messages/message_left.dart';
import 'package:multime_app/modules/chat/presentation/widgets/messages/message_right.dart';
import 'package:multime_app/modules/chat/presentation/widgets/messages/read_receipt.dart';
import 'package:multime_app/modules/chat/presentation/widgets/messages/timestamp_separator.dart';
import 'package:multime_app/modules/chat/presentation/widgets/shimmer/message_shimmer.dart';
import 'package:multime_app/modules/offer/presentation/bloc/offer_bloc.dart';
import 'package:multime_app/modules/offer/presentation/widgets/offer_message.dart';
import 'package:pubnub/pubnub.dart';

class Messages extends StatefulWidget {
  final String channelName;
  final String name;
  final String avatar;
  final Function(bool, ChatMessage?)? onReply;
  final OfferBloc? offerBloc;
  final ChatBloc? chatBloc;

  const Messages({
    super.key,
    required this.channelName,
    required this.name,
    required this.avatar,
    this.onReply,
    this.offerBloc,
    this.chatBloc,
  });

  @override
  State<Messages> createState() => MessagesState();
}

class MessagesState extends State<Messages> {
  // [VARIABLES] Tapping functionality
  final Set<int> _tapped = {};

  // [VARIABLES] Progress tracking for downloading files
  String? _downloadingFile;

  // [VARIABLES] Action menu entry for long-press
  OverlayEntry? _actionMenuEntry;

  // [VARIABLES] ScrollController
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasReachedEnd = false;

  // [VARIABLES] Replying messages functionality
  bool _isReplying = false;
  String? _replyingTo;
  String? _replyingMessage;

  // [VARIABLES] Handle marking messages as read
  bool _isScrollBottom = false;
  static const num _threshold = 100;
  bool _hasInitialized = false;
  late BigInt _lastTimetoken;

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();

    if (_actionMenuEntry?.mounted == true) {
      _actionMenuEntry!.remove();
      _actionMenuEntry = null;
    }

    super.dispose();
  }

  // [FUNCTIONALITY 1] Listener for scroll events to trigger loading more messages when reaching the end of the list
  // [FUNCTIONALITY 2] Handle marking messages as read when scrolling to the bottom
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100) {
      _loadMoreMessages();
    }

    final isScrollBottom = _scrollController.position.pixels <= _threshold;
    if (_isScrollBottom != isScrollBottom) {
      _isScrollBottom = isScrollBottom;

      if (isScrollBottom && _hasInitialized) {
        final state = context.read<ChatBloc>().state;

        if (state.messages.isNotEmpty) {
          final latest = state.messages.first;

          if (!latest.isPending) {
            context.read<ChatBloc>().add(
                  DebouncingMarkRead(
                    latest.timetoken,
                  ),
                );
          }
        }
      }
    }
  }

  // [FUNCTIONALTIY] Load more messages when the user scrolls to the end of the list
  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore || _hasReachedEnd) return;

    setState(() {
      _isLoadingMore = true;
    });
    context.read<ChatBloc>().add(
          LoadMoreMessages(),
        );
  }

  // [FUNCTIONALITY] Clear the replying message state
  void clearReplyState() {
    setState(() {
      _isReplying = false;
      _replyingTo = null;
      _replyingMessage = null;
    });

    widget.onReply?.call(
      _isReplying,
      null,
    );
  }

  // [FUNCTIONALITY] Scroll to the bottom of the messages list
  void scrollToBottom({bool animate = true}) {
    if (_scrollController.hasClients) {
      if (animate) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 600),
          curve: Curves.easeInOut,
        );
      } else {
        _scrollController.jumpTo(0);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ChatBloc, ChatState>(
      listenWhen: (previous, current) {
        // [FUNCTIONALITY] Handle marking messages as read when there is no scroll
        if (previous.isLoading && !current.isLoading && !_hasInitialized) {
          _hasInitialized = true;
          if (current.messages.isNotEmpty) {
            _lastTimetoken = BigInt.parse(
              current.messages.first.timetoken,
            );
          }
        }

        if (current.messages.isNotEmpty) {
          if (_hasInitialized &&
              BigInt.parse(current.messages.first.timetoken) !=
                  _lastTimetoken) {
            WidgetsBinding.instance.addPostFrameCallback(
              (_) {
                if (!mounted) return;

                final scrollable = _scrollController.hasClients &&
                    _scrollController.position.maxScrollExtent > 0;
                final isBottom = !scrollable ||
                    _scrollController.position.pixels <= _threshold;
                if (isBottom) {
                  context.read<ChatBloc>().add(
                        DebouncingMarkRead(
                          current.messages.first.timetoken,
                        ),
                      );
                }
              },
            );

            _lastTimetoken = BigInt.parse(
              current.messages.first.timetoken,
            );
          }
        }

        // Update 'isLoadingMore' and 'hasReachedEnd' states
        if (previous.isLoadingMore != current.isLoadingMore) {
          setState(() {
            _isLoadingMore = current.isLoadingMore;
          });
        }

        if (previous.hasReachedEnd != current.hasReachedEnd) {
          setState(() {
            _hasReachedEnd = current.hasReachedEnd;
          });
        }

        // Handle read receipts
        if (previous.markReadResponse != current.markReadResponse) {
          return false;
        }

        // Handle snackbar messages
        return previous.snackbarMessage != current.snackbarMessage &&
            current.snackbarMessage != null;
      },
      listener: (context, state) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              state.snackbarMessage!,
            ),
          ),
        );
      },
      child: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          // ✅ Situation when the messages are loading
          if (state.isLoading) {
            return ListView.builder(
              reverse: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 12,
              padding: PaddingConstants.padAll12,
              itemBuilder: (context, index) {
                final me = index % 2 == 0;
                return MessageShimmer(
                  me: me,
                );
              },
            );
          }

          // ✅ Situation when there is an error
          if (state.error != null) {
            debugPrint("Chat error: ${state.error}");
            return Padding(
              padding: PaddingConstants.padAll16,
              child: Center(
                child: Text("Error: ${state.error}"),
              ),
            );
          }

          // ✅ Situation when there are no messages
          final messages = state.messages;
          if (messages.isEmpty) {
            final List<String> stickers = [
              'sticker_1',
              'sticker_4',
              'sticker_8',
              'sticker_10',
            ];
            final List<String> suggestions = [
              'I want to ask about your service',
              'Are you free now?',
            ];

            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text('Start a conversation now with each other'),
                Gap(4.h),
                Text(
                  'Choose a sticker below to say hello',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textSecondary100(context),
                      ),
                ),
                Gap(12.h),
                SizedBox(
                  height: 120.h,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    padding: PaddingConstants.padAll16,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final sticker = stickers[index];

                      return InkWell(
                        onTap: () {
                          context.read<ChatBloc>().add(
                                SendSticker(sticker),
                              );
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.r),
                          child: Image.asset(
                            'assets/sticker/$sticker.png',
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, trace) {
                              return Container(
                                color: Theme.of(context).lightGrey(context),
                                child: Icon(
                                  Icons.image_not_supported,
                                  color:
                                      Theme.of(context).greyScale500(context),
                                  size: 48,
                                ),
                              );
                            },
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (context, index) => Gap(20.w),
                    itemCount: stickers.length,
                  ),
                ),
                Text(
                  'or',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).textSecondary100(context),
                      ),
                ),
                SizedBox(
                  height: 70.h,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    padding: PaddingConstants.padAll16,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final suggestion = suggestions[index];

                      return InkWell(
                        onTap: () {
                          context.read<ChatBloc>().add(
                                SendMessage(message: suggestion),
                              );
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          decoration: BoxDecoration(
                            color: Theme.of(context).greyScale100(context),
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          child: Center(
                            child: Text(
                              suggestion,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                            ),
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (context, index) => Gap(10.w),
                    itemCount: suggestions.length,
                  ),
                ),
              ],
            );
          }

          final items = insertChatMiscellaneous(
            messages: List<ChatMessage>.from(messages.reversed),
            readReceipt: state.markReadResponse,
            currentUID: gs.uid ?? 0,
          );

          // ✅ Situation when there are messages
          return Column(
            children: [
              // CircularProgressIndicator at the top when loading more messages
              if (_isLoadingMore && !_hasReachedEnd)
                Padding(
                  padding: EdgeInsets.only(top: 12.h),
                  child: Center(
                    child: const CircularProgressIndicator(),
                  ),
                ),

              // List of messages
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  reverse: true,
                  padding: PaddingConstants.padAll12,
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    final item = items[index];
                    final key = GlobalKey();

                    if (item is Timestamp) {
                      return TimestampSeparator(
                        timestamp: item.timestamp,
                      );
                    } else if (item is ReadReceipt) {
                      return ReadReceiptWidget(
                        markReadResponse: item.markReadResponse,
                        currentUID: gs.uid ?? 0,
                      );
                    } else if (item is Message) {
                      // Extract the message from the list
                      final message = item.message;
                      final me = message.uuid == gs.uid!;

                      // Determine if the message is an offer
                      final isOffer =
                          message.offerID != null && message.offerID! > 0;
                      if (isOffer) {
                        return OfferMessage(
                          offerID: message.offerID!,
                          createdAt: message.createdAt!,
                          updatedAt: message.updatedAt!,
                          orderNo: message.orderNo!,
                          title: message.title!,
                          description: message.description!,
                          price: message.price!,
                          status: message.status!,
                          type: message.type!,
                          sellerID: message.sellerID!,
                          customerID: message.customerID,
                          requestID: message.requestID,
                          serviceID: message.serviceID,
                          files: message.files,
                          paymentStatus: message.paymentStatus,
                          paymentMethod: message.paymentMethod,
                          transactionID: message.transactionID,
                          sellerName: widget.name,
                          sellerAvatar: widget.avatar,
                          offerBloc: widget.offerBloc,
                          chatBloc: widget.chatBloc,
                          channelName: widget.channelName,
                        );
                      }

                      // Determine if the message is a media message
                      final mediaMessage =
                          message.image != null && message.image!.isNotEmpty ||
                              message.file != null && message.file!.isNotEmpty;

                      // Handle showing the timetoken tapping functionality
                      final timetoken = _tapped.contains(index);
                      final showTimetoken = !mediaMessage && timetoken;

                      // Should we show avatar or not?
                      final bool isLastMessageFromUser = () {
                        // Return 'false' if the message is from the current user
                        if (me) return false;

                        // Move to the previous item (because the list is reversed)
                        if (index - 1 >= 0) {
                          final prev = items[index - 1];

                          // If the previous item is a 'Timestamp', then return 'true'
                          if (prev is Timestamp) {
                            return true;
                          }

                          // If the previous item is a 'Message', check if the UUIDs are different
                          if (prev is Message) {
                            final previousMessage = prev.message;
                            return previousMessage.uuid != message.uuid;
                          }
                        }

                        // If there is no previous item or it is not a 'Timestamp' or 'Message', return 'true'
                        return true;
                      }();

                      return KeyedSubtree(
                        key: key,
                        child: GestureDetector(
                          onLongPressStart: (details) {
                            _showActionMenu(
                              details.globalPosition,
                              key,
                              message,
                              context.read<ChatBloc>(),
                            );
                          },
                          onTap: () {
                            setState(() {
                              timetoken
                                  ? _tapped.remove(index)
                                  : _tapped.add(index);
                            });

                            if (message.file != null &&
                                message.file!.isNotEmpty) {
                              _downloadingFile = message.file;
                              context.read<ChatBloc>().add(
                                    DownloadFile(
                                      message.fileID!,
                                      message.fileName!,
                                    ),
                                  );
                            }
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 4.h),
                            child: me
                                ? MessageRight(
                                    message: message.message,
                                    sticker: message.sticker,
                                    translated: message.translated,
                                    image: message.image,
                                    imageID: message.imageID,
                                    imageName: message.imageName,
                                    pendingImage: message.pendingImage,
                                    file: message.file,
                                    fileName: message.fileName,
                                    pendingFile: message.pendingFile,
                                    fileSize: message.fileSize,
                                    showTimetoken: showTimetoken,
                                    time: showTimetoken
                                        ? formatTimetoken(message.timetoken)
                                        : '',
                                    isPending: message.isPending,
                                    downloadingFile: _downloadingFile,
                                    parentMessage: message.parentMessage,
                                    name: widget.name,
                                  )
                                : MessageLeft(
                                    message: message.message,
                                    sticker: message.sticker,
                                    translated: message.translated,
                                    image: message.image,
                                    imageID: message.imageID,
                                    imageName: message.imageName,
                                    file: message.file,
                                    fileName: message.fileName,
                                    fileSize: message.fileSize,
                                    username: widget.name,
                                    avatar: isLastMessageFromUser
                                        ? widget.avatar
                                        : null,
                                    showTimetoken: showTimetoken,
                                    time: showTimetoken
                                        ? formatTimetoken(message.timetoken)
                                        : '',
                                    downloadingFile: _downloadingFile,
                                    parentMessage: message.parentMessage,
                                    name: widget.name,
                                  ),
                          ),
                        ),
                      );
                    } else {
                      return const SizedBox();
                    }
                  },
                ),
              ),
              if (_isReplying)
                Container(
                  width: double.infinity,
                  height: 70.h,
                  padding: EdgeInsets.only(
                    top: 10.h,
                    bottom: 10.h,
                    left: 20.w,
                    right: 10.w,
                  ),
                  color: Theme.of(context).greyScale50(context),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              height: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Theme.of(context)
                                      .informationBase(context),
                                ),
                              ),
                            ),
                            Gap(12.w),
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _replyingTo!,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Gap(4.h),
                                  Text(
                                    _replyingMessage!,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: Theme.of(context)
                                              .textSecondary(context),
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => clearReplyState(),
                        padding: EdgeInsets.zero,
                        icon: Icon(
                          Icons.close_rounded,
                          size: 24.sp,
                          color: Theme.of(context).blackPrimary(context),
                        ),
                      )
                    ],
                  ),
                )
            ],
          );
        },
      ),
    );
  }

  List<ChatItem> insertChatMiscellaneous({
    required List<ChatMessage> messages,
    MarkReadResponse? readReceipt,
    required int currentUID,
  }) {
    // Insert timestamp among messages that have timetoken difference more than 10mins (TimestampSeparator)
    const int threshold = 10;
    final List<ChatItem> result = [];

    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];
      final timestamp = Timetoken(
        BigInt.parse(message.timetoken),
      ).toDateTime();

      if (i == 0) {
        result.add(Timestamp(timestamp));
      } else {
        final previousMessage = messages[i - 1];
        final previousTimestamp = Timetoken(
          BigInt.parse(previousMessage.timetoken),
        ).toDateTime();
        final difference = timestamp.difference(previousTimestamp).inMinutes;

        if (difference >= threshold) {
          result.add(Timestamp(timestamp));
        }
      }
      result.add(
        Message(message),
      );

      // Insert read receipt for latest message being marked as read (ReadReceipt)
      final showReadReceipt = readReceipt != null &&
          readReceipt.timetoken == message.timetoken &&
          readReceipt.readBy.isNotEmpty;

      if (showReadReceipt) {
        result.add(
          ReadReceipt(readReceipt),
        );
      }
    }

    return result.reversed.toList();
  }

  void _showActionMenu(
    Offset position,
    GlobalKey key,
    ChatMessage message,
    ChatBloc bloc,
  ) {
    // Remove any existing action menu
    if (_actionMenuEntry?.mounted == true) {
      _actionMenuEntry!.remove();
      _actionMenuEntry = null;
    }

    // Calculate the position and the size of the message bubble
    final renderbox = key.currentContext?.findRenderObject() as RenderBox?;
    final offset = renderbox?.localToGlobal(Offset.zero);
    final size = renderbox?.size;

    if (offset == null || size == null) return;

    // Check if the message is from the current user
    final bool me = message.uuid == gs.uid!;

    // Variables for calculating the position for the action menu
    final screenSize = MediaQuery.of(context).size;
    const double menuHeight = 100;
    const double spacing = 8;

    // Calcute y offset for the action menu
    double top = offset.dy + size.height + spacing;
    if (top + menuHeight > screenSize.height) {
      top = offset.dy - menuHeight - spacing;
      if (top < spacing) top = spacing;
    }

    // Calculate x offset for the action menu
    double left = offset.dx;
    double right = screenSize.width - (offset.dx + size.width);

    _actionMenuEntry = OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            // Overlay background
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  _actionMenuEntry?.remove();
                  _actionMenuEntry = null;
                },
                child: Container(
                  color:
                      Theme.of(context).blackPrimary(context).withOpacity(0.6),
                ),
              ),
            ),

            // Highlighted message bubble
            Positioned(
              left: offset.dx,
              top: offset.dy,
              child: IgnorePointer(
                child: BlocProvider.value(
                  value: bloc,
                  child: Material(
                    color: Theme.of(context).transparent(context),
                    child: SizedBox(
                      width: size.width,
                      height: size.height,
                      child: me
                          ? MessageRight(
                              key: ValueKey('overlay_${message.timetoken}'),
                              message: message.message,
                              sticker: message.sticker,
                              translated: message.translated,
                              image: message.image,
                              imageID: message.imageID,
                              imageName: message.imageName,
                              pendingImage: message.pendingImage,
                              file: message.file,
                              fileName: message.fileName,
                              pendingFile: message.pendingFile,
                              fileSize: message.fileSize,
                              showTimetoken: false,
                              time: '',
                              isPending: message.isPending,
                              downloadingFile: _downloadingFile,
                              parentMessage: message.parentMessage,
                              name: widget.name,
                            )
                          : MessageLeft(
                              key: ValueKey('overlay_${message.timetoken}'),
                              message: message.message,
                              sticker: message.sticker,
                              translated: message.translated,
                              image: message.image,
                              imageID: message.imageID,
                              imageName: message.imageName,
                              file: message.file,
                              fileName: message.fileName,
                              fileSize: message.fileSize,
                              username: widget.name,
                              avatar: widget.avatar,
                              showTimetoken: false,
                              time: '',
                              downloadingFile: _downloadingFile,
                              parentMessage: message.parentMessage,
                              name: widget.name,
                            ),
                    ),
                  ),
                ),
              ),
            ),

            // Message action menu
            Positioned(
              left: me ? null : left,
              right: me ? right : null,
              top: top,
              child: SizedBox(
                height: 100,
                child: MessageActionMenu(
                  onAction: (action) {
                    _actionMenuEntry?.remove();
                    _actionMenuEntry = null;
                    _handleAction(
                      action,
                      message,
                    );
                  },
                  me: me,
                  isMediaMessage: (message.image != null &&
                          message.image!.isNotEmpty) ||
                      (message.file != null && message.file!.isNotEmpty) ||
                      (message.sticker != null && message.sticker!.isNotEmpty),
                ),
              ),
            ),
          ],
        );
      },
    );

    Overlay.of(context).insert(_actionMenuEntry!);
  }

  void _handleAction(
    String action,
    ChatMessage message,
  ) {
    switch (action) {
      // Handle 'Copy' action
      case 'Copy':
        final String text =
            (message.translated != null && message.translated!.isNotEmpty)
                ? message.translated!
                : message.timetoken;

        Clipboard.setData(
          ClipboardData(text: text),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Copied to clipboard'),
          ),
        );
        break;

      // Handle 'Reply' action
      case 'Reply':
        setState(() {
          _isReplying = true;

          // Set the replying username
          if (message.uuid == gs.uid) {
            _replyingTo = gs.user?.displayName ?? 'Myself';
          } else {
            _replyingTo = widget.name;
          }

          // Set the replying message
          if (message.image != null && message.image!.isNotEmpty) {
            if (message.uuid == gs.uid) {
              _replyingMessage = 'You sent an image';
            } else {
              _replyingMessage = '${widget.name} sent you an image';
            }
          } else if (message.file != null && message.file!.isNotEmpty) {
            if (message.uuid == gs.uid) {
              _replyingMessage = 'You sent a file';
            } else {
              _replyingMessage = '${widget.name} sent you a file';
            }
          } else if (message.sticker != null && message.sticker!.isNotEmpty) {
            if (message.uuid == gs.uid) {
              _replyingMessage = 'You sent a sticker';
            } else {
              _replyingMessage = '${widget.name} sent you a sticker';
            }
          } else if (message.translated != null &&
              message.translated!.isNotEmpty) {
            _replyingMessage = '${message.translated!} (${message.message})';
          } else {
            _replyingMessage = message.message;
          }
        });

        widget.onReply?.call(
          _isReplying,
          message,
        );

        break;

      // Handle 'Delete' action
      case 'Delete':
        final bloc = context.read<ChatBloc>();

        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (_) {
            return CustomMessageDialog(
              title: 'Do you want to delete this message?',
              subtitle: 'This message will be deleted permanently.',
              confirmText: 'Delete',
              cancelText: 'Cancel',
              onConfirm: () {
                bloc.add(
                  DeleteMessage(
                    channelName: widget.channelName,
                    timetoken: message.timetoken,
                  ),
                );

                context.pop();
              },
              onCancel: () => context.pop(),
            );
          },
        );
        break;
    }
    debugPrint("$action selected");
  }
}
