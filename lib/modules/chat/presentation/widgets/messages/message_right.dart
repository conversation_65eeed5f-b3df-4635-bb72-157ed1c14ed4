import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/chat/chat_message.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';

class MessageRight extends StatefulWidget {
  final String message;
  final String? sticker;
  final String? translated;
  final String time;
  final bool showTimetoken;
  final String? image;
  final String? imageID;
  final String? imageName;
  final String? pendingImage;
  final String? file;
  final String? fileName;
  final String? pendingFile;
  final int? fileSize;
  final bool isPending;
  final String? downloadingFile;
  final ChatMessage? parentMessage;
  final String? name;

  const MessageRight({
    super.key,
    required this.message,
    this.sticker,
    this.translated,
    required this.time,
    required this.showTimetoken,
    this.image,
    this.imageID,
    this.imageName,
    this.pendingImage,
    this.file,
    this.fileName,
    this.pendingFile,
    this.fileSize,
    this.isPending = false,
    this.downloadingFile,
    this.parentMessage,
    this.name,
  });

  @override
  State<MessageRight> createState() => _MessageRightState();
}

class _MessageRightState extends State<MessageRight>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Determine if the message has a sticker
    final hasSticker = widget.sticker != null && widget.sticker!.isNotEmpty;

    // Determine if the message has an image
    final isImageLoaded = widget.image != null && widget.image!.isNotEmpty;
    final isImagePending =
        widget.pendingImage != null && widget.pendingImage!.isNotEmpty;
    final hasImage = isImagePending || isImageLoaded;

    // Determine if the message has a file
    final isFileLoaded = widget.file != null && widget.file!.isNotEmpty;
    final isFilePending =
        widget.pendingFile != null && widget.pendingFile!.isNotEmpty;
    final hasFile = isFilePending || isFileLoaded;

    // Determine if the message is replying to another message
    final isReplying = widget.parentMessage != null;
    final hasParentImage = widget.parentMessage?.image != null &&
        widget.parentMessage!.image!.isNotEmpty;
    final hasParentFile = widget.parentMessage?.file != null &&
        widget.parentMessage!.file!.isNotEmpty;
    final hasParentSticker = widget.parentMessage?.sticker != null &&
        widget.parentMessage!.sticker!.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Flexible(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: hasFile ? 1.sw * 4 / 5 : 1.sw * 3 / 5,
                ),
                child: Container(
                  padding:
                      hasImage ? EdgeInsets.zero : PaddingConstants.padAll12,
                  decoration: hasSticker
                      ? null
                      : BoxDecoration(
                          color: Theme.of(context).greyScale100(context),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                  child: _buildMessage(
                    hasSticker: hasSticker,
                    hasImage: hasImage,
                    isImagePending: isImagePending,
                    hasFile: hasFile,
                    isReplying: isReplying,
                    hasParentImage: hasParentImage,
                    hasParentFile: hasParentFile,
                    hasParentSticker: hasParentSticker,
                  ),
                ),
              ),
            ),
          ],
        ),
        if (widget.showTimetoken) _buildTimetoken(),
        if (widget.isPending) _buildPending(),
      ],
    );
  }

  Widget _buildMessage({
    required bool hasSticker,
    required bool hasImage,
    required bool isImagePending,
    required bool hasFile,
    required bool isReplying,
    required bool hasParentImage,
    required bool hasParentFile,
    required bool hasParentSticker,
  }) {
    if (isReplying) {
      return _buildReplyMessage(
        hasParentImage,
        hasParentFile,
        hasParentSticker,
      );
    }

    if (hasSticker) {
      return _buildSticker();
    }

    if (hasImage) {
      return _buildImage(isImagePending);
    }

    if (hasFile) {
      return _buildFile();
    }

    return _buildText();
  }

  String _replyMessage(
    bool hasParentImage,
    bool hasParentFile,
    bool hasParentSticker,
  ) {
    if (hasParentImage) {
      return '[Image]';
    } else if (hasParentFile) {
      return '[Attachment] ${widget.parentMessage!.fileName}';
    } else if (hasParentSticker) {
      return '[Sticker]';
    }
    return widget.parentMessage!.message;
  }

  Widget _buildReplyPreview(
    bool hasParentImage,
    bool hasParentFile,
    bool hasParentSticker,
  ) {
    if (hasParentImage) {
      return Container(
        width: 42.w,
        height: 42.h,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6.r),
          child: CachedNetworkImage(
            imageUrl: widget.parentMessage!.image!,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              width: 42.w,
              height: 42.h,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        ),
      );
    } else if (hasParentFile) {
      return SvgPicture.asset(
        AppAssets.pdf1Svg,
        width: 42.w,
        height: 42.h,
      );
    } else if (hasParentSticker) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(6.r),
        child: Image.asset(
          'assets/sticker/${widget.parentMessage!.sticker}.png',
          width: 42.w,
          height: 42.h,
          fit: BoxFit.contain,
          errorBuilder: (context, error, trace) {
            return Container(
              color: Theme.of(context).lightGrey(context),
              child: Icon(
                Icons.image_not_supported,
                color: Theme.of(context).greyScale500(context),
                size: 42,
              ),
            );
          },
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildReplyMessage(
    bool hasParentImage,
    bool hasParentFile,
    bool hasParentSticker,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.parentMessage != null) ...[
          SizedBox(
            width: double.infinity,
            height: 42.h,
            child: Row(
              children: [
                Container(
                  height: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).informationBase(context),
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                Gap(4.w),
                Expanded(
                  child: Row(
                    children: [
                      _buildReplyPreview(
                        hasParentImage,
                        hasParentFile,
                        hasParentSticker,
                      ),
                      Gap(8.w),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              (widget.parentMessage!.uuid == gs.uid)
                                  ? gs.user!.displayName
                                  : widget.name!,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    fontFamily: AppFontFamily.semiBold,
                                  ),
                            ),
                            Gap(2.h),
                            Text(
                              _replyMessage(
                                hasParentImage,
                                hasParentFile,
                                hasParentSticker,
                              ),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    fontFamily: AppFontFamily.regular,
                                    color: Theme.of(context)
                                        .textSecondary(context),
                                  ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
        Gap(8.h),
        _buildText(),
      ],
    );
  }

  Widget _buildSticker() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: Image.asset(
        'assets/sticker/${widget.sticker}.png',
        width: 120.w,
        height: 120.h,
        fit: BoxFit.contain,
        errorBuilder: (context, error, trace) {
          return Container(
            color: Theme.of(context).lightGrey(context),
            child: Icon(
              Icons.image_not_supported,
              color: Theme.of(context).greyScale500(context),
              size: 64,
            ),
          );
        },
      ),
    );
  }

  Widget _buildImage(bool isImagePending) {
    if (isImagePending) {
      return Stack(
        children: [
          Opacity(
            opacity: 0.5,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: Image.file(
                File(widget.pendingImage!),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const Positioned.fill(
            child: Center(
              child: CircularProgressIndicator(),
            ),
          ),
        ],
      );
    }

    return GestureDetector(
      onTap: () {
        context.push(
          RouteName.interactiveImage,
          extra: {
            'image': CachedNetworkImageProvider(widget.image!),
            'image_id': widget.imageID!,
            'image_name': widget.imageName!,
            'bloc': context.read<ChatBloc>(),
          },
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: Hero(
          tag: 'interactive_image_${widget.imageID}',
          child: CachedNetworkImage(
            imageUrl: widget.image!,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              width: double.infinity,
              height: 200.h,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFile() {
    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        return Row(
          children: [
            if (widget.downloadingFile == widget.file && state.isDownloading)
              SizedBox(
                width: 48.w,
                height: 48.h,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else
              SvgPicture.asset(
                AppAssets.pdf1Svg,
                width: 48.w,
                height: 48.h,
              ),
            Gap(10.w),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    (widget.pendingFile != null &&
                            widget.pendingFile!.isNotEmpty)
                        ? widget.pendingFile!.split('/').last
                        : widget.fileName!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    widget.fileSize != null
                        ? '${(widget.fileSize! / (1024 * 1024)).toStringAsFixed(2)} MB'
                        : '—',
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.translated != null && widget.translated!.isNotEmpty) ...[
          Text(
            widget.translated!,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: AppFontFamily.regular,
                ),
          ),
          Gap(12.h),
        ],
        if (widget.message.isNotEmpty)
          Text(
            widget.message,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily:
                      widget.translated != null && widget.translated!.isNotEmpty
                          ? AppFontFamily.lightItalic
                          : AppFontFamily.regular,
                  fontSize: (widget.translated != null &&
                          widget.translated!.isNotEmpty)
                      ? 12.sp
                      : null,
                ),
          ),
      ],
    );
  }

  Widget _buildTimetoken() {
    return Padding(
      padding: EdgeInsets.only(
        top: 4.h,
        left: 32.w,
      ),
      child: Text(
        widget.time,
        style: Theme.of(context).textTheme.labelSmall!.copyWith(
              color: Theme.of(context).textSecondary(context),
              fontFamily: AppFontFamily.regular,
              fontSize: 10.sp,
            ),
      ),
    );
  }

  Widget _buildPending() {
    return Padding(
      padding: EdgeInsets.only(
        top: 4.h,
        left: 32.w,
      ),
      child: Text(
        'Sending...',
        style: Theme.of(context).textTheme.labelSmall!.copyWith(
              color: Theme.of(context).textSecondary(context),
              fontFamily: AppFontFamily.regular,
              fontSize: 10.sp,
            ),
      ),
    );
  }
}
