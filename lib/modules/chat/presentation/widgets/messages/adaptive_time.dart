import 'dart:async';

import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/date_time_utils.dart';
import 'package:pubnub/pubnub.dart';

class AdaptiveTimeWidget extends StatefulWidget {
  final String timetoken;

  const AdaptiveTimeWidget({
    super.key,
    required this.timetoken,
  });

  @override
  State<AdaptiveTimeWidget> createState() => _AdaptiveTimeWidgetState();
}

class _AdaptiveTimeWidgetState extends State<AdaptiveTimeWidget> {
  String _ago = '';
  Timer? _timer;
  Duration? _updateInterval;

  @override
  void initState() {
    super.initState();

    _updateTimeAgo();
  }

  void _updateTimeAgo() {
    _ago = calculateTimetokenDifference(widget.timetoken);

    final time = _convertTimetoken(widget.timetoken);
    final difference = DateTime.now().difference(time);

    Duration interval;
    if (difference.inMinutes < 60) {
      interval = const Duration(minutes: 1);
    } else if (difference.inHours < 24) {
      interval = const Duration(hours: 1);
    } else {
      interval = const Duration(days: 1);
    }

    if (_updateInterval != interval) {
      _timer?.cancel();
      _updateInterval = interval;
      _timer = Timer.periodic(
        interval,
        (timer) {
          if (mounted) {
            setState(() {
              _updateTimeAgo();
            });
          }
        },
      );
    }

    if (mounted) {
      setState(() {});
    }
  }

  DateTime _convertTimetoken(String timetoken) {
    if (BigInt.tryParse(timetoken) == null) {
      return DateTime.now();
    }

    return Timetoken(
      BigInt.parse(timetoken),
    ).toDateTime();
  }

  @override
  void didUpdateWidget(AdaptiveTimeWidget previous) {
    super.didUpdateWidget(previous);

    if (widget.timetoken != previous.timetoken) {
      _updateTimeAgo();
    }
  }

  @override
  void dispose() {
    _timer?.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      _ago,
      style: Theme.of(context).textTheme.bodySmall!.copyWith(
            color: Theme.of(context).textSecondary(context),
          ),
    );
  }
}
