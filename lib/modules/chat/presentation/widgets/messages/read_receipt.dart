import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/model/chat/mark_read_response.dart';
import 'package:multime_app/core/themes/theme.dart';

class ReadReceiptWidget extends StatelessWidget {
  final MarkReadResponse markReadResponse;
  final int currentUID;

  const ReadReceiptWidget({
    super.key,
    required this.markReadResponse,
    required this.currentUID,
  });

  @override
  Widget build(BuildContext context) {
    final partner = markReadResponse.readBy
        .where((user) => user.userID != currentUID)
        .toList();

    if (currentUID == markReadResponse.senderID && partner.isNotEmpty) {
      return Padding(
        padding: EdgeInsets.zero,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Icon(
              Icons.done_all,
              size: 12.sp,
              color: Theme.of(context).textSecondary100(context),
            ),
            Gap(4.w),
            Text(
              partner.length == 1
                  ? 'Seen'
                  : 'Seen by ${partner.first.username} and ${partner.length - 1} others',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).textSecondary100(context),
                    fontSize: 10.sp,
                  ),
            ),
          ],
        ),
      );
    }

    return SizedBox.shrink();
  }
}
