import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/theme.dart';

class TimestampSeparator extends StatelessWidget {
  final DateTime timestamp;

  const TimestampSeparator({
    super.key,
    required this.timestamp,
  });

  @override
  Widget build(BuildContext context) {
    final formattedTimestamp =
        DateFormat('HH:mm, dd MMM yyyy').format(timestamp);

    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: 10.h,
        ),
        child: Text(
          formattedTimestamp,
          style: Theme.of(context).textTheme.labelSmall!.copyWith(
                color: Theme.of(context).textSecondary(context),
                fontFamily: AppFontFamily.regular,
              ),
        ),
      ),
    );
  }
}
