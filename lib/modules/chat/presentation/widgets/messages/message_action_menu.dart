import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/theme.dart';

class MessageActionMenu extends StatelessWidget {
  final void Function(String action) onAction;
  final bool me;
  final bool isMediaMessage;

  const MessageActionMenu({
    super.key,
    required this.onAction,
    this.me = false,
    this.isMediaMessage = false,
  });

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> actions = [
      {
        'label': 'Reply',
        'icon': AppAssets.reply2Svg,
      },
    ];
    if (!isMediaMessage) {
      actions.insert(0, {
        'label': 'Copy',
        'icon': AppAssets.copy1Svg,
      });
    }
    if (me) {
      actions.add({
        'label': 'Delete',
        'icon': AppAssets.deleteSvg,
      });
    }

    return Material(
      color: Theme.of(context).transparent(context),
      child: Container(
        padding: PaddingConstants.padAll12,
        decoration: BoxDecoration(
          color: Theme.of(context).whitePrimary(context),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: actions.take(4).map(
            (action) {
              return _buildAction(
                context,
                action['icon'],
                action['label'],
                action['label'] == 'Delete',
              );
            },
          ).toList(),
        ),
      ),
    );
  }

  Widget _buildAction(
    BuildContext context,
    String icon,
    String label,
    bool isDelete,
  ) {
    return SizedBox(
      width: 70.w,
      height: 70.h,
      child: Material(
        color: Theme.of(context).transparent(context),
        child: InkWell(
          onTap: () => onAction(label),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                icon,
                width: 24.w,
                height: 24.h,
              ),
              Gap(8.h),
              Text(
                label,
                textAlign: TextAlign.center,
                style: isDelete
                    ? Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Theme.of(context).primary(context),
                        )
                    : Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
