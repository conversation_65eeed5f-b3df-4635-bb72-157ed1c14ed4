import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';

class InteractiveImage extends StatefulWidget {
  final ImageProvider image;
  final String imageID;
  final String imageName;

  const InteractiveImage({
    super.key,
    required this.image,
    required this.imageID,
    required this.imageName,
  });

  @override
  State<InteractiveImage> createState() => _InteractiveImageState();
}

class _InteractiveImageState extends State<InteractiveImage>
    with TickerProviderStateMixin {
  final TransformationController _transformationController =
      TransformationController();
  late AnimationController _animationController;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 120),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();

    super.dispose();
  }

  void _handleTap() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  void _handleDoubleTap(TapDownDetails details) {
    // Get the position where the user double-tapped
    final position = details.localPosition;

    if (_transformationController.value.getMaxScaleOnAxis() > 1.0) {
      // If the image is zoomed in, reset the transformation
      final Animation<Matrix4> animation = Matrix4Tween(
        begin: _transformationController.value,
        end: Matrix4.identity(),
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ),
      );

      _animationController.reset();
      _animationController.forward();

      animation.addListener(() {
        _transformationController.value = animation.value;
      });

      // Show controls when the image is reset
      if (!_showControls) {
        setState(() {
          _showControls = true;
        });
      }
    } else {
      // If the image is not zoomed in, apply a zoom transformation
      final double scale = 2;
      final Offset pointer = position;
      final Matrix4 zoomed = Matrix4.identity()
        ..translate(pointer.dx, pointer.dy)
        ..scale(scale)
        ..translate(-pointer.dx, -pointer.dy);

      final Animation<Matrix4> animation = Matrix4Tween(
        begin: _transformationController.value,
        end: zoomed,
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ),
      );

      _animationController.reset();
      _animationController.forward();

      animation.addListener(() {
        _transformationController.value = animation.value;
      });

      // Hide controls when the image is zoomed in
      if (_showControls) {
        setState(() {
          _showControls = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).blackPrimary(context),
          body: Stack(
            children: [
              Center(
                child: GestureDetector(
                  onTap: _handleTap,
                  onDoubleTapDown: _handleDoubleTap,
                  child: InteractiveViewer(
                    transformationController: _transformationController,
                    child: Hero(
                      tag: 'interactive_image_${widget.imageID}',
                      child: Image(
                        image: widget.image,
                        fit: BoxFit.cover,
                        width: double.infinity,
                      ),
                    ),
                  ),
                ),
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 120),
                switchInCurve: Curves.easeIn,
                switchOutCurve: Curves.easeOut,
                child: _showControls
                    ? Container(
                        key: const ValueKey('controls_visible'),
                        padding: PaddingConstants.padSymH16.add(
                          EdgeInsets.only(top: 40),
                        ),
                        color: Theme.of(context)
                            .blackPrimary(context)
                            .withOpacity(0.75),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Theme.of(context).whitePrimary(context),
                              ),
                              onPressed: () => context.pop(),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.download,
                                color: Theme.of(context).whitePrimary(context),
                              ),
                              onPressed: () => context.read<ChatBloc>().add(
                                    DownloadFile(
                                      widget.imageID,
                                      widget.imageName,
                                    ),
                                  ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(
                        key: ValueKey('controls_hidden'),
                      ),
              ),
              if (state.isDownloading)
                Positioned.fill(
                  child: Container(
                    color: Colors.black.withOpacity(0.5),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
