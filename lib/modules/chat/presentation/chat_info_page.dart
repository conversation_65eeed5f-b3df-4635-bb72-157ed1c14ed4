import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/chat/presentation/widgets/custom_message_dialog.dart';
import 'package:multime_app/modules/chat/presentation/widgets/navigation_bar/user_avatar.dart';
import 'package:permission_handler/permission_handler.dart';

class ChatInfoPage extends StatefulWidget {
  final String avatar;
  final String name;

  const ChatInfoPage({
    super.key,
    required this.avatar,
    required this.name,
  });

  @override
  State<ChatInfoPage> createState() => _ChatInfoPageState();
}

class _ChatInfoPageState extends State<ChatInfoPage> {
  bool _notificationGranted = false;

  @override
  void initState() {
    super.initState();
    _checkNotificationPermission();
  }

  Future<void> _checkNotificationPermission() async {
    final status = await Permission.notification.status;
    setState(() {
      _notificationGranted = status == PermissionStatus.granted;
    });
  }

  Future<void> _handleNotificationToggle() async {
    if (_notificationGranted) {
      _showPermissionDialog(
        title: 'Notification Permission',
        subtitle:
            'To turn off notifications, please disable them in your device settings.',
        confirmText: 'Turn off',
      );
    } else {
      final status = await Permission.notification.request();

      if (status == PermissionStatus.granted) {
        setState(() {
          _notificationGranted = true;
        });
      } else if (status == PermissionStatus.permanentlyDenied) {
        _showPermissionDialog(
          title: 'Notification Permission',
          subtitle:
              'Please allow notifications within your device settings so you never miss important messages from your contacts.',
          confirmText: 'Turn on',
        );
      }
    }
  }

  void _showPermissionDialog({
    required String title,
    required String subtitle,
    required String confirmText,
  }) {
    showDialog(
      context: context,
      builder: (_) {
        return CustomMessageDialog(
          title: title,
          subtitle: subtitle,
          confirmText: confirmText,
          onConfirm: () {
            openAppSettings();
            context.pop();
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            width: 24.w,
            height: 24.h,
          ),
          onPressed: () => context.pop(),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: PaddingConstants.padAll16,
        child: SingleChildScrollView(
          child: Column(
            children: [
              UserAvatar(
                username: widget.name,
                size: 100,
                imageURL: widget.avatar,
                isOnline: false,
              ),
              Gap(24.h),
              Text(
                widget.name,
                style: Theme.of(context).textTheme.headlineMedium,
                overflow: TextOverflow.ellipsis,
              ),
              Gap(24.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /*
                  _buildAction(
                    context: context,
                    icon: AppAssets.balanceUser,
                    label: 'View profile',
                    onTap: () {},
                  ),
                  _buildAction(
                    context: context,
                    icon: AppAssets.searchSvg,
                    label: 'Search',
                    onTap: () {},
                  ),
                  _buildAction(
                    context: context,
                    icon: AppAssets.notiSvg,
                    label: 'Turn off\nnotification',
                    onTap: () {},
                  ),
                  */
                ],
              ),
              Gap(24.h),

              // THIS IS ONLY FOR VERSION 1, SUBJECT TO CHANGE IN THE FUTURE
              _buildListTile(
                context: context,
                icon: AppAssets.balanceUser,
                title: 'View profile',
                onTap: () {},
              ),
              _buildListTile(
                context: context,
                icon: _notificationGranted
                    ? AppAssets.notiOffSvg
                    : AppAssets.notiSvg,
                title: _notificationGranted
                    ? 'Turn off notification'
                    : 'Turn on notification',
                onTap: _handleNotificationToggle,
              ),
              // END

              _buildListTile(
                context: context,
                icon: AppAssets.icon_notetext,
                title: 'My Orders',
                onTap: () {},
              ),
              _buildListTile(
                context: context,
                icon: AppAssets.noteFavoriteSvg,
                title: 'My Offers',
                onTap: () {},
              ),
              /*
              _buildListTile(
                context: context,
                icon: AppAssets.noteEditSvg,
                title: 'Set a nickname',
              ),
              _buildListTile(
                context: context,
                icon: AppAssets.gallerySvg,
                title: 'Media files, files and links',
              ),
              _buildListTile(
                context: context,
                icon: AppAssets.voiceChat2Svg,
                title: 'My language',
              ),
              _buildListTile(
                context: context,
                icon: AppAssets.slashSvg,
                title: 'Block',
              ),
              _buildListTile(
                context: context,
                icon: AppAssets.dangerSvg,
                title: 'Report',
              ),
              _buildListTile(
                context: context,
                icon: AppAssets.trash1Svg,
                title: 'Delete conversation history',
                isDelete: true,
              ),
              */
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListTile({
    required BuildContext context,
    required String icon,
    required String title,
    VoidCallback? onTap,
    bool isDelete = false,
  }) {
    return ListTile(
      onTap: onTap,
      leading: SvgPicture.asset(
        icon,
        width: 24.w,
        height: 24.h,
      ),
      title: Text(
        title,
        style: isDelete
            ? Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).primary(context),
                )
            : Theme.of(context).textTheme.bodyLarge,
      ),
      trailing: isDelete
          ? null
          : SvgPicture.asset(
              AppAssets.arrowRightSvg,
              width: 20.w,
              height: 20.h,
            ),
    );
  }

  Widget _buildAction({
    required BuildContext context,
    required String icon,
    required String label,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          SvgPicture.asset(
            icon,
            width: 24.w,
            height: 24.h,
          ),
          Gap(10.h),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
