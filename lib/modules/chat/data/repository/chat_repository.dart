import 'package:dio/dio.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/chat/channel_authorization.dart';
import 'package:multime_app/core/model/chat/contacts.dart';
import 'package:multime_app/core/model/chat/conversations.dart';
import 'package:multime_app/core/model/chat/mark_read_response.dart';
import 'package:multime_app/core/model/chat/message_translation.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';

import '../../../../core/model/chat/personal_update_channel_token.dart';
import '../../../../core/model/chat/presence_token_model.dart';

abstract class ChatRepository {
  Future<List<Conversations>> getConversations({
    int page = 1,
    int limit = 10,
    CancelToken? cancelToken,
  });

  Future<ChannelAuthorization> createChannel(
    int uid, {
    CancelToken? cancelToken,
  });

  Future<String> refreshToken(
    String channelName, {
    CancelToken? cancelToken,
  });

  Future<MessageTranslation> translateMessage(
    String filePath,
    String fileName,
    String sourceLang,
    String targetLang, {
    CancelToken? cancelToken,
  });

  Future<List<Contacts>> getContacts({
    int page = 1,
    int limit = 10,
    CancelToken? cancelToken,
  });

  Future<PersonalUpdateChannelToken> getPersonalUpdateToken(
    int uid, {
    CancelToken? cancelToken,
  });

  Future<MarkReadResponse> markMessageAsRead(
    String channelID,
    String? timetoken, {
    CancelToken? cancelToken,
  });

  Future<PresenceTokenModel> presenceToken(
    int userId, {
    CancelToken? cancelToken,
  });
}

class ChatRepositoryImpl implements ChatRepository {
  final ApiClient _apiClient = getIt<ApiClient>();

  // ✅ [StrongBody API] Get conversations/channels of current user
  @override
  Future<List<Conversations>> getConversations({
    int page = 1,
    int limit = 10,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.getConversations,
        method: ApiType.get,
        headers: {'Scope': 'admin-dashboard'},
        queryParameters: {
          'page': page,
          'limit': limit,
        },
        cancelToken: cancelToken,
      );

      final conversations = (response['data']['data'] as List<dynamic>? ?? [])
          .map((item) => Conversations.fromJson(item as Map<String, dynamic>))
          .toList();
      return conversations;
    } catch (error) {
      print('Error fetching conversations: $error');
      rethrow;
    }
  }

  // ✅ [StrongBody API] Create a new channel
  @override
  Future<ChannelAuthorization> createChannel(
    int uid, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.createChannel,
        method: ApiType.post,
        data: {'user_id': uid},
        headers: {'Scope': 'multi-me'},
        cancelToken: cancelToken,
      );
      return ChannelAuthorization.fromJson(response);
    } catch (error) {
      print('Error creating channel: $error');
      rethrow;
    }
  }

  // ✅ [StrongBody API] Refresh the token for the specific channel
  @override
  Future<String> refreshToken(
    String channelName, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: '${ApiConst.conversation}/${channelName}/refresh-token',
        method: ApiType.post,
        headers: {'Scope': 'multi-me'},
        cancelToken: cancelToken,
      );

      final token =
          (response['data'] as Map<String, dynamic>?)?['token'] as String? ??
              '';
      await gs.saveChannelToken(channelName, token);
      return token;
    } catch (error) {
      print('Error refreshing token: $error');
      rethrow;
    }
  }

  // ✅ [StrongBody API] Speech-to-text translation for messages
  @override
  Future<MessageTranslation> translateMessage(
    String filePath,
    String fileName,
    String sourceLang,
    String targetLang, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.translateMessage,
        method: ApiType.post,
        data: FormData.fromMap({
          'audio_file': await MultipartFile.fromFile(
            filePath,
            filename: fileName,
          ),
          'source_lang': sourceLang,
          'target_lang': targetLang,
        }),
        headers: {'Scope': 'multi-me'},
        cancelToken: cancelToken,
      );

      return MessageTranslation.fromJson(response);
    } catch (error) {
      print('Error translating message: $error');
      rethrow;
    }
  }

  // ✅ [StrongBody API] Get contacts of the current user
  @override
  Future<List<Contacts>> getContacts({
    int page = 1,
    int limit = 10,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.getHeaderMessage,
        method: ApiType.get,
        headers: {'Scope': 'multi-me'},
        queryParameters: {
          'page': page,
          'limit': limit,
        },
        cancelToken: cancelToken,
      );

      final data = response['data']['data'];
      final contacts = List<Contacts>.from(
        data.map(
          (item) => Contacts.fromJson(item),
        ),
      );
      return contacts;
    } catch (error) {
      print('Error fetching conversations: $error');
      rethrow;
    }
  }

  // ✅ [StrongBody API] Get personal update channel token for the current user
  @override
  Future<PersonalUpdateChannelToken> getPersonalUpdateToken(
    int uid, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.personalUpdateToken,
        method: ApiType.get,
        queryParameters: {
          'user_id': uid,
        },
        headers: {'Scope': 'multi-me'},
        cancelToken: cancelToken,
      );
      return PersonalUpdateChannelToken.fromJson(response);
    } catch (error) {
      print('Error fetching personal update token: $error');
      rethrow;
    }
  }

  // ✅ [StrongBody API] Mark message as read and get read status
  @override
  Future<MarkReadResponse> markMessageAsRead(
    String channelID,
    String? timetoken, {
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.markReadAndStatus,
        method: ApiType.post,
        headers: {'Scope': 'multi-me'},
        data: {
          'channel_id': channelID,
          'message_timetoken': timetoken,
        },
        cancelToken: cancelToken,
      );

      if (response['code'] != null && response['code'] != 0) {
        print(
          '❌ [Marking as Read API] Failed with code ${response['code']}, message \"${response['message']}\". Stopping further processing.',
        );
        return MarkReadResponse.empty();
      }

      return MarkReadResponse.fromJson(response);
    } catch (error) {
      print('Error marking message as read (from ChatRepository): $error');
      rethrow;
    }
  }

  @override
  Future<PresenceTokenModel> presenceToken(int userId,
      {CancelToken? cancelToken}) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.presenceToken,
        method: ApiType.get,
        queryParameters: {'user_id': userId},
        headers: {'Scope': 'multi-me'},
        cancelToken: cancelToken,
      );

      print('🔍 Personal update token raw response: $response');
      return PresenceTokenModel.fromJson(response);
    } catch (error) {
      print('Error fetching personal update token: $error');
      rethrow;
    }
  }
}
