import 'package:pubnub/pubnub.dart';

import '../../../../core/model/chat/presence_update_model.dart';

class PubNubServicesPresence {
  final PubNub pubnub;
  Subscription? _subscription; // ✅ Thay đổi từ late thành nullable

  PubNubServicesPresence(
    this.pubnub,
  );

  // ✅ Subscribe vào presence_global channel với presence events
  Future<void> subscribePresenceGlobal() async {
    // ✅ Dispose subscription cũ nếu có
    await _subscription?.dispose();

    // ✅ Khởi tạo subscription mới
    _subscription = pubnub.subscribe(
      channels: {'presence_global'},
      withPresence: true,
    );

    print('✅ PubNubServicesPresence: Subscribed to presence_global');
  }

  Stream<PresenceUpdate> presenceUpdates() {
    // ✅ Kiểm tra subscription có tồn tại không
    if (_subscription == null) {
      print('❌ PubNubServicesPresence: Subscription not initialized');
      return Stream.empty();
    }

    return _subscription!.presence
        .map((event) {
          final uuid = event.uuid.toString();
          final userId = int.tryParse(uuid);

          if (userId == null) {
            print('⚠️ Could not parse userId from uuid: $uuid');
            return null;
          }

          bool isOnline = false;
          DateTime? lastSeenAt;

          switch (event.action) {
            case PresenceAction.join:
              isOnline = true;
              break;
            case PresenceAction.leave:
            case PresenceAction.timeout:
              isOnline = false;
              lastSeenAt = DateTime.now();
              break;
            default:
              print('⚠️ Unknown presence action: ${event.action}');
              return null;
          }

          return PresenceUpdate(
            userId: userId,
            isOnline: isOnline,
            lastSeenAt: lastSeenAt,
          );
        })
        .where((e) => e != null)
        .cast<PresenceUpdate>();
  }

  /// Fetch current online users (hereNow)
  Future<List<PresenceUpdate>> fetchHereNow() async {
    try {
      final result = await pubnub.hereNow(
        channels: {'presence_global'},
        stateInfo: StateInfo.onlyUUIDs, // chỉ lấy UUID
      );

      final updates = <PresenceUpdate>[];
      result.channels.forEach((_, channelData) {
        channelData.uuids.forEach((uuid, _) {
          final userId = int.tryParse(uuid);
          if (userId != null) {
            updates.add(PresenceUpdate(userId: userId, isOnline: true));
          }
        });
      });

      return updates;
    } catch (e) {
      print('❌ Error fetching hereNow: $e');
      return [];
    }
  }

  /// Dispose subscription
  Future<void> dispose() async {
    await _subscription?.dispose();
    _subscription = null;
  }

  Future<void> leavePresence() async {
    if (_subscription != null) {
      await _subscription!.dispose();
      _subscription = null;
      print('✅ Left presence_global channel');
    } else {
      print('⚠️ No active subscription to leave');
    }
  }
}
