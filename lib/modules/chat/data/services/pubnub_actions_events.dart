import 'dart:async';

import 'package:flutter/material.dart';
import 'package:multime_app/core/model/chat/mark_read_response.dart';
import 'package:multime_app/modules/chat/data/repository/chat_repository.dart';

class PubNubActionsEvents with WidgetsBindingObserver {
  final ChatRepository _repository;
  final String _channelID;
  String? _lastTimetoken;
  bool _isAppActive = true;

  // [DEBOUNCER]
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(seconds: 10);

  PubNubActionsEvents({
    required ChatRepository repository,
    required String channelID,
  })  : _repository = repository,
        _channelID = channelID {
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _isAppActive = state == AppLifecycleState.resumed;

    // Mark latest message as read when app resumes
    if (_isAppActive && _lastTimetoken != null) {
      _markMessageAsRead(_lastTimetoken!);
    }
  }

  // [COMMON FUNCTION] Mark specific message as read
  Future<MarkReadResponse?> _markMessageAsRead(
    String timetoken,
  ) async {
    try {
      final response = await _repository.markMessageAsRead(
        _channelID,
        timetoken,
      );
      return response;
    } catch (error) {
      debugPrint('Error marking message as read: $error');
    }

    // [DEFAULT] Return 'null'
    return null;
  }

  // Handle marking message as read based on 'isDebouncing' flag. If true, use debouncer to delay the action. If false, mark immediately.
  Future<MarkReadResponse?> markMessageAsRead(
    String timetoken, {
    bool isDebouncing = false,
  }) async {
    try {
      _lastTimetoken = timetoken;
      _cancel();

      if (isDebouncing) {
        final completer = Completer<MarkReadResponse?>();
        _debounceTimer = Timer(_debounceDuration, () async {
          if (_isAppActive) {
            final response = await _markMessageAsRead(timetoken);
            completer.complete(response);
          } else {
            completer.complete(null);
          }
        });
        return completer.future;
      } else {
        return await _markMessageAsRead(timetoken);
      }
    } catch (error) {
      debugPrint(
        'Error marking message as read (from PubNub - Actions and Events): $error',
      );
    }

    // [DEFAULT] Return 'null'
    return null;
  }

  // Cancel any ongoing debouncer
  void _cancel() {
    _debounceTimer?.cancel();
    _debounceTimer = null;
  }

  void dispose() {
    _cancel();
    WidgetsBinding.instance.removeObserver(this);
  }
}
