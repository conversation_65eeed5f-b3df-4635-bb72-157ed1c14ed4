import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/chat/chat_message.dart';
import 'package:multime_app/core/model/offer/create_offer_response.dart';
import 'package:pubnub/pubnub.dart';

class PubNubServices {
  final PubNub pubnub;

  PubNubServices({
    required this.pubnub,
  });

  // Variables used for pagination/lazy loading
  late dynamic _history;
  bool _isInitialized = false;

  // ✅ [PubNub] Initialize the history object for a channel
  Future<void> _initializeHistory(
    String channelName,
    int chunkSize,
  ) async {
    if (!_isInitialized) {
      _history = pubnub.channel(channelName).history(chunkSize: chunkSize);
      _isInitialized = true;
    }
  }

  // ✅ [PubNub] Fetch first batch of messages from a channel
  Future<List<ChatMessage>> fetchMessages(
    String channelName, {
    int chunkSize = 20,
  }) async {
    await _initializeHistory(
      channelName,
      chunkSize,
    );

    if (_history.hasMore) {
      try {
        await _history.more();
        final messages = (_history.messages as List)
            .map(
              (message) => ChatMessage.fromHistory(
                message,
                channelName,
                pubnub,
              ),
            )
            .toList();
        return messages;
      } catch (error) {
        print('Error fetching messages: $error');
        rethrow;
      }
    }

    return [];
  }

  // ✅ [PubNub] Fetch more messages from a channel
  Future<List<ChatMessage>> fetchMoreMessages(
    String channelName, {
    int chunkSize = 20,
  }) async {
    if (!_isInitialized) {
      return await fetchMessages(
        channelName,
        chunkSize: chunkSize,
      );
    }

    if (_history.hasMore) {
      final length = _history.messages.length;
      await _history.more();
      final messages = (_history.messages as List)
          .sublist(length)
          .map(
            (message) => ChatMessage.fromHistory(
              message,
              channelName,
              pubnub,
            ),
          )
          .toList();
      return messages;
    }
    return [];
  }

  // ✅ [PubNub] Determine if there are more messages to load
  bool get hasMoreMessages => _isInitialized && _history.hasMore;

  // ✅ [PubNub] Reset pagination state
  void resetPagination() {
    _isInitialized = false;
  }

  // ✅ [PubNub] Subscribe to a channel and listen for messages
  Stream<ChatMessage> subscribeChannel(String channelName) {
    final subscription = pubnub.subscribe(
      channels: {
        channelName,
      },
    );

    return subscription.messages.where(
      (envelope) {
        final original = envelope.originalMessage;
        if (original?['d'] is Map) {
          final data = original['d'] as Map;
          if (data['type'] == 'read' || data['source'] == 'actions') {
            return false;
          }
        }
        return true;
      },
    ).map(
      (message) {
        return ChatMessage.fromEnvelope(message, pubnub);
      },
    );
  }

  // ✅ [PubNub] Send a text message to a channel
  Future<void> sendMessage(
    String channelName,
    String message,
  ) async {
    await pubnub.publish(
      channelName,
      {
        'type': 'text',
        'content': message,
        'uuid': gs.uid!,
      },
    );
  }

  // ✅ [PubNub] Send a reply message to a channel
  Future<void> sendReply(
    String channelName,
    String message,
    ChatMessage parentMessage,
  ) async {
    await pubnub.publish(
      channelName,
      {
        'type': 'text',
        'content': message,
        'parent': parentMessage.toJson(),
        'uuid': gs.uid!,
      },
    );
  }

  // ✅ [PubNub] Send a sticker to a channel
  Future<void> sendSticker(
    String channelName,
    String sticker,
  ) async {
    await pubnub.publish(
      channelName,
      {
        'type': 'sticker',
        'sticker': sticker,
        'uuid': gs.uid!,
      },
    );
  }

  // ✅ [PubNub] Send a translation message to a channel
  Future<void> sendTranslation(
    String channelName,
    String message,
    String translated,
  ) async {
    await pubnub.publish(
      channelName,
      {
        'type': 'text',
        'content': message,
        'translated': translated,
        'uuid': gs.uid!,
      },
    );
  }

  // ✅ [PubNub] Send a file to a channel
  Future<void> sendFile(
    String channelName,
    File file,
  ) async {
    final bytes = await file.readAsBytesSync();

    await pubnub.files.sendFile(
      channelName,
      file.uri.pathSegments.last,
      bytes,
      fileMessage: {
        'type': 'file',
        'uuid': gs.uid!,
        'size': bytes.length,
      },
    );
  }

  // ✅ [PubNub] Download a file from a channel
  Future<List<int>> downloadFile(
    String channelName,
    String fileID,
    String fileName,
  ) async {
    final result = await pubnub.files.downloadFile(
      channelName,
      fileID,
      fileName,
    );
    return result.fileContent;
  }

  // ✅ [PubNub] Send an image to a channel with compression to ensure it fits within size limits (under 5MB)
  Future<void> sendImage(
    String channelName,
    File file,
  ) async {
    final bytes = await file.readAsBytesSync();
    const int targetSize = 5 * 1024 * 1024;

    if (bytes.lengthInBytes <= targetSize) {
      await pubnub.files.sendFile(
        channelName,
        file.uri.pathSegments.last,
        bytes,
        fileMessage: {
          'type': 'image',
          'uuid': gs.uid!,
        },
      );
      return;
    }

    int quality = 90;
    const int minimumQuality = 30;
    Uint8List? compressed;

    while (quality >= minimumQuality) {
      compressed = await FlutterImageCompress.compressWithList(
        bytes,
        quality: quality,
      );

      if (compressed.lengthInBytes <= targetSize) {
        await pubnub.files.sendFile(
          channelName,
          file.uri.pathSegments.last,
          compressed,
          fileMessage: {
            'type': 'image',
            'uuid': gs.uid!,
          },
        );
        return;
      }
    }

    quality -= 10;
  }

  // ✅ [PubNub] Delete a message from a channel
  Future<void> deleteMessage(
    String channelName,
    String timetokenString,
  ) async {
    final timetoken = Timetoken(
      BigInt.parse(timetokenString),
    );

    // Calculate the end timetoken by adding one to the original timetoken
    final endTimetoken = Timetoken(
      timetoken.value + BigInt.one,
    );

    await pubnub
        .channel(channelName)
        .messages(
          from: timetoken,
          to: endTimetoken,
        )
        .delete();
  }

  // ✅ [PubNub] Send an offer message to a channel
  Future<void> sendOffer(
    String channelName,
    CreateOfferResponse createOfferResponse,
  ) async {
    await pubnub.publish(
      channelName,
      {
        'type': 'offer',
        'offer': {
          'id': createOfferResponse.offerID,
          'created_at': createOfferResponse.createdAt.toIso8601String(),
          'updated_at': createOfferResponse.updatedAt.toIso8601String(),
          'order_no': createOfferResponse.orderNo,
          'title': createOfferResponse.title,
          'description': createOfferResponse.description,
          'price': createOfferResponse.price,
          'status': createOfferResponse.status,
          'type': createOfferResponse.type,
          'seller_id': createOfferResponse.sellerID,
          'customer_id': createOfferResponse.customerID,
          'request_id': createOfferResponse.requestID,
          'service_id': createOfferResponse.serviceID,
          'files': createOfferResponse.files,
          'payment_status': createOfferResponse.paymentStatus,
          'payment_method': createOfferResponse.paymentMethod,
          'transaction_id': createOfferResponse.transactionID,
        },
        'uuid': gs.uid!,
      },
    );
  }

  // ✅ [PubNub] Subscribe to personal update channel
  Stream<Envelope> subscribePersonalUpdateChannel() {
    try {
      final subscription = pubnub.subscribe(
        channels: {'user_${gs.uid!}_updates'},
      );
      return subscription.messages;
    } catch (error) {
      print('Error subscribing to personal update channel: $error');
      return Stream.empty();
    }
  }

  // ✅ [PubNub] Process channel updates from the subscription
  Stream<Map<String, dynamic>> processPersonalUpdateChannel() {
    return subscribePersonalUpdateChannel().map(
      (envelope) {
        if (envelope.payload is Map<String, dynamic>) {
          return envelope.payload as Map<String, dynamic>;
        }
        return <String, dynamic>{};
      },
    ).handleError(
      (error) {
        print('Error processing personal update channel: $error');
      },
    );
  }
}
