import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:pubnub/pubnub.dart';

class PubNubInstance {
  static const _subscribeKey = '******************************************';
  static const _publishKey = '******************************************';

  static PubNub create(String authKey) {
    return PubNub(
      defaultKeyset: Keyset(
        subscribeKey: _subscribeKey,
        publishKey: _publishKey,
        authKey: authKey,
        userId: UserId(
          gs.uid!.toString(),
        ),
      ),
    );
  }
}
