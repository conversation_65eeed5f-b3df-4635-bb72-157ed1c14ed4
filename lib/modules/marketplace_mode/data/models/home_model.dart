import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_location.dart';

class MarketplaceResponse {
  final List<Section> sections;

  MarketplaceResponse({required this.sections});

  factory MarketplaceResponse.fromJson(Map<String, dynamic> json) {
    return MarketplaceResponse(
      sections:
          (json['sections'] as List).map((e) => Section.fromJson(e)).toList(),
    );
  }
}

class Section {
  final String sectionId;
  final String title;
  final String seeMoreLink;
  final List<Product> items;

  Section({
    required this.sectionId,
    required this.title,
    required this.seeMoreLink,
    required this.items,
  });

  factory Section.fromJson(Map<String, dynamic> json) {
    return Section(
      sectionId: json['section_id'],
      title: json['title'] ??'',
      seeMoreLink: json['see_more_link'] ?? '',
      items: (json['items'] as List<dynamic>?)?.map((e) => Product.fromJson(e)).toList() ??[],
    );
  }
}

class Product extends Equatable {
  final String id;
  final String? type;
  final String? title;
  final String description;
  final num price;
  final String condition;
  final String? imageUrl;
  // final bool isFav;
  final bool isLiked;
  final String? locationType;
  final int orderCount;
  final num rating;
  final int viewCount;
  final Location? location;
  final Seller? seller;

  const Product({
    required this.id,
    this.type,
    required this.title,
    required this.description,
    required this.price,
    required this.condition,
    this.imageUrl,
    // this.isFav = false,
    this.isLiked = false,
    this.locationType,
    this.orderCount = 0,
    this.rating = 0.0,
    this.viewCount = 0,
    this.location,
    this.seller,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as String,
      type: json['type'] as String?,
      title: json['title'] as String?,
      description: json['description'] as String,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      condition: json['condition'] as String,
      imageUrl: json['image_url'] as String?,
      isLiked: json['is_liked'] as bool? ?? false,
      locationType: json['location_type'] as String?,
      orderCount: json['order_count'] as int? ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      viewCount: json['view_count'] as int? ?? 0,
      location:
          json['location'] != null ? Location.fromJson(json['location']) : null,
      seller: json['seller'] != null ? Seller.fromJson(json['seller']) : null,
    );
  }

  Product copyWith({bool? isFav}) {
    return Product(
      id: id,
      type: type,
      title: title,
      description: description,
      price: price,
      condition: condition,
      imageUrl: imageUrl,
      // isFav: isFav ?? this.isFav,
      isLiked: isLiked,
      locationType: locationType,
      orderCount: orderCount,
      rating: rating,
      viewCount: viewCount,
      location: location,
      seller: seller,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        title,
        description,
        price,
        condition,
        imageUrl,
        // isFav,
        isLiked,
        locationType,
        orderCount,
        rating,
        viewCount,
        location,
        seller
      ];
}

class Seller extends Equatable {
  final String id;
  final String name;
  final String? imageUrl;
  final double rating;

  Seller({
    required this.id,
    required this.name,
    this.imageUrl,
    this.rating = 0.0,
  });

  factory Seller.fromJson(Map<String, dynamic> json) {
    return Seller(
      id: json['id'] as String,
      name: json['name'] as String,
      imageUrl: json['image_url'] as String?,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
    );
  }

  @override
  List<Object?> get props => [id, name, imageUrl, rating];
}

// {category_id: d032d397-a219-45fa-af32-e01f42ae3414, condition: new, created_at: 2025-03-24T11:47:31.747Z, description: Test Product Description, id: df887589-4c44-4249-a63c-27dc8e7de3f8, locations: [{address: Test Product Address, city: string, country: string, id: 1faa68de-aa3a-4c43-9efe-1ee1ab9e1392, location_lat: 0, location_long: 0, postal_code: string, state: string}], price: 10, quantity: 0, seller_id: b128e8c8-c1d9-4c9a-acbc-23924cd50fd7, status: new, title: Test Product Title, updated_at: 2025-03-24T11:47:31.747Z}