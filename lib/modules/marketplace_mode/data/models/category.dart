import 'package:equatable/equatable.dart';

class CategoryList extends Equatable {
  CategoryList({
    this.items,
  });

  CategoryList.fromJson(dynamic json) {
    items = [];

    // Handle the specific API response structure: {code, message, data: {list: [...]}}
    if (json['code'] == 0 &&
        json['data'] != null &&
        json['data']['list'] != null) {
      print('📋 Found list in data.list (API response structure)');
      (json['data']['list'] as List).forEach((v) {
        items?.add(Category.fromJson(v));
      });
    }
    // Handle other possible structures
    else if (json['items'] != null) {
      print('📋 Found items array in response');
      json['items'].forEach((v) {
        items?.add(Category.fromJson(v));
      });
    } else if (json['data'] != null && json['data']['items'] != null) {
      print('📋 Found items in data.items');
      json['data']['items'].forEach((v) {
        items?.add(Category.fromJson(v));
      });
    } else if (json['data'] != null && json['data'] is List) {
      print('📋 Found direct array in data');
      (json['data'] as List).forEach((v) {
        items?.add(Category.fromJson(v));
      });
    } else if (json is List) {
      print('📋 Found direct array response');
      json.forEach((v) {
        items?.add(Category.fromJson(v));
      });
    } else {
      print('⚠️ Unknown response structure, trying to find category data...');
      if (json is Map) {
        print('🔍 Available keys: ${json.keys}');
      }
    }

    print('✅ Parsed ${items?.length ?? 0} categories');
  }

  List<Category>? items;

  CategoryList copyWith({
    List<Category>? items,
  }) =>
      CategoryList(
        items: items ?? this.items,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (items != null) {
      map['items'] = items?.map((v) => v.toJson()).toList();
    }
    return map;
  }

  @override
  // TODO: implement props
  List<Object?> get props => [items];
}

class Category extends Equatable {
  Category({
    this.createdAt,
    this.description,
    this.id,
    this.isActive,
    this.modeType,
    this.name,
    this.updatedAt,
    this.slug,
    this.coverImage,
    this.icon,
    this.path,
    this.depth,
    this.parentId,
    this.createdBy,
  });

  Category.fromJson(dynamic json) {
    createdAt = json['created_at'];
    description = json['description'];
    id = json['id']?.toString(); // Convert to string since API returns int
    isActive = json['is_active'];
    modeType =
        json['type'] ?? json['mode_type']; // Handle both 'type' and 'mode_type'
    name = json['name']?.toString().trim(); // Ensure name is string and trimmed
    updatedAt = json['updated_at'];

    print('✅ Parsed category: $name (ID: $id)');
  }

  String? createdAt;
  String? description;
  String? id;
  bool? isActive;
  String? modeType;
  String? name;
  String? updatedAt;

  // Additional fields from API
  String? slug;
  String? coverImage;
  String? icon;
  String? path;
  int? depth;
  int? parentId;
  String? createdBy;

  Category copyWith({
    String? createdAt,
    String? description,
    String? id,
    bool? isActive,
    String? modeType,
    String? name,
    String? updatedAt,
    String? slug,
    String? coverImage,
    String? icon,
    String? path,
    int? depth,
    int? parentId,
    String? createdBy,
  }) =>
      Category(
        createdAt: createdAt ?? this.createdAt,
        description: description ?? this.description,
        id: id ?? this.id,
        isActive: isActive ?? this.isActive,
        modeType: modeType ?? this.modeType,
        name: name ?? this.name,
        updatedAt: updatedAt ?? this.updatedAt,
        slug: slug ?? this.slug,
        coverImage: coverImage ?? this.coverImage,
        icon: icon ?? this.icon,
        path: path ?? this.path,
        depth: depth ?? this.depth,
        parentId: parentId ?? this.parentId,
        createdBy: createdBy ?? this.createdBy,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['created_at'] = createdAt;
    map['description'] = description;
    map['id'] = id;
    map['is_active'] = isActive;
    map['mode_type'] = modeType;
    map['name'] = name;
    map['updated_at'] = updatedAt;
    return map;
  }

  @override
  List<Object?> get props => [
        createdAt,
        description,
        id,
        isActive,
        modeType,
        name,
        updatedAt,
        slug,
        coverImage,
        icon,
        path,
        depth,
        parentId,
        createdBy
      ];
}
