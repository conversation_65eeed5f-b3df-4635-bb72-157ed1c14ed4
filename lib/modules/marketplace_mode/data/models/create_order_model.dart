class CreateOrderModel {
  final String? buyerId;
  final String? createdAt;
  final String? id;
  final List<OrderItem>? items;
  final Map<String, dynamic>? metadata;
  final String? notes;
  final String? sellerId;
  final String? status;
  final List<StatusHistory>? statusHistory;
  final int? totalAmount;
  final String? updatedAt;

  CreateOrderModel({
    this.buyerId,
    this.createdAt,
    this.id,
    this.items,
    this.metadata,
    this.notes,
    this.sellerId,
    this.status,
    this.statusHistory,
    this.totalAmount,
    this.updatedAt,
  });

  factory CreateOrderModel.fromJson(Map<String, dynamic> json) {
    return CreateOrderModel(
      buyerId: json['buyer_id'] as String?,
      createdAt: json['created_at'] as String?,
      id: json['id'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => OrderItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      notes: json['notes'] as String?,
      sellerId: json['seller_id'] as String?,
      status: json['status'] as String?,
      statusHistory: (json['status_history'] as List<dynamic>?)
          ?.map((e) => StatusHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalAmount: json['total_amount'] as int?,
      updatedAt: json['updated_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'buyer_id': buyerId,
        'created_at': createdAt,
        'id': id,
        'items': items?.map((e) => e.toJson()).toList(),
        'metadata': metadata,
        'notes': notes,
        'seller_id': sellerId,
        'status': status,
        'status_history': statusHistory?.map((e) => e.toJson()).toList(),
        'total_amount': totalAmount,
        'updated_at': updatedAt,
      };
}

class OrderItem {
  final Map<String, dynamic>? metadata;
  final int? quantity;
  final String? referenceId;
  final String? referenceType;
  final num? unitPrice;

  OrderItem({
    this.metadata,
    this.quantity,
    this.referenceId,
    this.referenceType,
    this.unitPrice,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      metadata: json['metadata'] as Map<String, dynamic>?,
      quantity: json['quantity'] as int?,
      referenceId: json['reference_id'] as String?,
      referenceType: json['reference_type'] as String?,
      unitPrice: json['unit_price'] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
        'metadata': metadata,
        'quantity': quantity,
        'reference_id': referenceId,
        'reference_type': referenceType,
        'unit_price': unitPrice,
      };
}

class StatusHistory {
  final String? reason;
  final String? status;
  final String? timestamp;

  StatusHistory({
    this.reason,
    this.status,
    this.timestamp,
  });

  factory StatusHistory.fromJson(Map<String, dynamic> json) {
    return StatusHistory(
      reason: json['reason'] as String?,
      status: json['status'] as String?,
      timestamp: json['timestamp'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'reason': reason,
        'status': status,
        'timestamp': timestamp,
      };
}
