import 'package:multime_app/modules/marketplace_mode/data/models/product_detail_model.dart';

class LikedProductsModel {
  final List<ProductDetail> products;

  LikedProductsModel({
    this.products = const [],
  });

  factory LikedProductsModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return LikedProductsModel();
    return LikedProductsModel(
      products: (json['items'] as List<dynamic>?)
              ?.map((e) => ProductDetail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}
