import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';

class SearchModel {
  final int? total;
  final int? page;
  final int? pageSize;
  final List<Product>? products;

  SearchModel({
    this.total,
    this.page,
    this.pageSize,
    this.products,
  });

  factory SearchModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return SearchModel();
    return SearchModel(
      total: json['total'] as int?,
      page: json['page'] as int?,
      pageSize: json['page_size'] as int?,
      products: (json['products'] as List<dynamic>?)?.map((e) => Product.fromJson(e as Map<String, dynamic>)).toList(),
    );
  }
}

// class ProductSearchModel {
//   final String? id;
//   final String? name;
//   final String? description;
//   final double? price;
//   final String? category;

//   ProductSearchModel({
//     this.id,
//     this.name,
//     this.description,
//     this.price,
//     this.category,
//   });

//   factory ProductSearchModel.fromJson(Map<String, dynamic>? json) {
//     if (json == null) return ProductSearchModel();
//     return ProductSearchModel(
//       id: json['id'] as String?,
//       name: json['name'] as String?,
//       description: json['description'] as String?,
//       price: (json['price'] as num?)?.toDouble(),
//       category: json['category'] as String?,
//     );
//   }
// }
