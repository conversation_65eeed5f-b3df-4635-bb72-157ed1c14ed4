
class Location {
  final String id;
  final String address;
  final String city;
  final String country;
  final double locationLat;
  final double locationLong;
  final String postalCode;
  final String state;

  Location({
    required this.id,
    required this.address,
    required this.city,
    required this.country,
    required this.locationLat,
    required this.locationLong,
    required this.postalCode,
    required this.state,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      id: json['id'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      locationLat: (json['location_lat'] as num?)?.toDouble() ?? 0.0,
      locationLong: (json['location_long'] as num?)?.toDouble() ?? 0.0,
      postalCode: json['postal_code'] ?? '',
      state: json['state'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
  'id': id,
  'address': address,
  'city': city,
  'country': country,
  'location_lat': locationLat,
  'location_long': locationLong,
  'postal_code': postalCode,
  'state': state,
};

}
