class PaymentIntentResponse {
  final double amount;
  final String checkoutUrl;
  final String clientSecret;
  final String currency;
  final List<FeeBreakdown> feeBreakdown;
  final String id;
  final String paymentId;
  final String status;

  PaymentIntentResponse({
    required this.amount,
    required this.checkoutUrl,
    required this.clientSecret,
    required this.currency,
    required this.feeBreakdown,
    required this.id,
    required this.paymentId,
    required this.status,
  });

  factory PaymentIntentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentIntentResponse(
      amount: json['amount'] ?? 0,
      checkoutUrl: json['checkout_url'] ?? '',
      clientSecret: json['client_secret'] ?? '',
      currency: json['currency'] ?? '',
      feeBreakdown: (json['fee_breakdown'] as List<dynamic>?)
              ?.map((e) => FeeBreakdown.fromJson(e))
              .toList() ??
          [],
      id: json['id'] ?? '',
      paymentId: json['payment_id'] ?? '',
      status: json['status'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'amount': amount,
        'client_secret': clientSecret,
        'currency': currency,
        'fee_breakdown': feeBreakdown.map((e) => e.toJson()).toList(),
        'payment_id': paymentId,
        'status': status,
      };
}

class FeeBreakdown {
  final num amount;
  final String calculationBasis;
  final String feeType;
  final num percentage;

  FeeBreakdown({
    required this.amount,
    required this.calculationBasis,
    required this.feeType,
    required this.percentage,
  });

  factory FeeBreakdown.fromJson(Map<String, dynamic> json) {
    return FeeBreakdown(
      amount: json['amount'] ?? 0,
      calculationBasis: json['calculation_basis'] ?? '',
      feeType: json['fee_type'] ?? '',
      percentage: json['percentage'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        'amount': amount,
        'calculation_basis': calculationBasis,
        'fee_type': feeType,
        'percentage': percentage,
      };
}
