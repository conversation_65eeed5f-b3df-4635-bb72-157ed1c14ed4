import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/seller_model.dart';

class Product extends Equatable {
  final String productID;
  final String productName;
  final num price;
  final String about;
  final String condition;
  final String type;
  final String brand;
  final DateTime postDate;
  final Seller seller;
  final bool isFav;
  final String shopName;
  final String image;

  const Product({
    required this.image,
    required this.shopName,
    required this.productID,
    required this.productName,
    required this.price,
    required this.about,
    required this.condition,
    required this.type,
    required this.brand,
    required this.postDate,
    required this.seller,
    required this.isFav,
  });

  Product copyWith({bool? isLiked}) {
    return Product(
      productID: productID,
      productName: productName,
      price: price,
      about: about,
      condition: condition,
      type: type,
      brand: brand,
      postDate: postDate,
      seller: seller,
      isFav: isLiked ?? isFav,
      shopName: shopName,
      image: image,
    );
  }

  @override
  List<Object?> get props => [
        productID,
        about,
        condition,
        brand,
        postDate,
        seller,
        shopName,
        image,
        type,
        isFav,
        about,
        price,
        productName,
      ];
}
