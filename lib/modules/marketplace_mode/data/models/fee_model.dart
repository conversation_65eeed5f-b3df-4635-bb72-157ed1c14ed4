import 'package:multime_app/modules/marketplace_mode/data/models/payment_intent_model.dart';

class FeeResponse {
  final List<FeeBreakdown> feeBreakdown;
  final num platformFee;
  final num subtotal;
  final num totalAmount;
  final num totalFees;

  FeeResponse({
    required this.feeBreakdown,
    required this.platformFee,
    required this.subtotal,
    required this.totalAmount,
    required this.totalFees,
  });

  factory FeeResponse.fromJson(Map<String, dynamic> json) {
    return FeeResponse(
      feeBreakdown: (json['fee_breakdown'] as List<dynamic>?)
              ?.map((e) => FeeBreakdown.fromJson(e))
              .toList() ??
          [],
      platformFee: json['platform_fee'] ?? 0,
      subtotal: json['subtotal'] ?? 0,
      totalAmount: json['total_amount'] ?? 0,
      totalFees: json['total_fees'] ?? 0,
    );
  }
}
