class AddressListResponse {
  final List<Address> data;
  final bool success;

  AddressListResponse({required this.data, required this.success});

  factory AddressListResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'];
    final list = (data != null && data['data'] != null && data['data'] is List)
        ? data['data'] as List
        : <dynamic>[];
    return AddressListResponse(
      data: list
              .map((item) => Address.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      success: json['success'] ?? false,
    );
  }
}

class DeleteResponse {
  final String message;
  final String code;

  DeleteResponse({required this.message, required this.code});

  factory DeleteResponse.fromJson(Map<String, dynamic> json) {
    return DeleteResponse(
      code: json['code'] ?? '',
      message: json['message'] ?? '',
    );
  }
}

class AddressResponse {
  final Address data;
  final bool success;

  AddressResponse({required this.data, required this.success});

  factory AddressResponse.fromJson(Map<String, dynamic> json) {
    return AddressResponse(
      data: Address.fromJson(json['data'] ?? null),
      success: json['success'] ?? false,
    );
  }
}

class Address {
  final String street;
  final String? recipientName;
  final String city;
  final int? countryId;
  final DateTime? createdAt;
  final int? id;
  final bool? isDefault;
  final String? phone;
  final String? postalCode;
  final String? state;
  final DateTime? updatedAt;
  final int userId;
  final double? latitude;
  final double? longitude;

  Address({
    required this.street,
    required this.recipientName,
    required this.city,
    this.countryId,
    this.createdAt,
    this.id,
    this.isDefault,
    this.phone,
    this.postalCode,
    this.state,
    this.updatedAt,
    required this.userId,
    this.latitude = 0,
    this.longitude = 0,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street']?.toString() ?? '',
      recipientName: json['recipient_name']?.toString(),
      city: json['city']?.toString() ?? '',
      countryId: _parseIntSafely(json['country_id']) ?? 0,
      createdAt: DateTime.tryParse(json['created_at']?.toString() ?? '') ??
          DateTime.now(),
      id: _parseIntSafely(json['id']) ?? 0,
      isDefault: json['is_default'] ?? false,
      phone: json['phone']?.toString() ?? '',
      postalCode: json['postal_code']?.toString() ?? '',
      state: json['state']?.toString() ?? '',
      updatedAt: DateTime.tryParse(json['updated_at']?.toString() ?? '') ??
          DateTime.now(),
      userId: _parseIntSafely(json['user_id']) ?? 0,
      latitude: _parseDoubleSafely(json['latitude']),
      longitude: _parseDoubleSafely(json['longitude']),
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  static double? _parseDoubleSafely(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'recipient_name': recipientName,
      'city': city,
      'country_id': countryId,
      'created_at': createdAt?.toIso8601String(),
      'id': id,
      'is_default': isDefault,
      'phone': phone,
      'postal_code': postalCode,
      'state': state,
      'updated_at': updatedAt?.toIso8601String(),
      'user_id': userId,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}
