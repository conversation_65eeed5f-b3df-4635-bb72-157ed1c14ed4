import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';

class SimilarProduct {
  final List<Product> products;

  SimilarProduct({
    this.products = const [],
  });

  factory SimilarProduct.fromJson(Map<String, dynamic>? json) {
    if (json == null) return SimilarProduct();
    return SimilarProduct(
      products: (json['products'] as List<dynamic>?)
              ?.map((e) => Product.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}
