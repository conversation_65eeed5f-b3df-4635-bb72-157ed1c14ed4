import 'package:multime_app/modules/marketplace_mode/data/models/product_location.dart';

class ProductDetail {
  final String id;
  final String categoryId;
  final String condition;
  final DateTime createdAt;
  final String description;
  final List<Location> locations;
  final double price;
  final int quantity;
  final String sellerId;
  final String sellerName;
  final String sellerPhoto;
  final String status;
  final String title;
  final DateTime updatedAt;
  final List<String> mediaUrls;

  ProductDetail({
    required this.id,
    required this.categoryId,
    required this.condition,
    required this.createdAt,
    required this.description,
    required this.locations,
    required this.price,
    required this.quantity,
    required this.sellerId,
    required this.status,
    required this.title,
    required this.updatedAt,
    required this.mediaUrls,
    required this.sellerName,
    required this.sellerPhoto,
  });

  factory ProductDetail.fromJson(Map<String, dynamic> json) {
    return ProductDetail(
      id: json['id'] ?? '',
      categoryId: json['category_id'] ?? '',
      condition: json['condition'] ?? '',
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      description: json['description'] ?? '',
      locations: (json['locations'] as List<dynamic>?)
              ?.map((e) => Location.fromJson(e))
              .toList() ??
          [],
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      quantity: json['quantity'] ?? 0,
      sellerId: json['seller_id'] ?? '',
      status: json['status'] ?? '',
      title: json['title'] ?? '',
      updatedAt: DateTime.tryParse(json['updated_at'] ?? '') ?? DateTime.now(),
      mediaUrls: (json['media_urls'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      sellerName: json['seller_name'] ?? '',
      sellerPhoto: json['seller_photo'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
        'id': id,
        'category_id': categoryId,
        'condition': condition,
        'created_at': createdAt.toIso8601String(),
        'description': description,
        'locations': locations.map((e) => e.toJson()).toList(),
        'price': price,
        'quantity': quantity,
        'seller_id': sellerId,
        'status': status,
        'title': title,
        'updated_at': updatedAt.toIso8601String(),
        'media_urls': mediaUrls,
      };
}
