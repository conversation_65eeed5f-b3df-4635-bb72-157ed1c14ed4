//todo
import 'package:dio/dio.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/likedproducts/liked_products_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_detail_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/search_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/similar_product.dart';

abstract class ProductRepository {
  Future<MarketplaceResponse> getProductList({Map<String, dynamic> queryParam});
  Future<Product> postProduct({required Map<String, dynamic> data});
  Future<SearchModel> searchProduct({required Map<String, dynamic> queryParam});
  Future<List<String>> uploadProductImage(
      {required FormData data, required pid});
  Future<ProductDetail> getProductDetail({required pid});
  Future<Section> getSeeMoreProducts({required section});
  Future<SimilarProduct> getSimilarProducts(
      {required pid, Map<String, dynamic>? query});
  Future<LikedProductsModel> getLikedProducts();
}

class ProductRepositoryRemote implements ProductRepository {
  final ApiClient _apiClient;
  ProductRepositoryRemote(this._apiClient);

  @override
  Future<MarketplaceResponse> getProductList(
      {Map<String, dynamic>? queryParam}) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.getHomePageData,
          method: ApiType.get,
          queryParameters: queryParam);
      return MarketplaceResponse.fromJson(response);
    } catch (e) {
      print("getProductList err: $e");
      rethrow;
    }
  }

  @override
  Future<Product> postProduct({required Map<String, dynamic> data}) async {
    try {
      print(data);
      final response = await _apiClient.request(
          path: ApiConst.product, method: ApiType.post, data: data);
      return Product.fromJson(response);
    } catch (e) {
      print("postProduct repo err: $e");
      rethrow;
    }
  }

  @override
  Future<SearchModel> searchProduct(
      {required Map<String, dynamic> queryParam}) async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.searchProduct,
          method: ApiType.get,
          queryParameters: queryParam);
      return SearchModel.fromJson(response);
    } on NetworkException catch (e) {
      print("search product err: ${e.message}");
      rethrow;
    }
  }

  @override
  Future<List<String>> uploadProductImage(
      {required FormData data, required pid}) async {
    try {
      List<dynamic> response = await _apiClient.request(
        path: "${ApiConst.uploadProductImage + pid}/media",
        method: ApiType.post,
        data: data,
      );
      return List<String>.from(response);
    } catch (e) {
      print("uploadProductImage repo err: $e");
      rethrow;
    }
  }

  @override
  Future<ProductDetail> getProductDetail({required pid}) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.product}/$pid', method: ApiType.get);
      return ProductDetail.fromJson(response);
    } on NetworkException catch (e) {
      print("getProductDetail error: ${e.message}");
      rethrow;
    }
  }

  @override
  Future<Section> getSeeMoreProducts({required section}) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.getHomePageData}/section/$section',
          method: ApiType.get);
      return Section.fromJson(response);
    } on NetworkException catch (e) {
      print("getSeeMoreProducts error: ${e.message}");
      rethrow;
    }
  }

  @override
  Future<LikedProductsModel> getLikedProducts() async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.productLiked, method: ApiType.get);
      return LikedProductsModel.fromJson(response);
    } on NetworkException catch (e) {
      print("getLikedProducts error: ${e.message}");
      rethrow;
    }
  }

  @override
  Future<SimilarProduct> getSimilarProducts(
      {required pid, Map<String, dynamic>? query}) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.similarproducts}$pid/similar',
          method: ApiType.get,
          queryParameters: query);
      return SimilarProduct.fromJson(response);
    } on NetworkException catch (e) {
      print("getSeeMoreProducts error: ${e.message}");
      rethrow;
    }
  }
}
