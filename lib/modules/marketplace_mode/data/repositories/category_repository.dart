//todo
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/category.dart';

abstract class CategoryRepository {
  Future<CategoryList> getCategoryList({Map<String, dynamic>? queryParam});
}

class CategoryRepositoryRemote implements CategoryRepository {
  final ApiClient _apiClient;
  CategoryRepositoryRemote(this._apiClient);
  @override
  Future<CategoryList> getCategoryList(
      {Map<String, dynamic>? queryParam}) async {
    try {
      print('🌐 Making API call to: ${ApiConst.getCategoryList}');
      print('📤 Query params: $queryParam');
      final response = await _apiClient.request(
          path: ApiConst.getCategoryList,
          method: ApiType.get,
          queryParameters: queryParam);
      print('📥 Raw API response: $response');
      print('📄 Response type: ${response.runtimeType}');

      final categoryList = CategoryList.fromJson(response);
      print('🔄 Parsed CategoryList: $categoryList');
      print('📋 Items in parsed list: ${categoryList.items?.length ?? 0}');

      return categoryList;
    } catch (e) {
      print("❌ getCategoryList err: $e");
      print("🔍 Error type: ${e.runtimeType}");
      rethrow;
    }
  }
}
