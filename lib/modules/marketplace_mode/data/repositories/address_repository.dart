import 'package:dio/dio.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';

abstract class AddressRepository {
  Future<AddressListResponse> getAddresses();
  Future<ApiResponse<AddressResponse>> createAddress(Map<String, dynamic> data);
  Future<AddressResponse> updateAddress(
      Map<String, dynamic> data, int addressId);
  Future deleteAddress(DeleteAddressEvent event);
  Future toggleSetDefault(ToggleSetDefaultEvent event);
}

class AddressRepositoryRemote implements AddressRepository {
  final GlobalStorage _globalStorage;
  final ApiClient _apiClient;

  AddressRepositoryRemote(this._apiClient, this._globalStorage);

  @override
  Future deleteAddress(DeleteAddressEvent event) async {
    try {
      final response = await _apiClient.request(
        path: "${ApiConst.shippingAddress}/${event.addressId}",
        method: ApiType.delete,
        headers: {
          'Authorization': 'Bearer ${_globalStorage.accessToken}',
          'Scope': AppConstants.multi_me,
        },
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<AddressResponse> updateAddress(
      Map<String, dynamic> data, int addressId) async {
    try {
      final response = await _apiClient.request(
        path: "${ApiConst.shippingAddress}/$addressId",
        method: ApiType.patch,
        data: data,
        headers: {
          'Authorization': 'Bearer ${_globalStorage.accessToken}',
          'Scope': AppConstants.multi_me,
        },
      );
      return AddressResponse.fromJson(response);
    } catch (e) {
      print("update addresses error: $e");
      rethrow;
    }
  }

  @override
  Future<AddressListResponse> getAddresses() async {
    try {
      final response = await _apiClient.request(
          path: ApiConst.getShippingAddress,
          method: ApiType.get,
          headers: {
            'Authorization': 'Bearer ${_globalStorage.accessToken}',
            'Scope': AppConstants.multi_me,
          });
      return AddressListResponse.fromJson(response);
    } catch (e) {
      print("get addresses error: $e");
      rethrow;
    }
  }

  @override
  Future<ApiResponse<AddressResponse>> createAddress(
      Map<String, dynamic> data) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.shippingAddress,
        method: ApiType.post,
        data: data,
        headers: {
          'Authorization': 'Bearer ${_globalStorage.accessToken}',
          'Scope': AppConstants.multi_me,
        },
      );
      return ApiResponse<AddressResponse>.completed(
          AddressResponse.fromJson(response));
    } on DioException catch (e) {
      final errorData = e.response?.data;
      if (errorData is Map<String, dynamic>) {
        return ApiResponse.error(
          errorData['message'] ?? 'Create address failed',
          code: errorData['code'],
        );
      }
      return ApiResponse.error('Create address failed');
    } catch (e) {
      return ApiResponse.error('Unknown error');
    }
  }

  @override
  Future toggleSetDefault(ToggleSetDefaultEvent event) async {
    try {
      final response = await _apiClient.request(
        path: "${ApiConst.shippingAddress}/${event.addressId}/default",
        method: ApiType.patch,
        headers: {
          'Authorization': 'Bearer ${_globalStorage.accessToken}',
          'Scope': AppConstants.multi_me,
        },
      );
      return response;
    } catch (e) {
      return Future.error('Toggle set default failed');
    }
  }
}
