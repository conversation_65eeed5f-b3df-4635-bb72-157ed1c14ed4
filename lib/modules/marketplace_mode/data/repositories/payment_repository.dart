import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/create_order_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/fee_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/payment_intent_model.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_confirm_order/bloc/payment_bloc.dart';

abstract class PaymentRepository {
  Future<FeeResponse> calculateFee(CalculateFee event);
  Future<PaymentIntentResponse> createPaymentIntent(CreatePaymentIntent event);
  Future<CreateOrderModel> createOrder(CreateOrderEvent event);
}

class PaymentRepositoryRemote implements PaymentRepository {
  final GlobalStorage _globalStorage;
  final ApiClient _apiClient;

  PaymentRepositoryRemote(this._apiClient, this._globalStorage);

  @override
  Future<FeeResponse> calculateFee(CalculateFee event) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.calculateFee,
        method: ApiType.post,
        data: {
          "amount": event.amount,
          "platform_type": "network",
          "reference_type": event.referenceType,
          "user_id": _globalStorage.user!.id,
        },
      );
      return FeeResponse.fromJson(response);
    } catch (e) {
      print("calculate fee error: $e");
      rethrow;
    }
  }

  @override
  Future<PaymentIntentResponse> createPaymentIntent(
      CreatePaymentIntent event) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.createPaymentIntents,
        method: ApiType.post,
        data: {
          "amount": event.amount,
          "cancel_url": "http://cancel.com/",
          "currency": event.currency,
          "metadata": {},
          "payment_method_types": ["card"],
          "platform_type": "network",
          "reference_id": _globalStorage.user!.id,
          "reference_type": event.referenceType,
          "success_url": "http://success.com/",
          "user_id": _globalStorage.user!.id,
        },
      );
      return PaymentIntentResponse.fromJson(response);
    } catch (e) {
      print("create payment intent error: $e");
      rethrow;
    }
  }

  @override
  Future<CreateOrderModel> createOrder(CreateOrderEvent event) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.order,
        method: ApiType.post,
        data: {
          "buyer_id": event.buyerId,
          "seller_id": event.sellerId,
          "items": event.items,
          "metadata": event.metadata,
          "notes": event.notes,
        },
      );
      return CreateOrderModel.fromJson(response);
    } catch (e) {
      print("createOrder  error: $e");
      rethrow;
    }
  }
  
}
