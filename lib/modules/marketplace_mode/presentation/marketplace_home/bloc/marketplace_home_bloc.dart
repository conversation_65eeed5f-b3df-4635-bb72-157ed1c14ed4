import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';

part 'marketplace_home_event.dart';
part 'marketplace_home_state.dart';

class MarketplaceHomeBloc
    extends Bloc<MarketplaceHomeEvent, MarketplaceHomeState> {
  MarketplaceHomeBloc(this._productRepositoryRemote)
      : super(
          MarketplaceHomeState.initial(),
        ) {
    on<MarketplaceGetFeaturedProducts>(
        (event, emit) => _onGetHomePageApi(event, emit));
    // on<MarketplaceLikeToggle>(_onToggleLike);
    // on<MarketplaceSeeMoreEvent>(_onMarketplaceSeeMoreEvent);
  }
  final ProductRepositoryRemote _productRepositoryRemote;
  void _onGetHomePageApi(
    MarketplaceGetFeaturedProducts event,
    Emitter<MarketplaceHomeState> emit,
  ) async {
    try {
      // emit(MarketplaceFeaturedLoading());
      MarketplaceResponse marketplaceResponse =
          await _productRepositoryRemote.getProductList();
      List<Product> newProducts = [];
      List<Product> likeNewProducts = [];
      List<Product> featuredProducts = [];
      List<Product> mostViewProducts = [];

      for (var section in marketplaceResponse.sections) {
        if (section.sectionId == "new") {
          newProducts = section.items;
        } else if (section.sectionId == "like_new") {
          likeNewProducts = section.items;
        } else if (section.sectionId == "most_viewed") {
          mostViewProducts = section.items;
        } else if (section.sectionId == "featured") {
          featuredProducts = section.items;
        }
      }
      emit(state.copyWith(
        newProducts: newProducts,
        likeNewProducts: likeNewProducts,
        featuredProducts: featuredProducts,
        mostViewProduct: mostViewProducts,
        // allProducts: newProducts + likeNewProducts,
      ));
    } on NetworkException catch (e) {
      print(e.message);
      // emit(MarketplaceFeaturedError(message: e.message));
    }
  }

  // void _onToggleLike(
  //     MarketplaceLikeToggle event, Emitter<MarketplaceHomeState> emit) {
  //   if (event.productStatus == ProductStatus.featured) {
  //     final updatedFeaturedProducts = state.featuredProducts.map((product) {
  //       final isLiked = event.productList!.any((p) => p.id == product.id);
  //       return product.copyWith(isFav: isLiked);
  //     }).toList();

  //     emit(state.copyWith(featuredProducts: updatedFeaturedProducts));
  //   } else if (event.productStatus == ProductStatus.news) {
  //     print('product check');

  //     final updatedNewProducts = (state.newProducts).map((product) {
  //       print('product checking');

  //       if (product.id == event.toggledProduct.id) {
  //         print('product found');
  //         // Toggle only the product that was changed
  //         return product.copyWith(isFav: !product.isLiked);
  //       }
  //       return product;
  //     }).toList();

  //     emit(state.copyWith(newProducts: updatedNewProducts));
  //   }
  // }

  // void _onMarketplaceSeeMoreEvent(
  //   MarketplaceSeeMoreEvent event,
  //   Emitter<MarketplaceHomeState> emit,
  // ) async {
  //   emit(MarketplaceSeeMoreState(section: event.section));
  // }
}
