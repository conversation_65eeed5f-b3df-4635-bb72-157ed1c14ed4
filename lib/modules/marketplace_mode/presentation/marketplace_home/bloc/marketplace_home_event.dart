part of 'marketplace_home_bloc.dart';

enum ProductStatus { featured, news }

sealed class MarketplaceHomeEvent extends Equatable {
  const MarketplaceHomeEvent();

  @override
  List<Object?> get props => [];
}

class MarketplaceGetFeaturedProducts extends MarketplaceHomeEvent {
  const MarketplaceGetFeaturedProducts();
}

// class MarketplaceLikeToggle extends MarketplaceHomeEvent {
//   final List<ProductDetail>? productList;
//   final Product toggledProduct; // 👈 Add this
//   final ProductStatus productStatus;

//   const MarketplaceLikeToggle({
//     required this.productList,
//     required this.toggledProduct, // 👈 Add this
//     required this.productStatus,
//   });

//   @override
//   List<Object?> get props => [productList,toggledProduct, productStatus];
// }

class MarketplaceSeeMoreEvent extends MarketplaceHomeEvent {
  final String section;


  const MarketplaceSeeMoreEvent({
    required this.section,

  });

  @override
  List<Object?> get props => [section];
}