part of 'marketplace_home_bloc.dart';

class MarketplaceHomeState extends Equatable {
  final List<Product> newProducts;
  final List<Product> likeNewProducts;
  final List<Product> featuredProducts;
  final List<Product> mostViewProduct;
  // final List<Product> allProducts;

  const MarketplaceHomeState({
    this.newProducts = const [],
    this.likeNewProducts = const [],
    this.featuredProducts = const [],
    this.mostViewProduct = const [],
    // this.allProducts = const [],
  });
  MarketplaceHomeState copyWith({
    List<Product>? newProducts,
    List<Product>? likeNewProducts,
    List<Product>? featuredProducts,
    List<Product>? mostViewProduct,
  }) {
    return MarketplaceHomeState(
      newProducts: newProducts ?? this.newProducts,
      likeNewProducts: likeNewProducts ?? this.likeNewProducts,
      featuredProducts: featuredProducts ?? this.featuredProducts,
      mostViewProduct: mostViewProduct ?? this.mostViewProduct,
    );
  }

  factory MarketplaceHomeState.initial() {
    return const MarketplaceHomeState();
  }

  @override
  List<Object> get props =>
      [newProducts, likeNewProducts, featuredProducts, mostViewProduct];
}

// final class MarketplaceSeeMoreState extends MarketplaceHomeState {
//   final String section;
//   const MarketplaceSeeMoreState({required this.section});

//   @override
//   List<Object> get props => [section];
// }

// final class MarketplaceFeaturedLoading extends MarketplaceHomeState {}

// final class MarketplaceFeaturedLoaded extends MarketplaceHomeState {
//   final ProductList productList;
//   const MarketplaceFeaturedLoaded({required this.productList});
//   @override
//   List<Object> get props => [productList];
// }

// final class MarketplaceFeaturedError extends MarketplaceHomeState {
//   final String message;
//   const MarketplaceFeaturedError({required this.message});
//   @override
//   List<Object> get props => [message];
// }

// final class MarketplaceNewsLoaded extends MarketplaceHomeState {
//   final List<Product>? productList;
//   const MarketplaceNewsLoaded({this.productList});
//   @override
//   List<Object?> get props => [productList];
// }
