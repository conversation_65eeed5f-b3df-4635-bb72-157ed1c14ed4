import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/application/bottom_bar/application_page.dart';
import 'package:multime_app/modules/application/bottom_bar/bottomSheet_model.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_home/marketplace_home_page.dart';

class MarketPage extends StatefulWidget {
  const MarketPage({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _MarketPageState createState() => _MarketPageState();
}

class _MarketPageState extends State<MarketPage> {
  // @override
  // void initState() {
  //   super.initState();
  //   context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
  // }
  @override
  Widget build(BuildContext context) {
    return ApplicationPage(
      page: const MarketplaceHomeScreen(),
      bottomSheetItems: [
        BottomSheetItem(
          name: "Post product",
          iconLead: AppAssets.editSvg,
          onTap: () {
            (context).push(RouteName.postProductScreen);
          },
        ),
        BottomSheetItem(
          name: "Manage Your Posts",
          iconLead: AppAssets.icon_note,
          onTap: () {
            (context).push(RouteName.managePost);
          },
        ),
        BottomSheetItem(
          name: "Favorite",
          iconLead: AppAssets.favoriteMarket,
          onTap: () {
            (context).push(RouteName.likedScreen);
          },
        ),
      ],
    );
  }
}
