import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/application/app_bar/app_bar.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_home/bloc/marketplace_home_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/horizonal_product/horizonal_product.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/horizonal_product/large_horizonal_product.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/notifications/notification_promotion_card.dart';

import '../../../../shared/widgets/app_loader/404_strongbody.dart';
import '../../../../shared/widgets/app_loader/loading_simmer_widget.dart';

class MarketplaceHomeScreen extends StatelessWidget {
  const MarketplaceHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        moden: LocaleKeys.marketplace.tr(),
        onTapSearch: () => context.push(RouteName.marketplaceSearch),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //Post for Sale
            const PostNotification(),
            // Danh sách sản phẩm "Most viewed"
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.featured.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 18.sp),
                  ),
                  TextButton(
                    onPressed: () {
                      context.push(RouteName.productGridScreen, extra: {
                        'section': 'featured',
                        'title': 'Featured',
                      });
                    },
                    child: Text(
                      LocaleKeys.seeMore.tr(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).errorBase(context),
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 230.h,
              child: BlocBuilder<MarketplaceHomeBloc, MarketplaceHomeState>(
                builder: (context, state) {
                  if (state.featuredProducts.isNotEmpty) {
                    return ListView.builder(
                      padding: const EdgeInsets.only(
                        left: 16,
                      ),
                      scrollDirection: Axis.horizontal,
                      itemCount: state.featuredProducts.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 16),
                          child: HorizonalProduct(
                            id: state.featuredProducts[index].id,
                            image: state.featuredProducts[index].imageUrl ?? '',
                            productName:
                                state.featuredProducts[index].title ?? 'N/A',
                            price: state.featuredProducts[index].price,
                            shopAddress:
                                state.featuredProducts[index].location?.address,
                          ),
                        );
                      },
                    );
                  } else if (state.featuredProducts.isEmpty) {
                    return ListView.builder(
                        padding: const EdgeInsets.only(left: 16),
                        scrollDirection: Axis.horizontal,
                        itemCount: 4,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 16),
                            child: LoadingSimmerSB(
                              width: 170.w,
                              height: 230.h,
                            ),
                          );
                        });
                  } else {
                    return const Padding(
                      padding: EdgeInsets.only(left: 16),
                      child: Text('Unable to load featured products'),
                    );
                  }
                },
              ),
            ),
            // Safety notification
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SvgPicture.asset(AppAssets.safeSvg),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.newText.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 18.sp),
                  ),
                  TextButton(
                    onPressed: () {
                      context.push(RouteName.productGridScreen, extra: {
                        'section': 'new',
                        'title': 'New',
                      });
                    },
                    child: Text(
                      LocaleKeys.seeMore.tr(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).errorBase(context),
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 230.h,
              child: BlocBuilder<MarketplaceHomeBloc, MarketplaceHomeState>(
                builder: (context, state) {
                  return ListView.builder(
                    padding: const EdgeInsets.only(left: 16),
                    scrollDirection: Axis.horizontal,
                    itemCount: state.newProducts.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: HorizonalProduct(
                          id: state.newProducts[index].id,
                          image: state.newProducts[index].imageUrl ?? '',
                          productName: state.newProducts[index].title ?? 'N/A',
                          price: state.newProducts[index].price,
                          shopAddress:
                              state.newProducts[index].location?.address,
                          isFav: state.newProducts[index].isLiked,
                          onTap: () {
                            // TODO: Implement like functionality
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SvgPicture.asset(AppAssets.adsSvg),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.mostView.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 18.sp),
                  ),
                  TextButton(
                    onPressed: () {
                      context.push(RouteName.productGridScreen, extra: {
                        'section': 'most_viewed',
                        'title': 'Most Viewed',
                      });
                    },
                    child: Text(
                      LocaleKeys.seeMore.tr(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).errorBase(context),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
              child: Text(
                "Upgrade your account to a pro account to get more views",
                style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.normal,
                    color: Colors.blue),
              ),
            ),
            SizedBox(
              height: 230.h,
              child: BlocBuilder<MarketplaceHomeBloc, MarketplaceHomeState>(
                builder: (context, state) {
                  if (state.mostViewProduct.isNotEmpty) {
                    return ListView.builder(
                      padding: const EdgeInsets.only(left: 16),
                      scrollDirection: Axis.horizontal,
                      itemCount: state.mostViewProduct.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 16),
                          child: LargeHorizonalProduct(
                            id: state.mostViewProduct[index].id,
                            image: state.mostViewProduct[index].imageUrl ?? '',
                            productName:
                                state.mostViewProduct[index].title ?? 'N/A',
                            price: state.mostViewProduct[index].price,
                            shopAddress:
                                state.mostViewProduct[index].location?.address,
                          ),
                        );
                      },
                    );
                  } else if (state.mostViewProduct.isEmpty) {
                    return const PageNotFound();
                  } else {
                    return const PageNotFound();
                  }
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.likeNew.tr(),
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
                  ),
                  TextButton(
                    onPressed: () {
                      context.push(RouteName.productGridScreen, extra: {
                        'section': 'like_new',
                        'title': 'Like New',
                      });
                    },
                    child: Text(
                      LocaleKeys.seeMore.tr(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).errorBase(context),
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 230.h,
              child: BlocBuilder<MarketplaceHomeBloc, MarketplaceHomeState>(
                builder: (context, state) {
                  if (state.likeNewProducts.isNotEmpty) {
                    return ListView.builder(
                      padding: const EdgeInsets.only(left: 16),
                      scrollDirection: Axis.horizontal,
                      itemCount: state.likeNewProducts.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 16),
                          child: HorizonalProduct(
                            id: state.likeNewProducts[index].id,
                            image: state.likeNewProducts[index].imageUrl ?? '',
                            productName:
                                state.likeNewProducts[index].title ?? 'N/A',
                            price: state.likeNewProducts[index].price,
                            shopAddress:
                                state.likeNewProducts[index].location?.address,
                          ),
                        );
                      },
                    );
                  } else {
                    return const Padding(
                      padding: EdgeInsets.only(left: 16),
                      child: Text('Unable to load Products'),
                    );
                  }
                },
              ),
            ),
            Gap(40.h),
          ],
        ),
      ),
    );
  }
}
