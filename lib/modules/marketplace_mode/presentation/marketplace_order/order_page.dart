// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:multime_app/core/l10n/locale_keys.g.dart';

// class OrderPage extends StatelessWidget {
//   const OrderPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title:  Text(LocaleKeys.orderPage.tr()),
//       ),
//       body:  Center(
//         child: Text(LocaleKeys.orderPage.tr()),
//       ),
//     );
//   }
// }
