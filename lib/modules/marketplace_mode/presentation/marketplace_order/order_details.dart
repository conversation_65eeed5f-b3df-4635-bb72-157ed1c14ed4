// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:multime_app/core/constants/app_assets.dart';
// import 'package:multime_app/core/l10n/locale_keys.g.dart';
// import 'package:multime_app/shared/models/marketplace/product_fake_data.dart';
// import 'package:multime_app/modules/marketplace_mode/presentation/widgets/bottomsheet/cancel_bottomsheet.dart';
// import 'package:multime_app/modules/marketplace_mode/presentation/widgets/button/roundbutton.dart';

// class OrderDetailsScreen extends StatelessWidget {
//   const OrderDetailsScreen({super.key});
//   String limitText(String text, int maxLength) {
//     if (text.length > maxLength) {
//       return '${text.substring(0, maxLength)}...';
//     }
//     return text;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(LocaleKeys.orderDetailsTitle.tr()),
//         centerTitle: true,
//       ),
//       body: SingleChildScrollView(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Status and Order Info
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   LocaleKeys.orderDetailsHint.tr(),
//                   style: const TextStyle(
//                       fontSize: 18, fontWeight: FontWeight.bold),
//                 ),
//                 Container(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 12,
//                     vertical: 6,
//                   ),
//                   decoration: BoxDecoration(
//                     color: Colors.yellow[700],
//                     borderRadius: BorderRadius.circular(12),
//                   ),
//                   child: Text(
//                     LocaleKeys.awaitingApproval.tr(),
//                     style: TextStyle(
//                       color: Colors.white,
//                       fontWeight: FontWeight.bold,
//                       fontSize: 12.sp,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             Gap(16.h),
//             // Product Details
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Container(
//                   height: 80.h,
//                   width: 80.h,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(8),
//                     image: DecorationImage(
//                       image: NetworkImage(fakeDataProduct[0].image),
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 ),
//                 Gap(16.w),
//                 Expanded(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         "Samsonite freeform hardside expandable with double spinner wheels",
//                         style: TextStyle(
//                             fontWeight: FontWeight.normal, fontSize: 14.sp),
//                         maxLines: 3,
//                         overflow: TextOverflow.ellipsis,
//                       ),
//                       Gap(8.h),
//                       Text("x1",
//                           style:
//                               TextStyle(color: Colors.grey, fontSize: 12.sp)),
//                     ],
//                   ),
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.only(left: 16.0),
//                   child: Text("\$400", style: TextStyle(fontSize: 16.sp)),
//                 ),
//                 Gap(8.h),
//               ],
//             ),
//             Divider(
//               height: 32.h,
//               color: Colors.grey,
//             ),
//             // Seller Info
//             Padding(
//               padding: const EdgeInsets.symmetric(vertical: 4),
//               child: Row(
//                 children: [
//                   SvgPicture.asset(AppAssets.sellerAvatarSvg),
//                   Gap(16.w),
//                   Expanded(
//                     child: Text(
//                       LocaleKeys.sellerInfo.tr(),
//                       style: TextStyle(
//                           fontSize: 14.sp, fontWeight: FontWeight.normal),
//                     ),
//                   ),
//                   RoundedIconButton(
//                       text: LocaleKeys.contactSeller.tr(),
//                       icon: AppAssets.contactMailSvg,
//                       onPressed: () {})
//                 ],
//               ),
//             ),
//             Divider(
//               height: 32.h,
//               color: Colors.grey,
//             ),
//             // Shipping Info

//             Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Row(
//                   children: [
//                     SvgPicture.asset(AppAssets.locationMarkSvg),
//                     Gap(8.w),
//                     Text(
//                       LocaleKeys.sentFrom.tr(),
//                       style: TextStyle(
//                           fontWeight: FontWeight.normal, fontSize: 14.sp),
//                     ),
//                   ],
//                 ),
//                 Text(
//                   limitText(
//                       "Nr Vishal Complex, Pattharkuva, Relief Rd, Ahmedabad, Gujarat",
//                       35),
//                   style: TextStyle(
//                     fontSize: 14.sp,
//                     fontWeight: FontWeight.normal,
//                   ),
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text("Seller Name", style: TextStyle(fontSize: 14.sp)),
//                     Text(
//                       " (+84) 3456789",
//                       style: TextStyle(
//                         fontWeight: FontWeight.normal,
//                         fontSize: 14.sp,
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//             Gap(32.h),
//             Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Row(
//                   children: [
//                     SvgPicture.asset(AppAssets.truckFastSvg),
//                     Gap(8.w),
//                     Text(
//                       LocaleKeys.deliveredTo.tr(),
//                       style: TextStyle(
//                           fontWeight: FontWeight.normal, fontSize: 14.sp),
//                     ),
//                   ],
//                 ),
//                 Text(
//                   limitText(
//                       " 123 Luong Ngoc Quyen Str., Ha Dong, Hanoi,...", 35),
//                   style:
//                       TextStyle(fontSize: 14.sp, fontWeight: FontWeight.normal),
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       "Nguyen Van A",
//                       style: TextStyle(
//                         fontSize: 14.sp,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     Text(
//                       " (+84) 3456789",
//                       style: TextStyle(
//                           fontWeight: FontWeight.normal, fontSize: 14.sp),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//             Divider(
//               height: 32.h,
//               color: Colors.grey,
//             ),
//             // Order Timeline
//             Row(
//               children: [
//                 SvgPicture.asset(AppAssets.orderedSvg),
//                 Gap(8.w),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       LocaleKeys.orderPlaced.tr(),
//                       style: const TextStyle(fontWeight: FontWeight.normal),
//                     ),
//                     Text("Sep 24, 2024",
//                         style: TextStyle(
//                             fontWeight: FontWeight.normal, fontSize: 12.sp)),
//                   ],
//                 ),
//               ],
//             ),
//             Gap(16.h),
//             Row(
//               children: [
//                 SvgPicture.asset(AppAssets.shippingSvg),
//                 Gap(8.w),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       LocaleKeys.orderShipped.tr(),
//                       style: const TextStyle(fontWeight: FontWeight.normal),
//                     ),
//                     Text("Sep 24, 2024",
//                         style: TextStyle(
//                             fontWeight: FontWeight.normal, fontSize: 12.sp)),
//                   ],
//                 ),
//               ],
//             ),

//             Divider(
//               height: 32.h,
//               color: Colors.grey,
//             ),
//             // Payment Summary
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 8.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text("${LocaleKeys.subtotal.tr()} (2)",
//                           style: TextStyle(
//                               fontWeight: FontWeight.normal, fontSize: 14.sp)),
//                       Text("\$416.25",
//                           style: TextStyle(
//                               fontWeight: FontWeight.normal, fontSize: 14.sp)),
//                     ],
//                   ),
//                   Gap(8.h),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text(
//                         LocaleKeys.totalPayment.tr(),
//                         style: TextStyle(
//                             fontWeight: FontWeight.bold, fontSize: 16.sp),
//                       ),
//                       Text(
//                         "\$416.75",
//                         style: TextStyle(
//                           fontWeight: FontWeight.bold,
//                           fontSize: 16.sp,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//             Divider(
//               height: 32.h,
//               color: Colors.grey,
//             ),
//             // Order ID and Payment Details
//             Padding(
//               padding: EdgeInsets.symmetric(horizontal: 8.0.w),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text(
//                         LocaleKeys.orderID.tr(),
//                         style: TextStyle(
//                             fontSize: 14.sp, fontWeight: FontWeight.normal),
//                       ),
//                       Text(
//                         "#123546",
//                         style: TextStyle(
//                             fontSize: 14.sp, fontWeight: FontWeight.normal),
//                       )
//                     ],
//                   ),
//                   Gap(4.h),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text(
//                         LocaleKeys.orderDate.tr(),
//                         style: TextStyle(
//                             fontSize: 14.sp, fontWeight: FontWeight.normal),
//                       ),
//                       Text(
//                         "24/09/2024",
//                         style: TextStyle(
//                             fontSize: 14.sp, fontWeight: FontWeight.normal),
//                       )
//                     ],
//                   ),
//                   Gap(4.h),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Text(
//                         "${LocaleKeys.payment.tr()}:",
//                         style: TextStyle(
//                             fontSize: 14.sp, fontWeight: FontWeight.normal),
//                       ),
//                       Text(
//                         "mastercard • 1234",
//                         style: TextStyle(
//                             fontSize: 14.sp, fontWeight: FontWeight.normal),
//                       )
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//             Divider(
//               height: 32.h,
//               color: Colors.grey,
//             ),
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Padding(
//                   padding: const EdgeInsets.only(top: 4),
//                   child: SvgPicture.asset(AppAssets.infoDetailSvg),
//                 ),
//                 Gap(8.h),
//                 Expanded(
//                   // Sử dụng Expanded để Text chiếm hết không gian còn lại
//                   child: Text(
//                     "Keep in mind that you have until Nov 1, 2024 to approve this delivery or request a revision. After this date, the order will be finalized and marked as complete.",
//                     style: TextStyle(
//                       color: Colors.blue,
//                       fontSize: 12.sp,
//                       fontWeight: FontWeight.normal,
//                     ),
//                     softWrap: true, // Tự động xuống dòng
//                     overflow: TextOverflow.visible, // Không bị cắt bớt
//                   ),
//                 ),
//               ],
//             ),
//             Gap(24.h),
//             // Cancel Button
//             Container(
//               padding: const EdgeInsets.only(bottom: 16),
//               width: double.infinity,
//               child: ElevatedButton(
//                 onPressed: () {
//                   showCancelBottomSheet(context);
//                 },
//                 style: ElevatedButton.styleFrom(
//                   padding: const EdgeInsets.symmetric(vertical: 16),
//                   // backgroundColor: Theme.of(context).errorBase(context),
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(10), // Bo tròn khung
//                     side: const BorderSide(
//                       color: Colors.black54, // Màu khung
//                       width: 1, // Độ dày khung
//                     ),
//                   ),
//                 ),
//                 child: Text(
//                   LocaleKeys.cancelOrder.tr(),
//                   style: const TextStyle(color: Colors.black54),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
