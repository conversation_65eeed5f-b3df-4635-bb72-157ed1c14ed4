import 'package:flutter/material.dart';

class CustomExpansionTile extends StatefulWidget {
  final Widget title;
  final Widget trailing;
  final List<Widget> children;

  const CustomExpansionTile({
    required this.title,
    required this.trailing,
    required this.children,
    super.key,
  });

  @override
  // ignore: library_private_types_in_public_api
  _CustomExpansionTileState createState() => _CustomExpansionTileState();
}

class _CustomExpansionTileState extends State<CustomExpansionTile> {
  bool _isExpanded = false;

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: _toggleExpansion,
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: Row(
              children: [
                Expanded(child: widget.title), // Đ<PERSON><PERSON> tiêu đề về bên tr<PERSON><PERSON>(
                  alignment: Alignment.bottomRight, // <PERSON>ă<PERSON> chỉnh về bên phải
                  child: widget.trailing,
                ),
              ],
            ),
          ),
        ),
        if (_isExpanded)
          Column(
            children: widget.children,
          ),
      ],
    );
  }
}
