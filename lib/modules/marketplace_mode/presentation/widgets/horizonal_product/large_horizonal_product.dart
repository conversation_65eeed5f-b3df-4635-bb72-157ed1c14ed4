import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';

class LargeHorizonalProduct extends StatelessWidget {
  final Product? products;
  final String image;
  final String productName;
  final num price;
  final String? shopAddress;
  final VoidCallback? onTap;
  final bool? isFav;
  final String? id;

  const LargeHorizonalProduct({
    super.key,
    this.products,
    required this.image,
    required this.productName,
    required this.price,
    this.shopAddress,
    this.onTap,
    this.isFav,
    this.id,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => context.push(RouteName.productDetailScreen, extra: id),
      child: Container(
        width: 220.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.greyScale100(context),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageSection(context),
            Gap(10.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Text(
                productName,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Gap(8.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Text(
                shopAddress ?? 'N/A',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey,
                ),
              ),
            ),
            Gap(8.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                children: [
                  Text(
                    NumberFormat.simpleCurrency(decimalDigits: 0).format(price),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection(BuildContext context) {
    final theme = Theme.of(context);

    if (image.isEmpty) {
      return SizedBox(
        height: 130.h,
        width: double.infinity,
        child: Icon(
          Icons.image,
          color: theme.greyScale700(context),
          size: 50.sp,
        ),
      );
    }

    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          child: Image.network(
            image,
            height: 130.h,
            width: double.infinity,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return SizedBox(
                height: 130.h,
                width: double.infinity,
                child: Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            (loadingProgress.expectedTotalBytes!)
                        : null,
                  ),
                ),
              );
            },
          ),
        ),
        Positioned(
          top: 8,
          left: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '15% OFF',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: CircleAvatar(
            radius: 16,
            backgroundColor: AppColors.whitePrimary,
            child: IconButton(
              onPressed: () {}, // TODO: Add favorite logic
              icon: Icon(
                CupertinoIcons.heart,
                size: 18.sp,
                color: AppColors.blackPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
