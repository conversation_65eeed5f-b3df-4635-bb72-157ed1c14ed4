import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_model_fake.dart';

class HorizonalProduct extends StatelessWidget {
  final Product? products;
  final String image;
  final String productName;
  final num price;
  final String? shopAddress;
  final VoidCallback? onTap;
  final bool? isFav;
  final String? id;

  const HorizonalProduct({
    super.key,
    this.products,
    required this.image,
    required this.productName,
    required this.price,
    this.shopAddress,
    this.isFav,
    this.onTap,
    this.id,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return GestureDetector(
          onTap: () {
            context.push(RouteName.productDetailScreen, extra: id);
          },
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: 170.w,
              minWidth: 140.w,
              maxHeight: 230.h,
            ),
            child: Container(
              decoration: BoxDecoration(
                // color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  width: 1,
                  color: Theme.of(context).greyScale100(context),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Image with Overlay
                  Stack(
                    children: [
                      ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                        child: image.isEmpty
                            ? SizedBox(
                                height: 120.h,
                                width: double.infinity,
                                child: Icon(
                                  Icons.image,
                                  color:
                                      Theme.of(context).greyScale700(context),
                                  size: 40.sp,
                                ),
                              )
                            : Image.network(
                                image,
                                height: 130.h,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                loadingBuilder: (BuildContext context,
                                    Widget child,
                                    ImageChunkEvent? loadingProgress) {
                                  if (loadingProgress == null) {
                                    return child;
                                  } else {
                                    return SizedBox(
                                      height: 130.h,
                                      width: double.infinity,
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          value: loadingProgress
                                                      .expectedTotalBytes !=
                                                  null
                                              ? loadingProgress
                                                      .cumulativeBytesLoaded /
                                                  (loadingProgress
                                                          .expectedTotalBytes ??
                                                      1)
                                              : null,
                                        ),
                                      ),
                                    );
                                  }
                                },
                              ),
                      ),
                      // Discount Badge
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '15% OFF',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 11.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      // Favorite Icon
                      Positioned(
                        top: 8,
                        right: 8,
                        child: CircleAvatar(
                          radius: 15,
                          backgroundColor: AppColors.whitePrimary,
                          child: IconButton(
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                              onPressed: onTap ?? () {},
                              icon: (isFav ?? false) == false
                                  ? Icon(
                                      CupertinoIcons.heart,
                                      size: 16.sp,
                                      color: AppColors.blackPrimary,
                                    )
                                  : Icon(
                                      CupertinoIcons.heart_fill,
                                      size: 16.sp,
                                      color: Colors.red,
                                    )),
                        ),
                      ),
                    ],
                  ),
                  Gap(8.h),
                  // Product Name
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      productName,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Gap(6.h),
                  // Address and Date
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      shopAddress ?? 'N/A',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                  Gap(6.h),
                  // Pricing
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      children: [
                        Flexible(
                          child: Text(
                            NumberFormat.simpleCurrency(decimalDigits: 0)
                                .format(price),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
