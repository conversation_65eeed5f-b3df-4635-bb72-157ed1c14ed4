import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RoundedIconButton extends StatelessWidget {
  final String text;
  final String icon;
  final VoidCallback onPressed;

  const RoundedIconButton({
    super.key,
    required this.text,
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: SvgPicture.asset(
        icon,
        // height: 20.h, // Đặt kích thước của icon
        // width: 20.w,
      ), // Icon SVG đứng trước
      label: Text(
        text,
        style: TextStyle(
          fontSize: 12.sp,
          color: Colors.blue, // Màu chữ
          fontWeight: FontWeight.normal,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent, // Màu nền trong suốt
        elevation: 0, // Bỏ hiệu ứng đổ bóng
        foregroundColor: Colors.blue, // Màu icon
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30), // Bo tròn khung
          side: const BorderSide(
            color: Colors.blue, // Màu khung
            width: 1, // Độ dày khung
          ),
        ),
      ),
    );
  }
}
