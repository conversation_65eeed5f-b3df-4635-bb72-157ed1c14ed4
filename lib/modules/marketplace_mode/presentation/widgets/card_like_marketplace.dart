import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardLikeMarketplace extends StatelessWidget {
  const CardLikeMarketplace(
      {super.key,
      required this.id,
      required this.imageProduct,
      required this.nameProduct,
      required this.locationProduct,
      required this.priceProduct,
      required this.dateProduct,
      required this.authorImage,
      required this.authorName});
  final String id,
      imageProduct,
      nameProduct,
      locationProduct,
      dateProduct,
      authorImage,
      authorName;
  final num priceProduct;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.push(RouteName.productDetailScreen, extra: id),
      child: Container(
        padding: const EdgeInsets.only(bottom: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hình ảnh sản phẩm
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CustomImage(
                path: imageProduct, // Link ảnh placeholder
                width: 120.w,
                height: 120.h,
                fit: BoxFit.cover,
                imageType: ImageType.network,
              ),
            ),
            Gap(12.w),
            // Thông tin sản phẩm
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  //Gap(4.h),
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(50.r),
                        child: CustomImage(
                          path: authorImage, // Link ảnh placeholder
                          width: 25.w,
                          height: 25.w,
                          fit: BoxFit.cover,
                          imageType: ImageType.network,
                          size: 15.sp,
                        ),
                      ),
                      Gap(8.w),
                      Text(
                        authorName,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 10.sp,
                        ),
                      ),
                    ],
                  ),
                  // Gap(4.h),
                  Text(
                    nameProduct,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                    ),
                  ),
                  //Gap(4.h),
                  Text(
                    locationProduct,
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 10.sp,
                    ),
                  ),
                  //  Gap(8.h),
                  Text(
                    "\$$priceProduct",
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Nút thêm/bớt số lượng
            Column(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.black.withOpacity(0.1),
                  child: IconButton(
                    onPressed: () {
                      // Xử lý khi nhấn nút yêu thích
                    },
                    icon: Icon(
                      CupertinoIcons.suit_heart_fill,
                      size: 18.sp,
                      color: Colors.red,
                    ),
                  ),
                ),
                Text(
                  dateProduct,
                  style: TextStyle(fontSize: 10.sp, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
