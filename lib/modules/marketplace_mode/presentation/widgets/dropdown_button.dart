import 'package:flutter/material.dart';

class ActionDropdown extends StatefulWidget {
  final String hintText; // Gợi ý khi chưa chọn
  final List<String> items; // <PERSON>h sách các mục
  final ValueChanged<String?> onChanged; // Hàm callback khi thay đổi giá trị

  const ActionDropdown({
    super.key,
    required this.hintText,
    required this.items,
    required this.onChanged,
  });

  @override
  State<ActionDropdown> createState() => _ActionDropdownState();
}

class _ActionDropdownState extends State<ActionDropdown> {
  String? _selectedValue;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: _selectedValue,
      decoration: InputDecoration(
        labelText: widget.hintText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      items: widget.items
          .map(
            (item) => DropdownMenuItem<String>(
              value: item,
              child: Text(
                item,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          )
          .toList(),
      onChanged: (value) {
        setState(() {
          _selectedValue = value;
        });
        widget.onChanged(value); // Gọi callback khi thay đổi giá trị
      },
      dropdownColor: Colors.white,
      icon: const Icon(Icons.keyboard_arrow_down),
      isExpanded: true, // Chiếm hết chiều rộng
    );
  }
}
