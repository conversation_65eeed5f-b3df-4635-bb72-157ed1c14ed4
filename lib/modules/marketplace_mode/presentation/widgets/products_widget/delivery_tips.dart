import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';

class DeliveryAndSafetyTips extends StatelessWidget {
  DeliveryAndSafetyTips({super.key});
  final DateTime today = DateTime.now();
  final DateTime fiveDaysLater = DateTime.now().add(const Duration(days: 5));

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.estDelivery.tr(),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[500],
                  fontWeight: FontWeight.normal,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
              Text(
                "${DateFormat('E, MMM d').format(today)} - ${DateFormat('E, MMM d').format(fiveDaysLater)}",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
            ],
          ),
          Gap(16.h),
          Row(
            children: [
              SvgPicture.asset(AppAssets.likeSvg),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  LocaleKeys.safePurchase.tr(),
                  style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.normal,
                      color: Colors.grey[800],
                      fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
                ),
              ),
            ],
          ),
          Gap(16.h),
          Row(
            children: [
              SvgPicture.asset(AppAssets.discountSvg),
              Gap(8.w),
              Expanded(
                child: Text.rich(
                  TextSpan(
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[800],
                      fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                    ),
                    children: [
                      TextSpan(
                        text: LocaleKeys.thereWill.tr(),
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                      TextSpan(
                        text: "3%",
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).errorBase(context),
                        ),
                      ),
                      TextSpan(
                        text: LocaleKeys.chargeRate.tr(),
                        style: TextStyle(
                          fontWeight: FontWeight.normal,
                          fontSize: 12.sp,
                        ),
                      ),
                      TextSpan(
                        text: "> \$1000",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).errorBase(context),
                          fontSize: 12.sp,
                        ),
                      ),
                      TextSpan(
                        text: " \n${LocaleKeys.thereWill.tr()} ",
                        style: TextStyle(
                          fontWeight: FontWeight.normal,
                          fontSize: 12.sp,
                        ),
                      ),
                      TextSpan(
                        text: "5%",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).errorBase(context),
                          fontSize: 12.sp,
                        ),
                      ),
                      TextSpan(
                        text: LocaleKeys.chargeRate.tr(),
                        style: TextStyle(
                          fontWeight: FontWeight.normal,
                          fontSize: 12.sp,
                        ),
                      ),
                      TextSpan(
                        text: "< \$999",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).errorBase(context),
                          fontSize: 12.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
