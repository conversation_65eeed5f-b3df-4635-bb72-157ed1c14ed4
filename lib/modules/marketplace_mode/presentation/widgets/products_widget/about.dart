import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';

class AboutThisItem extends StatefulWidget {
  final String condition;
  final String type;
  final String description;

  const AboutThisItem({
    super.key,
    required this.condition,
    required this.type,
    required this.description,
  });

  @override
  State<AboutThisItem> createState() => _AboutThisItemState();
}

class _AboutThisItemState extends State<AboutThisItem> {
  bool _isExpanded = false;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(LocaleKeys.aboutThisItem.tr(),
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily)),

          ///condition of product
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(LocaleKeys.condition.tr(),
                  style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      fontWeight: FontWeight.normal,
                      fontFamily: GoogleFonts.plusJakartaSans().fontFamily)),
              Text(widget.condition,
                  style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                      fontFamily: GoogleFonts.plusJakartaSans().fontFamily)),
            ],
          ),

          /// type of product
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(LocaleKeys.type.tr(),
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      color: Colors.grey,
                      fontFamily: GoogleFonts.plusJakartaSans().fontFamily)),
              Text(widget.type,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                        color: Theme.of(context).blackPrimary(context),
                      )),
            ],
          ),
          const SizedBox(height: 8),

          const SizedBox(height: 16),
          Text(LocaleKeys.description.tr(),
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily)),
          const SizedBox(height: 8),

          /// description of product
          Text(
            _isExpanded
                ? widget.description
                : widget.description.length > 100
                    ? "${widget.description.substring(0, 100)}..."
                    : widget.description,
            style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[800],
                fontWeight: FontWeight.normal,
                fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
          ),
          if (widget.description.length > 100)
            Center(
              child: TextButton.icon(
                icon: _isExpanded
                    ? Icon(
                        CupertinoIcons.control,
                        color: Theme.of(context).errorBase(context),
                        size: 16.sp,
                      )
                    : Icon(
                        CupertinoIcons.chevron_down,
                        color: Theme.of(context).errorBase(context),
                        size: 16.sp,
                      ),
                onPressed: () {
                  setState(() {
                    _isExpanded = !_isExpanded; // Toggle the expanded state
                  });
                },
                label: Text(
                  _isExpanded ? LocaleKeys.hide.tr() : LocaleKeys.show.tr(),
                  style: TextStyle(
                    color: Theme.of(context).errorBase(context),
                    decoration: TextDecoration.underline,
                    fontSize: 14,
                    fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
