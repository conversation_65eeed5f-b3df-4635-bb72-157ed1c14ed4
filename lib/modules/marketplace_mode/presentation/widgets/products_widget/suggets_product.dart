import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product_detail/bloc/product_detail_bloc.dart';
import 'package:multime_app/modules/news_mode/presentation/widgets/home_page/session_heading.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';


class RelatedProducts extends StatelessWidget {
  const RelatedProducts({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProductDetailBloc, ProductDetailState>(
      listener: (context, state) {
        if (state.similarProduct.status == Status.loading) {
          AppLoader.show(context);
        } else {
          AppLoader.hide();
        }
      },
      builder: (context, state) {
        print('similar products');

        if (state.similarProduct.status == Status.completed) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TSectionHeading(
                  title: 'More like this',
                  buttonTitle: 'See More',
                  textColor: Theme.of(context).errorBase(context),
                ),
                state.similarProduct.data!.products.isEmpty
                    ? const Center(
                        child: Text('No similar products available currently.'))
                    : SizedBox(
                        height: 250.h,
                        child: ListView.builder(
                            itemCount:
                                state.similarProduct.data!.products.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              var product =
                                  state.similarProduct.data!.products[index];
                              return _buildProductCard(
                                  context,
                                  product.title ?? '',
                                  "\$ ${product.price % 1 == 0 ? product.price.toInt() : product.price}",
                                  product.imageUrl ?? '',
                                  product.location?.address ?? 'N/A');
                            }),
                      ),
              ],
            ),
          );
        } else if (state.similarProduct.status == Status.error) {
          return const Center(
              child: Text('Unable to load similar products data'));
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildProductCard(BuildContext context, String title, String price,
      String img, String address) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: Container(
        width: 160.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).lightGrey(context),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hình ảnh sản phẩm
            img.isEmpty
                ? ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(10)),
                    child: SizedBox(
                      height: 155.h,
                      width: double.infinity,
                      child: Icon(
                        Icons.image,
                        size: 25.sp,
                        color: Theme.of(context).greyScale700(context),
                      ),
                    ),
                  )
                : Stack(
                    children: [
                      ClipRRect(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        child: Image.network(
                          img, // Thay bằng hình ảnh của bạn
                          height: 155.h,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        top: 8,
                        right: 8,
                        child: CircleAvatar(
                          radius: 16,
                          backgroundColor: Colors.grey.withOpacity(0.5),
                          child: IconButton(
                            onPressed: () {},
                            icon: Icon(
                              CupertinoIcons.suit_heart,
                              size: 18.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          height: 24,
                          width: 39,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Center(
                            child: Text(
                              'New',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
            SizedBox(height: 8.h),
            // Tên sản phẩm
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 15,
                        width: 120,
                        child: Text(
                          address,
                          style: TextStyle(fontSize: 10.sp),
                        ),
                      ),
                      // Text(
                      //   formatDate(DateT), // Hiển thị ngày định dạng
                      //   style: TextStyle(
                      //     fontSize: 10.sp,
                      //     color: Colors.grey,
                      //     fontWeight: FontWeight.bold,
                      //   ),
                      // ),
                    ],
                  ),
                  Text(
                    " ${price}",
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Theme.of(context).primary(context),
                        ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
