import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../../../../core/constants/app_assets.dart';
import '../show_more_market_place.dart';

class ProductImageCarousel extends StatelessWidget {
  ProductImageCarousel({super.key, required this.images});

  final PageController _controller = PageController();

  final List<String> images;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        images.isNotEmpty
            ?
            // Carousel Image Slider
            SizedBox(
                height: MediaQuery.of(context).size.height * 0.4,
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16), // Rounded corners
                    topRight: Radius.circular(16),
                  ),
                  child: PageView.builder(
                    controller: _controller,
                    itemCount: images.length,
                    onPageChanged: (index) {
                      // setState(() {}); // Cập nhật trạng thái nếu cần
                    },
                    itemBuilder: (context, index) {
                      return Image.network(
                        images[index],
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      (loadingProgress.expectedTotalBytes ?? 1)
                                  : null,
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              )
            : SizedBox(
                height: MediaQuery.of(context).size.height * 0.4,
                child: Center(
                  child: Icon(
                    Icons.image,
                    size: 60.sp,
                    color: Theme.of(context).greyScale700(context),
                  ),
                ),
              ),
        Positioned(
          top: 48,
          left: 8,
          child: CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).whitePrimary(context),
            child: IconButton(
              icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
              onPressed: () {
                context.pop();
                // Handle share action
              },
            ),
          ),
        ),
        // "Favorite" button
        Positioned(
          top: 48,
          right: 8,
          child: CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).whitePrimary(context),
            child: IconButton(
              icon: SvgPicture.asset(AppAssets.moreOptionSvg),
              onPressed: () {
                showMoreOptions(context);
                // Handle share action
              },
            ),
          ),
        ),
        Positioned(
          top: 48,
          right: 56,
          child: CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).whitePrimary(context),
            child: IconButton(
              onPressed: () {
                // Xử lý khi nhấn nút yêu thích
              },
              icon: Icon(
                CupertinoIcons.suit_heart,
                size: 18.sp,
                color: Colors.black,
              ),
            ),
          ),
        ),

        // Smooth Page Indicator
        if (images.isNotEmpty)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Center(
              child: SmoothPageIndicator(
                controller: _controller,
                count: images.length,
                effect: ExpandingDotsEffect(
                  dotHeight: 8.0,
                  dotWidth: 8.0,
                  activeDotColor: Colors.white,
                  dotColor: Colors.grey.shade400,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
