import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';

class SellerInfo extends StatelessWidget {
  const SellerInfo({super.key, this.sellerName});
  final String? sellerName;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      color: Theme.of(context).greyScale200(context),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: Theme.of(context).secondaryBase(context),
                child: SvgPicture.asset(AppAssets.sellerAvatarSvg),
              ),
              const SizedBox(width: 8),
              Text(
                sellerName ?? 'Seller Name: N/A',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
            ],
          ),
          ElevatedButton(
            onPressed: () {
              // Handle contact action
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).errorBase(context),
            ),
            child: Row(
              children: [
                const Icon(Icons.mail, color: Colors.white),
                Text(LocaleKeys.contactMe.tr(),
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontFamily: GoogleFonts.plusJakartaSans().fontFamily)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
