import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class AddressCard extends StatelessWidget {
  final String name;
  final String phone;
  final String address;
  final String? city;
  final String? state;
  final int? country;
  final bool isDefault;
  final void Function()? onDeleteAddress;
  final void Function()? onEditAddress;

  const AddressCard({
    super.key,
    required this.name,
    required this.phone,
    required this.address,
    this.onDeleteAddress,
    this.onEditAddress,
    this.city,
    this.country,
    this.state,
    this.isDefault = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.only(
          left: 12.0,
          right: 12,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        name,
                        style: Theme.of(context).textTheme.lightBodyLargeBold,
                      ),
                      Gap(8.h),
                      Text(
                        " |   (+84)$phone",
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular,
                      ),
                      Gap(10.w),
                      GestureDetector(
                        onTap: onEditAddress,
                        child: SvgPicture.asset(AppAssets.penMessageSvg),
                      )
                    ],
                  ),
                  Gap(8.h),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        address,
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              "${city ?? ''}",
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumRegular,
                              softWrap: true,
                            ),
                          ),
                          GestureDetector(
                            onTap: onDeleteAddress,
                            child: Icon(
                              Icons.delete,
                              size: 20.sp,
                              color: Theme.of(context).errorBase(context),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (isDefault)
                    Container(
                      width: 70,
                      height: 30,
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .errorBase(context)
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: Text(
                          LocaleKeys.defaultText.tr(),
                          style: TextStyle(
                            color: Theme.of(context).errorBase(context),
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  const Gap(10),
                  Divider(
                    color: Colors.grey[300],
                    thickness: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
