import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';

class ProductTitleAndPrice extends StatelessWidget {
  final String title;
  final String location;
  final double price;
  final String created;

  const ProductTitleAndPrice({
    super.key,
    required this.title,
    required this.location,
    required this.price,
    required this.created,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
            ),
          ),
          SizedBox(height: 16.h),
          // Location
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(AppAssets.location1MarkSvg),
              Gap(8.w),
              Text(
                location,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
            ],
          ),
          Gap(8.h),
          // Created
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(AppAssets.onlineTimeSvg),
              Gap(8.w),
              Text(
                '${LocaleKeys.createDate.tr()}: $created',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
            ],
          ),
          Gap(8.h),
          // Price
          Row(
            children: [
              Text(
                "\$ ${price % 1 == 0 ? price.toInt() : price}",
                style: TextStyle(
                  fontSize: 24.sp,
                  color: Theme.of(context).errorBase(context),
                  fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                ),
              ),
              Gap(8.w),
              Icon(
                Icons.check_circle,
                color: Theme.of(context).successBase(context),
                size: 20.sp,
              ),
              Text(
                LocaleKeys.shippingPrice.tr(),
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: Theme.of(context).successBase(context),
                    ),
              ),
            ],
          ),
          Text(
            LocaleKeys.contactText.tr(),
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.normal,
              fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
            ),
          ),
        ],
      ),
    );
  }
}
