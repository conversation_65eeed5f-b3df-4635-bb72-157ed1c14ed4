import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/l10n/locale_keys.g.dart';

class NotificationWarningCard extends StatefulWidget {
  const NotificationWarningCard({super.key});

  @override
  _NotificationWarningCardState createState() =>
      _NotificationWarningCardState();
}

class _NotificationWarningCardState extends State<NotificationWarningCard> {
  bool _isVisible = true;

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).warningLight(context),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgPicture.asset(AppAssets.warningNoteSvg),
                Gap(8.w),
                Expanded(
                  // Đảm bảo nội dung văn bản nằm trong chiều rộng còn lại
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.note.tr(),
                        style: TextStyle(
                          color: Theme.of(context).errorBase(context),
                          fontSize: 12.sp,
                          fontWeight: FontWeight.normal,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      Gap(8.h),
                      RichText(
                        text: TextSpan(
                          style:
                              TextStyle(color: Colors.black, fontSize: 12.sp),
                          children: [
                            TextSpan(
                              text: LocaleKeys.multiMe.tr(),
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.normal,
                                fontSize: 12.sp,
                              ),
                            ),
                            TextSpan(
                              text: LocaleKeys.doesNot.tr(),
                              style: TextStyle(
                                color: Theme.of(context).errorBase(context),
                                fontWeight: FontWeight.bold,
                                fontSize: 12.sp,
                              ),
                            ),
                            TextSpan(
                              text: LocaleKeys.allowDepositPrivate.tr(),
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.normal,
                                fontSize: 12.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Gap(8.h),
                      Text(
                        LocaleKeys.pleaseOnlyBuySell.tr(),
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isVisible = false;
                });
              },
              child: Icon(CupertinoIcons.multiply_circle,
                  size: 16.sp, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}
