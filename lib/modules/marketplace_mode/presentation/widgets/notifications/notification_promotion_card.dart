import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_font_family.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';

class PostNotification extends StatefulWidget {
  const PostNotification({super.key});

  @override
  _PromotionCardState createState() => _PromotionCardState();
}

class _PromotionCardState extends State<PostNotification> {
  bool _isVisible = true;

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: <PERSON>ack(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).lightGrey(context),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.gotUnusedItem.tr(),
                  style: Theme.of(context).textTheme.lightHeadingSmall.copyWith(
                        fontSize: 20.sp,
                        fontFamily: AppFontFamily.medium,
                      ),
                ),
                Gap(8.h),
                Text(
                  LocaleKeys.turnThemIntoCash.tr(),
                  style: Theme.of(context).textTheme.lightBodyMediumRegular,
                ),
                Gap(24.h),
                CustomeButtonDating(
                  onTap: () {},
                  text: LocaleKeys.postForSale.tr(),
                  colorBackground: Theme.of(context).secondary(context),
                  colorText: Colors.white,
                  height: 45.h,
                  width: double.infinity,
                ),
              ],
            ),
          ),
          Positioned(
            top: 20,
            right: 16,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isVisible = false;
                });
              },
              child: Icon(
                Icons.close_rounded,
                size: 20.sp,
                color: Theme.of(context).secondaryBase(context),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
