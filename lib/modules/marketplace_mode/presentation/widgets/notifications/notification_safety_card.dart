import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';

class NotificationSafetyCard extends StatefulWidget {
  const NotificationSafetyCard({super.key});

  @override
  State<NotificationSafetyCard> createState() => _NotificationSafetyCardState();
}

class _NotificationSafetyCardState extends State<NotificationSafetyCard> {
  bool _isVisible = true; // Trạng thái hiển thị của widget

  @override
  Widget build(BuildContext context) {
    return _isVisible
        ? Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Stack(
              children: [
                // Container chính
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      SvgPicture.asset(AppAssets.likeSvg),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.only(right: 10),
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: LocaleKeys.itsTotally.tr(),
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w300),
                                ),
                                TextSpan(
                                  text: LocaleKeys.safe.tr(),
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextSpan(
                                  text: LocaleKeys.toPurchaseAtMultiMe.tr(),
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w300),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Nút đóng ở góc phải
                Positioned(
                  top: 0,
                  right: 0,
                  child: IconButton(
                    onPressed: () {
                      // Khi nhấn nút close, ẩn widget
                      setState(() {
                        _isVisible = false;
                      });
                    },
                    icon: Icon(
                      CupertinoIcons.multiply_circle,
                      size: 16.sp,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
          )
        : const Gap(0); // Trả về SizedBox rỗng khi widget bị ẩn
  }
}
