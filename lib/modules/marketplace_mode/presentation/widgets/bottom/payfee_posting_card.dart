import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';

class PayfeePostingCard extends StatelessWidget {
  const PayfeePostingCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 1.w),
        borderRadius: BorderRadius.circular(16.r),
      ),
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          // Icon bên trái
          SizedBox(
            width: 53.w,
            height: 53.h,
            child:SvgPicture.asset(AppAssets.premiumSvg),
          ),
          Gap(14.w),

          // Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.upgradeToPro.tr(),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // Gap(4.h),
                Text(
                  LocaleKeys.postFreeAds.tr(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                Text(
                  LocaleKeys.adTypeAds.tr(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.normal,
                  ),
                ),
                Text(
                  LocaleKeys.appliesToAllPosts.tr(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
          Gap(16.w),

          Icon(
            CupertinoIcons.chevron_right,
            color: Colors.black,
            size: 24.w,
          ),
        ],
      ),
    );
  }
}
