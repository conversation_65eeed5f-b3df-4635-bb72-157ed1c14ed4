import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/bottom/free_posting_card.dart';

import 'package:multime_app/modules/marketplace_mode/presentation/widgets/bottom/payfee_posting_card.dart';

class CustomBottomSheetContent extends StatelessWidget {
  const CustomBottomSheetContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 26),
      width: double.infinity,
      height: 600.h,
      // color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                child: const Icon(Icons.close),
                onTap: () => context.pop(),
              ),
              Text(
                LocaleKeys.selectPostsFrom.tr(),
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
              ),
              const Gap(10),
            ],
          ),
          Gap(30.h),

          // Offers Section
          Text(
            LocaleKeys.offersFrom.tr(),
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
          ),
          Gap(8.h),
          const FreePostingCard(),
          Gap(8.h),
          Text(
            LocaleKeys.payFee.tr(),
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
          ),
          Gap(8.h),
          const PayfeePostingCard(),

          // Apply Button
          const Spacer(),

          Container(
            padding: const EdgeInsets.only(bottom: 28),
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                context.pop();
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 14),
                backgroundColor: Theme.of(context).errorBase(context),
              ),
              child: Text(
                LocaleKeys.apply.tr(),
                style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
