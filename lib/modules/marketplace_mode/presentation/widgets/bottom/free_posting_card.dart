import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';

class FreePostingCard extends StatelessWidget {
  const FreePostingCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).attentionBase(context),
          width: 1.w,
        ),

        borderRadius: BorderRadius.circular(12.r), // Bo góc
      ),
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          // Icon bên trái
          SizedBox(
            width: 53.w,
            height: 53.h,
            child: SvgPicture.asset(AppAssets.freeSvg,  width: 53.w,
              height: 53.h,fit: BoxFit.cover,),
          ),
          Gap(14.w),

          // Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.freePosting.tr(),
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).primary(context)
                      ),
                ),
                // Gap(4.h),
                Text(
                  LocaleKeys.adType.tr(),
                   style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).primary(context),
                ),
                ),
              ],
            ),
          ),
          Gap(16.w),

          // Badge (X10)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).attentionBase(context), width: 2.w),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Text(
              'X10',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).attentionDark(context),
                fontSize: 14.sp
              ),
            ),
          ),
        ],
      ),
    );
  }
}
