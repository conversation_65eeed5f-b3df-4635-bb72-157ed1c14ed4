import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';

void showMoreOptions(BuildContext context) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    shape: const RoundedRectangleBorder(

      borderRadius: BorderRadius.vertical(
        top: Radius.circular(16),
      ),
    ),
    builder: (BuildContext context) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 16, left: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Tạo thanh kéo
            Container(
              height: 4.h,
              width: 60.w,
              margin: const EdgeInsets.only(top: 8, bottom: 4),
              decoration: BoxDecoration(
                color: Colors.grey[500],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Icon(CupertinoIcons.xmark_circle, size: 24.sp,color: Theme.of(context).textPrimary(context),),
              title: Text(
                LocaleKeys.notInterested.tr(),
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              onTap: () {
                // Xử lý khi chọn 'Not interested'
                context.pop();
              },
            ),
            ListTile(
              leading: Icon(CupertinoIcons.person_crop_circle_badge_xmark,
                  size: 24.sp,
                color: Theme.of(context).textPrimary(context),
              ),
              title: Text(
                LocaleKeys.noRecommendSeller.tr(),
               style: Theme.of(context).textTheme.bodyLarge,
              ),
              onTap: () {
                // Xử lý khi chọn 'Don't recommend'
                context.pop();
              },
            ),
            ListTile(
              leading:  Icon(CupertinoIcons.flag, size: 24,color: Theme.of(context).textPrimary(context),),
              title: Text(
                LocaleKeys.reportSeller.tr(),
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              onTap: () {
                // Xử lý khi chọn 'Report this seller'
                context.pop();
              },
            ),
          ],
        ),
      );
    },
  );
}