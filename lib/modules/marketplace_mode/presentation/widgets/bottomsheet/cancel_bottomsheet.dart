import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

void showCancelBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Nút "Cancel"
          Container(
            decoration: const BoxDecoration(
              color: Color.fromRGBO(246, 245, 245, 1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Align(
              alignment: Alignment.topLeft,
              child: TextButton(
                onPressed: () {
                  context.pop(); // Đóng Bottom Sheet
                },
                child: Text(
                  "Cancel",
                  style: TextStyle(
                      color: Colors.blue, // Màu chữ trắng
                      fontSize: 14.sp,
                      fontWeight: FontWeight.normal // <PERSON>ích thước chữ
                      ),
                ),
              ),
            ),
          ),
          Gap(8.h),
          // Nội dung thông báo
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              "You have to wait seller to confirm and you receive money/product",
              style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                  fontWeight: FontWeight.normal),
            ),
          ),
          Gap(16.h),
          // Tiêu đề "Reason for cancel?"
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              "Reason for cancel?",
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
            ),
          ),
          Gap(16.h),
          // Ô nhập lý do
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100, // Màu nền
                    borderRadius: BorderRadius.circular(16), // Bo tròn 4 góc
                  ),
                  child: TextFormField(
                    decoration: InputDecoration(
                      hintText: "Enter reason refusal",
                      hintStyle: TextStyle(
                        color: Colors.grey, // Màu chữ gợi ý
                        fontSize: 14.sp,
                      ),
                      border: InputBorder.none, // Loại bỏ viền
                      enabledBorder:
                          InputBorder.none, // Loại bỏ viền khi không focus
                      focusedBorder: InputBorder.none, // Loại bỏ viền khi focus
                      contentPadding: const EdgeInsets.only(
                        top: 8, // Đặt hintText sát góc trên
                        left: 8, // Khoảng cách từ mép trái
                      ),
                    ),
                    maxLines: 5, // Cho phép nhiều dòng
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 10, // Đặt icon ở gần đáy
                  left: 10, // Đặt icon gần mép trái
                  child: GestureDetector(
                    onTap: () {
                      // Xử lý khi người dùng chọn hình ảnh
                    },
                    child: Icon(
                      Icons.image, // Biểu tượng hình ảnh
                      color: Colors.black54, // Màu icon
                      size: 24.sp, // Kích thước icon
                    ),
                  ),
                ),
              ],
            ),
          ),

          Gap(20.h),
          // Nút "Cancel offer"
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Logic hủy
                  context.pop(); // Đóng bottom sheet
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  "Cancel offer",
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
          Gap(40.h),
        ],
      );
    },
  );
}
