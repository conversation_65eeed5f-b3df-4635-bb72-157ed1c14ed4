import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/create_order_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/fee_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/payment_intent_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_detail_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/payment_repository.dart';
part 'payment_event.dart';
part 'payment_state.dart';

class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  PaymentRepository paymentRepository;
  PaymentBloc({required this.paymentRepository})
      : super(PaymentState.initial()) {
    on<SetProductDetails>(_onSetProductDetails);
    on<CalculateFee>(_onCalculateFee);
    on<CreatePaymentIntent>(_onCreatePaymentIntent);
    on<CreateOrderEvent>(_onCreateOrderEvent);
    on<SetAddressDetails>(_onSetAddressDetails);
  }

  void _onSetProductDetails(
    SetProductDetails event,
    Emitter<PaymentState> emit,
  ) async {
    emit(state.copyWith(productDetail: event.productDetails));
    add(CalculateFee(
        amount: event.productDetails.price, referenceType: 'product'));
  }

  void _onCalculateFee(
    CalculateFee event,
    Emitter<PaymentState> emit,
  ) async {
    emit(state.copyWith(feeResponse: const ApiResponse.loading()));
    try {
      var response = await paymentRepository.calculateFee(event);
      emit(state.copyWith(feeResponse: ApiResponse.completed(response)));
    } on NetworkException catch (e) {
      emit(state.copyWith(feeResponse: ApiResponse.error(e.message)));
    }
  }

  void _onCreatePaymentIntent(
    CreatePaymentIntent event,
    Emitter<PaymentState> emit,
  ) async {
    emit(state.copyWith(intentResponse: const ApiResponse.loading()));
    try {
      var response = await paymentRepository.createPaymentIntent(event);
      emit(state.copyWith(intentResponse: ApiResponse.completed(response)));
    } on NetworkException catch (e) {
      emit(state.copyWith(intentResponse: ApiResponse.error(e.message)));
    }
  }

  void _onCreateOrderEvent(
    CreateOrderEvent event,
    Emitter<PaymentState> emit,
  ) async {
    emit(state.copyWith(orderResponse: const ApiResponse.loading()));
    try {
      var response = await paymentRepository.createOrder(event);
      emit(state.copyWith(orderResponse: ApiResponse.completed(response)));
    } on NetworkException catch (e) {
      emit(state.copyWith(orderResponse: ApiResponse.error(e.message)));
    } catch (e) {
      print('_onCreateOrderEvent: $e');
    }
  }

  void _onSetAddressDetails(
    SetAddressDetails event,
    Emitter<PaymentState> emit,
  ) {
    emit(state.copyWith(address: event.address));
  }
}
