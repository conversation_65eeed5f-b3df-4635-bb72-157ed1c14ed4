part of 'payment_bloc.dart';

sealed class PaymentEvent extends Equatable {
  const PaymentEvent();

  @override
  List<Object> get props => [];
}

class CreatePaymentIntent extends PaymentEvent {
  final num amount;
  final String currency;
  final String referenceType;

  const CreatePaymentIntent({
    required this.amount,
    required this.currency,
    required this.referenceType,
  });

  @override
  List<Object> get props => [amount, currency, referenceType];
}

class CalculateFee extends PaymentEvent {
  final double amount;
  final String referenceType;

  const CalculateFee({
    required this.amount,
    required this.referenceType,
  });

  @override
  List<Object> get props => [amount, referenceType];
}

class SetProductDetails extends PaymentEvent {
  final ProductDetail productDetails;

  const SetProductDetails({required this.productDetails});

  @override
  List<Object> get props => [productDetails];
}

class CreateOrderEvent extends PaymentEvent {
  final String buyerId;
  final String sellerId;
  final List<Map<String, dynamic>> items;
  final Map<String, dynamic> metadata;
  final String notes;

  const CreateOrderEvent({
    required this.buyerId,
    required this.sellerId,
    required this.items,
    required this.metadata,
    required this.notes,
  });

  @override
  List<Object> get props => [buyerId, sellerId, items, metadata, notes];
}

class SetAddressDetails extends PaymentEvent {
  final Address address;

  const SetAddressDetails({required this.address});

  @override
  List<Object> get props => [address];
}
