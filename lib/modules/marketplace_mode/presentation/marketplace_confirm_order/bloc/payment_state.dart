part of 'payment_bloc.dart';

class PaymentState extends Equatable {
  const PaymentState(
      {this.productDetail,
      this.feeResponse,
      this.intentResponse,
      this.orderResponse,
      this.address});
  final ProductDetail? productDetail;
  final ApiResponse<FeeResponse>? feeResponse;
  final ApiResponse<PaymentIntentResponse>? intentResponse;
  final ApiResponse<CreateOrderModel>? orderResponse;
  final Address? address;

  factory PaymentState.initial() {
    return const PaymentState(
        productDetail: null, feeResponse: null, intentResponse: null);
  }

  PaymentState copyWith({
    ProductDetail? productDetail,
    ApiResponse<FeeResponse>? feeResponse,
    ApiResponse<PaymentIntentResponse>? intentResponse,
    ApiResponse<CreateOrderModel>? orderResponse,
    Address? address,
  }) {
    return PaymentState(
      productDetail: productDetail ?? this.productDetail,
      feeResponse: feeResponse ?? this.feeResponse,
      intentResponse: intentResponse ?? this.intentResponse,
      orderResponse: orderResponse ?? this.orderResponse,
      address: address ?? this.address,
    );
  }

  @override
  List<Object?> get props =>
      [productDetail, feeResponse, intentResponse, orderResponse,address];
}
