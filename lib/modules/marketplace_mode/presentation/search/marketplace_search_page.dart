import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/drawer_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/filter_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/search_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/custom_sort_dropdown.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/category_filter/category_filter_drawer.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/filter_drawer.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/search_results/active_marketplace_search.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/search_results/empty_marketplace_search.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/search_results/recent_marketplace_search.dart';

class MarketplaceSearchPage extends StatefulWidget {
  const MarketplaceSearchPage({super.key});

  @override
  State<MarketplaceSearchPage> createState() => _MarketplaceSearchPageState();
}

class _MarketplaceSearchPageState extends State<MarketplaceSearchPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => SearchBloc(
              productRepository: getIt<ProductRepositoryRemote>(),
              globalStorage: getIt<GlobalStorage>(),
            ),
          ),
          BlocProvider(
            create: (context) => DrawerBloc(),
          ),
          BlocProvider(
            create: (context) => FilterBloc(),
          ),
        ],
        child: Scaffold(
          key: _scaffoldKey,
          appBar: AppBar(
            title: BlocBuilder<SearchBloc, SearchState>(
              builder: (context, state) {
                if (state.status == SearchStatus.empty &&
                    state.searchQuery!.isEmpty) {
                  if (_searchController.text.isNotEmpty) {
                    _searchController.clear();
                  }
                }

                return Transform.translate(
                  offset: Offset(-10.w, 0),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      if (value.trim().isEmpty) {
                        context.read<FilterBloc>().add(
                              const FiltersResetEvent(),
                            );
                        context.read<SearchBloc>().add(
                              const SearchFieldClearEvent(),
                            );
                      }

                      context.read<SearchBloc>().add(
                            SearchQueryChangeEvent(
                              value,
                              filterState: context.read<FilterBloc>().state,
                            ),
                          );
                    },
                    decoration: InputDecoration(
                      hintText: 'Search',
                      hintStyle: Theme.of(context)
                          .textTheme
                          .lightBodyLargeRegular
                          .copyWith(
                            color: Theme.of(context).greyScale500(context),
                          ),
                      prefixIcon: Transform.scale(
                        scale: 0.4,
                        child: SvgPicture.asset(
                          AppAssets.marketplaceSearchIcon,
                          height: 20,
                          width: 20,
                        ),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide:  BorderSide(
                          color: Theme.of(context).greyScale100(context),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide:  BorderSide(
                          color: Theme.of(context).greyScale100(context),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            centerTitle: false,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_rounded),
              onPressed: () => context.pop(),
            ),
            backgroundColor: Colors.transparent,
            scrolledUnderElevation: 0,
          ),
          drawer: BlocBuilder<DrawerBloc, DrawerState>(
            builder: (context, state) {
              return Drawer(
                key: ValueKey(state.status),
                width: MediaQuery.of(context).size.width * 0.85,
                clipBehavior: Clip.none,
                child: state.status == DrawerStatus.category
                    ? const CategoryFilterDrawer()
                    : const FilterDrawer(),
              );
            },
          ),
          body: BlocListener<FilterBloc, FilterState>(
            listener: (context, filterState) {
              print('Filter state: $filterState');
              final searchQuery = context.read<SearchBloc>().state.searchQuery;
              if (searchQuery != null && searchQuery.isNotEmpty) {
                context.read<SearchBloc>().add(
                      SearchQueryChangeEvent(
                        searchQuery,
                        filterState: filterState,
                      ),
                    );
              }
            },
            child: NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification notification) {
                CustomSortDropdown.closeDropdownWhenScroll();
                return false;
              },
              child: BlocBuilder<SearchBloc, SearchState>(
                builder: (context, state) {
                  if (state.showError) {
                    return Text('An unexpected error occured: ${state.error}');
                  }
                  if (state.showActiveSearches) {
                    return ActiveMarketplaceSearch(
                      scaffoldKey: _scaffoldKey,
                    );
                  }
                  if (state.showRecentSearches) {
                    return RecentMarketplaceSearch(
                      searchController: _searchController,
                    );
                  }
                  return const EmptyMarketplaceSearch();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
