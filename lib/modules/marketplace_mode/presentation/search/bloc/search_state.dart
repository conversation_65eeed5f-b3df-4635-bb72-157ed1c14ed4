part of 'search_bloc.dart';

enum SearchStatus {
  empty,
  active,
  error,
}

class SearchState extends Equatable {
  final SearchStatus status;
  final String? searchQuery;
  final SearchModel? result;
  final String? error;
  final List<String> recentSearches;
  final bool isLoading;
  final bool isSuccess;
  final int currentPage;
  final int totalPages;

  const SearchState({
    required this.status,
    this.searchQuery = "",
    this.result,
    this.error,
    this.recentSearches = const [],
    this.isLoading = false,
    this.isSuccess = false,
    this.currentPage = 1,
    this.totalPages = 1,
  });

  @override
  List<Object?> get props => [
        status,
        searchQuery,
        result,
        error,
        recentSearches,
        isLoading,
        isSuccess,
        currentPage,
        totalPages,
      ];

  SearchState update({
    SearchStatus? status,
    String? query,
    SearchModel? result,
    String? error,
    List<String>? recentSearches,
    bool? isLoading,
    bool? isSuccess,
    int? currentPage,
    int? totalPages,
  }) {
    return SearchState(
      status: status ?? this.status,
      searchQuery: query ?? searchQuery,
      result: result ?? this.result,
      error: error ?? this.error,
      recentSearches: recentSearches ?? this.recentSearches,
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
    );
  }

  List<Product> get products => result?.products ?? [];
  bool get hasResults => products.isNotEmpty;
  bool get hasError => error != null && error!.isNotEmpty;
  bool get hasRecentSearches => recentSearches.isNotEmpty;
  bool get showEmpty => status == SearchStatus.empty && !hasRecentSearches;
  bool get showRecentSearches =>
      status == SearchStatus.empty && hasRecentSearches;
  bool get showActiveSearches => status == SearchStatus.active;
  bool get showError => status == SearchStatus.error;
}
