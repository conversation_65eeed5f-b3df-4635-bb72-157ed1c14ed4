import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/utils/debounce.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/search_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/filter_bloc.dart';

part 'search_event.dart';
part 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final Debouncer _debouncer = Debouncer(
    delay: const Duration(milliseconds: 500),
  );

  final GlobalStorage globalStorage;

  ProductRepository productRepository;
  SearchBloc({
    required this.productRepository,
    required this.globalStorage,
  }) : super(const SearchState(status: SearchStatus.empty)) {
    on<SearchQueryChangeEvent>(_searchQueryChangeEvent);
    on<SearchFieldClearEvent>(_searchFieldClearEvent);
    on<LoadRecentSearchesEvent>(_loadRecentSearchesEvent);
    on<RemoveRecentSearchEvent>(_removeRecentSearchEvent);
    on<ClearRecentSearchesEvent>(_clearRecentSearchesEvent);

    add(const LoadRecentSearchesEvent());
  }

  Future<void> _loadRecentSearchesEvent(
      LoadRecentSearchesEvent event, Emitter<SearchState> emit) async {
    try {
      final recentSearches =
          globalStorage.getSearchHistories(GlobalStorageKey.searchHistory);
      emit(
        state.update(
          recentSearches: recentSearches,
        ),
      );
    } catch (e) {
      print('Error loading recent searches: $e');
    }
  }

  Future<void> _removeRecentSearchEvent(
      RemoveRecentSearchEvent event, Emitter<SearchState> emit) async {
    try {
      await globalStorage.removeSearchHistory(
        event.index,
        key: GlobalStorageKey.searchHistory,
      );
      final recentSearches =
          globalStorage.getSearchHistories(GlobalStorageKey.searchHistory);
      emit(
        state.update(
          recentSearches: recentSearches,
        ),
      );
    } catch (e) {
      print('Error removing recent search: $e');
    }
  }

  Future<void> _clearRecentSearchesEvent(
      ClearRecentSearchesEvent event, Emitter<SearchState> emit) async {
    try {
      await globalStorage.clearSearchHistories(
        key: GlobalStorageKey.searchHistory,
      );
      emit(
        state.update(
          recentSearches: [],
        ),
      );
    } catch (e) {
      print('Error clearing recent searches: $e');
    }
  }

  FutureOr<void> _searchQueryChangeEvent(
      SearchQueryChangeEvent event, Emitter<SearchState> emit) {
    print('in search');
    final searchQuery = event.searchQuery.trim();
    print(searchQuery);

    return Future(() {
      return _debouncer.asynchronousDebounce(() async {
        if (searchQuery.isEmpty) {
          try {
            if (!emit.isDone) {
              emit(
                state.update(
                  status: SearchStatus.empty,
                  query: searchQuery,
                  result: null,
                  isLoading: false,
                  isSuccess: false,
                ),
              );
            }
          } catch (e) {
            print('Error in Search Bloc: $e');
            if (!emit.isDone) {
              emit(
                state.update(
                  status: SearchStatus.error,
                  error: e.toString(),
                  isLoading: false,
                  isSuccess: false,
                ),
              );
            }
          }
        } else {
          try {
            if (!emit.isDone) {
              emit(
                state.update(
                  status: SearchStatus.active,
                  query: searchQuery,
                  isLoading: true,
                  isSuccess: false,
                ),
              );
            }

            final queryParams = {
              "query": searchQuery,
            };

            if (event.filterState != null) {
              if (event.filterState!.minPrice != null) {
                queryParams["min_price"] =
                    event.filterState!.minPrice.toString();
              }
              if (event.filterState!.maxPrice != null) {
                queryParams["max_price"] =
                    event.filterState!.maxPrice.toString();
              }
            }

            final response = await productRepository.searchProduct(
              queryParam: queryParams,
            );

            print("Response: $response");
            print("List: ${response.products}");

            await globalStorage.addSearchHistory(
              searchQuery,
              GlobalStorageKey.searchHistory,
            );
            final recentSearches = globalStorage
                .getSearchHistories(GlobalStorageKey.searchHistory);

            if (!emit.isDone) {
              emit(
                state.update(
                  status: SearchStatus.active,
                  query: searchQuery,
                  result: response,
                  recentSearches: recentSearches,
                  isLoading: false,
                  isSuccess: true,
                ),
              );
            }
          } catch (e) {
            print('Error in Search Bloc: $e');
            if (!emit.isDone) {
              emit(
                state.update(
                  status: SearchStatus.error,
                  error: e.toString(),
                  isLoading: false,
                  isSuccess: false,
                ),
              );
            }
          }
        }
      });
    });
  }

  FutureOr<void> _searchFieldClearEvent(
      SearchFieldClearEvent event, Emitter<SearchState> emit) {
    emit(
      state.update(
        status: SearchStatus.empty,
        query: "",
        result: null,
        isLoading: false,
        isSuccess: false,
      ),
    );
  }

  @override
  Future<void> close() {
    _debouncer.cancel();
    return super.close();
  }
}
