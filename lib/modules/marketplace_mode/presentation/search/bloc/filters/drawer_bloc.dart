import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'drawer_state.dart';
part 'drawer_event.dart';

class DrawerBloc extends Bloc<DrawerEvent, DrawerState> {
  DrawerBloc() : super(const DrawerState(DrawerStatus.filter)) {
    on<ShowFilterDrawerEvent>(
      (event, emit) => emit(
        state.update(
          status: DrawerStatus.filter,
        ),
      ),
    );

    on<ShowCategoryFilterDrawerEvent>(
      (event, emit) => emit(
        state.update(
          status: DrawerStatus.category,
        ),
      ),
    );
  }
}
