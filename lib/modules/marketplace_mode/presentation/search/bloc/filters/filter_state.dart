part of 'filter_bloc.dart';

class FilterState extends Equatable {
  final Map<String, bool> checkedCriteria;
  final double? minPrice;
  final double? maxPrice;

  const FilterState({
    required this.checkedCriteria,
    this.minPrice,
    this.maxPrice,
  });

  @override
  List<Object?> get props => [checkedCriteria, minPrice, maxPrice];

  FilterState update({
    String? checkboxValue,
    double? minPrice,
    double? maxPrice,
  }) {
    final newCriteria = Map<String, bool>.from(checkedCriteria);
    if (checkboxValue != null) {
      newCriteria[checkboxValue] = !(newCriteria[checkboxValue] ?? false);
    }

    return FilterState(
      checkedCriteria: newCriteria,
      minPrice: minPrice,
      maxPrice: maxPrice,
    );
  }

  List<String> get selectedFilters {
    return checkedCriteria.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }
}
