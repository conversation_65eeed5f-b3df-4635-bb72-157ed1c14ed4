part of 'filter_bloc.dart';

abstract class FilterEvent extends Equatable {
  const FilterEvent();

  @override
  List<Object> get props => [];
}

class CheckBoxToggleEvent extends FilterEvent {
  final String checkboxValue;
  final double? minPrice;
  final double? maxPrice;
  final bool isPriceFilter;

  const CheckBoxToggleEvent(
    this.checkboxValue, {
    this.minPrice,
    this.maxPrice,
    this.isPriceFilter = false,
  });

  @override
  List<Object> get props => [
        checkboxValue,
        minPrice ?? 0,
        maxPrice ?? 0,
        isPriceFilter,
      ];
}

class FiltersResetEvent extends FilterEvent {
  const FiltersResetEvent();
}

class UpdateCustomPriceRangeEvent extends FilterEvent {
  final double? minPrice;
  final double? maxPrice;

  const UpdateCustomPriceRangeEvent({this.minPrice, this.maxPrice});

  @override
  List<Object> get props => [minPrice ?? 0, maxPrice ?? 0];
}
