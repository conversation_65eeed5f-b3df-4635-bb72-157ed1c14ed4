import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'filter_event.dart';
part 'filter_state.dart';

class FilterBloc extends Bloc<FilterEvent, FilterState> {
  FilterBloc() : super(const FilterState(checkedCriteria: {})) {
    on<CheckBoxToggleEvent>((event, emit) {
      final isChecked = state.checkedCriteria[event.checkboxValue] ?? false;

      if (event.isPriceFilter) {
        const keys = [
          'Under \$25',
          '\$50 - \$200',
          '\$200 - \$500',
          'Over \$500',
          'Choose your price',
        ];

        final newCriteria = Map<String, bool>.from(state.checkedCriteria);
        for (final key in keys) {
          newCriteria[key] = false;
        }

        if (!isChecked) {
          newCriteria[event.checkboxValue] = true;
          emit(
            FilterState(
              checkedCriteria: newCriteria,
              minPrice: event.minPrice,
              maxPrice: event.maxPrice,
            ),
          );
        } else {
          emit(
            FilterState(
              checkedCriteria: newCriteria,
              minPrice: null,
              maxPrice: null,
            ),
          );
        }
      } else {
        emit(
          state.update(
            checkboxValue: event.checkboxValue,
            minPrice: state.minPrice,
            maxPrice: state.maxPrice,
          ),
        );
      }
    });

    on<FiltersResetEvent>((event, emit) {
      emit(
        const FilterState(
          checkedCriteria: {},
          minPrice: null,
          maxPrice: null,
        ),
      );
    });

    on<UpdateCustomPriceRangeEvent>((event, emit) {
      emit(
        FilterState(
          checkedCriteria: state.checkedCriteria,
          minPrice: event.minPrice,
          maxPrice: event.maxPrice,
        ),
      );
    });
  }
}
