part of 'search_bloc.dart';

class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object> get props => [];
}

class SearchQueryChangeEvent extends SearchEvent {
  final String searchQuery;
  final FilterState? filterState;

  const SearchQueryChangeEvent(this.searchQuery, {this.filterState});

  @override
  List<Object> get props => [searchQuery];
}

class SearchFieldClearEvent extends SearchEvent {
  const SearchFieldClearEvent();
}

class LoadRecentSearchesEvent extends SearchEvent {
  const LoadRecentSearchesEvent();
}

class RemoveRecentSearchEvent extends SearchEvent {
  final int index;

  const RemoveRecentSearchEvent(this.index);

  @override
  List<Object> get props => [index];
}

class ClearRecentSearchesEvent extends SearchEvent {
  const ClearRecentSearchesEvent();
}

class ChangePageEvent extends SearchEvent {
  final int page;

  const ChangePageEvent(this.page);

  @override
  List<Object> get props => [page];
}
