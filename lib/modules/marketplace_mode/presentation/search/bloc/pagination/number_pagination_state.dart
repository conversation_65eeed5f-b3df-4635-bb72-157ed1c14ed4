part of 'number_pagination_bloc.dart';

abstract class NumberPaginationState extends Equatable {
  final List<Product> products;
  final int currentPage;
  final int totalPages;

  const NumberPaginationState({
    required this.products,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  List<Object> get props => [products, currentPage, totalPages];
}

class NumberPaginationLoaded extends NumberPaginationState {
  const NumberPaginationLoaded({
    required super.products,
    required super.currentPage,
    required super.totalPages,
  });
}
