part of 'product_pagination_bloc.dart';

abstract class ProductPaginationState extends Equatable {
  final List<Product> products;
  final bool isMoreAvailable;

  const ProductPaginationState({
    this.products = const [],
    this.isMoreAvailable = true,
  });

  @override
  List<Object> get props => [products, isMoreAvailable];
}

class ProductPaginationLoaded extends ProductPaginationState {
  const ProductPaginationLoaded({
    required super.products,
    required super.isMoreAvailable,
  });
}
