import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_model_fake.dart';
import 'package:multime_app/shared/models/marketplace/product_fake_data.dart';

part 'product_pagination_event.dart';
part 'product_pagination_state.dart';

class ProductPaginationBloc
    extends Bloc<ProductPaginationEvent, ProductPaginationState> {
  final List<Product> allProducts = List.from(fakeDataProduct);
  static const int batch = 10;

  ProductPaginationBloc()
      : super(
          ProductPaginationLoaded(
            products: List.from(fakeDataProduct.take(batch)),
            isMoreAvailable: batch < fakeDataProduct.length,
          ),
        ) {
    on<LoadMoreEvent>((event, emit) {
      final currentProducts = state.products.length;
      final newProducts =
          allProducts.skip(currentProducts).take(batch).toList();

      if (newProducts.isNotEmpty) {
        emit(ProductPaginationLoaded(
          products: [...state.products, ...newProducts],
          isMoreAvailable:
              (currentProducts + newProducts.length) < allProducts.length,
        ));
      } else {
        emit(ProductPaginationLoaded(
          products: state.products,
          isMoreAvailable: false,
        ));
      }
    });
  }
}
