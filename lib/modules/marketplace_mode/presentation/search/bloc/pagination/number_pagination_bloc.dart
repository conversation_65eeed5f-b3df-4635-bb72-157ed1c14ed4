import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_model_fake.dart';
import 'package:multime_app/shared/models/marketplace/product_fake_data.dart';

part 'number_pagination_event.dart';
part 'number_pagination_state.dart';

class NumberPaginationBloc
    extends Bloc<NumberPaginationEvent, NumberPaginationState> {
  final List<Product> allProducts = List.from(fakeDataProduct);
  static const int batch = 10;

  NumberPaginationBloc()
      : super(NumberPaginationLoaded(
          products: List.from(fakeDataProduct.take(batch)),
          currentPage: 0,
          totalPages: (fakeDataProduct.length / batch).ceil(),
        )) {
    on<ChangePageEvent>((event, emit) {
      final startIndex = event.page * batch;

      if (startIndex >= allProducts.length) {
        return;
      }

      final newProducts = allProducts.skip(startIndex).take(batch).toList();

      emit(NumberPaginationLoaded(
        products: newProducts,
        currentPage: event.page,
        totalPages: (allProducts.length / batch).ceil(),
      ));
    });
  }
}
