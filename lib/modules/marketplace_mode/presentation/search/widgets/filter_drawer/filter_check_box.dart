import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/filter_bloc.dart';

class FilterCheckBox extends StatelessWidget {
  final String checkboxValue;

  const FilterCheckBox({
    super.key,
    required this.checkboxValue,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        BlocBuilder<FilterBloc, FilterState>(
          builder: (context, state) {
            return SizedBox(
              height: 24,
              width: 24,
              child: Checkbox(
                checkColor: AppColors.whitePrimary,
                fillColor: WidgetStateProperty.resolveWith<Color?>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return Theme.of(context).secondaryBase(context);
                    }
                    return Colors.transparent;
                  },
                ),
                side:  BorderSide(
                  color: Theme.of(context).greyScale600(context),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                value: state.checkedCriteria[checkboxValue] ?? false,
                onChanged: (bool? value) {
                  context
                      .read<FilterBloc>()
                      .add(CheckBoxToggleEvent(checkboxValue));
                },
              ),
            );
          },
        ),
        Gap(8.w),
        Text(
          checkboxValue,
          style: Theme.of(context).textTheme.lightBodyLargeRegular,
        ),
      ],
    );
  }
}
