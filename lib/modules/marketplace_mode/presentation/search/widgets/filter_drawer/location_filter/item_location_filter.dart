import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/filter_check_box.dart';

class ItemLocationFilter extends StatelessWidget {
  const ItemLocationFilter({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration:  BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color:Theme.of(context).greyScale100(context),
            width: 1,
          ),
        ),
      ),
      child: ExpansionTile(
        dense: true,
        tilePadding: EdgeInsets.zero,
        childrenPadding: EdgeInsets.zero,
        iconColor: AppColors.blackPrimary,
        collapsedIconColor: AppColors.blackPrimary,
        initiallyExpanded: true,
        title: Text(
          'Item location',
          style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
        ),
        children: [
          const FilterCheckBox(
            checkboxValue: 'Near',
          ),
          Gap(15.h),
          const FilterCheckBox(
            checkboxValue: 'Asia',
          ),
          Gap(15.h),
          const FilterCheckBox(
            checkboxValue: 'Africa',
          ),
          Gap(15.h),
          const FilterCheckBox(
            checkboxValue: 'Australia',
          ),
          Gap(15.h),
          const FilterCheckBox(
            checkboxValue: 'Europe',
          ),
          Gap(15.h),
          const FilterCheckBox(
            checkboxValue: 'North America',
          ),
          Gap(15.h),
          const FilterCheckBox(
            checkboxValue: 'South America',
          ),
          Gap(10.h),
        ],
      ),
    );
  }
}
