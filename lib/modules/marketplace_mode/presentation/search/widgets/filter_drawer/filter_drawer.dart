import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/drawer_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/location_filter/item_location_filter.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/price_filter/price_filter.dart';

class FilterDrawer extends StatelessWidget {
  const FilterDrawer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Filter & Refine',
          style: Theme.of(context).textTheme.lightBodyXLargeMedium,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.close_rounded),
          onPressed: () {
            context.pop();
          },
        ),
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
      ),
      body: Padding(
        padding: PaddingConstants.padSymH16.add(
          PaddingConstants.padSymV2,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Category',
                  style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                ),
                TextButton(
                  onPressed: () {
                    context
                        .read<DrawerBloc>()
                        .add(const ShowCategoryFilterDrawerEvent());
                  },
                  style: TextButton.styleFrom(
                    backgroundColor: Theme.of(context).greyScale100(context),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    minimumSize: const Size(44, 36),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_rounded,
                    size: 24,
                    color: AppColors.blackPrimary,
                  ),
                ),
              ],
            ),
            const PriceFilter(),
            const ItemLocationFilter(),
          ],
        ),
      ),
    );
  }
}
