import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/debounce.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/filter_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/price_filter/price_filter_checkbox.dart';

class PriceFilter extends StatefulWidget {
  const PriceFilter({
    super.key,
  });

  @override
  State<PriceFilter> createState() => _PriceFilterState();
}

class _PriceFilterState extends State<PriceFilter> {
  final Debouncer _debouncer = Debouncer(
    delay: const Duration(milliseconds: 1000),
  );

  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  void _onPriceChanged(BuildContext context) {
    _debouncer(() {
      final minText = _minPriceController.text.trim();
      final maxText = _maxPriceController.text.trim();

      final min = double.tryParse(minText);
      final max = double.tryParse(maxText);

      if (min != null && max != null && max < min) {
        _minPriceController.clear();
        _maxPriceController.clear();
        debugPrint('❌ Invalid price range, min have to be less than max');
        return;
      }

      context.read<FilterBloc>().add(
            UpdateCustomPriceRangeEvent(
              minPrice: min,
              maxPrice: max,
            ),
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<FilterBloc>().state;
    final isCustomized = state.checkedCriteria['Choose your price'] ?? false;

    if (isCustomized) {
      final min = state.minPrice?.toStringAsFixed(0) ?? '';
      final max = state.maxPrice?.toStringAsFixed(0) ?? '';

      if (_minPriceController.text != min) {
        _minPriceController.text = min;
      }
      if (_maxPriceController.text != max) {
        _maxPriceController.text = max;
      }
    } else {
      if (_minPriceController.text.isNotEmpty) _minPriceController.clear();
      if (_maxPriceController.text.isNotEmpty) _maxPriceController.clear();
    }

    return Container(
      decoration:  BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).greyScale100(context),
            width: 1,
          ),
        ),
      ),
      child: ExpansionTile(
        dense: true,
        tilePadding: EdgeInsets.zero,
        childrenPadding: EdgeInsets.zero,
        iconColor: AppColors.blackPrimary,
        collapsedIconColor: AppColors.blackPrimary,
        initiallyExpanded: true,
        title: Text(
          'Price',
          style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
        ),
        children: [
          const PriceFilterCheckbox(
            checkboxValue: 'Under \$25',
            minPrice: 0,
            maxPrice: 25,
          ),
          Gap(15.h),
          const PriceFilterCheckbox(
            checkboxValue: '\$50 - \$200',
            minPrice: 50,
            maxPrice: 200,
          ),
          Gap(15.h),
          const PriceFilterCheckbox(
            checkboxValue: '\$200 - \$500',
            minPrice: 200,
            maxPrice: 500,
          ),
          Gap(15.h),
          const PriceFilterCheckbox(
            checkboxValue: 'Over \$500',
            minPrice: 500,
            maxPrice: null,
          ),
          Gap(15.h),
          const PriceFilterCheckbox(
            checkboxValue: 'Choose your price',
          ),
          Gap(15.h),
          Row(
            children: [
              SizedBox(
                height: 42,
                width: 80,
                child: TextField(
                  controller: _minPriceController,
                  onChanged: (value) {
                    _onPriceChanged(context);
                  },
                  enabled: isCustomized,
                  textAlign: TextAlign.center,
                  decoration: InputDecoration(
                    hintText: 'Min.',
                    hintStyle: Theme.of(context)
                        .textTheme
                        .lightBodyLargeMedium
                        .copyWith(
                          color: Theme.of(context).greyScale500(context),
                        ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide:  BorderSide(
                        color: Theme.of(context).greyScale500(context)
                      ),
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Theme.of(context).greyScale500(context)
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide:  BorderSide(
                        color:Theme.of(context).greyScale500(context)
                      ),
                    ),
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(r'^\d*\.?\d{0,2}'),
                    ),
                    TextInputFormatter.withFunction(
                      (oldValue, newValue) {
                        if (newValue.text.startsWith('0') &&
                            newValue.text.length > 1 &&
                            !newValue.text.startsWith('0.')) {
                          return oldValue;
                        }

                        if (newValue.text.isEmpty) {
                          return newValue;
                        }

                        final double? value = double.tryParse(newValue.text);
                        if (value != null) {
                          return newValue;
                        }

                        return oldValue;
                      },
                    ),
                  ],
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                ),
              ),
              Gap(20.w),
              SizedBox(
                height: 42,
                width: 80,
                child: TextField(
                  controller: _maxPriceController,
                  onChanged: (value) {
                    _onPriceChanged(context);
                  },
                  enabled: isCustomized,
                  textAlign: TextAlign.center,
                  decoration: InputDecoration(
                    hintText: 'Max.',
                    hintStyle: Theme.of(context)
                        .textTheme
                        .lightBodyLargeMedium
                        .copyWith(
                          color: Theme.of(context).greyScale500(context)
                        ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide:  BorderSide(
                        color: Theme.of(context).greyScale500(context)
                      ),
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Theme.of(context).greyScale500(context)
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide:  BorderSide(
                        color: Theme.of(context).greyScale500(context)
                      ),
                    ),
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(r'^\d*\.?\d{0,2}'),
                    ),
                    TextInputFormatter.withFunction(
                      (oldValue, newValue) {
                        if (newValue.text.startsWith('0') &&
                            newValue.text.length > 1 &&
                            !newValue.text.startsWith('0.')) {
                          return oldValue;
                        }

                        if (newValue.text.isEmpty) {
                          return newValue;
                        }

                        final double? value = double.tryParse(newValue.text);
                        if (value != null) {
                          return newValue;
                        }

                        return oldValue;
                      },
                    ),
                  ],
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                ),
              ),
            ],
          ),
          Gap(10.h),
        ],
      ),
    );
  }
}
