import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/filter_check_box.dart';

class CategoryFilter extends StatelessWidget {
  final String title;
  final List<String> filterOptions;

  const CategoryFilter({
    super.key,
    required this.title,
    required this.filterOptions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration:  BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color:Theme.of(context).greyScale100(context),
            width: 1,
          ),
        ),
      ),
      child: ExpansionTile(
        dense: true,
        tilePadding: EdgeInsets.zero,
        childrenPadding: EdgeInsets.zero,
        iconColor: AppColors.blackPrimary,
        collapsedIconColor: AppColors.blackPrimary,
        initiallyExpanded: true,
        title: Text(
          title,
          style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
        ),
        children: [
          Gap(8.h),
          ...filterOptions.map(
            (option) => Padding(
              padding: EdgeInsets.only(
                bottom: 16.h,
              ),
              child: FilterCheckBox(
                checkboxValue: option,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
