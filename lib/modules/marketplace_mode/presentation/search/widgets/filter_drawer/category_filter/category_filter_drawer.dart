import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/drawer_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/category_filter/category_filter.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/filter_drawer/filter_check_box.dart';

class CategoryFilterDrawer extends StatelessWidget {
  const CategoryFilterDrawer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Category',
          style: Theme.of(context).textTheme.lightBodyXLargeMedium,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            width: 24,
          ),
          onPressed: () {
            context.read<DrawerBloc>().add(const ShowFilterDrawerEvent());
          },
        ),
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: PaddingConstants.padSymH16.add(
            PaddingConstants.padSymV10,
          ),
          child: Column(
            children: [
              const FilterCheckBox(
                checkboxValue: 'All Category',
              ),
              Gap(14.h),
               Divider(
                color:Theme.of(context).greyScale100(context),
                thickness: 1,
                height: 0,
              ),
              const CategoryFilter(
                title: 'Art & Crafts',
                filterOptions: [
                  'Supplies',
                  'Handmade Goods',
                  'DIY Kits',
                ],
              ),
              const CategoryFilter(
                title: 'Automotive',
                filterOptions: [
                  'Parts & Accessories',
                  'Vehicles',
                  'Tools',
                ],
              ),
              const CategoryFilter(
                title: 'Books & Media',
                filterOptions: ['Books', 'Movies', 'Music'],
              ),
              const CategoryFilter(
                title: 'Electronics',
                filterOptions: [
                  'Mobile Phones',
                  'Laptops & Tablets',
                  'Accessories'
                ],
              ),
              const CategoryFilter(
                title: 'Fashion',
                filterOptions: ['Clothing', 'Shoes', 'Jewelry', 'Accessories'],
              ),
              const CategoryFilter(
                title: 'Food & Beverages',
                filterOptions: [
                  'Groceries',
                  'Gourmet Items',
                  'Meal Kits',
                ],
              ),
              const CategoryFilter(
                title: 'Health & Beauty',
                filterOptions: [
                  'Skincare',
                  'Makeup',
                  'Supplements',
                ],
              ),
              const CategoryFilter(
                title: 'Home & Garden',
                filterOptions: [
                  'Furniture',
                  'Decor',
                  'Gardening Tools',
                  'Appliances'
                ],
              ),
              const CategoryFilter(
                title: 'Real Estate',
                filterOptions: ['Rentals', 'Sales', 'Commercial Properties'],
              ),
              const CategoryFilter(
                title: 'Services',
                filterOptions: ['Freelance Work', 'Home Services', 'Tutoring'],
              ),
              const CategoryFilter(
                title: 'Sports & Outdoors',
                filterOptions: [
                  'Exercise',
                  'Equipment',
                  'Outdoor Gear',
                  'Apparel'
                ],
              ),
              const CategoryFilter(
                title: 'Toys & Games',
                filterOptions: [
                  'Educational Toys',
                  'Board Games',
                  'Outdoor Toys',
                  'Other'
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
