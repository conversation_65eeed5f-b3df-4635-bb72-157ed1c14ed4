// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CustomSortDropdown extends StatefulWidget {
  final List<String> sortCriteria;

  const CustomSortDropdown({
    super.key,
    required this.sortCriteria,
  });

  @override
  // ignore: no_logic_in_create_state
  State<CustomSortDropdown> createState() {
    _instance = _CustomSortDropdownState();
    return _instance!;
  }

  static _CustomSortDropdownState? _instance;

  static void closeDropdownWhenScroll() {
    _instance?._closeDropdown();
  }
}

class _CustomSortDropdownState extends State<CustomSortDropdown> {
  String? dropdownItem;

  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  bool _isDropdownOpen = false;

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          width: 160.w,
          child: CompositedTransformFollower(
            link: _layerLink,
            offset: Offset(0, 40.h),
            showWhenUnlinked: false,
            child: Material(
              elevation: 2,
              borderRadius: BorderRadius.circular(4),
              color: AppColors.whitePrimary,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: widget.sortCriteria.map((String value) {
                  return InkWell(
                    onTap: () {
                      setState(() {
                        dropdownItem = value;
                        _closeDropdown();
                      });
                    },
                    overlayColor: WidgetStateProperty.resolveWith<Color?>(
                      (state) {
                        if (state.contains(WidgetState.pressed)) {
                          // ignore: deprecated_member_use
                          return Colors.grey.withOpacity(0.1);
                        }
                        return null;
                      },
                    ),
                    splashFactory: InkRipple.splashFactory,
                    borderRadius: BorderRadius.circular(4),
                    child: Padding(
                      padding: PaddingConstants.padSymH16.add(
                        PaddingConstants.padSymV12,
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          value,
                          style:
                              Theme.of(context).textTheme.lightBodyLargeRegular,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        );
      },
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isDropdownOpen = true;
    });
  }

  void _closeDropdown() {
    if (_isDropdownOpen && _overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isDropdownOpen = false;

      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleDropdown,
        child: Container(
          height: 40.h,
          width: 160.w,
          decoration: BoxDecoration(
            color: AppColors.whitePrimary,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: Theme.of(context).greyScale500(context),
            ),
          ),
          padding: PaddingConstants.padSymH16.copyWith(
            right: 12.w,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  dropdownItem ?? 'Sort by',
                  style: Theme.of(context).textTheme.lightBodyLargeRegular,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                _isDropdownOpen
                    ? Icons.keyboard_arrow_up_rounded
                    : Icons.keyboard_arrow_down_rounded,
                color: AppColors.blackPrimary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
