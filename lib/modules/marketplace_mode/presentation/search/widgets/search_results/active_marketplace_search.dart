import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/drawer_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/filters/filter_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/search_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/widgets/custom_sort_dropdown.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/horizonal_product/horizonal_product.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/widgets/profile_pagination.dart';

class ActiveMarketplaceSearch extends StatelessWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;

  ActiveMarketplaceSearch({
    super.key,
    required this.scaffoldKey,
  });

  final List<String> sortCriteria = <String>[
    'Name (A-Z)',
    'Name (Z-A)',
    'Price (Low to High)',
    'Price (High to Low)',
  ];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: PaddingConstants.padAll16,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _resultsCount(context),
            Gap(8.h),
             Divider(color: Theme.of(context).greyScale100(context),),
            Gap(4.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _filterButton(context),
                CustomSortDropdown(
                  sortCriteria: sortCriteria,
                ),
              ],
            ),
            Gap(10.h),
            _searchQueryChip(context),
            _filterQueryChips(context),
            Gap(20.h),
            _searchResultsGrid(),
            Gap(16.h),
            _numberPagination(),
          ],
        ),
      ),
    );
  }

  Widget _numberPagination() {
    return Padding(
      padding: PaddingConstants.padSymH20.add(
        PaddingConstants.padSymH10,
      ),
      child: BlocBuilder<SearchBloc, SearchState>(
        builder: (context, state) {
          if (state.isLoading || state.showError || !state.hasResults) {
            return const SizedBox.shrink();
          }

          return ProfilePagination(
            currentPage: state.currentPage,
            totalPages: state.totalPages,
            onPageChanged: (int value) {
              // context.read<SearchBloc>().add(ChangePageEvent(page: value));
            },
          );
        },
      ),
    );
  }

  Widget _searchResultsGrid() {
    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (!state.hasResults) {
          return Center(
            child: Text(
              'No results found for "${state.searchQuery}"',
              style: Theme.of(context).textTheme.lightBodyLargeMedium,
              textAlign: TextAlign.center,
            ),
          );
        }

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 16.h,
            crossAxisSpacing: 16.w,
            childAspectRatio: 0.55,
          ),
          itemCount: state.products.length,
          itemBuilder: (context, index) {
            return HorizonalProduct(
              id: state.products[index].id,
              image: state.products[index].imageUrl ?? '',
              productName: state.products[index].title ?? 'N/A',
              price: state.products[index].price,
              shopAddress: state.products[index].location?.address ?? 'N/A',
            );
          },
        );
      },
    );
  }

  String _chipsLabel(String filter, FilterState state) {
    if (filter != 'Choose your price') return filter;

    final min = state.minPrice;
    final max = state.maxPrice;

    if (min != null && max != null) {
      return '\$${min.toStringAsFixed(0)} - \$${max.toStringAsFixed(0)}';
    } else if (min != null && max == null) {
      return 'From \$${min.toStringAsFixed(0)}';
    } else if (min == null && max != null) {
      return 'Under \$${max.toStringAsFixed(0)}';
    }

    return filter;
  }

  Widget _filterQueryChips(BuildContext context) {
    return BlocBuilder<FilterBloc, FilterState>(
      builder: (context, state) {
        final filters = state.selectedFilters;
        const priceFilters = [
          'Under \$25',
          '\$50 - \$200',
          '\$200 - \$500',
          'Over \$500',
          'Choose your price',
        ];

        return Wrap(
          spacing: 6.w,
          children: [
            ...List.generate(
              filters.length,
              (index) {
                final filter = filters[index];
                final isPriceFilter = priceFilters.contains(filter);

                if (filter == 'Choose your price' &&
                    state.minPrice == null &&
                    state.maxPrice == null) {
                  return const SizedBox.shrink();
                }

                return Chip(
                  onDeleted: () {
                    context.read<FilterBloc>().add(
                          CheckBoxToggleEvent(
                            filters[index],
                            isPriceFilter: isPriceFilter,
                          ),
                        );
                  },
                  backgroundColor: AppColors.errorLight,
                  deleteIcon:  Icon(
                    Icons.close,
                    size: 18,
                    color: Theme.of(context).primary(context),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side:  BorderSide(
                      color: Theme.of(context).primary(context),
                    ),
                  ),
                  label: Text(
                    _chipsLabel(filter, state),
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeRegular
                        .copyWith(
                          color: Theme.of(context).primary(context),
                        ),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _searchQueryChip(BuildContext context) {
    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Align(
                alignment: Alignment.centerLeft,
                child: Chip(
                  onDeleted: () {
                    context.read<SearchBloc>().add(
                          const SearchFieldClearEvent(),
                        );
                    context.read<FilterBloc>().add(
                          const FiltersResetEvent(),
                        );
                  },
                  backgroundColor: AppColors.errorLight,
                  deleteIcon:  Icon(
                    Icons.close,
                    size: 18,
                    color:Theme.of(context).primary(context),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                    side:  BorderSide(
                      color:Theme.of(context).primary(context),
                    ),
                  ),
                  label: Text(
                    "Search: '${state.searchQuery}'",
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeRegular
                        .copyWith(
                          color:Theme.of(context).primary(context),
                        ),
                  ),
                ),
              ),
            ),
            Gap(4.w),
            TextButton(
              onPressed: () {
                context.read<SearchBloc>().add(
                      const SearchFieldClearEvent(),
                    );
                context.read<FilterBloc>().add(
                      const FiltersResetEvent(),
                    );
              },
              style: TextButton.styleFrom(
                overlayColor:Colors.transparent,
              ),
              child: Text(
                'Clear all',
                style:
                    Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).secondaryBase(context),
                          decoration: TextDecoration.underline,
                        ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _filterButton(BuildContext context) {
    return BlocBuilder<FilterBloc, FilterState>(
      builder: (context, state) {
        return ElevatedButton(
          onPressed: () {
            context.read<DrawerBloc>().add(const ShowFilterDrawerEvent());
            scaffoldKey.currentState?.openDrawer();
          },
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.zero,
            backgroundColor: AppColors.whitePrimary,
            // ignore: deprecated_member_use
            overlayColor: Colors.grey.withOpacity(0.2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
              side:  BorderSide(
                color:Theme.of(context).greyScale500(context),
              ),
            ),
            minimumSize: const Size(110, 40),
          ).copyWith(
            elevation: WidgetStateProperty.all(0),
          ),
          child: Row(
            children: [
               Icon(
                Icons.filter_alt_outlined,
                color: Theme.of(context).greyScale900(context),
              ),
              Gap(4.w),
              Text(
                'Filter (${context.read<FilterBloc>().state.selectedFilters.length})',
                style: Theme.of(context).textTheme.lightBodyLargeRegular,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _resultsCount(BuildContext context) {
    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Text(
            'Searching for ${state.searchQuery}...',
            style: Theme.of(context).textTheme.lightBodyMediumRegular,
          );
        }

        if (!state.hasResults) {
          return Text(
            'No results found',
            style: Theme.of(context).textTheme.lightBodyMediumRegular,
          );
        }

        return Text(
          'You found ${state.products.length} results',
          style: Theme.of(context).textTheme.lightBodyMediumRegular,
        );
      },
    );
  }
}
