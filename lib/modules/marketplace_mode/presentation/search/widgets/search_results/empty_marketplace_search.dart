import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class EmptyMarketplaceSearch extends StatelessWidget {
  const EmptyMarketplaceSearch({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Gap(30.h),
          SvgPicture.asset(
            AppAssets.marketplaceSearch,
            height: 120,
            width: 120,
          ),
          Gap(30.h),
          Text(
            'Need help? Type in the keyword you\'re searching for.',
            style: Theme.of(context).textTheme.lightBodyLargeRegular,
            textAlign: TextAlign.center,
          )
        ],
      ),
    );
  }
}
