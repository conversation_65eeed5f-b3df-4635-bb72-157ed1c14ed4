import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_model_fake.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/pagination/product_pagination_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/bloc/search_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/horizonal_product/horizonal_product.dart';
import 'package:multime_app/shared/models/marketplace/product_fake_data.dart';

class RecentMarketplaceSearch extends StatelessWidget {
  final TextEditingController searchController;

  RecentMarketplaceSearch({
    super.key,
    required this.searchController,
  });

  // NOTE:
  // Products data will be replaced later by the actual data from the API
  // When the API is being integrated, I'll rewrite the 'HorizonalProduct' widget later
  final List<Product> productSuggestions = List.from(fakeDataProduct);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProductPaginationBloc(),
      child: SingleChildScrollView(
        child: Padding(
          padding: PaddingConstants.padAll16.copyWith(
            top: 8.h,
          ),
          child: Column(
            children: [
              _recentSearchesList(context),

              // Align(
              //   alignment: Alignment.centerLeft,
              //   child: Text(
              //     'Product Suggestions',
              //     style: Theme.of(context).textTheme.lightBodyXLargeBold,
              //   ),
              // ),
              // Gap(20.h),
              // _productSuggestionsGrid(),
              // Gap(16.h),
              // BlocBuilder<ProductPaginationBloc, ProductPaginationState>(
              //   builder: (context, state) {
              //     if (state.isMoreAvailable) {
              //       return ElevatedButton(
              //         onPressed: () {
              //           context
              //               .read<ProductPaginationBloc>()
              //               .add(LoadMoreEvent());
              //         },
              //         style: ElevatedButton.styleFrom(
              //           backgroundColor: AppColors.whitePrimary,
              //           // ignore: deprecated_member_use
              //           overlayColor: Colors.grey.withOpacity(0.2),
              //           shape: RoundedRectangleBorder(
              //             borderRadius: BorderRadius.circular(8),
              //             side: const BorderSide(
              //               color: AppColors.blue1F2D44,
              //             ),
              //           ),
              //           minimumSize: const Size(90, 40),
              //         ).copyWith(
              //           elevation: WidgetStateProperty.all(0),
              //         ),
              //         child: Text(
              //           'More',
              //           style:
              //               Theme.of(context).textTheme.lightBodyLargeRegular,
              //         ),
              //       );
              //     } else {
              //       return const SizedBox();
              //     }
              //   },
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _recentSearchesList(BuildContext context) {
    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        final recentSearches = state.recentSearches;

        return recentSearches.isEmpty
            ? Gap(10.h)
            : Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 8,
                        ),
                        child: Text(
                          'Recent searches',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumRegular,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          context
                              .read<SearchBloc>()
                              .add(const ClearRecentSearchesEvent());
                        },
                        style: TextButton.styleFrom(
                          overlayColor: Colors.transparent,
                        ),
                        child: Text(
                          'Delete all',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumSemiBold
                              .copyWith(
                                color: Theme.of(context).secondaryBase(context),
                              ),
                        ),
                      ),
                    ],
                  ),
                  ListView.builder(
                    padding: const EdgeInsets.only(
                      left: 10,
                    ),
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: recentSearches.length,
                    itemBuilder: (context, index) {
                      return ListTile(
                        contentPadding: EdgeInsets.zero,
                        leading: SvgPicture.asset(
                          AppAssets.marketplaceSearchIcon,
                          height: 20,
                          width: 20,
                        ),
                        trailing: IconButton(
                          icon: const Icon(
                            Icons.close_rounded,
                            size: 24,
                            color: AppColors.blackPrimary,
                          ),
                          onPressed: () {
                            context
                                .read<SearchBloc>()
                                .add(RemoveRecentSearchEvent(index));
                          },
                        ),
                        title: Text(
                          recentSearches[index],
                          style:
                              Theme.of(context).textTheme.lightBodyLargeMedium,
                        ),
                        onTap: () {
                          searchController.text = recentSearches[index];
                          context.read<SearchBloc>().add(
                              SearchQueryChangeEvent(recentSearches[index]));
                        },
                      );
                    },
                  ),
                  Gap(26.h),
                ],
              );
      },
    );
  }

  Widget _productSuggestionsGrid() {
    return BlocBuilder<ProductPaginationBloc, ProductPaginationState>(
      builder: (context, state) {
        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 16.h,
            crossAxisSpacing: 16.w,
            childAspectRatio: 0.55,
          ),
          itemCount: state.products.length,
          itemBuilder: (context, index) {
            return HorizonalProduct(
              products: state.products[index],
              image: state.products[index].image,
              productName: state.products[index].productName,
              price: state.products[index].price,
              shopAddress: state.products[index].seller.shopAddress,
            );
          },
        );
      },
    );
  }
}
