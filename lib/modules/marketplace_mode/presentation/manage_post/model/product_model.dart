enum ProductStatus {
  pending,
  processing,
  inTransit,
  completed,
  orderCancelled,
  returnInProgress,
}

class Product {
  final String title;
  final String description;
  final String imageUrl;
  final double price;
  final String date;
  final ProductStatus status;

  Product({
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.price,
    required this.date,
    required this.status,
  });
}
