import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../model/product_model.dart';

class ProductCartPost extends StatelessWidget {
  final Product product;
  const ProductCartPost({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          // margin: const EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    product.imageUrl,
                    width: 120.h,
                    height: 120.h,
                    fit: BoxFit.cover,
                  ),
                ),
                const Gap(15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            product.title,
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeSemiBold,
                          ),
                          GestureDetector(
                            onTap: () {},
                            child: SvgPicture.asset(
                              AppAssets.more,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        width: 170.w,
                        child: Text(
                          product.description,
                          style: Theme.of(context)
                              .textTheme
                              .lightBodySmallRegular
                              .copyWith(color: Colors.grey),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        "\$${product.price.toStringAsFixed(2)}",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeSemiBold
                            .copyWith(color: Colors.red),
                      ),
                      Text(
                        product.date,
                        style: Theme.of(context)
                            .textTheme
                            .lightBodySmallRegular
                            .copyWith(color: Colors.grey),
                      ),
                      const Gap(5),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          StatusChip(status: product.status),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        Divider(
          color: Theme.of(context).lightGrey(context),
        )
      ],
    );
  }
}

class StatusChip extends StatelessWidget {
  final ProductStatus status;
  const StatusChip({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    Map<ProductStatus, Color> statusColors = {
      ProductStatus.pending: Colors.grey,
      ProductStatus.processing: Colors.blue,
      ProductStatus.inTransit: Colors.lightBlue,
      ProductStatus.completed: Colors.green,
      ProductStatus.orderCancelled: Colors.red,
      ProductStatus.returnInProgress: Colors.orange,
    };

    Map<ProductStatus, String> statusTexts = {
      ProductStatus.pending: "Pending",
      ProductStatus.processing: "Processing",
      ProductStatus.inTransit: "In Transit",
      ProductStatus.completed: "Completed",
      ProductStatus.orderCancelled: "Order Cancelled",
      ProductStatus.returnInProgress: "Return in Progress",
    };

    return Container(
      decoration: BoxDecoration(
        color: statusColors[status]!.withOpacity(0.2),
        borderRadius: BorderRadius.circular(10),
      ),
      width: 100,
      height: 30,
      child: Center(
        child: Text(
          statusTexts[status]!,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: statusColors[status],
          ),
        ),
      ),
    );
  }
}
