import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/manage_post/manage_post_page.dart';

import '../../../../app/routers/routers_name.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../application/bottom_bar/application_page.dart';
import '../../../application/bottom_bar/bottomSheet_model.dart';

class ManageBottomBar extends StatefulWidget {
  const ManageBottomBar({super.key});

  @override
  State<ManageBottomBar> createState() => _ManageBottomBarState();
}

class _ManageBottomBarState extends State<ManageBottomBar> {
  @override
  Widget build(BuildContext context) {
    return ApplicationPage(
      page: ManagePostsPage(),
      bottomSheetItems: [
        BottomSheetItem(
          name: "Post product",
          iconLead: AppAssets.editSvg,
          onTap: () {
            (context).push(RouteName.postProductScreen);
          },
        ),
        BottomSheetItem(
          name: "Manage Your Posts",
          iconLead: AppAssets.favoriteMarket,
          onTap: () {
            (context).push(RouteName.likedScreen);
          },
        ),
        BottomSheetItem(
          name: "Favorite",
          iconLead: AppAssets.favoriteMarket,
          onTap: () {
            (context).push(RouteName.orderDetailsScreen);
          },
        ),
      ],
    );
  }
}
