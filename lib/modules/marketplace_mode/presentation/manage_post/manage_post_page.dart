// <PERSON>h sách trạng thái
import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/manage_post/model/product_model.dart';
import 'widgets/product_cart_post.dart';

class ManagePostsPage extends StatelessWidget {
  final List<Product> products = [
    Product(
      title: "Macbook 14",
      imageUrl:
          "https://media.vneconomy.vn/w800/images/upload/2024/09/10/apple-iphone-16-pro-series.jpg",
      price: 450,
      date: "January 19, 10:02 AM",
      status: ProductStatus.pending,
      description: 'ReloadedReloadedReloadedReloadedReloaded',
    ),
    Product(
      title: "Macbook 14",
      imageUrl:
          "https://media.vneconomy.vn/w800/images/upload/2024/09/10/apple-iphone-16-pro-series.jpg",
      price: 450,
      date: "January 19, 10:02 AM",
      status: ProductStatus.processing,
      description: 'ReloadedReloadedReloadedReloadedReloaded',
    ),
    Product(
      title: "Macbook 14",
      imageUrl:
          "https://media.vneconomy.vn/w800/images/upload/2024/09/10/apple-iphone-16-pro-series.jpg",
      price: 450,
      date: "January 19, 10:02 AM",
      status: ProductStatus.inTransit,
      description: 'ReloadedReloadedReloadedReloadedReloaded',
    ),
    Product(
      title: "Macbook 14",
      imageUrl:
          "https://media.vneconomy.vn/w800/images/upload/2024/09/10/apple-iphone-16-pro-series.jpg",
      price: 450,
      date: "January 19, 10:02 AM",
      status: ProductStatus.completed,
      description: 'ReloadedReloadedReloadedReloadedReloaded',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            "Manage Your Posts",
            style: Theme.of(context).textTheme.lightBodyXLargeSemiBold,
          ),
          backgroundColor: Colors.white,
          elevation: 0,
          foregroundColor: Colors.black,
          bottom: TabBar(
            labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: Theme.of(context).primary(context),
                  fontWeight: FontWeight.w800,
                ),
            indicatorSize: TabBarIndicatorSize.tab,
            unselectedLabelStyle:
                Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Colors.grey,
                      fontWeight: FontWeight.w300,
                    ),
            dividerColor: Colors.grey,
            indicator:  BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).primary(context),
                  width: 2,
                ),
              ),
            ),
            tabs:const [
               Tab(text: "Currently Displaying"),
               Tab(text: "Pending"),
               Tab(text: "Processing"),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            ListView.builder(
              padding: const EdgeInsets.all(10),
              itemCount: products.length,
              itemBuilder: (context, index) {
                return ProductCartPost(product: products[index]);
              },
            ),
            const Center(child: Text("Pending Items")),
            const Center(child: Text("Processing Items")),
          ],
        ),
      ),
    );
  }
}
