import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/sames/custom_dropdown_button.dart';

class OrderSupportPage extends StatelessWidget {
  const OrderSupportPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(
          "Order support",
          style: TextStyle(
            color: Colors.black,
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(0),
              child: SizedBox(
                width: double.infinity,
                child: CustomDropDownButton(
                  hintText: "Choose you action",
                  isCheck: false,
                  hintTextColor: Theme.of(context).textSecondary100(context),
                  iconColor: Theme.of(context).textSecondary100(context),
                  arr: const [
                    "Request a refund",
                    "Cancel order",
                    "Modify order details",
                    "Report product issue",
                    "Check order status",
                    "Request exchange/return",
                    "Report delivery delay",
                    "Complain about service",
                    "Other support request",
                  ],
                ),
              ),
            ),
            Gap(20.h),
            TextField(
              decoration: InputDecoration(
                hintText: "Order ID",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
            ),
            Gap(20.h),
            TextField(
              maxLines: 5,
              decoration: InputDecoration(
                hintText: "Enter reason refusal",
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
            Gap(20.h),
            Text(
              "Your photo",
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            Gap(8.h),
            GestureDetector(
                onTap: () {
                  // Handle file upload
                },
                child: SvgPicture.asset(
                  AppAssets.uploadPhotoSvg,

                )),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Handle report submission

                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  "Report",
                  style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Gap(20.h),
          ],
        ),
      ),
    );
  }
}
