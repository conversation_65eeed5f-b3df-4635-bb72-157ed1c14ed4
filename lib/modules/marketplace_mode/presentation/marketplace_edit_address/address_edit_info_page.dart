import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import '../../data/models/address.dart';

class AddressEditInfoPage extends StatefulWidget {
  final bool isCheckUpdate;
  final Address? stateAddress;
  const AddressEditInfoPage({
    super.key,
    this.stateAddress,
    this.isCheckUpdate = false,
  });

  @override
  State<AddressEditInfoPage> createState() => _AddressEditInfoPageState();
}

class _AddressEditInfoPageState extends State<AddressEditInfoPage> {
  TextEditingController recipientNameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController specificAddressController = TextEditingController();
  TextEditingController addressTypeController = TextEditingController();
  TextEditingController cityController = TextEditingController();
  TextEditingController stateController = TextEditingController();
  TextEditingController countryController = TextEditingController();

  bool isDefault = false;

  @override
  void initState() {
    super.initState();
    if (widget.isCheckUpdate == true) {
      recipientNameController.text = widget.stateAddress?.recipientName ?? '';
      phoneController.text = widget.stateAddress?.phone ?? '';
      addressController.text = widget.stateAddress?.city ?? '';
      specificAddressController.text = widget.stateAddress?.city ?? '';
      countryController.text =
          widget.stateAddress?.countryId?.toString() ?? '0';
      isDefault = widget.stateAddress?.isDefault ?? false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            "Address ",
            style: Theme.of(context).textTheme.lightBodyXLargeBold,
          ),
          centerTitle: true,
        ),
        body: Padding(
          padding: const EdgeInsets.all(12.0),
          child: SingleChildScrollView(
            child: BlocConsumer<AddressBloc, AddressState>(
              listenWhen: (previous, current) {
                return widget.isCheckUpdate
                    ? previous.updateAddress != current.updateAddress
                    : previous.createAddress != current.createAddress ||
                        previous.isError != current.isError;
              },
              listener: (context, state) {
                if (state.isLoading) {
                  AppLoader.show(context);
                } else {
                  AppLoader.hide();
                }

                if (state.isSuccess) {
                  context.pop();
                }
                if (state.isError && state.errorMessage != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.errorMessage!),
                      backgroundColor: Colors.grey[500],
                      duration: Duration(seconds: 1),
                    ),
                  );
                }
              },
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Full Name & Phone Fields
                    Row(
                      children: [
                        Expanded(
                          child: _buildTextField(LocaleKeys.fullName.tr(),
                              LocaleKeys.name.tr(), recipientNameController),
                        ),
                        Gap(12.w),
                        Expanded(
                          child: _buildTextField(LocaleKeys.phone.tr(),
                              "(+84) 366254756", phoneController),
                        ),
                      ],
                    ),
                    Gap(10.h),
                    // Address Field
                    Row(
                      children: [
                        Expanded(
                          child: _buildTextField(
                            LocaleKeys.addressLabel.tr(),
                            LocaleKeys.addressLabel.tr(),
                            addressController,
                            onSubmitted: (value) {},
                          ),
                        ),
                      ],
                    ),
                    Gap(10.h),
                    // Specific Address Field
                    Row(
                      children: [
                        Expanded(
                          child: _buildTextField(
                            "Specific Address",
                            "Specific Address",
                            specificAddressController,
                          ),
                        ),
                      ],
                    ),

                    Gap(15.h),

                    // Set Default Address
                    BlocBuilder<AddressBloc, AddressState>(
                      builder: (context, state) {
                        return Column(
                          children: [
                            Row(
                              children: [
                                Checkbox(
                                  checkColor: Colors.white,
                                  value: state.isDefault,
                                  onChanged: (value) {
                                    context.read<AddressBloc>().add(
                                        ToggleIsDefaultEvent(value ?? false));
                                  },
                                ),
                                Text(
                                  LocaleKeys.setDefaultCheckboxLabel.tr(),
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.normal),
                                ),
                              ],
                            ),
                            Gap(20.h),

                            // Save Button
                            GestureDetector(
                              onTap: () {
                                widget.isCheckUpdate
                                    ? context
                                        .read<AddressBloc>()
                                        .add(UpdateAddressEvent(
                                          addressId: widget.stateAddress?.id,
                                          phone: phoneController.text.trim(),
                                          street: addressController.text.trim(),
                                          recipientName: recipientNameController
                                              .text
                                              .trim(),
                                          city: specificAddressController.text
                                              .trim(),
                                          isDefault: state.isDefault,
                                        ))
                                    : context
                                        .read<AddressBloc>()
                                        .add(CreateAddressEvent(
                                          phone: phoneController.text.trim(),
                                          street: addressController.text.trim(),
                                          recipientName: recipientNameController
                                              .text
                                              .trim(),
                                          city: specificAddressController.text
                                              .trim(),
                                          isDefault: state.isDefault,
                                        ));
                              },
                              child: Container(
                                width: double.infinity,
                                height: 50.h,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).errorBase(context),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Center(
                                  child: Text(
                                    LocaleKeys.saveChangeButton.tr(),
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // Custom Widget for Input Fields
  Widget _buildTextField(
      String label, String placeholder, TextEditingController controller,
      {Function(String)? onSubmitted}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: controller,
          decoration: InputDecoration(
            label: Text(
              label,
            ),
            labelStyle: const TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
            contentPadding:
                const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onSubmitted: onSubmitted,
        ),
      ],
    );
  }
}
