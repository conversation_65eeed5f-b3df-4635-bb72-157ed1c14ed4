import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/date_time_utils.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product_detail/bloc/product_detail_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/products_widget/about.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/products_widget/delivery_tips.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/products_widget/product_img.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/products_widget/product_title_price.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/products_widget/seller_inf.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/products_widget/suggets_product.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';

class ProductDetailsScreen extends StatelessWidget {
  const ProductDetailsScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProductDetailBloc, ProductDetailState>(
        listener: (context, state) {
      if (state.product.status == Status.loading) {
        AppLoader.show(context);
      } else {
        AppLoader.hide();
      }
    }, builder: (context, state) {
      if (state.product.status == Status.completed) {
        return Scaffold(
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ProductImageCarousel(
                  images: state.product.data!.mediaUrls.isNotEmpty
                      ? state.product.data!.mediaUrls
                      : [],
                ),
                ProductTitleAndPrice(
                  title: state.product.data!.title,
                  location: state.product.data?.locations.first.address ?? '',
                  price: state.product.data!.price,
                  created:
                      getTimeAgo(state.product.data!.createdAt, context.locale),
                ),
                Gap(16.h),
                SellerInfo(
                  sellerName: state.product.data?.sellerName ?? 'N/A',
                ),
                AboutThisItem(
                  condition: state.product.data?.condition ?? '',
                  type: 'product',
                  description: state.product.data?.description ?? '',
                ),
                Gap(16.h),
                DeliveryAndSafetyTips(),
                Gap(24.h),
                const RelatedProducts(),
              ],
            ),
          ),
          bottomNavigationBar: BuyNowButton(
            price: state.product.data!.price.toString(),
          ),
        );
      } else if (state.product.status == Status.error) {
        return const Center(child: Text('Unable to load product data'));
      } else {
        return const Scaffold(body: SizedBox.shrink());
      }
    });
  }
}

class BuyNowButton extends StatelessWidget {
  const BuyNowButton({super.key, required this.price});
  final String price;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ElevatedButton(
            onPressed: () {
              // Handle buy now action
              context.push(RouteName.confirmOrderScreen, extra: {
                'product': context.read<ProductDetailBloc>().state.product.data,
              }); // Navigate to confirm order screen
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).secondary(context),
              padding: const EdgeInsets.symmetric(
                  vertical: 8, horizontal: 14), // Adjusted padding
              minimumSize: const Size(double.infinity, 48),

              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(AppAssets.shopMarketSvg),
                const Gap(8),
                Text(
                  '${LocaleKeys.buyNow.tr()} (US\$$price)',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
                  ),
                )
              ],
            ),
          ),
          Gap(10),
        ],
      ),
    );
  }
}
