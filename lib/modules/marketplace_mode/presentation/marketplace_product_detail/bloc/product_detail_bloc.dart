import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_detail_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/similar_product.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';

part 'product_detail_event.dart';
part 'product_detail_state.dart';

class ProductDetailBloc extends Bloc<ProductDetailEvent, ProductDetailState> {
  final ProductRepository _productRepository;
  ProductDetailBloc(this._productRepository)
      : super(ProductDetailState.initial()) {
    on<GetProductApiEvent>(_onGetProduct);
    on<GetSimilarProductApiEvent>(_onGetSimilarProduct);
  }

  Future<void> _onGetProduct(
    GetProductApiEvent event,
    Emitter<ProductDetailState> emit,
  ) async {
    try {
      emit(state.copyWith(product: const ApiResponse.loading()));

      /// get product
      final product = await _productRepository.getProductDetail(pid: event.pid);
      emit(state.copyWith(product: ApiResponse.completed(product)));
    } on NetworkException catch (e) {
      emit(state.copyWith(product: ApiResponse.error(e.message)));
    }
  }

  Future<void> _onGetSimilarProduct(
    GetSimilarProductApiEvent event,
    Emitter<ProductDetailState> emit,
  ) async {
    try {
      emit(state.copyWith(similarProduct: const ApiResponse.loading()));

      /// get product
      final product =
          await _productRepository.getSimilarProducts(pid: event.pid);
      emit(state.copyWith(similarProduct: ApiResponse.completed(product)));
    } on NetworkException catch (e) {
      emit(state.copyWith(similarProduct: ApiResponse.error(e.message)));
    }
  }
}
