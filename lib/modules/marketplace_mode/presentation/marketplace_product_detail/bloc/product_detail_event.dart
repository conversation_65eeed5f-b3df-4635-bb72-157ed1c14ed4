part of 'product_detail_bloc.dart';

sealed class ProductDetailEvent extends Equatable {
  const ProductDetailEvent();

  @override
  List<Object> get props => [];
}

class GetProductApiEvent extends ProductDetailEvent {
  final String pid;
  const GetProductApiEvent({required this.pid});
}


class GetSimilarProductApiEvent extends ProductDetailEvent {
  final String pid;
  const GetSimilarProductApiEvent({required this.pid});
}
