part of 'product_detail_bloc.dart';

class ProductDetailState extends Equatable {
  final ApiResponse<ProductDetail> product;
  final ApiResponse<SimilarProduct> similarProduct;

  const ProductDetailState({
    required this.similarProduct,
    required this.product,
  });

  ProductDetailState copyWith({
    ApiResponse<ProductDetail>? product,
    ApiResponse<SimilarProduct>? similarProduct,
  }) {
    return ProductDetailState(
      product: product ?? this.product,
      similarProduct: similarProduct ?? this.similarProduct,
    );
  }

  factory ProductDetailState.initial() {
    return const ProductDetailState(
      product: ApiResponse.loading(),
      similarProduct: ApiResponse.loading(),
    );
  }

  @override
  List<Object?> get props => [product, similarProduct];
}
