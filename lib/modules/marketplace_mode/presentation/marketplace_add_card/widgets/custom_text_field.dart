import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class CustomTextField extends StatelessWidget {
  final String label;
  final String hint;
  final bool isCVV;

  const CustomTextField({
    Key? key,
    required this.label,
    required this.hint,
    this.isCVV = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
        ),
        Gap(8.h),
        TextFormField(
          obscureText: isCVV,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            hintText: hint,
          ),
        ),
      ],
    );
  }
}
