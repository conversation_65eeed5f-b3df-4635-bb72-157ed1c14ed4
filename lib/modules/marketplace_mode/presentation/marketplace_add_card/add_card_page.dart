import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';

class AddCardPage extends StatelessWidget {
  const AddCardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.addNewCardTitle.tr()),
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocaleKeys.cardDetailsTitle.tr(),
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold),
            ),
            Gap(16.h),

            CardField(
              decoration: const InputDecoration(border: OutlineInputBorder()),
              onCardChanged: (card) {
                print("card: $card");
              },
            ),
            // CustomTextField(
            //   label: LocaleKeys.cardNumberLabel.tr(),
            //   hint: LocaleKeys.cardNumberHint.tr(),
            //   isCVV: false,
            // ),
            // Gap(16.h),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Expanded(
            //         child: CustomTextField(
            //       label: LocaleKeys.expiryDateLabel.tr(),
            //       hint: LocaleKeys.expiryDateHint.tr(),
            //       isCVV: false,
            //     )),
            //     Gap(16.w),
            //     Expanded(
            //         child: CustomTextField(
            //       label: LocaleKeys.cvvLabel.tr(),
            //       hint: LocaleKeys.cvvLabel.tr(),
            //       isCVV: true,
            //     )),
            //   ],
            // ),
            // Gap(16.h),
            // CustomTextField(
            //   label: LocaleKeys.cardholderNameLabel.tr(),
            //   hint: LocaleKeys.cardholderNameHint.tr(),
            //   isCVV: false,
            // ),
            // Gap(16.h),
            // Text(
            //   " ${LocaleKeys.verificationTransaction.tr()} \$0.50 ${LocaleKeys.verificationTransactionText.tr()}",
            //   style: TextStyle(fontSize: 12.sp, color: Colors.grey),
            // ),
            // const Spacer(),
            // ElevatedButton(
            //   onPressed: () {
            //     // Handle confirmation
            //   },
            //   style: ElevatedButton.styleFrom(
            //     backgroundColor: Theme.of(context).errorBase(context),
            //     padding: EdgeInsets.symmetric(vertical: 12.h),
            //     minimumSize: Size(double.infinity, 50.h), // Full width button
            //   ),
            //   child: Text(
            //     LocaleKeys.confirmButtonText.tr(),
            //     style: TextStyle(color: Colors.white, fontSize: 16.sp),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
