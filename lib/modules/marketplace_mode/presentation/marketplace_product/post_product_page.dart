import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/category.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/bloc/post_product_bloc.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import 'package:multime_app/shared/widgets/sames/custom_dropdown_button.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/bottom/bottomSheetPostProduct.dart';

class PostProductPage extends StatefulWidget {
  const PostProductPage({super.key});

  @override
  State<PostProductPage> createState() => _PostProductPageState();
}

class _PostProductPageState extends State<PostProductPage> {
  bool _isChecked = false;
  final TextEditingController _controllerTitle =
      TextEditingController(text: 'Test Product Title');
  final TextEditingController _controllerDescription =
      TextEditingController(text: 'Test Product Description');
  final TextEditingController _controllerPrice =
      TextEditingController(text: '10');
  final TextEditingController _controllerAddress =
      TextEditingController(text: 'Test Product Address');
  String? _errorText;

  @override
  void dispose() {
    _controllerTitle.dispose();
    _controllerDescription.dispose();
    _controllerPrice.dispose();
    _controllerAddress.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PostProductBloc, PostProductState>(
      listenWhen: (previous, current) {
        return previous.postProduct?.status != current.postProduct?.status ||
            previous.postImage?.status != current.postImage?.status;
      },
      listener: (context, state) {
        if (state.postProduct?.status == Status.loading ||
            state.postImage?.status == Status.loading) {
          AppLoader.show(context);
        } else {
          AppLoader.hide();
        }
        if (state.postProduct?.status == Status.error ||
            state.postImage?.status == Status.error) {
          // context.pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(state.postProduct?.message ??
                    state.postProduct?.message ??
                    '')),
          );
        } else if (state.postProduct?.status == Status.completed &&
            state.postImage?.status != Status.completed) {
          context
              .read<PostProductBloc>()
              .add(UploadImageEvent(state.postProduct!.data!.id));
        } else if (state.postImage?.status == Status.completed) {
          context.pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Product Posted successfully')),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => context.pop(),
          ),
          title: const Text(
            "Post product",
            style: TextStyle(color: Colors.black),
          ),
          backgroundColor: Colors.white,
          elevation: 0,
        ),
        body: Stack(
          children: [
            // Nội dung chính
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.horizontalEdge,
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    10.verticalSpace,
                    BlocConsumer<PostProductBloc, PostProductState>(
                      listener: (context, state) {
                        // print("listening to: $state");
                      },
                      builder: (context, state) {
                        // print("current state: $state");
                        if (state.images.isNotEmpty) {
                          return Wrap(
                              spacing: 8.0, // Space between images
                              runSpacing: 8.0, // Space between rows
                              children:
                                  List.generate(state.images.length, (index) {
                                return Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(
                                          8), // Optional rounded corners
                                      child: Image.file(
                                        File(state.images[index].path),
                                        width: 100, // Adjust size as needed
                                        height: 100,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Positioned(
                                      top: -5,
                                      right: -5,
                                      child: GestureDetector(
                                        onTap: () {
                                          print('called');
                                          context.read<PostProductBloc>().add(
                                              DeleteImageEvent(
                                                  state.images[index]));
                                        },
                                        child: Container(
                                          decoration: const BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          padding: const EdgeInsets.all(4),
                                          child: const Icon(Icons.close,
                                              color: Colors.white, size: 16),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }));
                        } else {
                          return GestureDetector(
                            onTap: () {
                              context
                                  .read<PostProductBloc>()
                                  .add(const ImageUpdateEvent());
                            },
                            child: SvgPicture.asset(AppAssets.uploadSvg),
                          );
                        }
                      },
                    ),
                    Gap(20.h),
                    StrongBodyTextField(
                      controller: _controllerTitle,
                      labalText: LocaleKeys.productHintText.tr(),
                      maxLines: 2,
                      onChanged: (text) {
                        // Kiểm tra độ dài khi người dùng nhập
                        if (text.length < 50 || text.length > 150) {
                          setState(() {
                            _errorText =
                                'Title must be between 50 and 150 characters.';
                          });
                        } else {
                          setState(() {
                            _errorText = null; // Không có lỗi nếu độ dài hợp lệ
                          });
                        }
                      },
                      errorText: _errorText,
                    ),
                    Gap(16.h),
                    StrongBodyTextField(
                      controller: _controllerDescription,
                      maxLines: 3,
                      labalText:
                          'Outstanding features of the product, what it includes, how long it has been used...',
                    ),
                    Gap(16.h),
                    // Status Dropdown
                    Text(LocaleKeys.statusText.tr(),
                        style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.normal)),
                    Gap(5.h),
                    CustomDropDownButton(
                      arr: const ['New', 'Used'],
                      hintText: 'Choose your product\'s status,',
                      onChanged: (status) {
                        context
                            .read<PostProductBloc>()
                            .add(StatusSelectedEvent(status));
                      },
                    ),

                    Gap(16.h),
                    // Category Dropdown
                    const Text("Category",
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.normal)),
                    Gap(5.h),
                    BlocBuilder<PostProductBloc, PostProductState>(
                      builder: (context, state) {
                        return CustomDropDownButton(
                          arr: List.generate(
                              state.categories?.data?.items?.length ?? 0,
                              (index) {
                            return state.categories!.data!.items![index].name!;
                          }),
                          hintText: 'Choose your product\'s category,',
                          onChanged: (selectedCategory) {
                            Category category = state.categories!.data!.items!
                                .where((cat) => cat.name == selectedCategory)
                                .toList()
                                .first;
                            context
                                .read<PostProductBloc>()
                                .add(CategorySelectedEvent(category.id ?? ''));
                          },
                        );
                      },
                    ),
                    Gap(16.h),
                    StrongBodyTextField(
                      labalText: 'Type your price',
                      controller: _controllerPrice,
                      prefixIcon: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text(
                          "\$",
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      hintStyle: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey,
                      ),
                    ),

                    // Price
                    Row(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft, // Căn trái Checkbox
                          child: Transform.translate(
                            offset: const Offset(-8, 0),
                            // Dịch sang trái thêm chút (có thể điều chỉnh offset)
                            child: Checkbox(
                              value: _isChecked,
                              activeColor:
                                  Theme.of(context).informationBase(context),
                              onChanged: (bool? newValue) {
                                setState(() {
                                  _isChecked = newValue ?? false;
                                });
                              },
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _isChecked =
                                  !_isChecked; // Chuyển trạng thái khi nhấn vào label
                            });
                          },
                          child: Text(
                            "Include shipping price",
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),

                    StrongBodyTextField(
                      controller: _controllerAddress,
                      label: 'Address',
                      hintText: 'Type your address here...',
                    ),
                    Gap(16.h),
                    Row(
                      children: [
                        SvgPicture.asset(AppAssets.warningNoteSvg),
                        Gap(8.h),
                        Expanded(
                          child: Text(
                            "Note: Please check to make sure the content of the ad matches the product you are selling.",
                            style: TextStyle(
                              color: Colors.amber,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.normal,
                            ),
                            softWrap: true,
                            overflow: TextOverflow.visible,
                          ),
                        ),
                      ],
                    ),
                    Gap(140.h)
                  ],
                ),
              ),
            ),
            // Phần cố định dưới cùng
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                color: Colors.white, // Đặt màu nền để che nội dung khi cuộn
                padding:
                    const EdgeInsets.only(left: 16.0, right: 16, bottom: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Divider(
                      thickness: 1,
                      color: Colors.grey,
                    ),
                    GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          backgroundColor:
                              Theme.of(context).whitePrimary(context),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(35),
                            ),
                          ),
                          builder: (bottomsheetcontext) {
                            return const CustomBottomSheetContent();
                          },
                        );
                      },
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: "Select: ",
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.normal, // In thường
                                  ),
                                ),
                                TextSpan(
                                  text: "Free posting",
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold, // In đậm
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            CupertinoIcons.chevron_down,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      thickness: 1,
                      color: Colors.grey,
                    ),
                    Gap(16.h),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          context.read<PostProductBloc>().add(
                                PostProductApiEvent(
                                  {
                                    "category_id": context
                                        .read<PostProductBloc>()
                                        .state
                                        .selectedCategory,
                                    "title": _controllerTitle.text.trim(),
                                    "description":
                                        _controllerDescription.text.trim(),
                                    "price":
                                        num.parse(_controllerPrice.text.trim()),
                                    "seller_id":
                                        getIt<GlobalStorage>().user?.id,
                                    "condition": context
                                        .read<PostProductBloc>()
                                        .state
                                        .statusSelected!
                                        .toLowerCase(),
                                    "status": context
                                        .read<PostProductBloc>()
                                        .state
                                        .statusSelected!
                                        .toLowerCase(),
                                    "locations": [
                                      {
                                        "address":
                                            _controllerAddress.text.trim(),
                                        "city": "string",
                                        "country": "string",
                                        "location_lat": 0,
                                        "location_long": 0,
                                        "postal_code": "string",
                                        "state": "string"
                                      }
                                    ],
                                    // "dimension_unit": "cm",
                                    // "height": 0,
                                    // "length": 0,
                                    // "weight": 0,
                                    // "weight_unit": "kg",
                                    // "width": 0,
                                    "quantity": 10,
                                  },
                                ),
                              );
                          // Thực hiện post sản phẩm
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: Colors.red,
                        ),
                        child: const Text(
                          "Post product",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // void showCustomBottomSheet(BuildContext parentcontext) {
  // showModalBottomSheet(
  //   context: parentcontext,
  //   isScrollControlled: true,
  //   backgroundColor: Theme.of(parentcontext).whitePrimary(parentcontext),
  //   shape: const RoundedRectangleBorder(
  //     borderRadius: BorderRadius.vertical(
  //       top: Radius.circular(35),
  //     ),
  //   ),
  //   builder: (bottomsheetcontext) {
  //     return const CustomBottomSheetContent();
  //   },
  // );
  // }

}
