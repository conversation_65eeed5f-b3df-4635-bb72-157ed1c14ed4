import 'package:bloc/bloc.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/grid_bloc/product_grid_event.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/grid_bloc/product_grid_state.dart';

class ProductGridBloc extends Bloc<ProductGridEvent, ProductGridState> {
  final ProductRepositoryRemote _productRepositoryRemote;
  ProductGridBloc(this._productRepositoryRemote)
      : super(ProductGridState.initial()) {
    on<GetSeeMoreProductEvent>(_onGetSeeMoreProductEvent);
    on<UpdateSectionTitle>(_onUpdateSectionTitle);
  }

  _onGetSeeMoreProductEvent(
    GetSeeMoreProductEvent event,
    Emitter<ProductGridState> emit,
  ) async {
    try {
      // emit(MarketplaceFeaturedLoading());
      Section products = await _productRepositoryRemote.getSeeMoreProducts(
          section: event.section);

      emit(state.copyWith(
        products: ApiResponse.completed(products.items),
        // allProducts: newProducts + likeNewProducts,
      ));
    } on NetworkException catch (e) {
      print(e.message);
      emit(state.copyWith(
        products: ApiResponse.error(e.message),
        // allProducts: newProducts + likeNewProducts,
      ));
    } catch (e) {
      emit(state.copyWith(
        products: ApiResponse.error(e.toString()),
        // allProducts: newProducts + likeNewProducts,
      ));
    }
  }

  _onUpdateSectionTitle(
    UpdateSectionTitle event,
    Emitter<ProductGridState> emit,
  ) {
    emit(state.copyWith(title: event.title));
  }
}
