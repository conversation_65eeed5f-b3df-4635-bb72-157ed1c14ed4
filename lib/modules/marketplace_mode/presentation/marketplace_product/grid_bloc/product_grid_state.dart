import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';

class ProductGridState extends Equatable {
  final ApiResponse<List<Product>> products;
  final String title;

  const ProductGridState({
    this.products = const ApiResponse.loading(),
    required this.title,
  });
  ProductGridState copyWith({
    ApiResponse<List<Product>>? products,
    String? title,
  }) {
    return ProductGridState(
      title: title ?? this.title,
      products: products ?? this.products,
    );
  }

  factory ProductGridState.initial() {
    return const ProductGridState(title: '');
  }

  @override
  List<Object> get props => [products, title];
}
