import 'package:equatable/equatable.dart';

sealed class ProductGridEvent extends Equatable {
  const ProductGridEvent();

  @override
  List<Object> get props => [];
}

class GetSeeMoreProductEvent extends ProductGridEvent {
  final String section;
  const GetSeeMoreProductEvent({required this.section});
}

class UpdateSectionTitle extends ProductGridEvent {
  final String title;
  const UpdateSectionTitle({required this.title});
}
