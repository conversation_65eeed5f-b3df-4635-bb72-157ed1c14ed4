part of 'post_product_bloc.dart';

sealed class PostProductEvent extends Equatable {
  const PostProductEvent();

  @override
  List<Object> get props => [];
}

class GetCategoriesEvent extends PostProductEvent {
  const GetCategoriesEvent();
}

class PostProductApiEvent extends PostProductEvent {
  final Map<String, dynamic> data;
  const PostProductApiEvent(this.data);
}

class CategorySelectedEvent extends PostProductEvent {
  final String selectedCategory;
  const CategorySelectedEvent(this.selectedCategory);
}

class StatusSelectedEvent extends PostProductEvent {
  final String selectedStatus;
  const StatusSelectedEvent(this.selectedStatus);
}

class ImageUpdateEvent extends PostProductEvent {
  const ImageUpdateEvent();
}

class DeleteImageEvent extends PostProductEvent {
  final XFile image;
  const DeleteImageEvent(this.image);
}

class UploadImageEvent extends PostProductEvent {
  final String pid;
  const UploadImageEvent(this.pid);
}
