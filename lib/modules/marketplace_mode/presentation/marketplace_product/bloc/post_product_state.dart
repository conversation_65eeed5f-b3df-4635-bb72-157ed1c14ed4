part of 'post_product_bloc.dart';

class PostProductState extends Equatable {
  final ApiResponse<CategoryList>? categories;
  final ApiResponse<Product>? postProduct;
  final ApiResponse<List<String>>? postImage;
  final String? selectedCategory;
  final String? statusSelected;
  final List<XFile> images;

  const PostProductState({
    this.categories,
    this.postProduct,
    this.selectedCategory,
    this.statusSelected,
    this.images = const [],
    this.postImage,
  });

  PostProductState copyWith({
    ApiResponse<CategoryList>? categories,
    ApiResponse<Product>? postProduct,
    ApiResponse<List<String>>? postImage,
    String? selectedCategory,
    String? statusSelected,
    List<XFile>? images,
  }) {
    return PostProductState(
      categories: categories ?? this.categories,
      postProduct: postProduct ?? this.postProduct,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      statusSelected: statusSelected ?? this.statusSelected,
      images: images ?? this.images,
      postImage: postImage ?? this.postImage,
    );
  }

  factory PostProductState.initial() {
    return const PostProductState(
        categories: null,
        postProduct: null,
        selectedCategory: null,
        statusSelected: null,
        postImage: null
        );
  }
  @override
  List<Object?> get props => [
        categories,
        postProduct,
        selectedCategory,
        statusSelected,
        images,
        postImage
      ];
}
