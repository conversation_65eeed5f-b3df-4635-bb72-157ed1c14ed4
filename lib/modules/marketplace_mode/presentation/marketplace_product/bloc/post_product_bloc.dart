import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/category.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/category_repository.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';
import 'package:http_parser/http_parser.dart';

part 'post_product_event.dart';
part 'post_product_state.dart';

class PostProductBloc extends Bloc<PostProductEvent, PostProductState> {
  final CategoryRepositoryRemote _categoryRepositoryRemote;
  final ProductRepositoryRemote _productRepositoryRemote;
  PostProductBloc(this._categoryRepositoryRemote, this._productRepositoryRemote)
      : super(PostProductState.initial()) {
    on<GetCategoriesEvent>((event, emit) => _onGetCategories(event, emit));
    on<PostProductApiEvent>((event, emit) => _onPostProductApi(event, emit));
    on<CategorySelectedEvent>(
        (event, emit) => _onCategorySelected(event, emit));
    on<StatusSelectedEvent>((event, emit) => _onStatusSelected(event, emit));
    on<ImageUpdateEvent>((event, emit) => _onImageUpdate(event, emit));
    on<DeleteImageEvent>((event, emit) => _onImageDelete(event, emit));
    on<UploadImageEvent>(
        (event, emit) => _onUploadProductImageApi(event, emit));
  }

  _onGetCategories(
    GetCategoriesEvent event,
    Emitter<PostProductState> emit,
  ) async {
    try {
      emit(state.copyWith(categories: const ApiResponse.loading()));
      CategoryList products = await _categoryRepositoryRemote.getCategoryList();
      emit(state.copyWith(categories: ApiResponse.completed(products)));
    } on NetworkException catch (e) {
      emit(state.copyWith(categories: ApiResponse.error(e.message)));
    }
  }

  _onPostProductApi(
    PostProductApiEvent event,
    Emitter<PostProductState> emit,
  ) async {
    if (state.images.isEmpty) {
      emit(state.copyWith(
          postProduct: const ApiResponse.error('Select images to continue')));
    } else {
      try {
        emit(state.copyWith(postProduct: const ApiResponse.loading()));
        Product product =
            await _productRepositoryRemote.postProduct(data: event.data);
        emit(state.copyWith(postProduct: ApiResponse.completed(product)));
      } on NetworkException catch (e) {
        emit(state.copyWith(postProduct: ApiResponse.error(e.message)));
      } catch (e) {
        emit(state.copyWith(
            postProduct: const ApiResponse.error(
                'Unable to process your request at this time.')));
      }
    }
  }

  _onUploadProductImageApi(
    UploadImageEvent event,
    Emitter<PostProductState> emit,
  ) async {
    try {
      emit(state.copyWith(postImage: const ApiResponse.loading()));

      var files = await Future.wait(state.images.map((img) async {
        var file = await MultipartFile.fromFile(
          img.path,
          contentType: MediaType('image',
              img.path.split('.').last), // Or whatever the image type is
        );
        return file;
      }).toList());
      print("files");
      var resp = await _productRepositoryRemote.uploadProductImage(
          data: FormData.fromMap({"files": files}), pid: event.pid);

      emit(state.copyWith(postImage: ApiResponse.completed(resp)));
    } on NetworkException catch (e) {
      emit(state.copyWith(postImage: ApiResponse.error(e.message)));
    } catch (e) {
      emit(state.copyWith(
          postImage: const ApiResponse.error(
              'Unable to process your request at this time.')));
    }
  }

  _onCategorySelected(
    CategorySelectedEvent event,
    Emitter<PostProductState> emit,
  ) {
    emit(state.copyWith(selectedCategory: event.selectedCategory));
  }

  _onStatusSelected(
    StatusSelectedEvent event,
    Emitter<PostProductState> emit,
  ) {
    emit(state.copyWith(statusSelected: event.selectedStatus));
  }

  Future<void> _onImageUpdate(
    ImageUpdateEvent event,
    Emitter<PostProductState> emit,
  ) async {
    final ImagePicker picker = ImagePicker();
    List<XFile>? images = await picker.pickMultiImage();

    if (images.isNotEmpty) {
      emit(state.copyWith(images: images));
    }
  }

  void _onImageDelete(
    DeleteImageEvent event,
    Emitter<PostProductState> emit,
  ) {
    List<XFile> updatedImages = List.from(state.images)..remove(event.image);
    emit(state.copyWith(images: updatedImages));
  }
}
