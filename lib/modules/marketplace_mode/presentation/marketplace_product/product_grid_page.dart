import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/base/api_response/status.dart';

import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/grid_bloc/product_grid_bloc.dart';

import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/grid_bloc/product_grid_state.dart';

import 'package:multime_app/shared/models/marketplace/product_fake_data.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';

// Hàm định dạng ngày
String formatDate(DateTime date) {
  return DateFormat('d MMM').format(date); // Định dạng ngày theo "12 Sep"
}

class ProductGridScreen extends StatefulWidget {
  final String section;
  final String title;
  const ProductGridScreen({
    super.key,
    required this.section,
    required this.title,
  });

  @override
  State<ProductGridScreen> createState() => _ProductGridScreenState();
}

class _ProductGridScreenState extends State<ProductGridScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.chevron_back),
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(
          widget.title,
          style: TextStyle(
            color: Colors.black,
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: BlocConsumer<ProductGridBloc, ProductGridState>(
        listener: (context, state) {
          if (state.products.status == Status.loading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
        },
        builder: (context, state) {
          if (state.products.status == Status.error) {
            // AppLoader.hide();

            return Text('${state.products.message}');
          } else if (state.products.status == Status.completed) {
            // AppLoader.hide();
            if (state.products.data!.isEmpty) {
              return const Center(
                child: Text('There are no more products currently.'),
              );
            } else {
              return Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
                child: GridView.builder(
                  itemCount: state.products.data!.length, // Số lượng sản phẩm
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2, // Số cột
                    mainAxisSpacing: 12,
                    crossAxisSpacing: 12,
                    childAspectRatio: 0.73, // Tỉ lệ chiều ngang/dọc
                  ),
                  itemBuilder: (context, index) {
                    return _buildProductCard(state.products.data![index]);
                  },
                ),
              );
            }
          } else {
            return const SizedBox.shrink();
          }
        },
      ),
    );
  }

  Widget _buildProductCard(Product product) {
    return GestureDetector(
      onTap: () {
        context.push(RouteName.productDetailScreen, extra: product.id);
      },
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hình ảnh sản phẩm
            Stack(
              children: [
                product.imageUrl == null
                    ? SizedBox(
                        height: 160.h,
                        width: double.infinity,
                        child:  Icon(
                          Icons.image,
                          color: Theme.of(context).greyScale700(context),
                        ),
                      )
                    : ClipRRect(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        child: Image.network(
                          product.imageUrl!, // Thay bằng hình ảnh của bạn
                          height: 160.h,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey.withOpacity(0.5),
                    child: IconButton(
                      onPressed: () {},
                      icon: Icon(
                        CupertinoIcons.heart,
                        size: 18.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Gap(8.h),
            // Tên sản phẩm
            Text(
              product.title ?? 'N/A',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  product.location?.address ?? '',
                  style: TextStyle(fontSize: 10.sp, color: Colors.grey),
                ),
                Text(
                  formatDate(
                      fakeDataProduct[0].postDate), // Hiển thị ngày định dạng
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.grey,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            // Hiển thị giá tiền đã định dạng
            Text(
              NumberFormat.simpleCurrency(decimalDigits: 0)
                  .format(product.price),
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
