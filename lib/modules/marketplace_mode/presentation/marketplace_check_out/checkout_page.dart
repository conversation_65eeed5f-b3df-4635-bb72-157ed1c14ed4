import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/create_order_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_detail_model.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_confirm_order/bloc/payment_bloc.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../../shared/widgets/sames/build_bottom_total.dart';

class CheckoutScreen extends StatelessWidget {
  const CheckoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PaymentBloc, PaymentState>(listener: (context, state) {
      if (state.intentResponse?.status == Status.loading ||
          state.orderResponse?.status == Status.loading) {
        AppLoader.show(context);
      } else {
        AppLoader.hide();
      }
      if (state.orderResponse?.status == Status.completed) {
        context.go(RouteName.paymentSuccessPage);
      } else if (state.orderResponse?.status == Status.error) {
        context.go(RouteName.marketMode);
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Payment failed, try again later'),
            backgroundColor: Colors.red));
      }
    }, builder: (context, state) {
      if (state.intentResponse?.status == Status.completed) {
        final webViewController = WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                print("Loading progress: $progress%");
              },
              onPageStarted: (String url) {
                print("Page started loading: $url");
              },
              onPageFinished: (String url) {
                print("Page finished loading: $url");
              },
              onNavigationRequest: (NavigationRequest request) {
                print("Navigating to: ${request.url}");
                if (request.url == "http://success.com/") {
                  context.read<PaymentBloc>().add(
                        CreateOrderEvent(
                            buyerId: gs.user!.id!.toString(),
                            sellerId: state.productDetail!.sellerId,
                            items: [
                              OrderItem(
                                metadata: {
                                  'item': state.productDetail?.toJson()
                                },
                                quantity: 1,
                                referenceId: state.productDetail!.id,
                                referenceType: 'PRODUCT',
                                unitPrice: state.productDetail!.price,
                              ).toJson(),
                            ],
                            metadata: {
                              'intent': state.intentResponse?.data!.toJson(),
                              'buyer': gs.user,
                              'address': state.address?.toJson(),
                            },
                            notes: 'Please deliver items before 5 PM'),
                      );
                  return NavigationDecision.prevent;
                } else if (request.url == "http://cancel.com/") {
                  context.go(RouteName.marketMode);
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                      content: Text('Payment failed, try again later'),
                      backgroundColor: Colors.red));
                  return NavigationDecision.prevent;
                }
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(
              Uri.parse(state.intentResponse?.data?.checkoutUrl ?? ''));
        return Container(
          padding: EdgeInsets.only(top: 10.h),
          child: SafeArea(
            child: WebViewWidget(
              controller: webViewController,
            ),
          ),
        );
      } else {
        ProductDetail? product = state.productDetail;
        return Scaffold(
          appBar: _buildAppBar(context),
          backgroundColor: Colors.white,
          body: _buildBody(
            context,
            product,
            state.feeResponse!.data!.platformFee,
            state.feeResponse!.data!.totalAmount,
          ),
          bottomNavigationBar: BuildBottomTotal(
            total:
                '${context.read<PaymentBloc>().state.feeResponse?.data?.totalAmount}',
            onPressed: () {
              if (state.address != null) {
                context.read<PaymentBloc>().add(CreatePaymentIntent(
                    amount: state.feeResponse!.data!.totalAmount,
                    currency: "USD",
                    referenceType: 'product'));
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Address cannot be empty')));
              }
            },
          ),
        );
      }
    });
  }

  // AppBar
  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.white,
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.black),
        onPressed: () {
          context.pop();
        },
      ),
      title: Text(
        LocaleKeys.checkoutTitle.tr(),
        style: GoogleFonts.plusJakartaSans(
          fontSize: 20.sp,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
    );
  }

  // Main Body
  Widget _buildBody(
      BuildContext context, ProductDetail? product, platformFee, totalAmount) {
    return SingleChildScrollView(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildWarningMessage(),
        Gap(16.h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircleAvatar(
                radius: 6,
                backgroundColor: Colors.grey,
              ),
              Gap(8.w),
              Container(
                height: 2.h,
                width: 60.w,
                color: Colors.grey,
              ),
              Gap(8.w),
              CircleAvatar(
                radius: 6,
                backgroundColor: Theme.of(context).errorBase(context),
              ),
            ],
          ),
        ),
        Gap(16.h),
        _buildAddressSection(context),
        Gap(24.h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: _buildOrderDetails(
            product?.title,
            product?.description,
            product?.price,
            product?.mediaUrls ?? [],
            context,
          ),
        ),
        Gap(24.h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: _buildPaymentOptions(context),
        ),
        Gap(16.h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: _buildPaymentDetails(
            product?.price,
            platformFee,
            totalAmount,
            context,
          ),
        ),
        Gap(50.h),
      ],
    ));
  }

  // Build the warning message
  Widget _buildWarningMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.yellow[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          SvgPicture.asset(AppAssets.warningNoteSvg),
          Gap(8.w),
          Expanded(
            child: Text(
              LocaleKeys.warningMessage.tr(),
              style: GoogleFonts.plusJakartaSans(
                fontSize: 12.sp,
                fontWeight: FontWeight.normal,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build address section
  Widget _buildAddressSection(BuildContext context) {
    return BlocBuilder<AddressBloc, AddressState>(
      builder: (context, state) {
        if (state.addresses?.data?.isNotEmpty ?? false) {
          List<Address> address = [];
          address = state.addresses!.data!
              .where((add) => add.isDefault == true)
              .toList();
          if (address.isEmpty) {
            address.add(state.addresses!.data!.first);
          }

          context
              .read<PaymentBloc>()
              .add(SetAddressDetails(address: address.first));

          return GestureDetector(
            onTap: () {
              (context).push(RouteName.addressMarketplace);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding:
                    const EdgeInsets.all(16), // Khoảng cách bên trong của khung
                decoration: BoxDecoration(
                  color: Colors.white, // Màu nền của khung
                  borderRadius: BorderRadius.circular(10), // Bo góc cho khung
                  border: Border.all(
                    color: Theme.of(context)
                        .lightGrey(context), // Màu viền của khung
                    width: 1, // Độ dày viền
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Theme.of(context).errorBase(context),
                    ),
                    Gap(8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                "${address?.first.street}",
                                style: GoogleFonts.plusJakartaSans(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Gap(8.w),
                              Text(
                                "${address?.first.phone}",
                                style: GoogleFonts.plusJakartaSans(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                          Gap(4.h),
                          Text(
                            "${address?.first.city}, ${address?.first.state}, ${address?.first.street}",
                            style: GoogleFonts.plusJakartaSans(
                              fontSize: 12.sp,
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.arrow_forward_ios,
                        size: 16.sp, color: Colors.black),
                  ],
                ),
              ),
            ),
          );
        } else {
          return GestureDetector(
            onTap: () {
              (context).push(RouteName.addressMarketplace);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding:
                    const EdgeInsets.all(16), // Khoảng cách bên trong của khung
                decoration: BoxDecoration(
                  color: Colors.white, // Màu nền của khung
                  borderRadius: BorderRadius.circular(10), // Bo góc cho khung
                  border: Border.all(
                    color: Theme.of(context)
                        .lightGrey(context), // Màu viền của khung
                    width: 1, // Độ dày viền
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Theme.of(context).errorBase(context),
                    ),
                    Gap(8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Select address",
                            style: GoogleFonts.plusJakartaSans(
                              fontSize: 12.sp,
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.arrow_forward_ios,
                        size: 16.sp, color: Colors.black),
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }

  // Build order details section
  Widget _buildOrderDetails(
      title, desciption, price, List<String> mediaUrls, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.orderDetailsTitle.tr(),
          style: GoogleFonts.plusJakartaSans(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        Gap(8.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            (mediaUrls.isEmpty ?? false)
                ? Container(
                    height: 80.h,
                    width: 80.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(mediaUrls.first),
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                : Container(
                    height: 80.h,
                    width: 80.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.image,
                      size: 25.sp,
                      color: Theme.of(context).greyScale700(context),
                    ),
                  ),
            Gap(12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "$title",
                    style: GoogleFonts.plusJakartaSans(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    "$desciption",
                    style: GoogleFonts.plusJakartaSans(
                      fontSize: 10.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Gap(24.h),
                  Text(
                    "$price\$",
                    style: GoogleFonts.plusJakartaSans(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build payment options section
  Widget _buildPaymentOptions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.paymentOptionsTitle.tr(),
          style: GoogleFonts.plusJakartaSans(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        Gap(16.h),
        _buildTempCardTile(context),
        // _buildPayPalOption(),
        // Gap(8.h),
        // _buildVisaCardOptions(context),
      ],
    );
  }

  // Build Temp Card option for first build option (Usama 22 March)
  Widget _buildTempCardTile(
    BuildContext context,
  ) {
    return Container(
      height: 50.h,
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey, // Màu của khung
          width: 1.0, // Độ dày của khung
        ),
        color: Colors.white,
        borderRadius: BorderRadius.circular(10), // Góc bo tròn
      ),
      child: Card(
        elevation: 0, // Loại bỏ bóng nếu muốn
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10), // Bo góc đồng nhất
        ),

        child: Row(
          children: [
            Gap(8.w),
            SvgPicture.asset(AppAssets.cards),
            Gap(8.w),
            Text(
              LocaleKeys.CreditDebitCard.tr(),
              style: GoogleFonts.plusJakartaSans(fontSize: 14),
            ),
            const Spacer(),
            Radio(
              focusColor: Theme.of(context).errorBase(context),
              fillColor: WidgetStateProperty.all(
                Theme.of(context).errorBase(context),
              ),
              value: "Card",
              groupValue: "Card",
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }

  // // Build PayPal option
  // Widget _buildPayPalOption() {
  //   return Container(
  //     height: 50.h,
  //     decoration: BoxDecoration(
  //       border: Border.all(
  //         color: Colors.grey, // Màu của khung
  //         width: 1.0, // Độ dày của khung
  //       ),
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(10), // Góc bo tròn
  //     ),
  //     child: Card(
  //       elevation: 0, // Loại bỏ bóng nếu muốn
  //       color: Colors.white,
  //       shape: RoundedRectangleBorder(
  //         borderRadius: BorderRadius.circular(10), // Bo góc đồng nhất
  //       ),
  //       child: Row(
  //         children: [
  //           Gap(8.w),
  //           SvgPicture.asset(AppAssets.paypalSvg),
  //           Gap(8.w),
  //           Text(
  //             LocaleKeys.paypal.tr(),
  //             style: GoogleFonts.plusJakartaSans(fontSize: 14),
  //           ),
  //           const Spacer(),
  //           Radio(
  //             focusColor: Theme.of(context).errorBase(context),
  //             fillColor: WidgetStateProperty.all(Theme.of(context).errorBase(context)),
  //             value: "PayPal",
  //             groupValue: "PayPal",
  //             onChanged: (value) {},
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Build Visa card options
  // Widget _buildVisaCardOptions(BuildContext context) {
  //   return Container(
  //     decoration: BoxDecoration(
  //       color: Colors.white,
  //       border: Border.all(
  //         color: Colors.grey, // Màu của viền
  //         width: 1.0, // Độ dày của viền
  //       ),
  //       borderRadius: BorderRadius.circular(12), // Góc bo tròn cho viền
  //     ),
  //     child: Card(
  //       elevation: 0, // Loại bỏ bóng nếu không cần
  //       color: Colors.white,
  //       shape: RoundedRectangleBorder(
  //         borderRadius: BorderRadius.circular(12), // Bo góc đồng nhất
  //       ),
  //       child: Column(
  //         children: [
  //           CustomExpansionTile(
  //             title: Text(
  //               LocaleKeys.visaCard.tr(),
  //               style: GoogleFonts.plusJakartaSans(fontSize: 14),
  //             ),
  //             trailing: const Icon(
  //               Icons.keyboard_arrow_down, // Biểu tượng dropdown
  //               color: Colors.grey, // Màu sắc biểu tượng
  //             ),
  //             children: [
  //               _buildVisaCardTile(
  //                   "HDFC Bank ****1234", "HDFC", AppAssets.visaSvg),
  //               _buildVisaCardTile("HDFC VietinBank ****1234", "VietinBank",
  //                   AppAssets.viettinSvg),
  //             ],
  //           ),
  //           Padding(
  //             padding: const EdgeInsets.symmetric(vertical: 8),
  //             child: _buildAddNewCardTile(context),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Build individual visa card tile
  // Widget _buildVisaCardTile(String cardName, String bankName, String pic) {
  //   return Row(
  //     children: [
  //       Gap(16.h),
  //       Padding(
  //         padding: const EdgeInsets.symmetric(horizontal: 16),
  //         child: SvgPicture.asset(pic),
  //       ),
  //       Expanded(
  //         child: Text(
  //           cardName,
  //           style: GoogleFonts.plusJakartaSans(
  //               fontSize: 14.sp, fontWeight: FontWeight.normal),
  //         ),
  //       ),
  //       Radio(
  //         value: bankName,
  //         groupValue: "Visa",
  //         onChanged: (value) {},
  //       ),
  //     ],
  //   );
  // }

  // Build add new card tile
  // Widget _buildAddNewCardTile(BuildContext context) {
  //   return GestureDetector(
  //     onTap: () {
  //       (context).push(RouteName.addCardPage);
  //     },
  //     child: Row(
  //       children: [
  //         const Padding(
  //           padding: EdgeInsets.symmetric(horizontal: 16),
  //           child: Icon(Icons.add_circle_outline, color: Colors.grey),
  //         ),
  //         Expanded(
  //           child: Text(
  //             LocaleKeys.addNewCard.tr(),
  //             style: GoogleFonts.plusJakartaSans(fontSize: 14.sp),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Build payment details section
  Widget _buildPaymentDetails(
    price,
    service,
    total,
    BuildContext context,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.paymentDetailsTitle.tr(),
          style: GoogleFonts.plusJakartaSans(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        Gap(16.h),
        // _buildDetailRow(LocaleKeys.orderID.tr(), "#123546"),
        _buildDetailRow(LocaleKeys.orderDate.tr(), "24/09/2024"),
        _buildDetailRow(LocaleKeys.productPrice.tr(), "\$$price"),
        // _buildDetailRow(LocaleKeys.voucher.tr(), "-\$2"),
        _buildDetailRow(LocaleKeys.serviceFee.tr(), "\$$service"),
        const Divider(thickness: 1, height: 24),
        _buildTotalCostRow(
          total,
          context,
        ),
        Gap(16.h),
        _buildAgreementText(AppAssets.contactSvg),
      ],
    );
  }

  // Build total cost row
  Widget _buildTotalCostRow(
    total,
    BuildContext context,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.totalCost.tr(),
          style: GoogleFonts.plusJakartaSans(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        Text(
          "\$$total",
          style: GoogleFonts.plusJakartaSans(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).errorBase(context),
          ),
        ),
      ],
    );
  }

  // Build agreement text
  Widget _buildAgreementText(String pic) {
    return Row(
      children: [
        SvgPicture.asset(
          pic,
          // color: Theme.of(context).errorBase(context),
        ),
        //Icon(Icons.abc),
        Gap(8.w),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(color: Colors.black, fontSize: 12.sp),
              children: [
                TextSpan(text: LocaleKeys.termsAndConditions.tr()),
                TextSpan(
                  text: LocaleKeys.termsAndConditionsLink.tr(),
                  style: const TextStyle(color: Colors.blue),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Build individual detail row
  Widget _buildDetailRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: GoogleFonts.plusJakartaSans(
                fontSize: 14.sp, fontWeight: FontWeight.normal),
          ),
          Text(
            value,
            style: GoogleFonts.plusJakartaSans(
                fontSize: 14.sp, fontWeight: FontWeight.normal),
          ),
        ],
      ),
    );
  }
}
