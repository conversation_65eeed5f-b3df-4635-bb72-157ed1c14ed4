part of 'liked_bloc.dart';

sealed class LikedEvent extends Equatable {
  const LikedEvent();

  @override
  List<Object> get props => [];
}

// class LikeProductEvent extends LikedEvent {
//   const LikeProductEvent({required this.product});
//   final Product product;

//   @override
//   List<Object> get props => [product];
// }

class GetLikeProducts extends LikedEvent {
  const GetLikeProducts();
}
