import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/home_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/likedproducts/liked_products_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';

part 'liked_event.dart';
part 'liked_state.dart';

class LikedBloc extends Bloc<LikedEvent, LikedProductState> {
  final ProductRepository _productRepository;
  LikedBloc(this._productRepository) : super(LikedProductState.initial()) {
    // on<LikeProductEvent>(_onLikeProduct);
    on<GetLikeProducts>(_onGetLikeProducts);
  }

  // void _onLikeProduct(LikeProductEvent event, Emitter<LikedProductState> emit) {
  //   final currentList = state.likedProducts?.data?.products ?? [];

  //   ///checking liked or not
  //   bool? isLiked = currentList.any((p) => p.id == event.product.id);
  //   List<Product> updatedList;
  //   if (isLiked) {
  //     updatedList = List<Product>.from(
  //         currentList.where((p) => p.id != event.product.id));
  //   } else {
  //     // Add the product if it was not liked before (toggle on)
  //     updatedList = List<Product>.from(currentList)..add(event.product);
  //   }
  //   emit(state.copyWith(
  //       // likedProducts: ApiResponse.completed(updatedList),
  //       toggledProduct: event.product));
  // }

  Future<void> _onGetLikeProducts(
      GetLikeProducts event, Emitter<LikedProductState> emit) async {
    emit(state.copyWith(likedProducts: const ApiResponse.loading()));
    try {
      final likedProducts = await _productRepository.getLikedProducts();
      emit(state.copyWith(likedProducts: ApiResponse.completed(likedProducts)));
    } on NetworkException catch (e) {
      emit(state.copyWith(likedProducts: ApiResponse.error(e.message)));
    }
  }
}
