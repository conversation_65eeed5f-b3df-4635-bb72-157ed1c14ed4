part of 'liked_bloc.dart';

class LikedProductState extends Equatable {
  final ApiResponse<LikedProductsModel>? likedProducts;
  final Product? toggledProduct;
  const LikedProductState(
      {required this.likedProducts, required this.toggledProduct});

  LikedProductState copyWith(
      {ApiResponse<LikedProductsModel>? likedProducts, Product? toggledProduct}) {
    return LikedProductState(
      likedProducts: likedProducts ?? this.likedProducts,
      toggledProduct: toggledProduct ?? this.toggledProduct,
    );
  }

  factory LikedProductState.initial() {
    return const LikedProductState(likedProducts: null, toggledProduct: null);
  }

  @override
  List<Object?> get props => [likedProducts, toggledProduct];
}
