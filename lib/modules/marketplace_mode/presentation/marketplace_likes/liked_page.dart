import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_likes/bloc/liked_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/card_like_marketplace.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';

class LikedPage extends StatelessWidget {
  const LikedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(CupertinoIcons.chevron_left),
          onPressed: () => context.pop(),
        ),
        title: Text(
          LocaleKeys.liked.tr(),
          style: TextStyle(
            fontSize: 32.sp,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: BlocConsumer<LikedBloc, LikedProductState>(
        listener: (context, state) {
          if (state.likedProducts?.status == Status.loading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
        },
        builder: (context, state) {
          if (state.likedProducts?.status == Status.completed) {
            return ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              itemCount: state.likedProducts?.data?.products.length ??
                  0, // Số lượng sản phẩm
              itemBuilder: (context, index) {
                var product = state.likedProducts?.data?.products[index];
                return CardLikeMarketplace(
                  id: product?.id ?? '',
                  imageProduct: product?.mediaUrls.first ?? '',
                  nameProduct: product?.title ?? '',
                  locationProduct: product?.locations.first.address ?? '',
                  priceProduct: product?.price ?? 1.00,
                  dateProduct: 'N/A',
                  authorImage: product?.sellerPhoto ?? '',
                  authorName: product?.sellerName ?? '',
                );
              },
            );
          } else {
            return const SizedBox.shrink();
          }
        },
      ),
    );
  }
}
