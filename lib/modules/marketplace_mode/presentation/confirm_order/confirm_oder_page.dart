// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:gap/gap.dart';
// import 'package:go_router/go_router.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:multime_app/app/routers/routers_name.dart';
// import 'package:multime_app/core/l10n/locale_keys.g.dart';
// import 'package:multime_app/shared/models/marketplace/product_fake_data.dart';
// import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_check_out/checkout_page.dart';

// // Thêm thư viện intl

// class ConfirmOderPage extends StatelessWidget {
//   const ConfirmOderPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         elevation: 0,
//         backgroundColor: Colors.white,
//         title: Text(
//           LocaleKeys.confirmOrder.tr(),
//           style: TextStyle(
//             color: Colors.black,
//             fontSize: 18.sp,
//             fontWeight: FontWeight.normal,
//             fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//           ),
//         ),
//         leading: IconButton(
//           icon: const Icon(Icons.arrow_back, color: Colors.black),
//           onPressed: () => context.pop(),
//         ),
//       ),
//       body: Column(
//         children: [
//           // Progress Indicator
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 const CircleAvatar(
//                   radius: 6,
//                   backgroundColor: Theme.of(context).errorBase(context),
//                 ),
//                 Gap(8.w),
//                 Container(
//                   height: 2,
//                   width: 60,
//                   color: Colors.grey,
//                 ),
//                 Gap(8.w),
//                 const CircleAvatar(
//                   radius: 6,
//                   backgroundColor: Colors.grey,
//                 ),
//               ],
//             ),
//           ),

//           // Shop Information
//           Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Row(
//               children: [
//                 Text(
//                   fakeDataProduct[0].shopName,
//                   style: TextStyle(
//                     fontWeight: FontWeight.bold,
//                     fontSize: 16.sp,
//                     fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                   ),
//                 ),
//                 const Spacer(),
//                 Text(
//                   NumberFormat.simpleCurrency(decimalDigits: 0)
//                       .format(fakeDataProduct[0].price),
//                   style: TextStyle(
//                     color: Theme.of(context).errorBase(context),
//                     fontWeight: FontWeight.bold,
//                     fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                   ),
//                 ),
//               ],
//             ),
//           ),

//           // Product Details
//           Container(
//             margin: const EdgeInsets.symmetric(horizontal: 16),
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(8),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.grey.withOpacity(0.2),
//                   blurRadius: 8,
//                   offset: const Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.start,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Container(
//                   height: 80.h,
//                   width: 80.w,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(8),
//                     image: DecorationImage(
//                       image: NetworkImage(fakeDataProduct[0].image),
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 ),
//                 Gap(12.w),
//                 Expanded(
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     mainAxisAlignment: MainAxisAlignment.start,
//                     children: [
//                       Text(
//                         "Macbook Air M2",
//                         style: GoogleFonts.plusJakartaSans(
//                           fontSize: 16.sp,
//                           fontWeight: FontWeight.w500,
//                         ),
//                       ),
//                       Text(
//                         "1 - Air M2",
//                         style: GoogleFonts.plusJakartaSans(
//                           fontSize: 10.sp,
//                           color: Colors.grey[600],
//                         ),
//                       ),
//                       Gap(24.h),
//                       Text(
//                         "80\$",
//                         style: GoogleFonts.plusJakartaSans(
//                           fontSize: 14.sp,
//                           fontWeight: FontWeight.bold,
//                           color: Colors.black,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),

//           Gap(14.h),

//           // Order Details
//           Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 const Divider(color: Colors.grey),
//                 Gap(8.h),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       LocaleKeys.orderID.tr(),
//                       style: TextStyle(
//                         fontWeight: FontWeight.normal,
//                         fontSize: 14.sp,
//                         fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                       ),
//                     ),
//                     Text(fakeDataProduct[0].productID,
//                         style: TextStyle(
//                           color: Colors.black,
//                           fontWeight: FontWeight.normal,
//                           fontSize: 14.sp,
//                           fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                         )),
//                   ],
//                 ),
//                 Gap(8.h),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       LocaleKeys.orderDate.tr(),
//                       style: TextStyle(
//                         fontWeight: FontWeight.normal,
//                         fontSize: 14.sp,
//                         fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                       ),
//                     ),
//                     Text(
//                       DateFormat('dd/MM/yyyy').format(DateTime.now()),
//                       style: TextStyle(
//                           color: Colors.black,
//                           fontWeight: FontWeight.normal,
//                           fontSize: 14.sp,
//                           fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
//                     ),
//                   ],
//                 ),
//                 Gap(8.h),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       LocaleKeys.productPrice.tr(),
//                       style: TextStyle(
//                         fontWeight: FontWeight.normal,
//                         fontSize: 14.sp,
//                         fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                       ),
//                     ),
//                     Text(
//                       NumberFormat.simpleCurrency(decimalDigits: 0)
//                           .format(fakeDataProduct[0].price),
//                       style: TextStyle(
//                         color: Colors.black,
//                         fontWeight: FontWeight.normal,
//                         fontSize: 14.sp,
//                         fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                       ),
//                     ),
//                   ],
//                 ),
//                 Gap(8.h),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       LocaleKeys.voucher.tr(),
//                       style: TextStyle(
//                         fontWeight: FontWeight.normal,
//                         fontSize: 14.sp,
//                         fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                       ),
//                     ),
//                     Text(
//                       "-2\$",
//                       style: TextStyle(
//                           color: Colors.black,
//                           fontWeight: FontWeight.normal,
//                           fontSize: 14.sp,
//                           fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
//                     ),
//                   ],
//                 ),
//                 Gap(8.h),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       LocaleKeys.serviceFee.tr(),
//                       style: TextStyle(
//                         fontWeight: FontWeight.normal,
//                         fontSize: 14.sp,
//                         fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
//                       ),
//                     ),
//                     Text(
//                       "9\$",
//                       style: TextStyle(
//                           color: Colors.black,
//                           fontWeight: FontWeight.normal,
//                           fontSize: 14.sp,
//                           fontFamily: GoogleFonts.plusJakartaSans().fontFamily),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),

//           const Spacer(),

//           // Bottom Bar
//           _buildBottomNavBar(context),
//         ],
//       ),
//     );
//   }
// }

// Widget _buildBottomNavBar(BuildContext context) {
//   return Column(
//     mainAxisSize: MainAxisSize.min,
//     children: [
//       // Đường kẻ ngang
//       const Divider(
//         color: Colors.grey, // Màu của đường kẻ
//         thickness: 1, // Độ dày của đường kẻ
//         height: 1, // Chiều cao của Divider
//       ),
//       Center(
//         child: Padding(
//           padding: const EdgeInsets.only(top: 10, bottom: 20, right: 20),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               // Phần hiển thị "Total Cost"
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.end,
//                 children: [
//                   Text(
//                     LocaleKeys.totalCost.tr(),
//                     style: TextStyle(
//                       fontSize: 16.sp,
//                       fontWeight: FontWeight.bold,
//                       color: Colors.black,
//                     ),
//                   ),
//                   Gap(5.h),
//                   Text(
//                     '\$400',
//                     style: TextStyle(
//                       fontSize: 16.sp,
//                       fontWeight: FontWeight.bold,
//                       color: Theme.of(context).errorBase(context),
//                     ),
//                   ),
//                 ],
//               ),
//               Gap(20.w),
//               // Nút Continue
//               ElevatedButton(
//                 style: ElevatedButton.styleFrom(
//                   backgroundColor: Theme.of(context).errorBase(context), // Màu nền đỏ
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(8), // Bo góc
//                   ),
//                   padding: const EdgeInsets.symmetric(
//                       horizontal: 20, vertical: 12), // Padding
//                 ),
//                 onPressed: () {
//                   // Hành động khi nhấn nút

//                   (context).push(RouteName.checkoutScreen);
//                 },
//                 child: Text(
//                   LocaleKeys.continueButton.tr(),
//                   style: TextStyle(
//                     color: Colors.white,
//                     fontSize: 16.sp,
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     ],
//   );
// }
