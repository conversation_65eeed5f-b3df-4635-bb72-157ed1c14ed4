import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/address_repository.dart';

part 'address_event.dart';
part 'address_state.dart';

class AddressBloc extends Bloc<AddressEvent, AddressState> {
  final AddressRepository addressRepository;

  AddressBloc({required this.addressRepository})
      : super(AddressState.initial()) {
    on<GetAddressEvent>(_onGetAddressEvent);
    on<CreateAddressEvent>(_onCreateAddressEvent);
    on<ToggleIsDefaultEvent>(_onToggleIsDefault);
    on<ToggleSetDefaultEvent>(_onToggleSetDefaultEvent);
    on<UpdateAddressEvent>(_onUpdateAddressEvent);
    on<DeleteAddressEvent>(_onDeleteAddressEvent);
  }

  Future<void> _onDeleteAddressEvent(
      DeleteAddressEvent event, Emitter<AddressState> emit) async {
    emit(state.copyWith(deleteAddress: const ApiResponse.loading()));
    try {
      final response = await addressRepository.deleteAddress(event);
      emit(state.copyWith(deleteAddress: ApiResponse.completed(response)));
      add(const GetAddressEvent());
    } on NetworkException catch (e) {
      emit(state.copyWith(addresses: ApiResponse.error(e.message)));
    }
  }

  Future<void> _onUpdateAddressEvent(
      UpdateAddressEvent event, Emitter<AddressState> emit) async {
    emit(state.copyWith(
        isLoading: true,
        isError: false,
        isSuccess: false,
        updateAddress: const ApiResponse.loading()));
    try {
      final address = Address(
        id: event.addressId,
        street: event.street!,
        recipientName: event.recipientName,
        city: event.city!,
        createdAt: DateTime.now(),
        userId: gs.user!.id!,
        phone: event.phone,
        state: 'active',
        postalCode: '0',
        countryId: gs.user!.countryId,
        isDefault: event.isDefault ?? false,
      );
      final response = await addressRepository.updateAddress(
          address.toJson(), event.addressId!);
      emit(state.copyWith(
          isLoading: false,
          isError: false,
          isSuccess: true,
          updateAddress: ApiResponse.completed(response)));
    } on NetworkException catch (e) {
      emit(state.copyWith(addresses: ApiResponse.error(e.message)));
    }
  }

  Future<void> _onGetAddressEvent(
      GetAddressEvent event, Emitter<AddressState> emit) async {
    emit(state.copyWith(
        isLoading: true, addresses: const ApiResponse.loading()));
    try {
      final addresses = await addressRepository.getAddresses();

      emit(state.copyWith(
          isSuccess: true,
          isLoading: false,
          addresses: ApiResponse.completed(addresses.data)));
    } on NetworkException catch (e) {
      emit(state.copyWith(addresses: ApiResponse.error(e.message)));
    }
  }

  Future<void> _onCreateAddressEvent(
      CreateAddressEvent event, Emitter<AddressState> emit) async {
    emit(state.copyWith(isLoading: true, isError: false, isSuccess: false));
    try {
      final address = Address(
        street: event.street!,
        recipientName: event.recipientName,
        city: event.city!,
        createdAt: DateTime.now(),
        userId: gs.user!.id!,
        phone: event.phone,
        state: 'active',
        postalCode: '0',
        countryId: gs.user!.countryId,
        isDefault: event.isDefault,
      );
      final response = await addressRepository.createAddress(address.toJson());
      if (response.code == 40071) {
        emit(state.copyWith(
            errorMessage: response.message, isLoading: false, isError: true));
        return;
      }
      emit(state.copyWith(
          createAddress: response, isLoading: false, isSuccess: true));
      // add(const GetAddressEvent());
    } on NetworkException catch (e) {
      emit(state.copyWith(
        createAddress: ApiResponse.error(e.message),
        isLoading: false,
        // isError: true
      ));
    }
  }

  Future<void> _onToggleIsDefault(
      ToggleIsDefaultEvent event, Emitter<AddressState> emit) async {
    emit(state.copyWith(isDefault: event.value));
    print('default:${event.value}');
  }

  Future<void> _onToggleSetDefaultEvent(
      ToggleSetDefaultEvent event, Emitter<AddressState> emit) async {
    emit(state.copyWith(isLoading: true, isError: false, isSuccess: false));
    try {
      await addressRepository.toggleSetDefault(event);
      // emit(state.copyWith(isLoading: false, isSuccess: true, isError: false));
      add(const GetAddressEvent());
    } catch (e) {
      emit(state.copyWith(
          isError: true,
          errorMessage: 'An error occurred while setting default address'));
    }
  }
}
