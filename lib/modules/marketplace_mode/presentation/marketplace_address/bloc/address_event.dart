part of 'address_bloc.dart';

sealed class AddressEvent extends Equatable {
  const AddressEvent();

  @override
  List<Object?> get props => [];
}

class DeleteAddressEvent extends AddressEvent {
  final String addressId;

  const DeleteAddressEvent({required this.addressId});

  @override
  List<Object?> get props => [addressId];
}

class UpdateAddressEvent extends AddressEvent {
  final int? addressId;
  final String? street;
  final String? recipientName;
  final String? city;
  final String? phone;
  final bool? isDefault;

  const UpdateAddressEvent({
    this.addressId,
    this.street,
    this.recipientName,
    this.city,
    this.phone,
    this.isDefault,
  });

  @override
  List<Object?> get props => [
        addressId,
        street,
        recipientName,
        city,
        phone,
        isDefault,
      ];
}

class GetAddressEvent extends AddressEvent {
  const GetAddressEvent();
}

class CreateAddressEvent extends AddressEvent {
  final String? street;
  final String? recipientName;
  final String? city;
  final String? phone;
  final bool? isDefault;

  const CreateAddressEvent({
    this.street,
    this.recipientName,
    this.city,
    this.phone,
    this.isDefault = false,
  });

  @override
  List<Object?> get props => [
        street,
        recipientName,
        city,
        phone,
      ];
}

class ToggleIsDefaultEvent extends AddressEvent {
  final bool value;
  const ToggleIsDefaultEvent(this.value);
  @override
  List<Object?> get props => [value];
}

class FetchMapImageEvent extends AddressEvent {
  final String address;
  const FetchMapImageEvent(this.address);
}

class ToggleSetDefaultEvent extends AddressEvent {
  final int addressId;
  const ToggleSetDefaultEvent(this.addressId);
  @override
  List<Object?> get props => [addressId];
}
