part of 'address_bloc.dart';

class AddressState extends Equatable {
  final ApiResponse<AddressResponse>? createAddress;
  final ApiResponse<List<Address>>? addresses;
  final ApiResponse<AddressResponse>? updateAddress;
  final ApiResponse? deleteAddress;
  final bool isDefault;
  final bool isLoading;
  final bool isError;
  final String? errorMessage;
  final bool isSuccess;

  const AddressState(
      {this.createAddress,
      this.addresses,
      this.updateAddress,
      this.deleteAddress,
      this.isDefault = false,
      this.isLoading = false,
      this.isError = false,
      this.errorMessage,
      this.isSuccess = false});

  factory AddressState.initial() {
    return const AddressState(
      createAddress: null,
      updateAddress: null,
      addresses: null,
      isDefault: false,
      deleteAddress: null,
      isLoading: false,
      isError: false,
      errorMessage: null,
      isSuccess: false,
    );
  }

  AddressState copyWith({
    ApiResponse<AddressResponse>? createAddress,
    ApiResponse<List<Address>>? addresses,
    ApiResponse<AddressResponse>? updateAddress,
    ApiResponse? deleteAddress,
    bool isDefault = false,
    bool? isLoading,
    bool? isError,
    String? errorMessage,
    bool? isSuccess,
  }) {
    return AddressState(
      createAddress: createAddress ?? this.createAddress,
      addresses: addresses ?? this.addresses,
      updateAddress: updateAddress ?? this.updateAddress,
      deleteAddress: deleteAddress ?? this.deleteAddress,
      isDefault: isDefault,
      isLoading: isLoading ?? this.isLoading,
      isError: isError ?? this.isError,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }

  @override
  List<Object?> get props => [
        createAddress,
        addresses,
        isDefault,
        deleteAddress,
        updateAddress,
        isLoading,
        isError,
        errorMessage,
        isSuccess
      ];
}
