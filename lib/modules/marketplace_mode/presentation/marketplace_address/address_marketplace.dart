import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';
import 'package:multime_app/shared/models/marketplace/product_fake_data.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/widgets/products_widget/personal_info_card.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';

class AddressMarketplace extends StatefulWidget {
  const AddressMarketplace({super.key});

  @override
  State<AddressMarketplace> createState() => _AddressMarketplaceState();
}

class _AddressMarketplaceState extends State<AddressMarketplace> {
  final TextEditingController displayNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final user = fakeDataUser[0];
    displayNameController.text = user.userName;
    emailController.text = user.userEmail;
    phoneNumberController.text = user.userPhone;
  }

  @override
  void dispose() {
    displayNameController.dispose();
    emailController.dispose();
    phoneNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        title: Text(
          "Address",
          style: Theme.of(context).textTheme.lightBodyXLargeBold,
        ),
        centerTitle: true,
      ),
      body: BlocConsumer<AddressBloc, AddressState>(
        listener: (context, state) {
          if (state.isLoading) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
        },
        builder: (context, state) {
          final addressList = state.addresses?.data ?? [];
          if (state.isSuccess && addressList.isNotEmpty) {
            return ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: addressList.length + 1,
              itemBuilder: (context, index) {
                if (index < addressList.length) {
                  final address = addressList[index];
                  return GestureDetector(
                    onTap: () {
                      context.read<AddressBloc>().add(ToggleSetDefaultEvent(
                            address.id!,
                          ));
                    },
                    child: AddressCard(
                      onDeleteAddress: () {
                        context.read<AddressBloc>().add(DeleteAddressEvent(
                              addressId: address.id?.toString() ?? '',
                            ));
                      },
                      onEditAddress: () {
                        context.push(RouteName.personalInformationPage, extra: {
                          'isCheck': true,
                          'address': address,
                        }).then((value) {
                          context
                              .read<AddressBloc>()
                              .add(const GetAddressEvent());
                        });
                      },
                      name: address.recipientName ?? '',
                      phone: address.phone ?? '',
                      address: address.street ?? '',
                      isDefault: address.isDefault ?? false,
                      city: address.city ?? '',
                      country: address.countryId ?? 0,
                      state: address.state ?? '',
                    ),
                  );
                } else {
                  return Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: OutlinedButton.icon(
                      onPressed: () {
                        context
                            .push(RouteName.personalInformationPage)
                            .then((value) {
                          context
                              .read<AddressBloc>()
                              .add(const GetAddressEvent());
                        });
                      },
                      icon: Icon(
                        Icons.add,
                        color: Theme.of(context).primary(context),
                      ),
                      label: Text(
                        LocaleKeys.addNewAddress.tr(),
                        style: TextStyle(
                          color: Theme.of(context).primary(context),
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: Theme.of(context).primary(context),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  );
                }
              },
            );
          } else if (state.addresses?.status == Status.error) {
            return Center(
              child: Text(state.addresses?.message ?? ''),
            );
          } else if (state.addresses?.status == Status.completed &&
              (state.addresses?.data == null ||
                  state.addresses!.data!.isEmpty)) {
            return Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.padding32,
                  vertical: AppSpacing.padding16h),
              child: Align(
                alignment: Alignment.center,
                child: Column(
                  children: [
                    SvgPicture.asset(
                      AppAssets.lionStrongbodyAi,
                      width: MediaQuery.of(context).size.width * 0.12,
                      height: MediaQuery.of(context).size.height * 0.3,
                    ),
                    Gap(16.h),
                    Text('You don\'t have an address yet',
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold),
                    Gap(16.h),
                    Text(
                        'Please fill in your shipping information here, making it more convenient for you when placing an order.',
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular),
                    Gap(16.h),
                    ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primary(context),
                          padding: EdgeInsets.symmetric(
                              horizontal: AppSpacing.padding16,
                              vertical: AppSpacing.padding12h),
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius10),
                          ),
                        ),
                        onPressed: () {
                          context.push(RouteName.personalInformationPage).then(
                            (value) {
                              context
                                  .read<AddressBloc>()
                                  .add(const GetAddressEvent());
                            },
                          );
                        },
                        child: Text(
                          'Add my Address',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumMedium
                              .copyWith(
                                color: Theme.of(context).whitePrimary(context),
                              ),
                        ))
                  ],
                ),
              ),
            );
          } else {
            return const Center(
              child: SizedBox.shrink(),
            );
          }
        },
      ),
    );
  }
}
