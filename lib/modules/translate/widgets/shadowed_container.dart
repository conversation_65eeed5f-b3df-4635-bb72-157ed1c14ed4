import 'package:flutter/material.dart';

Widget shadowedContainer({
  required Widget child,
  EdgeInsetsGeometry? margin,
  EdgeInsetsGeometry? padding,
  double? width = double.infinity,
  double? height,
}) {
  return Container(
    width: double.infinity,
    height: height,
    margin: margin,
    padding: padding,
    decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.06),
            blurRadius: 44,
            offset: Offset(0, 6),
          ),
        ]),
    child: child,
  );
}
