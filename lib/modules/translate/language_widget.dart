import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../core/l10n/locale_keys.g.dart';
import 'bloc/language_bloc.dart';
import 'bloc/language_event.dart';
import 'bloc/language_state.dart';

class LanguageSettingWidget extends StatelessWidget {
  final LanguageBloc languageBloc;

  const LanguageSettingWidget({
    super.key,
    required this.languageBloc,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageBloc, LanguageState>(
      bloc: languageBloc,
      builder: (context, state) {
        final selectedLanguage = (state is LanguageChanged)
            ? state.locale
            : (state is LanguageInitial)
                ? state.locale
                : languageBloc.getSelectedLanguage();

        return GestureDetector(
          child: SizedBox(
            width: double.infinity,
            child:
                Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Gap(5),
                SizedBox(
                  width: double.infinity,
                  height: 30,
                  child: Text(
                    "Language / ${LocaleKeys.language.tr()}",
                    style: Theme.of(context).textTheme.lightBodyLargeRegular,
                  ),
                ),
                Text(
                  LocaleKeys.language.tr(),
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeRegular
                      .copyWith(
                        color: Theme.of(context).textSecondary(context)
                      ),
                ),
              ],
            ),
          ),
          onTap: () {
            showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  backgroundColor: Theme.of(context).whitePrimary(context),
                  content: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: languageBloc.getLanguageOptions().map((option) {
                        return ListTile(
                          leading: ClipRRect(
                            borderRadius: BorderRadius.circular(99),
                            child: SvgPicture.asset(
                              option.flagIcon,
                              width: 25,
                              height: 25,
                              fit: BoxFit.cover,
                            ),
                          ),
                          title: Text(
                            option.displayName,
                            style: Theme.of(context).textTheme.lightBodyLargeRegular
                          ),
                          onTap: () {
                            languageBloc.add(
                                ChangeLanguageEvent(option.locale, context));
                            context.pop();
                          },
                        );
                      }).toList(),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}
