import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';

import '../model/language_option.dart';
import 'language_event.dart';
import 'language_state.dart';

class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  final GlobalStorage _globalStorage;
  bool _isLanguageOptionExpanded = false;
   List<LanguageOption> filteredLanguages = [];

  final BuildContext context;
  LanguageBloc(this._globalStorage, this.context)
      : super(LanguageInitial(_globalStorage.locale)) {
    on<ChangeLanguageEvent>(_onChangeLanguage);
    on<ToggleLanguageOptionExpansion>(_onToggleLanguageOptionExpansion);
    on<SearchLanguageEvent>(_onSearchLanguage);
  }

  void _onChangeLanguage(
      ChangeLanguageEvent event, Emitter<LanguageState> emit) {
    _globalStorage.locale = event.locale;
    context.setLocale(event.locale);
    // EasyLocalization.of(event.context)?.setLocale(event.locale);
    emit(LanguageChanged(event.locale));
  }

  List<LanguageOption> getLanguageOptions() {
    return [
      LanguageOption(
        locale: const Locale('en'),
        displayName: 'English',
        flagIcon: "assets/svg/language_svg/uk.svg",
      ),
      LanguageOption(
        locale: const Locale('ar'),
        displayName: 'العربية',
        flagIcon: 'assets/svg/language_svg/ar.svg',
      ),
      LanguageOption(
        locale: const Locale('hi'),
        displayName: 'हिन्दी',
        flagIcon: 'assets/svg/language_svg/india.svg',
      ),
      LanguageOption(
        locale: const Locale('zh'),
        displayName: '中文',
        flagIcon: 'assets/svg/language_svg/china.svg',
      ),
      LanguageOption(
        locale: const Locale('es'),
        displayName: 'Español',
        flagIcon: 'assets/svg/language_svg/es.svg',
      ),
       LanguageOption(
        locale: const Locale('vi'),
        displayName: 'Tiếng Việt',
        flagIcon: 'assets/svg/language_svg/vn.svg',
      ),
    ];
  }

  Locale getSelectedLanguage() {
    return _globalStorage.locale;
  }

  void _onToggleLanguageOptionExpansion(
      ToggleLanguageOptionExpansion event, Emitter<LanguageState> emit) {
    _isLanguageOptionExpanded = !_isLanguageOptionExpanded;
    emit(LanguageToggleState(!_isLanguageOptionExpanded));
  }
  void _onSearchLanguage(SearchLanguageEvent event, Emitter<LanguageState> emit) {
   try {

     if(event.query.isEmpty) {
       filteredLanguages = getLanguageOptions();
     }else {
       filteredLanguages = getLanguageOptions()
           .where((option) => option.displayName.toLowerCase().contains(event.query.toLowerCase()))
           .toList();

     }
      emit(LanguageSearchState(event.query, filteredLanguages));
    } catch (e) {
      debugPrint('Error: $e');
   }
  }
}
