import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';

import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm.dart';

class GetStartedPage extends StatefulWidget {
  const GetStartedPage({super.key});

  @override
  State<GetStartedPage> createState() => _GetStartedPageState();
}

class _GetStartedPageState extends State<GetStartedPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Gap(60.h),
            // Logo Section
            ClipRRect(
              borderRadius: BorderRadius.circular(AppRadius.radius20),
              child: Image.asset(
                AppAssets.logoMultime,
                width: 160.w,
              ),
            ),
            Gap(20.h),
            Text(
              "One App for All Your Connections",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).secondary(context),
                fontSize: 18.sp,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              "Choose your own avatar as regular user, buyer, seller, date, business partner, observer, and friend.",
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            Gap(30.h),
            // Features Section
            _buildKeyFeature("Key Features:"),
            ...[
              "Social Networking",
              "Reconnect (Friends & Clients)",
              "Business Matching",
              "Secure Marketplace",
              "Dating",
              "Private Posts & Comments",
            ].map((feature) => _buildFeature(feature)),
            Gap(40.h),
            // Footer Section
            Text(
              "Powered by StrongBody Technology",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).secondary(context),
                fontSize: 16.sp,
              ),
            ),
            Gap(20.h),
            ButtonSB(
              onTap: () => context.push(RouteName.selectionPage),
              text: 'Continue',
            ),
            Gap(20.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Already a member?",
                  style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    color: Theme.of(context).secondary(context),
                    fontSize: 16.sp,
                  ),
                ),
                Gap(10.w),
                GestureDetector(
                  onTap: () => context.push(RouteName.loginPage),
                  child: Text(
                    "Sign In",
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumBold
                        .copyWith(
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ),
              ],
            ),
            Gap(30.h),
          ],
        ),
      ),
    );
  }

  Widget _buildFeature(String feature) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: AppSpacing.padding6, horizontal: AppSpacing.padding16),
      child: Row(
        children: [
          SvgPicture.asset(
            AppAssets.checkedIcon,
            width: 20.w,
          ),
          Gap(10.w),
          Expanded(
            child: Text(
              feature,
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                color: Theme.of(context).secondary(context),
                fontSize: 16.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyFeature(String key) {
    return Padding(
      padding: EdgeInsets.symmetric(
          vertical: AppSpacing.padding6, horizontal: AppSpacing.padding32h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              key,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).secondary(context),
                fontSize: 16.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
