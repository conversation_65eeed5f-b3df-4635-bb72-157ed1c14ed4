import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import '../../core/constants/privacy_policy.dart';

class PrivacyPolicyPage extends StatefulWidget {
  const PrivacyPolicyPage({super.key});

  @override
  _PrivacyPolicyPageState createState() => _PrivacyPolicyPageState();
}

class _PrivacyPolicyPageState extends State<PrivacyPolicyPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  int _currentIndex = 0;

  final List<String> titles = [
    "Terms of Service",
    "Privacy Policy",
    "Payment Terms",
  ];

  final List<String> contents = [
    PrivacyPolicy.privacy01,
    PrivacyPolicy.privacy02,
    PrivacyPolicy.privacy03,
  ];
  bool isCheckBox = false;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: titles.length, vsync: this);
    _pageController = PageController();
  }

  void _nextPage() {
    if (_currentIndex < titles.length - 1) {
      setState(() {
        _currentIndex++;
      });
      _pageController.animateToPage(_currentIndex,
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
      _tabController.animateTo(_currentIndex);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Row(
          children: [
            Gap(12.w),
            GestureDetector(
              onTap: () => context.pop(),
              child: Text(
                "Back",
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
              ),
            ),
          ],
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: titles.map((title) => Tab(text: title)).toList(),
          onTap: (index) {
            _pageController.jumpToPage(index);
            setState(() {
              _currentIndex = index;
            });
          },
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: titles.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                  _tabController.animateTo(index);
                });
              },
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap(10.h),
                        Text(
                          "Last update: October 2024",
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeSemiBold,
                        ),
                        Gap(10.h),
                        Text(
                          contents[index],
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumRegular,
                        ),
                        Gap(100.h),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomSheet: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(95, 107, 107, 107),
              blurRadius: 10,
            )
          ],
          borderRadius: BorderRadius.circular(16),
        ),
        height: 160.h,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  Checkbox(
                    activeColor: Colors.red,
                    checkColor: Colors.white,
                    value: isCheckBox || _currentIndex == titles.length - 1,
                    onChanged: (value) {
                      setState(() {
                        isCheckBox = value!;
                      });
                    },
                  ),
                  const Text("I agree"),
                  const Spacer(),
                  Text("${_currentIndex + 1}/${titles.length}"),
                ],
              ),
              Gap(10.h),
              GestureDetector(
                onTap: _currentIndex == titles.length - 1
                    ? () => context.pop()
                    : _nextPage,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: _currentIndex == titles.length - 1
                        ? Colors.red
                        : Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.red, width: 2),
                  ),
                  child: Center(
                    child: Text(
                      _currentIndex == titles.length - 1 ? "Let's go!" : "Next",
                      style: TextStyle(
                        fontSize: 16,
                        color: _currentIndex == titles.length - 1
                            ? Colors.white
                            : Colors.red,
                      ),
                    ),
                  ),
                ),
              ),
              Gap(10.h),
            ],
          ),
        ),
      ),
    );
  }
}
