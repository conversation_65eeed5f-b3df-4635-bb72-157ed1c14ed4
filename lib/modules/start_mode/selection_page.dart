import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../app/routers/routers_name.dart';
import '../../core/constants/app_assets.dart';
import '../../core/l10n/locale_keys.g.dart';
import '../../shared/widgets/sames/button_mtm.dart';
import '../setting_mode/presentation/widgets/card_setting.dart';

class SelectionPage extends StatefulWidget {
  @override
  _SelectionPageState createState() => _SelectionPageState();
}

class _SelectionPageState extends State<SelectionPage> {
  String selectedMode = "StrongBody.ai";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              "What brings you here?",
              style: Theme.of(context).textTheme.lightBodyXLargeSemiBold,
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "If you're coming to MultiMe from StrongBody Network, this is the section for you, you can learn more from the modes on our app.",
              style: Theme.of(context).textTheme.lightBodyMediumRegular,
            ),
            Gap(15.h),
            CardMode(
              callback: () {},
              title: LocaleKeys.socialMode.tr(),
              subtitle: LocaleKeys.manageYourConnection.tr(),
              image: AppAssets.social_icon,
            ),
            Gap(7.h),
            CardMode(
              callback: () {},
              title: LocaleKeys.businessMode.tr(),
              subtitle: LocaleKeys.connectToPartnerChances.tr(),
              image: AppAssets.business_icon,
            ),
            Gap(7.h),
            CardMode(
              callback: () {},
              title: LocaleKeys.newsMode.tr(),
              subtitle: LocaleKeys.readNews.tr(),
              image: AppAssets.new_icon,
            ),
            Gap(7.h),
            CardMode(
              callback: () {},
              title: LocaleKeys.marketplace.tr(),
              subtitle: LocaleKeys.shoppingProductsAndService.tr(),
              image: AppAssets.market_icon,
            ),
            Gap(7.h),
            CardMode(
              callback: () {},
              title: LocaleKeys.datingMode.tr(),
              subtitle: LocaleKeys.findRealPartnerForYou.tr(),
              image: AppAssets.dating_icon,
            ),
            Gap(7.h),
            CardMode(
              callback: () {},
              title: LocaleKeys.strongBodyAi.tr(),
              subtitle: LocaleKeys.findRealPartnerForYou.tr(),
              image: AppAssets.strongbody_icon,
            ),
            Gap(50.h),
            ButtonSB(
              onTap: () => context.go(RouteName.loginPage),
              text: 'Continue',
            ),
            Gap(20.h),
          ],
        ),
      ),
    );
  }
}
