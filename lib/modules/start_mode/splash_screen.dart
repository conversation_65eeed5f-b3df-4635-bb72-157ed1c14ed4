import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/country/bloc/country_bloc.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/modules/locale_laguage.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  double _size = 20.0;
  bool _showSvg = false;

  final globalStorage = getIt<GlobalStorage>();
  late List<Country> countries = [];

  @override
  void initState() {
    super.initState();
    countries = globalStorage.countries ?? [];
    if (countries.isEmpty) {
      context.read<CountryBloc>().add(const FetchCountriesEvent());
    }
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final screenWidth = MediaQuery.of(context).size.width;

      Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          _size = screenWidth * 3;
        });

        Future.delayed(const Duration(milliseconds: 1200), () {
          if (mounted) {
            setState(() {
              _showSvg = true;
            });
          }
        });
      });
    });

    //Navigator
    Timer(
      const Duration(seconds: 3),
      () {
        final storage = getIt<GlobalStorage>();
        print("user token db${storage.accessToken}");
        if (storage.accessToken != null) {
          context.go(RouteName.homeStrongBody);
        } else if (storage.isLoggedIn == false) {
          context.go(RouteName.loginPage);
        } else {
          context.go(RouteName.loginPage);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          if (gs.locale.languageCode.isEmpty) LocalePrinterWidget(),
          Positioned(
            width: _size,
            height: _size,
            child: Center(
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 1300),
                curve: Curves.easeInOut,
                width: _size,
                height: _size,
                decoration: BoxDecoration(
                  color: Theme.of(context).backgroundRed(context),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
          if (_showSvg)
            Center(
              child: SvgPicture.asset(
                AppAssets.onStartScreen,
                width: 300.w,
              ),
            ),
        ],
      ),
    );
  }
}
