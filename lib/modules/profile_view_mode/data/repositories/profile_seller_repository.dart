import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/seller/models/seller_profile.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';

import '../../../../core/base/api_response/api_response.dart';
import '../../../../core/model/blog_model_seller/blog_model_seller.dart';
import '../../../strongbody.ai_mode/presentation/detail_product/model/product_model.dart';

abstract class ProfileSellerRepoRemote {
  Future<ApiResponse<List<ServiceModel>>> getServices(int profileId, int page);
  Future<ApiResponse<SellerProfileModel>> getProfile(int profileId);
  Future<ApiResponse<List<ProductModel>>> getProductSeller(
      int profileId, int page);

  Future<ApiResponse<List<BlogModelSeller>>> getBlogsSeller(
      int sellerId, int page, int limit);

  Future<ApiResponse<ProductModel>> getProductSellerById(
      int productId, CancelToken cancelToken);
}

class ProfileSellerRepoRemoteImp implements ProfileSellerRepoRemote {
  final ApiClient _apiClient;

  ProfileSellerRepoRemoteImp(this._apiClient);

  @override
  Future<ApiResponse<List<ServiceModel>>> getServices(
      int profileId, int page) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.getServices,
        method: ApiType.get,
        headers: {
          // 'Scope': 'multi-me', // Example scope, adjust as needed
          'x-api-key': 'your_api_key',
        },
        queryParameters: {
          'page': page,
          'limit': 10,
          'order_by': 'created_at',
          'order_dir': 'DESC',
          'user_id': profileId,
        },
      );

      print('API Response for getServices: $response');

      // Kiểm tra response structure
      if (response['code'] != 0) {
        return ApiResponse.error(response['message'] ?? 'API Error');
      }

      final data = response['data'];

      // Kiểm tra xem data có phải là list hay object
      List<dynamic> listJson;
      if (data is List) {
        listJson = data;
      } else if (data != null && data['list'] != null) {
        listJson = data['list'];
      } else if (data != null && data['data'] != null) {
        listJson = data['data'];
      } else {
        return ApiResponse.completed([]); // Trả về list rỗng nếu không có data
      }

      final services = List<ServiceModel>.from(
        listJson.map((item) => ServiceModel.fromJson(item)),
      );

      return ApiResponse.completed(services);
    } catch (e) {
      print('Error in getServices: $e');
      return ApiResponse.error('Failed to load services: $e');
    }
  }

  @override
  Future<ApiResponse<SellerProfileModel>> getProfile(int profileId) async {
    try {
      final response = await _apiClient.request(
          method: ApiType.get,
          path: "${ApiConst.getProfile}$profileId",
          headers: {
            'Scope': 'multi-me',
            'x-api-key': 'your_api_key',
          });

      final data = response['data'];

      final model = SellerProfileModel.fromJson(data);
      print('API Response for getProfile: $model');

      return ApiResponse.completed(model);
    } catch (e) {
      debugPrint('Error in getProfile: $e');
      return ApiResponse.error('Failed to load profile: $e');
    }
  }

  @override
  Future<ApiResponse<List<ProductModel>>> getProductSeller(
      int profileId, int page) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.getProductSeller,
        method: ApiType.get,
        queryParameters: {
          'user_id': profileId,
          'page': page,
          'limit': 10,
          'order_by': 'created_at',
          'order_dir': 'DESC',
        },
      );

      final listJson = response['data']['data'];

      final products = List<ProductModel>.from(
        listJson.map((item) => ProductModel.fromJson(item)),
      );

      return ApiResponse.completed(products);
    } catch (e) {
      return ApiResponse.error('Failed to load product: $e');
    }
  }

  @override
  Future<ApiResponse<List<BlogModelSeller>>> getBlogsSeller(
      int sellerId, int page, int limit) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.getBlogSeller,
        method: ApiType.get,
        queryParameters: {
          'author_id': sellerId,
          'page': page,
          'limit': limit,
          'order_by': 'created_at',
          'order_dir': 'desc',
        },
      );
      final listJson = response['data']['list'];
      final blogs = List<BlogModelSeller>.from(
        listJson.map((item) => BlogModelSeller.fromJson(item)),
      );
      return ApiResponse.completed(blogs);
    } catch (e) {
      return ApiResponse.error('Failed to load blogs: $e');
    }
  }

  @override
  Future<ApiResponse<ProductModel>> getProductSellerById(
      int productId, CancelToken cancelToken) async {
    try {
      final response = await _apiClient.request(
        path: "${ApiConst.getProductSeller}/$productId",
        method: ApiType.get,
        cancelToken: cancelToken,
      );

      final listJson = response['data'];
      final product = ProductModel.fromJson(listJson);
      debugPrint('API Response for getProductSellerById: $product');
      return ApiResponse.completed(product);
    } catch (e) {
      debugPrint('Error in getProductSellerById: $e');
      return ApiResponse.error('Failed to load product: $e');
    }
  }
}
