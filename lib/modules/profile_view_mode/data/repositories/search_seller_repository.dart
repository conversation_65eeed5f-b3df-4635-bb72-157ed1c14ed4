import '../../../../core/base/api_response/api_response.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/network/api.dart';
import '../../../../core/network/api_type.dart';
import '../../../strongbody.ai_mode/data/models/services/service_model.dart';
import '../../../strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import '../models/product_seller_model.dart';

abstract class SearchSellerRepoRemote {
  Future<ApiResponse<List<ServiceModel>>> getServices(
      int sellerId, int page, String keyword);
  Future<ApiResponse<List<ProductModel>>> getProducts(
      int sellerId, int page, String keyword);
}

class SearchSellerRepoRemoteImp implements SearchSellerRepoRemote {
  final ApiClient _apiClient;

  SearchSellerRepoRemoteImp(this._apiClient);

  @override
  Future<ApiResponse<List<ProductModel>>> getProducts(
      int sellerId, int page, String keyword) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.getProductSeller,
        method: ApiType.get,
        queryParameters: {
          // 'seller_id': sellerId,
          'page': page,
          'limit': 10,
          'order_by': 'created_at',
          'order_dir': 'DESC',
          'keyword': keyword,
          'user_id': sellerId,
        },
      );

      final listJson = response['data']['list'];

      final products = List<ProductModel>.from(
        listJson.map((item) => ProductModel.fromJson(item)),
      );

      return ApiResponse.completed(products);
    } catch (e) {
      return ApiResponse.error('Failed to load services: $e'); //  xử lý lỗi
    }
  }

  @override
  Future<ApiResponse<List<ServiceModel>>> getServices(
      int sellerId, int page, String keyword) async {
    try {
      final response = await _apiClient.request(
        path: ApiConst.getServices,
        method: ApiType.get,
        headers: {
          // 'Scope': 'multi-me', // Example scope, adjust as needed
          'x-api-key': 'your_api_key',
        },
        queryParameters: {
          // 'seller_id': sellerId,
          'page': page,
          'limit': 10,
          'order_by': 'created_at',
          'order_dir': 'DESC',
          'keyword': keyword,
          'user_id': sellerId,
        },
      );

      final listJson = response['data']['list']; // Trích đúng phần list
      final services = List<ServiceModel>.from(
        listJson.map((item) => ServiceModel.fromJson(item)),
      );

      return ApiResponse.completed(services); //  wrap lại trong ApiResponse
    } catch (e) {
      return ApiResponse.error('Failed to load services: $e'); //  xử lý lỗi
    }
  }
}
