// import '../../../../shared/models/user/user_model.dart';

// class ProductSellerModel {
//   final int id;
//   final String name;
//   final String description;
//   final String sku;
//   final String slug;
//   final int categoryId;
//   final CategorySellerModel? category;
//   final String categoryName;
//   final int userId;
//   final UserModel? user;
//   final String email;
//   final String brandtext;
//   final int brandId;
//   final BrandModel brand;
//   final int stockQuantity;
//   final bool isActive;
//   final DateTime expiryDate;
//   final DateTime manufactureDate;
//   final List<String> image;
//   final String countryOfOrigin;
//   final String licenseNo;
//   final String licenseFile;
//   final List<PricingTierModel> pricingTiers;
//   final int views;
//   final double rating;
//   final DateTime createdAt;

//   ProductSellerModel({
//     required this.id,
//     required this.name,
//     required this.description,
//     required this.slug,
//     required this.sku,
//     required this.categoryId,
//     required this.userId,
//     required this.brandtext,
//     required this.brandId,
//     required this.brand,
//     required this.stockQuantity,
//     required this.isActive,
//     required this.expiryDate,
//     required this.manufactureDate,
//     required this.image,
//     required this.countryOfOrigin,
//     required this.licenseNo,
//     required this.licenseFile,
//     required this.pricingTiers,
//     required this.views,
//     required this.rating,
//     required this.createdAt,
//     this.category,
//     this.user,
//     required this.categoryName,
//     required this.email,
//   });

//   factory ProductSellerModel.fromJson(Map<String, dynamic> json) {
//     return ProductSellerModel(
//       id: json['id'] ?? 0,
//       name: json['name'] ?? '',
//       description: json['description'] ?? '',
//       slug: json['slug'] ?? '',
//       sku: json['sku'] ?? '',
//       categoryId: json['category_id'] ?? 0,
//       userId: json['user_id'] ?? 0,
//       categoryName: json['category_name'] ?? '',
//       email: json['email'] ?? '',
//       user: UserModel.fromJson(json['user'] ?? {}),
//       category: CategorySellerModel.fromJson(json['category'] ?? {}),
//       brandtext: json['brandtext'] ?? '',
//       brandId: json['brand_id'] ?? 0,
//       brand: BrandModel.fromJson(json['brand'] ?? {}),
//       stockQuantity: json['stock_quantity'] ?? 0,
//       isActive: json['is_active'] ?? false,
//       expiryDate:
//           DateTime.tryParse(json['expiry_date'] ?? '') ?? DateTime.now(),
//       manufactureDate:
//           DateTime.tryParse(json['manufacture_date'] ?? '') ?? DateTime.now(),
//       image: List<String>.from(json['image'] ?? []),
//       countryOfOrigin: json['country_of_origin'] ?? '',
//       licenseNo: json['license_no'] ?? '',
//       licenseFile: json['license_file'] ?? '',
//       pricingTiers: (json['pricing_tiers'] as List<dynamic>?)
//               ?.map((item) => PricingTierModel.fromJson(item))
//               .toList() ??
//           [],
//       views: json['views'] ?? 0,
//       rating: (json['rating'] ?? 0.0).toDouble(),
//       createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'description': description,
//       'slug': slug,
//       'sku': sku,
//       'category_id': categoryId,
//       'user_id': userId,
//       'brandtext': brandtext,
//       'brand_id': brandId,
//       'brand': brand.toJson(),
//       'stock_quantity': stockQuantity,
//       'is_active': isActive,
//       'expiry_date': expiryDate.toIso8601String(),
//       'manufacture_date': manufactureDate.toIso8601String(),
//       'image': image,
//       'country_of_origin': countryOfOrigin,
//       'license_no': licenseNo,
//       'license_file': licenseFile,
//       'pricing_tiers': pricingTiers.map((tier) => tier.toJson()).toList(),
//       'views': views,
//       'rating': rating,
//       'created_at': createdAt.toIso8601String(),
//       'category': category?.toJson(),
//       'user': user?.toJson(),
//       'email': email,
//       'category_name': categoryName,
//     };
//   }

//   String get title => name;
//   double get price => pricingTiers.isNotEmpty ? pricingTiers.first.price : 0.0;
//   String get imageUrl => image.isNotEmpty ? image.first : '';
// }

// class BrandModel {
//   final int id;
//   final String name;
//   final String description;
//   final String logo;
//   final String website;
//   final String email;
//   final String phone;
//   final String countryOfOrigin;
//   final int foundedYear;
//   final bool isActive;
//   final bool isVerified;
//   final String licenseNumber;
//   final String licenseFile;

//   BrandModel({
//     required this.id,
//     required this.name,
//     required this.description,
//     required this.logo,
//     required this.website,
//     required this.email,
//     required this.phone,
//     required this.countryOfOrigin,
//     required this.foundedYear,
//     required this.isActive,
//     required this.isVerified,
//     required this.licenseNumber,
//     required this.licenseFile,
//   });

//   factory BrandModel.fromJson(Map<String, dynamic> json) {
//     return BrandModel(
//       id: json['id'] ?? 0,
//       name: json['name'] ?? '',
//       description: json['description'] ?? '',
//       logo: json['logo'] ?? '',
//       website: json['website'] ?? '',
//       email: json['email'] ?? '',
//       phone: json['phone'] ?? '',
//       countryOfOrigin: json['country_of_origin'] ?? '',
//       foundedYear: json['founded_year'] ?? 0,
//       isActive: json['is_active'] ?? false,
//       isVerified: json['is_verified'] ?? false,
//       licenseNumber: json['license_number'] ?? '',
//       licenseFile: json['license_file'] ?? '',
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'description': description,
//       'logo': logo,
//       'website': website,
//       'email': email,
//       'phone': phone,
//       'country_of_origin': countryOfOrigin,
//       'founded_year': foundedYear,
//       'is_active': isActive,
//       'is_verified': isVerified,
//       'license_number': licenseNumber,
//       'license_file': licenseFile,
//     };
//   }
// }

// class PricingTierModel {
//   final int id;
//   final int productId;
//   final double price;
//   final String unit;
//   final String description;
//   final int stockQuantity;
//   final DateTime createdAt;
//   final DateTime updatedAt;

//   PricingTierModel({
//     required this.id,
//     required this.productId,
//     required this.price,
//     required this.unit,
//     required this.description,
//     required this.createdAt,
//     required this.updatedAt,
//     required this.stockQuantity,
//   });

//   factory PricingTierModel.fromJson(Map<String, dynamic> json) {
//     return PricingTierModel(
//       id: json['id'] ?? 0,
//       productId: json['product_id'] ?? 0,
//       price: (json['price'] ?? 0.0).toDouble(),
//       unit: json['unit'] ?? '',
//       description: json['description'] ?? '',
//       createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
//       updatedAt: DateTime.tryParse(json['updated_at'] ?? '') ?? DateTime.now(),
//       stockQuantity: json['stock_quantity'] ?? 0,
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'product_id': productId,
//       'price': price,
//       'unit': unit,
//       'description': description,
//       'created_at': createdAt.toIso8601String(),
//       'updated_at': updatedAt.toIso8601String(),
//       'stock_quantity': stockQuantity,
//     };
//   }
// }

// class CategorySellerModel {
//   final int id;
//   final String name;
//   final String description;
//   final String slug;
//   final bool isActive;
//   final String type;
//   final String coverImage;
//   final String icon;
//   final String path;
//   final int depth;

//   CategorySellerModel({
//     required this.id,
//     required this.name,
//     required this.description,
//     required this.isActive,
//     required this.slug,
//     required this.coverImage,
//     required this.icon,
//     required this.path,
//     required this.depth,
//     required this.type,
//   });

//   factory CategorySellerModel.fromJson(Map<String, dynamic> json) {
//     return CategorySellerModel(
//       id: json['id'] ?? 0,
//       name: json['name'] ?? '',
//       description: json['description'] ?? '',
//       slug: json['slug'] ?? '',
//       coverImage: json['cover_image'] ?? '',
//       icon: json['icon'] ?? '',
//       path: json['path'] ?? '/',
//       depth: json['depth'] ?? 0,
//       isActive: json['is_active'] ?? false,
//       type: json['type'] ?? '',
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'description': description,
//       'slug': slug,
//       'cover_image': coverImage,
//       'icon': icon,
//       'path': path,
//       'depth': depth,
//       'type': type,
//       'is_active': isActive,
//     };
//   }
// }
