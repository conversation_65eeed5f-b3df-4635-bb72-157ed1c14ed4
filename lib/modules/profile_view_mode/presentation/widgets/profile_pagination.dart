import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/constants/app_assets.dart';

class ProfilePagination extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final ValueChanged<int> onPageChanged;

  const ProfilePagination({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> pageButtons = [];

    pageButtons.add(_buildPageButton(1, currentPage == 0, context));

    if (currentPage == 0 && totalPages > 2) {
      pageButtons.add(_buildPageButton(2, currentPage == 1, context));
    }

    if (currentPage > 2) {
      pageButtons.add(_buildDots(context));
    }

    if (currentPage > 0 && currentPage < totalPages - 1) {
      pageButtons.add(_buildPageButton(currentPage + 1, true, context));
    }

    if (currentPage < totalPages - 3) {
      pageButtons.add(_buildDots(context));
    }

    if (currentPage == totalPages - 1 && totalPages > 2) {
      pageButtons.add(_buildPageButton(
          totalPages - 1, currentPage == totalPages - 2, context));
    }

    if (totalPages > 1) {
      pageButtons.add(
          _buildPageButton(totalPages, currentPage == totalPages - 1, context));
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            color: currentPage > 0 ? Colors.black : Colors.grey,
            width: 16.w,
            height: 16.h,
          ),
          onPressed:
              currentPage > 0 ? () => onPageChanged(currentPage - 1) : null,
        ),
        ...pageButtons,
        IconButton(
          icon: SvgPicture.asset(
            AppAssets.arrowRightSvg,
            color: currentPage < totalPages - 1 ? Colors.black : Colors.grey,
            width: 16.w,
            height: 16.h,
          ),
          onPressed: currentPage < totalPages - 1
              ? () => onPageChanged(currentPage + 1)
              : null,
        ),
      ],
    );
  }

  Widget _buildDots(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      child: Text('...', style: Theme.of(context).textTheme.displaySmall),
    );
  }

  Widget _buildPageButton(int page, bool isSelected, BuildContext context) {
    return GestureDetector(
      onTap: () => onPageChanged(page - 1),
      child: Container(
        width: 36.w,
        height: 36.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).blackPrimary(context)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Text(
          '$page',
          style: TextStyle(
            fontSize: 16,
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
