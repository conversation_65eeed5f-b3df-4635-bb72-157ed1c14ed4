import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/widgets/text_icon_widget.dart';
import 'package:multime_app/modules/seller/models/seller_profile.dart';

class ContainerProfile extends StatelessWidget {
  final int sellerID;
  final SellerProfileModel? sellerProfile;

  const ContainerProfile({
    super.key,
    required this.sellerID,
    this.sellerProfile,
  });

  String formatViews(int views) {
    if (views >= 1000) {
      return '${(views / 1000).toStringAsFixed(1)}k';
    }
    return views.toString();
  }

  // String _getUserInitials() {
  //   if (sellerProfile?.user.firstName?.isNotEmpty == true &&
  //       sellerProfile?.user.lastName?.isNotEmpty == true) {
  //     return '${sellerProfile!.user.firstName![0]}${sellerProfile!.user.lastName![0]}'
  //         .toUpperCase();
  //   } else if (sellerProfile?.shopName.isNotEmpty == true) {
  //     return sellerProfile!.shopName.substring(0, 1).toUpperCase();
  //   } else {
  //     return 'U'; // Default for User
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(children: [
                  SizedBox(
                    width: 72.w,
                    height: 72.h,
                    child:
                        sellerProfile?.user.profilePicture?.isNotEmpty == true
                            ? CircleAvatar(
                                radius: 30,
                                backgroundImage: NetworkImage(
                                  sellerProfile!.user.profilePicture!,
                                ),
                              )
                            : CircleAvatar(
                                radius: 30,
                                backgroundColor: Theme.of(context).primaryColor,
                                child: Text(
                                  "${sellerProfile?.user.firstName?.substring(0, 1) ?? 'U'}${sellerProfile?.user.lastName?.substring(0, 1) ?? ''}",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 24.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: SvgPicture.asset(
                      AppAssets.actionPoint,
                      width: 16.w,
                      height: 16.h,
                    ),
                  ),
                ]),
                Gap(16.h),
                Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Text(
                                sellerProfile?.user.displayName ?? '',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightHeadingXSmall,
                                softWrap: true,
                              ),
                            ),
                            Gap(16.w),
                            ElevatedButton(
                              onPressed: () {},
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Theme.of(context).whitePrimary(context),
                                minimumSize: Size(100.w, 40.h),
                                padding: EdgeInsets.symmetric(
                                    vertical: 6.h, horizontal: 12.w),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.r),
                                  side: BorderSide(
                                    color: Theme.of(context).primaryColor,
                                    width: 1.w,
                                  ),
                                ),
                              ),
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    AppAssets.message2Svg,
                                    colorFilter: ColorFilter.mode(
                                      Theme.of(context).primaryColor,
                                      BlendMode.srcIn,
                                    ),
                                    width: 20.w,
                                    height: 20.h,
                                  ),
                                  Gap(6.w),
                                  Text(
                                    'Contact',
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelLarge!
                                        .copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: Colors.red,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Gap(8.h),
                        Row(
                          children: [
                            Expanded(
                              child: TextIcon(
                                text:
                                    sellerProfile?.profession.isNotEmpty == true
                                        ? sellerProfile!.profession
                                        : 'Professional',
                                svgIcon: AppAssets.briefcase1Svg,
                                maxLines: 1,
                              ),
                            ),
                            Gap(12.w),
                            Expanded(
                              child: TextIcon(
                                text: sellerProfile?.user.country?.title ??
                                    'Viet Nam',
                                svgIcon: AppAssets.locationTick,
                                maxLines: 1,
                              ),
                            ),
                            Gap(12.w),
                            Expanded(
                              child: TextIcon(
                                text: formatViews(1200),
                                svgIcon: AppAssets.eye1Svg,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                        Gap(16.h),
                        Text(
                          sellerProfile?.about.isNotEmpty == true
                              ? sellerProfile!.about
                              : 'Personal Training makes personalized fitness accessible and convenient by connecting clients with certified personal trainers for customized workout sessions.',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color,
                                    fontSize: 14.sp,
                                  ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          softWrap: true,
                        ),
                      ]),
                ),
              ],
            ),
            Gap(16.h),
          ],
        ),
      ),
    );
  }
}
