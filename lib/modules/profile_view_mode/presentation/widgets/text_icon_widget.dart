import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class TextIcon extends StatelessWidget {
  final String text;
  final String svgIcon;
  final double? iconSize;
  final TextStyle? textStyle;
  final int maxLines;

  const TextIcon({
    super.key,
    required this.text,
    required this.svgIcon,
    this.iconSize,
    this.textStyle,
    required this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveTextStyle =
        textStyle ?? Theme.of(context).textTheme.lightBodySmallBold;
    const iconSize = 14.0;

    return Row(
      children: [
        SvgPicture.asset(
          svgIcon,
          width: iconSize,
          height: iconSize,
        ),
        Gap(8.w),
        Expanded(
          child: Text(
            text,
            style: effectiveTextStyle,
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
            softWrap: false,
          ),
        ),
      ],
    );
  }
}
