import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/extension.dart';
import 'package:multime_app/modules/profile_view_mode/data/enum/enum.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/seach/bloc/search_seller_bloc.dart';

class SearchHeaderSliver extends StatefulWidget {
  final TabController tabController;
  final int sellerID;
  final TextEditingController searchController;
  final Function(String)? onSearchChanged;
  final List<String> recentSearches;

  const SearchHeaderSliver({
    super.key,
    required this.tabController,
    required this.sellerID,
    required this.searchController,
    this.onSearchChanged,
    required this.recentSearches,
  });

  @override
  State<SearchHeaderSliver> createState() => _SearchHeaderSliverState();
}

class _SearchHeaderSliverState extends State<SearchHeaderSliver> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final hasRecentSearches = widget.recentSearches.isNotEmpty;

    // Chiều cao theo 1 hoặc 2 dòng Wrap
    final recentMaxHeight = _isExpanded ? 2 * 36.h + 8.h : 36.h;

    final expandedHeight = hasRecentSearches
        ? (kToolbarHeight + 100.h + recentMaxHeight)
        : kToolbarHeight;

    return SliverLayoutBuilder(
      builder: (context, constraints) {
        final scrollOffset = constraints.scrollOffset;

        final collapseRatio =
        (scrollOffset / (expandedHeight - kToolbarHeight)).clamp(0.0, 1.0);
        final isCollapsed = collapseRatio > 0.8;

        return SliverAppBar(
          expandedHeight: expandedHeight,
          pinned: true,
          backgroundColor: Theme.of(context).whitePrimary(context),
          leading: GestureDetector(
            child: Icon(Icons.arrow_back_ios_outlined, color: Colors.grey[700]),
            onTap: () => context.pop(),
          ),
          title: _buildSearchBar(context),
          flexibleSpace: hasRecentSearches && !isCollapsed
              ? FlexibleSpaceBar(
            collapseMode: CollapseMode.pin,
            background: Padding(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + kToolbarHeight,
              ),
              child: _buildRecentSearches(context),
            ),
          )
              : null,
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(48.h),
            child: _buildTabBar(context),
          ),
        );
      },
    );
  }

  Widget _buildRecentSearches(BuildContext context) {
    final showToggle = widget.recentSearches.length > 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Row(
            children: [
              Text('Recent Searches',
                  style: Theme.of(context).textTheme.lightBodyLargeBold),
              const Spacer(),
              IconButton(
                onPressed: () {
                  context.read<SearchSellerBloc>().add(ClearRecentSearchTerms());
                },
                icon: Icon(Icons.delete_outline, color: Colors.grey[700]),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: ClipRect(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: _isExpanded ? 2 * 36.h + 8.h : 36.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(child: Wrap(
                    spacing: 8.w,
                    runSpacing: 8.h,
                    children: [
                      ...widget.recentSearches.map(
                            (item) => _buildHistoryRecentSearches(context, item),
                      ),

                    ],
                  )),
                  Gap(16.w),
                  if (showToggle && _isExpanded == false)
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                      child: Icon(
                        Icons.expand_more,
                        size: 32.sp,
                        color: Colors.grey[700],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }


  Widget _buildSearchBar(BuildContext context) {
    return TextField(
      controller: widget.searchController,
      onChanged: widget.onSearchChanged,
      decoration: InputDecoration(
        hintText: 'Search services, shop...',
        hintStyle: TextStyle(
          color: Colors.grey[600],
          fontSize: 14.sp,
        ),
        prefixIcon: Icon(
          Icons.search_rounded,
          color: Colors.grey[700],
          size: 20.w,
        ),
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
        filled: true,
        fillColor: Theme.of(context).scaffoldBackgroundColor,
      ),
      style: TextStyle(
        fontSize: 14.sp,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      color: Theme.of(context).whitePrimary(context),
      child: TabBar(
        controller: widget.tabController,
        tabs: SearchSellerTab.values
            .map((tab) => Tab(text: tab.name.capitalize()))
            .toList(),
        indicatorColor: Theme.of(context).primaryColor,
        labelColor: Theme.of(context).primaryColor,
        unselectedLabelColor:
        Theme.of(context).textTheme.bodyMedium?.color,
        indicatorWeight: 2.w,
      ),
    );
  }

  Widget _buildHistoryRecentSearches(BuildContext context, String text) {
    return GestureDetector(
      onTap: () => widget.onSearchChanged?.call(text),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 12.sp),
        ),
      ),
    );
  }
}
