import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/profile_view_mode/data/enum/enum.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/card_service_profile.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/card_shop_profile.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/generic_profile_sliver.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/seach/bloc/search_seller_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/seach/widgets/search_header_sliver.dart';

class SearchSeller extends StatefulWidget {
  final int sellerId;
  const SearchSeller({super.key, required this.sellerId});

  @override
  State<SearchSeller> createState() => _SearchSellerState();
}

class _SearchSellerState extends State<SearchSeller>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late TextEditingController _searchController;


  void _onTabChanged() {
    if (_tabController.indexIsChanging ||
        _tabController.index != _tabController.previousIndex) {
      final tab = SearchSellerTab.values[_tabController.index];
      context.read<SearchSellerBloc>().add(TabSearchSellerChanged(
          tab, widget.sellerId, _searchController.text.trim()));
    }
  }

  void _onSearchChanged(String query) {
    final currentTab = SearchSellerTab.values[_tabController.index];
    context.read<SearchSellerBloc>().add(
          LoadSearchSellerData(widget.sellerId, currentTab, query.trim()),
        );
  }

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();

    _tabController =
        TabController(length: SearchSellerTab.values.length, vsync: this);

    // Listen to tab changes from both tap and swipe
    _tabController.addListener(_onTabChanged);

    context.read<SearchSellerBloc>().add(LoadSearchSellerData(widget.sellerId,
        SearchSellerTab.service, _searchController.text.trim()));
  }

  @override
  void dispose() {
    super.dispose();
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _searchController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<SearchSellerBloc, SearchSellerState>(
          builder: (context, state) {
        if (_tabController.index !=
            SearchSellerTab.values.indexOf(state.currentTab)) {
          _tabController
              .animateTo(SearchSellerTab.values.indexOf(state.currentTab));
        }
        return NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SearchHeaderSliver(
                searchController: _searchController,
                tabController: _tabController,
                sellerID: widget.sellerId,
                onSearchChanged: _onSearchChanged,
                recentSearches: state.recentSearches,
              ),

            ];
          },
          body: TabBarView(
            controller: _tabController,
            children: SearchSellerTab.values.map((tab) {
              return RefreshIndicator(
                onRefresh: () => _onRefreshTab(tab),
                child: _buildTabContent(state, tab),
              );
            }).toList(),
          ),
        );
      }),
    );
  }

  Widget _buildTabContent(SearchSellerState state, SearchSellerTab tab) {
    switch (tab) {
      case SearchSellerTab.service:
        return NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            if (scrollInfo is ScrollEndNotification &&
                scrollInfo.metrics.pixels >=
                    scrollInfo.metrics.maxScrollExtent - 300) {
              if (state.currentTab == tab) {
                _triggerLoadMore(tab);
              }
            }
            return false;
          },
          child: CustomScrollView(
            slivers: [
              ProfileTabSliverGrid(
                title: 'No Services Available',
                subtitle: 'This profile hasn\'t added any services yet.',
                items: state.servicePaged.items,
                response: state.servicesResponse,
                hasMore: state.servicePaged.hasMore,
                aspectRatio: 0.75,
                onRetry: () {
                  context.read<SearchSellerBloc>().add(
                        LoadSearchSellerData(
                            widget.sellerId,
                            SearchSellerTab.service,
                            _searchController.text.trim()),
                      );
                },
                itemBuilder: (context, item) => CardServiceProfileSeller(
                  status: 'Online',
                  firstName: item.title,
                  image: item.image[0],
                  description: TFormatter.htmlToFormattedText(item.description),
                  rating: item.rating,
                  price: item.price.toDouble(),
                ),
              ),
            ],
          ),
        );
      case SearchSellerTab.shop:
        return NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            if (scrollInfo is ScrollEndNotification &&
                scrollInfo.metrics.pixels >=
                    scrollInfo.metrics.maxScrollExtent - 300) {
              if (state.currentTab == tab) {
                _triggerLoadMore(tab);
              }
            }
            return false;
          },
          child: CustomScrollView(
            key: PageStorageKey('shop_tab'),
            slivers: [
              ProfileTabSliverGrid(
                title: 'No Shop Available',
                subtitle: 'This profile hasn\'t added any shops yet.',
                items: state.shopPaged.items,
                response: state.shopResponse,
                hasMore: state.shopPaged.hasMore,
                aspectRatio: 0.65,
                onRetry: () {
                  context.read<SearchSellerBloc>().add(
                        LoadSearchSellerData(
                            widget.sellerId,
                            SearchSellerTab.shop,
                            _searchController.text.trim()),
                      );
                },
                itemBuilder: (context, item) => CardShopProfileSeller(
                  firstName: item.name,
                  image: item.image.isNotEmpty ? item.image[0] : '',
                  description:TFormatter.htmlToFormattedText(item.description),
                  rating: item.rating,
                  price: item.pricingTiers.isNotEmpty
                      ? item.pricingTiers[0].price
                      : 0.0,
                ),
              ),
            ],
          ),
        );
    }
  }


  Future<void> _onRefreshTab(SearchSellerTab tab) async {
    final bloc = context.read<SearchSellerBloc>();
    final sellerId = widget.sellerId;

    bloc.add(
        LoadSearchSellerData(sellerId, tab, _searchController.text.trim()));
  }

  void _triggerLoadMore(SearchSellerTab tab) {
    final bloc = context.read<SearchSellerBloc>();
    final sellerId = widget.sellerId;

    bloc.add(
        LoadMoreSearchSellerData(sellerId, tab, _searchController.text.trim()));
  }
}
