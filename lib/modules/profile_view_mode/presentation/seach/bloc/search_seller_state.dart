part of 'search_seller_bloc.dart';

class SearchSellerState extends Equatable {
  final SearchSellerTab currentTab;

  // Services tab
  final ApiResponse<List<ServiceModel>> servicesResponse;
  final PagedData<ServiceModel> servicePaged;

  //Shop tab
  final ApiResponse<List<ProductModel>> shopResponse;
  final PagedData<ProductModel> shopPaged;

  // history search
  final List<String> recentSearches;

  const SearchSellerState({
    this.currentTab = SearchSellerTab.service,
    this.servicesResponse = const ApiResponse.loading(),
    this.servicePaged = const PagedData(items: [], page: 1, hasMore: true),
    this.shopResponse = const ApiResponse.loading(),
    this.shopPaged = const PagedData(items: [], page: 1, hasMore: true),
    this.recentSearches = const [],
  });

  SearchSellerState copyWith({
    SearchSellerTab? currentTab,
    ApiResponse<List<ServiceModel>>? servicesResponse,
    PagedData<ServiceModel>? servicePaged,
    ApiResponse<List<ProductModel>>? shopResponse,
    PagedData<ProductModel>? shopPaged,
    List<String>? recentSearches,
  }) {
    return SearchSellerState(
      currentTab: currentTab ?? this.currentTab,
      servicesResponse: servicesResponse ?? this.servicesResponse,
      servicePaged: servicePaged ?? this.servicePaged,
      shopResponse: shopResponse ?? this.shopResponse,
      shopPaged: shopPaged ?? this.shopPaged,
      recentSearches: recentSearches ?? this.recentSearches,
    );
  }

  @override
  List<Object?> get props => [
        currentTab,
        servicesResponse,
        servicePaged,
        shopResponse,
        shopPaged,
        recentSearches,
      ];
}
