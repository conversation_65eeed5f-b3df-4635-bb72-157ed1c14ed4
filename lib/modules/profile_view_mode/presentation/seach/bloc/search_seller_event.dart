part of 'search_seller_bloc.dart';



abstract class SearchSellerEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class TabSearchSellerChanged extends SearchSellerEvent {
  final SearchSellerTab tab;
  final int sellerId;
  final String keyword;

  TabSearchSellerChanged(this.tab, this.sellerId, this.keyword);

  @override
  List<Object> get props => [tab, sellerId, keyword];
}

// Load data for specific tab
class LoadSearchSellerData extends SearchSellerEvent {
  final int sellerId;
  final SearchSellerTab tab;
  final String keyword;

  LoadSearchSellerData(this.sellerId, this.tab, this.keyword);

  @override
  List<Object> get props => [sellerId, tab, keyword];
}

// Load more data for specific tab
class LoadMoreSearchSellerData extends SearchSellerEvent {
  final int sellerId;
  final SearchSellerTab tab;
  final String keyword;

  LoadMoreSearchSellerData(this.sellerId, this.tab, this.keyword);

  @override
  List<Object> get props => [sellerId, tab, keyword];
}


class LoadRecentSearchTerms extends SearchSellerEvent {
  LoadRecentSearchTerms();

  @override
  List<Object> get props => [];
}

// Clear all recent search terms
class ClearRecentSearchTerms extends SearchSellerEvent {
  ClearRecentSearchTerms();

  @override
  List<Object> get props => [];
}
// Remove a specific term from recent searches
class RemoveRecentSearchTerm extends SearchSellerEvent {
  final int index;

  RemoveRecentSearchTerm(this.index);

  @override
  List<Object> get props => [index];
}
