import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/profile_view_mode/data/repositories/search_seller_repository.dart';

import '../../../../../core/base/api_response/api_response.dart';
import '../../../../../core/base/api_response/status.dart';
import '../../../../../core/domain/global_dependencies.dart';
import '../../../../../core/domain/storages/global_storage.dart';
import '../../../../../core/utils/debounce.dart';
import '../../../../strongbody.ai_mode/data/models/services/service_model.dart';
import '../../../../strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import '../../../data/enum/enum.dart';
import '../../../data/models/product_seller_model.dart';
import '../../profile_view_home/bloc/page_data.dart';

part 'search_seller_event.dart';
part 'search_seller_state.dart';

class SearchSellerBloc extends Bloc<SearchSellerEvent, SearchSellerState> {
  final SearchSellerRepoRemote _searchSellerRepo;
  final Debouncer _debouncer = Debouncer(
    delay: const Duration(milliseconds: 1000),
  );
  SearchSellerBloc(this._searchSellerRepo) : super(SearchSellerState()) {
    on<TabSearchSellerChanged>(_onTabSearchSellerChanged);

    // Generic data loading events
    on<LoadSearchSellerData>(_onLoadSearchSellerData);
    on<LoadMoreSearchSellerData>(_onLoadMoreSearchSellerData);

    // Recent search management
    on<LoadRecentSearchTerms>(_onLoadRecentSearchTerms);
    on<ClearRecentSearchTerms>(_onClearRecentSearchTerms);
    on<RemoveRecentSearchTerm>(_onRemoveRecentSearchTerm);

    add(LoadRecentSearchTerms());
  }

  Future<void> _onLoadSearchSellerData(
      LoadSearchSellerData event, Emitter<SearchSellerState> emit) async {
    switch (event.tab) {
      case SearchSellerTab.service:
        await _loadServices(event.sellerId, event.keyword, emit,
            isLoadMore: false);
        break;
      case SearchSellerTab.shop:
        await _loadShop(event.sellerId, event.keyword, emit, isLoadMore: false);
        break;
    }
  }

  Future<void> _onLoadMoreSearchSellerData(
      LoadMoreSearchSellerData event, Emitter<SearchSellerState> emit) async {
    switch (event.tab) {
      case SearchSellerTab.service:
        await _loadServices(event.sellerId, event.keyword, emit,
            isLoadMore: true);
        break;
      case SearchSellerTab.shop:
        await _loadShop(event.sellerId, event.keyword, emit, isLoadMore: true);
        break;
    }
  }

  Future<void> _onTabSearchSellerChanged(
      TabSearchSellerChanged event, Emitter<SearchSellerState> emit) async {
    emit(state.copyWith(currentTab: event.tab));

    // Automatically load data when switching tabs if no data or error
    final shouldLoadData = _shouldLoadDataForTab(event.tab);
    if (shouldLoadData) {
      switch (event.tab) {
        case SearchSellerTab.service:
          if (state.servicePaged.items.isEmpty ||
              state.servicesResponse.status == Status.error) {
            await _loadServices(event.sellerId, event.keyword, emit,
                isLoadMore: false);
          }
          break;
        case SearchSellerTab.shop:
          if (state.shopPaged.items.isEmpty ||
              state.shopResponse.status == Status.error) {
            await _loadShop(event.sellerId, event.keyword, emit,
                isLoadMore: false);
          }
          break;
      }
    }
  }

  Future<void> _onLoadRecentSearchTerms(
      LoadRecentSearchTerms event, Emitter<SearchSellerState> emit) async {
    try {
      final recentSearches = gs.getSearchHistories(
        GlobalStorageKey.searchHistory,
      );
      emit(
        state.copyWith(
          recentSearches: recentSearches,
        ),
      );
    } catch (e) {
      print('Error loading recent searches: $e');
    }
  }

  Future<void> _onClearRecentSearchTerms(
      ClearRecentSearchTerms event, Emitter<SearchSellerState> emit) async {
    await gs.clearSearchHistories(
      key: GlobalStorageKey.searchHistory,
    );
    emit(
      state.copyWith(
        recentSearches: [],
      ),
    );
  }

  Future<void> _onRemoveRecentSearchTerm(
      RemoveRecentSearchTerm event, Emitter<SearchSellerState> emit) async {
    await gs.removeSearchHistory(
      event.index,
      key: GlobalStorageKey.searchHistory,
    );
    final recentSearches = gs.getSearchHistories(
      GlobalStorageKey.searchHistory,
    );
    emit(
      state.copyWith(
        recentSearches: recentSearches,
      ),
    );
  }

  Future<void> _loadServices(
      int sellerId, String keyword, Emitter<SearchSellerState> emit,
      {required bool isLoadMore}) async {
    if (isLoadMore) {
      if (!state.servicePaged.hasMore ||
          state.servicesResponse.status == Status.loading) {
        return;
      }

      final nextPage = state.servicePaged.page + 1;

      final response =
          await _searchSellerRepo.getServices(sellerId, nextPage, keyword);

      if (response.status == Status.completed) {
        final newItems = response.data!;
        final updatedItems = [...state.servicePaged.items, ...newItems];
        emit(state.copyWith(
          servicesResponse: ApiResponse.completed(updatedItems),
          servicePaged: PagedData(
            items: updatedItems,
            page: nextPage,
            hasMore: newItems.length == 10,
          ),
        ));
      } else if (response.status == Status.error) {
        emit(state.copyWith(servicesResponse: response));
      }
    } else {
      return Future(() {
        return _debouncer.asynchronousDebounce(() async {
          try {
            if (!emit.isDone) {
              // Reset về loading và page đầu tiên
              emit(state.copyWith(
                servicesResponse: const ApiResponse.loading(),
                servicePaged:
                    const PagedData(items: [], page: 1, hasMore: true),
              ));
            }

            final response =
                await _searchSellerRepo.getServices(sellerId, 1, keyword);

            await gs.addSearchHistory(
              keyword,
              GlobalStorageKey.searchHistory,
            );

            final recentSearches = gs.getSearchHistories(
              GlobalStorageKey.searchHistory,
            );

            if (!emit.isDone) {
              if (response.status == Status.completed) {
                final newItems = response.data!;
                emit(state.copyWith(
                  servicesResponse: ApiResponse.completed(newItems),
                  recentSearches: recentSearches,
                  servicePaged: PagedData(
                    items: newItems,
                    page: 1,
                    hasMore: newItems.length == 10,
                  ),
                ));
              } else {
                emit(state.copyWith(servicesResponse: response));
              }
            }
          } catch (e) {
            print('Error in Search Services: $e');
            if (!emit.isDone) {
              emit(state.copyWith(
                servicesResponse: ApiResponse.error(e.toString()),
              ));
            }
          }
        });
      });
    }
  }

  Future<void> _loadShop(
      int sellerId, String keyword, Emitter<SearchSellerState> emit,
      {required bool isLoadMore}) async {
    if (isLoadMore) {
      if (!state.shopPaged.hasMore ||
          state.shopResponse.status == Status.loading) {
        return;
      }

      final nextPage = state.shopPaged.page + 1;
      final response =
          await _searchSellerRepo.getProducts(sellerId, nextPage, keyword);
      await gs.addSearchHistory(
        keyword,
        GlobalStorageKey.searchHistory,
      );

      final recentSearches = gs.getSearchHistories(
        GlobalStorageKey.searchHistory,
      );

      if (response.status == Status.completed) {
        final newItems = response.data!;
        final updatedItems = [...state.shopPaged.items, ...newItems];
        emit(state.copyWith(
          shopResponse: ApiResponse.completed(updatedItems),
          recentSearches: recentSearches,
          shopPaged: PagedData(
            items: updatedItems,
            page: nextPage,
            hasMore: newItems.length == 10,
          ),
        ));
      } else if (response.status == Status.error) {
        emit(state.copyWith(shopResponse: response));
      }
    } else {
      return Future(() {
        return _debouncer.asynchronousDebounce(() async {
          try {
            if (!emit.isDone) {
              // Reset về loading và page đầu tiên
              emit(state.copyWith(
                shopResponse: const ApiResponse.loading(),
                shopPaged: const PagedData(items: [], page: 1, hasMore: true),
              ));
            }

            final response =
                await _searchSellerRepo.getProducts(sellerId, 1, keyword);

            if (!emit.isDone) {
              if (response.status == Status.completed) {
                final newItems = response.data!;
                emit(state.copyWith(
                  shopResponse: ApiResponse.completed(newItems),
                  shopPaged: PagedData(
                    items: newItems,
                    page: 1,
                    hasMore: newItems.length == 10,
                  ),
                ));
              } else {
                emit(state.copyWith(shopResponse: response));
              }
            }
          } catch (e) {
            print('Error in Search Shop: $e');
            if (!emit.isDone) {
              emit(state.copyWith(
                shopResponse: ApiResponse.error(e.toString()),
              ));
            }
          }
        });
      });
    }
  }

  bool _shouldLoadDataForTab(SearchSellerTab tab) {
    switch (tab) {
      case SearchSellerTab.service:
        return state.servicePaged.items.isEmpty ||
            state.servicesResponse.status == Status.error;
      case SearchSellerTab.shop:
        return state.shopPaged.items.isEmpty ||
            state.shopResponse.status == Status.error;
    }
  }
}
