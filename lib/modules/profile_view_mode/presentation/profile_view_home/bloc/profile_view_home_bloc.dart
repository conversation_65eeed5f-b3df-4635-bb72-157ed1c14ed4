import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/data/repositories/profile_seller_repository.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/page_data.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_event.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_state.dart';

import '../../../../../core/base/api_response/api_response.dart';
import '../../../../../core/base/api_response/status.dart';
import '../../../data/enum/enum.dart';

class ProfileSellerBloc extends Bloc<ProfileSellerEvent, ProfileSellerState> {
  final ProfileSellerRepoRemote _profileSellerRepo;

  ProfileSellerBloc(this._profileSellerRepo) : super(ProfileSellerState()) {
    on<TabProfileSellerChanged>(_onTabProfileSellerChanged);

    // Generic data loading events
    on<LoadProfileSellerData>(_onLoadProfileSellerData);
    on<LoadMoreProfileSellerData>(_onLoadMoreProfileSellerData);
    // Profile Event
    on<LoadProfileSeller>(_onLoadProfileSeller);
  }

  Future<void> _onTabProfileSellerChanged(
      TabProfileSellerChanged event, Emitter<ProfileSellerState> emit) async {
    emit(state.copyWith(currentTab: event.tab));

    // Tự động load dữ liệu khi chuyển tab nếu chưa có data hoặc đã có lỗi
    final shouldLoadData = _shouldLoadDataForTab(event.tab);
    if (shouldLoadData) {
      switch (event.tab) {
        case ProfileSellerTab.service:
          await _loadServices(event.sellerId, emit, isLoadMore: false);
          break;
        case ProfileSellerTab.shop:
          await _loadShop(event.sellerId, emit, isLoadMore: false);
          break;
        case ProfileSellerTab.blog:
          await _loadBlog(event.sellerId, emit, isLoadMore: false);
          break;
        case ProfileSellerTab.review:
          // TODO: Implement review loading
          break;
      }
    }
  }

  bool _shouldLoadDataForTab(ProfileSellerTab tab) {
    switch (tab) {
      case ProfileSellerTab.service:
        return state.servicePaged.items.isEmpty ||
            state.servicesResponse.status == Status.error;
      case ProfileSellerTab.shop:
        return state.shopPaged.items.isEmpty ||
            state.shopResponse.status == Status.error;
      case ProfileSellerTab.blog:
        return state.blogsPaged.items.isEmpty ||
            state.blogsResponse.status == Status.error;
      case ProfileSellerTab.review:
        return false; // Not implemented yet
    }
  }

  Future<void> _onLoadProfileSellerData(
      LoadProfileSellerData event, Emitter<ProfileSellerState> emit) async {
    switch (event.tab) {
      case ProfileSellerTab.service:
        await _loadServices(event.sellerId, emit, isLoadMore: false);
        break;
      case ProfileSellerTab.shop:
        await _loadShop(event.sellerId, emit, isLoadMore: false);
        break;
      case ProfileSellerTab.blog:
        await _loadBlog(event.sellerId, emit, isLoadMore: false);
        break;
      case ProfileSellerTab.review:
        // TODO: Implement review loading
        break;
    }
  }

  Future<void> _onLoadMoreProfileSellerData(
      LoadMoreProfileSellerData event, Emitter<ProfileSellerState> emit) async {
    switch (event.tab) {
      case ProfileSellerTab.service:
        await _loadServices(event.sellerId, emit, isLoadMore: true);
        break;
      case ProfileSellerTab.shop:
        await _loadShop(event.sellerId, emit, isLoadMore: true);
        break;
      case ProfileSellerTab.blog:
        await _loadBlog(event.sellerId, emit, isLoadMore: true);
        break;
      case ProfileSellerTab.review:
        // TODO: Implement review load more
        break;
    }
  }

  Future<void> _loadServices(int sellerId, Emitter<ProfileSellerState> emit,
      {required bool isLoadMore}) async {
    if (isLoadMore) {
      if (!state.servicePaged.hasMore ||
          state.servicesResponse.status == Status.loading) {
        return;
      }

      final nextPage = state.servicePaged.page + 1;
      final response = await _profileSellerRepo.getServices(sellerId, nextPage);

      if (response.status == Status.completed) {
        final newItems = response.data!;
        final updatedItems = [...state.servicePaged.items, ...newItems];
        emit(state.copyWith(
          servicesResponse: ApiResponse.completed(updatedItems),
          servicePaged: PagedData(
            items: updatedItems,
            page: nextPage,
            hasMore: newItems.length == 10,
          ),
        ));
      } else if (response.status == Status.error) {
        emit(state.copyWith(servicesResponse: response));
      }
    } else {
      // Reset về loading và page đầu tiên
      emit(state.copyWith(
        servicesResponse: const ApiResponse.loading(),
        servicePaged: const PagedData(items: [], page: 1, hasMore: true),
      ));

      final response = await _profileSellerRepo.getServices(sellerId, 1);

      if (response.status == Status.completed) {
        final newItems = response.data!;
        emit(state.copyWith(
          servicesResponse: ApiResponse.completed(newItems),
          servicePaged: PagedData(
            items: newItems,
            page: 1,
            hasMore: newItems.length == 10,
          ),
        ));
      } else {
        emit(state.copyWith(servicesResponse: response));
      }
    }
  }

  Future<void> _loadShop(int sellerId, Emitter<ProfileSellerState> emit,
      {required bool isLoadMore}) async {
    if (isLoadMore) {
      if (!state.shopPaged.hasMore ||
          state.shopResponse.status == Status.loading) {
        return;
      }

      final nextPage = state.shopPaged.page + 1;
      final response =
          await _profileSellerRepo.getProductSeller(sellerId, nextPage);

      if (response.status == Status.completed) {
        final newItems = response.data!;
        final updatedItems = [...state.shopPaged.items, ...newItems];
        emit(state.copyWith(
          shopResponse: ApiResponse.completed(updatedItems),
          shopPaged: PagedData(
            items: updatedItems,
            page: nextPage,
            hasMore: newItems.length == 10,
          ),
        ));
      } else if (response.status == Status.error) {
        emit(state.copyWith(shopResponse: response));
      }
    } else {
      // Reset về loading và page đầu tiên
      emit(state.copyWith(
        shopResponse: const ApiResponse.loading(),
        shopPaged: const PagedData(items: [], page: 1, hasMore: true),
      ));

      final response = await _profileSellerRepo.getProductSeller(sellerId, 1);

      if (response.status == Status.completed) {
        final newItems = response.data!;
        emit(state.copyWith(
          shopResponse: ApiResponse.completed(newItems),
          shopPaged: PagedData(
            items: newItems,
            page: 1,
            hasMore: newItems.length == 10,
          ),
        ));
      } else {
        emit(state.copyWith(shopResponse: response));
      }
    }
  }

  Future<void> _loadBlog(int sellerId, Emitter<ProfileSellerState> emit,
      {required bool isLoadMore}) async {
    if (isLoadMore) {
      if (!state.blogsPaged.hasMore ||
          state.blogsResponse.status == Status.loading) {
        return;
      }

      final nextPage = state.blogsPaged.page + 1;
      final response =
          await _profileSellerRepo.getBlogsSeller(sellerId, nextPage, 10);

      if (response.status == Status.completed) {
        final newItems = response.data!;
        final updatedItems = [...state.blogsPaged.items, ...newItems];
        emit(state.copyWith(
          blogsResponse: ApiResponse.completed(updatedItems),
          blogsPaged: PagedData(
            items: updatedItems,
            page: nextPage,
            hasMore: newItems.length == 10,
          ),
        ));
      } else if (response.status == Status.error) {
        emit(state.copyWith(blogsResponse: response));
      }
    } else {
      emit(state.copyWith(
        blogsResponse: const ApiResponse.loading(),
        blogsPaged: const PagedData(items: [], page: 1, hasMore: true),
      ));

      final response = await _profileSellerRepo.getBlogsSeller(sellerId, 1, 10);

      if (response.status == Status.completed) {
        final newItems = response.data!;
        emit(state.copyWith(
          blogsResponse: ApiResponse.completed(newItems),
          blogsPaged: PagedData(
            items: newItems,
            page: 1,
            hasMore: newItems.length == 10,
          ),
        ));
      } else {
        emit(state.copyWith(blogsResponse: response));
      }
    }
  }

  Future<void> _onLoadProfileSeller(
      LoadProfileSeller event, Emitter<ProfileSellerState> emit) async {
    emit(state.copyWith(profileResponse: const ApiResponse.loading()));
    final result = await _profileSellerRepo.getProfile(event.profileId);
    emit(state.copyWith(profileResponse: result));
  }
}
