import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/page_data.dart';

import '../../../../../core/base/api_response/api_response.dart';
import '../../../../../core/model/blog_model_seller/blog_model_seller.dart';
import '../../../../seller/models/seller_profile.dart';
import '../../../../strongbody.ai_mode/data/models/services/service_model.dart';
import '../../../../strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import '../../../data/enum/enum.dart';
import '../../../data/models/product_seller_model.dart';

class ProfileSellerState extends Equatable {
  final ProfileSellerTab currentTab;
  final ApiResponse<SellerProfileModel> profileResponse;

  // Services tab
  final ApiResponse<List<ServiceModel>> servicesResponse;
  final PagedData<ServiceModel> servicePaged;

  //Shop tab
  final ApiResponse<List<ProductModel>> shopResponse;
  final PagedData<ProductModel> shopPaged;

  // Blogs tab
  final ApiResponse<List<BlogModelSeller>> blogsResponse;
  final PagedData<BlogModelSeller> blogsPaged;

  const ProfileSellerState({
    this.currentTab = ProfileSellerTab.service,
    this.profileResponse = const ApiResponse.loading(),
    this.servicesResponse = const ApiResponse.loading(),
    this.servicePaged = const PagedData(items: [], page: 1, hasMore: true),
    this.shopResponse = const ApiResponse.loading(),
    this.shopPaged = const PagedData(items: [], page: 1, hasMore: true),
    this.blogsResponse = const ApiResponse.loading(),
    this.blogsPaged = const PagedData(items: [], page: 1, hasMore: true),
  });

  ProfileSellerState copyWith({
    ProfileSellerTab? currentTab,
    ApiResponse<SellerProfileModel>? profileResponse,
    ApiResponse<List<ServiceModel>>? servicesResponse,
    PagedData<ServiceModel>? servicePaged,
    ApiResponse<List<ProductModel>>? shopResponse,
    PagedData<ProductModel>? shopPaged,
    ApiResponse<List<BlogModelSeller>>? blogsResponse,
    PagedData<BlogModelSeller>? blogsPaged,
  }) {
    return ProfileSellerState(
      currentTab: currentTab ?? this.currentTab,
      profileResponse: profileResponse ?? this.profileResponse,
      servicesResponse: servicesResponse ?? this.servicesResponse,
      servicePaged: servicePaged ?? this.servicePaged,
      shopResponse: shopResponse ?? this.shopResponse,
      shopPaged: shopPaged ?? this.shopPaged,
      blogsResponse: blogsResponse ?? this.blogsResponse,
      blogsPaged: blogsPaged ?? this.blogsPaged,
    );
  }

  @override
  List<Object?> get props => [
        currentTab,
        profileResponse,
        servicesResponse,
        servicePaged,
        shopResponse,
        shopPaged,
        blogsResponse,
        blogsPaged,
      ];
}
