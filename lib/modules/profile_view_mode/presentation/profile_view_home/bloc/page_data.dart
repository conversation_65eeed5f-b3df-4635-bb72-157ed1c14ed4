class PagedData<T> {
  final List<T> items;
  final int page;
  final bool hasMore;
  final bool isLoadingMore;

  const PagedData({
    required this.items,
    required this.page,
    required this.hasMore,
    this.isLoadingMore = false,
  });

  PagedData<T> copyWith({
    List<T>? items,
    int? page,
    bool? hasMore,
    bool? isLoadingMore,
  }) {
    return PagedData(
      items: items ?? this.items,
      page: page ?? this.page,
      hasMore: hasMore ?? this.hasMore,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }
}
