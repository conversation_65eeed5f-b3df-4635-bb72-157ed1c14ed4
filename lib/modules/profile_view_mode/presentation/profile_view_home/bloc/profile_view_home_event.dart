import 'package:equatable/equatable.dart';
import '../../../data/enum/enum.dart';

abstract class ProfileSellerEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class TabProfileSellerChanged extends ProfileSellerEvent {
  final ProfileSellerTab tab;
  final int sellerId;

  TabProfileSellerChanged(this.tab, this.sellerId);

  @override
  List<Object> get props => [tab, sellerId];
}

// Load data for specific tab
class LoadProfileSellerData extends ProfileSellerEvent {
  final int sellerId;
  final ProfileSellerTab tab;

  LoadProfileSellerData(this.sellerId, this.tab);

  @override
  List<Object> get props => [sellerId, tab];
}

// Load more data for specific tab
class LoadMoreProfileSellerData extends ProfileSellerEvent {
  final int sellerId;
  final ProfileSellerTab tab;

  LoadMoreProfileSellerData(this.sellerId, this.tab);

  @override
  List<Object> get props => [sellerId, tab];
}

// Profile Event
class LoadProfileSeller extends ProfileSellerEvent {
  final int profileId;

  LoadProfileSeller(this.profileId);

  @override
  List<Object> get props => [profileId];
}
