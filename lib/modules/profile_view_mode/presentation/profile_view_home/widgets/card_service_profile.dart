import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardServiceProfileSeller extends StatelessWidget {
  const CardServiceProfileSeller({
    super.key,
    required this.firstName,
    required this.image,
    required this.description,
    required this.rating,
    required this.price,
    required this.status,
  });

  final String firstName, image, description, status;
  final double rating, price;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 176.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image section
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                ),
                child: CustomImage(
                  path: image,
                  width: double.infinity,
                  height: 120.h,
                  fit: BoxFit.cover,
                  imageType: ImageType.network,
                ),
              ),
              Positioned(
                top: 2.h,
                right: 2.w,
                child: IconButton(
                  onPressed: () {},
                  icon: Icon(
                    Icons.favorite_outlined,
                    color: Colors.black,
                    size: 20,
                  ),
                ),
              )
            ],
          ),
          // Content section
          Expanded(
            child: Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8.r),
                  bottomRight: Radius.circular(8.r),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: 'by ',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        TextSpan(
                          text: firstName,
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall!
                              .copyWith(fontWeight: FontWeight.bold),
                        )
                      ],
                    ),
                  ),
                  Gap(4.h),
                  Expanded(
                    child: Text(
                      description,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  Gap(4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          'From US \$$price',
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(color: Colors.green),
                        ),
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 14.w),
                          Gap(2.w),
                          Text(
                            '$rating',
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall!
                                .copyWith(color: Colors.grey),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Gap(4.h),
                  Row(
                    children: [
                      Icon(Icons.airplane_ticket_outlined,
                          color: Colors.blue, size: 16.w),
                      Gap(4.w),
                      Text(
                        status,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
