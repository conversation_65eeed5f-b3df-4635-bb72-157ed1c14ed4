import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class CardBlogProfileSeller extends StatelessWidget {
  const CardBlogProfileSeller(
      {super.key,
      required this.image,
      required this.firstName,
      required this.title,
      required this.description,
      required this.category});
  final String image, firstName, title, description, category;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 176.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image section
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                ),
                child: Image.network(
                  image,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: 120.h,
                ),
              ),
              Positioned(
                top: 2.h,
                right: 2.w,
                child: IconButton(
                  onPressed: () {},
                  icon: Icon(
                    Icons.favorite_outlined,
                    color: Colors.black,
                    size: 20,
                  ),
                ),
              )
            ],
          ),
          // Content section
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8.r),
                bottomRight: Radius.circular(8.r),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                RichText(
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'by ',
                        style: Theme.of(context).textTheme.lightBodySmallMedium,
                      ),
                      TextSpan(
                        text: firstName,
                        style: Theme.of(context).textTheme.lightBodySmallBold,
                      )
                    ],
                  ),
                ),
                Gap(8.h),
                Text(
                  title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.lightBodyMediumBold,
                ),
                Gap(8.h),
                Text(
                  description,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.lightBodyLargeMedium,
                ),
                Gap(8.h),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFFD8F6E5), // light green
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    category,
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                      fontSize: 13,
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
