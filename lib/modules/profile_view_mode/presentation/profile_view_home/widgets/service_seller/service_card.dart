import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../../../app/routers/routers_name.dart';
import '../../../../../../core/utils/formatters.dart';
import '../../../../../strongbody.ai_mode/data/models/services/service_model.dart';
import '../card_service_profile.dart';

class ServiceCardSeller extends StatelessWidget {
  final ServiceModel item;
  const ServiceCardSeller({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.push(RouteName.serviceDetail, extra: item),
      child: CardServiceProfileSeller(
        status: item.status,
        firstName: item.title,
        image: item.image[0],
        description: TFormatter.htmlToFormattedText(item.description),
        rating: item.rating,
        price: item.price.toDouble(),
      ),
    );
  }
}
