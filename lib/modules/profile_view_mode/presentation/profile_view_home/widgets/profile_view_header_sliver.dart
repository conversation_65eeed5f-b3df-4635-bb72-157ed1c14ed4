import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/extension.dart';
import 'package:multime_app/modules/profile_view_mode/data/enum/enum.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_state.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/widgets/profile_view_container.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class ProfileViewHeaderSliver extends StatelessWidget {
  final TabController tabController;
  final int sellerID;

  const ProfileViewHeaderSliver({
    super.key,
    required this.tabController,
    required this.sellerID,
  });

  @override
  Widget build(BuildContext context) {
    return SliverLayoutBuilder(
      builder: (context, constraints) {
        final scrollOffset = constraints.scrollOffset;
        final expandedHeight = 380.h;
        final toolbarHeight = kToolbarHeight;
        final collapseRatio =
            ((scrollOffset) / (expandedHeight - toolbarHeight)).clamp(0.0, 1.0);
        final isCollapsed = collapseRatio > 0.5;

        return SliverAppBar(
          expandedHeight: expandedHeight,
          pinned: true,
          backgroundColor: Theme.of(context).whitePrimary(context),
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios_outlined,
              color: isCollapsed
                  ? Colors.grey[700]
                  : Colors.white.withOpacity(0.9),
            ),
            onPressed: () => context.pop(),
          ),
          title: _buildSearchBar(context, isCollapsed, collapseRatio),
          actions: [_buildPopupMenu(context, isCollapsed, collapseRatio)],
          flexibleSpace: FlexibleSpaceBar(
            collapseMode: CollapseMode.parallax,
            background: Column(
              children: [
                RepaintBoundary(child: _buildHeaderImage(context)),
                Gap(16.h),
                _buildProfileContainer(context),
              ],
            ),
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(48.h),
            child: _buildTabBar(context),
          ),
        );
      },
    );
  }

  Widget _buildSearchBar(
      BuildContext context, bool isCollapsed, double collapseRatio) {
    return TextField(
      onTap: () {
        context.push(RouteName.searchSeller, extra: {'sellerId': sellerID});
      },
      readOnly: true,
      decoration: InputDecoration(
        hintText: 'Search services, products...',
        hintStyle: TextStyle(
          color: isCollapsed
              ? Colors.grey[600]
              : Colors.white.withOpacity(0.7 + (collapseRatio * 0.3)),
          fontSize: 14.sp,
        ),
        prefixIcon: Icon(
          Icons.search_rounded,
          color: isCollapsed
              ? Colors.grey[700]
              : Colors.white.withOpacity(0.8 + (collapseRatio * 0.2)),
          size: 20.w,
        ),
        border: InputBorder.none,
      ),
      style: TextStyle(
        fontSize: 14.sp,
        color: isCollapsed
            ? Colors.black87
            : Colors.white.withOpacity(0.9 + (collapseRatio * 0.1)),
      ),
    );
  }

  Widget _buildPopupMenu(
      BuildContext context, bool isCollapsed, double collapseRatio) {
    return PopupMenuButton(
      icon: Icon(
        Icons.more_vert,
        color: isCollapsed
            ? Colors.grey[700]
            : Colors.white.withOpacity(0.8 + (collapseRatio * 0.2)),
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'share',
          child: Row(
            children: [
              Icon(Icons.share_outlined, size: 20.w, color: Colors.red),
              const Gap(12),
              const Text('Share', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'report',
          child: Row(
            children: [
              Icon(Icons.flag_outlined, size: 20.w, color: Colors.red),
              const Gap(12),
              const Text('Report', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
      onSelected: (value) => print('$value selected'),
    );
  }

  Widget _buildHeaderImage(BuildContext context) {
    return BlocBuilder<ProfileSellerBloc, ProfileSellerState>(
      buildWhen: (p, c) => p.profileResponse != c.profileResponse,
      builder: (context, state) {
        String backgroundUrl =
            'https://cdn-media.sforum.vn/storage/app/media/anh-dep-8.jpg';
        if (state.profileResponse.status == Status.completed &&
            state.profileResponse.data?.user.backgroundPicture?.isNotEmpty ==
                true) {
          backgroundUrl = state.profileResponse.data!.user.backgroundPicture!;
        }
        return CustomImage(
          path: backgroundUrl,
          width: double.infinity,
          height: 180.h,
          fit: BoxFit.cover,
          imageType: ImageType.network,
        );
      },
    );
  }

  Widget _buildProfileContainer(BuildContext context) {
    return BlocBuilder<ProfileSellerBloc, ProfileSellerState>(
      buildWhen: (p, c) => p.profileResponse != c.profileResponse,
      builder: (context, state) {
        if (state.profileResponse.status == Status.loading) {
          return SizedBox(
              height: 120.h,
              child: const Center(child: CircularProgressIndicator()));
        }

        if (state.profileResponse.status == Status.completed) {
          return ContainerProfile(
            sellerID: sellerID,
            sellerProfile: state.profileResponse.data!,
          );
        }

        return ContainerProfile(sellerID: sellerID); // fallback
      },
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      color: Theme.of(context).whitePrimary(context),
      child: TabBar(
        controller: tabController,
        tabs: ProfileSellerTab.values
            .map((tab) => Tab(text: tab.name.capitalize()))
            .toList(),
        indicatorColor: Theme.of(context).primaryColor,
        labelColor: Theme.of(context).primaryColor,
        unselectedLabelColor: Theme.of(context).textTheme.bodyMedium?.color,
        indicatorWeight: 2.w,
      ),
    );
  }
}
