import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class CardShopProfileSeller extends StatelessWidget {
  const CardShopProfileSeller(
      {super.key,
      required this.firstName,
      required this.image,
      required this.description,
      required this.rating,
      required this.price});
  final String firstName, image, description;
  final double rating, price;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 176.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image section
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                ),
                child: Image.network(
                  image,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: 120.h,
                ),
              ),
              Positioned(
                top: 2.h,
                right: 2.w,
                child: IconButton(
                  onPressed: () {},
                  icon: Icon(
                    Icons.favorite_outlined,
                    color: Colors.black,
                    size: 20,
                  ),
                ),
              )
            ],
          ),
          // Content section
          Expanded(
            child: Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8.r),
                  bottomRight: Radius.circular(8.r),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: 'by ',
                          style:
                              Theme.of(context).textTheme.lightBodySmallMedium,
                        ),
                        TextSpan(
                          text: firstName,
                          style: Theme.of(context).textTheme.lightBodySmallBold,
                        )
                      ],
                    ),
                  ),
                  Gap(4.h),
                  Expanded(
                    child: Text(
                      description,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.lightBodyMediumBold,
                    ),
                  ),
                  Gap(4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        flex: 2,
                        child: Text(
                          '\$${price.toStringAsFixed(0)}',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumMedium
                              .copyWith(
                                  color: Theme.of(context).secondary(context)),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Flexible(
                        flex: 1,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.star,
                              color: Color(0xFF687588),
                              size: 12.w,
                            ),
                            Gap(2.w),
                            Text(
                              '${rating.toStringAsFixed(1)}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    color: Color(0xFF687588),
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Gap(4.h),
                  Text(
                    'From 2025 VN',
                    style: Theme.of(context).textTheme.lightBodySmallMedium,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
