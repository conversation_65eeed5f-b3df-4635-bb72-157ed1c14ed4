import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/data/enum/enum.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_state.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/generic_profile_sliver.dart';

import '../bloc/profile_view_home_event.dart';
import 'service_seller/service_card.dart';

class ServiceTab extends StatefulWidget {
  final int profileId;
  const ServiceTab({super.key, required this.profileId});

  @override
  State<ServiceTab> createState() => _ServiceTabState();
}

class _ServiceTabState extends State<ServiceTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  void _triggerLoadMore(BuildContext context) {
    final bloc = context.read<ProfileSellerBloc>();
    bloc.add(
        LoadMoreProfileSellerData(widget.profileId, ProfileSellerTab.service));
  }

  Future<void> _onRefresh(BuildContext context) async {
    final bloc = context.read<ProfileSellerBloc>();
    bloc.add(LoadProfileSellerData(widget.profileId, ProfileSellerTab.service));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocBuilder<ProfileSellerBloc, ProfileSellerState>(
      buildWhen: (prev, curr) =>
          prev.servicesResponse != curr.servicesResponse ||
          prev.servicePaged != curr.servicePaged,
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: () => _onRefresh(context),
          child: NotificationListener<ScrollNotification>(
            onNotification: (scrollInfo) {
              if (scrollInfo is ScrollEndNotification &&
                  scrollInfo.metrics.pixels >=
                      scrollInfo.metrics.maxScrollExtent - 300) {
                if (state.currentTab == ProfileSellerTab.service) {
                  _triggerLoadMore(context);
                }
              }
              return false;
            },
            child: CustomScrollView(
              key: const PageStorageKey('service_tab'),
              slivers: [
                ProfileTabSliverGrid(
                  title: 'No Services Available',
                  subtitle: 'This profile hasn\'t added any services yet.',
                  items: state.servicePaged.items,
                  response: state.servicesResponse,
                  hasMore: state.servicePaged.hasMore,
                  aspectRatio: 0.75,
                  onRetry: () => _onRefresh(context),
                  itemBuilder: (context, item) => ServiceCardSeller(item: item),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
