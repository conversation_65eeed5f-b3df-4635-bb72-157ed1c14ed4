import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/utils/formatters.dart';
import '../../../data/enum/enum.dart';
import '../bloc/profile_view_home_bloc.dart';
import '../bloc/profile_view_home_event.dart';
import '../bloc/profile_view_home_state.dart';
import 'card_blog_profile.dart';
import 'generic_profile_sliver.dart';

class BlogTab extends StatefulWidget {
  final int profileId;
  const BlogTab({super.key, required this.profileId});

  @override
  State<BlogTab> createState() => _BlogTabState();
}

class _BlogTabState extends State<BlogTab> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  void _triggerLoadMore(BuildContext context) {
    final bloc = context.read<ProfileSellerBloc>();
    bloc.add(
        LoadMoreProfileSellerData(widget.profileId, ProfileSellerTab.blog));
  }

  Future<void> _onRefresh(BuildContext context) async {
    final bloc = context.read<ProfileSellerBloc>();
    bloc.add(LoadProfileSellerData(widget.profileId, ProfileSellerTab.blog));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocBuilder<ProfileSellerBloc, ProfileSellerState>(
      buildWhen: (prev, curr) =>
          prev.blogsResponse != curr.blogsResponse ||
          prev.blogsPaged != curr.blogsPaged,
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: () => _onRefresh(context),
          child: NotificationListener<ScrollNotification>(
            onNotification: (scrollInfo) {
              if (scrollInfo is ScrollEndNotification &&
                  scrollInfo.metrics.pixels >=
                      scrollInfo.metrics.maxScrollExtent - 300) {
                if (state.currentTab == ProfileSellerTab.blog) {
                  _triggerLoadMore(context);
                }
              }
              return false;
            },
            child: CustomScrollView(
              key: const PageStorageKey('blog_tab'),
              slivers: [
                ProfileTabSliverGrid(
                  title: 'No Blog Available',
                  subtitle: 'This profile hasn\'t added any blog yet.',
                  items: state.blogsPaged.items,
                  response: state.blogsResponse,
                  hasMore: state.blogsPaged.hasMore,
                  aspectRatio: 0.60,
                  onRetry: () => _onRefresh(context),
                  itemBuilder: (context, item) => GestureDetector(
                    onTap: () => context.push(
                      RouteName.detailBlogPage,
                      extra: item,
                    ),
                    child: CardBlogProfileSeller(
                      firstName: item.authorName,
                      image: item.image.isNotEmpty ? item.image[0] : '',
                      description: TFormatter.htmlToFormattedText(item.content),
                      title: item.title,
                      category: item.categoryName,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
