import 'package:flutter/material.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/base/api_response/status.dart';

import 'service_loading_shimmer.dart';

typedef ItemBuilder = Widget Function(BuildContext context, dynamic item);

class ProfileTabSliverGrid extends StatelessWidget {
  const ProfileTabSliverGrid({
    super.key,
    required this.items,
    required this.response,
    required this.hasMore,
    required this.onRetry,
    required this.itemBuilder,
    this.aspectRatio = 0.75,
    this.crossAxisSpacing = 15,
    this.mainAxisSpacing = 15,
    this.padding = const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
    required this.title,
    required this.subtitle,
  });

  final List<dynamic> items;
  final ApiResponse response;
  final bool hasMore;
  final VoidCallback onRetry;
  final ItemBuilder itemBuilder;
  final double aspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final EdgeInsets padding;
  final String title, subtitle;

  @override
  Widget build(BuildContext context) {
    // Nếu có data, hiển thị ngay
    if (items.isNotEmpty) {
      return _buildGrid(items);
    }

    // Xử lý các trạng thái khác
    if (response.status == Status.loading) {
      return const ServiceGridLoadingShimmer(itemCount: 6);
    }

    if (response.status == Status.error) {
      return ErrorServiceState(
        message: response.message,
        onRetry: onRetry,
      );
    }

    return response.when(
      loading: () => const ServiceGridLoadingShimmer(itemCount: 6),
      error: (msg) => ErrorServiceState(
        message: msg,
        onRetry: onRetry,
      ),
      completed: (items) {
        if (items.isEmpty) {
          return EmptyServiceState(
            title: title,
            subtitle: subtitle,
          );
        }
        return _buildGrid(items);
      },
    );
  }

  Widget _buildGrid(List<dynamic> items) {
    return SliverList(
      delegate: SliverChildListDelegate([
        Padding(
          padding: padding,
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: aspectRatio,
              crossAxisSpacing: crossAxisSpacing,
              mainAxisSpacing: mainAxisSpacing,
            ),
            itemCount: items.length,
            itemBuilder: (context, index) => itemBuilder(context, items[index]),
          ),
        ),
        if (hasMore)
          Padding(
            padding: padding,
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: aspectRatio,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: 2,
              itemBuilder: (context, index) => const ServiceLoadingShimmer(),
            ),
          ),
      ]),
    );
  }
}
