import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ProfileBlog extends StatefulWidget {
  const ProfileBlog({super.key, required this.firstName, required this.image, required this.description,required this.category,required this.title});
  final String firstName,image,title,description, category;

  @override
  State<ProfileBlog> createState() => _ProfileBlogState();
}

class _ProfileBlogState extends State<ProfileBlog> {
  List<dynamic> items = [];
  int currentPage = 1;
  final int limit = 10;
  bool isLoading = false;
  bool hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  // Load initial data (page 1) - start with 10 items
  Future<void> _loadInitialData() async {
    setState(() {
      isLoading = true;
    });

    await _fetchData(page: 1, limit: limit);

    setState(() {
      isLoading = false;
    });
  }

  // Load more data when "See more" is pressed
  Future<void> _loadMoreItems() async {
    if (isLoading || !hasMoreData) return;

    setState(() {
      isLoading = true;
    });

    await _fetchData(page: currentPage + 1, limit: limit);

    setState(() {
      isLoading = false;
    });
  }

  // API call function (to be implemented later)
  Future<void> _fetchData({required int page, required int limit}) async {
    try {
      // Simulate API response - generate 10 items per page
      await Future.delayed(Duration(milliseconds: 500));
      final newItems = List.generate(limit, (index) => {
        'id': (page - 1) * limit + index + 1,
        'firstName': widget.firstName,
        'image': widget.image,
        'description': '${widget.description} ${(page - 1) * limit + index + 1}',
        'title': '${widget.title} ${(page - 1) * limit + index + 1}',
        'category': widget.category,
      });

      setState(() {
        if (page == 1) {
          items = newItems; // Initial load - replace with first 10 items
        } else {
          items.addAll(newItems); // Add next 10 items to existing list
        }
        currentPage = page;

        // Simulate having max 50 items total (adjust based on your needs)
        hasMoreData = items.length < 50;
      });

    } catch (error) {
      print('Error loading data: $error');
      // Show error message to user
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 12.w,
        vertical: 14.h,
      ),
      child: Column(
        children: [
          Text(
            textAlign: TextAlign.center,
            maxLines: 3,
            'This profile is still setting up.\nBrowse similar services from our connection platform',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 32),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Divider(
                  color: Colors.black,
                  height: 1,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Text('YOU MAY LIKE', style: Theme.of(context).textTheme.bodyMedium),
              ),
              Expanded(
                child: Divider(
                  color: Colors.black,
                  height: 1,
                ),
              ),
            ],
          ),
          Gap(10.h),
          // Show loading indicator during initial load
          if (isLoading && items.isEmpty)
            Padding(
              padding: EdgeInsets.symmetric(vertical: 50.h),
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).secondary(context),
                ),
              ),
            )
          else
            GridView.builder(
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 0.62,
              ),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                return SizedBox(
                  width: 176.w,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Image section
                      Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8.r),
                              topRight: Radius.circular(8.r),
                            ),
                            child: Image.network(
                              item['image'] ?? widget.image,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: 120.h,
                            ),
                          ),
                          Positioned(
                            top: 2.h,
                            right: 2.w,
                            child: IconButton(
                              onPressed: () {},
                              icon: Icon(
                                Icons.favorite_outlined,
                                color: Colors.black,
                                size: 20,
                              ),
                            ),
                          )
                        ],
                      ),
                      // Content section
                      Container(
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(8.r),
                            bottomRight: Radius.circular(8.r),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            RichText(
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'by ',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodySmallMedium,
                                  ),
                                  TextSpan(
                                    text: item['firstName'] ?? widget.firstName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodySmallBold,
                                  )
                                ],
                              ),
                            ),
                            Gap(8.h),
                            Text(
                              item['title'] ?? widget.title,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.lightBodyMediumBold,
                            ),
                            Gap(8.h),

                            Text(
                              item['description'] ?? widget.description,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.lightBodyLargeMedium,
                            ),
                            Gap(8.h),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: const Color(0xFFD8F6E5), // light green
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                item['category'] ?? widget.category,
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 13,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          // See More Button
          if (hasMoreData && items.isNotEmpty) ...[
            Gap(16.h),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: isLoading ? null : _loadMoreItems,
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  side: BorderSide(
                    color: Theme.of(context).secondary(context),
                    width: 1.5,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: isLoading
                    ? SizedBox(
                  height: 20.h,
                  width: 20.w,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).secondary(context),
                    ),
                  ),
                )
                    : Text(
                  'See more',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).secondary(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}