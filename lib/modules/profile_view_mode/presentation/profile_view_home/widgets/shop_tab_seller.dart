import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/card_shop_profile.dart';

import '../../../../../app/routers/routers_name.dart';
import '../../../../../core/utils/formatters.dart';
import '../../../data/enum/enum.dart';
import '../bloc/profile_view_home_event.dart';
import '../bloc/profile_view_home_state.dart';
import 'generic_profile_sliver.dart';

class ShopTab extends StatefulWidget {
  final int profileId;
  const ShopTab({super.key, required this.profileId});

  @override
  State<ShopTab> createState() => _ShopTabState();
}

class _ShopTabState extends State<ShopTab> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  void _triggerLoadMore(BuildContext context) {
    final bloc = context.read<ProfileSellerBloc>();
    bloc.add(
        LoadMoreProfileSellerData(widget.profileId, ProfileSellerTab.shop));
  }

  Future<void> _onRefresh(BuildContext context) async {
    final bloc = context.read<ProfileSellerBloc>();
    bloc.add(LoadProfileSellerData(widget.profileId, ProfileSellerTab.shop));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocBuilder<ProfileSellerBloc, ProfileSellerState>(
      buildWhen: (prev, curr) =>
          prev.shopResponse != curr.shopResponse ||
          prev.shopPaged != curr.shopPaged,
      builder: (context, state) {
        return RefreshIndicator(
          onRefresh: () => _onRefresh(context),
          child: NotificationListener<ScrollNotification>(
            onNotification: (scrollInfo) {
              if (scrollInfo is ScrollEndNotification &&
                  scrollInfo.metrics.pixels >=
                      scrollInfo.metrics.maxScrollExtent - 300) {
                if (state.currentTab == ProfileSellerTab.shop) {
                  _triggerLoadMore(context);
                }
              }
              return false;
            },
            child: CustomScrollView(
              key: const PageStorageKey('shop_tab'),
              slivers: [
                ProfileTabSliverGrid(
                  title: 'No Shop Available',
                  subtitle: 'This profile hasn\'t added any shops yet.',
                  items: state.shopPaged.items,
                  response: state.shopResponse,
                  hasMore: state.shopPaged.hasMore,
                  aspectRatio: 0.65,
                  onRetry: () => _onRefresh(context),
                  itemBuilder: (context, item) => GestureDetector(
                    onTap: () {
                      context.push(RouteName.productDetailSeller,
                          extra: item.id);
                    },
                    child: CardShopProfileSeller(
                      firstName: item.name,
                      image: item.image.isNotEmpty ? item.image[0] : '',
                      description:
                          TFormatter.htmlToFormattedText(item.description),
                      rating: item.rating,
                      price: item.pricingTiers.isNotEmpty
                          ? item.pricingTiers[0].price
                          : 0.0,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
