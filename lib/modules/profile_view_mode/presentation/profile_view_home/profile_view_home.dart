import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/data/enum/enum.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_event.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_state.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/profile_view_header_sliver.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/review_tab_seller.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/widgets/service_tab_seller.dart';

import 'widgets/blog_tab_seller.dart';
import 'widgets/shop_tab_seller.dart';

class ProfileViewHome extends StatefulWidget {
  final int profileId;

  const ProfileViewHome({super.key, required this.profileId});

  @override
  State<ProfileViewHome> createState() => _ProfileViewHomeState();
}

class _ProfileViewHomeState extends State<ProfileViewHome>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController =
        TabController(length: ProfileSellerTab.values.length, vsync: this);

    // Listen to tab changes from both tap and swipe
    _tabController.addListener(_onTabChanged);

    context
        .read<ProfileSellerBloc>()
        .add(LoadProfileSellerData(widget.profileId, ProfileSellerTab.service));
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging ||
        _tabController.index != _tabController.previousIndex) {
      final tab = ProfileSellerTab.values[_tabController.index];
      context
          .read<ProfileSellerBloc>()
          .add(TabProfileSellerChanged(tab, widget.profileId));
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<ProfileSellerBloc, ProfileSellerState>(
        builder: (context, state) {
          // Sync TabController với state
          if (_tabController.index !=
              ProfileSellerTab.values.indexOf(state.currentTab)) {
            _tabController
                .animateTo(ProfileSellerTab.values.indexOf(state.currentTab));
          }

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                ProfileViewHeaderSliver(
                  tabController: _tabController,
                  sellerID: widget.profileId,
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: ProfileSellerTab.values.map((tab) {
                return _buildTabContent(state, tab);
              }).toList(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabContent(ProfileSellerState state, ProfileSellerTab tab) {
    switch (tab) {
      case ProfileSellerTab.service:
        return ServiceTab(profileId: widget.profileId);
      case ProfileSellerTab.shop:
        return ShopTab(profileId: widget.profileId);
      case ProfileSellerTab.blog:
        return BlogTab(profileId: widget.profileId);

      case ProfileSellerTab.review:
        return ReviewTab();
    }
  }
}
