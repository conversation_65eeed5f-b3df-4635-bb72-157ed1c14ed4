part of 'detail_product_seller_bloc.dart';

class DetailProductSellerEvent extends Equatable {
  const DetailProductSellerEvent();
  @override
  List<Object?> get props => [];
}

class FetchDetailProductSeller extends DetailProductSellerEvent {
  final int productId;

  const FetchDetailProductSeller({required this.productId});

  @override
  List<Object?> get props => [productId];
}

class FetchProductLike extends DetailProductSellerEvent {
  final int categoryId;

  const FetchProductLike({
    required this.categoryId,
  });

  @override
  List<Object?> get props => [categoryId];
}

class FetchProductTrending extends DetailProductSellerEvent {
  const FetchProductTrending();

  @override
  List<Object?> get props => [];
}
