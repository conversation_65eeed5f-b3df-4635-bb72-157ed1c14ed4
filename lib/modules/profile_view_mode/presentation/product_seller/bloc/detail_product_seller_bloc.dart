import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/modules/profile_view_mode/data/models/product_seller_model.dart';

import '../../../../strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import '../../../../strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';
import '../../../data/repositories/profile_seller_repository.dart';

part 'detail_product_seller_event.dart';
part 'detail_product_seller_state.dart';

class DetailProductSellerBloc
    extends Bloc<DetailProductSellerEvent, DetailProductSellerState> {
  final ProfileSellerRepoRemote profileSellerRepoRemote;
  final HomeStrongbodyAiRepository homeStrongbodyAiRepository;
  CancelToken? _cancelToken;

  DetailProductSellerBloc(
      {required this.profileSellerRepoRemote,
      required this.homeStrongbodyAiRepository})
      : super(DetailProductSellerState.initial()) {
    on<FetchDetailProductSeller>(_onFetchDetailProductSeller);
    on<FetchProductLike>(_onFetchProductLike);
    on<FetchProductTrending>(_onFetchProductTrending);
  }

  Future<void> _onFetchDetailProductSeller(FetchDetailProductSeller event,
      Emitter<DetailProductSellerState> emit) async {
    emit(state.copyWith(product: ApiResponse.loading()));
    try {
      _cancelToken?.cancel();
      _cancelToken = CancelToken();
      final response = await profileSellerRepoRemote.getProductSellerById(
          event.productId, _cancelToken!);
      emit(state.copyWith(product: response));
    } catch (e) {
      emit(state.copyWith(product: ApiResponse.error(e.toString())));
    }
  }

  Future<void> _onFetchProductLike(
      FetchProductLike event, Emitter<DetailProductSellerState> emit) async {
    emit(state.copyWith(productLike: ApiResponse.loading()));
    try {
      final param = {
        'category_id': event.categoryId,
      };
      final response = await homeStrongbodyAiRepository.getServiceByQuery(
          param: param, 'like', resourceType: 'product');
      final result = response.data!.data.list
          .map((e) => ProductModel.fromJson(e))
          .toList();
      emit(state.copyWith(
          productLike: result.isNotEmpty
              ? ApiResponse.completed(result)
              : ApiResponse.error('No data found')));
    } catch (e) {
      emit(state.copyWith(productLike: ApiResponse.error(e.toString())));
    }
  }

  Future<void> _onFetchProductTrending(FetchProductTrending event,
      Emitter<DetailProductSellerState> emit) async {
    emit(state.copyWith(productTrending: ApiResponse.loading()));
    try {
      final response = await homeStrongbodyAiRepository
          .getServiceByQuery('trending', resourceType: 'product');
      final result = response.data!.data.list
          .map((e) => ProductModel.fromJson(e))
          .toList();
      emit(state.copyWith(
          productTrending: result.isNotEmpty
              ? ApiResponse.completed(result)
              : ApiResponse.error('No data found')));
    } catch (e) {
      emit(state.copyWith(productTrending: ApiResponse.error(e.toString())));
    }
  }

  @override
  Future<void> close() {
    _cancelToken?.cancel();
    return super.close();
  }
}
