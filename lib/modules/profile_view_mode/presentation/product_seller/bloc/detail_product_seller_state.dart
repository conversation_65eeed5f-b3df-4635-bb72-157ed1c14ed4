part of 'detail_product_seller_bloc.dart';

class DetailProductSellerState extends Equatable {
  final ApiResponse<ProductModel> product;
  final ApiResponse<List<ProductModel>> productLike;
  final ApiResponse<List<ProductModel>> productTrending;

  const DetailProductSellerState(
      {required this.product,
      required this.productLike,
      required this.productTrending});
  factory DetailProductSellerState.initial() {
    return DetailProductSellerState(
      product: ApiResponse.loading(),
      productLike: ApiResponse.loading(),
      productTrending: ApiResponse.loading(),
    );
  }
  DetailProductSellerState copyWith({
    ApiResponse<ProductModel>? product,
    ApiResponse<List<ProductModel>>? productLike,
    ApiResponse<List<ProductModel>>? productTrending,
  }) {
    return DetailProductSellerState(
      product: product ?? this.product,
      productLike: productLike ?? this.productLike,
      productTrending: productTrending ?? this.productTrending,
    );
  }

  @override
  List<Object?> get props => [product, productLike, productTrending];
}
