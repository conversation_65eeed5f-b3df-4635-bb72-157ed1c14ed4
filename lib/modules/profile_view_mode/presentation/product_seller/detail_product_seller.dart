import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/product_seller/bloc/bottom_detail_product_seller.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/product_seller/bloc/detail_product_seller_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/widgets/listview_blog_trending.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/custom_appbar_scrollview_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/detail_document_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/widgets/detail_shipping_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_customer_review.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_feature_product.dart';

import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../../core/base/api_response/status.dart';
import '../../../../core/components/loading_over_lay.dart';
import 'widgets/detail_product_review_seller.dart';
import 'widgets/detail_product_seller_contact.dart';
import 'widgets/detail_product_seller_info.dart';

class DetailProductSeller extends StatefulWidget {
  const DetailProductSeller({super.key});

  @override
  State<DetailProductSeller> createState() => _DetailProductSellerState();
}

class _DetailProductSellerState extends State<DetailProductSeller>
    with SingleTickerProviderStateMixin {
  int _current = 0;
  bool isCollapsed = false;
  late AutoScrollController scrollController;
  late TabController tabController;
  bool pauseRectGetterIndex = false;
  final double expandedHeight = 270.0;

  static const int sectionCount = 3;
  final List<GlobalKey> sectionKeys = List.generate(3, (_) => GlobalKey());

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: sectionCount, vsync: this);
    scrollController = AutoScrollController();
    scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    final double threshold = expandedHeight -
        kToolbarHeight -
        MediaQuery.of(context).padding.top -
        10;
    bool shouldCollapse =
        scrollController.hasClients && scrollController.offset > threshold;
    if (isCollapsed != shouldCollapse) {
      setState(() {
        isCollapsed = shouldCollapse;
      });
    }

    if (!pauseRectGetterIndex) {
      for (int i = sectionKeys.length - 1; i >= 0; i--) {
        final keyContext = sectionKeys[i].currentContext;
        if (keyContext != null) {
          final box = keyContext.findRenderObject();
          if (box is RenderBox) {
            final offset = box.localToGlobal(Offset.zero,
                ancestor: context.findRenderObject());
            if (offset.dy <= kToolbarHeight + 60) {
              if (tabController.index != i) {
                tabController.animateTo(i);
              }
              break;
            }
          }
        }
      }
    }
  }

  void animateAndScrollTo(int index) async {
    pauseRectGetterIndex = true;
    tabController.animateTo(index);
    await scrollController.scrollToIndex(index,
        preferPosition: AutoScrollPosition.begin);
    pauseRectGetterIndex = false;
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DetailProductSellerBloc, DetailProductSellerState>(
      builder: (context, state) {
        final product = state.product;
        final productLike = state.productLike;
        final productTrending = state.productTrending;

        // Show loading when product is being fetched
        if (state.product.status == Status.loading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        if (state.product.status == Status.error) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.product.message ?? 'An error occurred',
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        return LoadingOverlay(
          isLoading: state.product.status == Status.loading,
          child: Scaffold(
            bottomNavigationBar: BottomDetailProductSeller(
              productModel: product.data!,
            ),
            body: CustomScrollView(
              controller: scrollController,
              slivers: [
                CustomAppbarScrollviewProduct(
                  tabController: tabController,
                  onTabTap: animateAndScrollTo,
                  isCollapsed: isCollapsed,
                  expandedHeight: expandedHeight,
                  images: product.data?.image ?? [],
                  currentImage: _current,
                  onCarouselChanged: (index) {
                    setState(() {
                      _current = index;
                    });
                  },
                ),
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSpacing.padding16,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Gap(16.h),
                            AutoScrollTag(
                              key: const ValueKey(0),
                              controller: scrollController,
                              index: 0,
                              child: Container(
                                key: sectionKeys[0],
                                child: DetailInfoProductSeller(
                                    productModel: product.data!),
                              ),
                            ),
                            Gap(16.h),
                            const DetailShippingProduct(),
                            Gap(16.h),
                            DetailContactProductSeller(
                                productModel: product.data!),
                            Gap(16.h),
                            AutoScrollTag(
                              key: const ValueKey(1),
                              controller: scrollController,
                              index: 1,
                              child: Container(
                                key: sectionKeys[1],
                                child: DetailReviewProductSeller(
                                    productModel: product.data!),
                              ),
                            ),
                            Gap(16.h),
                            const DetailDocumentProduct(),
                            Gap(16.h),
                            AutoScrollTag(
                              key: const ValueKey(2),
                              controller: scrollController,
                              index: 2,
                              child: Container(
                                key: sectionKeys[2],
                                child: const DetailCustomerReview(),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Gap(16.h),
                      if (productLike.data?.isNotEmpty ?? false)
                        ListViewGeneric<ProductModel>(
                          isLoading: state.productLike.status == Status.loading,
                          items: productLike.data?.toList() ?? [],
                          onItemTap: (product) => context.push(
                              RouteName.detailProductPage,
                              extra: product),
                          showMore: true,
                          title: 'Maybe you like',
                          itemBuilder: (context, product, index) =>
                              CardFeatureProduct(product: product),
                        ),
                      Gap(16.h),
                      if (productTrending.data?.isNotEmpty ?? false)
                        ListViewGeneric<ProductModel>(
                          isLoading:
                              state.productTrending.status == Status.loading,
                          items: productTrending.data?.toList() ?? [],
                          onItemTap: (product) => context.push(
                              RouteName.detailProductPage,
                              extra: product),
                          showMore: true,
                          title: 'Trending',
                          itemBuilder: (context, product, index) =>
                              CardFeatureProduct(product: product),
                        ),
                      Gap(32.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
