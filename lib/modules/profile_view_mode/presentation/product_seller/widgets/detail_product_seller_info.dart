import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import '../../../data/models/product_seller_model.dart';

class DetailInfoProductSeller extends StatefulWidget {
  const DetailInfoProductSeller({super.key, required this.productModel});
  final ProductModel productModel;

  @override
  State<DetailInfoProductSeller> createState() =>
      _DetailInfoProductSellerState();
}

class _DetailInfoProductSellerState extends State<DetailInfoProductSeller> {
  late List<String> units;
  late String selectedUnit;
  late int randomViews;

  @override
  void initState() {
    super.initState();
    randomViews = Random().nextInt(1000) + 50;
    final primaryPricingTier = widget.productModel.pricingTiers;
    units = primaryPricingTier.map((e) => e.unit).toSet().toList();
    selectedUnit = units.first;
  }

  @override
  Widget build(BuildContext context) {
    final primaryPricingTier = widget.productModel.pricingTiers;
    final currentPricing = primaryPricingTier.firstWhere(
      (tier) => tier.unit == selectedUnit,
      orElse: () => primaryPricingTier.first,
    );

    Widget buildRickText(String title, String content) {
      return RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: '$title  ',
              style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color: Theme.of(context).greyScale500(context),
                  ),
            ),
            TextSpan(
              text: '  $content',
              style:
                  Theme.of(context).textTheme.lightBodyLargeSemiBold.copyWith(
                        color: Theme.of(context).secondary(context),
                      ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).alertAttentionBase(context),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${widget.productModel.name}',
              style: Theme.of(context).textTheme.lightHeadingMedium),
          Gap(16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              IconLabel(context, AppAssets.eyeSvg, '$randomViews views'),
              IconLabel(context, AppAssets.share_newSvg, 'Copy link'),
              IconLabel(context, AppAssets.heart_icon, 'Like'),
            ],
          ),
          Gap(16.h),
          Text(
            '\$${currentPricing.price.toStringAsFixed(0)}',
            style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(
                  color: Colors.red.shade700,
                ),
          ),
          if (currentPricing.description.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 4.h),
              child: Text(
                currentPricing.description,
                style:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Theme.of(context).greyScale500(context),
                        ),
              ),
            ),
          Gap(10.h),
          SizedBox(
            height: 48.0,
            child: ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              scrollDirection: Axis.horizontal,
              itemCount: units.length,
              separatorBuilder: (_, __) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                final unit = units[index];
                final isSelected = unit == selectedUnit;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedUnit = unit;
                    });
                  },
                  child: Container(
                    width: 90.w,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 10),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.red : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? Colors.red : Colors.grey,
                        width: 1.2,
                      ),
                    ),
                    child: Text(
                      unit,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Gap(10.h),
          buildRickText('Brand', 'Curr'),
          Gap(10.h),

          buildRickText('Category', '${widget.productModel.category?.name}'),
          Gap(10.h),
          buildRickText(
              'Country of Origin', '${widget.productModel.countryOfOrigin}'),
          Gap(10.h),
          Text('Product certifications',
              style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                    color: Theme.of(context).greyScale600(context),
                  )),
          Gap(10.h),
          Row(
            children: [
              Column(
                children: [
                  SvgPicture.asset(
                    AppAssets.rcsSvg,
                  ),
                  Text('Picture.png',
                      style:
                          Theme.of(context).textTheme.lightBodyMediumSemiBold),
                ],
              ),
              Gap(10.w),
              Column(
                children: [
                  SvgPicture.asset(
                    AppAssets.fileCertificationSvg,
                  ),
                  Text('file.pdf',
                      style:
                          Theme.of(context).textTheme.lightBodyMediumSemiBold),
                ],
              )
            ],
          ),
          // ReadMoreText(
          //   TFormatter.htmlToFormattedText(productModel.description),
          //   trimLines: 3,
          //   colorClickableText: Theme.of(context).alertInformationBase(context),
          //   trimMode: TrimMode.Line,
          //   trimCollapsedText: 'more',
          //   trimExpandedText: 'less',
          //   style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
          // ),
          Gap(16.h)
        ],
      ),
    );
  }

  Widget IconLabel(BuildContext context, String icon, String label) {
    return Row(
      children: [
        SvgPicture.asset(icon, color: Theme.of(context).greyScale600(context)),
        Gap(10.w),
        Text(label,
            style: Theme.of(context)
                .textTheme
                .lightBodyMediumMedium
                .copyWith(color: Theme.of(context).greyScale600(context))),
      ],
    );
  }
}
