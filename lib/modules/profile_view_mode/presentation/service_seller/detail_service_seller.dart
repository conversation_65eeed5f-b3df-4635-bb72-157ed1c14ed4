import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/bloc/detail_service_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/bottom_detail_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/custom_appbar_scrollview.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_description_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_contact_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_customer_review.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_faq_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_review_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/widgets/detail_service_package.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_suggest_service.dart';
import 'package:multime_app/shared/widgets/list_card/List_view_generic.dart';
import 'package:readmore/readmore.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

import '../../../../core/components/loading_over_lay.dart';
import 'widgets/detail_service_shimmer.dart';

class DetailServiceSeller extends StatefulWidget {
  const DetailServiceSeller({
    super.key,
  });

  @override
  State<DetailServiceSeller> createState() => _DetailServiceSellerState();
}

class _DetailServiceSellerState extends State<DetailServiceSeller>
    with SingleTickerProviderStateMixin {
  int _current = 0;
  bool isCollapsed = false;
  late AutoScrollController scrollController;
  late TabController tabController;
  bool pauseRectGetterIndex = false;
  final double expandedHeight = 270.0;

  static const int sectionCount = 4;
  final List<GlobalKey> sectionKeys = List.generate(4, (_) => GlobalKey());

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: sectionCount, vsync: this);
    scrollController = AutoScrollController();
    scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    final double threshold = expandedHeight -
        kToolbarHeight -
        MediaQuery.of(context).padding.top -
        10;
    bool shouldCollapse =
        scrollController.hasClients && scrollController.offset > threshold;
    if (isCollapsed != shouldCollapse) {
      setState(() {
        isCollapsed = shouldCollapse;
      });
    }

    if (!pauseRectGetterIndex) {
      for (int i = sectionKeys.length - 1; i >= 0; i--) {
        final keyContext = sectionKeys[i].currentContext;
        if (keyContext != null) {
          final box = keyContext.findRenderObject();
          if (box is RenderBox) {
            final offset = box.localToGlobal(Offset.zero,
                ancestor: context.findRenderObject());
            if (offset.dy <= kToolbarHeight + 60) {
              if (tabController.index != i) {
                tabController.animateTo(i);
              }
              break;
            }
          }
        }
      }
    }
  }

  void animateAndScrollTo(int index) async {
    pauseRectGetterIndex = true;
    tabController.animateTo(index);
    await scrollController.scrollToIndex(index,
        preferPosition: AutoScrollPosition.begin);
    pauseRectGetterIndex = false;
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DetailServiceBloc, DetailServiceState>(
      builder: (context, state) {
        final serviceLike = state.serviceLike;
        final serviceTrending = state.serviceTrending;

        if (state.errorMessage != null) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64.h,
                    color: Colors.red[400],
                  ),
                  Gap(16.h),
                  Text(
                    'Something went wrong',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  Gap(8.h),
                  Text(
                    state.errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                    textAlign: TextAlign.center,
                  ),
                  Gap(24.h),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        // Show shimmer loading when service detail is loading
        if (state.isLoadingDetail || state.service == null) {
          return const DetailServiceShimmer();
        }

        return LoadingOverlay(
          isLoading:
              state.isLoadingServiceLike || state.isLoadingServiceTrending,
          child: Scaffold(
            bottomNavigationBar: BottomDetailService(),
            body: CustomScrollView(
              controller: scrollController,
              slivers: [
                CustomAppbarScrollview(
                  sellerId: state.service!.user?.id,
                  sellerName: state.service!.shop?.shopName,
                  sellerAvatar: state.service!.user?.backgroundPicture,
                  sellerIsOnline: true,
                  tabController: tabController,
                  onTabTap: animateAndScrollTo,
                  isCollapsed: isCollapsed,
                  expandedHeight: expandedHeight,
                  images: state.service!.image,
                  currentImage: _current,
                  onCarouselChanged: (index) {
                    setState(() {
                      _current = index;
                    });
                  },
                ),
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('${state.service!.title}',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightHeadingMedium),
                            Gap(10.h),
                            Row(
                              children: [
                                SvgPicture.asset(AppAssets.eyeSvg),
                                Gap(4.w),
                                Text('${state.service!.views} views',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodySmallMedium),
                              ],
                            ),
                            Gap(10.h),
                            Text(
                              '\$ ${state.service!.price}',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightHeadingMedium
                                  .copyWith(
                                    color: Theme.of(context)
                                        .alertInformationBase(context),
                                  ),
                            ),
                            Gap(10.h),
                            ReadMoreText(
                              TFormatter.htmlToFormattedText(
                                  state.service!.description),
                              trimLines: 3,
                              colorClickableText: Theme.of(context)
                                  .alertInformationBase(context),
                              trimMode: TrimMode.Line,
                              trimCollapsedText: 'more',
                              trimExpandedText: 'less',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeSemiBold,
                            ),
                            Gap(16.h),
                            DetailContactService(serviceModel: state.service!),
                            Gap(16.h),
                            AutoScrollTag(
                              key: ValueKey(0),
                              controller: scrollController,
                              index: 0,
                              child: Container(
                                key: sectionKeys[0],
                                child: DetailServicePackage(),
                              ),
                            ),
                            Gap(16.h),
                            AutoScrollTag(
                              key: ValueKey(1),
                              controller: scrollController,
                              index: 1,
                              child: Container(
                                key: sectionKeys[1],
                                child: DetailReviewService(
                                    serviceModel: state.service!),
                              ),
                            ),
                            Gap(16.h),
                            AutoScrollTag(
                              key: ValueKey(2),
                              controller: scrollController,
                              index: 2,
                              child: Container(
                                key: sectionKeys[2],
                                child: DetailDescriptionService(
                                    serviceModel: state.service!),
                              ),
                            ),
                            Gap(16.h),
                            AutoScrollTag(
                              key: ValueKey(3),
                              controller: scrollController,
                              index: 3,
                              child: Container(
                                key: sectionKeys[3],
                                child: DetailFAQService(),
                              ),
                            ),
                            Gap(16.h),
                            DetailCustomerReview(),
                          ],
                        ),
                      ),
                      Gap(16.h),
                      ListViewGeneric<ServiceModel>(
                        isLoading: state.isLoadingServiceLike,
                        items: serviceLike,
                        onItemTap: (service) => context.push(
                          RouteName.serviceDetail,
                          extra: service,
                        ),
                        showMore: true,
                        title: 'May you Like',
                        itemBuilder: (context, service, index) =>
                            CardSuggestService(service: service),
                        isShowButton: false,
                        isListHome: false,
                      ),
                      Gap(16.h),
                      ListViewGeneric<ServiceModel>(
                        isLoading: state.isLoadingServiceTrending,
                        items: serviceTrending,
                        onItemTap: (service) => context.push(
                          RouteName.serviceDetail,
                          extra: service,
                        ),
                        showMore: true,
                        title: 'Trending',
                        itemBuilder: (context, service, index) =>
                            CardSuggestService(service: service),
                        isShowButton: false,
                        isListHome: false,
                      ),
                      Gap(32.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
