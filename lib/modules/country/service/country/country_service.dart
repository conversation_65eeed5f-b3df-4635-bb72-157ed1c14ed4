import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/country/model/country/country.dart';

class CountryService {
  final ApiClient apiClient;

  CountryService({required this.apiClient});

  Future<List<Country>> getAllCountries(String token) async {
    List<Country> allCountries = [];
    int page = 1;
    int totalPages = 1;

    try {
      do {
        final response = await apiClient.request(
          path: '${ApiConst.getCoutries}?page=$page&limit=10',
          method: ApiType.get,
          headers: {
            'Scope': '${AppConstants.multi_me}',
            'x-api-key': 'your_api_key'
          },
        );

        final data = response['data'];
        final List<dynamic> countryData = data['data'];
        totalPages = data['total_page'];

        final List<Country> countries =
            countryData.map((e) => Country.fromJson(e)).toList();
        allCountries.addAll(countries);

        page++;
      } while (page <= totalPages);

      return allCountries;
    } catch (e) {
      rethrow;
    }
  }
}
