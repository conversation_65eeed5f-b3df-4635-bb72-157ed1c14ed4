import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/modules/country/service/country/country_service.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
part 'country_event.dart';
part 'country_state.dart';

class CountryBloc extends Bloc<CountryEvent, CountryState> {
  final CountryService countryService;
  final GlobalStorage globalStorage;

  CountryBloc({required this.countryService, required this.globalStorage})
      : super(CountryState.initial()) {
    on<FetchCountriesEvent>(_onFetchCountriesEvent);
  }

  Future<void> _onFetchCountriesEvent(
      FetchCountriesEvent event, Emitter<CountryState> emit) async {
    emit(state.copyWith(
        isLoading: true, error: null, errowrMessage: '', isLoaded: false));
    print('================================');
    try {
      // final token = globalStorage.accessToken!;

      print('Fetching countries...');
      const token =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicGVybWlzc2lvbnMiOiJTdXBlciIsImlzc3VlZEF0IjoiMjAyNS0wNi0yN1QwMjowMTozMS4xMzM3MTc1MzFaIiwiZXhwIjo1MzUwOTg5NjkxfQ.XRH6msEHHz8yfXpo12hD4HMX8jU1QoIvhR-p1OITbMU';
      final countries = await countryService.getAllCountries(token);
      globalStorage.setCountries(countries);
      emit(state.copyWith(
        isLoading: false,
        isLoaded: true,
        countries: countries,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errowrMessage: 'Failed to fetch countries: $e',
      ));
    }
  }
}
