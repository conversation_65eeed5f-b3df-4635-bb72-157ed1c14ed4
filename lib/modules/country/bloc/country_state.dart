part of 'country_bloc.dart';

class CountryState {
  final bool isLoading;
  final String? error;
  final List<Country> countries;
  final bool isLoaded;
  final bool isRefreshing;
  final String errowrMessage;

  CountryState({
    this.isLoading = false,
    this.error,
    this.countries = const [],
    this.isLoaded = false,
    this.isRefreshing = false,
    this.errowrMessage = '',
  });

  factory CountryState.initial() => CountryState();

  CountryState copyWith({
    bool? isLoading,
    String? error,
    List<Country>? countries,
    bool? isLoaded,
    bool? isRefreshing,
    String? errowrMessage,
  }) {
    return CountryState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      countries: countries ?? this.countries,
      isLoaded: isLoaded ?? this.isLoaded,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      errowrMessage: errowrMessage ?? this.errowrMessage,
    );
  }
}
