class Country {
  final int? id;
  final String? createdAt;
  final String? updatedAt;
  final String? deletedAt;
  final int? createdBy;
  final int? updatedBy;
  final String? title;
  final String? code;
  final String? description;
  final String? coverImage;
  final String? flag;
  final String? phoneCode;
  final int? status;

  Country({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.title,
    this.code,
    this.description,
    this.coverImage,
    this.flag,
    this.phoneCode,
    this.status,
  });

  factory Country.fromJson(Map<String, dynamic>? json) {
    if (json == null) return Country();

    return Country(
      id: _parseIntSafely(json['id']),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at']?.toString(),
      deletedAt: json['deleted_at']?.toString(),
      createdBy: _parseIntSafely(json['created_by']),
      updatedBy: _parseIntSafely(json['updated_by']),
      title: json['title']?.toString(),
      code: json['code']?.toString(),
      description: json['description']?.toString(),
      coverImage: json['cover_image']?.toString(),
      flag: json['flag']?.toString(),
      phoneCode: json['phone_code']?.toString(),
      status: _parseIntSafely(json['status']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'created_by': createdBy,
      'updated_by': updatedBy,
      'title': title,
      'code': code,
      'description': description,
      'cover_image': coverImage,
      'flag': flag,
      'phone_code': phoneCode,
      'status': status,
    };
  }

  Country copyWith({
    int? id,
    String? createdAt,
    String? updatedAt,
    String? deletedAt,
    int? createdBy,
    int? updatedBy,
    String? title,
    String? code,
    String? description,
    String? coverImage,
    String? flag,
    String? phoneCode,
    int? status,
  }) {
    return Country(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      title: title ?? this.title,
      code: code ?? this.code,
      description: description ?? this.description,
      coverImage: coverImage ?? this.coverImage,
      flag: flag ?? this.flag,
      phoneCode: phoneCode ?? this.phoneCode,
      status: status ?? this.status,
    );
  }

  // Helper method để parse int safely
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  // Getter helpers
  String get displayName => title ?? code ?? 'Unknown Country';
  String get fullPhoneCode => phoneCode != null ? '+$phoneCode' : '';
  bool get isActive => status == 1;
  bool get hasFlag => flag != null && flag!.isNotEmpty;
  bool get hasCoverImage => coverImage != null && coverImage!.isNotEmpty;

  @override
  String toString() {
    return 'Country(id: $id, title: $title, code: $code, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Country && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
