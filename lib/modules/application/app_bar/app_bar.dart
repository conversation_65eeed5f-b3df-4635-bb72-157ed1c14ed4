import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/application/app_bar/custom_dropdown_appbar.dart';
import '../../../core/l10n/locale_keys.g.dart';
import '../bottom_bar/bloc/application_bloc.dart';
import '../bottom_bar/bloc/application_event.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String moden;
  final VoidCallback? onTapSearch, onTapNotification, onTapTranslate;
  const CustomAppBar(
      {super.key,
      required this.moden,
      this.onTapSearch,
      this.onTapNotification,
      this.onTapTranslate});

  @override
  // ignore: library_private_types_in_public_api
  _CustomAppBarState createState() => _CustomAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _CustomAppBarState extends State<CustomAppBar> {
  late String _currentText;

  @override
  void initState() {
    super.initState();
    _currentText = widget.moden;
  }

  void _navigateToScreen(String selected) {
    switch (selected) {
      case LocaleKeys.social:
        context.go(RouteName.profileSocial);
        break;
      case LocaleKeys.marketplace:
        context.go(RouteName.marketMode);
        break;
      case LocaleKeys.business:
        context.go(RouteName.businesMode);
        break;
      case LocaleKeys.dating:
        context.go(RouteName.datingMode);
        break;
      case LocaleKeys.newText:
        context.go(RouteName.newPage);
        context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
        break;
      case 'StrongBody.ai':
        context.go(RouteName.homeStrongBody);
        context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      centerTitle: true,
      elevation: 0,
      leadingWidth: double.infinity,
      leading: Padding(
        padding: const EdgeInsets.only(left: 20.0),
        child: CustomDropdown(
          initialText: _currentText,
          items: const [
            LocaleKeys.social,
            LocaleKeys.marketplace,
            LocaleKeys.business,
            LocaleKeys.dating,
            LocaleKeys.newText,
            'StrongBody.ai',
          ],
          modeMapping: const {
            'Social': 'social',
            'Business': 'business',
            'News': 'news',
            'Market': 'market',
            'Dating': 'dating',
            'StrongBody': 'strongbody',
          },
          onItemSelected: (selected) {
            setState(() {
              _currentText = selected;
            });
            _navigateToScreen(selected);
          },
        ),
      ),
      actions: [
        Row(
          children: [
            GestureDetector(
                onTap: widget.onTapTranslate,
                child: SvgPicture.asset(AppAssets.translateAppBarSvg)),
            Gap(10.w),
            GestureDetector(
                onTap: () {
                  context.pushNamed(RouteName.notificationPage);
                },
                child: SvgPicture.asset(AppAssets.notiSvg)),
            Gap(10.w),
            GestureDetector(
                onTap: widget.onTapSearch,
                child: SvgPicture.asset(AppAssets.searchSvg)),
            Gap(16.w)
          ],
        ),
      ],
    );
  }
}
