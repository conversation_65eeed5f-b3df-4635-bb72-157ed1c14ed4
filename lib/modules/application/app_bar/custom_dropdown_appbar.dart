import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../app/routers/routers_name.dart';
import '../../../core/di/locator.dart';
import '../../../core/domain/storages/global_storage.dart';
import '../bottom_bar/bloc/application_bloc.dart';
import '../bottom_bar/bloc/application_event.dart';

class CustomDropdown extends StatefulWidget {
  final List<String> items;
  final String initialText;
  final void Function(String) onItemSelected;
  final Map<String, String> modeMapping;

  const CustomDropdown({
    super.key,
    required this.items,
    required this.initialText,
    required this.onItemSelected,
    required this.modeMapping,
  });

  @override
  // ignore: library_private_types_in_public_api
  _CustomDropdownState createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  bool _isDropdownOpen = false;
  OverlayEntry? _overlayEntry;

  void _switchMode(String selectedItem, BuildContext context) async {
    final mode = widget.modeMapping[selectedItem] ??
        _getModeFromDisplayName(selectedItem);

    await getIt<GlobalStorage>().setCurrentAppMode(mode);

    widget.onItemSelected(selectedItem);

    if (context.mounted) {
      switch (mode) {
        case 'social':
          context.push(RouteName.profileSocial);
          break;
        case 'business':
          context.push(RouteName.businesOverView);
          break;
        case 'news':
          context.push(RouteName.newPage);
          context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
          break;
        case 'market':
          context.push(RouteName.marketMode);
          break;
        case 'dating':
          context.push(RouteName.datingMode);
          break;
        case 'strongbody':
          context.push(RouteName.homeStrongBody);
          context.read<ApplicationBloc>().add(ApplicationSelectIndex(0));
          break;
      }
    }
  }

  String _getModeFromDisplayName(String displayName) {
    final lowerCaseName = displayName.toLowerCase();

    if (lowerCaseName.contains('social')) return 'social';
    if (lowerCaseName.contains('business')) return 'business';
    if (lowerCaseName.contains('news') || lowerCaseName.contains('new'))
      return 'news';
    if (lowerCaseName.contains('market')) return 'market';
    if (lowerCaseName.contains('dating')) return 'dating';
    if (lowerCaseName.contains('strongbody') ||
        lowerCaseName.contains('strong')) return 'strongbody';

    return 'social';
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    final overlay = Overlay.of(context);
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: _closeDropdown, // Close dropdown when tapped outside
              child: Container(color: Colors.transparent),
            ),
          ),
          Positioned(
            top: position.dy + 50.h, // Adjust dropdown position
            left: position.dx,
            child: Material(
              color: Colors.transparent,
              child: Container(
                width: 162.w,
                padding: EdgeInsets.symmetric(vertical: 8.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).secondary(context).withOpacity(0.9),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: widget.items.length,
                  separatorBuilder: (_, __) => Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Divider(
                      color: Colors.white.withOpacity(0.2),
                      thickness: 1,
                    ),
                  ),
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        _switchMode(widget.items[index], context);
                        // widget.onItemSelected(widget.items[index]);
                        _closeDropdown();
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 3.h),
                        child: Text(
                          widget.items[index],
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w600,
                            fontFamily:
                                GoogleFonts.plusJakartaSans().fontFamily,
                          ),
                        ).tr(),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );

    overlay.insert(_overlayEntry!);
    setState(() {
      _isDropdownOpen = true;
    });
  }

  void _closeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isDropdownOpen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleDropdown,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.initialText,
            style: TextStyle(
              fontSize: 20.sp,
              color: Theme.of(context).primary(context),
              fontWeight: FontWeight.w700,
              fontFamily: GoogleFonts.plusJakartaSans().fontFamily,
            ),
          ).tr(),
          Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: Icon(
              Icons.arrow_drop_down,
              color: Theme.of(context).primary(context),
              size: 35.sp,
            ),
          ),
        ],
      ),
    );
  }
}
