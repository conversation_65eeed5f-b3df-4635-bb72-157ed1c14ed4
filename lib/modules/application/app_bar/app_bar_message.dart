// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import '../../../core/constants/app_assets.dart';

// ignore: must_be_immutable
class AppBarMessage extends StatefulWidget implements PreferredSizeWidget {
  void Function()? onTapTranslate;
  void Function()? onTapAddGroup;

  AppBarMessage({
    Key? key,
    this.onTapTranslate,
    this.onTapAddGroup,
  }) : super(key: key);

  @override
  State<AppBarMessage> createState() => _AppBarMessageState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _AppBarMessageState extends State<AppBarMessage> {
  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('Message',
              style: Theme.of(context).textTheme.lightBodyXLargeSemiBold),
          Row(
            children: [
              GestureDetector(
                onTap: widget.onTapTranslate,
                child: SvgPicture.asset(
                  AppAssets.translate_icon,
                ),
              ),
              Gap(15.w),
              GestureDetector(
                onTap: widget.onTapAddGroup,
                child: SvgPicture.asset(
                  AppAssets.add_group,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
