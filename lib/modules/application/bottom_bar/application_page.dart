import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>';
import 'package:multime_app/modules/application/bottom_bar/bloc/application_bloc.dart';
import 'package:multime_app/modules/application/bottom_bar/bloc/application_event.dart';
import 'package:multime_app/modules/application/bottom_bar/bloc/application_state.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/widgets/login_redirect.dart';
import 'package:multime_app/modules/chat/presentation/chat_homepage.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/my_order_home_page/my_order_home_page.dart';

import '../../../core/constants/app_assets.dart';
import '../../../core/di/locator.dart';
import '../../../core/domain/storages/global_storage.dart';
import '../../../core/l10n/locale_keys.g.dart';
import 'bottomSheet_model.dart';

class ApplicationPage extends StatelessWidget {
  ApplicationPage(
      {super.key, required this.page, required this.bottomSheetItems});

  final Widget page;
  final List<BottomSheetItem> bottomSheetItems;
  final token = getIt<GlobalStorage>().accessToken;

  @override
  Widget build(BuildContext context) {
    List<Widget> pages = [
      page,
      if (getIt<GlobalStorage>().accessToken == null)
        const LoginRedirect()
      else
        const MyOrderHomePage(),
      if (getIt<GlobalStorage>().accessToken == null)
        const LoginRedirect()
      else
        const ChatHomepage(),
      if (getIt<GlobalStorage>().accessToken == null)
        const LoginRedirect()
      else
        HomeUserPage(),
      const MyOrderHomePage(),
      const ChatHomepage(),
      HomeUserPage(),
    ];
    return BlocBuilder<ApplicationBloc, ApplicationState>(
      builder: (context, state) {
        final user = getIt<GlobalStorage>().user;
        return Scaffold(
          body: pages[state.index],
          bottomNavigationBar: _buildBottomNavBar(
            context,
            state,
            user?.profilePicture ??
                "https://cdn.pixabay.com/photo/2015/08/23/20/48/girl-903401_640.jpg",
          ),
        );
      },
    );
  }

  Widget _buildBottomNavBar(
      BuildContext context, ApplicationState state, String imgAvt) {
    final List<String> icons = [
      AppAssets.homeSvg,
      AppAssets.orderSvg,
      AppAssets.messageSvg,
      imgAvt,
      AppAssets.addSquare,
      AppAssets.closeSquare
    ];

    return Material(
      child: Container(
        height: 80.h,
        decoration: const BoxDecoration(color: Colors.white, boxShadow: [
          BoxShadow(
            color: Color.fromARGB(95, 107, 107, 107),
            blurRadius: 10,
          )
        ]),
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildNavItem(context, state, 0, LocaleKeys.home, icons[0]),
              _buildNavItem(context, state, 1, LocaleKeys.order, icons[1]),
              _buildNavItem(context, state, 4, 'Multi',
                  state.isBottomSheetOpen ? icons[5] : icons[4]),
              _buildNavItem(context, state, 2, LocaleKeys.message, icons[2]),
              _buildNavItem(context, state, 3, LocaleKeys.Me, icons[3]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, ApplicationState state, int index,
      String label, String icon) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          if (index == 4) {
            final applicationBloc = context.read<ApplicationBloc>();
            if (!applicationBloc.state.isBottomSheetOpen) {
              applicationBloc.add(ApplicationOpenBottomSheet());
            }
            Future.delayed(const Duration(milliseconds: 100), () {
              // ignore: use_build_context_synchronously
              _showBottomSheet(context);
            });
          } else {
            context.read<ApplicationBloc>().add(ApplicationSelectIndex(index));
          }
        },
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              index == 3
                  ? SizedBox(
                      height: 26,
                      width: 26,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: Image.network(
                          icon,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              AppAssets.logoMultime,
                              fit: BoxFit.cover,
                            );
                          },
                        ),
                      ),
                    )
                  : SvgPicture.asset(
                      icon,
                      colorFilter: ColorFilter.mode(
                        state.isBottomSheetOpen && index == 4
                            ? Theme.of(context).errorBase(context)
                            : state.index == index && !state.isBottomSheetOpen
                                ? Theme.of(context).errorBase(context)
                                : Colors.black,
                        BlendMode.srcIn,
                      ),
                    ),
              Gap(4.h),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: state.isBottomSheetOpen && index == 4
                      ? Theme.of(context).errorDark(context)
                      : state.index == index && !state.isBottomSheetOpen
                          ? Theme.of(context).errorBase(context)
                          : Colors.black,
                ),
              ).tr(),
            ],
          ),
        ),
      ),
    );
  }

  void _showBottomSheet(BuildContext context) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: "BottomSheet",
      barrierColor: Colors.transparent,
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Stack(
          children: [
            Positioned(
              left: 16.0,
              right: 16.0,
              bottom: MediaQuery.of(context).viewInsets.bottom + 80.h,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    //  color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: bottomSheetItems.map((item) {
                      return GestureDetector(
                        onTap: () {
                          context.pop(); // Đóng dialog
                          item.onTap?.call(); // Thực thi hành động
                          // context
                          //     .read<ApplicationBloc>()
                          //     .add(ApplicationOpenBottomSheet());
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).textPrimary(context),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ListTile(
                            leading: SvgPicture.asset(
                              item.iconLead,
                            ),
                            title: Text(
                              item.name,
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    ).then((_) {
      // Khi dialog đóng lại
      context.read<ApplicationBloc>().add(ApplicationOpenBottomSheet());
    });
  }
}
