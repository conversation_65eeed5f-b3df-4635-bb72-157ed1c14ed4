
import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:multime_app/modules/application/bottom_bar/bloc/application_event.dart';
import 'package:multime_app/modules/application/bottom_bar/bloc/application_state.dart';

class ApplicationBloc extends Bloc<ApplicationEvent, ApplicationState>{
  ApplicationBloc() : super(ApplicationState.initial()){
    on<ApplicationSelectIndex>(_onSelectIndex);
    on<ApplicationOpenBottomSheet>(_onOpenBottomSheet);
  }

  void _onSelectIndex(ApplicationSelectIndex event, Emitter<ApplicationState> emit) {
    emit(state.copyWith(index: event.index));
  }
  void _onOpenBottomSheet(ApplicationOpenBottomSheet event, Emitter<ApplicationState> emit) {
    debugPrint('Before Open Bottom Sheet : ${state.isBottomSheetOpen}');
    emit(state.copyWith(isBottomSheetOpen: !state.isBottomSheetOpen));
    debugPrint('After Open Bottom Sheet : ${state.isBottomSheetOpen}');
  }


}