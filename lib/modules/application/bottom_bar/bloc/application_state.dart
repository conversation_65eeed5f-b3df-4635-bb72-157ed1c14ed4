import 'package:equatable/equatable.dart';

class ApplicationState extends Equatable {
  final int index;
  final bool isBottomSheetOpen;

  const ApplicationState({required this.index, this.isBottomSheetOpen = false});

  factory ApplicationState.initial() {
    return const ApplicationState(index: 0, isBottomSheetOpen: false);
  }

  ApplicationState copyWith({int? index, bool? isBottomSheetOpen}) {
    return ApplicationState(
        index: index ?? this.index,
        isBottomSheetOpen: isBottomSheetOpen ?? this.isBottomSheetOpen);
  }

  @override
  List<Object> get props => [index, isBottomSheetOpen];
}
