import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_model.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_details_model.dart';

abstract class GooglePlacesRepository {
  Future<GooglePlacesAutocompleteResponse> getAutocomplete({
    required String input,
    String? language,
    String? types,
    String? components,
  });

  Future<GooglePlacesDetailsResponse> getPlaceDetails({
    required String placeId,
    String? language,
  });
}

class GooglePlacesRepositoryRemote implements GooglePlacesRepository {
  final ApiClient _apiClient;

  GooglePlacesRepositoryRemote(this._apiClient);

  @override
  Future<GooglePlacesAutocompleteResponse> getAutocomplete({
    required String input,
    String? language,
    String? types,
    String? components,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'input': input,
      };

      // Chỉ thêm các tham số optional nếu được truyền vào
      if (language != null) queryParams['language'] = language;
      if (types != null) queryParams['types'] = types;
      if (components != null) queryParams['components'] = components;

      print('🌐 Calling Google Places API with params: $queryParams');

      final response = await _apiClient.request(
        path: ApiConst.googlePlacesAutocomplete,
        method: ApiType.get,
        queryParameters: queryParams,
      );

      print('📥 Google Places API Response: $response');

      final result = GooglePlacesAutocompleteResponse.fromJson(response);
      print('🔍 API Code: ${result.code}, Message: ${result.message}');
      print('📍 Parsed predictions count: ${result.predictions.length}');

      if (result.predictions.isNotEmpty) {
        print('📋 First prediction: ${result.predictions.first.description}');
      }

      return result;
    } catch (e) {
      print("❌ Google Places Autocomplete error: $e");
      rethrow;
    }
  }

  @override
  Future<GooglePlacesDetailsResponse> getPlaceDetails({
    required String placeId,
    String? language,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'place_id': placeId,
      };

      // Chỉ thêm language nếu được truyền vào
      if (language != null) queryParams['language'] = language;

      print('🌐 Calling Google Places Details API with params: $queryParams');

      final response = await _apiClient.request(
        path: ApiConst.googlePlacesDetails,
        method: ApiType.get,
        queryParameters: queryParams,
      );

      print('📥 Google Places Details API Response: $response');

      final result = GooglePlacesDetailsResponse.fromJson(response);
      print('🔍 Details API Code: ${result.code}, Message: ${result.message}');
      print('📍 Place details: ${result.data.data.result.name}');
      print(
          '📍 Location: lat=${result.data.data.result.geometry.location.lat}, lng=${result.data.data.result.geometry.location.lng}');

      return result;
    } catch (e) {
      print("❌ Google Places Details error: $e");
      rethrow;
    }
  }
}
