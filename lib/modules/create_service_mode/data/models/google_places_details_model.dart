class GooglePlacesDetailsResponse {
  final int code;
  final String message;
  final GooglePlacesDetailsData data;

  GooglePlacesDetailsResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory GooglePlacesDetailsResponse.fromJson(Map<String, dynamic> json) {
    return GooglePlacesDetailsResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: GooglePlacesDetailsData.fromJson(
          json['data'] as Map<String, dynamic>? ?? {}),
    );
  }
}

class GooglePlacesDetailsData {
  final GooglePlacesDetailsInner data;

  GooglePlacesDetailsData({
    required this.data,
  });

  factory GooglePlacesDetailsData.fromJson(Map<String, dynamic> json) {
    return GooglePlacesDetailsData(
      data: GooglePlacesDetailsInner.fromJson(
          json['data'] as Map<String, dynamic>? ?? {}),
    );
  }
}

class GooglePlacesDetailsInner {
  final List<dynamic> htmlAttributions;
  final PlaceDetailsResult result;
  final String status;

  GooglePlacesDetailsInner({
    required this.htmlAttributions,
    required this.result,
    required this.status,
  });

  factory GooglePlacesDetailsInner.fromJson(Map<String, dynamic> json) {
    return GooglePlacesDetailsInner(
      htmlAttributions: json['html_attributions'] as List<dynamic>? ?? [],
      result: PlaceDetailsResult.fromJson(
          json['result'] as Map<String, dynamic>? ?? {}),
      status: json['status'] ?? '',
    );
  }
}

class PlaceDetailsResult {
  final List<AddressComponent> addressComponents;
  final String adrAddress;
  final String formattedAddress;
  final PlaceGeometry geometry;
  final String icon;
  final String iconBackgroundColor;
  final String iconMaskBaseUri;
  final String name;
  final List<PlacePhoto> photos;
  final String placeId;
  final String reference;
  final List<String> types;
  final String url;
  final int utcOffset;
  final String vicinity;

  PlaceDetailsResult({
    required this.addressComponents,
    required this.adrAddress,
    required this.formattedAddress,
    required this.geometry,
    required this.icon,
    required this.iconBackgroundColor,
    required this.iconMaskBaseUri,
    required this.name,
    required this.photos,
    required this.placeId,
    required this.reference,
    required this.types,
    required this.url,
    required this.utcOffset,
    required this.vicinity,
  });

  factory PlaceDetailsResult.fromJson(Map<String, dynamic> json) {
    return PlaceDetailsResult(
      addressComponents: (json['address_components'] as List<dynamic>?)
              ?.map((item) =>
                  AddressComponent.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      adrAddress: json['adr_address'] ?? '',
      formattedAddress: json['formatted_address'] ?? '',
      geometry: PlaceGeometry.fromJson(
          json['geometry'] as Map<String, dynamic>? ?? {}),
      icon: json['icon'] ?? '',
      iconBackgroundColor: json['icon_background_color'] ?? '',
      iconMaskBaseUri: json['icon_mask_base_uri'] ?? '',
      name: json['name'] ?? '',
      photos: (json['photos'] as List<dynamic>?)
              ?.map((item) => PlacePhoto.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      placeId: json['place_id'] ?? '',
      reference: json['reference'] ?? '',
      types: (json['types'] as List<dynamic>?)?.cast<String>() ?? [],
      url: json['url'] ?? '',
      utcOffset: json['utc_offset'] ?? 0,
      vicinity: json['vicinity'] ?? '',
    );
  }
}

class AddressComponent {
  final String longName;
  final String shortName;
  final List<String> types;

  AddressComponent({
    required this.longName,
    required this.shortName,
    required this.types,
  });

  factory AddressComponent.fromJson(Map<String, dynamic> json) {
    return AddressComponent(
      longName: json['long_name'] ?? '',
      shortName: json['short_name'] ?? '',
      types: (json['types'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

class PlaceGeometry {
  final LatLng location;
  final Viewport viewport;

  PlaceGeometry({
    required this.location,
    required this.viewport,
  });

  factory PlaceGeometry.fromJson(Map<String, dynamic> json) {
    return PlaceGeometry(
      location:
          LatLng.fromJson(json['location'] as Map<String, dynamic>? ?? {}),
      viewport:
          Viewport.fromJson(json['viewport'] as Map<String, dynamic>? ?? {}),
    );
  }
}

class LatLng {
  final double lat;
  final double lng;

  LatLng({
    required this.lat,
    required this.lng,
  });

  factory LatLng.fromJson(Map<String, dynamic> json) {
    return LatLng(
      lat: (json['lat'] as num?)?.toDouble() ?? 0.0,
      lng: (json['lng'] as num?)?.toDouble() ?? 0.0,
    );
  }
}

class Viewport {
  final LatLng northeast;
  final LatLng southwest;

  Viewport({
    required this.northeast,
    required this.southwest,
  });

  factory Viewport.fromJson(Map<String, dynamic> json) {
    return Viewport(
      northeast:
          LatLng.fromJson(json['northeast'] as Map<String, dynamic>? ?? {}),
      southwest:
          LatLng.fromJson(json['southwest'] as Map<String, dynamic>? ?? {}),
    );
  }
}

class PlacePhoto {
  final int height;
  final List<String> htmlAttributions;
  final String photoReference;
  final int width;

  PlacePhoto({
    required this.height,
    required this.htmlAttributions,
    required this.photoReference,
    required this.width,
  });

  factory PlacePhoto.fromJson(Map<String, dynamic> json) {
    return PlacePhoto(
      height: json['height'] ?? 0,
      htmlAttributions:
          (json['html_attributions'] as List<dynamic>?)?.cast<String>() ?? [],
      photoReference: json['photo_reference'] ?? '',
      width: json['width'] ?? 0,
    );
  }
}
