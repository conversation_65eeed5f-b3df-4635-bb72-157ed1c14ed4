class GooglePlacesAutocompleteResponse {
  final int code;
  final String message;
  final GooglePlacesData data;

  GooglePlacesAutocompleteResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory GooglePlacesAutocompleteResponse.fromJson(Map<String, dynamic> json) {
    return GooglePlacesAutocompleteResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: GooglePlacesData.fromJson(
          json['data'] as Map<String, dynamic>? ?? {}),
    );
  }

  // Getter để lấy predictions một cách dễ dàng
  List<PlacePrediction> get predictions => data.predictions;
}

class GooglePlacesData {
  final GooglePlacesDataInner data;

  GooglePlacesData({
    required this.data,
  });

  factory GooglePlacesData.fromJson(Map<String, dynamic> json) {
    return GooglePlacesData(
      data: GooglePlacesDataInner.fromJson(
          json['data'] as Map<String, dynamic>? ?? {}),
    );
  }

  // Getter để lấy predictions
  List<PlacePrediction> get predictions => data.predictions;
}

class GooglePlacesDataInner {
  final List<PlacePrediction> predictions;
  final String status;

  GooglePlacesDataInner({
    required this.predictions,
    required this.status,
  });

  factory GooglePlacesDataInner.fromJson(Map<String, dynamic> json) {
    return GooglePlacesDataInner(
      predictions: (json['predictions'] as List<dynamic>?)
              ?.map((item) =>
                  PlacePrediction.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      status: json['status'] ?? '',
    );
  }
}

class PlacePrediction {
  final String description;
  final String placeId;
  final List<MatchedSubstring> matchedSubstrings;
  final String reference;
  final StructuredFormatting structuredFormatting;
  final List<Term> terms;
  final List<String> types;

  PlacePrediction({
    required this.description,
    required this.placeId,
    this.matchedSubstrings = const [],
    this.reference = '',
    required this.structuredFormatting,
    this.terms = const [],
    this.types = const [],
  });

  factory PlacePrediction.fromJson(Map<String, dynamic> json) {
    return PlacePrediction(
      description: json['description'] ?? '',
      placeId: json['place_id'] ?? '',
      matchedSubstrings: (json['matched_substrings'] as List<dynamic>?)
              ?.map((item) =>
                  MatchedSubstring.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      reference: json['reference'] ?? '',
      structuredFormatting: StructuredFormatting.fromJson(
          json['structured_formatting'] as Map<String, dynamic>? ?? {}),
      terms: (json['terms'] as List<dynamic>?)
              ?.map((item) => Term.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      types: (json['types'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }
}

class MatchedSubstring {
  final int length;
  final int offset;

  MatchedSubstring({
    required this.length,
    required this.offset,
  });

  factory MatchedSubstring.fromJson(Map<String, dynamic> json) {
    return MatchedSubstring(
      length: json['length'] ?? 0,
      offset: json['offset'] ?? 0,
    );
  }
}

class StructuredFormatting {
  final String mainText;
  final List<MatchedSubstring> mainTextMatchedSubstrings;
  final String secondaryText;
  final List<MatchedSubstring> secondaryTextMatchedSubstrings;

  StructuredFormatting({
    required this.mainText,
    this.mainTextMatchedSubstrings = const [],
    this.secondaryText = '',
    this.secondaryTextMatchedSubstrings = const [],
  });

  factory StructuredFormatting.fromJson(Map<String, dynamic> json) {
    return StructuredFormatting(
      mainText: json['main_text'] ?? '',
      mainTextMatchedSubstrings:
          (json['main_text_matched_substrings'] as List<dynamic>?)
                  ?.map((item) =>
                      MatchedSubstring.fromJson(item as Map<String, dynamic>))
                  .toList() ??
              [],
      secondaryText: json['secondary_text'] ?? '',
      secondaryTextMatchedSubstrings:
          (json['secondary_text_matched_substrings'] as List<dynamic>?)
                  ?.map((item) =>
                      MatchedSubstring.fromJson(item as Map<String, dynamic>))
                  .toList() ??
              [],
    );
  }
}

class Term {
  final int offset;
  final String value;

  Term({
    required this.offset,
    required this.value,
  });

  factory Term.fromJson(Map<String, dynamic> json) {
    return Term(
      offset: json['offset'] ?? 0,
      value: json['value'] ?? '',
    );
  }
}
