import 'dart:async';

/// Utility class để handle debouncing operations một cách an toàn
class Debouncer {
  final Duration delay;
  Timer? _timer;

  Debouncer({required this.delay});

  /// Run một function sau khi delay, cancel c<PERSON><PERSON> calls trước đ<PERSON>
  void run(Function() action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  /// Cancel debounce timer hiện tại
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }

  /// Dispose debouncer
  void dispose() {
    cancel();
  }
}
