import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/category.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_details_model.dart';

class CreateServiceState extends Equatable {
  final ApiResponse<CategoryList>? categories;
  final ApiResponse<ServiceModel>? createService;
  final ApiResponse<Map<String, dynamic>>? postImage;
  final ApiResponse<Map<String, dynamic>>? postCertificate;
  final List<File> imageFiles;
  final String name;
  final String description;
  final String category;
  final String timeToComplete;
  final double startPrice;
  final List<File> certificateFiles;
  final bool isSubmitting;
  final bool isSuccess;
  final String? errorMessage;
  final String serviceType;
  final String locationType;
  final PlaceDetailsResult? locationDetails;

  const CreateServiceState({
    this.categories,
    this.createService,
    this.imageFiles = const [],
    this.name = '',
    this.description = '',
    this.category = '',
    this.timeToComplete = '',
    this.startPrice = 0.0,
    this.certificateFiles = const [],
    this.isSubmitting = false,
    this.isSuccess = false,
    this.errorMessage,
    this.serviceType = 'online',
    this.locationType = 'online',
    this.postImage,
    this.postCertificate,
    this.locationDetails,
  });

  CreateServiceState copyWith({
    ApiResponse<CategoryList>? categories,
    ApiResponse<ServiceModel>? createService,
    List<File>? imageFiles,
    String? name,
    String? description,
    String? category,
    String? timeToComplete,
    double? startPrice,
    List<File>? certificateFiles,
    bool? isSubmitting,
    bool? isSuccess,
    String? errorMessage,
    String? serviceType,
    String? locationType,
    ApiResponse<Map<String, dynamic>>? postImage,
    ApiResponse<Map<String, dynamic>>? postCertificate,
    PlaceDetailsResult? locationDetails,
  }) {
    return CreateServiceState(
      imageFiles: imageFiles ?? this.imageFiles,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      timeToComplete: timeToComplete ?? this.timeToComplete,
      startPrice: startPrice ?? this.startPrice,
      certificateFiles: certificateFiles ?? this.certificateFiles,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isSuccess: isSuccess ?? this.isSuccess,
      errorMessage: errorMessage,
      serviceType: serviceType ?? this.serviceType,
      locationType: locationType ?? this.locationType,
      createService: createService ?? this.createService,
      categories: categories ?? this.categories,
      postImage: postImage ?? this.postImage,
      postCertificate: postCertificate ?? this.postCertificate,
      locationDetails: locationDetails ?? this.locationDetails,
    );
  }

  @override
  List<Object?> get props => [
        imageFiles,
        name,
        description,
        category,
        timeToComplete,
        startPrice,
        certificateFiles,
        isSubmitting,
        isSuccess,
        errorMessage,
        serviceType,
        locationType,
        createService,
        categories,
        postImage,
        postCertificate,
        locationDetails,
      ];
}
