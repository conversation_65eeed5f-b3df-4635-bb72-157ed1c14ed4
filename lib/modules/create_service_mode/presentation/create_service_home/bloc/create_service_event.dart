import 'dart:io';

import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_details_model.dart';

abstract class CreateServiceEvent extends Equatable {
  @override
  List<Object> get props => [];
}

class UpdateServiceImage extends CreateServiceEvent {
  final List<File> imageFiles;

  UpdateServiceImage(this.imageFiles);

  @override
  List<Object> get props => [imageFiles];
}

class RemoveServiceImage extends CreateServiceEvent {
  final int index;

  RemoveServiceImage(this.index);

  @override
  List<Object> get props => [index];
}

class UpdateServiceName extends CreateServiceEvent {
  final String name;
  UpdateServiceName(this.name);

  @override
  List<Object> get props => [name];
}

class UpdateServiceDescription extends CreateServiceEvent {
  final String description;
  UpdateServiceDescription(this.description);

  @override
  List<Object> get props => [description];
}

class UpdateServiceCategory extends CreateServiceEvent {
  final String category;
  UpdateServiceCategory(this.category);

  @override
  List<Object> get props => [category];
}

class UpdateServiceTimeToComplete extends CreateServiceEvent {
  final String time;
  UpdateServiceTimeToComplete(this.time);

  @override
  List<Object> get props => [time];
}

class UpdateServiceStartPrice extends CreateServiceEvent {
  final double startPrice;
  UpdateServiceStartPrice(this.startPrice);

  @override
  List<Object> get props => [startPrice];
}

class UpdateServiceCertificate extends CreateServiceEvent {
  final List<File> certificateFiles;

  UpdateServiceCertificate(this.certificateFiles);

  @override
  List<Object> get props => [certificateFiles];
}

class RemoveServiceCertificate extends CreateServiceEvent {
  final int index;

  RemoveServiceCertificate(this.index);

  @override
  List<Object> get props => [index];
}

class SubmitService extends CreateServiceEvent {}

class ChangeServiceType extends CreateServiceEvent {
  final String type;

  ChangeServiceType(this.type);

  @override
  List<Object> get props => [type];
}

class CreateService extends CreateServiceEvent {
  final Map<String, dynamic> data;

  CreateService(this.data);

  @override
  List<Object> get props => [data];
}

class GetCategoriesEvent extends CreateServiceEvent {
  GetCategoriesEvent();
}

class AddImageApiEvent extends CreateServiceEvent {
  final String id;
  final List<File> imageFiles;

  AddImageApiEvent({required this.imageFiles, required this.id});

  @override
  List<Object> get props => [imageFiles];
}

class AddCertificateApiEvent extends CreateServiceEvent {
  final String id;
  final List<File> imageFiles;

  AddCertificateApiEvent({required this.imageFiles, required this.id});

  @override
  List<Object> get props => [imageFiles];
}

class CreateServiceWithFormData extends CreateServiceEvent {
  final FormData formData;

  CreateServiceWithFormData(this.formData);

  @override
  List<Object> get props => [formData];
}

class UpdateServiceLocation extends CreateServiceEvent {
  final PlaceDetailsResult locationDetails;

  UpdateServiceLocation(this.locationDetails);

  @override
  List<Object> get props => [locationDetails];
}
