import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/category.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/category_repository.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';
import 'create_service_event.dart';
import 'create_service_state.dart';
import 'package:http_parser/http_parser.dart';

class CreateServiceBloc extends Bloc<CreateServiceEvent, CreateServiceState> {
  final HomeStrongbodyAiRepository _servicesRepository;
  final CategoryRepositoryRemote _categoryRepositoryRemote;
  CreateServiceBloc(this._servicesRepository, this._categoryRepositoryRemote)
      : super(const CreateServiceState()) {
    on<UpdateServiceImage>((event, emit) {
      emit(state.copyWith(imageFiles: event.imageFiles));
    });

    on<RemoveServiceImage>((event, emit) {
      final updatedFiles = List<File>.from(state.imageFiles);
      updatedFiles.removeAt(event.index);
      emit(state.copyWith(imageFiles: updatedFiles));
    });

    on<UpdateServiceName>((event, emit) {
      emit(state.copyWith(name: event.name));
    });

    on<UpdateServiceDescription>((event, emit) {
      emit(state.copyWith(description: event.description));
    });

    on<UpdateServiceCategory>((event, emit) {
      emit(state.copyWith(category: event.category));
    });

    on<UpdateServiceTimeToComplete>((event, emit) {
      emit(state.copyWith(timeToComplete: event.time));
    });

    on<UpdateServiceStartPrice>((event, emit) {
      emit(state.copyWith(startPrice: event.startPrice));
    });

    on<UpdateServiceCertificate>((event, emit) {
      emit(state.copyWith(certificateFiles: event.certificateFiles));
    });

    on<RemoveServiceCertificate>((event, emit) {
      final updatedFiles = List<File>.from(state.certificateFiles);
      updatedFiles.removeAt(event.index);
      emit(state.copyWith(certificateFiles: updatedFiles));
    });

    on<SubmitService>((event, emit) {
      emit(state.copyWith(isSubmitting: true));

      Future.delayed(const Duration(seconds: 2), () {
        emit(state.copyWith(isSubmitting: false, isSuccess: true));
      });
    });
    on<ChangeServiceType>((event, emit) {
      if (event.type == 'onsite') {
        emit(
            state.copyWith(serviceType: event.type, locationType: 'in-person'));
      } else if (event.type == 'hybrid') {
        emit(state.copyWith(serviceType: event.type, locationType: 'both'));
      } else {
        ///online same for both
        emit(state.copyWith(serviceType: event.type, locationType: event.type));
      }
    });

    on<UpdateServiceLocation>((event, emit) {
      emit(state.copyWith(locationDetails: event.locationDetails));
    });
    on<GetCategoriesEvent>((
      event,
      emit,
    ) async {
      try {
        // Prevent duplicate API calls if categories are already loaded or loading
        if (state.categories?.status == Status.loading) {
          return;
        }

        if (state.categories?.status == Status.completed &&
            state.categories?.data?.items != null &&
            state.categories!.data!.items!.isNotEmpty) {
          return;
        }

        emit(state.copyWith(categories: const ApiResponse.loading()));
        CategoryList products =
            await _categoryRepositoryRemote.getCategoryList();
        print(
            '✅ Categories loaded successfully: ${products.items?.length ?? 0} items');
        emit(state.copyWith(categories: ApiResponse.completed(products)));
      } on NetworkException catch (e) {
        print('❌ Network error loading categories: ${e.message}');
        emit(state.copyWith(categories: ApiResponse.error(e.message)));
      } catch (e) {
        print('❌ General error loading categories: $e');
        emit(state.copyWith(categories: ApiResponse.error(e.toString())));
      }
    });

    on<CreateService>((event, emit) async {
      emit(state.copyWith(createService: const ApiResponse.loading()));
      try {
        var service = await _servicesRepository.createService(event.data);
        // add(AddImageApiEvent(imageFiles: state.imageFiles, id: service.id));
        emit(state.copyWith(createService: ApiResponse.completed(service)));
      } on NetworkException catch (e) {
        print("error in create service bloc: $e");
        emit(state.copyWith(createService: ApiResponse.error(e.message)));
      }
    });

    on<CreateServiceWithFormData>((event, emit) async {
      emit(state.copyWith(createService: const ApiResponse.loading()));
      try {
        var service =
            await _servicesRepository.createServiceWithFormData(event.formData);
        emit(state.copyWith(createService: ApiResponse.completed(service)));
      } on NetworkException catch (e) {
        print("error in create service with form data bloc: $e");
        emit(state.copyWith(createService: ApiResponse.error(e.message)));
      }
    });

    on<AddImageApiEvent>((event, emit) async {
      emit(state.copyWith(postImage: const ApiResponse.loading()));
      try {
        emit(state.copyWith(postImage: const ApiResponse.loading()));

        var files = await Future.wait(event.imageFiles.map((img) async {
          var file = await MultipartFile.fromFile(
            img.path,
            contentType: MediaType('image',
                img.path.split('.').last), // Or whatever the image type is
          );
          return file;
        }).toList());
        var resp = await _servicesRepository.addImageApi(
            data: FormData.fromMap({"files": files}), id: event.id);
        add(AddCertificateApiEvent(
            imageFiles: state.certificateFiles, id: event.id));
        emit(state.copyWith(postImage: ApiResponse.completed(resp)));
      } on NetworkException catch (e) {
        emit(state.copyWith(postImage: ApiResponse.error(e.message)));
      } catch (e) {
        emit(state.copyWith(
            postImage: const ApiResponse.error(
                'Unable to process your request at this time.')));
      }
    });
    on<AddCertificateApiEvent>((event, emit) async {
      emit(state.copyWith(postCertificate: const ApiResponse.loading()));
      try {
        emit(state.copyWith(postCertificate: const ApiResponse.loading()));

        var files = await Future.wait(event.imageFiles.map((img) async {
          var file = await MultipartFile.fromFile(
            img.path,
            contentType: MediaType('image',
                img.path.split('.').last), // Or whatever the image type is
          );
          return file;
        }).toList());
        var resp = await _servicesRepository.addCertificateApi(
            data: FormData.fromMap({"files": files}), id: event.id);
        emit(state.copyWith(postCertificate: ApiResponse.completed(resp)));
      } on NetworkException catch (e) {
        emit(state.copyWith(postCertificate: ApiResponse.error(e.message)));
      } catch (e) {
        emit(state.copyWith(
            postImage: const ApiResponse.error(
                'Unable to process your request at this time.')));
      }
    });
  }
}
