import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_details_model.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_event.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_home/service_category.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_home/service_image.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_home/service_name.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_home/service_start_price.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_home/service_type.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/category_repository.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

import '../../../../core/l10n/locale_keys.g.dart';
import '../widgets/service_app_bar.dart';
import '../widgets/service_home/service_certificate.dart';
import '../widgets/service_home/service_description.dart';
import '../widgets/service_home/service_establish_credibility.dart';
import '../widgets/service_home/service_location.dart';
import '../widgets/service_home/service_projected.dart';
import '../widgets/submit_button.dart';
import 'bloc/create_service_bloc.dart';

class CreateServiceHome extends StatefulWidget {
  const CreateServiceHome({super.key});

  @override
  State<CreateServiceHome> createState() => _CreateServiceHomeState();
}

class _CreateServiceHomeState extends State<CreateServiceHome> {
  PlaceDetailsResult? _selectedLocationDetails;
  late CreateServiceBloc _createServiceBloc;

  @override
  void initState() {
    super.initState();
    _createServiceBloc = CreateServiceBloc(
        getIt<HomeStrongbodyAiRepository>(), getIt<CategoryRepositoryRemote>())
      ..add(GetCategoriesEvent());
  }

  void _handleLocationDetailsReceived(PlaceDetailsResult details) {
    setState(() {
      _selectedLocationDetails = details;
    });
    // Save location information to CreateServiceBloc for FormData submission
    _createServiceBloc.add(UpdateServiceLocation(details));
  }

  @override
  void dispose() {
    _createServiceBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: BlocProvider(
        create: (context) => CreateServiceBloc(
            getIt<HomeStrongbodyAiRepository>(),
            getIt<CategoryRepositoryRemote>())
          ..add(GetCategoriesEvent()),
        child: Scaffold(
            appBar: ServiceAppBar(title: LocaleKeys.CreateNewService.tr()),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Gap(20.h),
                    const ServiceImage(),
                    Gap(20.h),
                    const ServiceName(),
                    Gap(20.h),
                    ServiceDescription(),
                    Gap(12.h),
                    const ServiceCategory(),
                    Gap(12.h),
                    ServiceProjected(),
                    Gap(18.h),
                    const ServiceStartPrice(),
                    Gap(20.h),
                    const ServiceCertificate(),
                    Gap(20.h),
                    ServiceEstablishCredibility(),
                    Gap(20.h),
                    const ServiceType(),
                    Gap(20.h),
                    ServiceLocation(
                      onLocationDetailsReceived: _handleLocationDetailsReceived,
                    ),
                    if (_selectedLocationDetails != null) ...[
                      Gap(10.h),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.location_on,
                                    color: Colors.green.shade700, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  "Selected Location",
                                  style: TextStyle(
                                    color: Colors.green.shade700,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _selectedLocationDetails!.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _selectedLocationDetails!.formattedAddress,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              "Coordinates: ${_selectedLocationDetails!.geometry.location.lat}, ${_selectedLocationDetails!.geometry.location.lng}",
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                                fontFamily: 'monospace',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    Gap(20.h),
                    const SubmitButton(),
                    Gap(50.h)
                  ],
                ),
              ),
            )),
      ),
    );
  }
}
