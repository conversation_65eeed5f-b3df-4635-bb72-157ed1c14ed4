import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_model.dart';

abstract class LocationAutocompleteEvent extends Equatable {
  const LocationAutocompleteEvent();

  @override
  List<Object?> get props => [];
}

class SearchLocationEvent extends LocationAutocompleteEvent {
  final String query;

  const SearchLocationEvent(this.query);

  @override
  List<Object?> get props => [query];
}

class SelectLocationEvent extends LocationAutocompleteEvent {
  final PlacePrediction selectedPlace;

  const SelectLocationEvent(this.selectedPlace);

  @override
  List<Object?> get props => [selectedPlace];
}

class GetLocationDetailsEvent extends LocationAutocompleteEvent {
  final String placeId;

  const GetLocationDetailsEvent(this.placeId);

  @override
  List<Object?> get props => [placeId];
}

class ClearLocationSearchEvent extends LocationAutocompleteEvent {
  const ClearLocationSearchEvent();
}
