import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:multime_app/modules/create_service_mode/data/repositories/google_places_repository.dart';
import 'package:multime_app/modules/create_service_mode/presentation/blocs/location_autocomplete_event.dart';
import 'package:multime_app/modules/create_service_mode/presentation/blocs/location_autocomplete_state.dart';
import 'package:multime_app/modules/create_service_mode/utils/debouncer.dart';

class LocationAutocompleteBloc
    extends Bloc<LocationAutocompleteEvent, LocationAutocompleteState> {
  final GooglePlacesRepository _googlePlacesRepository;
  final Debouncer _debouncer;

  LocationAutocompleteBloc(this._googlePlacesRepository)
      : _debouncer = Debouncer(delay: const Duration(milliseconds: 500)),
        super(const LocationAutocompleteState()) {
    on<SearchLocationEvent>(_onSearchLocation);
    on<SelectLocationEvent>(_onSelectLocation);
    on<GetLocationDetailsEvent>(_onGetLocationDetails);
    on<ClearLocationSearchEvent>(_onClearLocationSearch);
  }

  Future<void> _onSearchLocation(SearchLocationEvent event,
      Emitter<LocationAutocompleteState> emit) async {
    final query = event.query.trim();

    // Cancel previous debounced operations
    _debouncer.cancel();

    if (query.isEmpty) {
      emit(state.copyWith(
        status: LocationAutocompleteStatus.initial,
        predictions: [],
        query: query,
      ));
      return;
    }

    // Set loading state immediately for better UX
    emit(state.copyWith(
      status: LocationAutocompleteStatus.loading,
      query: query,
    ));

    // Use debouncer to delay API call
    final completer = Completer<void>();

    _debouncer.run(() {
      if (!completer.isCompleted) {
        completer.complete();
      }
    });

    // Wait for debounce
    await completer.future;

    // Check if bloc is still active and query hasn't changed
    if (emit.isDone || state.query != query) {
      return;
    }

    try {
      print('🔍 Searching for: $query');

      final response = await _googlePlacesRepository.getAutocomplete(
        input: query,
      );

      // Double check before emitting
      if (!emit.isDone && state.query == query) {
        print('📍 Found ${response.predictions.length} predictions');

        emit(state.copyWith(
          status: LocationAutocompleteStatus.success,
          predictions: response.predictions,
          query: query,
          errorMessage: null,
        ));
      }
    } catch (e) {
      print('❌ Search error: $e');

      // Check before emitting error
      if (!emit.isDone && state.query == query) {
        emit(state.copyWith(
          status: LocationAutocompleteStatus.error,
          errorMessage: e.toString(),
          query: query,
        ));
      }
    }
  }

  void _onSelectLocation(
      SelectLocationEvent event, Emitter<LocationAutocompleteState> emit) {
    print('✅ Selected location: ${event.selectedPlace.description}');
    // Tự động gọi API để lấy chi tiết địa điểm
    add(GetLocationDetailsEvent(event.selectedPlace.placeId));
  }

  Future<void> _onGetLocationDetails(GetLocationDetailsEvent event,
      Emitter<LocationAutocompleteState> emit) async {
    try {
      print('🔍 Getting location details for place_id: ${event.placeId}');

      emit(state.copyWith(
        status: LocationAutocompleteStatus.loading,
      ));

      final response = await _googlePlacesRepository.getPlaceDetails(
        placeId: event.placeId,
      );

      print('📍 Location details retrieved successfully');
      print('📍 Location: ${response.data.data.result.name}');
      print('📍 Lat: ${response.data.data.result.geometry.location.lat}');
      print('📍 Lng: ${response.data.data.result.geometry.location.lng}');

      emit(state.copyWith(
        status: LocationAutocompleteStatus.success,
        selectedLocationDetails: response.data.data.result,
        errorMessage: null,
      ));
    } catch (e) {
      print('❌ Get location details error: $e');

      emit(state.copyWith(
        status: LocationAutocompleteStatus.error,
        errorMessage: 'Failed to get location details: ${e.toString()}',
      ));
    }
  }

  void _onClearLocationSearch(
      ClearLocationSearchEvent event, Emitter<LocationAutocompleteState> emit) {
    _debouncer.cancel();
    emit(const LocationAutocompleteState());
  }

  @override
  Future<void> close() {
    _debouncer.dispose();
    return super.close();
  }
}
