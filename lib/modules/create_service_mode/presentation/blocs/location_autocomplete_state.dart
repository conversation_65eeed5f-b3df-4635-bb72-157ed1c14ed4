import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_model.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_details_model.dart';

enum LocationAutocompleteStatus { initial, loading, success, error }

class LocationAutocompleteState extends Equatable {
  final LocationAutocompleteStatus status;
  final List<PlacePrediction> predictions;
  final String query;
  final String? errorMessage;
  final PlaceDetailsResult? selectedLocationDetails;

  const LocationAutocompleteState({
    this.status = LocationAutocompleteStatus.initial,
    this.predictions = const [],
    this.query = '',
    this.errorMessage,
    this.selectedLocationDetails,
  });

  LocationAutocompleteState copyWith({
    LocationAutocompleteStatus? status,
    List<PlacePrediction>? predictions,
    String? query,
    String? errorMessage,
    PlaceDetailsResult? selectedLocationDetails,
  }) {
    return LocationAutocompleteState(
      status: status ?? this.status,
      predictions: predictions ?? this.predictions,
      query: query ?? this.query,
      errorMessage: errorMessage ?? this.errorMessage,
      selectedLocationDetails:
          selectedLocationDetails ?? this.selectedLocationDetails,
    );
  }

  @override
  List<Object?> get props =>
      [status, predictions, query, errorMessage, selectedLocationDetails];
}
