import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';
import '../create_service_home/bloc/create_service_bloc.dart';
import '../create_service_home/bloc/create_service_event.dart';
import '../create_service_home/bloc/create_service_state.dart';

class UploadImageService extends StatefulWidget {
  const UploadImageService({super.key});

  @override
  State<UploadImageService> createState() => _UploadImageServiceState();
}

class _UploadImageServiceState extends State<UploadImageService> {
  final ImagePicker _picker = ImagePicker();

  Future<void> selectImage() async {
    final XFile? pickedFile = await _picker.pickImage(
      source: ImageSource.gallery,
      maxHeight: 1024,
      maxWidth: 1024,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      final newImage = File(pickedFile.path);
      context.read<CreateServiceBloc>().add(
            UpdateServiceImage([
              ...context.read<CreateServiceBloc>().state.imageFiles,
              newImage
            ]),
          );
    }
  }

  void removeImage(int index) {
    context.read<CreateServiceBloc>().add(RemoveServiceImage(index));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateServiceBloc, CreateServiceState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (state.imageFiles.isNotEmpty)
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    ...List.generate(state.imageFiles.length, (index) {
                      return _buildImageItem(state.imageFiles[index], index);
                    }),
                    _buildAddImageButton(),
                  ],
                ),
              )
            else
              _buildUploadPrompt(),
          ],
        );
      },
    );
  }

  Widget _buildImageItem(File image, int index) {
    return Container(
      margin: EdgeInsets.only(right: 8.w),
      width: MediaQuery.of(context).size.width * 0.43,
      height: 150.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Image.file(
              image,
              fit: BoxFit.cover,
              width: MediaQuery.of(context).size.width * 0.4,
              height: 144.h,
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: GestureDetector(
              onTap: () => removeImage(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(AppAssets.trashSvg,
                    height: 16.h, width: 16.h),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: selectImage,
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(10),
        dashPattern: const [5, 3],
        color: Theme.of(context).textSecondary(context),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.43,
          height: 144.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
          ),
          child: Center(
            child: Icon(
              Icons.add,
              color: Theme.of(context).backgroundRed(context),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUploadPrompt() {
    return GestureDetector(
      onTap: selectImage,
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(10),
        dashPattern: const [5, 3],
        color: Theme.of(context).textSecondary(context),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                Gap(18.h),
                SvgPicture.asset(
                  AppAssets.uploadImageSvg,
                ),
                Gap(8.h),
                Text(
                  LocaleKeys.Clickheretouploadimage.tr(),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Gap(8.h),
                Text.rich(
                  TextSpan(
                    text: LocaleKeys.Supportsimageformats.tr(),
                    style: Theme.of(context).textTheme.labelSmall!.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                    children: [
                      TextSpan(
                        text: "10MB",
                        style: TextStyle(
                          color: Theme.of(context).secondary(context),
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
                Gap(18.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
