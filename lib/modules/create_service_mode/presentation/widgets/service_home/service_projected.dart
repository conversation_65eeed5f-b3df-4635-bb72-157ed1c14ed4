import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ServiceProjected extends StatefulWidget {
  const ServiceProjected({super.key});

  @override
  State<ServiceProjected> createState() => _ServiceProjectedState();
}

class _ServiceProjectedState extends State<ServiceProjected> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      maxLength: 200,
      style: Theme.of(context).textTheme.lightBodyLargeRegular,
      decoration: InputDecoration(
        labelText: "Projected completion time",
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        floatingLabelStyle:
            Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                  color: Theme.of(context).textSecondary(context),
                ),
        labelStyle: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
              color: Theme.of(context).textSecondary100(context),
            ),
        contentPadding:
            const EdgeInsets.only(left: 16, top: 12, bottom: 12, right: 84),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        counterText: '',
      ),
    );
  }
}
