import 'dart:io';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../create_service_home/bloc/create_service_bloc.dart';
import '../../create_service_home/bloc/create_service_event.dart';

class ServiceCertificate extends StatefulWidget {
  const ServiceCertificate({super.key});

  @override
  State<ServiceCertificate> createState() => _ServiceCertificateState();
}

class _ServiceCertificateState extends State<ServiceCertificate> {
  Future<void> _selectFile() async {
    try {
      // Show file selection options
      await _showFileSelectionOptions();
    } catch (e) {
      print('File selection error: $e');
      _showError('An error occurred while selecting file. Please try again.');
    }
  }

  Future<void> _showFileSelectionOptions() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      builder: (context) => SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: Icon(
                  Icons.image,
                  color: Theme.of(context).primaryColor,
                ),
                title: const Text(
                  'Select Image',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _selectImage();
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.picture_as_pdf,
                  color: Colors.red[600],
                ),
                title: const Text(
                  'Select PDF',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _selectPDF();
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final newFile = File(result.files.single.path!);
        final selectedCertificates =
            context.read<CreateServiceBloc>().state.certificateFiles;
        context.read<CreateServiceBloc>().add(
              UpdateServiceCertificate([...selectedCertificates, newFile]),
            );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Image added successfully'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 1),
            ),
          );
        }
      }
    } catch (e) {
      print('Image selection error: $e');
      _showError('Failed to select image. Please try again.');
    }
  }

  Future<void> _selectPDF() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        // Check file size (max 10MB)
        final fileSize = await file.length();
        if (fileSize > 10 * 1024 * 1024) {
          _showError('PDF file size must be less than 10MB');
          return;
        }

        final selectedCertificates =
            context.read<CreateServiceBloc>().state.certificateFiles;
        context.read<CreateServiceBloc>().add(
              UpdateServiceCertificate([...selectedCertificates, file]),
            );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('PDF added successfully'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 1),
            ),
          );
        }
      }
    } catch (e) {
      print('PDF selection error: $e');
      _showError('Failed to select PDF. Please try again.');
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _removeFile(int index) async {
    // Show confirmation dialog
    final shouldRemove = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove File'),
        content: const Text('Are you sure you want to remove this file?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (shouldRemove == true) {
      try {
        context.read<CreateServiceBloc>().add(RemoveServiceCertificate(index));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text('File removed successfully'),
                ],
              ),
              backgroundColor: Colors.orange[600],
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } catch (e) {
        print('Error removing file: $e');
        _showError('Failed to remove file. Please try again.');
      }
    }
  }

  String _getFileExtension(String path) {
    return path.split('.').last.toLowerCase();
  }

  bool _isImageFile(String path) {
    final extension = _getFileExtension(path);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  bool _isPdfFile(String path) {
    return _getFileExtension(path) == 'pdf';
  }

  @override
  Widget build(BuildContext context) {
    final selectedCertificates =
        context.select((CreateServiceBloc bloc) => bloc.state.certificateFiles);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(context),
        SizedBox(height: 16.h),
        _buildDescription(context),
        SizedBox(height: 16.h),
        _buildFileTypeInfo(context),
        SizedBox(height: 16.h),
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(
              opacity: animation,
              child: SizeTransition(
                sizeFactor: animation,
                child: child,
              ),
            );
          },
          child: selectedCertificates.isNotEmpty
              ? Column(
                  key: const ValueKey('files_selected'),
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selected Files (${selectedCertificates.length})',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: Theme.of(context).textSecondary(context),
                          ),
                    ),
                    Gap(8.h),
                    ...selectedCertificates.asMap().entries.map(
                          (entry) => _buildFileItem(entry.value, entry.key),
                        ),
                  ],
                )
              : Container(
                  key: const ValueKey('upload_prompt'),
                  child: _buildUploadPrompt(),
                ),
        ),
        SizedBox(height: 16.h),
        _buildFooterText(context),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          LocaleKeys.certificate.tr(),
          style: Theme.of(context).textTheme.titleMedium,
        ),
        Text(
          LocaleKeys.fieldOptional.tr(),
          style: Theme.of(context).textTheme.labelSmall,
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      LocaleKeys.certificateDescription.tr(),
      style: Theme.of(context).textTheme.lightBodyLargeRegular,
    );
  }

  Widget _buildFileTypeInfo(BuildContext context) {
    return Text(
      LocaleKeys.supportedFileTypes.tr(),
      style: Theme.of(context).textTheme.lightBodySmallRegular,
    );
  }

  Widget _buildFooterText(BuildContext context) {
    return Text(
      LocaleKeys.selfDeclaredInfo.tr(),
      style: Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
            color: Theme.of(context).greyScale600(context),
            fontStyle: FontStyle.italic,
          ),
    );
  }

  Widget _buildFileItem(File file, int index) {
    final fileName = file.path.split('/').last;
    final isImage = _isImageFile(file.path);
    final isPdf = _isPdfFile(file.path);

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // File preview
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
            ),
            child: isImage
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      file,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.broken_image,
                          color: Colors.grey[400],
                          size: 30,
                        );
                      },
                    ),
                  )
                : isPdf
                    ? Icon(
                        Icons.picture_as_pdf,
                        color: Colors.red[400],
                        size: 30,
                      )
                    : Icon(
                        Icons.insert_drive_file,
                        color: Colors.grey[400],
                        size: 30,
                      ),
          ),
          Gap(12.w),
          // File info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Gap(4.h),
                FutureBuilder<int>(
                  future: file.length(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      final sizeInKB = snapshot.data! / 1024;
                      final sizeText = sizeInKB > 1024
                          ? '${(sizeInKB / 1024).toStringAsFixed(1)} MB'
                          : '${sizeInKB.toStringAsFixed(1)} KB';
                      return Text(
                        '$sizeText • ${isImage ? 'Image' : isPdf ? 'PDF' : 'File'}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).textSecondary(context),
                            ),
                      );
                    }
                    return Text(
                      isImage
                          ? 'Image'
                          : isPdf
                              ? 'PDF'
                              : 'File',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).textSecondary(context),
                          ),
                    );
                  },
                ),
              ],
            ),
          ),
          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _selectFile,
                icon: Icon(
                  Icons.edit,
                  color: Theme.of(context).backgroundRed(context),
                  size: 20,
                ),
                tooltip: 'Replace file',
              ),
              IconButton(
                onPressed: () => _removeFile(index),
                icon: const Icon(
                  Icons.delete,
                  color: Colors.red,
                  size: 20,
                ),
                tooltip: 'Remove file',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUploadPrompt() {
    return GestureDetector(
      onTap: _selectFile,
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(6),
        strokeWidth: 1,
        color: Theme.of(context).greyScale400(context),
        dashPattern: const [6, 3],
        child: Container(
          height: 100.h,
          width: 100.h,
          decoration: BoxDecoration(
            color: Theme.of(context).greyScale100(context),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                AppAssets.gallerySvg,
                height: 24.h,
                width: 24.h,
              ),
              Text(
                LocaleKeys.addFile.tr(),
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
