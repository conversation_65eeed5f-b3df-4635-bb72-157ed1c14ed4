import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_bloc.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_event.dart';

import '../../../../../core/l10n/locale_keys.g.dart';

class ServiceStartPrice extends StatefulWidget {
  const ServiceStartPrice({super.key});

  @override
  State<ServiceStartPrice> createState() => _ServiceStartPriceState();
}

class _ServiceStartPriceState extends State<ServiceStartPrice> {
  final TextEditingController _controller = TextEditingController();
  final NumberFormat _numberFormat = NumberFormat("#,##0", "en_US");
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // _controller.addListener(_onTextChanged);
    _focusNode.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    // _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // void _onTextChanged() {
  //   final text = _controller.text.replaceAll(',', '');
  //   if (text.isNotEmpty) {
  // final formattedText = _formatNumber(text);
  // _controller.value = TextEditingValue(
  //   text: formattedText,
  //   selection: TextSelection.collapsed(offset: formattedText.length),
  // );
  // }
  // }

  // String _formatNumber(String value) {
  //   try {
  //     final number = int.parse(value);
  //     return _numberFormat.format(number);
  //   } catch (e) {
  //     return value;
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      onChanged: (value) {
        context
            .read<CreateServiceBloc>()
            .add(UpdateServiceStartPrice(double.parse(value)));
      },
      focusNode: _focusNode,
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      style: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
            color: Colors.black,
          ),
      decoration: InputDecoration(
        labelText: LocaleKeys.starting_price.tr(),
        floatingLabelStyle:
            Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                  color: Theme.of(context).textSecondary(context),
                ),
        labelStyle: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
              color: Theme.of(context).textSecondary100(context),
            ),
        prefixText: '\$ ',
        prefixStyle: Theme.of(context).textTheme.lightBodyLargeRegular,
        filled: true,
        fillColor: Colors.white,
        contentPadding:
            const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            color: Theme.of(context).lightGrey(context),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.black),
        ),
      ),
    );
  }
}
