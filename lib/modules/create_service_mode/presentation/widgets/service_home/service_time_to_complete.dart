import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../core/l10n/locale_keys.g.dart';
import '../../create_service_home/bloc/create_service_bloc.dart';
import '../../create_service_home/bloc/create_service_event.dart';
import '../../create_service_home/bloc/create_service_state.dart';

class ServiceTimeToComplete extends StatefulWidget {
  const ServiceTimeToComplete({super.key});

  @override
  State<ServiceTimeToComplete> createState() => _ServiceTimeToCompleteState();
}

class _ServiceTimeToCompleteState extends State<ServiceTimeToComplete> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant ServiceTimeToComplete oldWidget) {
    super.didUpdateWidget(oldWidget);
    final state = context.read<CreateServiceBloc>().state;
    if (_controller.text != state.timeToComplete) {
      _updateTextController(state.timeToComplete);
    }
  }

  void _updateTextController(String text) {
    _controller.text = text;
    _controller.selection = TextSelection.collapsed(offset: text.length);
  }

  void _onTextChanged(String value) {
    context.read<CreateServiceBloc>().add(UpdateServiceTimeToComplete(value));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateServiceBloc, CreateServiceState>(
      builder: (context, state) {
        if (_controller.text != state.timeToComplete) {
          _updateTextController(state.timeToComplete);
        }

        return TextField(
          controller: _controller,
          focusNode: _focusNode,
          keyboardType: TextInputType.text,
          onChanged: _onTextChanged,
          style: Theme.of(context).textTheme.lightBodyLargeRegular,
          decoration: InputDecoration(
            labelText: LocaleKeys.time_to_complete_order.tr(),
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            floatingLabelStyle:
                Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                      color: Theme.of(context).textSecondary(context),
                    ),
            labelStyle:
                Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                      color: Theme.of(context).textSecondary100(context),
                    ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        );
      },
    );
  }
}
