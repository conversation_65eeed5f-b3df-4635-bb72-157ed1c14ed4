// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../create_service_home/bloc/create_service_bloc.dart';
import '../../create_service_home/bloc/create_service_event.dart';
import '../../create_service_home/bloc/create_service_state.dart';

// ignore: must_be_immutable
class ServiceDescription extends StatefulWidget {
  String? lableText;

  ServiceDescription({
    super.key,
    this.lableText = LocaleKeys.ServiceDescription,
  });

  @override
  State<ServiceDescription> createState() => _ServiceDescriptionState();
}

class _ServiceDescriptionState extends State<ServiceDescription> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    _controller.addListener(() {
      context
          .read<CreateServiceBloc>()
          .add(UpdateServiceDescription(_controller.text));
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateServiceBloc, CreateServiceState>(
      builder: (context, state) {
        if (_controller.text != state.description) {
          _controller.text = state.description;
          _controller.selection =
              TextSelection.collapsed(offset: _controller.text.length);
        }

        return TextField(
          controller: _controller,
          focusNode: _focusNode,
          maxLength: 500,
          maxLines: 7,
          minLines: 5,
          decoration: InputDecoration(
            labelText: widget.lableText?.tr(),
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            floatingLabelStyle:
                Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                      color: Theme.of(context).textSecondary(context),
                    ),
            labelStyle:
                Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                      color: Theme.of(context).textSecondary100(context),
                    ),
            alignLabelWithHint: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            counterText: '',
            contentPadding:
                const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          ),
          style: Theme.of(context).textTheme.bodyLarge,
        );
      },
    );
  }
}
