import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/modules/create_service_mode/data/repositories/google_places_repository.dart';
import 'package:multime_app/modules/create_service_mode/presentation/blocs/location_autocomplete_bloc.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_home/service_location.dart';

/// Test page để verify rằng Google Places autocomplete hoạt động
class ServiceLocationTestPage extends StatefulWidget {
  const ServiceLocationTestPage({super.key});

  @override
  State<ServiceLocationTestPage> createState() =>
      _ServiceLocationTestPageState();
}

class _ServiceLocationTestPageState extends State<ServiceLocationTestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Service Location Test'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: BlocProvider(
        create: (context) =>
            LocationAutocompleteBloc(getIt<GooglePlacesRepository>()),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Test Google Places Autocomplete',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Nhập "lon", "hanoi", "ho chi minh" để test autocomplete',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 24),
              ServiceLocation(
                onLocationSelected: (location) {
                  print('✅ Location selected: ${location.description}');
                  print('   Place ID: ${location.placeId}');
                  print(
                      '   Main Text: ${location.structuredFormatting.mainText}');
                  print(
                      '   Secondary Text: ${location.structuredFormatting.secondaryText}');
                },
              ),
              const SizedBox(height: 20),
              const Text(
                '💡 Tips:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '• Nhập ít nhất 2-3 ký tự\n'
                '• Chờ 500ms để debounce hoạt động\n'
                '• Xem console logs để debug\n'
                '• API endpoint: /v1/admin/google-places/autocomplete',
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
