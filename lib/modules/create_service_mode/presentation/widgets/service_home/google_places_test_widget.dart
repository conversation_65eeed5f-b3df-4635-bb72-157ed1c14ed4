import 'package:flutter/material.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/modules/create_service_mode/data/repositories/google_places_repository.dart';

/// Widget test để kiểm tra Google Places API
class GooglePlacesTestWidget extends StatefulWidget {
  const GooglePlacesTestWidget({super.key});

  @override
  State<GooglePlacesTestWidget> createState() => _GooglePlacesTestWidgetState();
}

class _GooglePlacesTestWidgetState extends State<GooglePlacesTestWidget> {
  final TextEditingController _controller = TextEditingController();
  final GooglePlacesRepository _repository = getIt<GooglePlacesRepository>();
  String _result = '';
  bool _isLoading = false;

  Future<void> _testApi() async {
    if (_controller.text.isEmpty) return;

    setState(() {
      _isLoading = true;
      _result = '';
    });

    try {
      final response = await _repository.getAutocomplete(
        input: _controller.text,
        language: 'vi',
      );

      setState(() {
        _result = '''
API Response Successfully Parsed:
- Code: ${response.code}
- Message: ${response.message}
- Status: ${response.data.data.status}
- Predictions count: ${response.predictions.length}

Predictions:
${response.predictions.map((p) => '''
  • ${p.description}
    Place ID: ${p.placeId}
    Main Text: ${p.structuredFormatting.mainText}
    Secondary Text: ${p.structuredFormatting.secondaryText}
    Types: ${p.types.join(', ')}
''').join('\n')}
''';
      });
    } catch (e) {
      setState(() {
        _result = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Google Places API Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test API: /v1/admin/google-places/autocomplete',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      labelText: 'Enter location (e.g., "hanoi")',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testApi,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Test'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Result:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade50,
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result.isEmpty ? 'Enter a location and tap Test' : _result,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
