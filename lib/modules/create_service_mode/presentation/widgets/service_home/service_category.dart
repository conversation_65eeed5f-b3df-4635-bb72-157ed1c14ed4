import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_state.dart';
import 'package:multime_app/shared/widgets/sames/custom_dropdown_button.dart';
import '../../../../../core/base/api_response/status.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../create_service_home/bloc/create_service_bloc.dart';
import '../../create_service_home/bloc/create_service_event.dart';

class ServiceCategory extends StatefulWidget {
  const ServiceCategory({super.key});

  @override
  State<ServiceCategory> createState() => _ServiceCategoryState();
}

class _ServiceCategoryState extends State<ServiceCategory> {
  @override
  void initState() {
    super.initState();
    // Categories are already loaded in CreateServiceBloc from CreateServiceHome
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CreateServiceBloc, CreateServiceState>(
      listener: (context, state) {
        if (state.categories?.status == Status.error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to load categories: ${state.categories?.message ?? 'Unknown error'}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      builder: (context, state) {
        return Column(
          children: [
            18.verticalSpace,
            // Category dropdown with loading state handling
            state.categories?.status == Status.loading
                ? Container(
                    height: 50.h,
                    child: CustomDropDownButton(
                      arr: [],
                      hintText: LocaleKeys.CategoryServices.tr(),
                    ))
                : state.categories?.status == Status.error
                    ? Container(
                        height: 50.h,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.red),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            'Error loading categories: ${state.categories?.message ?? 'Unknown error'}',
                            style: TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    : CustomDropDownButton(
                        arr: state.categories?.data?.items
                                ?.where((category) =>
                                    category.name != null &&
                                    category.name!.trim().isNotEmpty)
                                .map((category) => category.name!)
                                .toList() ??
                            [],
                        hintText: LocaleKeys.CategoryServices.tr(),
                        onChanged: (selectedCategory) {
                          if (state.categories?.data?.items != null) {
                            final category = state.categories!.data!.items!
                                .firstWhere(
                                    (cat) => cat.name == selectedCategory);
                            context
                                .read<CreateServiceBloc>()
                                .add(UpdateServiceCategory(category.id ?? ''));
                          }
                        },
                      ),
            10.verticalSpace,
          ],
        );
      },
    );
  }
}
