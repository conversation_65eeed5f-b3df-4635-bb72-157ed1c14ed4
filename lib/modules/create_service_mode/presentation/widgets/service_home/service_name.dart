import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../../create_service_home/bloc/create_service_bloc.dart';
import '../../create_service_home/bloc/create_service_event.dart';
import '../../create_service_home/bloc/create_service_state.dart';

class ServiceName extends StatefulWidget {
  const ServiceName({super.key});

  @override
  State<ServiceName> createState() => _ServiceNameState();
}

class _ServiceNameState extends State<ServiceName> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isTypingComplete = false;

  @override
  void initState() {
    super.initState();

    _controller.addListener(_onTextChanged);
    _focusNode.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    if (mounted) {
      setState(() {
        _isTypingComplete = text.isNotEmpty;
      });
    }
    context.read<CreateServiceBloc>().add(UpdateServiceName(text));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateServiceBloc, CreateServiceState>(
      builder: (context, state) {
        if (_controller.text != state.name) {
          _controller.text = state.name;
          _controller.selection =
              TextSelection.collapsed(offset: _controller.text.length);
        }

        return Stack(
          children: [
            TextField(
              controller: _controller,
              focusNode: _focusNode,
              maxLength: 200,
              style: Theme.of(context).textTheme.lightBodyLargeRegular,
              decoration: InputDecoration(
                labelText: LocaleKeys.service_name.tr(),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
                floatingLabelStyle:
                    Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                          color: Theme.of(context).textSecondary(context),
                        ),
                labelStyle:
                    Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                          color: Theme.of(context).textSecondary100(context),
                        ),
                contentPadding: const EdgeInsets.only(
                    left: 16, top: 12, bottom: 12, right: 84),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                counterText: '',
              ),
            ),
            Positioned(
              right: 16,
              bottom: 16,
              child: Text(
                '${_controller.text.length}/200',
                style: Theme.of(context).textTheme.displaySmall!.copyWith(
                      color: _isTypingComplete
                          ? Colors.black
                          : Theme.of(context).textSecondary100(context),
                    ),
              ),
            ),
          ],
        );
      },
    );
  }
}
