import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/create_service_mode/data/repositories/google_places_repository.dart';
import 'package:multime_app/modules/create_service_mode/presentation/blocs/location_autocomplete_bloc.dart';
import 'package:multime_app/modules/create_service_mode/presentation/blocs/location_autocomplete_event.dart';
import 'package:multime_app/modules/create_service_mode/presentation/blocs/location_autocomplete_state.dart';

import 'package:multime_app/modules/create_service_mode/data/models/google_places_model.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_details_model.dart';

class ServiceLocation extends StatefulWidget {
  final Function(PlacePrediction)? onLocationSelected;
  final Function(PlaceDetailsResult)? onLocationDetailsReceived;
  final String? initialValue;

  const ServiceLocation({
    super.key,
    this.onLocationSelected,
    this.onLocationDetailsReceived,
    this.initialValue,
  });

  @override
  State<ServiceLocation> createState() => _ServiceLocationState();
}

class _ServiceLocationState extends State<ServiceLocation> {
  final TextEditingController _locationController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  late LocationAutocompleteBloc _locationBloc;

  @override
  void initState() {
    super.initState();
    _locationBloc = LocationAutocompleteBloc(getIt<GooglePlacesRepository>());

    // Set initial value if provided
    if (widget.initialValue != null) {
      _locationController.text = widget.initialValue!;
    }
  }

  @override
  void dispose() {
    _locationController.dispose();
    _focusNode.dispose();
    _locationBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _locationBloc,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          "Service Location",
          style: Theme.of(context).textTheme.titleMedium,
        ),
        SizedBox(height: 20),
        BlocListener<LocationAutocompleteBloc, LocationAutocompleteState>(
          listener: (context, state) {
            if (state.selectedLocationDetails != null &&
                widget.onLocationDetailsReceived != null) {
              widget.onLocationDetailsReceived!(state.selectedLocationDetails!);
            }
          },
          child:
              BlocBuilder<LocationAutocompleteBloc, LocationAutocompleteState>(
            builder: (context, state) {
              return Column(
                children: [
                  TextField(
                    controller: _locationController,
                    focusNode: _focusNode,
                    maxLength: 200,
                    style: Theme.of(context).textTheme.lightBodyLargeRegular,
                    onChanged: (value) {
                      _locationBloc.add(SearchLocationEvent(value));
                    },
                    decoration: InputDecoration(
                      labelText: "Enter service location",
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      floatingLabelStyle: Theme.of(context)
                          .textTheme
                          .lightBodySmallRegular
                          .copyWith(
                            color: Theme.of(context).textSecondary(context),
                          ),
                      labelStyle: Theme.of(context)
                          .textTheme
                          .lightBodyLargeRegular
                          .copyWith(
                            color: Theme.of(context).textSecondary100(context),
                          ),
                      contentPadding: const EdgeInsets.only(
                          left: 16, top: 12, bottom: 12, right: 84),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      counterText: '',
                      suffixIcon:
                          state.status == LocationAutocompleteStatus.loading
                              ? Container(
                                  width: 20,
                                  height: 20,
                                  padding: EdgeInsets.all(12),
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : null,
                    ),
                  ),
                  // Hiển thị danh sách suggestions
                  if (state.predictions.isNotEmpty) ...[
                    SizedBox(height: 8),
                    Container(
                      constraints: BoxConstraints(maxHeight: 200),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: state.predictions.length,
                        itemBuilder: (context, index) {
                          final prediction = state.predictions[index];
                          return ListTile(
                            dense: true,
                            leading: Icon(Icons.location_on, size: 20),
                            title: Text(
                              prediction.structuredFormatting.mainText,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                            subtitle: prediction.structuredFormatting
                                    .secondaryText.isNotEmpty
                                ? Text(
                                    prediction
                                        .structuredFormatting.secondaryText,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: Colors.grey.shade600,
                                        ),
                                  )
                                : null,
                            onTap: () {
                              _locationController.text = prediction.description;
                              _locationBloc
                                  .add(SelectLocationEvent(prediction));
                              _locationBloc.add(ClearLocationSearchEvent());
                              _focusNode.unfocus();

                              // Gọi callback để thông báo cho parent widget
                              if (widget.onLocationSelected != null) {
                                widget.onLocationSelected!(prediction);
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ] else if (state.status ==
                          LocationAutocompleteStatus.success &&
                      state.query.isNotEmpty &&
                      state.predictions.isEmpty) ...[
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.search_off,
                              color: Colors.orange.shade700, size: 20),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              "Không tìm thấy địa điểm phù hợp",
                              style: TextStyle(color: Colors.orange.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  if (state.status == LocationAutocompleteStatus.error) ...[
                    SizedBox(height: 8),
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red, size: 20),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              "Có lỗi xảy ra khi tìm kiếm địa điểm",
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ),
      ]),
    );
  }
}
