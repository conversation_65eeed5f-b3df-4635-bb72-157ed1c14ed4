import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ServiceEstablishCredibility extends StatefulWidget {
  const ServiceEstablishCredibility({super.key});

  @override
  State<ServiceEstablishCredibility> createState() =>
      _ServiceEstablishCredibilityState();
}

class _ServiceEstablishCredibilityState
    extends State<ServiceEstablishCredibility> {
  final TextEditingController _numberOfClientsController =
      TextEditingController();
  final TextEditingController _experienceController = TextEditingController();

  final FocusNode _focusNode = FocusNode();
  final FocusNode _focusNode2 = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Establish Your Credibility",
          style: Theme.of(context).textTheme.titleMedium,
        ),
        Gap(20.h),
        Text<PERSON>ield(
          controller: _numberOfClientsController,
          focusNode: _focusNode,
          maxLength: 200,
          style: Theme.of(context).textTheme.lightBodyLargeRegular,
          decoration: InputDecoration(
            labelText: "Number of Clients",
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            floatingLabelStyle:
                Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                      color: Theme.of(context).textSecondary(context),
                    ),
            labelStyle:
                Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                      color: Theme.of(context).textSecondary100(context),
                    ),
            contentPadding:
                const EdgeInsets.only(left: 16, top: 12, bottom: 12, right: 84),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            counterText: '',
          ),
        ),
        Gap(20.h),
        TextField(
          controller: _experienceController,
          focusNode: _focusNode2,
          maxLength: 200,
          style: Theme.of(context).textTheme.lightBodyLargeRegular,
          decoration: InputDecoration(
            labelText: "Experience",
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            floatingLabelStyle:
                Theme.of(context).textTheme.lightBodySmallRegular.copyWith(
                      color: Theme.of(context).textSecondary(context),
                    ),
            labelStyle:
                Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
                      color: Theme.of(context).textSecondary100(context),
                    ),
            contentPadding:
                const EdgeInsets.only(left: 16, top: 12, bottom: 12, right: 84),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            counterText: '',
          ),
        ),
      ],
    );
  }
}
