import 'package:flutter/material.dart';
import 'package:multime_app/modules/create_service_mode/data/models/google_places_model.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_home/service_location.dart';

/// Ví dụ về cách sử dụng ServiceLocation widget với Google Places Autocomplete
class ServiceLocationExample extends StatefulWidget {
  const ServiceLocationExample({super.key});

  @override
  State<ServiceLocationExample> createState() => _ServiceLocationExampleState();
}

class _ServiceLocationExampleState extends State<ServiceLocationExample> {
  PlacePrediction? selectedLocation;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Service Location Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ServiceLocation(
              onLocationSelected: (location) {
                setState(() {
                  selectedLocation = location;
                });
              },
            ),
            const SizedBox(height: 20),
            const Text(
              'Nhập địa điểm để xem gợi ý từ Google Places API',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
            if (selectedLocation != null) ...[
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Địa điểm đã chọn:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Địa chỉ: ${selectedLocation!.description}'),
                    Text('Place ID: ${selectedLocation!.placeId}'),
                    Text(
                        'Main Text: ${selectedLocation!.structuredFormatting.mainText}'),
                    Text(
                        'Secondary Text: ${selectedLocation!.structuredFormatting.secondaryText}'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
