import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_title.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/upload_image_service.dart';

import '../../../../../core/l10n/locale_keys.g.dart';
import '../../create_service_home/bloc/create_service_bloc.dart';
import '../../create_service_home/bloc/create_service_state.dart';

class ServiceImage extends StatelessWidget {
  const ServiceImage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateServiceBloc, CreateServiceState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ServiceTitle(
              title: LocaleKeys.service_image.tr(),
              onTap: () {},
              isHelp: false,
            ),
            Gap(16.h),
            const UploadImageService(),
            Gap(16.h),
            ..._buildGuidelines(context),
          ],
        );
      },
    );
  }

  List<Widget> _buildGuidelines(BuildContext context) {
    final guidelines = [
      LocaleKeys.upload_images_for_impressions.tr(),
      LocaleKeys.square_product_photo.tr(),
      LocaleKeys.include_at_least_two_images.tr(),
    ];

    return guidelines.map((text) => BulletPointText(text: text)).toList();
  }
}

class BulletPointText extends StatelessWidget {
  final String text;
  const BulletPointText({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('•'),
          Gap(8.w),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: Theme.of(context).secondaryDark(context),
                  ),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }
}
