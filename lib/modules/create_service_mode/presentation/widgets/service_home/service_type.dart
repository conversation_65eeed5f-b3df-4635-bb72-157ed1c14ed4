import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_bloc.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_event.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_state.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/service_title.dart';

class ServiceType extends StatelessWidget {
  const ServiceType({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreateServiceBloc, CreateServiceState>(
      builder: (context, state) {
        return RadioGroup(
          title: 'Service Type',
          options: const ['online', 'onsite', 'hybrid'],
          selectedValue: state.serviceType,
          onChanged: (value) =>
              context.read<CreateServiceBloc>().add(ChangeServiceType(value)),
        );
      },
    );
  }
}

class RadioGroup extends StatelessWidget {
  final String title;
  final List<String> options;
  final String selectedValue;
  final ValueChanged<String> onChanged;

  const RadioGroup({
    super.key,
    required this.title,
    required this.options,
    required this.selectedValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ServiceTitle(title: title, onTap: () {}, isHelp: false),
        ...options.map((option) => RadioListTile<String>(
              title: Text(option),
              value: option,
              groupValue: selectedValue,
              onChanged: (value) {
                if (value != null) onChanged(value);
              },
            )),
      ],
    );
  }
}
