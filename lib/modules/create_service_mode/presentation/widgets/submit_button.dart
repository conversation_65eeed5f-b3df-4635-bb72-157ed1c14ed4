import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:dio/dio.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_bloc.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_event.dart';
import 'package:multime_app/modules/create_service_mode/presentation/create_service_home/bloc/create_service_state.dart';
import 'package:multime_app/modules/create_service_mode/presentation/widgets/created_service_completed_dialog.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import 'package:http_parser/http_parser.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class SubmitButton extends StatelessWidget {
  const SubmitButton({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CreateServiceBloc, CreateServiceState>(
      listenWhen: (current, prev) =>
          current.createService?.status != prev.createService?.status,
      listener: (context, state) {
        // Show loader when create service is loading
        if (state.createService?.status == Status.loading) {
          AppLoader.show(context);
        } else {
          AppLoader.hide();
        }

        // Handle create service states
        if (state.createService?.status == Status.error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(state.createService?.message ??
                    'Failed to create service')),
          );
        }

        // Show success dialog when service creation is completed
        if (state.createService?.status == Status.completed) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return const CreatedServiceCompletedDialog();
            },
          );
        }
      },
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.resolveWith<Color>(
                (states) {
                  if (states.contains(WidgetState.disabled)) {
                    return Theme.of(context).errorLight(context);
                  }
                  return Theme.of(context).primary(context);
                },
              ),
              foregroundColor: WidgetStateProperty.all(Colors.white),
              padding: WidgetStateProperty.all(
                  const EdgeInsets.symmetric(vertical: 12)),
              shape: WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            onPressed: () async {
              if (state.createService?.status != Status.loading) {
                // Create FormData for the admin/service endpoint
                var formData = FormData();

                // Add basic fields
                formData.fields.addAll([
                  MapEntry('title', state.name),
                  MapEntry('description', state.description),
                  MapEntry('reject_notes', ''),
                  MapEntry('user_id', (gs.user?.id ?? 0).toString()),
                  MapEntry('category_id', state.category),
                  MapEntry('price', state.startPrice.toString()),
                  MapEntry('type', state.serviceType),
                  MapEntry('deliverables',
                      state.description), // Using description as deliverables
                  MapEntry(
                      'delivery_lead_time_day',
                      state.timeToComplete.isNotEmpty
                          ? state.timeToComplete
                          : '1'),
                  MapEntry(
                      'slug', state.name.toLowerCase().replaceAll(' ', '-')),
                  MapEntry('status', 'pending'),
                ]);

                // Add location fields if available
                if (state.locationDetails != null) {
                  formData.fields.addAll([
                    MapEntry(
                        'address', state.locationDetails!.formattedAddress),
                    MapEntry(
                        'longitude',
                        state.locationDetails!.geometry.location.lng
                            .toString()),
                    MapEntry(
                        'latitude',
                        state.locationDetails!.geometry.location.lat
                            .toString()),
                  ]);
                } else {
                  formData.fields.addAll([
                    MapEntry('address', ''),
                    MapEntry('longitude', '0'),
                    MapEntry('latitude', '0'),
                  ]);
                }

                // Add images
                for (int i = 0; i < state.imageFiles.length; i++) {
                  var file = state.imageFiles[i];
                  var multipartFile = await MultipartFile.fromFile(
                    file.path,
                    filename: file.path.split('/').last,
                    contentType: MediaType('image', file.path.split('.').last),
                  );
                  formData.files.add(MapEntry('image[$i]', multipartFile));
                }

                // Add cover image (use first image as cover if available)
                if (state.imageFiles.isNotEmpty) {
                  var coverFile = state.imageFiles.first;
                  var multipartFile = await MultipartFile.fromFile(
                    coverFile.path,
                    filename: coverFile.path.split('/').last,
                    contentType:
                        MediaType('image', coverFile.path.split('.').last),
                  );
                  formData.files.add(MapEntry('cover_image[0]', multipartFile));
                }

                // Dispatch the new event
                context
                    .read<CreateServiceBloc>()
                    .add(CreateServiceWithFormData(formData));
              }
            },
            child: Text(LocaleKeys.CreateYourService.tr()),
          ),
        );
      },
    );
  }
}
