import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';

class ServiceTitle extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  final bool isHelp;
  final Color? color;

  const ServiceTitle(
      {super.key,
      required this.title,
      required this.onTap,
      required this.isHelp,
      this.color});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: color,
              ),
        ),
        if (isHelp)
          IconButton(
            icon: SvgPicture.asset(AppAssets.helpCircle,
                width: 16.w, height: 16.h),
            onPressed: onTap,
          ),
      ],
    );
  }
}
