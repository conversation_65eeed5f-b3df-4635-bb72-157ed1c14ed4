import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/l10n/locale_keys.g.dart';

class CreatedServiceCompletedDialog extends StatefulWidget {
  const CreatedServiceCompletedDialog({super.key});

  @override
  State<CreatedServiceCompletedDialog> createState() =>
      _CreatedServiceCompletedDialogState();
}

class _CreatedServiceCompletedDialogState
    extends State<CreatedServiceCompletedDialog> {
  bool _isCopied = false;
  final String _serviceLink = "https://strongbody.ai/s/GVAQGq";

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _serviceLink)).then((_) {
      setState(() {
        _isCopied = true;
      });

      Future.delayed(const Duration(seconds: 2), () {
        setState(() {
          _isCopied = false;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Center(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Add close button at the top right
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).hintColor,
                      size: 24,
                    ),
                  ),
                ],
              ),
              Gap(24.h),
              Image.asset(
                "assets/svg/create_service_icon/service_completed.gif",
                width: 200.w,
                height: 200.h,
              ),
              Gap(24.h),
              Text(
                LocaleKeys.CreatedServiceCompleted.tr(),
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      color: Theme.of(context).successBase(context),
                      fontWeight: FontWeight.w700,
                    ),
              ),
              Gap(16.h),
              Text(
                LocaleKeys.ShareYourServices.tr(),
                style: Theme.of(context).textTheme.lightBodyLargeMedium,
                textAlign: TextAlign.center,
              ),
              Gap(34.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    AppAssets.messengerSvg,
                    width: 42.w,
                    height: 42.h,
                  ),
                  Gap(20.w),
                  SvgPicture.asset(
                    AppAssets.facebookSvg,
                    width: 42.w,
                    height: 42.h,
                  ),
                  Gap(20.w),
                  SvgPicture.asset(
                    AppAssets.twitterSvg,
                    width: 42.w,
                    height: 42.h,
                  ),
                  Gap(20.w),
                  SvgPicture.asset(
                    AppAssets.linkedinSvg,
                    width: 42.w,
                    height: 42.h,
                  ),
                ],
              ),
              Gap(30.h),
              Text(
                LocaleKeys.OrCopyLink.tr(),
                style: Theme.of(context).textTheme.lightBodySmallBold,
              ),
              Gap(20.h),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: _isCopied
                    ? Container(
                        key: const ValueKey(1),
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                            vertical: 18.h, horizontal: 10.w),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                "Copy Successful!",
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeRegular
                                    .copyWith(
                                      color: Theme.of(context)
                                          .successDark(context),
                                    ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Icon(
                              Icons.check_circle,
                              color: Theme.of(context).successDark(context),
                            ),
                          ],
                        ),
                      )
                    : Container(
                        key: const ValueKey(2),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 12.w),
                                child: Text(
                                  _serviceLink,
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightBodyLargeRegular,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.copy,
                                color: Theme.of(context).secondaryDark(context),
                              ),
                              onPressed: _copyToClipboard,
                            ),
                          ],
                        ),
                      ),
              ),
              Gap(30.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primary(context),
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () {
                    // Close the dialog first
                    Navigator.of(context).pop();

                    // Navigate back to home screen by popping until we reach the first route
                    // This should bring us back to the mode selection or main screen
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                  child: Text(
                    LocaleKeys.Done.tr(),
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
