import 'package:multime_app/shared/models/user/user_model.dart';

class UserModelResponse {
  final UserModel user;

  UserModelResponse({required this.user});

  factory UserModelResponse.fromJson(Map<String, dynamic> json) {
    return UserModelResponse(
      user: UserModel.fromJson(json['data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': user.toJson(),
    };
  }
}
