part of 'user_bloc.dart';

class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object?> get props => [];
}

class UpdateAvatarUserEvent extends UserEvent {
  final String? avatar;

  UpdateAvatarUserEvent(this.avatar);

  @override
  List<Object?> get props => [avatar];
}

class UpdateBackgroundUserEvent extends UserEvent {
  final String? backgroundPath;

  UpdateBackgroundUserEvent(this.backgroundPath);

  @override
  List<Object?> get props => [backgroundPath];
}

class UpdateProfileUserEvent extends UserEvent {
  final String? lastName;
  final String? firstName;
  final int? countryId;
  final String? image;
  final String? background;

  UpdateProfileUserEvent(
      {this.lastName,
      this.firstName,
      this.countryId,
      this.image,
      this.background});

  @override
  List<Object?> get props =>
      [lastName, firstName, countryId, image, background];
}

class FetchUserEvent extends UserEvent {
  FetchUserEvent();

  @override
  List<Object?> get props => [];
}
