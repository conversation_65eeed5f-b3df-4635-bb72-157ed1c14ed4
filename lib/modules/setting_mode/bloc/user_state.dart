part of 'user_bloc.dart';

class UserState extends Equatable {
  final bool? isLoading;
  final String errorMessage;
  final bool isSuccess;
  final UserModel? user;

  const UserState({
    this.isLoading = false,
    this.errorMessage = '',
    this.isSuccess = false,
    this.user,
  });

  factory UserState.initial() {
    return const UserState(
      isLoading: false,
      isSuccess: false,
      errorMessage: '',
    );
  }

  UserState copyWith({
    bool? isLoading,
    String? errorMessage,
    bool? isSuccess,
    UserModel? user,
  }) {
    return UserState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
      user: user ?? this.user,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        errorMessage,
        isSuccess,
        user,
      ];
}
