import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dio/dio.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:multime_app/shared/models/user/user_model.dart';

part 'user_event.dart';
part 'user_state.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final AuthRepository authRepository;
  final GlobalStorage globalStorage;
  UserBloc({required this.authRepository, required this.globalStorage})
      : super(UserState.initial()) {
    on<UpdateAvatarUserEvent>(_onUpdateAvatar);
    on<UpdateBackgroundUserEvent>(_onUpdateBackground);
    on<UpdateProfileUserEvent>(_onUpdateProfile);
    on<FetchUserEvent>(_onFetchUser);
  }

  void _onUpdateAvatar(
      UpdateAvatarUserEvent event, Emitter<UserState> emit) async {
    emit(state.copyWith(
      isLoading: true,
      errorMessage: '',
      isSuccess: false,
    ));

    try {
      final token = globalStorage.accessToken;
      final user = globalStorage.user;

      if (token == null || user == null) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: 'User not authenticated',
          isSuccess: false,
        ));
        return;
      }

      final formData = FormData.fromMap({
        'profile_picture': await MultipartFile.fromFile(
          event.avatar!,
          filename: event.avatar!.split('/').last,
        ),
        'user_id': user.id,
      });

      final response = await authRepository.updateAvatar(formData, token);

      if (response.status == Status.completed) {
        add(FetchUserEvent());

        emit(state.copyWith(
          isLoading: false,
          isSuccess: true,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: response.message ?? 'Update failed',
          isSuccess: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
        isSuccess: false,
      ));
    }
  }

  void _onUpdateBackground(
      UpdateBackgroundUserEvent event, Emitter<UserState> emit) async {
    // Validate input path
    if (event.backgroundPath == null || event.backgroundPath!.isEmpty) {
      debugPrint('No background path provided, skipping update');
      return;
    }

    emit(state.copyWith(
      isLoading: true,
      errorMessage: '',
      isSuccess: false,
    ));

    try {
      final token = globalStorage.accessToken;
      final user = globalStorage.user;

      if (token == null || user == null) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: 'User not authenticated. Please login.',
          isSuccess: false,
        ));
        return;
      }

      final formData = FormData.fromMap({
        'background_image': await MultipartFile.fromFile(
          event.backgroundPath!,
          filename: event.backgroundPath!.split('/').last,
        ),
        'user_id': user.id,
      });

      final response = await authRepository.updateBackground(formData, token);

      if (response.status == Status.completed) {
        // Try to get updated background from response, fallback to local path
        String? updatedBackgroundUrl;

        // Check if response contains user data with background picture
        if (response.data is Map<String, dynamic>) {
          final responseData = response.data as Map<String, dynamic>;
          if (responseData.containsKey('data') &&
              responseData['data'] is Map<String, dynamic>) {
            final userData = responseData['data'] as Map<String, dynamic>;
            updatedBackgroundUrl = userData['background_picture'] as String?;
          } else if (responseData.containsKey('background_picture')) {
            updatedBackgroundUrl =
                responseData['background_picture'] as String?;
          }
        }

        // Update background in GlobalStorage immediately
        final updatedUser = user.copyWith(
          backgroundPicture: updatedBackgroundUrl ?? event.backgroundPath,
        );
        String? userRole = globalStorage.userRole;
        await globalStorage.saveUser(updatedUser, userRole);

        // Emit the updated user state immediately
        emit(state.copyWith(
          isLoading: false,
          isSuccess: true,
          user: updatedUser,
        ));

        // No need to fetch from server - just use the updated local data
        debugPrint('Background updated successfully using local data');
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: response.message ?? 'Update failed',
          isSuccess: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
        isSuccess: false,
      ));
    }
  }

  void _onUpdateProfile(
      UpdateProfileUserEvent event, Emitter<UserState> emit) async {
    emit(state.copyWith(
      isLoading: true,
      errorMessage: '',
      isSuccess: false,
    ));

    try {
      final token = globalStorage.accessToken;
      final user = globalStorage.user;

      Map<String, dynamic> formDataMap = {};

      formDataMap['first_name'] = event.firstName ?? user!.firstName ?? '';

      formDataMap['last_name'] = event.lastName ?? user!.lastName ?? '';

      formDataMap['country_id'] = event.countryId ?? user!.countryId;

      if (event.image != null && event.image!.isNotEmpty) {
        formDataMap['profile_picture'] = await MultipartFile.fromFile(
          event.image!,
          filename: event.image!.split('/').last,
        );
      }

      if (event.background != null && event.background!.isNotEmpty) {
        formDataMap['background_picture'] = await MultipartFile.fromFile(
          event.background!,
          filename: event.background!.split('/').last,
        );
      }

      final formData = FormData.fromMap(formDataMap);

      final response = await authRepository.updateUser(formData, token!);

      if (response.status == Status.completed) {
        final updatedUser = user!.copyWith(
          firstName: response.data!.user.firstName,
          lastName: response.data!.user.lastName,
          countryId: response.data!.user.countryId,
          profilePicture: response.data!.user.profilePicture,
          backgroundPicture: response.data!.user.backgroundPicture,
        );
        String? userRole = globalStorage.userRole;
        await globalStorage.saveUser(updatedUser, userRole);

        emit(state.copyWith(
          isLoading: false,
          isSuccess: true,
          user: updatedUser,
        ));
      } else {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: response.message,
          isSuccess: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
        isSuccess: false,
      ));
    }
  }

  void _onFetchUser(FetchUserEvent event, Emitter<UserState> emit) async {
    // debugPrint('Fetching user data from local storage...');
    // emit(state.copyWith(
    //   isLoading: true,
    //   errorMessage: '',
    //   isSuccess: false,
    // ));

    try {
      // Get user data from local storage only
      final user = globalStorage.user;

      if (user != null) {
        // debugPrint('Using cached user data: ${user.fullName}');
        // emit(state.copyWith(
        //   isLoading: false,
        //   isSuccess: true,
        //   user: user,
        // ));
      } else {
        debugPrint('No cached user data available');
        emit(state.copyWith(
          isLoading: false,
          isSuccess: false,
          errorMessage: 'No user data available. Please login.',
        ));
      }
    } catch (e) {
      debugPrint('Error in _onFetchUser: $e');
      emit(state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
        isSuccess: false,
        user: globalStorage.user,
      ));
    }
  }
}
