import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';

import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import 'allow_call_sheet.dart';
import 'allow_messaging_sheet.dart';

class SettingPrivacyPage extends StatefulWidget {
  const SettingPrivacyPage({super.key});

  @override
  State<SettingPrivacyPage> createState() => _SettingPrivacyPageState();
}

class _SettingPrivacyPageState extends State<SettingPrivacyPage> {
  bool _showViewedStatus = false;
  bool _allowViewingAndCommenting = false;
  bool _suggestFriendsFromContacts = true;
  String _messagingOption = LocaleKeys.everybody.tr();
  String _callOption = LocaleKeys.friend.tr();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).greyScale50(context),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          backgroundColor: Theme.of(context).greyScale50(context),
          title: Text(
            LocaleKeys.privacy.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(10.h),
            Text(
              LocaleKeys.textAndCall.tr(),
              style: Theme.of(context).textTheme.lightBodyXLargeBold,
            ),
            Gap(15.h),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: SvgPicture.asset(AppAssets.eyeSvg,
                      width: 20.w, height: 20.h, fit: BoxFit.cover),
                  title: Text(
                    LocaleKeys.showViewedStatus.tr(),
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeMedium
                        .copyWith(
                          color: Theme.of(context).blackPrimary(context),
                        ),
                  ),
                  trailing: CupertinoSwitch(
                    value: _showViewedStatus,
                    onChanged: (value) {
                      setState(() {
                        _showViewedStatus = value;
                      });
                    },
                  ),
                ),
                const Divider(
                  thickness: 1,
                  color: Color.fromARGB(255, 190, 190, 190),
                ),
                GestureDetector(
                  onTap: _handleMessagingOptions,
                  child: ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: SvgPicture.asset(
                      AppAssets.messagesSvg,
                      width: 20.w,
                      height: 20.h,
                      fit: BoxFit.contain,
                    ),
                    title: Text(
                      LocaleKeys.allowMessaging.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeMedium
                          .copyWith(
                            color: Theme.of(context).blackPrimary(context),
                          ),
                    ),
                    trailing: Text(_messagingOption,
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeRegular
                            .copyWith(
                              color: Theme.of(context).greyScale600(context),
                            )),
                  ),
                ),
                const Divider(
                  thickness: 1,
                  color: Color.fromARGB(255, 190, 190, 190),
                ),
                GestureDetector(
                  onTap: _handleCallOptions,
                  child: ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: SvgPicture.asset(
                      AppAssets.callCallingSvg,
                      width: 20.w,
                      height: 20.h,
                      fit: BoxFit.contain,
                    ),
                    title: Text(
                      LocaleKeys.allowCall.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeMedium
                          .copyWith(
                            color: Theme.of(context).blackPrimary(context),
                          ),
                    ),
                    trailing: Text(_callOption,
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyLargeRegular
                            .copyWith(
                              color: Theme.of(context).greyScale600(context),
                            )),
                  ),
                ),
                const Divider(
                  thickness: 1,
                  color: Color.fromARGB(255, 190, 190, 190),
                ),
              ],
            ),
            Gap(16.h),
            Text(
              "Diary",
              style: Theme.of(context).textTheme.lightBodyXLargeBold,
            ),
            Gap(10.h),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: SvgPicture.asset(AppAssets.message2Svg,
                  width: 20.w,
                  height: 20.h,
                  fit: BoxFit.contain,
                  colorFilter: ColorFilter.mode(
                      Theme.of(context).blackPrimary(context),
                      BlendMode.srcIn)),
              title: Text(
                LocaleKeys.allowViewingAndCommenting.tr(),
                style:
                    Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                          color: Theme.of(context).blackPrimary(context),
                        ),
              ),
              trailing: CupertinoSwitch(
                value: _allowViewingAndCommenting,
                onChanged: (value) {
                  setState(() {
                    _allowViewingAndCommenting = value;
                  });
                },
              ),
            ),
            const Divider(
              thickness: 1,
              color: Color.fromARGB(255, 190, 190, 190),
            ),
            Gap(16.h),
            Text(
              LocaleKeys.friendSource.tr(),
              style: Theme.of(context).textTheme.lightBodyXLargeBold,
            ),
            Gap(10.h),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: SvgPicture.asset(AppAssets.contacts1Svg,
                  width: 20.w, height: 20.h, fit: BoxFit.contain),
              title: Text(
                LocaleKeys.suggestFriendsFromContacts.tr(),
                style:
                    Theme.of(context).textTheme.lightBodyLargeSemiBold.copyWith(
                          color: Theme.of(context).blackPrimary(context),
                        ),
              ),
              subtitle: Text(
                LocaleKeys.addContactsToMultiMe.tr(),
                style: Theme.of(context).textTheme.titleSmall,
              ),
              trailing: CupertinoSwitch(
                value: _suggestFriendsFromContacts,
                onChanged: (value) {
                  setState(() {
                    _suggestFriendsFromContacts = value;
                  });
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleMessagingOptions() async {
    final option = await showMessagingOptionsSheet(
      context,
      currentOption: _messagingOption,
    );

    if (option != null) {
      setState(() {
        _messagingOption = option;
      });
    }
  }

  Future<void> _handleCallOptions() async {
    final option = await showCallOptionsSheet(
      context,
      currentOption: _callOption,
    );

    if (option != null) {
      setState(() {
        _callOption = option;
      });
    }
  }
}
