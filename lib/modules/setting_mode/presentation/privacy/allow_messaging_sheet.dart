// Tạo file mới: lib/modules/setting_mode/presentation/privacy/call_options_sheet.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';

import '../widgets/privacy_bottom_sheet.dart';

// Hàm hiển thị bottom sheet cho Call Options
Future<String?> showMessagingOptionsSheet(
    BuildContext context, {
      required String currentOption,
    }) async {
  final options = [
    PrivacyOptionItem(
      text: LocaleKeys.everybody.tr(),
      icon: AppAssets.peopleSvg,
      isSelected: currentOption == LocaleKeys.everybody.tr(),
    ),
    PrivacyOptionItem(
      text: LocaleKeys.friend.tr(),
      icon: AppAssets.profile2User,
      isSelected: currentOption == LocaleKeys.friend.tr(),
    ),
  ];

  final selectedIndex = await showPrivacyOptionsSheet(
    context,
    title: LocaleKeys.whoCanTextingYou.tr(),
    options: options,
  );

  if (selectedIndex != null) {
    switch (selectedIndex) {
      case 0:
        return LocaleKeys.everybody.tr();
      case 1:
        return LocaleKeys.friend.tr();
      default:
        return null;
    }
  }

  return null;
}
