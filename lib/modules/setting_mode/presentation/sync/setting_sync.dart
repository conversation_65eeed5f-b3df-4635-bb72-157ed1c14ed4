import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';

import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class SettingSyncPage extends StatefulWidget {
  const SettingSyncPage({super.key});

  @override
  State<SettingSyncPage> createState() => _SettingSyncPageState();
}

class _SettingSyncPageState extends State<SettingSyncPage> {
  bool _dataSynchronization = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          title: Text(
            LocaleKeys.syncSetting.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: SvgPicture.asset(AppAssets.cloudConnectionSvg,
                  width: 20.w, height: 20.h, fit: BoxFit.cover),
              title: Text(
                LocaleKeys.dataSynchronization.tr(),
                style: Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                      color: Theme.of(context).blackPrimary(context),
                    ),
              ),
              trailing: CupertinoSwitch(
                value: _dataSynchronization,
                onChanged: (value) {
                  setState(() {
                    _dataSynchronization = value;
                  });
                },
              ),
            ),
            Divider(
              thickness: 1,
              color: Theme.of(context).greyScale300(context),
            ),
            GestureDetector(
              onTap: () {},
              child: ListTile(
                contentPadding: EdgeInsets.zero,
                leading: SvgPicture.asset(
                  AppAssets.backSquareSvg,
                  width: 20.w,
                  height: 20.h,
                  fit: BoxFit.contain,
                ),
                title: Text(
                  LocaleKeys.backupOptions.tr(),
                  style:
                      Theme.of(context).textTheme.lightBodyLargeMedium.copyWith(
                            color: Theme.of(context).blackPrimary(context),
                          ),
                ),
                trailing: Text(LocaleKeys.everyday.tr(),
                    style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                          color: Theme.of(context).greyScale600(context),
                        )),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
