// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/theme.dart';

// ignore: must_be_immutable
class CardSettingWidget extends StatelessWidget {
  String svgIcon;
  String titleSetting;
  String subTitleSetting;
  int sizeIcon;
  Function()? onTap;

  CardSettingWidget({
    super.key,
    required this.svgIcon,
    required this.titleSetting,
    required this.subTitleSetting,
    this.sizeIcon = 40,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const SizedBox(width: 2),
          Padding(
            padding:
                EdgeInsets.symmetric(horizontal: sizeIcon == 40 ? 0.0 : 8.0.w),
            child: SvgPicture.asset(svgIcon,
                width: sizeIcon.w, height: sizeIcon.h, fit: BoxFit.cover),
          ),
          SizedBox(width: 10.w),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(titleSetting,
                  style: TextStyle(
                    color: Theme.of(context).blackPrimary(context),
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  )),
              Text(subTitleSetting,
                  style: TextStyle(
                    color: Theme.of(context).greyScale600(context),
                    fontSize: 12.sp,
                  )),
            ],
          )),
        ],
      ),
    );
  }
}
