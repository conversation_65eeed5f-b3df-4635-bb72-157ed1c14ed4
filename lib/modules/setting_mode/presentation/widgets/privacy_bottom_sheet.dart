// Tạo file mới: lib/modules/setting_mode/presentation/widgets/privacy_options_sheet.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../core/constants/app_assets.dart';

class PrivacyOptionItem {
  final String text;
  final String? icon;
  final bool isSelected;

  PrivacyOptionItem({
    required this.text,
    this.icon,
    this.isSelected = false,
  });
}

class PrivacyOptionsSheet extends StatelessWidget {
  final String title;
  final List<PrivacyOptionItem> options;
  final Function(int) onOptionSelected;

  const PrivacyOptionsSheet({
    Key? key,
    required this.title,
    required this.options,
    required this.onOptionSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Tiêu đề
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Center(
              child: Text(
                title,
                style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                textAlign: TextAlign.center,
              ),
            ),
          ),

          const Divider(
            thickness: 1,
            color: Color.fromARGB(255, 190, 190, 190),
          ),

          // Options
          ...List.generate(
            options.length,
            (index) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildOptionItem(context, index),
                  if (index < options.length - 1)
                    const Divider(
                      thickness: 1,
                      color: Color.fromARGB(255, 190, 190, 190),
                    ),
                ],
              );
            },
          ),
          Gap(16.h + MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildOptionItem(BuildContext context, int index) {
    final option = options[index];

    final Color textColor = option.isSelected
        ? Theme.of(context).blackPrimary(context)
        : Theme.of(context).greyScale500(context);

    return InkWell(
      onTap: () => onOptionSelected(index),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
        child: Row(
          children: [
            if (option.icon != null) ...[
              SvgPicture.asset(
                option.icon!,
                width: 24.w,
                height: 24.h,
                colorFilter: ColorFilter.mode(
                  textColor,
                  BlendMode.srcIn,
                ),
              ),
              Gap(12.w)
            ],
            Expanded(
              child: Text(
                option.text,
                style: Theme.of(context)
                    .textTheme
                    .lightBodyLargeMedium
                    .copyWith(color: textColor),
              ),
            ),
            if (option.isSelected)
              SvgPicture.asset(
                AppAssets.checkSvg,
                width: 24.w,
                height: 24.h,
                colorFilter: const ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

Future<int?> showPrivacyOptionsSheet(
  BuildContext context, {
  required String title,
  required List<PrivacyOptionItem> options,
}) {
  return showModalBottomSheet<int>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => PrivacyOptionsSheet(
      title: title,
      options: options,
      onOptionSelected: (index) {
        Navigator.of(context).pop(index);
      },
    ),
  );
}
