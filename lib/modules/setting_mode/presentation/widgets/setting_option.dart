import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../core/constants/app_assets.dart';

class SettingOption extends StatefulWidget {
  final String icon;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  final Widget? widget;
  final Color? color;
  final bool expandable;
  final Widget? expandedContent;

  const SettingOption({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.onTap,
    this.widget,
    this.color,
    this.expandable = false,
    this.expandedContent,
  });

  @override
  State<SettingOption> createState() => _SettingOptionState();
}

class _SettingOptionState extends State<SettingOption> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: widget.onTap,
          child: Padding(
            padding: EdgeInsets.only(left: 8.w, top: 16.h, bottom: 16.h),
            child: Row(
              children: [
                SizedBox(
                  width: 36.w,
                  height: 36.h,
                  child: Center(
                    child: SvgPicture.asset(
                      widget.icon,
                      height: 24.h,
                      width: 24.w,
                      colorFilter: widget.color != null
                          ? ColorFilter.mode(widget.color!, BlendMode.srcIn)
                          : null,
                    ),
                  ),
                ),
                Gap(16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Gap(4.h),
                      Text(
                        widget.subtitle,
                        style:
                            Theme.of(context).textTheme.lightBodySmallRegular,
                      ),
                    ],
                  ),
                ),
                if (widget.widget != null) widget.widget!,
                if (widget.expandable)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                    child: SvgPicture.asset(
                      _isExpanded
                          ? AppAssets.arrowDownSvg
                          : AppAssets.arrowRightSvg,
                    ),
                  ),
              ],
            ),
          ),
        ),
        if (_isExpanded && widget.expandable && widget.expandedContent != null)
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isExpanded ? null : 0,
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
            child: widget.expandedContent,
          ),
      ],
    );
  }
}
