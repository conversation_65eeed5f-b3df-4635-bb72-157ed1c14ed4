import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class CardMode extends StatelessWidget {
  const CardMode({
    super.key,
    required this.callback,
    this.isCheck = false,
    required this.title,
    required this.subtitle,
    required this.image,
  });
  final VoidCallback callback;
  final bool isCheck;
  final String title, subtitle, image;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: callback,
      child: Container(
        height: 68.h,
        width: double.maxFinite,
        padding: Platform.isIOS
            ? EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h)
            : EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppRadius.radius8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              image,
              width: 38.w,
              height: 38.h,
              fit: BoxFit.contain,
            ),
            Gap(12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.lightBodyLargeBold,
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.lightBodySmallRegular,
                  ),
                ],
              ),
            ),
            if (isCheck)
              SizedBox(
                width: 28.h,
                height: 28.h,
                child: Image.asset(
                  AppAssets.tickCircle,
                  fit: BoxFit.contain,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
