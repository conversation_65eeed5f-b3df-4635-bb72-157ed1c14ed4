// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/theme.dart';

// ignore: must_be_immutable
class UserSetting extends StatelessWidget {
  String imageUser;
  String name;
  UserSetting({
    super.key,
    required this.imageUser,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          height: 55.h,
          width: 55.w,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(100),
            child: Image.network(imageUser),
          ),
        ),
        SizedBox(width: 20.w),
        Text(
          name,
          style: TextStyle(
            color: Theme.of(context).blackPrimary(context),
            fontSize: 17.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }
}
