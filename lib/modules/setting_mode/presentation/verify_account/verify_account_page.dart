import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import 'widgets/biometric_switch.dart';
import 'widgets/security_title_widget.dart';

class VerifyAccountPage extends StatefulWidget {
  const VerifyAccountPage({super.key});

  @override
  State<VerifyAccountPage> createState() => _VerifyAccountPageState();
}

class _VerifyAccountPageState extends State<VerifyAccountPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          title: Text("Verify Account",
              style: Theme.of(context).textTheme.lightHeadingMedium),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            Gap(20.h),
            // Security status
            Column(
              children: [
                SvgPicture.asset(
                  AppAssets.icon_weak_securitySvg,
                  width: 80.w,
                  height: 80.h,
                ),
                Gap(10.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Security status:  ",
                      style: Theme.of(context).textTheme.lightBodyXLargeMedium,
                    ),
                    const Text(
                      "Weak",
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.amber),
                    ),
                  ],
                ),
                Text(
                  "3 security issues to address",
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(color: Theme.of(context).greyScale600(context)),
                ),
              ],
            ),
            Gap(20.h),
            // Security settings
            SecurityTitleWidget(
              icon: AppAssets.lock_icon_svg,
              title: "Two-factor authentication",
              warning: true,
            ),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).greyScale50(context),
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.all(15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 230.w,
                    child: Text(
                        "Enable two-factor authentication for this account.",
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular),
                  ),
                  // Switch check user
                  const BiometricSwitch(),
                ],
              ),
            ),
            const Divider(),
            SecurityTitleWidget(
              icon: AppAssets.user_tag_icon_svg,
              title: "Account identification",
              warning: true,
              expanded: true,
            ),
            SecurityTitleWidget(
              icon: AppAssets.sms_icon_svg,
              title: "Email",
              warning: true,
              expanded: true,
            ),
            SecurityTitleWidget(
              icon: AppAssets.laptopSvg,
              title: "Other devices are logged in",
              check: true,
              expanded: true,
            ),
            Gap(10.h),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: SvgPicture.asset(AppAssets.update_icon_svg),
              title: Text("Auto update",
                  style: Theme.of(context).textTheme.lightBodyMediumRegular),
              subtitle: Text(
                "Automatically update to the latest version of MultiMe",
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumRegular
                    .copyWith(color: Theme.of(context).greyScale600(context)),
              ),
              trailing: CupertinoSwitch(
                value: false,
                onChanged: (value) {},
              ),
            ),
          ],
        ),
      ),
    );
  }
}
