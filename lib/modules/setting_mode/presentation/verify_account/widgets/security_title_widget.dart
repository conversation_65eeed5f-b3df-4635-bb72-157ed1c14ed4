// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

// ignore: must_be_immutable
class SecurityTitleWidget extends StatelessWidget {
  String icon;
  String title;
  String? subtitle;
  bool? warning;
  bool? check;
  Widget? trailing;
  bool? expanded;
  void Function()? onTap;

  SecurityTitleWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.warning = false,
    this.check = false,
    this.trailing,
    this.expanded = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: SvgPicture.asset(
              icon,
              width: 25.w,
              height: 25.h,
            ),
            title: Text(title,
                style: Theme.of(context).textTheme.lightBodyMediumRegular),
            subtitle: subtitle != null
                ? Text(subtitle!,
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyMediumRegular
                        .copyWith(fontWeight: FontWeight.w400))
                : null,
            trailing: trailing ??
                (warning!
                    ? const Icon(Icons.warning_amber_outlined,
                        color: Colors.amber)
                    : check!
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : null),
          ),
          if (expanded!) const Divider(),
        ],
      ),
    );
  }
}
