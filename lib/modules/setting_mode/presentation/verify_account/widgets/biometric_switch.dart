import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';




class BiometricSwitch extends StatefulWidget {
  const BiometricSwitch({super.key});

  @override
  State<BiometricSwitch> createState() => _BiometricSwitchState();
}

class _BiometricSwitchState extends State<BiometricSwitch> {
  bool _isEnabled = false;
  final LocalAuthentication auth = LocalAuthentication();

  Future<void> _handleSwitch(bool newValue) async {
    if (newValue) {
      try {
        bool canCheck = await auth.canCheckBiometrics;
        bool isSupported = await auth.isDeviceSupported();

        if (!canCheck || !isSupported) {
          _showMessage('Thiết bị không hỗ trợ xác thực sinh trắc học');
          return;
        }

        bool authenticated = await auth.authenticate(
          localizedReason: '<PERSON>ui lòng xác thực để bật',
          options: const AuthenticationOptions(biometricOnly: true),
        );

        if (authenticated) {
          setState(() {
            _isEnabled = true;
          });
          _showMessage('<PERSON>á<PERSON> thực thành công!');
        } else {
          _showMessage('Xác thực thất bại.');
        }
      } catch (e) {
        _showMessage('Lỗi: $e');
      }
    } else {
      // Nếu tắt không cần xác thực
      setState(() {
        _isEnabled = false;
      });
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoSwitch(
      value: _isEnabled,
      onChanged: _handleSwitch,
    );
  }
}
