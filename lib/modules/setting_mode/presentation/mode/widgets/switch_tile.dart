import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class SettingSwitchTile extends StatelessWidget {
  final String leadingIcon;
  final String title;
  final String subtitle;
  final bool value;
  final ValueChanged<bool> onChanged;

  const SettingSwitchTile({
    super.key,
    required this.leadingIcon,
    required this.title,
    required this.subtitle,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: SvgPicture.asset(leadingIcon),
      title: Text(
        title,
        style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
              color: Theme.of(context).greyScale600(context),
            ),
      ),
      trailing: CupertinoSwitch(value: value, onChanged: onChanged),
    );
  }
}
