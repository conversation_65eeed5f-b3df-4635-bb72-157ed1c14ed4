import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/setting_mode/presentation/widgets/card_setting.dart';

import '../../../../app/routers/routers_name.dart';

class SettingModelPage extends StatefulWidget {
  const SettingModelPage({super.key});

  @override
  State<SettingModelPage> createState() => _SettingModelPageState();
}

class _SettingModelPageState extends State<SettingModelPage> {
  bool _turnOnNotifications = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          title: Text(
            LocaleKeys.settingMode.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(AppSpacing.padding10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppRadius.radius12),
                  color: Theme.of(context).whitePrimary(context),
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15.0, vertical: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.turnOnNotificationsForAllModes.tr(),
                        style:
                            Theme.of(context).textTheme.lightBodyMediumRegular,
                      ),
                      CupertinoSwitch(
                        value: _turnOnNotifications,
                        onChanged: (value) {
                          setState(() {
                            _turnOnNotifications = value;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
              Gap(7.h),
              CardMode(
                callback: () => context.push(RouteName.settingSocialMode),
                title: LocaleKeys.socialMode.tr(),
                subtitle: LocaleKeys.manageYourConnection.tr(),
                image: AppAssets.social_icon,
              ),
              Gap(7.h),
              CardMode(
                callback: () => context.push(RouteName.settingBusinessMode),
                title: LocaleKeys.businessMode.tr(),
                subtitle: LocaleKeys.connectToPartnerChances.tr(),
                image: AppAssets.business_icon,
              ),
              Gap(7.h),
              CardMode(
                callback: () => context.push(RouteName.settingNewMode),
                title: LocaleKeys.newsMode.tr(),
                subtitle: LocaleKeys.readNews.tr(),
                image: AppAssets.new_icon,
              ),
              Gap(7.h),
              CardMode(
                callback: () => context.push(RouteName.settingMarketplanceMode),
                title: LocaleKeys.marketplace.tr(),
                subtitle: LocaleKeys.shoppingProductsAndService.tr(),
                image: AppAssets.market_icon,
              ),
              Gap(7.h),
              CardMode(
                callback: () => context.push(RouteName.settingDatingMode),
                title: LocaleKeys.datingMode.tr(),
                subtitle: LocaleKeys.findRealPartnerForYou.tr(),
                image: AppAssets.dating_icon,
              ),
              Gap(7.h),
              CardMode(
                callback: () {},
                title: LocaleKeys.strongBodyAi.tr(),
                subtitle: LocaleKeys.findRealPartnerForYou.tr(),
                image: AppAssets.strongbody_icon,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
