import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../widgets/setting_divider.dart';
import '../widgets/setting_list_tile.dart';
import '../widgets/switch_tile.dart';

class SettingBusinessMode extends StatefulWidget {
  const SettingBusinessMode({super.key});

  @override
  State<SettingBusinessMode> createState() => _SettingBusinessModeState();
}

class _SettingBusinessModeState extends State<SettingBusinessMode> {
  bool _isNotification = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBar(
            title: Text(
              LocaleKeys.SettingBusinessMode.tr(),
              style: Theme.of(context).textTheme.lightHeadingMedium,
            ),
            centerTitle: true,
            leading: IconButton(
              icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              SettingListTile(
                leadingIcon: AppAssets.icon_profiletick,
                title: LocaleKeys.Postoffer.tr(),
                subtitle: LocaleKeys.ShareOpportunities.tr(),
              ),
              const SettingDivider(),
              SettingListTile(
                leadingIcon: AppAssets.icon_notetext,
                title: LocaleKeys.postRequest.tr(),
                subtitle: LocaleKeys.RequestSupport.tr(),
              ),
              const SettingDivider(),
              SettingSwitchTile(
                leadingIcon: AppAssets.icon_notification,
                title: LocaleKeys.Notification.tr(),
                subtitle: LocaleKeys.GetNotifiedWhenIHaveSomething.tr(),
                value: _isNotification,
                onChanged: (value) => setState(() => _isNotification = value),
              ),
            ],
          ),
        ));
  }
}
