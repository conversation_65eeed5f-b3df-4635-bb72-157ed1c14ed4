import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../../core/l10n/locale_keys.g.dart';
import '../widgets/setting_divider.dart';
import '../widgets/setting_list_tile.dart';
import '../widgets/switch_tile.dart';

class SettingDatingMode extends StatefulWidget {
  const SettingDatingMode({super.key});

  @override
  State<SettingDatingMode> createState() => _SettingDatingModeState();
}

class _SettingDatingModeState extends State<SettingDatingMode> {
  bool _commentAnonymous = false;
  bool _isNotification = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          title: Text(LocaleKeys.SettingDatingMode.tr(),
              style: Theme.of(context).textTheme.lightHeadingMedium),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            SettingListTile(
              leadingIcon: AppAssets.icon_user,
              title: LocaleKeys.EditprofileDatingmode.tr(),
              subtitle: LocaleKeys.Selecteyeprotectionmode.tr(),
            ),
            const SettingDivider(),
            SettingListTile(
              leadingIcon: AppAssets.profile2User,
              title: LocaleKeys.Managewhoicanview.tr(),
              subtitle: LocaleKeys.Selecteyeprotectionmode.tr(),
            ),
            const SettingDivider(),
            SettingSwitchTile(
              leadingIcon: AppAssets.icon_anonymous,
              title: LocaleKeys.Commentanonymous.tr(),
              subtitle: LocaleKeys.Onlypeopleyoulikecanfindyou.tr(),
              value: _commentAnonymous,
              onChanged: (value) => setState(() => _commentAnonymous = value),
            ),
            const SettingDivider(),
            SettingSwitchTile(
              leadingIcon: AppAssets.icon_notification,
              title: LocaleKeys.Notification.tr(),
              subtitle: LocaleKeys.GetNotifiedWhenYouMatch.tr(),
              value: _isNotification,
              onChanged: (value) => setState(() => _isNotification = value),
            ),
          ],
        ),
      ),
    );
  }
}
