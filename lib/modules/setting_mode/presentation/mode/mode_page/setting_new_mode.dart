import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import '../../../../../core/constants/app_assets.dart';
import '../../../../../core/l10n/locale_keys.g.dart';
import '../widgets/setting_divider.dart';
import '../widgets/setting_list_tile.dart';
import '../widgets/switch_tile.dart';

class SettingNewMode extends StatefulWidget {
  const SettingNewMode({super.key});

  @override
  State<SettingNewMode> createState() => _SettingNewModeState();
}

class _SettingNewModeState extends State<SettingNewMode> {
  bool _commentAnonymous = false;
  bool _isNotification = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          title: Text(
            LocaleKeys.SettingNewMode.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            SettingListTile(
              leadingIcon: AppAssets.icon_book_edit,
              title: LocaleKeys.Customarticle.tr(),
              subtitle: LocaleKeys.Changestyle.tr(),
            ),
            const SettingDivider(),
            SettingListTile(
              leadingIcon: AppAssets.icon_heart,
              title: LocaleKeys.liked.tr(),
              subtitle: LocaleKeys.Reviewlikedarticles.tr(),
            ),
            const SettingDivider(),
            SettingListTile(
              leadingIcon: AppAssets.icon_history,
              title: LocaleKeys.Reviewyourposts.tr(),
              subtitle: LocaleKeys.Reviewlikedarticles.tr(),
            ),
            const SettingDivider(),
            SettingSwitchTile(
              leadingIcon: AppAssets.icon_anonymous,
              title: LocaleKeys.Commentanonymous.tr(),
              subtitle: LocaleKeys.Onlypeopleyoulikecanfindyou.tr(),
              value: _commentAnonymous,
              onChanged: (value) => setState(() => _commentAnonymous = value),
            ),
            const SettingDivider(),
            SettingSwitchTile(
              leadingIcon: AppAssets.icon_notification,
              title: LocaleKeys.Notification.tr(),
              subtitle: LocaleKeys.GetNotifiedWhenIHaveSomething.tr(),
              value: _isNotification,
              onChanged: (value) => setState(() => _isNotification = value),
            ),
          ],
        ),
      ),
    );
  }
}
