import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class ButtonSetting extends StatelessWidget {
  String text;
  void Function()? onTap;
  ButtonSetting({
    super.key,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Gap(5),
        GestureDetector(
          onTap: onTap,
          child: SizedBox(
            width: double.infinity,
            height: 30,
            child: Text(text,
                style: Theme.of(context).textTheme.lightBodyLargeRegular),
          ),
        ),
        const Divider(
          color: Color.fromARGB(255, 174, 177, 181),
        ),
      ],
    );
  }
}
