import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/network/subscriber_service.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:multime_app/modules/setting_mode/presentation/account/widgets/button_setting.dart';
import 'package:multime_app/modules/translate/bloc/language_bloc.dart';

import '../../../../core/constants/app_assets.dart';
import '../../../../core/streams/presence_stream_controller.dart';
import '../../../translate/language_widget.dart';

class SettingAccountPage extends StatefulWidget {
  const SettingAccountPage({super.key});

  @override
  State<SettingAccountPage> createState() => _SettingAccountPageState();
}

class _SettingAccountPageState extends State<SettingAccountPage> {
  @override
  final authRepository = getIt<AuthRepository>();

  Widget build(BuildContext context) {
    final languageBloc = context.read<LanguageBloc>();

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBar(
          title: Text(
            LocaleKeys.settingAccount.tr(),
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          centerTitle: true,
          leading: IconButton(
            icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(AppSpacing.padding10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(15.h),
            ButtonSetting(
                text: "General",
                onTap: () => (context).push(RouteName.generalSetting)),
            ButtonSetting(
              onTap: () => (context).push(RouteName.addressMarketplace),
              text: "My Addresses",
            ),
            // ButtonSetting(
            //   text: "Manage payment method",
            // ),
            ButtonSetting(
              text: "Change Password",
              onTap: () => (context).push(RouteName.changePasswordPage),
            ),
            // ButtonSetting(
            //   text: "Verify account",
            //   onTap: () => (context).push(RouteName.settingVerifyAccount),
            // ),
            ButtonSetting(
              text: "Privacy",
              onTap: () => (context).push(RouteName.settingPrivacyPage),
            ),
            // ButtonSetting(
            //   text: "Message",
            // ),
            ButtonSetting(
              text: "Sync Setting",
              onTap: () => (context).push(RouteName.syncSetting),
            ),
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   children: [
            //     const Gap(5),
            //     SizedBox(
            //       width: double.infinity,
            //       height: 30,
            //       child: Text(
            //         "Language / Ngôn ngữ",
            //         style: Theme.of(context).textTheme.lightBodyLargeRegular,
            //       ),
            //     ),
            //     Text(
            //       "English",
            //       style: Theme.of(context)
            //           .textTheme
            //           .lightBodyLargeRegular
            //           .copyWith(
            //             color: Theme.of(context).greyScale600(context),
            //           ),
            //     ),
            //   ],
            // ),
            LanguageSettingWidget(
              languageBloc: languageBloc,
            ),
            Spacer(),
            GestureDetector(
              onTap: () async {
                try {
                  PresenceStreamController.emit(PresenceUnsubscribed());
                  debugPrint('✅ Sent PresenceUnsubscribed event before logout');
                } catch (e) {
                  debugPrint('❌ Error sending PresenceUnsubscribed: $e');
                }
                // Clear all social login sessions
                await authRepository
                    .signOut(getIt<GlobalStorage>().accessToken!);
                // Clear local user data
                await Future.wait([
                  getIt<GlobalStorage>().clearUser(),
                  getIt<GlobalStorage>().removeChannelTokens(),
                ]);
                if (context.mounted) {
                  context.go(RouteName.loginPage);
                }
                //
                try {
                  await deleteSubscriptionId();
                } catch (e) {
                  print('Error deleting subscriber: $e');
                }
              },
              child: Container(
                  height: 50.h,
                  alignment: Alignment.center,
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).blackPrimary(context),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    LocaleKeys.logOut.tr(),
                    style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                  )),
            ),
            Gap(28.h),
          ],
        ),
      ),
    );
  }
}
