import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:multime_app/modules/setting_mode/presentation/widgets/setting_mode_card.dart';
import 'package:multime_app/modules/setting_mode/presentation/widgets/setting_option.dart';
import 'package:multime_app/modules/theme/bloc/theme_bloc.dart';

import '../../../../core/streams/presence_stream_controller.dart';
import '../../../../core/themes/theme.dart';
import '../../../application/bottom_bar/bloc/application_bloc.dart';
import '../../../application/bottom_bar/bloc/application_event.dart';

class SettingPage extends StatelessWidget {
  SettingPage({super.key});

  final user = getIt<GlobalStorage>().user;
  final ValueNotifier<bool> _isShakeToExploreEnabled =
      ValueNotifier<bool>(false);
  final ValueNotifier<bool> _isSellerModeEnabled = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    final authRepository = getIt<AuthRepository>();
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: GestureDetector(
          onTap: () => context.pop(),
          child: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
            fit: BoxFit.scaleDown,
          ),
        ),
        title: Text(
          LocaleKeys.setting.tr(),
          style: AppTextStyle.lightHeadingMedium,
        ),
      ),
      body: ValueListenableBuilder<String>(
          valueListenable: getIt<GlobalStorage>().currentAppModeNotifier,
          builder: (context, currentAppMode, _) {
            return SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Gap(16.h),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.12,
                    width: MediaQuery.of(context).size.width * 0.26,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(99),
                      child: user!.profilePicture!.isNotEmpty
                          ? Image.network(
                              user!.profilePicture!,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: Colors.grey[300],
                              alignment: Alignment.center,
                              child: Text(
                                user?.firstName?.isNotEmpty == true
                                    ? user!.firstName![0].toUpperCase()
                                    : '?',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeBold,
                              ),
                            ),
                    ),
                  ),
                  Gap(8.h),
                  Text('${user?.firstName ?? 'user'} ',
                      style: AppTextStyle.lightBodyLargeSemiBold),
                  Gap(4.h),
                  Text(
                    '${user?.email}',
                    style: AppTextStyle.lightBodyMediumRegular.copyWith(
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                  Gap(30.h),
                  SettingModeCard(
                    onTap: () async {
                      await getIt<GlobalStorage>().setCurrentAppMode('social');
                      context.push(RouteName.profileSocial);
                    },
                    icon: AppAssets.social_icon,
                    title: LocaleKeys.socialMode.tr(),
                    subtitle: LocaleKeys.manageYourConnection.tr(),
                    isSelected: currentAppMode == 'social',
                  ),
                  SettingModeCard(
                    onTap: () async {
                      await getIt<GlobalStorage>()
                          .setCurrentAppMode('business');
                      (context).push(RouteName.businesOverView);
                    },
                    icon: AppAssets.business_icon,
                    title: LocaleKeys.businessMode.tr(),
                    subtitle: LocaleKeys.connectToPartnerChances.tr(),
                    isSelected: currentAppMode == 'business',
                  ),
                  SettingModeCard(
                    onTap: () async {
                      await getIt<GlobalStorage>().setCurrentAppMode('news');
                      (context).push(RouteName.newPage);
                      context
                          .read<ApplicationBloc>()
                          .add(ApplicationSelectIndex(0));
                    },
                    icon: AppAssets.new_icon,
                    title: LocaleKeys.newsMode.tr(),
                    subtitle: LocaleKeys.readNews.tr(),
                    isSelected: currentAppMode == 'news',
                  ),
                  SettingModeCard(
                    onTap: () async {
                      await getIt<GlobalStorage>().setCurrentAppMode('market');
                      (context).push(RouteName.marketMode);
                    },
                    icon: AppAssets.market_icon,
                    title: LocaleKeys.marketplace.tr(),
                    subtitle: LocaleKeys.shoppingProductsAndService.tr(),
                    isSelected: currentAppMode == 'market',
                  ),
                  SettingModeCard(
                    onTap: () async {
                      await getIt<GlobalStorage>().setCurrentAppMode('dating');
                      (context).push(RouteName.datingMode);
                    },
                    icon: AppAssets.dating_icon,
                    title: LocaleKeys.datingMode.tr(),
                    subtitle: LocaleKeys.findRealPartnerForYou.tr(),
                    isSelected: currentAppMode == 'dating',
                  ),
                  SettingModeCard(
                    onTap: () async {
                      await getIt<GlobalStorage>()
                          .setCurrentAppMode('strongbody');
                      (context).push(RouteName.homeStrongBody);
                      context
                          .read<ApplicationBloc>()
                          .add(ApplicationSelectIndex(0));
                    },
                    icon: AppAssets.strongbody_icon,
                    title: LocaleKeys.strongBodyAi.tr(),
                    subtitle: LocaleKeys.findRealPartnerForYou.tr(),
                    isSelected: currentAppMode == 'strongbody',
                  ),
                  const Divider(
                    height: 32,
                    thickness: 1,
                    color: Color.fromARGB(255, 190, 190, 190),
                  ),
                  SettingOption(
                    icon: AppAssets.theme,
                    title: LocaleKeys.darkMode.tr(),
                    subtitle: LocaleKeys.selectMode.tr(),
                    widget: CupertinoSwitch(
                      value: context.watch<ThemeBloc>().state.appTheme ==
                          AppThemeClass.darkTheme,
                      onChanged: (value) {
                        context
                            .read<ThemeBloc>()
                            .add(ChangeTheme(isDarkMode: value));
                      },
                    ),
                  ),
                  SettingOption(
                    icon: AppAssets.userOctagon,
                    title: LocaleKeys.settingAccount.tr(),
                    subtitle: LocaleKeys.yourInvisible.tr(),
                    onTap: () {
                      (context).push(RouteName.settingAccount);
                    },
                  ),
                  SettingOption(
                    icon: AppAssets.setting_3,
                    title: LocaleKeys.settingMode.tr(),
                    subtitle: LocaleKeys.yourInvisible.tr(),
                    onTap: () {
                      (context).push(RouteName.settingMode);
                    },
                  ),
                  SettingOption(
                    icon: AppAssets.shieldTick,
                    title: LocaleKeys.policy.tr(),
                    subtitle: LocaleKeys.yourActivityInvisible.tr(),
                  ),
                  SettingOption(
                    onTap: () {
                      (context).push(RouteName.helpCenter);
                    },
                    icon: AppAssets.support24,
                    title: LocaleKeys.helpCenterTitle.tr(),
                    subtitle: LocaleKeys.helpCenterDescription.tr(),
                  ),
                  const Divider(
                    height: 32,
                    thickness: 1,
                    color: Color.fromARGB(255, 190, 190, 190),
                  ),
                  SettingOption(
                    icon: AppAssets.shoppingCart,
                    color: Theme.of(context).primary(context),
                    title: LocaleKeys.shoppingCartTitle.tr(),
                    subtitle: LocaleKeys.shoppingCartDescription.tr(),
                    onTap: () => context.pushNamed(RouteName.homeShoppingCart),
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: _isShakeToExploreEnabled,
                    builder: (context, isEnabled, child) {
                      return SettingOption(
                        icon: AppAssets.smartPhone,
                        title: LocaleKeys.shakeToExploreTitle.tr(),
                        subtitle: LocaleKeys.shakeToExploreDescription.tr(),
                        widget: CupertinoSwitch(
                          value: isEnabled,
                          onChanged: (value) {
                            _isShakeToExploreEnabled.value = value;
                          },
                        ),
                      );
                    },
                  ),
                  SettingOption(
                    icon: AppAssets.emptyWallet,
                    title: LocaleKeys.balanceTitle.tr(),
                    subtitle: LocaleKeys.balanceDescription.tr(),
                    expandable: false,
                    onTap: () => (context).push(RouteName.balancePage),
                  ),
                  SettingOption(
                    icon: AppAssets.seller,
                    title: LocaleKeys.sellerModeTitle.tr(),
                    subtitle: LocaleKeys.sellerModeDescription.tr(),
                    expandable: true,
                    expandedContent: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("Turn on Seller Mode",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge),
                                  Gap(4.h),
                                  Text(
                                    "Switch to service selling",
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodySmallRegular
                                        .copyWith(
                                          color: Theme.of(context)
                                              .greyScale600(context),
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            ValueListenableBuilder<bool>(
                              valueListenable: _isSellerModeEnabled,
                              builder: (context, isEnabled, child) {
                                return CupertinoSwitch(
                                  value: isEnabled,
                                  onChanged: (value) {
                                    _isSellerModeEnabled.value = value;
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                        const Divider(
                          height: 32,
                          thickness: 1,
                          color: Color.fromARGB(255, 190, 190, 190),
                        ),
                        GestureDetector(
                          onTap: () {},
                          child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Go to Seller Dashboard",
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleLarge),
                                      Gap(4.h),
                                      Text("Direct to seller dashboard on Web",
                                          style: Theme.of(context)
                                              .textTheme
                                              .lightBodySmallRegular),
                                    ],
                                  ),
                                ),
                                SvgPicture.asset(
                                  AppAssets.export,
                                  height: 24.h,
                                  width: 24.w,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(15.h),
                  GestureDetector(
                    onTap: () async {
                      // Clear all social login sessions
                      try {
                        PresenceStreamController.emit(PresenceUnsubscribed());
                        debugPrint(
                            '✅ Sent PresenceUnsubscribed event before logout');
                      } catch (e) {
                        debugPrint('❌ Error sending PresenceUnsubscribed: $e');
                      }
                      await authRepository
                          .signOut(getIt<GlobalStorage>().accessToken!);
                      // Clear local user data
                      await Future.wait([
                        getIt<GlobalStorage>().clearUser(),
                        getIt<GlobalStorage>().removeChannelTokens(),
                      ]);
                      if (context.mounted) {
                        context.go(RouteName.loginPage);
                      }
                    },
                    child: Container(
                        height: 50.h,
                        alignment: Alignment.center,
                        width: double.maxFinite,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Theme.of(context).blackPrimary(context),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          LocaleKeys.logOut.tr(),
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeSemiBold,
                        )),
                  ),
                  Gap(28.h),
                ],
              ),
            );
          }),
    );
  }

  String _getInitials(String name) {
    if (name.trim().isEmpty) {
      return 'U';
    }

    List<String> nameParts =
        name.trim().split(' ').where((part) => part.isNotEmpty).toList();
    if (nameParts.isEmpty) {
      return 'U';
    }

    String initials = '';
    if (nameParts[0].isNotEmpty) {
      initials += nameParts[0][0].toUpperCase();
    }
    if (nameParts.length > 1 && nameParts[1].isNotEmpty) {
      initials += nameParts[1][0].toUpperCase();
    }

    return initials.isNotEmpty ? initials : 'U';
  }
}
