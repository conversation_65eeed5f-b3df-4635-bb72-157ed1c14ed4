import 'package:bloc/bloc.dart';
import 'package:multime_app/modules/setting_mode/presentation/home/<USER>/setting_event.dart';
import 'package:multime_app/modules/setting_mode/presentation/home/<USER>/setting_state.dart';

import '../../../../../core/di/locator.dart';
import '../../../../../core/domain/storages/global_storage.dart';

class SettingBloc extends Bloc<SettingEvent, SettingState> {
  final GlobalStorage _globalStorage = getIt<GlobalStorage>();

  SettingBloc() : super(const SettingState(currentMode: 'social')) {
    on<ChangeAppMode>(_onChangeAppMode);
    on<InitAppMode>(_onInitAppMode);
    add(const InitAppMode());
  }

  Future<void> _onChangeAppMode(
      ChangeAppMode event, Emitter<SettingState> emit) async {
    await _globalStorage.setCurrentAppMode(event.mode);
    emit(state.copyWith(currentMode: event.mode));
  }

  void _onInitAppMode(InitAppMode event, Emitter<SettingState> emit) {
    final currentMode = _globalStorage.currentAppMode;
    emit(state.copyWith(currentMode: currentMode));
  }
}
