import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/image_choice.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'package:multime_app/modules/setting_mode/bloc/user_bloc.dart';
import 'package:multime_app/modules/setting_mode/presentation/general/widgets/circle_avartar_edit.dart';
import 'package:multime_app/shared/models/user/user_model.dart';
import 'package:multime_app/modules/country/model/country/country.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import 'package:multime_app/shared/widgets/sames/button_mtm%20copy.dart';
import '../../../../app/routers/routers_name.dart';
import '../../../../core/constants/app_assets.dart';
import 'widgets/button_continue.dart';

class GeneralSetting extends StatefulWidget {
  const GeneralSetting({
    super.key,
  });

  @override
  State<GeneralSetting> createState() => _GeneralSettingState();
}

class _GeneralSettingState extends State<GeneralSetting> {
  UserModel? user;
  late TextEditingController _countryController;
  late TextEditingController _fistNameController;
  late TextEditingController _lastNameController;

  int? selectedCountryId;
  List<Country> countries = [];
  String pickedPath = '';
  bool _isShowingSnackbar = false;

  @override
  void initState() {
    super.initState();
    user = getIt<GlobalStorage>().user;

    _countryController = TextEditingController(
      text: user?.country?.id?.toString() ?? '',
    );
    _fistNameController = TextEditingController(
      text: user?.firstName ?? '',
    );
    _lastNameController = TextEditingController(
      text: user?.lastName ?? '',
    );
    selectedCountryId = user?.countryId;

    countries = getIt<GlobalStorage>().countries ?? [];
  }

  @override
  void dispose() {
    _countryController.dispose();
    _fistNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  String? _validateCountrySelection(int? value) {
    if (value == null) {
      return 'Please select a country';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state.isLoading!) {
            AppLoader.show(context);
          } else {
            AppLoader.hide();
          }
          if (state.isSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Update successful',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumMedium
                      .copyWith(
                        color: Theme.of(context).whitePrimary(context),
                      ),
                ),
                backgroundColor: Colors.green,
              ),
            );
            setState(() {
              user = getIt<GlobalStorage>().user;
            });
          } else if (state.errorMessage.isNotEmpty && !_isShowingSnackbar) {
            _isShowingSnackbar = true;
            ScaffoldMessenger.of(context)
                .showSnackBar(
                  SnackBar(content: Text('${state.errorMessage}')),
                )
                .closed
                .then((_) {
              _isShowingSnackbar = false;
            });
          }
        },
        child: Scaffold(
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBar(
              title: Text(
                LocaleKeys.General.tr(),
                style: Theme.of(context).textTheme.lightHeadingMedium,
              ),
              centerTitle: true,
              leading: IconButton(
                icon: SvgPicture.asset(AppAssets.arrowLeftSvg),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ),
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: AppSpacing.padding16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.AvatarImage.tr(),
                    style: Theme.of(context).textTheme.lightBodyLargeMedium,
                  ),
                  Gap(10.h),
                  CircleAvatarWithEdit(
                    imageAvt: pickedPath.isNotEmpty
                        ? pickedPath
                        : user?.profilePicture ??
                            'https://cdn.pixabay.com/photo/2015/08/23/20/48/girl-903401_640.jpg',
                    onTap: () async {
                      final selectedPath =
                          await showImageChoices(context: context);
                      if (selectedPath.isNotEmpty) {
                        setState(() {
                          pickedPath = selectedPath;
                        });
                      }
                    },
                  ),
                  Gap(14.h),
                  StrongBodyTextField(
                    controller: _fistNameController,
                    labalText: "Fist name",
                  ),
                  Gap(14.h),
                  StrongBodyTextField(
                    controller: _lastNameController,
                    labalText: "Last name",
                  ),
                  Gap(14.h),
                  FormField<int>(
                    initialValue: selectedCountryId,
                    validator: (value) => _validateCountrySelection(value),
                    builder: (FormFieldState<int> field) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Theme(
                            data: Theme.of(context).copyWith(
                              dropdownMenuTheme: DropdownMenuThemeData(
                                textStyle: TextStyle(color: Colors.black),
                                menuStyle: MenuStyle(
                                  backgroundColor:
                                      WidgetStateProperty.all(Colors.white),
                                ),
                                inputDecorationTheme: InputDecorationTheme(
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: field.hasError
                                          ? Theme.of(context).colorScheme.error
                                          : Theme.of(context)
                                              .greyScale300(context),
                                      width: 1,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: field.hasError
                                          ? Theme.of(context).colorScheme.error
                                          : Theme.of(context).primary(context),
                                      width: 2,
                                    ),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color:
                                          Theme.of(context).colorScheme.error,
                                      width: 1.5,
                                    ),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color:
                                          Theme.of(context).colorScheme.error,
                                      width: 2,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            child: DropdownMenu<int>(
                              menuHeight: 200,
                              width: double.infinity,
                              controller: _countryController,
                              dropdownMenuEntries: countries.map((country) {
                                return DropdownMenuEntry<int>(
                                  value: country.id ?? 0,
                                  label: country.title ?? 'Unknown',
                                );
                              }).toList(),
                              onSelected: (int? value) {
                                setState(() {
                                  selectedCountryId = value;
                                });
                                field.didChange(value);
                              },
                              initialSelection: selectedCountryId,
                              label: Text(
                                LocaleKeys.country.tr(),
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeMedium
                                    .copyWith(
                                        color: field.hasError
                                            ? Theme.of(context)
                                                .colorScheme
                                                .error
                                            : Theme.of(context)
                                                .greyScale400(context)),
                              ),
                            ),
                          ),
                          if (field.hasError)
                            Padding(
                              padding:
                                  const EdgeInsets.only(top: 8.0, left: 12.0),
                              child: Text(
                                field.errorText!,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .error),
                              ),
                            ),
                        ],
                      );
                    },
                  ),
                  Gap(8.h),
                  Text(LocaleKeys.YouAreAllowedToChange.tr(),
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallRegular
                          .copyWith(
                              color: Theme.of(context).greyScale600(context))),
                  Gap(10.h),
                  CustomeButtonDating(
                      onTap: () {
                        context.read<UserBloc>().add(
                              UpdateProfileUserEvent(
                                firstName: _fistNameController.text,
                                lastName: _lastNameController.text,
                                countryId: selectedCountryId,
                                image:
                                    pickedPath.isNotEmpty ? pickedPath : null,
                              ),
                            );
                      },
                      text: 'Save',
                      colorBackground: Theme.of(context).primary(context),
                      colorText: Theme.of(context).whitePrimary(context),
                      height: 50,
                      width: double.infinity),
                  Gap(24.h),
                  Divider(
                      thickness: 1,
                      color: Theme.of(context).greyScale200(context)),
                  Gap(24.h),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(LocaleKeys.AccountDeactivation.tr(),
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeSemiBold
                              .copyWith(
                                  color:
                                      Theme.of(context).greyScale600(context))),
                      Gap(16.h),
                      Text(LocaleKeys.WhatHappensWhenYou.tr(),
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumMedium
                              .copyWith(
                                  color:
                                      Theme.of(context).greyScale600(context))),
                      Gap(12.h),
                      Text(
                        LocaleKeys.yourProfileGigsHidden.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodySmallRegular
                            .copyWith(
                                color: Theme.of(context).greyScale600(context)),
                      ),
                      Gap(12.h),
                      Text(
                        LocaleKeys.activeOrdersCancelled.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodySmallRegular
                            .copyWith(
                                color: Theme.of(context).greyScale600(context)),
                      ),
                      Gap(12.h),
                      Text(
                        LocaleKeys.cantReactivateGigs.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodySmallRegular
                            .copyWith(
                                color: Theme.of(context).greyScale600(context)),
                      ),
                      Gap(16.h),
                      ButtonContinue(
                        onTap: () {
                          GoRouter.of(context)
                              .push(RouteName.accountDeactivationPage);
                        },
                      ),
                      Gap(40.h),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
