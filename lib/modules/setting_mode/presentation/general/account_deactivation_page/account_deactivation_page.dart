import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/setting_mode/bloc/user_bloc.dart';
import 'package:multime_app/modules/setting_mode/presentation/general/account_deactivation_page/confirm_dialog.dart';

class AccountDeactivationScreen extends StatefulWidget {
  const AccountDeactivationScreen({super.key});

  @override
  State<AccountDeactivationScreen> createState() =>
      _AccountDeactivationScreenState();
}

class _AccountDeactivationScreenState extends State<AccountDeactivationScreen> {
  int? selectedReasonIndex;
  final String userEmail = "***<EMAIL>";
  final TextEditingController _otherReasonController = TextEditingController();

  final List<String> deactivationReasons = [
    "I no longer use the account frequently.",
    "The system does not meet my needs.",
    "I am dissatisfied with the platform experience.",
    "I want to pause due to personal reasons.",
    "I cannot find suitable connections or services",
    "The service cost doesn't suit me.",
    "Privacy and account security are my concerns.",
    "Too many notifications overwhelm me.",
    "I have found a platform that suits me better.",
    "Other (Please share more if you'd like)."
  ];

  @override
  void dispose() {
    _otherReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              LocaleKeys.AccountDeactivation.tr(),
              style: Theme.of(context).textTheme.lightHeadingMedium,
            ),
            elevation: 0,
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "We have sent a confirmation email to ",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(
                                color: Theme.of(context).greyScale600(context)),
                      ),
                      TextSpan(
                        text: userEmail,
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(
                              color: Theme.of(context).greyScale600(context),
                              fontStyle: FontStyle.italic,
                            ),
                      ),
                      TextSpan(
                        text: " regarding your account deactivation request.",
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumRegular
                            .copyWith(
                                color: Theme.of(context).greyScale600(context)),
                      ),
                    ],
                  ),
                ),
                Divider(
                  color:
                      const Color.fromARGB(255, 123, 131, 144).withOpacity(0.5),
                  thickness: 1,
                  height: 32.h,
                ),
                Text(
                  "Why did you decide to leave this app?",
                  style: Theme.of(context).textTheme.lightBodyXLargeBold,
                ),
                Gap(8.h),
                Text(
                  "We're sorry to see you go. Please tell us why.",
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyMediumRegular
                      .copyWith(color: Theme.of(context).greyScale600(context)),
                ),
                Gap(16.h),
                ...List.generate(deactivationReasons.length, (index) {
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 24.w,
                          height: 24.h,
                          child: Checkbox(
                            value: selectedReasonIndex == index,
                            onChanged: (bool? value) {
                              setState(() {
                                selectedReasonIndex = value! ? index : null;
                              });
                            },
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            activeColor: Colors.black,
                            checkColor: Colors.white,
                            fillColor: WidgetStateProperty.resolveWith<Color>(
                                (Set<WidgetState> states) {
                              if (states.contains(WidgetState.selected)) {
                                return Colors.black;
                              }
                              return Colors.transparent;
                            }),
                            side: const BorderSide(
                                color: Colors.black, width: 1.5),
                          ),
                        ),
                        Gap(12.w),
                        Expanded(
                          child: Text(
                            deactivationReasons[index],
                            style: Theme.of(context)
                                .textTheme
                                .lightBodyLargeRegular
                                .copyWith(fontSize: 14.sp),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                if (selectedReasonIndex == 9)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: TextField(
                      controller: _otherReasonController,
                      decoration: InputDecoration(
                        hintText: "Enter reason",
                        hintStyle: Theme.of(context)
                            .textTheme
                            .lightBodyLargeRegular
                            .copyWith(color: Colors.grey),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: BorderSide(color: Colors.grey.shade400),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 12.h),
                      ),
                      style: Theme.of(context).textTheme.lightBodyLargeRegular,
                      maxLines: 5,
                      minLines: 4,
                    ),
                  ),
                Gap(50.h),
                SizedBox(
                  width: double.infinity,
                  height: 50.h,
                  child: ElevatedButton(
                    onPressed: selectedReasonIndex == null
                        ? null
                        : () {
                            showDialog(
                              context: context,
                              builder: (context) => BlocProvider.value(
                                value: context.read<UserBloc>(),
                                child: const ConfirmDialog(),
                              ),
                            );
                          },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: selectedReasonIndex == null
                          ? Theme.of(context).primary(context).withOpacity(0.5)
                          : Theme.of(context).primary(context),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      disabledBackgroundColor:
                          Theme.of(context).primary(context).withOpacity(0.5),
                      disabledForegroundColor: Colors.white.withOpacity(0.7),
                    ),
                    child: Text(
                      "Account Deactivation",
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyLargeSemiBold
                          .copyWith(
                            color: Colors.white,
                          ),
                    ),
                  ),
                ),
                Gap(16.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
