import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ButtonContinue extends StatelessWidget {
  void Function()? onTap;
  ButtonContinue({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).greyScale600(context),
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text("Continue",
                  style: Theme.of(context).textTheme.lightBodyLargeMedium),
              const Gap(10),
              SvgPicture.asset(AppAssets.icon_next),
            ],
          ),
        ),
      ),
    );
  }
}
