import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class CircleAvatarWithEdit extends StatelessWidget {
  final String imageAvt;
  final void Function()? onTap;

  const CircleAvatarWithEdit({
    super.key,
    required this.imageAvt,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(99),
          child: GestureDetector(
            onTap: onTap,
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundImage: _getImageProvider(imageAvt),
                  backgroundColor: Colors.grey[300], // <PERSON>àu nền nếu ảnh bị lỗi
                ),
                Container(
                  height: 30,
                  width: 80, // <PERSON><PERSON>ch thước nút Edit
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5), // <PERSON><PERSON><PERSON> mờ nền
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(99),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "Edit",
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Gap(20.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "- Maximum file size is 1 MB",
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              "- Format: .JPEG, .PNG",
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        )
      ],
    );
  }

  ImageProvider _getImageProvider(String path) {
    if (path.startsWith('http') || path.startsWith('https')) {
      return NetworkImage(path);
    } else if (path.isNotEmpty && File(path).existsSync()) {
      return FileImage(File(path));
    } else {
      return const AssetImage(
          'assets/image/logo.png'); // Use an existing asset as fallback
    }
  }
}
