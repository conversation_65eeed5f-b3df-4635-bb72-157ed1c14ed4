import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';

class OrderProductReponse {
  final OrderProductData orderProductData;
  OrderProductReponse({
    required this.orderProductData,
  });

  factory OrderProductReponse.fromJson(Map<String, dynamic> json) {
    return OrderProductReponse(
      orderProductData: OrderProductData.fromJson(json['data'] ?? {}),
    );
  }
}

class OrderProductData {
  final List<OrderProductModel> orderProductModel;
  OrderProductData({
    required this.orderProductModel,
  });
  factory OrderProductData.fromJson(Map<String, dynamic> json) {
    return OrderProductData(
      orderProductModel: (json['data'] as List<dynamic>?)
              ?.map((item) => OrderProductModel.fromJson(item))
              .toList() ??
          [],
    );
  }
}
