import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';

class OrderServiceReponse {
  final OrderServiceData orderServiceData;
  OrderServiceReponse({
    required this.orderServiceData,
  });

  factory OrderServiceReponse.fromJson(Map<String, dynamic> json) {
    return OrderServiceReponse(
      orderServiceData: OrderServiceData.fromJson(json['data'] ?? {}),
    );
  }
}

class OrderServiceData {
  final List<OrderServiceModel> orderServiceModel;
  OrderServiceData({
    required this.orderServiceModel,
  });
  factory OrderServiceData.fromJson(Map<String, dynamic> json) {
    return OrderServiceData(
      orderServiceModel: (json['list'] as List<dynamic>?)
              ?.map((item) => OrderServiceModel.fromJson(item))
              .toList() ??
          [],
    );
  }
}
