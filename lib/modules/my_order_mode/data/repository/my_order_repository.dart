import 'package:multime_app/core/base/api_response/api_response.dart';
import 'package:multime_app/core/constants/api_endpoints.dart';
import 'package:multime_app/core/constants/app_constants.dart';
import 'package:multime_app/core/network/api.dart';
import 'package:multime_app/core/network/api_type.dart';
import 'package:multime_app/modules/my_order_mode/data/response/order_product_reponse.dart';

import '../response/order_service_reponse.dart';

abstract class MyOrderRepository {
  Future<ApiResponse<OrderProductReponse>> getOrderList(
      Map<String, dynamic> queryParameters, String token);
  Future<ApiResponse<OrderServiceReponse>> getOrderServiceList(
      Map<String, dynamic> queryParameters, String token);
  Future<ApiResponse<OrderProductReponse>> updateOrderProductStatus(
      int orderId, String orderStatus, String shippingStatus, String token);
  Future<ApiResponse<OrderProductReponse>> updateOrderServiceBuyerStatus(
      int offerId, String token);
}

class MyOrderRepositoryRemote implements MyOrderRepository {
  final ApiClient _apiClient;
  MyOrderRepositoryRemote(this._apiClient);

  @override
  Future<ApiResponse<OrderProductReponse>> getOrderList(
      Map<String, dynamic> queryParameters, String token) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.orderProduct}',
          method: ApiType.get,
          queryParameters: queryParameters,
          headers: {
            'Authorization': 'Bearer ${token}',
            'Scope': AppConstants.multi_me
          });
      return ApiResponse<OrderProductReponse>.completed(
          OrderProductReponse.fromJson(response));
    } catch (e) {
      print("getOrderList err: $e");
      rethrow;
    }
  }

  @override
  Future<ApiResponse<OrderServiceReponse>> getOrderServiceList(
      Map<String, dynamic> queryParameters, String token) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.orderService}',
          method: ApiType.get,
          queryParameters: queryParameters,
          headers: {
            'Authorization': 'Bearer ${token}',
            'Scope': AppConstants.multi_me
          });
      return ApiResponse<OrderServiceReponse>.completed(
          OrderServiceReponse.fromJson(response));
    } catch (e) {
      print("getOrderServiceList err: $e");
      rethrow;
    }
  }

  @override
  Future<ApiResponse<OrderProductReponse>> updateOrderProductStatus(int orderId,
      String orderStatus, String shippingStatus, String token) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.updateOrderProductDetails}/$orderId/status',
          method: ApiType.put,
          data: {
            'order_status': orderStatus,
            'shipping_status': shippingStatus,
          },
          headers: {
            'Authorization': 'Bearer ${token}',
            'Scope': AppConstants.multi_me
          });
      return ApiResponse<OrderProductReponse>.completed(
          OrderProductReponse.fromJson(response));
    } catch (e) {
      print("updateOrderProductStatus err: $e");
      rethrow;
    }
  }

  @override
  Future<ApiResponse<OrderProductReponse>> updateOrderServiceBuyerStatus(
      int offerId, String token) async {
    try {
      final response = await _apiClient.request(
          path: '${ApiConst.paymentServiceBuyer}/$offerId/completed',
          method: ApiType.patch,
          headers: {
            'Authorization': 'Bearer ${token}',
            'Scope': AppConstants.multi_me
          });
      return ApiResponse<OrderProductReponse>.completed(
          OrderProductReponse.fromJson(response));
    } catch (e) {
      print("updateOrderServiceBuyerStatus err: $e");
      rethrow;
    }
  }
}
