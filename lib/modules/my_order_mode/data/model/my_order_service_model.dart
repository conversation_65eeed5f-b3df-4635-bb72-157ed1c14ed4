class MyOrderServiceModel {
  final String name;
  final String avatarUrl;
  final String title;
  final String status;
  final String userStatus;
  final String country;
  final num money;
  final String category;
  final String imageTitle;
  final String description;
  final String imageDetail;
  final String idOrder;
  final String? endTime;
  final String? startTime;
  final String? deliveryUpdate;
  final String? orderStatus;
  final String? statusServiceExecution;
  final String? orderCompletion;
  final String? orderCancel;
  final String? sellerSendRequest;

  MyOrderServiceModel({
    required this.name,
    required this.avatarUrl,
    required this.title,
    required this.status,
    required this.userStatus,
    required this.country,
    required this.money,
    required this.category,
    required this.imageTitle,
    required this.description,
    required this.imageDetail,
    required this.idOrder,
    this.endTime,
    this.startTime,
    this.deliveryUpdate,
    this.orderStatus,
    this.statusServiceExecution,
    this.orderCompletion,
    this.orderCancel,
    this.sellerSendRequest,
  });

  /// <PERSON>yển đổi từ JSON sang Model
  factory MyOrderServiceModel.fromJson(Map<String, dynamic> json) {
    return MyOrderServiceModel(
      name: json['name'] ?? '',
      avatarUrl: json['avatarUrl'] ?? '',
      title: json['title'] ?? '',
      status: json['status'] ?? '',
      userStatus: json['userStatus'] ?? '',
      country: json['country'] ?? '',
      money: json['money'] ?? 0,
      category: json['category'] ?? '',
      imageTitle: json['imageTitle'] ?? '',
      description: json['description'] ?? '',
      imageDetail: json['imageDetail'] ?? '',
      idOrder: json['idOrder'] ?? '',
      endTime: json['endTime'],
      startTime: json['startTime'],
      deliveryUpdate: json['deliveryUpdate'],
      orderStatus: json['orderStatus'],
      statusServiceExecution: json['statusServiceExecution'],
      orderCompletion: json['orderCompletion'],
      orderCancel: json['orderCancel'],
      sellerSendRequest: json['sellerSendRequest'],
    );
  }

  /// Chuyển đổi từ Model sang JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'avatarUrl': avatarUrl,
      'title': title,
      'status': status,
      'userStatus': userStatus,
      'country': country,
      'money': money,
      'category': category,
      'imageTitle': imageTitle,
      'description': description,
      'imageDetail': imageDetail,
      'idOrder': idOrder,
      'endTime': endTime,
      'startTime': startTime,
      'deliveryUpdate': deliveryUpdate,
      'orderStatus': orderStatus,
      'statusServiceExecution': statusServiceExecution,
      'orderCompletion': orderCompletion,
      'orderCancel': orderCancel,
      'sellerSendRequest': sellerSendRequest,
    };
  }
}
