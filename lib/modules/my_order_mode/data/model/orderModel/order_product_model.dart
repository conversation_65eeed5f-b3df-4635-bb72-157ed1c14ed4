import 'package:multime_app/core/model/shop/shop_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/shared/models/user/user_model.dart';

class OrderProductModel {
  final int? id;
  final String? orderNo;
  final int? total;
  final int? serviceFee;
  final int? commissionFee;
  final String? orderStatus;
  final String? payStatus;
  final String? withdrawalStatus;
  final String? shippingStatus;
  final String? cancelStatus;
  final String? type;
  final String? method;
  final DateTime? completedDate;
  final int? addressId;
  final Address? address;
  final int? refundAmount;
  final String? refundNotes;
  final DateTime? refundDate;
  final int? productId;
  final String? unit;
  final int? quantity;
  final ProductSnapshot? productSnapshot;
  final int? customerId;
  final UserModel? customer;
  final int? shopId;
  final ShopModel? shop;
  final String? paymentIntentId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  OrderProductModel({
    this.id,
    this.orderNo,
    this.total,
    this.serviceFee,
    this.commissionFee,
    this.orderStatus,
    this.payStatus,
    this.withdrawalStatus,
    this.cancelStatus,
    this.type,
    this.method,
    this.completedDate,
    this.addressId,
    this.address,
    this.refundAmount,
    this.refundNotes,
    this.refundDate,
    this.productId,
    this.unit,
    this.quantity,
    this.productSnapshot,
    this.customerId,
    this.customer,
    this.shopId,
    this.shop,
    this.paymentIntentId,
    this.createdAt,
    this.updatedAt,
    this.shippingStatus,
  });

  OrderProductModel copyWith({
    int? id,
    String? orderNo,
    int? total,
    int? serviceFee,
    int? commissionFee,
    String? orderStatus,
    String? payStatus,
    String? withdrawalStatus,
    String? cancelStatus,
    String? type,
    String? method,
    DateTime? completedDate,
    int? addressId,
    Address? address,
    int? refundAmount,
    String? refundNotes,
    DateTime? refundDate,
    int? productId,
    String? unit,
    int? quantity,
    ProductSnapshot? productSnapshot,
    int? customerId,
    UserModel? customer,
    int? shopId,
    ShopModel? shop,
    String? paymentIntentId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? shippingStatus,
  }) {
    return OrderProductModel(
      id: id ?? this.id,
      orderNo: orderNo ?? this.orderNo,
      total: total ?? this.total,
      serviceFee: serviceFee ?? this.serviceFee,
      commissionFee: commissionFee ?? this.commissionFee,
      orderStatus: orderStatus ?? this.orderStatus,
      payStatus: payStatus ?? this.payStatus,
      withdrawalStatus: withdrawalStatus ?? this.withdrawalStatus,
      cancelStatus: cancelStatus ?? this.cancelStatus,
      type: type ?? this.type,
      method: method ?? this.method,
      completedDate: completedDate ?? this.completedDate,
      addressId: addressId ?? this.addressId,
      address: address ?? this.address,
      refundAmount: refundAmount ?? this.refundAmount,
      refundNotes: refundNotes ?? this.refundNotes,
      refundDate: refundDate ?? this.refundDate,
      productId: productId ?? this.productId,
      unit: unit ?? this.unit,
      quantity: quantity ?? this.quantity,
      productSnapshot: productSnapshot ?? this.productSnapshot,
      customerId: customerId ?? this.customerId,
      customer: customer ?? this.customer,
      shopId: shopId ?? this.shopId,
      shop: shop ?? this.shop,
      paymentIntentId: paymentIntentId ?? this.paymentIntentId,
      createdAt: createdAt ?? this.createdAt?.toUtc(),
      updatedAt: updatedAt ?? this.updatedAt?.toUtc(),
      shippingStatus: shippingStatus ?? this.shippingStatus,
    );
  }

  factory OrderProductModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return OrderProductModel();
    return OrderProductModel(
      id: json['id'] is int
          ? json['id']
          : int.tryParse(json['id']?.toString() ?? ''),
      orderNo: json['order_no']?.toString(),
      total: json['total'] is int
          ? json['total']
          : int.tryParse(json['total']?.toString() ?? ''),
      serviceFee: json['service_fee'] is int
          ? json['service_fee']
          : int.tryParse(json['service_fee']?.toString() ?? ''),
      commissionFee: json['commission_fee'] is int
          ? json['commission_fee']
          : int.tryParse(json['commission_fee']?.toString() ?? ''),
      orderStatus: json['order_status']?.toString(),
      payStatus: json['pay_status']?.toString(),
      withdrawalStatus: json['withdrawal_status']?.toString(),
      cancelStatus: json['cancel_status']?.toString(),
      type: json['type']?.toString(),
      method: json['method']?.toString(),
      completedDate: json['completed_date'] != null
          ? DateTime.tryParse(json['completed_date'].toString())
          : null,
      addressId: json['address_id'] is int
          ? json['address_id']
          : int.tryParse(json['address_id']?.toString() ?? ''),
      address:
          json['address'] != null && json['address'] is Map<String, dynamic>
              ? Address.fromJson(json['address'])
              : null,
      refundAmount: json['refund_amount'] is int
          ? json['refund_amount']
          : int.tryParse(json['refund_amount']?.toString() ?? ''),
      refundNotes: json['refund_notes']?.toString(),
      refundDate: json['refund_date'] != null
          ? DateTime.tryParse(json['refund_date'].toString())
          : null,
      productId: json['product_id'] is int
          ? json['product_id']
          : int.tryParse(json['product_id']?.toString() ?? ''),
      unit: json['unit']?.toString(),
      quantity: json['quantity'] is int
          ? json['quantity']
          : int.tryParse(json['quantity']?.toString() ?? ''),
      productSnapshot: json['product_snapshot'] != null &&
              json['product_snapshot'] is Map<String, dynamic>
          ? ProductSnapshot.fromJson(json['product_snapshot'])
          : null,
      customerId: json['customer_id'] is int
          ? json['customer_id']
          : int.tryParse(json['customer_id']?.toString() ?? ''),
      customer:
          json['customer'] != null && json['customer'] is Map<String, dynamic>
              ? UserModel.fromJson(json['customer'])
              : null,
      shopId: json['shop_id'] is int
          ? json['shop_id']
          : int.tryParse(json['shop_id']?.toString() ?? ''),
      shop: json['shop'] != null ? ShopModel.fromJson(json['shop']) : null,
      paymentIntentId: json['payment_intent_id']?.toString(),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'].toString())
          : null,
      shippingStatus: json['shipping_status']?.toString(),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'order_no': orderNo,
        'total': total,
        'service_fee': serviceFee,
        'commission_fee': commissionFee,
        'order_status': orderStatus,
        'pay_status': payStatus,
        'withdrawal_status': withdrawalStatus,
        'cancel_status': cancelStatus,
        'type': type,
        'method': method,
        'completed_date': completedDate,
        'address_id': addressId,
        'address': address?.toJson(),
        'refund_amount': refundAmount,
        'refund_notes': refundNotes,
        'refund_date': refundDate,
        'product_id': productId,
        'unit': unit,
        'quantity': quantity,
        'product_snapshot': productSnapshot?.toJson(),
        'customer_id': customerId,
        'customer': customer?.toJson(),
        'shop_id': shopId,
        'shop': shop?.toJson(),
        'payment_intent_id': paymentIntentId,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'shipping_status': shippingStatus,
      };
}

class ProductSnapshot {
  final String? snapshotName;
  final String? snapshotDescription;
  final String? snapshotSku;
  final String? snapshotBrandText;
  final int? snapshotCategoryId;
  final String? snapshotCategoryName;
  final int? snapshotPrice;
  final String? snapshotUnit;
  final String? snapshotImages;
  final String? snapshotCountryOfOrigin;
  final String? snapshotLicenseNo;
  final String? snapshotLicenseFile;
  final String? snapshotCreatedAt;
  final String? snapshotUpdatedAt;

  ProductSnapshot({
    this.snapshotName,
    this.snapshotDescription,
    this.snapshotSku,
    this.snapshotBrandText,
    this.snapshotCategoryId,
    this.snapshotCategoryName,
    this.snapshotPrice,
    this.snapshotUnit,
    this.snapshotImages,
    this.snapshotCountryOfOrigin,
    this.snapshotLicenseNo,
    this.snapshotLicenseFile,
    this.snapshotCreatedAt,
    this.snapshotUpdatedAt,
  });

  factory ProductSnapshot.fromJson(Map<String, dynamic>? json) {
    if (json == null) return ProductSnapshot();
    return ProductSnapshot(
      snapshotName: json['snapshot_name']?.toString(),
      snapshotDescription: json['snapshot_description']?.toString(),
      snapshotSku: json['snapshot_sku']?.toString(),
      snapshotBrandText: json['snapshot_brand_text']?.toString(),
      snapshotCategoryId: json['snapshot_category_id'] is int
          ? json['snapshot_category_id']
          : int.tryParse(json['snapshot_category_id']?.toString() ?? ''),
      snapshotCategoryName: json['snapshot_category_name']?.toString(),
      snapshotPrice: json['snapshot_price'] is int
          ? json['snapshot_price']
          : int.tryParse(json['snapshot_price']?.toString() ?? ''),
      snapshotUnit: json['snapshot_unit']?.toString(),
      snapshotImages: json['snapshot_images']?.toString(),
      snapshotCountryOfOrigin: json['snapshot_country_of_origin']?.toString(),
      snapshotLicenseNo: json['snapshot_license_no']?.toString(),
      snapshotLicenseFile: json['snapshot_license_file']?.toString(),
      snapshotCreatedAt: json['snapshot_created_at']?.toString(),
      snapshotUpdatedAt: json['snapshot_updated_at']?.toString(),
    );
  }
  Map<String, dynamic> toJson() => {
        "snapshot_name": snapshotName,
        "snapshot_description": snapshotDescription,
        "snapshot_sku": snapshotSku,
        "snapshot_brand_text": snapshotBrandText,
        "snapshot_category_id": snapshotCategoryId,
        "snapshot_category_name": snapshotCategoryName,
        "snapshot_price": snapshotPrice,
        "snapshot_unit": snapshotUnit,
        "snapshot_images": snapshotImages,
        "snapshot_country_of_origin": snapshotCountryOfOrigin,
        "snapshot_license_no": snapshotLicenseNo,
        "snapshot_license_file": snapshotLicenseFile,
        "snapshot_created_at": snapshotCreatedAt,
        "snapshot_updated_at": snapshotUpdatedAt,
      };
}
