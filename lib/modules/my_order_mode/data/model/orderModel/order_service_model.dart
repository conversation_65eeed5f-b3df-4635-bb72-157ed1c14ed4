import 'package:multime_app/shared/models/user/user_model.dart';

import '../../../../../core/model/category_model.dart';

class OrderServiceModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final int? createdBy;
  final int? updatedBy;
  final String? orderNo;
  final String? title;
  final String? description;
  final double? price;
  final String? status;
  final String? type;
  final int? sellerId;
  final UserModel? seller;
  final int? customerId;
  final UserModel? customer;
  final int? requestId;
  final RequestModel? request;
  final int? serviceId;
  final ServiceDetailModel? service;
  final List<FileModel>? files;
  final String? paymentStatus;
  final String? paymentMethod;
  final String? transactionId;
  final DateTime? paymentDate;
  final String? notes;
  final dynamic certificates;

  OrderServiceModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.orderNo,
    this.title,
    this.description,
    this.price,
    this.status,
    this.type,
    this.sellerId,
    this.seller,
    this.customerId,
    this.customer,
    this.requestId,
    this.request,
    this.serviceId,
    this.service,
    this.files,
    this.paymentStatus,
    this.paymentMethod,
    this.transactionId,
    this.paymentDate,
    this.notes,
    this.certificates,
  });

  factory OrderServiceModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return OrderServiceModel();
    return OrderServiceModel(
      id: json['id'] as int?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
      deletedAt: json['deleted_at'] != null
          ? DateTime.tryParse(json['deleted_at'])
          : null,
      createdBy: json['created_by'] as int?,
      updatedBy: json['updated_by'] as int?,
      orderNo: json['order_no']?.toString(),
      title: json['title']?.toString(),
      description: json['description']?.toString(),
      price: json['price'] is double
          ? json['price']
          : double.tryParse(json['price']?.toString() ?? ''),
      status: json['status']?.toString(),
      type: json['type']?.toString(),
      sellerId: json['seller_id'] as int?,
      seller:
          json['Seller'] != null ? UserModel.fromJson(json['Seller']) : null,
      customerId: json['customer_id'] as int?,
      customer: json['Customer'] != null
          ? UserModel.fromJson(json['Customer'])
          : null,
      requestId: json['request_id'] as int?,
      request: json['Request'] != null
          ? RequestModel.fromJson(json['Request'])
          : null,
      serviceId: json['service_id'] as int?,
      service: json['Service'] != null
          ? ServiceDetailModel.fromJson(json['Service'])
          : null,
      files: json['files'] != null
          ? List<FileModel>.from(
              (json['files'] as List).map((e) => FileModel.fromJson(e)))
          : null,
      paymentStatus: json['payment_status']?.toString(),
      paymentMethod: json['payment_method']?.toString(),
      transactionId: json['transaction_id']?.toString(),
      paymentDate: json['payment_date'] != null
          ? DateTime.tryParse(json['payment_date'])
          : null,
      notes: json['notes']?.toString(),
      certificates: json['certificates'],
    );
  }
}

class FileModel {
  final String? url;
  final String? name;
  final String? size;
  final String? type;

  FileModel({this.url, this.name, this.size, this.type});

  factory FileModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return FileModel();
    return FileModel(
      url: json['url']?.toString(),
      name: json['name']?.toString(),
      size: json['size']?.toString(),
      type: json['type']?.toString(),
    );
  }

  Map<String, dynamic> toJson() => {
        "url": url,
        "name": name,
        "size": size,
        "type": type,
      };
}

class RequestModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final int? createdBy;
  final int? updatedBy;
  final String? description;
  final String? status;
  final int? categoryId;
  final CategoryModel? category;
  final int? userId;
  final UserModel? user;
  final dynamic attachments;
  final String? notes;

  RequestModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.description,
    this.status,
    this.categoryId,
    this.category,
    this.userId,
    this.user,
    this.attachments,
    this.notes,
  });

  factory RequestModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return RequestModel();
    return RequestModel(
      id: json['id'] as int?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
      deletedAt: json['deleted_at'] != null
          ? DateTime.tryParse(json['deleted_at'])
          : null,
      createdBy: json['created_by'] as int?,
      updatedBy: json['updated_by'] as int?,
      description: json['description']?.toString(),
      status: json['status']?.toString(),
      categoryId: json['category_id'] as int?,
      category: json['category'] != null
          ? CategoryModel.fromJson(json['category'])
          : null,
      userId: json['user_id'] as int?,
      user: json['user'] != null ? UserModel.fromJson(json['user']) : null,
      attachments: json['attachments'],
      notes: json['notes']?.toString(),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "description": description,
        "status": status,
        "category_id": categoryId,
        "category": category?.toJson(),
        "user_id": userId,
        "user": user?.toJson(),
        "attachments": attachments,
        "notes": notes,
      };
}

class ServiceDetailModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final int? createdBy;
  final int? updatedBy;
  final String? title;
  final String? description;
  final String? rejectNotes;
  final int? userId;
  final UserModel? user;
  final int? categoryId;
  final CategoryModel? category;
  final String? address;
  final int? views;
  final int? rating;
  final String? longitude;
  final String? latitude;
  final List<String>? image;
  final String? coverImage;
  final double? price;
  final String? type;
  final String? deliverables;
  final int? deliveryLeadTimeDay;
  final String? slug;
  final String? status;

  ServiceDetailModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.createdBy,
    this.updatedBy,
    this.title,
    this.description,
    this.rejectNotes,
    this.userId,
    this.user,
    this.categoryId,
    this.category,
    this.address,
    this.views,
    this.rating,
    this.longitude,
    this.latitude,
    this.image,
    this.coverImage,
    this.price,
    this.type,
    this.deliverables,
    this.deliveryLeadTimeDay,
    this.slug,
    this.status,
  });

  factory ServiceDetailModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return ServiceDetailModel();
    return ServiceDetailModel(
      id: json['id'] as int?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
      deletedAt: json['deleted_at'] != null
          ? DateTime.tryParse(json['deleted_at'])
          : null,
      createdBy: json['created_by'] as int?,
      updatedBy: json['updated_by'] as int?,
      title: json['title']?.toString(),
      description: json['description']?.toString(),
      rejectNotes: json['reject_notes']?.toString(),
      userId: json['user_id'] as int?,
      user: json['user'] != null ? UserModel.fromJson(json['user']) : null,
      categoryId: json['category_id'] as int?,
      category: json['category'] != null
          ? CategoryModel.fromJson(json['category'])
          : null,
      address: json['address']?.toString(),
      views: json['views'] is int
          ? json['views']
          : int.tryParse(json['views']?.toString() ?? ''),
      rating: json['rating'] is int
          ? json['rating']
          : int.tryParse(json['rating']?.toString() ?? ''),
      longitude: json['longitude']?.toString(),
      latitude: json['latitude']?.toString(),
      image: (json['image'] is List)
          ? (json['image'] as List).map((e) => e?.toString() ?? '').toList()
          : [],
      coverImage: json['cover_image']?.toString(),
      price: json['price'] is double
          ? json['price']
          : double.tryParse(json['price']?.toString() ?? ''),
      type: json['type']?.toString(),
      deliverables: json['deliverables']?.toString(),
      deliveryLeadTimeDay: json['delivery_lead_time_day'] is int
          ? json['delivery_lead_time_day']
          : int.tryParse(json['delivery_lead_time_day']?.toString() ?? ''),
      slug: json['slug']?.toString(),
      status: json['status']?.toString(),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "title": title,
        "description": description,
        "reject_notes": rejectNotes,
        "user_id": userId,
        "user": user?.toJson(),
        "category_id": categoryId,
        "category": category?.toJson(),
        "address": address,
        "views": views,
        "rating": rating,
        "longitude": longitude,
        "latitude": latitude,
        "image": image,
        "cover_image": coverImage,
        "price": price,
        "type": type,
        "deliverables": deliverables,
        "delivery_lead_time_day": deliveryLeadTimeDay,
        "slug": slug,
        "status": status,
      };
}
