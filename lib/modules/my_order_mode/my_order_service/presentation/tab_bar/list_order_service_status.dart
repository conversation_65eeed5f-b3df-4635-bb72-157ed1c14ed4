import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/widgets/service_card/service_card.dart';

import '../../../../../../core/constants/padding_constant.dart';
import '../../../../../../shared/widgets/app_loader/loading_strongbody.dart';

class ListOrderServiceStatus extends StatefulWidget {
  const ListOrderServiceStatus(
      {super.key, required this.status, required this.serviceHomePageBloc});
  final String status;
  final ServiceHomePageBloc serviceHomePageBloc;

  @override
  State<ListOrderServiceStatus> createState() => _ListOrderServiceStatusState();
}

class _ListOrderServiceStatusState extends State<ListOrderServiceStatus> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load initial data for 'all' status
    if (widget.status == 'all') {
      context.read<ServiceHomePageBloc>().add(
            FetchOrderServiceEvent(status: widget.status, loadMore: false),
          );
    }

    // Simple scroll listener
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // Check if reached near bottom
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<ServiceHomePageBloc>().state;

      // Simple condition: has more data and not currently loading
      if (state.hasMore && !state.isLoading) {
        context.read<ServiceHomePageBloc>().add(
              FetchOrderServiceEvent(status: widget.status, loadMore: true),
            );
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[200],
      body: BlocBuilder<ServiceHomePageBloc, ServiceHomePageState>(
        builder: (context, state) {
          final orders = state.orderServiceModel ?? [];

          if (state.isLoading && orders.isEmpty) {
            return const LoadingStrongBody();
          }

          if (state.isError) {
            return _buildErrorWidget(state.errorMessage);
          }

          // Empty state
          if (state.isSuccess && orders.isEmpty) {
            return _buildEmptyWidget();
          }

          // Success state with data
          return _buildOrdersList(orders, state);
        },
      ),
    );
  }

  Widget _buildErrorWidget(String? errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            AppAssets.lionStrongbodyAi,
            width: MediaQuery.of(context).size.width * 0.12,
            height: MediaQuery.of(context).size.height * 0.3,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading orders',
            style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage ?? 'Please check your connection and try again',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<ServiceHomePageBloc>().add(
                    FetchOrderServiceEvent(
                        status: widget.status, loadMore: false),
                  );
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            AppAssets.lionStrongbodyAi,
            width: MediaQuery.of(context).size.width * 0.12,
            height: MediaQuery.of(context).size.height * 0.3,
          ),
          const SizedBox(height: 16),
          const Text('No orders found for this status.'),
        ],
      ),
    );
  }

  Widget _buildOrdersList(orders, ServiceHomePageState state) {
    return Padding(
      padding: PaddingConstants.padSymHV14,
      child: ListView.separated(
        controller: _scrollController,
        itemCount: orders.length + (state.isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= orders.length) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: const Center(child: CircularProgressIndicator()),
            );
          }

          return GestureDetector(
            onTap: () {
              context.push(RouteName.orderServiceDetailPage, extra: {
                'orderServiceModel': orders[index],
                'serviceHomePageBloc': widget.serviceHomePageBloc
              });
            },
            child: ServiceCard(
              sellDetail: orders[index],
              serviceHomePageBloc: widget.serviceHomePageBloc,
            ),
          );
        },
        separatorBuilder: (context, index) => Gap(10.h),
      ),
    );
  }
}
