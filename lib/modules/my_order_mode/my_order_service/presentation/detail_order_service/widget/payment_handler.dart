import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/components/stripe_card_payment.dart';
import 'package:multime_app/core/config/environment.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/model/payment_result_data.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_payment_options.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_service/bloc/checkout_service_bloc.dart';
import 'package:multime_app/service/pusher_service.dart';

/// Handles all payment-related operations for order services
class OrderServicePaymentHandler {
  static PaymentMethod _selectedPaymentMethod = PaymentMethod.card;
  static Timer? _timeoutTimer;
  static int? _customerId;
  static bool _hasNavigatedToResult = false;
  static List<String> _fileUrls = [];

  // Callbacks for handling payment results
  static Function(BuildContext, OrderServiceModelUpdate, OrderServiceModel)?
      _onPaymentSuccess;
  static Function(BuildContext, OrderServiceModelUpdate, OrderServiceModel)?
      _onPaymentFailed;

  /// Get payment method as string
  static String _getPaymentMethodString(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.card:
        return 'card';
      case PaymentMethod.cash:
        return 'cash';
      case PaymentMethod.bankTransfer:
        return 'bank_transfer';
    }
  }

  /// Initialize payment handler with callbacks
  static void initialize({
    required List<String> fileUrls,
    PaymentMethod paymentMethod = PaymentMethod.card,
    Function(BuildContext, OrderServiceModelUpdate, OrderServiceModel)?
        onPaymentSuccess,
    Function(BuildContext, OrderServiceModelUpdate, OrderServiceModel)?
        onPaymentFailed,
  }) {
    _fileUrls = fileUrls;
    _selectedPaymentMethod = paymentMethod;
    _customerId = gs.uid;
    _hasNavigatedToResult = false;
    _onPaymentSuccess = onPaymentSuccess;
    _onPaymentFailed = onPaymentFailed;
  }

  /// Handle card payment process
  static void handleCardPayment(BuildContext context, OrderServiceModel order) {
    final existingOrderId = order.id;
    if (existingOrderId != null) {
      context
          .read<CheckoutServiceBloc>()
          .add(CheckoutServicePaymentEvent(existingOrderId));
    }
  }

  /// Show card payment bottom sheet
  static Future<void> showCardPaymentBottomSheet(BuildContext context,
      String clientSecret, OrderServiceModel order) async {
    if (_selectedPaymentMethod != PaymentMethod.card) return;

    // Get order data before opening modal to avoid provider context issue
    final checkoutState = context.read<CheckoutServiceBloc>().state;
    final orderData = checkoutState.paymentResponse.data;

    // Setup Order tracking
    if (orderData?.transactionId != null && order.customerId != null) {
      _setupOrderStatusListener(context, order, order.customerId!);
    }

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StripeCardPayment(
          amount: order.price ?? 0.0,
          publishableKey: AppConfig.stripePublishableKey,
          clientSecret: clientSecret,
          onPaymentResult: (result) {
            Navigator.of(context).pop();

            if (result['success'] == true) {
              // Stripe payment success - wait for backend confirmation
              _showPaymentProcessingDialog(context);
              _startTimeoutTimer(context, order);
            } else {
              // Stripe payment failed - cleanup
              _cleanupOrderTracking();
            }
          },
        );
      },
    );
  }

  /// Setup order status listener with context
  static void _setupOrderStatusListener(
      BuildContext context, OrderServiceModel order, int orderId) {
    print('🔗 Setting up Pusher listener for Order ID: $orderId');

    PusherService.subscribeToOrderChannel(orderId, (orderUpdate) {
      if (orderUpdate.isPaymentSuccessful) {
        print('✅ Processing as successful payment...');
        if (_onPaymentSuccess != null) {
          _onPaymentSuccess!(context, orderUpdate, order);
        } else {
          _defaultHandlePaymentSuccess(context, orderUpdate, order);
        }
      } else if (orderUpdate.isPaymentFailed) {
        print('❌ Processing as failed payment...');
        if (_onPaymentFailed != null) {
          _onPaymentFailed!(context, orderUpdate, order);
        } else {
          _defaultHandlePaymentFailed(context, orderUpdate, order);
        }
      } else {
        print('⏳ Payment status pending or unknown...');
      }
    });
  }

  /// Show payment processing dialog
  static void _showPaymentProcessingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false, // Prevent back button
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('Processing Payment...',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              const Text('Please wait while we confirm your order.',
                  textAlign: TextAlign.center),
              const SizedBox(height: 8),
              const Text('This may take up to 30 seconds.',
                  style: TextStyle(color: Colors.grey, fontSize: 12)),
            ],
          ),
        ),
      ),
    );
  }

  /// Start timeout timer
  static void _startTimeoutTimer(
      BuildContext context, OrderServiceModel order) {
    _timeoutTimer = Timer(const Duration(seconds: 30), () {
      if (!context.mounted) return;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!context.mounted) return;

        _cleanupOrderTracking();
        _dismissProcessingDialog(context);
        _handlePaymentTimeout(context, order);
      });
    });
  }

  /// Cleanup order tracking
  static void _cleanupOrderTracking() {
    _timeoutTimer?.cancel();
    if (_customerId != null) {
      PusherService.unsubscribeFromOrderChannel(_customerId!);
    }
  }

  /// Dismiss processing dialog
  static void _dismissProcessingDialog(BuildContext context) {
    if (!context.mounted) return;
    if (Navigator.canPop(context)) {
      Navigator.pop(context); // Close processing dialog
    }
  }

  /// Handle payment timeout
  static void _handlePaymentTimeout(
      BuildContext context, OrderServiceModel order) {
    if (!context.mounted) return;
    if (_hasNavigatedToResult) return;
    _hasNavigatedToResult = true;

    final successData = PaymentResultData.success(
      amount: order.price ?? 0.0,
      itemName: order.title ?? 'Service',
      quantity: 1,
      itemPrice: order.price ?? 0.0,
      unit: 'service',
      imageUrl: _fileUrls.isNotEmpty ? _fileUrls : null,
      shopName: order.seller?.displayName ?? 'Unknown Seller',
    ).copyWith(
      itemDescription: order.description,
      orderCode: order.orderNo,
      orderStatus: 'Paid',
      paymentMethod: 'Stripe',
      transactionDate: DateTime.now(),
      primaryActionText: 'View Order',
      secondaryActionText: 'Continue Shopping',
    );

    context.go(RouteName.paymentResultPage, extra: {
      'paymentResult': successData,
      'onPrimaryAction': () {
        context.go(RouteName.orderServiceDetailPage, extra: order);
      },
      'onSecondaryAction': () {
        context.go(RouteName.homeStrongBody);
      },
    });
  }

  /// Default handler for payment failed (public method)
  static void handlePaymentFailed(BuildContext context,
      OrderServiceModelUpdate update, OrderServiceModel order) {
    _defaultHandlePaymentFailed(context, update, order);
  }

  /// Default handler for payment success (public method)
  static void handlePaymentSuccess(BuildContext context,
      OrderServiceModelUpdate update, OrderServiceModel order) {
    _defaultHandlePaymentSuccess(context, update, order);
  }

  /// Default handler for payment failed
  static void _defaultHandlePaymentFailed(BuildContext context,
      OrderServiceModelUpdate update, OrderServiceModel order) {
    _cleanupOrderTracking();
    _dismissProcessingDialog(context);

    final failedData = PaymentResultData.failed(
      amount: order.price ?? 0.0,
      itemName: order.title ?? 'Service',
      quantity: 1,
      itemPrice: order.service?.price ?? 0.0,
      unit: 'service',
      imageUrl: _fileUrls,
      shopName: 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: update.order_no,
      orderTime: DateTime.now(),
      orderStatus: update.order_status,
      phone: order.customer?.mobile,
      recipientName: order.customer?.firstName,
      street: order.customer?.address,
      city: order.customer?.city,
      userId: order.customerId,
      primaryActionText: 'Retry',
      secondaryActionText: 'Change Payment Method',
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': failedData,
      'onPrimaryAction': () {
        context.pop();
      },
      'onSecondaryAction': () {
        context.push(RouteName.homeStrongBody);
      },
    });
  }

  /// Default handler for payment success
  static void _defaultHandlePaymentSuccess(BuildContext context,
      OrderServiceModelUpdate update, OrderServiceModel order) {
    _cleanupOrderTracking();
    _dismissProcessingDialog(context);

    final successData = PaymentResultData.success(
      amount: order.price ?? 0.0,
      itemName: order.title ?? 'Service',
      quantity: 1,
      itemPrice: order.service?.price ?? 0.0,
      unit: 'service',
      imageUrl: _fileUrls,
      shopName: 'Unknown Shop',
    ).copyWith(
      paymentMethod: _getPaymentMethodString(_selectedPaymentMethod),
      transactionDate: DateTime.now(),
      orderCode: update.order_no,
      orderTime: DateTime.now(),
      orderStatus: update.order_status,
      phone: order.customer?.mobile,
      recipientName: order.customer?.firstName,
      street: order.customer?.address,
      city: order.customer?.city,
      userId: order.customerId,
    );

    context.push(RouteName.paymentResultPage, extra: {
      'paymentResult': successData,
      'onPrimaryAction': () {
        context.push(RouteName.orderServiceDetailPage, extra: order);
      },
      'onSecondaryAction': () {
        context.push(RouteName.homeStrongBody);
      },
    });
  }

  /// Set payment method
  static void setPaymentMethod(PaymentMethod method) {
    _selectedPaymentMethod = method;
  }

  /// Get current payment method
  static PaymentMethod get selectedPaymentMethod => _selectedPaymentMethod;

  /// Get payment method string
  static String get paymentMethodString =>
      _getPaymentMethodString(_selectedPaymentMethod);

  /// Dispose resources
  static void dispose() {
    _cleanupOrderTracking();
    _hasNavigatedToResult = false;
    _fileUrls.clear();
    _onPaymentSuccess = null;
    _onPaymentFailed = null;
  }
}
