import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/detail_order_service/widget/payment_handler.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/widgets/app_bar/app_bar_title.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/widgets/dialog_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/widgets/checkout_payment_options.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_service/bloc/checkout_service_bloc.dart';
import 'package:multime_app/service/pusher_service.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class DetailOrderServicePage extends StatefulWidget {
  DetailOrderServicePage({
    super.key,
    required this.sellDetail,
    required this.serviceHomePageBloc,
  });
  final OrderServiceModel sellDetail;
  final ServiceHomePageBloc serviceHomePageBloc;

  static const _fallbackImage =
      'https://cdn.pixabay.com/photo/2016/10/25/12/28/iceland-1768744_1280.jpg';
  static const _fallbackAvatar =
      'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_960_720.png';

  @override
  State<DetailOrderServicePage> createState() => _DetailOrderServicePageState();
}

class _DetailOrderServicePageState extends State<DetailOrderServicePage> {
  int? customerId;
  List<String> fileUrls = [];

  String? _firstServiceImage() {
    final service = widget.sellDetail.service;
    if (service == null) return DetailOrderServicePage._fallbackImage;
    final imgs = service.image;
    if (imgs != null && imgs.isNotEmpty && imgs.first.isNotEmpty) {
      return imgs.first;
    }
    if (service.coverImage != null && service.coverImage!.isNotEmpty) {
      return service.coverImage;
    }
    return DetailOrderServicePage._fallbackImage;
  }

  @override
  void initState() {
    super.initState();
    customerId = gs.uid;
    if (widget.sellDetail.files != null &&
        widget.sellDetail.files!.isNotEmpty) {
      fileUrls = widget.sellDetail.files!
          .map((file) => file.url ?? '')
          .where((url) => url.isNotEmpty)
          .toList();
    }

    // Initialize payment handler
    OrderServicePaymentHandler.initialize(fileUrls: fileUrls);
  }

  @override
  void dispose() {
    OrderServicePaymentHandler.dispose();
    super.dispose();
  }

  String _profilePictureSafe() {
    final p = widget.sellDetail.seller?.profilePicture;
    return (p != null && p.isNotEmpty)
        ? p
        : DetailOrderServicePage._fallbackAvatar;
  }

  Widget _buildHeader(
      BuildContext context, String clientSecret, String status) {
    final status = widget.sellDetail.status ?? LocaleKeys.serviceDetail.tr();
    final orderCode = widget.sellDetail.id ?? '';
    final price = widget.sellDetail.price != null
        ? '\$ ${widget.sellDetail.price!.toStringAsFixed(2)}'
        : '-';

    return Card(
      color: Theme.of(context).whitePrimary(context),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text('Order Code: $orderCode',
                    style: Theme.of(context).textTheme.lightBodyLargeRegular),
                Gap(10.w),
                Icon(Icons.copy, size: 18.r, color: Colors.grey[500]),
              ],
            ),
            Gap(8.h),
            status == 'pending'
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text('Awaiting payment',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightHeadingMedium
                                  .copyWith(
                                      color: Theme.of(context)
                                          .alertInformationBase(context))),
                          Gap(10.w),
                          Text('20000',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyLargeSemiBold
                                  .copyWith(
                                      color: Theme.of(context)
                                          .alertInformationBase(context))),
                        ],
                      ),
                      Gap(10.h),
                      Text('US$price',
                          style:
                              Theme.of(context).textTheme.lightHeadingMedium),
                      Gap(12.h),
                      Row(
                        children: [
                          ElevatedButton(
                            onPressed: () {
                              if (OrderServicePaymentHandler
                                      .selectedPaymentMethod ==
                                  PaymentMethod.card) {
                                // Nếu chưa có clientSecret thì gọi API để lấy
                                if (clientSecret == '') {
                                  OrderServicePaymentHandler.handleCardPayment(
                                      context, widget.sellDetail);
                                  print(
                                      'Fetching client secret... $clientSecret');
                                } else {
                                  // Nếu đã có clientSecret thì mở luôn payment modal
                                  OrderServicePaymentHandler
                                      .showCardPaymentBottomSheet(context,
                                          clientSecret, widget.sellDetail);
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context)
                                  .alertInformationBase(context),
                              padding: EdgeInsets.all(10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6.r),
                              ),
                            ),
                            child: Text(
                              'Go to payment',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightBodyMediumMedium
                                  .copyWith(
                                    color:
                                        Theme.of(context).whitePrimary(context),
                                  ),
                            ),
                          ),
                          SizedBox(width: 12.w),
                          OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                color: Theme.of(context).blackPrimary(context),
                              ),
                              padding: EdgeInsets.all(10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6.r),
                              ),
                            ),
                            onPressed: () {
                              showServiceCancelDialog(context,
                                  orderServiceModel: widget.sellDetail,
                                  serviceHomePageBloc:
                                      widget.serviceHomePageBloc);
                            },
                            child: Text(LocaleKeys.cancelOrder.tr(),
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyMediumMedium),
                          ),
                        ],
                      ),
                    ],
                  )
                : status == 'accepted'
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('In Progress',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightHeadingMedium
                                  .copyWith(
                                      color: Theme.of(context)
                                          .alertAttentionBase(context))),
                          Gap(10.h),
                          Text('US$price',
                              style: Theme.of(context)
                                  .textTheme
                                  .lightHeadingMedium),
                          Gap(12.h),
                          SizedBox(
                            width: 100.w,
                            height: 40.h,
                            child: StrongBodyButton(
                              label: 'Dispute',
                              onPressed: () {},
                              textColor:
                                  Theme.of(context).blackPrimary(context),
                              backgroundColor:
                                  Theme.of(context).whitePrimary(context),
                              borderColor:
                                  Theme.of(context).blackPrimary(context),
                            ),
                          ),
                        ],
                      )
                    : status == 'delivered'
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Service Provided',
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightHeadingMedium
                                      .copyWith(
                                          color: Theme.of(context)
                                              .alertAttentionBase(context))),
                              Gap(10.h),
                              Text('US$price',
                                  style: Theme.of(context)
                                      .textTheme
                                      .lightHeadingMedium),
                              Gap(12.h),
                              Row(
                                children: [
                                  Expanded(
                                    flex: 4,
                                    child: StrongBodyButton(
                                      label: 'Confirm Completion',
                                      onPressed: () {
                                        context
                                            .read<ServiceHomePageBloc>()
                                            .add(UpdateOrderServiceBuyerStatus(
                                              widget.sellDetail.id!,
                                            ));
                                      },
                                      textColor: Theme.of(context)
                                          .whitePrimary(context),
                                      backgroundColor: Theme.of(context)
                                          .alertAttentionBase(context),
                                    ),
                                  ),
                                  Gap(10.w),
                                  Expanded(
                                    flex: 2,
                                    child: StrongBodyButton(
                                      label: 'Dispute',
                                      onPressed: () {},
                                      textColor: Theme.of(context)
                                          .blackPrimary(context),
                                      backgroundColor: Theme.of(context)
                                          .whitePrimary(context),
                                      borderColor: Theme.of(context)
                                          .blackPrimary(context),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                        : status == 'completed'
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Completed',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightHeadingMedium
                                          .copyWith(
                                              color: Theme.of(context)
                                                  .successBase(context))),
                                  Gap(10.h),
                                  Text('US$price',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightHeadingMedium),
                                  Gap(12.h),
                                  Row(
                                    children: [
                                      Expanded(
                                        flex: 4,
                                        child: StrongBodyButton(
                                          label: 'Buy again',
                                          onPressed: () {
                                            context.push(
                                                RouteName.serviceReviewPage);
                                          },
                                          textColor: Theme.of(context)
                                              .whitePrimary(context),
                                          backgroundColor: Theme.of(context)
                                              .successBase(context),
                                        ),
                                      ),
                                      Gap(10.w),
                                      Expanded(
                                        flex: 2,
                                        child: StrongBodyButton(
                                          label: 'Rating',
                                          onPressed: () {
                                            context.push(
                                                RouteName.serviceReviewPage,
                                                extra: {
                                                  "orderServiceModel":
                                                      widget.sellDetail,
                                                  "serviceHomePageBloc":
                                                      widget.serviceHomePageBloc
                                                });
                                          },
                                          textColor: Theme.of(context)
                                              .blackPrimary(context),
                                          backgroundColor: Theme.of(context)
                                              .whitePrimary(context),
                                          borderColor: Theme.of(context)
                                              .blackPrimary(context),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            : SizedBox.shrink()
          ],
        ),
      ),
    );
  }

  Widget _buildServiceCard(BuildContext context) {
    final image = _firstServiceImage();
    final title =
        widget.sellDetail.service?.title ?? widget.sellDetail.title ?? '-';
    final desc = widget.sellDetail.service?.description ??
        widget.sellDetail.description ??
        '';

    return Card(
      color: Theme.of(context).whitePrimary(context),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      elevation: 1,
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                    style: Theme.of(context).textTheme.lightHeadingSmall,
                  ),
                ),
                Container(
                  width: 84.w,
                  height: 56.h,
                  margin: EdgeInsets.only(left: 5.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    color: Colors.grey.shade200,
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: Image.network(
                    image ?? DetailOrderServicePage._fallbackImage,
                    width: 84.w,
                    height: 64.h,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => Image.network(
                      DetailOrderServicePage._fallbackImage,
                      width: 84.w,
                      height: 64.h,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
            Gap(6.h),
            Text(TFormatter.htmlToFormattedText(desc),
                style: Theme.of(context).textTheme.lightBodyLargeMedium),
          ],
        ),
      ),
    );
  }

  // Widget _buildMessageCard() {
  Widget _buildSellerInfo(BuildContext context) {
    final seller = widget.sellDetail.seller;
    final name = seller?.displayName;
    // final profession = seller?.profession ?? '';

    return Card(
      color: Theme.of(context).whitePrimary(context),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Seller information',
                style: Theme.of(context).textTheme.lightHeadingSmall),
            Gap(12.h),
            Text('$name',
                style: Theme.of(context).textTheme.lightBodyMediumMedium),
            Gap(12.h),
            Row(
              children: [
                SvgPicture.asset(AppAssets.briefcase,
                    color: Theme.of(context).alertAttentionBase(context)),
                Gap(8.w),
                Text('Dentist',
                    style: Theme.of(context).textTheme.lightBodyMediumSemiBold),
                Gap(10.w),
                SvgPicture.asset(AppAssets.locationTick,
                    color: Theme.of(context).primary(context)),
                Gap(8.w),
                Text('Viet Nam',
                    style: Theme.of(context).textTheme.lightBodyMediumSemiBold),
              ],
            ),
            Gap(12.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: Theme.of(context).blackPrimary(context),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(AppAssets.shopSvg),
                          Gap(10.w),
                          Text(
                            'View Shop',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodySmallRegular,
                          ),
                        ],
                      )),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: Theme.of(context).blackPrimary(context),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                      ),
                      onPressed: () {},
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(AppAssets.messageAppbarSvg),
                          Gap(10.w),
                          Text(
                            'Chat',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodySmallRegular,
                          ),
                        ],
                      )),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo(BuildContext context) {
    final orderNumber = widget.sellDetail.id ?? '-';
    final orderDate = widget.sellDetail.createdAt != null
        ? DateFormat('yyyy-MM-dd HH:mm:ss').format(widget.sellDetail.createdAt!)
        : '-';
    final paymentDate = widget.sellDetail.paymentDate != null
        ? DateFormat('yyyy-MM-dd HH:mm:ss')
            .format(widget.sellDetail.paymentDate!)
        : '-';

    return Card(
      color: Theme.of(context).whitePrimary(context),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Basic information of the order',
                style: Theme.of(context).textTheme.lightHeadingSmall),
            Gap(12.h),
            _infoRow(context, 'Order number', "$orderNumber"),
            _infoRow(context, 'Order date', orderDate),
            _infoRow(context, 'Payment date', paymentDate),
          ],
        ),
      ),
    );
  }

  Widget _infoRow(BuildContext context, String label, value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: Row(
        children: [
          Expanded(
              child: Text(label,
                  style: Theme.of(context).textTheme.lightBodyMediumRegular)),
          Text(value,
              style: Theme.of(context).textTheme.lightBodyMediumSemiBold),
        ],
      ),
    );
  }

  void _handleCardPayment(BuildContext context) {
    OrderServicePaymentHandler.handleCardPayment(context, widget.sellDetail);
  }

  // Delegate to payment handler methods
  void _handlePaymentSuccess(OrderServiceModelUpdate update) {
    OrderServicePaymentHandler.handlePaymentSuccess(
        context, update, widget.sellDetail);
  }

  void _handlePaymentFailed(OrderServiceModelUpdate update) {
    OrderServicePaymentHandler.handlePaymentFailed(
        context, update, widget.sellDetail);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CheckoutServiceBloc, CheckoutServiceState>(
        listener: (context, state) {
      // Handle service response errors
      if (state.serviceResponse.status == Status.error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(state.serviceResponse.message ?? 'Create order error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      // Handle payment response
      else if (state.paymentResponse.status == Status.error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(state.paymentResponse.message ?? 'Create payment error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      } else if (state.paymentResponse.status == Status.completed &&
          state.paymentResponse.data?.clientSecret != null &&
          state.paymentResponse.data!.clientSecret.isNotEmpty) {
        OrderServicePaymentHandler.showCardPaymentBottomSheet(
          context,
          state.paymentResponse.data!.clientSecret,
          widget.sellDetail,
        );
      } else if (state.serviceResponse.status == Status.completed &&
          state.serviceResponse.data?.clientSecret != null &&
          state.serviceResponse.data!.clientSecret.isNotEmpty) {
        OrderServicePaymentHandler.showCardPaymentBottomSheet(
          context,
          state.serviceResponse.data!.clientSecret,
          widget.sellDetail,
        );
      }
    }, builder: (context, state) {
      return Scaffold(
        appBar: CustomAppBarService(
          title: LocaleKeys.serviceDetail.tr(),
        ),
        body: BlocListener<ServiceHomePageBloc, ServiceHomePageState>(
          listener: (context, state) {
            if (state.isLoading) {
              AppLoader.show(context);
            } else {
              AppLoader.hide();
            }
            if (state.isSuccess) {
              context.pop();
            }
          },
          child: Container(
            color: Theme.of(context).greyScale50(context),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(
                        context,
                        state.paymentResponse.data?.clientSecret ??
                            state.serviceResponse.data?.clientSecret ??
                            '',
                        state.serviceResponse.data?.status ?? ''),
                    Gap(12.h),
                    if (widget.sellDetail.status == 'delivered')
                      Container(
                        width: MediaQuery.of(context).size.width,
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(
                              Radius.circular(AppRadius.radius10)),
                          color: Theme.of(context).whitePrimary(context),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Proof of Delivery',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyLargeBold),
                            Gap(5.h),
                            Text(
                                'Images and videos uploaded by the expert as proof of service completion.',
                                style: Theme.of(context)
                                    .textTheme
                                    .lightBodyMediumMedium
                                    .copyWith(
                                      color: Theme.of(context)
                                          .greyScale600(context),
                                    )),
                            Gap(5.h),
                            Row(
                              children: [
                                Expanded(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: CustomImage(
                                      path:
                                          'https://demoda.vn/wp-content/uploads/2022/02/anh-que-huong-ruong-bac-thang-don-so-1.jpg',
                                      imageType: ImageType.network,
                                    ),
                                  ),
                                ),
                                Gap(10.w),
                                Expanded(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: CustomImage(
                                        path:
                                            'https://demoda.vn/wp-content/uploads/2022/02/anh-que-huong-ruong-bac-thang-don-so-1.jpg',
                                        imageType: ImageType.network),
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    Gap(12.h),
                    _buildServiceCard(context),
                    Gap(12.h),
                    _buildSellerInfo(context),
                    Gap(12.h),
                    _buildBasicInfo(context),
                    Gap(24.h),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
