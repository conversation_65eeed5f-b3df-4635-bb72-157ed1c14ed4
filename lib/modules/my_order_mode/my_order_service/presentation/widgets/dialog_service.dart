import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';

Future<void> showServiceCancelDialog(BuildContext context,
    {required OrderServiceModel orderServiceModel,
    required ServiceHomePageBloc serviceHomePageBloc}) async {
  await showDialog(
    context: context,
    builder: (context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text("Are you sure",
                  style: Theme.of(context).textTheme.lightBodyLargeBold),
              Gap(5.h),
              Text("you want to cancel this order?",
                  style: Theme.of(context).textTheme.lightBodyLargeBold),
              Gap(8.h),
              Row(
                children: [
                  Expanded(
                    child: StrongBodyButton(
                      label: 'Cancel',
                      onPressed: () {
                        context.pop();
                      },
                      textColor: Theme.of(context).blackPrimary(context),
                      backgroundColor: Theme.of(context).whitePrimary(context),
                      borderColor: Theme.of(context).blackPrimary(context),
                    ),
                  ),
                  Gap(10.w),
                  Expanded(
                    child: StrongBodyButton(
                      label: 'Confirm',
                      onPressed: () {},
                      textColor: Theme.of(context).whitePrimary(context),
                      backgroundColor: Theme.of(context).secondary(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}
