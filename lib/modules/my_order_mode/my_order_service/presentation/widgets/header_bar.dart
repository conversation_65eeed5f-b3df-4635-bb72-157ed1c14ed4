import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../../core/constants/app_assets.dart';

class HeaderBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBack;
  const HeaderBar({super.key, required this.title, this.onBack});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: false,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              context.pop();
            },
            child: SvgPicture.asset(AppAssets.arrowLeftSvg),
          ),
          const Spacer(),
          const Spacer(),
          Text(
            title,
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
          const Spacer(),
          const Spacer(),
          // GestureDetector(
          //   onTap:() {
          //     context.push(RouteName.serviceReviewSuccess);
          //   },
          //   child: Container(
          //     height: 40,
          //     width: 84,
          //     decoration: BoxDecoration(
          //       color: Theme.of(context).errorBase(context),
          //       borderRadius: BorderRadius.circular(8),
          //     ),
          //     child: Center(
          //       child: Text(
          //         LocaleKeys.send.tr(),
          //         style: Theme.of(context)
          //             .textTheme
          //             .lightBodyLargeMedium
          //             .copyWith(
          //           color: Colors.white,
          //         ),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
