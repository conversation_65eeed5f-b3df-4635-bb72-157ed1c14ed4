import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../../../core/constants/app_assets.dart';

class CustomAppBarService extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBack;
  const CustomAppBarService({super.key, required this.title, this.onBack});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: false,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: onBack ?? () => context.pop(),
            child: SvgPicture.asset(AppAssets.arrowLeftSvg),
          ),
          const Spacer(),
          Text(
            title,
            style: Theme.of(context).textTheme.lightHeadingMedium.copyWith(fontWeight: FontWeight.w700),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
