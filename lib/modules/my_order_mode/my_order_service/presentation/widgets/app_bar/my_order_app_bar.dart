import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_colors.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class MyOrderAppBar extends StatelessWidget {
  const MyOrderAppBar({super.key, required this.controller});
  final TabController controller;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TabBar(
        unselectedLabelColor: AppColors.textSecondary,
        unselectedLabelStyle: Theme.of(context).textTheme.lightBodyLargeRegular,
        controller: controller,
        tabAlignment: TabAlignment.center,
        isScrollable: true,
        indicatorColor: Theme.of(context).primary(context),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Theme.of(context).disabledLight(context),
        labelColor: Theme.of(context).primary(context),
        labelStyle: Theme.of(context).textTheme.lightBodyLargeBold,
        tabs: const [
          Tab(
            text: 'Pending',
          ),
          Tab(
            text: 'In Progress',
          ),
          Tab(
            text: 'Delivered',
          ),
          Tab(
            text: 'Completed',
          ),
          Tab(
            text: 'Cancel',
          ),
          Tab(
            text: 'Expired',
          ),
          Tab(
            text: 'Disputed',
          ),
          Tab(
            text: 'Refund',
          ),
        ],
      ),
    );
  }
}
