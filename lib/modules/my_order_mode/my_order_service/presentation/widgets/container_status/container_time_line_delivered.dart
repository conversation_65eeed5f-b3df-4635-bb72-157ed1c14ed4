import 'package:flutter/material.dart';

import '../../../../../../core/constants/app_assets.dart';
import '../../../../data/model/my_order_service_model.dart';
import '../text/order_status_timeline.dart';
import '../text/order_status_timeline_delivered.dart';

class ContainerTimeLineDelivered extends StatelessWidget {
  final MyOrderServiceModel sellDetail;
  const ContainerTimeLineDelivered({super.key, required this.sellDetail});

  @override
  Widget build(BuildContext context) {
    List<Widget> timelines = [];

    if (sellDetail.statusServiceExecution == 'Seller is working on the order') {
      timelines.add(const OrderStatusTimeline(
        image: AppAssets.myOrderRefresh,
        title: 'Service Execution',
        text: 'Customer requested modifications',
      ));
    }

    if (sellDetail.statusServiceExecution == 'Seller is working on the order') {
      timelines.addAll([
        const OrderStatusTimelineDelivered(
          image: AppAssets.myOrderCorrect,
          title: 'Delivery update',
          text: 'Delivery final update',
        ),
      ]);
    }
    if (sellDetail.statusServiceExecution == 'Seller is working on the order') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderTime,
        title: 'Service Execution',
        text: '${sellDetail.statusServiceExecution}',
      ));
      timelines.add(const OrderStatusTimeline(
        image: AppAssets.myOrderCorrect,
        title: 'Seller Confirmation',
        text: 'Seller confirmed the order',
      ));
    }

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Payment',
      text: 'Payment completed, funds held in multime',
    ));
    timelines.add(OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Order created',
      text: '5 Dec 2024 - Order #${sellDetail.idOrder}',
    ));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: timelines,
    );
  }
}

