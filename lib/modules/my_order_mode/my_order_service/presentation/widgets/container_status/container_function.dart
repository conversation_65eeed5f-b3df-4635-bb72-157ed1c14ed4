import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

import '../../../../../../core/themes/app_colors.dart';

class ContainerFunction extends StatelessWidget {
  final String image;
  final String text;
  const ContainerFunction({super.key, required this.image, required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: AppColors.lightGrey,
          borderRadius: BorderRadius.circular(50)
      ),
      child: Padding(
        padding: EdgeInsets.all(10.0),
        child: Row(
          children: [
            SvgPicture.asset(image),
            Gap(10.w),
            Text(text, style: Theme.of(context).textTheme.lightBodySmallRegular,)
          ],
        ),
      ),
    );
  }
}
