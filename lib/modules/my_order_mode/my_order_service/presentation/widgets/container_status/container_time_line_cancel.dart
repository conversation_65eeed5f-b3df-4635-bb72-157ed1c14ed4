import 'package:flutter/material.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import '../../../../../../core/constants/app_assets.dart';
import '../text/order_status_timeline.dart';

class ContainerTimeLineCompleted extends StatelessWidget {
  final OrderServiceModel sellDetail;
  const ContainerTimeLineCompleted({super.key, required this.sellDetail});

  @override
  Widget build(BuildContext context) {
    List<Widget> timelines = [];

    // if (sellDetail.orderCancel == 'Waiting for Supplier to confirm cancellation') {
    //   timelines.add(OrderStatusTimeline(
    //     image: AppAssets.myOrderFalse,
    //     title: 'Order cancel',
    //     text: '${sellDetail.orderCancel}',
    //   ));
    // }

    // if (sellDetail.orderCancel == 'Seller accepts cancellation request, order is canceled') {
    //   timelines.add(OrderStatusTimeline(
    //     image: AppAssets.myOrderFalse,
    //     title: 'Order cancel',
    //     text: '${sellDetail.orderCancel}',
    //   ));
    // }

    // if (sellDetail.orderCancel == 'Seller rejects cancellation -  order continues') {
    //   timelines.add(OrderStatusTimeline(
    //     image: AppAssets.myOrderFalse,
    //     title: 'Order cancel',
    //     text: '${sellDetail.orderCancel}',
    //   ));
    // }

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderTime,
      title: 'Service Execution',
      text: 'Seller is working on the order',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Seller Confirmation',
      text: 'Seller confirmed the order',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Payment',
      text: 'Payment completed, funds held in multime',
    ));
    timelines.add(OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Order created',
      text: '5 Dec 2024 - Order #${sellDetail.id}',
    ));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: timelines,
    );
  }
}
