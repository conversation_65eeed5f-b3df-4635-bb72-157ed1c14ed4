import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ContainerStatusDetail extends StatefulWidget {
  final String status;
  const ContainerStatusDetail({super.key, required this.status});

  @override
  State<ContainerStatusDetail> createState() => _ContainerStatusDetailState();
}

class _ContainerStatusDetailState extends State<ContainerStatusDetail> {
  @override
  Widget build(BuildContext context) {
    String status = widget.status;

    Color backgroundColor;
    Color textColor;

    switch (status) {
      case 'Pending':
        backgroundColor = Theme.of(context).successLight(context);
        textColor = Theme.of(context).successBase(context);
        break;
      case 'In Progress':
        backgroundColor = Theme.of(context).informationLight(context);
        textColor = Theme.of(context).informationBase(context);
        break;
      case 'Completed':
        backgroundColor = Theme.of(context).attentionLight(context);
        textColor = Theme.of(context).attentionBase(context);
        break;
      case 'Delivered':
        backgroundColor = Theme.of(context).successLight(context);
        textColor = Theme.of(context).informationBase(context);
        break;
      case 'Cancel':
        backgroundColor = Theme.of(context).focusHighlightLight(context);
        textColor = Theme.of(context).focusHighlightBase(context);
        break;
      case 'Late':
        backgroundColor = Theme.of(context).greyScale200(context);
        textColor = Theme.of(context).blackPrimary(context);
        break;
      case 'Disputed':
        backgroundColor = Theme.of(context).errorLight(context);
        textColor = Theme.of(context).errorBase(context);
        break;
      default:
        backgroundColor = Theme.of(context).errorLight(context);
        textColor = Theme.of(context).errorBase(context);
        break;
    }

    return Container(
      height: 28.h,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Center(
        child: Text(
          status == 'Refund' ? status = 'Disputed' : status = status,
          style: Theme.of(context)
              .textTheme
              .lightBodySmallBold
              .copyWith(color: textColor),
        ),
      ),
    );
  }
}
