import 'package:flutter/material.dart';
import '../../../../../../core/constants/app_assets.dart';
import '../../../../data/model/my_order_service_model.dart';
import '../text/order_status_timeline.dart';

class ContainerTimeLineRefund extends StatelessWidget {
  final MyOrderServiceModel sellDetail;
  const ContainerTimeLineRefund({super.key, required this.sellDetail});

  @override
  Widget build(BuildContext context) {
    List<Widget> timelines = [];

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderInfo,
      title: 'Order disputed',
      text: 'Dispute resolved',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderWarning,
      title: 'Service Execution',
      text: 'Order is past the expected completion date',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderTime,
      title: 'Service Execution',
      text: '<PERSON><PERSON> is working on the order',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Seller Confirmation',
      text: '<PERSON><PERSON> confirmed the order',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Payment',
      text: 'Payment completed, funds held in multime',
    ));
    timelines.add(OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Order created',
      text: '5 Dec 2024 - Order #${sellDetail.idOrder}',
    ));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: timelines,
    );
  }
}

