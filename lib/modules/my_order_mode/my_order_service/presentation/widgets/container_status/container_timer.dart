import 'dart:async';
import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/theme.dart';

class ContainerTimer extends StatefulWidget {
  final DateTime startTime;
  final DateTime endTime;

  const ContainerTimer(
      {super.key, required this.startTime, required this.endTime});

  @override
  _ContainerTimerState createState() => _ContainerTimerState();
}

class _ContainerTimerState extends State<ContainerTimer> {
  late Timer timer;
  Duration remainingTime = Duration.zero;

  @override
  void initState() {
    super.initState();
    debugPrint("Start Time: ${widget.startTime}");
    debugPrint("End Time: ${widget.endTime}");

    _calculateRemainingTime();
    _startTimer();
  }

  void _calculateRemainingTime() {
    final now = DateTime.now();
    setState(() {
      if (now.isBefore(widget.startTime)) {
        // Nếu chưa đến thời gian bắt đầu, đếm ngược đến thời gian bắt đầu
        remainingTime = widget.startTime.difference(now);
      } else {
        // Nếu đã đến thời gian bắt đầu, đếm ngược đến thời gian kết thúc
        remainingTime = widget.endTime.difference(now);
        if (remainingTime.isNegative) {
          remainingTime = Duration.zero;
        }
      }
    });
  }

  void _startTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _calculateRemainingTime();
      if (remainingTime.inSeconds <= 0) {
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    int days = remainingTime.inDays;
    int hours = remainingTime.inHours.remainder(24);
    int minutes = remainingTime.inMinutes.remainder(60);
    int seconds = remainingTime.inSeconds.remainder(60);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        height: 102,
        decoration: BoxDecoration(
          color: Theme.of(context).greyScale50(context),
          border: Border.all(
            width: 1,
            color: Theme.of(context).informationBase(context),
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.1),
              spreadRadius: 0.5,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              _buildTimeBox('$days', 'Days'),
              const Spacer(),
              _buildTimeBox('$hours', 'Hours'), // Sửa House thành Hours
              const Spacer(),
              _buildTimeBox('$minutes', 'Minutes'),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeBox(String value, String label) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
