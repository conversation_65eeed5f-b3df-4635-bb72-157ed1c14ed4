import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class ContainerStatus extends StatefulWidget {
  final String status;
  const ContainerStatus({super.key, required this.status});

  @override
  State<ContainerStatus> createState() => _ContainerStatusState();
}

class _ContainerStatusState extends State<ContainerStatus> {
  @override
  Widget build(BuildContext context) {
    // String status = widget.detail['status'] ?? ''; // Lấy giá trị status, nếu null thì mặc định là ''

    Color backgroundColor;
    Color textColor;

    switch (widget.status) {
      case 'pending':
        backgroundColor = Theme.of(context).warningLight(context);
        textColor = Theme.of(context).warningBase(context);
        break;
      case 'in_progress':
        backgroundColor = Theme.of(context).informationLight(context);
        textColor = Theme.of(context).informationBase(context);
        break;
      case 'completed':
        backgroundColor = Theme.of(context).attentionLight(context);
        textColor = Theme.of(context).attentionBase(context);
        break;
      case 'delivered':
        backgroundColor = Theme.of(context).successLight(context);
        textColor = Theme.of(context).successBase(context);
        break;
      case 'cancel':
        backgroundColor = Theme.of(context).focusHighlightLight(context);
        textColor = Theme.of(context).focusHighlightBase(context);
        break;
      case 'expired':
        backgroundColor = Theme.of(context).greyScale200(context);
        textColor = Theme.of(context).blackPrimary(context);
        break;
      case 'disputed':
        backgroundColor = Theme.of(context).errorLight(context);
        textColor = Theme.of(context).errorBase(context);
        break;
      default:
        backgroundColor = Theme.of(context).greyScale200(context);
        textColor = Theme.of(context).greyScale500(context);
        break;
    }

    return Container(
      height: 28.h,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Center(
        child: Text(
          widget.status,
          style: Theme.of(context)
              .textTheme
              .lightBodySmallBold
              .copyWith(color: textColor),
        ),
      ),
    );
  }
}
