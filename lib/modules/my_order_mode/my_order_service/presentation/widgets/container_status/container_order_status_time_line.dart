import 'package:flutter/material.dart';
import '../../../../../../core/constants/app_assets.dart';
import '../../../../data/model/my_order_service_model.dart';
import '../text/order_status_timeline.dart';

class ContainerOrderStatusTimeLine extends StatelessWidget {
  final MyOrderServiceModel sellDetail;
  const ContainerOrderStatusTimeLine({super.key, required this.sellDetail});

  @override
  Widget build(BuildContext context) {
    List<Widget> timelines = [];

    if (sellDetail.orderStatus == 'Seller is working on the order' &&
        sellDetail.deliveryUpdate != '' && sellDetail.status == 'delivered') {
      timelines.addAll([
        const OrderStatusTimeline(
          image: AppAssets.myOrderCorrect,
          title: 'Delivery update',
          text: 'Delivery final update',
        ),
      ]);
    }

    if (sellDetail.orderStatus == 'Seller has 24h to respond') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderTime,
        title: 'Seller Confirmation',
        text: sellDetail.orderStatus ?? '',
      ));
    } else if (sellDetail.orderStatus == 'Seller did not respond within 24h, order canceled.') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderFalse,
        title: 'Seller Confirmation',
        text: sellDetail.orderStatus ?? '',
      ));
    } else if (sellDetail.orderStatus == 'Seller confirmed the order') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderCorrect,
        title: 'Seller Confirmation',
        text: sellDetail.orderStatus ?? '',
      ));
    }

    if (sellDetail.orderStatus == 'Seller is working on the order' &&
        sellDetail.deliveryUpdate == '') {
      timelines.addAll([
        OrderStatusTimeline(
          image: AppAssets.myOrderTime,
          title: 'Service Execution',
          text: sellDetail.orderStatus ?? '',
        ),
        const OrderStatusTimeline(
          image: AppAssets.myOrderCorrect,
          title: 'Seller Confirmation',
          text: 'Seller confirmed the order',
        ),
      ]);
    }

    if (sellDetail.deliveryUpdate == 'Order is past the expected completion date') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderWarning,
        title: 'Delivery update',
        text: sellDetail.deliveryUpdate ?? '',
      ));
    } else if (sellDetail.deliveryUpdate == 'Delivery date changed') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderCalender,
        title: 'Delivery update',
        text: sellDetail.deliveryUpdate ?? ''
      ));
    } else if (sellDetail.deliveryUpdate == 'Delivery extended approved') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderClock,
        title: 'Delivery update',
        text: sellDetail.deliveryUpdate ?? '',
      ));
    } else if (sellDetail.deliveryUpdate == 'Delivery late dispute - Seller no response') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderWarning,
        title: 'Delivery update',
        text: sellDetail.deliveryUpdate ?? '',
      ));
    }

    if (sellDetail.deliveryUpdate != '') {
      timelines.addAll([
        OrderStatusTimeline(
          image: AppAssets.myOrderTime,
          title: 'Service Execution',
          text: sellDetail.orderStatus ?? '',
        ),
        const OrderStatusTimeline(
          image: AppAssets.myOrderCorrect,
          title: 'Seller Confirmation',
          text: 'Seller confirmed the order',
        ),
      ]);
    }
    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Payment',
      text: 'Payment completed, funds held in multime',
    ));
    timelines.add(OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Order created',
      text: '5 Dec 2024 - Order #${sellDetail.idOrder}',
    ));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: timelines,
    );
  }
}

