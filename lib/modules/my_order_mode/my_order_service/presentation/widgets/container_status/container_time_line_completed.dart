import 'package:flutter/material.dart';

import '../../../../../../core/constants/app_assets.dart';
import '../../../../data/model/my_order_service_model.dart';
import '../text/order_status_timeline.dart';
import '../text/order_status_timeline_delivered.dart';

class ContainerTimeLineCompleted extends StatelessWidget {
  final MyOrderServiceModel sellDetail;
  const ContainerTimeLineCompleted({super.key, required this.sellDetail});

  @override
  Widget build(BuildContext context) {
    List<Widget> timelines = [];

    if (sellDetail.orderCompletion == 'Buyer Accepted - ORDER_CONFIRMED') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderCorrect,
        title: 'Order Completion',
        text: '${sellDetail.orderCompletion}',
      ));
    }

    if (sellDetail.orderCompletion == 'Auto Completed - Buyer did not respond within 3 days') {
      timelines.add(OrderStatusTimeline(
        image: AppAssets.myOrderCorrect,
        title: 'Order Completion',
        text: '${sellDetail.orderCompletion}',
      ));
    }

    timelines.addAll([
      const OrderStatusTimelineDelivered(
        image: AppAssets.myOrderCorrect,
        title: 'Delivery update',
        text: 'Delivery final update',
      ),
    ]);

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Service Execution',
      text: 'Seller has completed and delivered the service',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Seller Confirmation',
      text: 'Seller confirmed the order',
    ));

    timelines.add(const OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Payment',
      text: 'Payment completed, funds held in multime',
    ));
    timelines.add(OrderStatusTimeline(
      image: AppAssets.myOrderCorrect,
      title: 'Order created',
      text: '5 Dec 2024 - Order #${sellDetail.idOrder}',
    ));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: timelines,
    );
  }
}

