import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/my_order_service_model.dart';

import '../../../../../../core/constants/app_assets.dart';

class ConnectToSeller extends StatefulWidget {
  final MyOrderServiceModel sellDetail;
  const ConnectToSeller({super.key, required this.sellDetail});

  @override
  State<ConnectToSeller> createState() => _ConnectToSellerState();
}

class _ConnectToSellerState extends State<ConnectToSeller> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                // <PERSON><PERSON>n thị ảnh đại diện của seller
                Container(
                  height: 40.h,
                  width: 40.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: NetworkImage(widget.sellDetail.avatarUrl),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const Gap(8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.sellDetail.name,
                      style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    const Gap(2),
                    Text(
                      widget.sellDetail.userStatus,
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallSemiBold
                          .copyWith(
                              color: Theme.of(context).successBase(context)),
                    ),
                  ],
                ),
              ],
            ),
            SvgPicture.asset(AppAssets.myOrderMessages)
          ],
        ),
        Divider(
          color: Theme.of(context).greyScale100(context),
          thickness: 1, // Độ dày
          height: 20, // Khoảng cách giữa các phần tử xung quanh
        ),
        Gap(12.h),
      ],
    );
  }
}
