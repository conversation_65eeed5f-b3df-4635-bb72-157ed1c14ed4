import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/upload_image.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/button/basic_button.dart';
import '../../../../../../app/routers/routers_name.dart';
import '../../../../../../core/constants/app_assets.dart';
import '../../../../../../core/themes/app_colors.dart';
import '../container_status/container_status_detail.dart';
import '../header_bar.dart';

class ReviewDetail extends StatefulWidget {
  final Map<String, dynamic> sellDetail;
  const ReviewDetail({super.key, required this.sellDetail});

  @override
  State<ReviewDetail> createState() => _ReviewDetailState();
}

class _ReviewDetailState extends State<ReviewDetail> {
  final TextEditingController _descriptionController = TextEditingController();
  int numberText = 0;
  final int maxLength = 50;
  bool hasImage = false;

  void _onImageChange(bool value) {
    setState(() {
      hasImage = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const HeaderBar(
        title: 'Review order',
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Gap(20.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Details',
                    style: Theme.of(context).textTheme.lightBodyLargeBold,
                  ),
                  ContainerStatusDetail(status: widget.sellDetail['status'])
                ],
              ),
              Gap(16.h),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).greyScale100(context),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                  child: Row(
                    children: [
                      Container(
                        height: 52.h,
                        width: 79.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          image: DecorationImage(
                            image:
                                NetworkImage(widget.sellDetail['imageDetail']),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Gap(10.h),
                      Expanded(
                        child: Text(
                          widget.sellDetail['description'],
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyMediumSemiBold,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          softWrap: true,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Gap(22.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    'Order summary',
                    style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                  ),
                ],
              ),
              _infomationOrder('Price', '\$${widget.sellDetail['money']}'),
              _infomationOrder(
                  'Service fee', '\$${widget.sellDetail['money'] * 0.05}'),
              Divider(
                color: Theme.of(context).greyScale100(context),
                thickness: 1,
                height: 20,
              ),
              _infomationOrder('Total cost',
                  '\$${widget.sellDetail['money'] + widget.sellDetail['money'] * 0.05}'),
              Gap(32.h),
              Divider(
                color: Theme.of(context).greyScale100(context),
                thickness: 1,
                height: 20,
              ),
              Gap(18.h),
              Row(
                children: [
                  Icon(Icons.error_outline,
                      color: Theme.of(context).whitePrimary(context), size: 26),
                  Gap(8.w),
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        style:
                            Theme.of(context).textTheme.lightBodySmallRegular,
                        children: [
                          const TextSpan(
                            text: 'It’s totally ',
                          ),
                          TextSpan(
                            text: 'safe ',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodySmallBold
                                .copyWith(color: AppColors.successBase),
                          ),
                          const TextSpan(
                            text: 'to purchase at ',
                          ),
                          TextSpan(
                            text: 'Multi.Me ',
                            style: Theme.of(context)
                                .textTheme
                                .lightBodySmallBold
                                .copyWith(color: AppColors.successBase),
                          ),
                          const TextSpan(
                            text:
                                '. Your money will only be sent to the seller after your confirmation on receiving the product, else it will return to you.',
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Gap(16.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Quality of\nitem',
                    style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                  ),
                  RatingBar.builder(
                    initialRating: 4,
                    minRating: 1,
                    direction: Axis.horizontal,
                    allowHalfRating: true,
                    itemCount: 5,
                    itemSize: 24,
                    itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                    itemBuilder: (context, _) => const Icon(
                      Icons.star,
                      color: Colors.amber,
                    ),
                    onRatingUpdate: (rating) {},
                  ),
                  Text(
                    'Excellent',
                    style: Theme.of(context)
                        .textTheme
                        .lightBodyLargeSemiBold
                        .copyWith(color: Colors.amber),
                  ),
                ],
              ),
              Gap(16.h),
              Text(
                'Add 50 characters and 1 product image to rate your order',
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumMedium
                    .copyWith(color: AppColors.textSecondary),
              ),
              Gap(16.h),
              UploadImage(
                onImageChange: _onImageChange,
              ),
              Gap(16.h),
              Container(
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).disabledLight(context),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: TextField(
                    controller: _descriptionController,
                    maxLength: maxLength,
                    decoration: InputDecoration(
                      hintText: 'Please share your comments for this product.',
                      hintStyle: Theme.of(context)
                          .textTheme
                          .lightBodyMediumMedium
                          .copyWith(color: AppColors.textSecondary),
                      border: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      counterText: '',
                      isDense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                    maxLines: null,
                    expands: true,
                  ),
                ),
              ),
              Gap(32.h),
              BasicAppButton(
                onPressed: () {
                  context.push(RouteName.serviceReviewSuccess);
                },
                title: 'Send',
                sizeTitle: 18,
                fontW: FontWeight.w600,
                colorButton: Theme.of(context).informationBase(context),
                height: 44,
                radius: 10,
              ),
              Gap(32.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _infomationOrder(String textKey, textValue) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            textKey,
            style: textKey == 'Total cost'
                ? Theme.of(context).textTheme.lightBodyMediumBold
                : Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(color: Theme.of(context).greyScale800(context)),
          ),
          Text(
            textValue,
            style: textKey == 'Total cost'
                ? Theme.of(context).textTheme.lightBodyMediumBold
                : Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(color: Theme.of(context).greyScale800(context)),
          ),
        ],
      ),
    );
  }

  String getCurrentTime() {
    DateTime now = DateTime.now();
    String formattedTime = DateFormat('MM/dd/yy  hh:mm').format(now);
    return formattedTime;
  }

  Widget _textTitle(String text, String icon) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SvgPicture.asset(icon),
            Gap(10.w),
            Text(
              text,
              style: Theme.of(context).textTheme.lightBodyLargeMedium,
            )
          ],
        ),
        SvgPicture.asset(AppAssets.arrowRightSvg),
      ],
    );
  }
}
