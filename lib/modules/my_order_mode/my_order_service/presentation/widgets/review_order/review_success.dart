import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/social_mode/presentation/widgets/button/basic_button.dart';

import '../../../../../../app/routers/routers_name.dart';

class ReviewSuccess extends StatelessWidget {
  const ReviewSuccess({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 34),
        child: BasicAppButton(
          onPressed: () {
            context.push(RouteName.myOrderHomePage);
          },
          title: 'Back to order',
          sizeTitle: 18,
          height: 44,
          radius: 10,
          colorButton: Theme.of(context).informationBase(context),
          fontW: FontWeight.w600,
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(AppAssets.myOrderSuccess),
            Gap(12.h),
            Text('Service review success',
                style: Theme.of(context).textTheme.lightHeadingMedium),
            Gap(12.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Center(
                child: Text(
                  'You have completed a product assessment for the product you have chosen to use.',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodyLargeMedium
                      .copyWith(color: Theme.of(context).disabledBase(context)),
                  textAlign: TextAlign.center,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
