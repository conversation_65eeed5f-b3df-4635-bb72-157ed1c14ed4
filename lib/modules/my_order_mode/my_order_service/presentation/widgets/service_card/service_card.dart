import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';
import '../../../../../../core/constants/app_assets.dart';

class ServiceCard extends StatefulWidget {
  final OrderServiceModel sellDetail;
  const ServiceCard(
      {super.key, required this.sellDetail, required this.serviceHomePageBloc});
  final ServiceHomePageBloc serviceHomePageBloc;

  @override
  State<ServiceCard> createState() => _ServiceCardState();
}

class _ServiceCardState extends State<ServiceCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: PaddingConstants.padAll12,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: Theme.of(context).whitePrimary(context),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).whitePrimary(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).greyScale200(context),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.sellDetail.seller?.firstName ?? 'NameShop',
                        style: Theme.of(context).textTheme.lightBodyLargeBold,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Row(
                        children: [
                          SvgPicture.asset(
                            AppAssets.location_tick,
                          ),
                          Gap(5.w),
                          Text(
                            widget.sellDetail?.seller?.address ?? 'Address',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Order date',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Gap(5.h),
                    Text(
                      _formatTime(widget.sellDetail?.createdAt) ?? 'N/A',
                      // 'Apr 19, 2024. 7:55 AM',
                      style: Theme.of(context).textTheme.bodySmall,
                    )
                  ],
                )
              ],
            ),
          ),
          Gap(10.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hiển thị ảnh sản phẩm
              SizedBox(
                width: 60.w,
                height: 60.h,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CustomImage(
                    path: widget.sellDetail.service?.image?.first ??
                        'https://cdn.pixabay.com/photo/2016/10/25/12/28/iceland-1768744_1280.jpg',
                    width: 60.w,
                    height: 60.h,
                    imageType: ImageType.network,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Gap(10.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.sellDetail.service?.title ?? 'N/A',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  color: Theme.of(context).textBlack(context),
                                ),
                          ),
                        ),
                      ],
                    ),
                    Gap(5.h),
                    Row(
                      children: [
                        Text(
                            'To pay:US\$${widget.sellDetail.service!.price ?? 0}'),
                      ],
                    ),
                    widget.sellDetail.status == 'pending'
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Gap(5.h),
                              Row(
                                children: [
                                  Text('Awaiting payment',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumSemiBold
                                          .copyWith(
                                              color: Theme.of(context)
                                                  .alertInformationBase(
                                                      context))),
                                  Gap(12.w),
                                  Text('23:59:02',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumSemiBold
                                          .copyWith(
                                              color: Theme.of(context)
                                                  .alertInformationBase(
                                                      context))),
                                ],
                              ),
                              Gap(10.h),
                              ElevatedButton(
                                onPressed: () {
                                  context.push(RouteName.orderServiceDetailPage,
                                      extra: {
                                        'orderServiceModel': widget.sellDetail,
                                        'serviceHomePageBloc':
                                            widget.serviceHomePageBloc
                                      });
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(context)
                                      .alertInformationBase(context),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Go to payment',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : widget.sellDetail.status == 'accepted'
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('In Progress',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumSemiBold
                                          .copyWith(
                                            color: Theme.of(context)
                                                .alertAttentionBase(context),
                                          )),
                                  Gap(5.h),
                                  Row(
                                    children: [
                                      SvgPicture.asset(
                                          AppAssets.clipboardTickMessageSvg),
                                      Gap(5.w),
                                      Expanded(
                                        child: Text(
                                          'You have successfully booked the service. Please contact an expert to start using the service.',
                                          style: Theme.of(context)
                                              .textTheme
                                              .lightBodyXSmallMedium
                                              .copyWith(
                                                color: Theme.of(context)
                                                    .greyScale500(context),
                                              ),
                                          maxLines: 2,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Gap(10.h),
                                  SizedBox(
                                    width: 100.h,
                                    height: 40.h,
                                    child: StrongBodyButton(
                                      label: 'Dispute',
                                      onPressed: () {},
                                      textColor: Theme.of(context)
                                          .blackPrimary(context),
                                      backgroundColor: Theme.of(context)
                                          .whitePrimary(context),
                                      borderColor: Theme.of(context)
                                          .blackPrimary(context),
                                    ),
                                  ),
                                ],
                              )
                            : widget.sellDetail.status == 'delivered'
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Gap(5.h),
                                      Text('Service Provided',
                                          style: Theme.of(context)
                                              .textTheme
                                              .lightBodyMediumSemiBold
                                              .copyWith(
                                                color: Theme.of(context)
                                                    .alertAttentionBase(
                                                        context),
                                              )),
                                      Gap(5.h),
                                      Row(
                                        children: [
                                          SvgPicture.asset(AppAssets
                                              .clipboardTickMessageSvg),
                                          Gap(5.w),
                                          Expanded(
                                            child: Text(
                                              'You have successfully booked the service. Please contact an expert to start using the service.',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyXSmallMedium
                                                  .copyWith(
                                                    color: Theme.of(context)
                                                        .greyScale500(context),
                                                  ),
                                              maxLines: 2,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Gap(12.h),
                                      SizedBox(
                                        width: 200.w,
                                        height: 40.h,
                                        child: StrongBodyButton(
                                          label: 'Confirm Completion',
                                          onPressed: () {
                                            context
                                                .read<ServiceHomePageBloc>()
                                                .add(
                                                    UpdateOrderServiceBuyerStatus(
                                                  widget.sellDetail.id!,
                                                ));
                                          },
                                          textColor: Theme.of(context)
                                              .whitePrimary(context),
                                          backgroundColor: Theme.of(context)
                                              .alertAttentionBase(context),
                                          borderColor: Theme.of(context)
                                              .alertAttentionBase(context),
                                        ),
                                      ),
                                      Gap(12.h),
                                      SizedBox(
                                        width: 100.w,
                                        height: 40.h,
                                        child: StrongBodyButton(
                                          label: 'Dispute',
                                          onPressed: () {},
                                          textColor: Theme.of(context)
                                              .blackPrimary(context),
                                          backgroundColor: Theme.of(context)
                                              .whitePrimary(context),
                                          borderColor: Theme.of(context)
                                              .blackPrimary(context),
                                        ),
                                      ),
                                    ],
                                  )
                                : widget.sellDetail.status == 'completed'
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Gap(5.h),
                                          Text('Completed',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyMediumSemiBold
                                                  .copyWith(
                                                      color: Theme.of(context)
                                                          .successBase(
                                                              context))),
                                          Gap(10.h),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: StrongBodyButton(
                                                  label: 'Buy again',
                                                  onPressed: () {},
                                                  textColor: Theme.of(context)
                                                      .whitePrimary(context),
                                                  backgroundColor:
                                                      Theme.of(context)
                                                          .successBase(context),
                                                  borderColor: Theme.of(context)
                                                      .successBase(context),
                                                ),
                                              ),
                                              Gap(10.w),
                                              Expanded(
                                                child: StrongBodyButton(
                                                  label: 'Rating',
                                                  onPressed: () {
                                                    context.push(
                                                        RouteName
                                                            .serviceReviewPage,
                                                        extra: {
                                                          "orderServiceModel":
                                                              widget.sellDetail,
                                                          "serviceHomePageBloc":
                                                              widget
                                                                  .serviceHomePageBloc
                                                        });
                                                  },
                                                  textColor: Theme.of(context)
                                                      .blackPrimary(context),
                                                  backgroundColor: Theme.of(
                                                          context)
                                                      .whitePrimary(context),
                                                  borderColor: Theme.of(context)
                                                      .blackPrimary(context),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : widget.sellDetail.status == 'cancelled'
                                        ? Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Gap(5.h),
                                              Text('Cancelled',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .lightBodyMediumSemiBold
                                                      .copyWith(
                                                          color: Theme.of(
                                                                  context)
                                                              .greyScale500(
                                                                  context))),
                                              Gap(10.h),
                                              SizedBox(
                                                width: 124.w,
                                                child: StrongBodyButton(
                                                  label: 'Buy again',
                                                  onPressed: () {},
                                                  textColor: Theme.of(context)
                                                      .whitePrimary(context),
                                                  backgroundColor:
                                                      Theme.of(context)
                                                          .successBase(context),
                                                  borderColor: Theme.of(context)
                                                      .successBase(context),
                                                ),
                                              ),
                                              Gap(10.h),
                                              StrongBodyButton(
                                                label:
                                                    'View Cancellation Details',
                                                onPressed: () {},
                                                textColor: Theme.of(context)
                                                    .blackPrimary(context),
                                                backgroundColor:
                                                    Theme.of(context)
                                                        .whitePrimary(context),
                                                borderColor: Theme.of(context)
                                                    .blackPrimary(context),
                                              ),
                                              SizedBox(height: 12.h),
                                              StrongBodyButton(
                                                label: 'Refund return',
                                                onPressed: () {},
                                                textColor: Theme.of(context)
                                                    .blackPrimary(context),
                                                backgroundColor:
                                                    Theme.of(context)
                                                        .whitePrimary(context),
                                                borderColor: Theme.of(context)
                                                    .blackPrimary(context),
                                              ),
                                            ],
                                          )
                                        : const SizedBox(),
                  ],
                ),
              ),
              Gap(10.h),
            ],
          ),
        ],
      ),
    );
  }

  String? _formatTime(DateTime? d) {
    return d != null ? DateFormat("MMM d, y. h:mm a").format(d) : null;
  }
}
