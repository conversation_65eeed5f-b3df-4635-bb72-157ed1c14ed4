import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/l10n/locale_keys.g.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../../core/constants/app_assets.dart';
import '../../../../../../core/themes/app_colors.dart';

class ResolutionRegarding extends StatefulWidget {
  const ResolutionRegarding({super.key});

  @override
  State<ResolutionRegarding> createState() => _ResolutionRegardingState();
}

class _ResolutionRegardingState extends State<ResolutionRegarding> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(
                width: 1, color: Theme.of(context).lightGrey(context)),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  AppAssets.tickCircleGreen,
                  height: 52,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Text(
                      LocaleKeys.resolutionRegarding.tr(),
                      style: Theme.of(context).textTheme.lightHeadingSmall,
                    )),
                    Text(
                      '\$192',
                      style: Theme.of(context).textTheme.lightHeadingSmall,
                    ),
                  ],
                ),
                Gap(8.h),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Theme.of(context).greyScale200(context),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 16),
                    child: Row(
                      children: [
                        Icon(Icons.error_outline,
                            color: Theme.of(context).whitePrimary(context),
                            size: 26),
                        Gap(8.w),
                        Expanded(
                            child: Text(
                          LocaleKeys.thereWill.tr(),
                          style: Theme.of(context)
                              .textTheme
                              .lightBodySmallMedium
                              .copyWith(
                                  color:
                                      Theme.of(context).whitePrimary(context)),
                        )),
                      ],
                    ),
                  ),
                ),
                Gap(16.h),
                _infomationCardOrder(LocaleKeys.method.tr(), 'Visa Debit Card'),
                _infomationCardOrder(
                    LocaleKeys.transactionDate.tr(), '12/26/2024'),
                _infomationCardOrder('Transaction Code', '*************'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _infomationCardOrder(String textKey, textValue) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            textKey,
            style: Theme.of(context)
                .textTheme
                .lightBodyMediumSemiBold
                .copyWith(color: Theme.of(context).textSecondary(context)),
          ),
          Text(
            textValue,
            style: Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                color: textKey == 'Transaction Code'
                    ? Theme.of(context).whitePrimary(context)
                    : AppColors.textPrimary),
          ),
        ],
      ),
    );
  }
}
