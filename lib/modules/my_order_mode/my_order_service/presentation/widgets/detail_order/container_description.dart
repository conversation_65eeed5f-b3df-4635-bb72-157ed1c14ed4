import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/my_order_service_model.dart';


class ContainerDescription extends StatelessWidget {
  final MyOrderServiceModel sellDetail;
  const ContainerDescription({super.key, required this.sellDetail});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color:Theme.of(context).greyScale100(context),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
        child: Row(
          children: [
            Container(
              height: 52.h,
              width: 79.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                image: DecorationImage(
                  image: NetworkImage(sellDetail.imageDetail),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Gap(10.h),
            Expanded(
              child: Text(
                sellDetail.description,
                style: Theme.of(context).textTheme.lightBodyMediumSemiBold,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                softWrap: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
