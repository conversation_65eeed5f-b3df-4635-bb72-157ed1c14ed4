import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../../core/constants/app_assets.dart';
import '../../../../../../core/themes/app_colors.dart';

class ContactSeller extends StatefulWidget {
  const ContactSeller({super.key});

  @override
  State<ContactSeller> createState() => _ContactSellerState();
}

class _ContactSellerState extends State<ContactSeller> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Gap(9.h),
        _textTitle('Contact Seller', AppAssets.myOrderMessage),
        Gap(32.h),
        Row(
          children: [
             Icon(Icons.error_outline,
                color: Theme.of(context).secondary(context), size: 26),
            Gap(8.w), // K<PERSON>ảng cách giữa icon và text
            Expanded(
              // Giới hạn chiều rộng của RichText
              child: RichText(
                text: TextSpan(
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallRegular, // Style mặc định
                  children: [
                    const TextSpan(
                      text: 'It’s totally ',
                    ),
                    TextSpan(
                      text: 'safe ',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallBold
                          .copyWith(color: AppColors.successBase),
                    ),
                    const TextSpan(
                      text: 'to purchase at ',
                    ),
                    TextSpan(
                      text: 'Multi.Me ',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallBold
                          .copyWith(color: AppColors.successBase),
                    ),
                    const TextSpan(
                      text:
                      '. Your money will only be sent to the seller after your confirmation on receiving the product, else it will return to you.',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        Gap(40.h),
      ],
    );
  }
  Widget _textTitle(String text, String icon){
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SvgPicture.asset(icon),
            Gap(10.w),
            Text(text, style: Theme.of(context).textTheme.lightBodyLargeMedium,)
          ],
        ),
        SvgPicture.asset(AppAssets.arrowRightSvg),
      ],
    );
  }
}