import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';

import '../../../../../../core/l10n/locale_keys.g.dart';
import '../../../../../../core/themes/app_colors.dart';

class ContainerOrder extends StatelessWidget {
  final OrderServiceModel sellDetail;
  const ContainerOrder({super.key, required this.sellDetail});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 28),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            width: 258,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Theme.of(context).whitePrimary(context)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 14),
              child: Column(
                children: [
                  Text(
                    'I will be your diet manager to ensure proper nutrition for each meal.',
                    style: Theme.of(context).textTheme.lightBodySmallRegular,
                    maxLines: null,
                    overflow: TextOverflow.visible,
                    softWrap: true,
                  ),
                  Gap(10.h),
                  Divider(
                    height: 1,
                    thickness: 1,
                    color: Theme.of(context).lightGrey(context),
                  ),
                  Gap(10.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.price.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(
                              color: Theme.of(context).greyScale700(context),
                            ),
                      ),
                      Text(
                        '\$${sellDetail.price! * 1.0}',
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(
                              color: Theme.of(context).greyScale700(context),
                            ),
                      )
                    ],
                  ),
                  Gap(10.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.serviceFee.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(
                              color: Theme.of(context).greyScale700(context),
                            ),
                      ),
                      Text(
                        '\$${sellDetail.price! * 0.05}',
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(
                              color: Theme.of(context).greyScale700(context),
                            ),
                      )
                    ],
                  ),
                  Gap(10.h),
                  Divider(
                    height: 1,
                    thickness: 1,
                    color: Theme.of(context).lightGrey(context),
                  ),
                  Gap(16.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.totalCost.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(color: AppColors.secondaryBase),
                      ),
                      Text(
                        '\$${sellDetail.price! * 0.05 + sellDetail.price!}',
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(color: AppColors.secondaryBase),
                      )
                    ],
                  ),
                  Gap(10.h),
                  Divider(
                    height: 1,
                    thickness: 1,
                    color: Theme.of(context).lightGrey(context),
                  ),
                  Gap(10.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        LocaleKeys.deliveryDate.tr(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(
                              color: Theme.of(context).greyScale700(context),
                            ),
                      ),
                      Text(
                        getFormattedToday(),
                        style: Theme.of(context)
                            .textTheme
                            .lightBodyMediumSemiBold
                            .copyWith(
                              color: Theme.of(context).greyScale700(context),
                            ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String getFormattedToday() {
    DateTime now = DateTime.now();
    return '${now.day} ${_getMonthAbbreviation(now.month)} ${now.year}';
  }

  String _getMonthAbbreviation(int month) {
    const List<String> months = [
      '',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month];
  }
}
