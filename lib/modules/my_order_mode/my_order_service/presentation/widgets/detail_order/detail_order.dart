import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/widgets/container_status/container_status.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class DetailOrder extends StatefulWidget {
  final OrderServiceModel sellDetail;
  const DetailOrder({super.key, required this.sellDetail});

  @override
  State<DetailOrder> createState() => _DetailOrderState();
}

class _DetailOrderState extends State<DetailOrder> {
  late String dateTime;
  int _selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    dateTime = getCurrentTime();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Details',
                  style: Theme.of(context).textTheme.lightBodyLargeBold),
              ContainerStatus(status: widget.sellDetail.status ?? '')
            ],
          ),
          Gap(16.h),
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).greyScale100(context),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
              child: Row(
                children: [
                  Container(
                    height: 52.h,
                    width: 79.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: CustomImage(
                        path:
                            // widget.sellDetail.service!.coverImage! ??
                            'https://cdn.pixabay.com/photo/2016/10/25/12/28/iceland-1768744_1280.jpg',
                        fit: BoxFit.cover,
                        imageType: ImageType.network),
                  ),
                  Gap(10.h),
                  Expanded(
                    child: Text(
                      widget.sellDetail.description ?? '',
                      style:
                          Theme.of(context).textTheme.lightBodyMediumSemiBold,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Gap(16.h),
          Container(
            padding: EdgeInsets.only(bottom: AppSpacing.padding10h),
            decoration: BoxDecoration(
                border: Border(
              bottom: BorderSide(
                color: Theme.of(context).greyScale300(context),
                width: 1,
              ),
            )),
            child: Row(
              children: [
                ClipOval(
                  child: CustomImage(
                    height: 40.h,
                    width: 40.w,
                    path:
                        'https://cdn.pixabay.com/photo/2016/10/25/12/28/iceland-1768744_1280.jpg',
                    fit: BoxFit.cover,
                    imageType: ImageType.network,
                  ),
                ),
                Gap(10.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.sellDetail.customer!.displayName ?? 'user',
                      style: Theme.of(context).textTheme.lightBodyLargeSemiBold,
                    ),
                    Gap(4.h),
                    Text(
                      'Online',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallSemiBold
                          .copyWith(
                            color: Colors.green,
                          ),
                    ),
                  ],
                ),
                const Spacer(),
                IconButton(
                    onPressed: () {},
                    icon: SvgPicture.asset(AppAssets.messageSvg)),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _selectOption(String text, String image) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          text,
          style: Theme.of(context).textTheme.lightBodyLargeMedium,
        ),
        Gap(10.w),
        SvgPicture.asset(image)
      ],
    );
  }

  String getCurrentTime() {
    DateTime now = DateTime.now();
    return DateFormat('MM/dd/yy  hh:mm').format(now);
  }
}
