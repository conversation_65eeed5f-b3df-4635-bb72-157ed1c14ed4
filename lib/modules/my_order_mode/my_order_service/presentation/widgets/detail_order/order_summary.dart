import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../../core/l10n/locale_keys.g.dart';

class OrderSummary extends StatefulWidget {
  final double price;
  const OrderSummary({super.key, required this.price});

  @override
  State<OrderSummary> createState() => _OrderSummaryState();
}

class _OrderSummaryState extends State<OrderSummary> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Gap(16.h),
        Text(
          LocaleKeys.orderSummary.tr(),
          style: Theme.of(context).textTheme.lightBodyXLargeSemiBold,
        ),
        Gap(16.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.price.tr(),
              style:
                  Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                        color: Theme.of(context).greyScale700(context),
                      ),
            ),
            Text(
              '\$${widget.price * 1.0}',
              style:
                  Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                        color: Theme.of(context).greyScale700(context),
                      ),
            )
          ],
        ),
        Gap(16.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.serviceFee.tr(),
              style:
                  Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                        color: Theme.of(context).greyScale700(context),
                      ),
            ),
            Text(
              '\$${0.05 * widget.price}',
              style:
                  Theme.of(context).textTheme.lightBodyMediumSemiBold.copyWith(
                        color: Theme.of(context).greyScale700(context),
                      ),
            )
          ],
        ),
        Gap(8.h),
        Divider(
          height: 1,
          thickness: 1,
          color: Theme.of(context).disabledLight(context),
        ),
        Gap(16.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.totalCost.tr(),
              style: Theme.of(context).textTheme.lightBodyMediumSemiBold,
            ),
            Text(
              '\$${0.05 * widget.price + widget.price}',
              style: Theme.of(context).textTheme.lightBodyMediumSemiBold,
            )
          ],
        ),
      ],
    );
  }
}
