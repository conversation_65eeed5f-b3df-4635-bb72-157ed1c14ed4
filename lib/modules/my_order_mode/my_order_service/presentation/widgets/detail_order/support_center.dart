import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../../app/routers/routers_name.dart';
import '../../../../../../core/constants/app_assets.dart';
import '../../../../../../core/themes/app_colors.dart';

class SupportCenter extends StatefulWidget {
  const SupportCenter({super.key});

  @override
  State<SupportCenter> createState() => _SupportCenterState();
}

class _SupportCenterState extends State<SupportCenter> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
         Divider(
          color: Theme.of(context).greyScale100(context),
          thickness: 1, // Độ dày
          height: 20, // K<PERSON><PERSON>ng cách giữa các phần tử xung quanh
        ),
        <PERSON>(18.h),
        <PERSON>(
          children: [
            Text('Support Center',style: Theme.of(context).textTheme.lightBodyLargeBold)
          ],
        ),
        Gap(9.h),
         Divider(
          color:Theme.of(context).greyScale100(context),
          thickness: 1, // Độ dày
          height: 20, // Khoảng cách giữa các phần tử xung quanh
        ),
        Gap(9.h),
        _textTitle('Contact Seller', AppAssets.myOrderMessage),
        Gap(9.h),
         Divider(
          color: Theme.of(context).greyScale100(context),
          thickness: 1, // Độ dày
          height: 20, // Khoảng cách giữa các phần tử xung quanh
        ),
        Gap(9.h),
        GestureDetector(
            onTap: (){
              context.push(RouteName.helpCenter);
            },
            child: _textTitle('Help Center', AppAssets.myOrderHelp)),
        Gap(9.h),
         Divider(
          color: Theme.of(context).greyScale100(context),
          thickness: 1, // Độ dày
          height: 20, // Khoảng cách giữa các phần tử xung quanh
        ),
        Gap(32.h),
        Row(
          children: [
             Icon(Icons.error_outline,
                color: Theme.of(context).whitePrimary(context), size: 26),
            Gap(8.w), // Khoảng cách giữa icon và text
            Expanded(
              // Giới hạn chiều rộng của RichText
              child: RichText(
                text: TextSpan(
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallRegular, // Style mặc định
                  children: [
                    const TextSpan(
                      text: 'It’s totally ',
                    ),
                    TextSpan(
                      text: 'safe ',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallBold
                          .copyWith(color: AppColors.successBase),
                    ),
                    const TextSpan(
                      text: 'to purchase at ',
                    ),
                    TextSpan(
                      text: 'Multi.Me ',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallBold
                          .copyWith(color: AppColors.successBase),
                    ),
                    const TextSpan(
                      text:
                      '. Your money will only be sent to the seller after your confirmation on receiving the product, else it will return to you.',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _textTitle(String text, String icon){
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SvgPicture.asset(icon),
            Gap(10.w),
            Text(text, style: Theme.of(context).textTheme.lightBodyLargeMedium,)
          ],
        ),
        SvgPicture.asset(AppAssets.arrowRightSvg),
      ],
    );
  }
}
