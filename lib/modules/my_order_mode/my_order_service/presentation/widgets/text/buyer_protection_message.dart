import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../../../../../core/l10n/locale_keys.g.dart';
import '../../../../../../core/themes/app_colors.dart';

class BuyerProtectionMessage extends StatelessWidget {
  const BuyerProtectionMessage({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
         Icon(Icons.error_outline,
            color: Theme.of(context).whitePrimary(context), size: 26),
        Gap(8.w), // Khoảng cách giữa icon và text
        Expanded(
          // Giới hạn chiều rộng của RichText
          child: RichText(
            text: TextSpan(
              style: Theme.of(context)
                  .textTheme
                  .lightBodySmallRegular, // Style mặc định
              children: [
                TextSpan(
                  text: '${LocaleKeys.itsTotally.tr()} ',
                ),
                TextSpan(
                  text: '${LocaleKeys.safe.tr()} ',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallBold
                      .copyWith(color: AppColors.successBase),
                ),
                TextSpan(
                  text: '${LocaleKeys.toPurchaseAt.tr()} ',
                ),
                TextSpan(
                  text: 'Multi.Me ',
                  style: Theme.of(context)
                      .textTheme
                      .lightBodySmallBold
                      .copyWith(color: AppColors.successBase),
                ),
                TextSpan(
                  text:
                  '. ${LocaleKeys.yourMoneyWill.tr()}',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
