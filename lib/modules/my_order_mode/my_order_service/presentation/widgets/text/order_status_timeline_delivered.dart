import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import '../../../../../../core/constants/app_assets.dart';

class OrderStatusTimelineDelivered extends StatelessWidget {
  final String image;
  final String title;
  final String text;
  const OrderStatusTimelineDelivered(
      {super.key,
      required this.image,
      required this.title,
      required this.text});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: SvgPicture.asset(image),
          ),
          Gap(24.h),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.lightBodySmallRegular,
                ),
                Gap(8.h),
                Text(
                  text,
                  style: Theme.of(context).textTheme.lightBodyMediumSemiBold,
                  maxLines: null,
                  overflow: TextOverflow.visible,
                  softWrap: true,
                ),
                Gap(8.h),
                Row(
                  children: [
                    SvgPicture.asset(AppAssets.myOrderUpFile),
                    Gap(8.w),
                    Text(
                      'Sharing file iphone ui ... figma.pdf',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodySmallRegular
                          .copyWith(
                              color:
                                  Theme.of(context).informationBase(context)),
                    )
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
