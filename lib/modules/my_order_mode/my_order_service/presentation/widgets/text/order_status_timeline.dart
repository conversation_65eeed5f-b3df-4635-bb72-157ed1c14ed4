import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';

class OrderStatusTimeline extends StatelessWidget {
  final String image;
  final String title;
  final String text;
  const OrderStatusTimeline({super.key, required this.image, required this.title, required this.text});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(image),
          Gap(24.h),
          Expanded( // <PERSON><PERSON><PERSON> bảo Row không bị tràn
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.lightBodySmallRegular,
                ),
                Gap(8.h),
                Text(
                  text,
                  style: Theme.of(context).textTheme.lightBodyMediumSemiBold,
                  maxLines: null,
                  overflow: TextOverflow.visible,
                  softWrap: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
