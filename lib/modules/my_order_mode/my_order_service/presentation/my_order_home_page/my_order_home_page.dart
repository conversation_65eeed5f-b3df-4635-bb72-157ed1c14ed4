import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/repository/my_order_repository.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import '../../../my_order_product/presentation/product_home_page/product_home_page.dart';
import '../service_home_page/service_home_page.dart';

class MyOrderHomePage extends StatefulWidget {
  const MyOrderHomePage({super.key});

  @override
  State<MyOrderHomePage> createState() => _MyOrderHomePageState();
}

class _MyOrderHomePageState extends State<MyOrderHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;
  final ProductOrderBloc _productOrderBloc =
      ProductOrderBloc(getIt<MyOrderRepository>());
  final ServiceHomePageBloc _serviceHomePageBloc =
      ServiceHomePageBloc(myOrderRepository: getIt<MyOrderRepository>());
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _productOrderBloc),
        BlocProvider.value(value: _serviceHomePageBloc),
      ],
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          title: Text(
            'My Order',
            style: Theme.of(context).textTheme.lightHeadingMedium,
          ),
        ),
        body: Column(
          children: [
            // TabBar bằng Container
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                height: 40.h,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).greyScale100(context),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    _buildTab(0, 'Service'),
                    _buildTab(1, 'Product'),
                  ],
                ),
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  ServiceHomePage(
                    serviceHomePageBloc: _serviceHomePageBloc,
                  ),
                  ProductHomePage(productOrderBloc: _productOrderBloc),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Widget Tab tự custom
  Widget _buildTab(int index, String title) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          _tabController.animateTo(index);
        },
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(vertical: 4.h),
          decoration: BoxDecoration(
            color: _selectedIndex == index
                ? Theme.of(context).secondaryBase(context)
                : Theme.of(context).greyScale100(context),
            borderRadius: BorderRadius.circular(10.r), // Bo góc
          ),
          child: Text(
            title,
            style: Theme.of(context).textTheme.lightBodyXLargeBold.copyWith(
                  color: _selectedIndex == index
                      ? Colors.white
                      : Theme.of(context).greyScale600(context),
                ),
          ),
        ),
      ),
    );
  }
}
