part of 'service_home_page_bloc.dart';

class ServiceHomePageState extends Equatable {
  final bool isLoading;
  final bool isSuccess;
  final String? errorMessage;
  final bool isError;
  final List<OrderServiceModel>? orderServiceModel;
  final int selectedIndexStatus;
  final String orderServiceStatus;
  final int currentPage;
  final bool hasMore;

  const ServiceHomePageState({
    this.isLoading = false,
    this.isSuccess = false,
    this.errorMessage,
    this.isError = false,
    this.orderServiceModel,
    this.selectedIndexStatus = 0,
    this.orderServiceStatus = 'all',
    this.currentPage = 1,
    this.hasMore = true,
  });

  factory ServiceHomePageState.initial() {
    return const ServiceHomePageState(
      errorMessage: null,
      isLoading: false,
      isSuccess: false,
      isError: false,
      orderServiceModel: [],
      selectedIndexStatus: 0,
      orderServiceStatus: 'all',
      currentPage: 1,
      hasMore: true,
    );
  }

  ServiceHomePageState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? errorMessage,
    bool? isError,
    List<OrderServiceModel>? orderServiceModel,
    int? selectedIndexStatus,
    String? orderServiceStatus,
    int? currentPage,
    bool? hasMore,
  }) {
    return ServiceHomePageState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      errorMessage: errorMessage ?? this.errorMessage,
      isError: isError ?? this.isError,
      orderServiceModel: orderServiceModel ?? this.orderServiceModel,
      selectedIndexStatus: selectedIndexStatus ?? this.selectedIndexStatus,
      orderServiceStatus: orderServiceStatus ?? this.orderServiceStatus,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isSuccess,
        errorMessage,
        isError,
        orderServiceModel,
        selectedIndexStatus,
        orderServiceStatus,
        currentPage,
        hasMore,
      ];
}
