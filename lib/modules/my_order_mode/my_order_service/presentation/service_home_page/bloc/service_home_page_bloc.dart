import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import '../../../../data/repository/my_order_repository.dart';
part 'service_home_page_event.dart';
part 'service_home_page_state.dart';

class ServiceHomePageBloc
    extends Bloc<ServiceHomePageEvent, ServiceHomePageState> {
  final MyOrderRepository myOrderRepository;
  ServiceHomePageBloc({required this.myOrderRepository})
      : super(ServiceHomePageState.initial()) {
    on<FetchOrderServiceEvent>(_onFetchOrderService);
    on<SelectOrderServiceStatus>((event, emit) {
      emit(state.copyWith(selectedIndexStatus: event.index));
    });
    on<UpdateOrderServiceBuyerStatus>(_onUpdateOrderServiceBuyerStatus);
  }

  Future<void> _onFetchOrderService(
      FetchOrderServiceEvent event, Emitter<ServiceHomePageState> emit) async {
    if (event.loadMore) {
      emit(state.copyWith(isLoading: true));
    } else {
      emit(state.copyWith(isLoading: true, isSuccess: false, isError: false));
    }

    final targetPage = event.loadMore ? state.currentPage + 1 : 1;

    try {
      final token = gs.accessToken;
      final response = await myOrderRepository.getOrderServiceList(
        {
          'page': targetPage,
          'limit': 10,
          if (event.status != 'all') 'status': event.status,
          '_t': DateTime.now().millisecondsSinceEpoch,
        },
        token!,
      );

      final newOrders = response.data!.orderServiceData.orderServiceModel;

      // Combine data appropriately
      final allOrders = event.loadMore
          ? [
              ...(state.orderServiceModel ?? <OrderServiceModel>[]),
              ...newOrders
            ]
          : newOrders;

      final hasMore = newOrders.length == 10;

      emit(state.copyWith(
        orderServiceModel: allOrders,
        orderServiceStatus: event.status,
        currentPage: targetPage,
        isLoading: false,
        isSuccess: true,
        isError: false,
        hasMore: hasMore,
      ));
    } on NetworkException catch (e) {
      emit(state.copyWith(
          isLoading: false,
          isSuccess: false,
          isError: true,
          errorMessage: e.message));
    } catch (e) {
      emit(state.copyWith(
          isLoading: false,
          isSuccess: false,
          isError: true,
          errorMessage: e.toString()));
    }
  }

  Future<void> _onUpdateOrderServiceBuyerStatus(
      UpdateOrderServiceBuyerStatus event,
      Emitter<ServiceHomePageState> emit) async {
    try {
      final token = gs.accessToken;

      await myOrderRepository.updateOrderServiceBuyerStatus(
        event.offerId,
        token!,
      );

      // Only refresh the data without showing full screen loading
      add(FetchOrderServiceEvent(
          status: state.orderServiceStatus, loadMore: false));
      return;
    } on NetworkException catch (e) {
      emit(state.copyWith(
          isLoading: false,
          isSuccess: false,
          isError: true,
          errorMessage: e.message));
    } catch (e) {
      emit(state.copyWith(
          isLoading: false,
          isSuccess: false,
          isError: true,
          errorMessage: e.toString()));
      print('Error updating service order status: $e');
    }
  }
}
