part of 'service_home_page_bloc.dart';

class ServiceHomePageEvent extends Equatable {
  ServiceHomePageEvent();

  @override
  List<Object?> get props => [];
}

class FetchOrderServiceEvent extends ServiceHomePageEvent {
  final String status;
  final bool loadMore;

  FetchOrderServiceEvent({
    required this.status,
    required this.loadMore,
  });

  @override
  List<Object?> get props => [status, loadMore];
}

class SelectOrderServiceStatus extends ServiceHomePageEvent {
  final int index;

  SelectOrderServiceStatus(this.index);

  @override
  List<Object?> get props => [index];
}

class UpdateOrderServiceBuyerStatus extends ServiceHomePageEvent {
  final int offerId;

  UpdateOrderServiceBuyerStatus(this.offerId);

  @override
  List<Object?> get props => [offerId];
}
