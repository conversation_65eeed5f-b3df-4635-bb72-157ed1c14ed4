import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/tab_bar/list_order_service_status.dart';

class ServiceHomePage extends StatefulWidget {
  const ServiceHomePage({super.key, required this.serviceHomePageBloc});
  final ServiceHomePageBloc serviceHomePageBloc;

  @override
  State<ServiceHomePage> createState() => _ServiceHomePageState();
}

class _ServiceHomePageState extends State<ServiceHomePage> {
  List<String> status = [
    'All status',
    'Awaiting payment',
    'In Progress',
    'Service Provided',
    'Completed',
    'Cancelled'
  ];

  final List<Map<String, String>> valueStatus = [
    {'orderStatus': 'all'},
    {'orderStatus': 'pending'},
    {'orderStatus': 'accepted'},
    {'orderStatus': 'delivered'},
    {'orderStatus': 'completed'},
    {'orderStatus': 'cancelled'},
  ];
  @override
  void initState() {
    super.initState();
    final orderStatus = valueStatus[0]['orderStatus'] ?? '';
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ServiceHomePageBloc>().add(
            SelectOrderServiceStatus(0),
          );
      context.read<ServiceHomePageBloc>().add(
            FetchOrderServiceEvent(status: orderStatus, loadMore: false),
          );
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ServiceHomePageBloc, ServiceHomePageState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(10.h),
            Padding(
              padding: EdgeInsets.only(
                  bottom: AppSpacing.padding10h,
                  left: AppSpacing.padding16,
                  right: AppSpacing.padding16),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.padding16,
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                      color: Theme.of(context).blackPrimary(context), width: 1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    focusColor: Theme.of(context).whitePrimary(context),
                    dropdownColor: Theme.of(context).whitePrimary(context),
                    value: state.selectedIndexStatus,
                    style: Theme.of(context).textTheme.lightBodyLargeRegular,
                    icon: SvgPicture.asset(AppAssets.arrowDownSvg),
                    isExpanded: true,
                    items: List.generate(status.length, (index) {
                      return DropdownMenuItem<int>(
                        value: index,
                        child: Text(status[index]),
                      );
                    }),
                    onChanged: (int? index) {
                      if (index == null) return;

                      context.read<ServiceHomePageBloc>().add(
                            SelectOrderServiceStatus(index),
                          );
                      final orderStatus =
                          valueStatus[index]['orderStatus'] ?? '';

                      // Reset to first page when changing status
                      context.read<ServiceHomePageBloc>().add(
                            FetchOrderServiceEvent(
                                status: orderStatus, loadMore: false),
                          );
                    },
                  ),
                ),
              ),
            ),
            Expanded(
              child: ListOrderServiceStatus(
                status: state.orderServiceStatus,
                serviceHomePageBloc: widget.serviceHomePageBloc,
              ),
            ),
          ],
        );
      },
    );
  }
}
