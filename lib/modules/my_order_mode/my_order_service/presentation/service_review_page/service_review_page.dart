import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class ServiceReviewPage extends StatefulWidget {
  const ServiceReviewPage(
      {super.key,
      required this.orderServiceModel,
      required this.serviceHomePageBloc});
  final OrderServiceModel orderServiceModel;
  final ServiceHomePageBloc serviceHomePageBloc;

  @override
  State<ServiceReviewPage> createState() => _ServiceReviewPageState();
}

class _ServiceReviewPageState extends State<ServiceReviewPage> {
  final _formKey = GlobalKey<FormState>();
  final _textController = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print("image: ${widget.orderServiceModel.service!.image![0]}");
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            context.pop();
          },
          icon: SvgPicture.asset(
            AppAssets.arrowLeftSvg,
          ),
        ),
        automaticallyImplyLeading: false,
        centerTitle: true,
        title: Text('Service Review',
            style: Theme.of(context).textTheme.lightHeadingMedium),
      ),
      body: GestureDetector(
        onTap: () {
          // Ẩn bàn phím khi tap vào vùng trống
          FocusScope.of(context).unfocus();
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CustomImage(
                        path: widget.orderServiceModel.service!.image![0],
                        imageType: ImageType.network,
                        fit: BoxFit.cover,
                        width: 30.w,
                        height: 70.h,
                      ),
                    ),
                  ),
                  Gap(15.w),
                  Expanded(
                    child: Text(
                      widget.orderServiceModel.service!.title!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      style: Theme.of(context).textTheme.lightBodyXLargeMedium,
                    ),
                  ),
                ],
              ),
              Gap(10.h),
              Row(
                children: [
                  Text(
                    'Service quality',
                    style: Theme.of(context).textTheme.lightBodyLargeMedium,
                  ),
                  Gap(10.h),
                  RatingBar.builder(
                    itemSize: 30,
                    initialRating: 3,
                    minRating: 1,
                    direction: Axis.horizontal,
                    allowHalfRating: true,
                    itemCount: 5,
                    itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
                    itemBuilder: (context, _) => Icon(
                      Icons.star,
                      color: Colors.amber,
                    ),
                    onRatingUpdate: (rating) {
                      print(rating);
                    },
                  ),
                ],
              ),
              Gap(10.h),
              Form(
                key: _formKey,
                child: TextFormField(
                  controller: _textController,
                  focusNode: _focusNode,
                  maxLines: 5,
                  decoration: InputDecoration(
                    hintText: 'Reviews',
                    hintStyle: Theme.of(context)
                        .textTheme
                        .lightBodyLargeRegular
                        .copyWith(
                          color: Theme.of(context).greyScale400(context),
                        ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Theme.of(context).greyScale600(context),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                  },
                  child: Container(
                    width: double.infinity,
                    color: Colors.transparent,
                  ),
                ),
              ),
              Row(
                children: [
                  Expanded(
                      child: StrongBodyButton(
                          textColor: Theme.of(context).blackPrimary(context),
                          backgroundColor:
                              Theme.of(context).whitePrimary(context),
                          borderColor: Theme.of(context).secondary(context),
                          label: 'Cancel',
                          onPressed: () {
                            context.pop();
                          })),
                  Gap(10.w),
                  Expanded(
                      child: StrongBodyButton(
                          textColor: Theme.of(context).whitePrimary(context),
                          backgroundColor: Theme.of(context).secondary(context),
                          label: 'Confirm',
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {}
                          })),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
