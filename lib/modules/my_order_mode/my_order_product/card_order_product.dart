import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/padding_constant.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_event.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_order_detail_page/widgets/show_dialog_detial_product_order.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardOrderProduct extends StatefulWidget {
  const CardOrderProduct({
    super.key,
    required this.cardOrderModel,
    this.productOrderBloc,
  });

  final OrderProductModel? cardOrderModel;
  final ProductOrderBloc? productOrderBloc;

  @override
  State<CardOrderProduct> createState() => _CardOrderProductState();
}

class _CardOrderProductState extends State<CardOrderProduct> {
  bool isExpanded = false;
  static const int initialSeconds = 24 * 60 * 60;
  int _secondsLeft = initialSeconds;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  void startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_secondsLeft > 0) {
        setState(() {
          _secondsLeft--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  String formatDuration(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int secs = seconds % 60;
    return '${hours.toString().padLeft(2, '0')}:'
        '${minutes.toString().padLeft(2, '0')}:'
        '${secs.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final parseImage = TFormatter.parseStringList(
        widget.cardOrderModel!.productSnapshot!.snapshotImages!);
    return Container(
      padding: PaddingConstants.padAll12,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: Theme.of(context).whitePrimary(context),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme.of(context).whitePrimary(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).greyScale200(context),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.cardOrderModel?.shop?.shopName ?? 'NameShop',
                        style: Theme.of(context).textTheme.lightBodyLargeBold,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Row(
                        children: [
                          SvgPicture.asset(
                            AppAssets.location_tick,
                          ),
                          Gap(5.w),
                          Text(
                            widget.cardOrderModel?.shop?.addresses?[0].city ??
                                'Address',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Order date',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Gap(5.h),
                    Text(
                      _formatTime(widget.cardOrderModel?.createdAt) ?? 'N/A',
                      // 'Apr 19, 2024. 7:55 AM',
                      style: Theme.of(context).textTheme.bodySmall,
                    )
                  ],
                )
              ],
            ),
          ),
          Gap(10.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hiển thị ảnh sản phẩm
              SizedBox(
                width: 60.w,
                height: 60.h,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CustomImage(
                    path: parseImage[0] ??
                        'https://cdn.pixabay.com/photo/2016/10/25/12/28/iceland-1768744_1280.jpg',
                    width: 60.w,
                    height: 60.h,
                    imageType: ImageType.network,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Gap(10.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.cardOrderModel?.productSnapshot
                                    ?.snapshotName ??
                                'N/A',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  color: Theme.of(context).textBlack(context),
                                ),
                          ),
                        ),
                        Text(
                          'x${widget.cardOrderModel?.quantity ?? 'N/A'} ${widget.cardOrderModel?.productSnapshot?.snapshotUnit ?? ''}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                    Gap(5.h),
                    Row(
                      children: [
                        Text(
                            'To pay:US\$${widget.cardOrderModel?.total ?? 0.0}'),
                      ],
                    ),
                    widget.cardOrderModel?.orderStatus == 'pending' &&
                            widget.cardOrderModel?.shippingStatus == 'pending'
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Gap(5.h),
                              Row(
                                children: [
                                  Text('Awaiting payment',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumSemiBold
                                          .copyWith(
                                              color: Theme.of(context)
                                                  .alertInformationBase(
                                                      context))),
                                  Gap(12.w),
                                  Text(
                                      _secondsLeft > 0
                                          ? formatDuration(_secondsLeft)
                                          : 'Hết giờ!',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumSemiBold
                                          .copyWith(
                                              color: Theme.of(context)
                                                  .alertInformationBase(
                                                      context))),
                                ],
                              ),
                              Gap(10.h),
                              ElevatedButton(
                                onPressed: () {},
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(context)
                                      .alertInformationBase(context),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Go to payment',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : widget.cardOrderModel?.orderStatus == 'confirmed' &&
                                widget.cardOrderModel?.shippingStatus ==
                                    'pending'
                            ? Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text('Preparing Order',
                                      style: Theme.of(context)
                                          .textTheme
                                          .lightBodyMediumSemiBold
                                          .copyWith(
                                            color: Theme.of(context)
                                                .alertAttentionBase(context),
                                          )),
                                ],
                              )
                            : widget.cardOrderModel?.orderStatus ==
                                        'confirmed' &&
                                    widget.cardOrderModel?.shippingStatus ==
                                        'shipped'
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Gap(5.h),
                                      Text('In Transit',
                                          style: Theme.of(context)
                                              .textTheme
                                              .lightBodyMediumSemiBold
                                              .copyWith(
                                                color: Theme.of(context)
                                                    .alertAttentionBase(
                                                        context),
                                              )),
                                      Gap(5.h),
                                      Row(
                                        children: [
                                          SvgPicture.asset(AppAssets
                                              .clipboardTickMessageSvg),
                                          Gap(5.w),
                                          Expanded(
                                            child: Text(
                                              'Your order is being delivered. Make sure your phone is available to receive the delivery.',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyXSmallMedium
                                                  .copyWith(
                                                    color: Theme.of(context)
                                                        .greyScale500(context),
                                                  ),
                                              maxLines: 2,
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  )
                                : widget.cardOrderModel?.orderStatus ==
                                            'confirmed' &&
                                        widget.cardOrderModel?.shippingStatus ==
                                            'delivered'
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Gap(5.h),
                                          Text('Delivered',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyMediumSemiBold
                                                  .copyWith(
                                                      color: Theme.of(context)
                                                          .alertAttentionBase(
                                                              context))),
                                          Gap(10.h),
                                          StrongBodyButton(
                                            label: 'Mark as Received',
                                            onPressed: () {
                                              showConfirmReceivedDialog(
                                                context,
                                                onConfirm: () {
                                                  context
                                                      .read<ProductOrderBloc>()
                                                    ..add(
                                                        UpdateOrderProductStatus(
                                                            widget
                                                                .cardOrderModel!
                                                                .id!,
                                                            'completed',
                                                            'delivered'));
                                                },
                                              );
                                            },
                                            textColor: Theme.of(context)
                                                .whitePrimary(context),
                                            backgroundColor: Theme.of(context)
                                                .alertAttentionBase(context),
                                            borderColor: Theme.of(context)
                                                .alertAttentionBase(context),
                                          ),
                                        ],
                                      )
                                    : widget.cardOrderModel?.orderStatus ==
                                                'completed' &&
                                            widget.cardOrderModel
                                                    ?.shippingStatus ==
                                                'delivered'
                                        ? Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Gap(5.h),
                                              Text('Completed',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .lightBodyMediumSemiBold
                                                      .copyWith(
                                                          color: Theme.of(
                                                                  context)
                                                              .successBase(
                                                                  context))),
                                              Gap(10.h),
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: StrongBodyButton(
                                                      label: 'Rating',
                                                      onPressed: () {},
                                                      textColor:
                                                          Theme.of(context)
                                                              .whitePrimary(
                                                                  context),
                                                      backgroundColor: Theme.of(
                                                              context)
                                                          .successBase(context),
                                                      borderColor: Theme.of(
                                                              context)
                                                          .successBase(context),
                                                    ),
                                                  ),
                                                  Gap(10.w),
                                                  Expanded(
                                                    child: StrongBodyButton(
                                                      label: 'Buy again',
                                                      onPressed: () {},
                                                      textColor:
                                                          Theme.of(context)
                                                              .blackPrimary(
                                                                  context),
                                                      backgroundColor:
                                                          Theme.of(context)
                                                              .whitePrimary(
                                                                  context),
                                                      borderColor:
                                                          Theme.of(context)
                                                              .blackPrimary(
                                                                  context),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: 12.h),
                                              StrongBodyButton(
                                                label: 'Refund return',
                                                onPressed: () {},
                                                textColor: Theme.of(context)
                                                    .blackPrimary(context),
                                                backgroundColor:
                                                    Theme.of(context)
                                                        .whitePrimary(context),
                                                borderColor: Theme.of(context)
                                                    .blackPrimary(context),
                                              ),
                                            ],
                                          )
                                        : widget.cardOrderModel?.orderStatus ==
                                                    'cancelled' &&
                                                widget.cardOrderModel
                                                        ?.shippingStatus ==
                                                    'failed'
                                            ? Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Gap(5.h),
                                                  Text('Cancelled',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .lightBodyMediumSemiBold
                                                          .copyWith(
                                                              color: Theme.of(
                                                                      context)
                                                                  .greyScale500(
                                                                      context))),
                                                  Gap(10.h),
                                                  SizedBox(
                                                    width: 120.w,
                                                    child: StrongBodyButton(
                                                      label: 'Buy again',
                                                      onPressed: () {},
                                                      textColor:
                                                          Theme.of(context)
                                                              .blackPrimary(
                                                                  context),
                                                      backgroundColor:
                                                          Theme.of(context)
                                                              .whitePrimary(
                                                                  context),
                                                      borderColor:
                                                          Theme.of(context)
                                                              .greyScale500(
                                                                  context),
                                                    ),
                                                  ),
                                                ],
                                              )
                                            : const SizedBox(),
                  ],
                ),
              ),
              Gap(10.h),
            ],
          ),
        ],
      ),
    );
  }

  String? _formatTime(DateTime? d) {
    return d != null ? DateFormat("MMM d, y. h:mm a").format(d) : null;
  }
}
