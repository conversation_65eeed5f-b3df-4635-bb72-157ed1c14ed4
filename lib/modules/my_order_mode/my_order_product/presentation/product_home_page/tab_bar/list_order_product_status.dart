import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_event.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_state.dart';

import '../../../../../../core/constants/padding_constant.dart';
import '../../../../../../shared/widgets/app_loader/loading_strongbody.dart';
import '../../../card_order_product.dart';

class ListOrderProductStatus extends StatefulWidget {
  const ListOrderProductStatus(
      {super.key,
      this.orderStatus,
      this.shippingStatus,
      this.productOrderBloc});
  final String? orderStatus;
  final String? shippingStatus;
  final ProductOrderBloc? productOrderBloc;
  @override
  State<ListOrderProductStatus> createState() => _ListOrderProductStatusState();
}

class _ListOrderProductStatusState extends State<ListOrderProductStatus> {
  final ScrollController _scrollController = ScrollController();
  // bool _isInitialLoaded = false;

  @override
  void initState() {
    super.initState();
    if (widget.orderStatus == 'all' && widget.shippingStatus != 'all') {
      context.read<ProductOrderBloc>().add(
            FetchOrdersByStatus(
                widget.orderStatus!, widget.shippingStatus!, false),
          );
    }

    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        final state = context.read<ProductOrderBloc>().state;
        if (state.hasMore && !state.isLoading) {
          context.read<ProductOrderBloc>().add(
                FetchOrdersByStatus(
                    widget.orderStatus!, widget.shippingStatus!, true),
              );
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).greyScale50(context),
      child: BlocBuilder<ProductOrderBloc, ProductOrderState>(
        builder: (context, state) {
          final list = state.orderProductModel ?? [];
          if (state.isLoading && list.isEmpty) {
            // initial loading
            return const LoadingStrongBody();
          } else if (state.isSuccess && list.isNotEmpty) {
            return Padding(
              padding: PaddingConstants.padSymHV14,
              child: ListView.separated(
                controller: _scrollController,
                itemBuilder: (context, index) {
                  if (index >= list.length) {
                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  }
                  return GestureDetector(
                    onTap: () {
                      context.push(RouteName.productOrderDetailPage, extra: {
                        "productOrderBloc": widget.productOrderBloc,
                        "cardOrderModel": list[index],
                      });
                    },
                    child: CardOrderProduct(
                      cardOrderModel: list[index],
                    ),
                  );
                },
                separatorBuilder: (context, index) => Gap(10.h),
                itemCount: list.length + (state.isLoading ? 1 : 0),
              ),
            );
          } else if (list.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    AppAssets.lionStrongbodyAi,
                    width: MediaQuery.of(context).size.width * 0.12,
                    height: MediaQuery.of(context).size.height * 0.3,
                  ),
                  Text('No orders found for this status.'),
                ],
              ),
            );
          } else if (state.isError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    AppAssets.lionStrongbodyAi,
                    width: MediaQuery.of(context).size.width * 0.12,
                    height: MediaQuery.of(context).size.height * 0.3,
                  ),
                  Text('No orders found for this status.'),
                ],
              ),
            );
          }
          return SizedBox.shrink();
        },
      ),
    );
  }
}
