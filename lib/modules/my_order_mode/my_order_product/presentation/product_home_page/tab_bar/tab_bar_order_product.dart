import 'package:flutter/material.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';

class TabBarOrderProduct extends StatelessWidget {
  const TabBarOrderProduct({super.key, required this.controller});

  final TabController controller;

  @override
  Widget build(BuildContext context) {
    return TabBar(
        controller: controller,
        isScrollable: true,
        indicatorColor: Theme.of(context).errorBase(context),
        tabAlignment: TabAlignment.start,
        dividerColor: Colors.transparent,
        unselectedLabelStyle: Theme.of(context).textTheme.labelMedium,
        labelStyle: Theme.of(context).textTheme.lightBodyLargeRegular.copyWith(
              color: Theme.of(context).primary(context),
            ),
        tabs: const [
          Tab(
            text: 'All',
          ),
          Tab(
            text: 'To Pay',
          ),
          Tab(
            text: 'To Ship',
          ),
          Tab(
            text: 'Completed',
          ),
          Tab(
            text: 'Cancelled',
          ),
          Tab(
            text: 'Return/refund',
          ),
        ]);
  }
}
