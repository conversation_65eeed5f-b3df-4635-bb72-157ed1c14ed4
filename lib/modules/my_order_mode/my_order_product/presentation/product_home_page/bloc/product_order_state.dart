import 'package:equatable/equatable.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';

class ProductOrderState extends Equatable {
  final bool isLoading;
  final bool isSuccess;
  final String? errorMessage;
  final bool isError;
  final int currentPage;

  final List<OrderProductModel>? orderProductModel;
  final int? selectedIndexStatus;
  final String orderState;
  final String shippingState;
  final bool hasMore;

  const ProductOrderState({
    this.isLoading = false,
    this.isSuccess = false,
    this.errorMessage,
    this.isError = false,
    this.orderProductModel,
    this.selectedIndexStatus,
    this.orderState = '',
    this.shippingState = '',
    this.currentPage = 1,
    this.hasMore = false,
  });

  /// Initial/default state
  factory ProductOrderState.initial() {
    return const ProductOrderState(
      errorMessage: null,
      isLoading: false,
      isSuccess: false,
      isError: false,
      orderProductModel: [],
      selectedIndexStatus: 0,
      orderState: '',
      shippingState: '',
      hasMore: false,
    );
  }

  /// Create a copy of the state with updated fields
  ProductOrderState copyWith({
    List<OrderProductModel>? orderProductModel,
    List<OrderServiceModel>? orderServiceModel,
    bool? isLoading,
    bool? isSuccess,
    String? errorMessage,
    bool? isError,
    int? selectedIndexStatus,
    String? orderState,
    String? shippingState,
    int? currentPage,
    bool? hasMore,
  }) {
    return ProductOrderState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      errorMessage: errorMessage ?? this.errorMessage,
      isError: isError ?? this.isError,
      orderProductModel: orderProductModel ?? this.orderProductModel,
      selectedIndexStatus: selectedIndexStatus ?? this.selectedIndexStatus,
      orderState: orderState ?? this.orderState,
      shippingState: shippingState ?? this.shippingState,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isSuccess,
        errorMessage,
        isError,
        orderProductModel,
        selectedIndexStatus,
        orderState,
        shippingState,
        currentPage,
        hasMore,
      ];
}
