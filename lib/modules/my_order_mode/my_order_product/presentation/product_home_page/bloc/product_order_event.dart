import 'package:equatable/equatable.dart';

sealed class ProductOrderEvent extends Equatable {
  const ProductOrderEvent();

  @override
  List<Object?> get props => [];
}

class FetchOrdersByStatus extends ProductOrderEvent {
  final String orderStatus;
  final String shippingStatus;
  final bool isLoadMore;

  const FetchOrdersByStatus(
      this.orderStatus, this.shippingStatus, this.isLoadMore);

  @override
  List<Object?> get props => [orderStatus, shippingStatus, isLoadMore];
}

class SelectOrderStatus extends ProductOrderEvent {
  final int index;

  const SelectOrderStatus(this.index);

  @override
  List<Object?> get props => [index];
}

class UpdateOrderProductStatus extends ProductOrderEvent {
  final int orderId;
  final String orderStatus;
  final String shippingStatus;

  const UpdateOrderProductStatus(
      this.orderId, this.orderStatus, this.shippingStatus);

  @override
  List<Object?> get props => [orderId, orderStatus, shippingStatus];
}
