import 'package:bloc/bloc.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/network/exception.dart';
import 'package:multime_app/modules/my_order_mode/data/repository/my_order_repository.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_event.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_state.dart';

class ProductOrderBloc extends Bloc<ProductOrderEvent, ProductOrderState> {
  MyOrderRepository myOrderRepository;
  ProductOrderBloc(this.myOrderRepository)
      : super(ProductOrderState.initial()) {
    on<FetchOrdersByStatus>(_onFetchOrdersByStatus);
    on<SelectOrderStatus>((event, emit) {
      emit(state.copyWith(selectedIndexStatus: event.index));
    });

    on<UpdateOrderProductStatus>(_onUpdateOrderProductStatus);
  }

  Future<void> _onUpdateOrderProductStatus(
      UpdateOrderProductStatus event, Emitter<ProductOrderState> emit) async {
    emit(state.copyWith(isLoading: true, isSuccess: false, isError: false));
    try {
      final token = gs.accessToken;
      final response = await myOrderRepository.updateOrderProductStatus(
        event.orderId,
        event.orderStatus,
        event.shippingStatus,
        token!,
      );
      add(FetchOrdersByStatus('all', 'all', false));
    } on NetworkException catch (e) {
      emit(state.copyWith());
    } catch (e) {
      print('Error in order bloc $e');
    }
  }

  Future<void> _onFetchOrdersByStatus(
      FetchOrdersByStatus event, Emitter<ProductOrderState> emit) async {
    emit(state.copyWith(isLoading: true, isSuccess: false, isError: false));
    try {
      int requestPage = state.currentPage;
      if (event.isLoadMore) {
        requestPage = state.currentPage + 1;
      } else {
        requestPage = 1;
      }

      final token = gs.accessToken;

      final response = await myOrderRepository.getOrderList(
        {
          'page': requestPage,
          'limit': 10,
          'customer_id': gs.user!.id,
          if (event.orderStatus != 'all') 'order_status': event.orderStatus,
          if (event.shippingStatus != 'all')
            'shipping_status': event.shippingStatus,
          '_t': DateTime.now().millisecondsSinceEpoch,
        },
        token!,
      );

      final orders = response.data!.orderProductData.orderProductModel;

      final newList = event.isLoadMore
          ? [...state.orderProductModel!, ...orders]
          : [...orders];
      final hasMore = orders.length == 10;
      print('order product list: $newList');
      emit(state.copyWith(
        orderProductModel: newList,
        orderState: event.orderStatus,
        shippingState: event.shippingStatus,
        isLoading: false,
        isSuccess: true,
        isError: false,
        currentPage: requestPage,
        hasMore: hasMore,
      ));
      return;
    } on NetworkException catch (_) {
      emit(state.copyWith());
    } catch (e) {
      print('Error in order bloc $e');
    }
  }
}
