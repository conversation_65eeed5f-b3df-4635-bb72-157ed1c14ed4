import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_event.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_state.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/tab_bar/list_order_product_status.dart';

import 'bloc/product_order_bloc.dart';

class ProductHomePage extends StatefulWidget {
  const ProductHomePage({super.key, this.productOrderBloc});
  final ProductOrderBloc? productOrderBloc;
  @override
  State<ProductHomePage> createState() => _ProductHomePageState();
}

class _ProductHomePageState extends State<ProductHomePage> {
  List<String> status = [
    'All status',
    'Awaiting payment',
    'Preparing Order',
    'In Transit',
    'Delivered',
    'Completed',
    'Request is being processed',
    'Has been issued',
    'Cancelled'
  ];

  final List<Map<String, String>> valueStatus = [
    {'orderStatus': 'all', 'shippingStatus': 'all'},
    {'orderStatus': 'pending', 'shippingStatus': 'pending'},
    {'orderStatus': 'confirmed', 'shippingStatus': 'pending'},
    {'orderStatus': 'confirmed', 'shippingStatus': 'shipped'},
    {'orderStatus': 'confirmed', 'shippingStatus': 'delivered'},
    {'orderStatus': 'completed', 'shippingStatus': 'delivered'},
    {'orderStatus': 'processing', 'shippingStatus': 'pending'},
    {'orderStatus': 'issued', 'shippingStatus': 'delivered'},
    {'orderStatus': 'cancelled', 'shippingStatus': 'failed'},
  ];
  @override
  void initState() {
    super.initState();
    final orderStatus = valueStatus[0]['orderStatus'] ?? '';
    final shippingStatus = valueStatus[0]['shippingStatus'] ?? '';
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductOrderBloc>().add(
            SelectOrderStatus(0),
          );
      context.read<ProductOrderBloc>().add(
            FetchOrdersByStatus(orderStatus, shippingStatus, false),
          );
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductOrderBloc, ProductOrderState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(10.h),
            Padding(
              padding: EdgeInsets.only(
                  bottom: AppSpacing.padding10h,
                  left: AppSpacing.padding16,
                  right: AppSpacing.padding16),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.padding16,
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                      color: Theme.of(context).blackPrimary(context), width: 1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    focusColor: Theme.of(context).whitePrimary(context),
                    dropdownColor: Theme.of(context).whitePrimary(context),
                    value: state.selectedIndexStatus,
                    style: Theme.of(context).textTheme.lightBodyLargeRegular,
                    icon: SvgPicture.asset(AppAssets.arrowDownSvg),
                    isExpanded: true,
                    items: List.generate(status.length, (index) {
                      return DropdownMenuItem<int>(
                        value: index,
                        child: Text(status[index]),
                      );
                    }),
                    onChanged: (int? index) {
                      context.read<ProductOrderBloc>().add(
                            SelectOrderStatus(index!),
                          );
                      final orderStatus =
                          valueStatus[index]['orderStatus'] ?? '';
                      final shippingStatus =
                          valueStatus[index]['shippingStatus'] ?? '';
                      context.read<ProductOrderBloc>().add(
                            FetchOrdersByStatus(
                                orderStatus, shippingStatus, false),
                          );
                    },
                  ),
                ),
              ),
            ),
            Expanded(
              child: ListOrderProductStatus(
                orderStatus: state.orderState,
                shippingStatus: state.shippingState,
                productOrderBloc: widget.productOrderBloc,
              ),
            ),
          ],
        );
      },
    );
  }
}
