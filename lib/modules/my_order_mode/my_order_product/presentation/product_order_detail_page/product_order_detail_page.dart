import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/base/api_response/status.dart';
import 'package:multime_app/core/components/stripe_card_payment.dart';
import 'package:multime_app/core/config/environment.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/data/param/order_product_param.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_event.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_state.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_order_detail_page/widgets/card_delivery_information_detail.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_order_detail_page/widgets/show_dialog_detial_product_order.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/status_order/widgets/card_order_detail_status.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/status_order/widgets/order_info_widget.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/bloc/check_out_product_bloc.dart';
import 'package:multime_app/shared/widgets/app_loader/app_loader.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';
import 'widgets/card_order_detail.dart';

class ProductOrderDetailPage extends StatefulWidget {
  const ProductOrderDetailPage({
    super.key,
    required this.cardOrderModel,
  });
  final OrderProductModel? cardOrderModel;

  @override
  State<ProductOrderDetailPage> createState() => _ProductOrderDetailPageState();
}

class _ProductOrderDetailPageState extends State<ProductOrderDetailPage> {
  bool _isPaymentProcessing = false;

  @override
  void initState() {
    super.initState();
  }

  Future<void> showCardPaymentBottomSheet(
      BuildContext context, String clientSecret) async {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            children: [
              Container(
                width: 40.w,
                height: 4.h,
                margin: EdgeInsets.only(top: 12.h),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Payment',
                      style: Theme.of(context).textTheme.lightHeadingLarge,
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: StripeCardPayment(
                  clientSecret: clientSecret,
                  publishableKey: AppConfig
                      .stripePublishableKey, // Replace with your Stripe publishable key
                  amount: (widget.cardOrderModel?.total ?? 0).toDouble(),
                  onPaymentResult: (paymentResult) {
                    Navigator.of(context).pop();
                    final status = paymentResult['status'];
                    if (status == 'succeeded') {
                      // Handle successful payment
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Payment successful!')),
                      );
                      // Update order status or refresh the page
                      setState(() {});
                    } else {
                      // Handle failed payment
                      final error = paymentResult['error'] ?? 'Unknown error';
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Payment failed: $error')),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _handleCardPayment(
      int shopId, int addressId, int productId, String unit, int quantity) {
    context.read<CheckoutProductBloc>().add(CheckoutProductOrderEvent(
          OrderProductParam(
            customer_id: gs.uid,
            shop_id: shopId,
            method: 'card',
            address_id: addressId,
            products: [
              OrderProduct(
                productId: productId,
                unit: unit,
                quantity: quantity,
              ),
            ],
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ProductOrderBloc, ProductOrderState>(
          listener: (context, state) {
            if (state.isLoading) {
              AppLoader.show(context);
            } else {
              AppLoader.hide();
            }
            if (state.isSuccess) {
              context.pop(context);
            } else if (state.isError) {}
          },
        ),
        BlocListener<CheckoutProductBloc, CheckoutProductState>(
          listener: (context, state) {
            if (state.orderProductResponse.status == Status.completed) {
              setState(() {
                _isPaymentProcessing = false;
              });

              final clientSecret =
                  state.orderProductResponse.data?.client_secret;
              if (clientSecret != null && clientSecret.isNotEmpty) {
                showCardPaymentBottomSheet(context, clientSecret);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text('Payment setup failed. Please try again.')),
                );
              }
            } else if (state.orderProductResponse.status == Status.error) {
              setState(() {
                _isPaymentProcessing = false;
              });

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(
                        'Error: ${state.orderProductResponse.message ?? "Payment setup failed"}')),
              );
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Order Detail'),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () {
              context.pop();
            },
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      OrderInfoWidget(cardOrderModel: widget.cardOrderModel),
                      Gap(10.h),
                      CardOrderDetailStatus(
                          cardOrderModel: widget.cardOrderModel!),
                      Gap(10.h),
                      widget.cardOrderModel?.orderStatus == 'pending' &&
                              widget.cardOrderModel?.shippingStatus == 'pending'
                          ? BlocBuilder<CheckoutProductBloc,
                              CheckoutProductState>(builder: (context, state) {
                              return Column(
                                children: [
                                  StrongBodyButton(
                                    label: _isPaymentProcessing
                                        ? 'Processing...'
                                        : 'Pay',
                                    onPressed: _isPaymentProcessing
                                        ? null
                                        : () {
                                            setState(() {
                                              _isPaymentProcessing = true;
                                            });

                                            // Check if we already have client_secret
                                            final clientSecret = state
                                                .orderProductResponse
                                                .data
                                                ?.client_secret;
                                            if (clientSecret != null &&
                                                clientSecret.isNotEmpty) {
                                              setState(() {
                                                _isPaymentProcessing = false;
                                              });
                                              showCardPaymentBottomSheet(
                                                  context, clientSecret);
                                            } else {
                                              // Need to create payment intent first
                                              _handleCardPayment(
                                                  widget
                                                      .cardOrderModel!.shopId!,
                                                  widget.cardOrderModel!
                                                      .addressId!,
                                                  widget.cardOrderModel!.id!,
                                                  widget.cardOrderModel!.unit!,
                                                  widget.cardOrderModel!
                                                      .quantity!);
                                            }
                                          },
                                    fontSize: 18,
                                    textColor:
                                        Theme.of(context).whitePrimary(context),
                                    backgroundColor: Theme.of(context)
                                        .alertInformationBase(context),
                                  ),
                                  Gap(16.h),
                                  StrongBodyButton(
                                    label: 'Cancel order',
                                    onPressed: () {
                                      showDialogCancelReason(
                                        context,
                                        () {
                                          context.read<ProductOrderBloc>()
                                            ..add(UpdateOrderProductStatus(
                                                widget.cardOrderModel!.id!,
                                                'cancelled',
                                                'failed'));
                                          context.pop();
                                        },
                                      );
                                    },
                                    fontSize: 18,
                                    textColor:
                                        Theme.of(context).textPrimary(context),
                                    backgroundColor:
                                        Theme.of(context).whitePrimary(context),
                                    borderColor:
                                        Theme.of(context).greyScale500(context),
                                  ),
                                ],
                              );
                            })
                          : widget.cardOrderModel?.orderStatus == 'confirmed' &&
                                  widget.cardOrderModel?.shippingStatus ==
                                      'pending'
                              ? SizedBox.shrink()
                              : widget.cardOrderModel?.orderStatus ==
                                          'confirmed' &&
                                      widget.cardOrderModel?.shippingStatus ==
                                          'shipped'
                                  ? Container(
                                      width: MediaQuery.of(context).size.width,
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(
                                                AppRadius.radius10)),
                                        color: Theme.of(context)
                                            .greyScale50(context),
                                        border: Border.all(
                                          color: Theme.of(context)
                                              .greyScale400(context),
                                          width: 1.0,
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text('Proof of Delivery',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyLargeBold),
                                          Gap(5.h),
                                          Text(
                                              'Photos and videos uploaded by the seller as delivery confirmation.',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .lightBodyMediumMedium
                                                  .copyWith(
                                                    color: Theme.of(context)
                                                        .greyScale600(context),
                                                  )),
                                          Gap(5.h),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  child: CustomImage(
                                                    path:
                                                        'https://demoda.vn/wp-content/uploads/2022/02/anh-que-huong-ruong-bac-thang-don-so-1.jpg',
                                                    imageType:
                                                        ImageType.network,
                                                  ),
                                                ),
                                              ),
                                              Gap(10.w),
                                              Expanded(
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  child: CustomImage(
                                                      path:
                                                          'https://demoda.vn/wp-content/uploads/2022/02/anh-que-huong-ruong-bac-thang-don-so-1.jpg',
                                                      imageType:
                                                          ImageType.network),
                                                ),
                                              )
                                            ],
                                          )
                                        ],
                                      ),
                                    )
                                  : widget.cardOrderModel?.orderStatus ==
                                              'completed' &&
                                          widget.cardOrderModel
                                                  ?.shippingStatus ==
                                              'delivered'
                                      ? Column(
                                          children: [
                                            StrongBodyButton(
                                              label: 'Rating',
                                              onPressed: () {
                                                context.read<ProductOrderBloc>()
                                                  ..add(
                                                      UpdateOrderProductStatus(
                                                          widget.cardOrderModel!
                                                              .id!,
                                                          'completed',
                                                          'delivered'));
                                                context.pop();
                                              },
                                              fontSize: 18,
                                              textColor: Theme.of(context)
                                                  .whitePrimary(context),
                                              backgroundColor: Theme.of(context)
                                                  .successBase(context),
                                            ),
                                            Gap(16.h),
                                            StrongBodyButton(
                                              label: 'Buy again',
                                              onPressed: () {},
                                              fontSize: 18,
                                              textColor: Theme.of(context)
                                                  .textPrimary(context),
                                              backgroundColor: Theme.of(context)
                                                  .whitePrimary(context),
                                              borderColor: Theme.of(context)
                                                  .greyScale500(context),
                                            ),
                                            Gap(16.h),
                                            StrongBodyButton(
                                              label: 'Refund return',
                                              onPressed: () {},
                                              fontSize: 18,
                                              textColor: Theme.of(context)
                                                  .textPrimary(context),
                                              backgroundColor: Theme.of(context)
                                                  .whitePrimary(context),
                                              borderColor: Theme.of(context)
                                                  .greyScale500(context),
                                            ),
                                            Gap(16.h),
                                            Container(
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              padding: EdgeInsets.all(16),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(
                                                        AppRadius.radius10)),
                                                color: Theme.of(context)
                                                    .greyScale50(context),
                                                border: Border.all(
                                                  color: Theme.of(context)
                                                      .greyScale400(context),
                                                  width: 1.0,
                                                ),
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text('Proof of Delivery',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .lightBodyLargeBold),
                                                  Gap(5.h),
                                                  Text(
                                                      'Photos and videos uploaded by the seller as delivery confirmation.',
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .lightBodyMediumMedium
                                                          .copyWith(
                                                            color: Theme.of(
                                                                    context)
                                                                .greyScale600(
                                                                    context),
                                                          )),
                                                  Gap(5.h),
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(10),
                                                          child: CustomImage(
                                                            path:
                                                                'https://demoda.vn/wp-content/uploads/2022/02/anh-que-huong-ruong-bac-thang-don-so-1.jpg',
                                                            imageType: ImageType
                                                                .network,
                                                          ),
                                                        ),
                                                      ),
                                                      Gap(10.w),
                                                      Expanded(
                                                        child: ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(10),
                                                          child: CustomImage(
                                                              path:
                                                                  'https://demoda.vn/wp-content/uploads/2022/02/anh-que-huong-ruong-bac-thang-don-so-1.jpg',
                                                              imageType:
                                                                  ImageType
                                                                      .network),
                                                        ),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              ),
                                            )
                                          ],
                                        )
                                      : widget.cardOrderModel?.orderStatus ==
                                                  'confirmed' &&
                                              widget.cardOrderModel
                                                      ?.shippingStatus ==
                                                  'delivered'
                                          ? Column(
                                              children: [
                                                StrongBodyButton(
                                                  label: 'Mark as Received',
                                                  onPressed: () {
                                                    showConfirmReceivedDialog(
                                                      context,
                                                      onConfirm: () {
                                                        context.read<
                                                            ProductOrderBloc>()
                                                          ..add(UpdateOrderProductStatus(
                                                              widget
                                                                  .cardOrderModel!
                                                                  .id!,
                                                              'completed',
                                                              'delivered'));
                                                      },
                                                    );
                                                  },
                                                  fontSize: 18,
                                                  textColor: Theme.of(context)
                                                      .whitePrimary(context),
                                                  backgroundColor:
                                                      Theme.of(context)
                                                          .alertAttentionBase(
                                                              context),
                                                ),
                                                Gap(16.h),
                                                StrongBodyButton(
                                                  label: 'Contact seller',
                                                  onPressed: () {},
                                                  fontSize: 18,
                                                  textColor: Theme.of(context)
                                                      .textPrimary(context),
                                                  backgroundColor: Theme.of(
                                                          context)
                                                      .whitePrimary(context),
                                                  borderColor: Theme.of(context)
                                                      .blackPrimary(context),
                                                ),
                                              ],
                                            )
                                          : Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Expanded(
                                                    child: StrongBodyButton(
                                                  label: 'By again',
                                                  onPressed: () {},
                                                  textColor: Theme.of(context)
                                                      .whitePrimary(context),
                                                  backgroundColor:
                                                      Theme.of(context)
                                                          .secondary(context),
                                                )),
                                                Gap(12.w),
                                                Expanded(
                                                    child: StrongBodyButton(
                                                  label: 'Contact Seller',
                                                  onPressed: () {},
                                                  fontSize: 12,
                                                  textColor: Theme.of(context)
                                                      .textPrimary(context),
                                                  borderColor: Theme.of(context)
                                                      .greyScale200(context),
                                                  backgroundColor: Theme.of(
                                                          context)
                                                      .whitePrimary(context),
                                                )),
                                              ],
                                            ),
                      Gap(16.h),
                      CardDeliveryInformationDetail(
                          cardOrderModel: widget.cardOrderModel!),
                      Gap(16.h),
                      CardOrderDetail(cardOrderModel: widget.cardOrderModel),
                      Gap(20.h),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
