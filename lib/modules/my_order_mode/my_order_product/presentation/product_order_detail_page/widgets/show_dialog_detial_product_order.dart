import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_bloc.dart';

Future<void> showDialogCancelReason(
    BuildContext context, VoidCallback? onConfirm) {
  final reasons = [
    'I want to update the delivery address/phone number.',
    'I want to add/change the discount code.',
    'I want to change the product (size, color, quantity…).',
    'The payment process is complicated.',
    'I found a better place to buy (cheaper, more reliable, faster delivery…).',
    'I no longer wish to make a purchase.',
    'I cannot find a suitable reason to cancel.',
  ];

  int selectedIndex = -1;

  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          backgroundColor: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Cancellation Reason',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ...List.generate(reasons.length, (index) {
                  return RadioListTile<int>(
                    value: index,
                    groupValue: selectedIndex,
                    contentPadding: EdgeInsets.zero,
                    title: Text(
                      reasons[index],
                      style: Theme.of(context).textTheme.lightBodyMediumMedium,
                    ),
                    activeColor: Colors.black,
                    onChanged: (val) {
                      setState(() {
                        selectedIndex = val!;
                      });
                    },
                  );
                }),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: const BorderSide(color: Colors.black),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Cancel',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: selectedIndex == -1 ? null : onConfirm,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).secondary(context),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Confirm',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeMedium
                              .copyWith(
                                color: selectedIndex != -1
                                    ? Theme.of(context).whitePrimary(context)
                                    : Theme.of(context).greyScale500(context),
                              ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

Future<void> showConfirmReceivedDialog(BuildContext context,
    {VoidCallback? onConfirm,
    bool barrierDismissible = true,
    ProductOrderBloc? productOrderBloc,
    int? orderId}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (ctx) {
      return Dialog(
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
        insetPadding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
        backgroundColor: Theme.of(context).whitePrimary(context),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              Text(
                'Have you received the goods yet?',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.lightBodyXLargeBold,
              ),
              SizedBox(height: 20.h),
              // Buttons
              Row(
                children: [
                  Expanded(
                    child: StrongBodyButton(
                      label: 'Cancel',
                      onPressed: () {
                        // Đóng dialog hiện tại
                        Navigator.of(ctx).pop();
                      },
                      textColor: Theme.of(context).blackPrimary(context),
                      backgroundColor: Theme.of(context).whitePrimary(context),
                      borderColor: Theme.of(context).blackPrimary(context),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: StrongBodyButton(
                      label: 'Confirm',
                      onPressed: () {
                        // Đóng dialog rồi gọi callback
                        Navigator.of(ctx).pop();
                        if (onConfirm != null) onConfirm();
                      },
                      textColor: Theme.of(context).whitePrimary(context),
                      backgroundColor: Theme.of(context).primary(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}
