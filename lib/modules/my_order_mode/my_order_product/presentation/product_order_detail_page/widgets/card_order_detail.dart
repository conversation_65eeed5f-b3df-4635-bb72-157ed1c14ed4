import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/constants/app_spacings.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';
import 'package:multime_app/shared/widgets/image_widget/image_widget.dart';

class CardOrderDetail extends StatelessWidget {
  const CardOrderDetail({super.key, required this.cardOrderModel});
  final OrderProductModel? cardOrderModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(AppRadius.radius10)),
        color: Theme.of(context).greyScale50(context),
        border: Border.all(
          color: Theme.of(context).greyScale400(context),
          width: 1.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Order Details',
              style: Theme.of(context).textTheme.lightBodyLargeBold),
          Gap(10.h),
          Container(
            padding: EdgeInsets.only(bottom: AppSpacing.padding16h),
            decoration: BoxDecoration(
              border: Border(
                  bottom: BorderSide(
                color: Theme.of(context).greyScale400(context),
                width: 1.0,
              )),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    ClipOval(
                      child: CustomImage(
                        path: cardOrderModel?.shop?.salesUser?.profilePicture ??
                            'https://images.pexels.com/photos/371633/pexels-photo-371633.jpeg?cs=srgb&dl=clouds-country-daylight-371633.jpg&fm=jpg',
                        fit: BoxFit.cover,
                        width: 45.w,
                        height: 45.h,
                        imageType: ImageType.network,
                      ),
                    ),
                    Gap(10.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${cardOrderModel?.shop?.salesUser?.fullName ?? 'seller'}',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeBold!
                              .copyWith(
                                color: Theme.of(context).textBlack(context),
                              ),
                        ),
                        Gap(5.h),
                        Row(
                          children: [
                            SvgPicture.asset(
                              AppAssets.location_tick,
                            ),
                            Gap(5.w),
                            Text(
                              cardOrderModel?.shop?.addresses?[0].city ??
                                  'Address',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        )
                      ],
                    ),
                  ],
                ),
                ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                          horizontal: AppSpacing.padding16,
                          vertical: AppSpacing.padding8h),
                      backgroundColor: Theme.of(context).whitePrimary(context),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppRadius.radius10),
                      ),
                      side: BorderSide(),
                    ),
                    onPressed: () {},
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          AppAssets.messageAppbarSvg,
                        ),
                        Gap(5.w),
                        Text(
                          'Chat',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    )),
              ],
            ),
          ),
          Gap(10.h),
          Container(
            padding: EdgeInsets.only(bottom: AppSpacing.padding16h),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).greyScale400(context),
                  width: 1.0,
                ),
              ),
            ),
            child: Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: CustomImage(
                    path:
                        'https://cdn.pixabay.com/photo/2016/10/25/12/28/iceland-1768744_1280.jpg',
                    fit: BoxFit.cover,
                    width: 60.w,
                    height: 60.h,
                    imageType: ImageType.network,
                  ),
                ),
                Gap(10.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cardOrderModel?.productSnapshot!.snapshotName ?? '',
                        maxLines: 2,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              color: Theme.of(context).textBlack(context),
                            ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Gap(5.h),
                      Text(
                        cardOrderModel?.productSnapshot!.snapshotDescription ??
                            '',
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Gap(5.h),
                      Row(
                        children: [
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'Brand: ',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodyMediumRegular
                                        .copyWith(
                                          color: Theme.of(context)
                                              .textBlack(context),
                                        ),
                                  ),
                                  TextSpan(
                                    text:
                                        '${cardOrderModel?.productSnapshot!.snapshotBrandText ?? 'B4BY'}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodyMediumSemiBold,
                                  )
                                ],
                              ),
                            ),
                          ),
                          Text(
                              '\$${cardOrderModel?.productSnapshot!.snapshotPrice ?? '0.00'}')
                        ],
                      ),
                      Gap(5.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: RichText(
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'Category: ',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodyMediumRegular
                                        .copyWith(
                                          color: Theme.of(context)
                                              .textBlack(context),
                                        ),
                                  ),
                                  TextSpan(
                                    text:
                                        '\$${cardOrderModel?.productSnapshot!.snapshotCategoryName ?? 'Healing'}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .lightBodyMediumSemiBold,
                                  )
                                ],
                              ),
                            ),
                          ),
                          Gap(10.w),
                          Text(
                              'x${cardOrderModel?.quantity ?? '1'} ${cardOrderModel?.productSnapshot!.snapshotUnit ?? ''}')
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          Gap(10.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              RichText(
                  text: TextSpan(
                      text: 'Order Total:  ',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyMediumRegular
                          .copyWith(
                              color: Theme.of(context).greyScale500(context)),
                      children: [
                    TextSpan(
                      text: ' \$${cardOrderModel!.total}',
                      style: Theme.of(context)
                          .textTheme
                          .lightBodyXLargeBold!
                          .copyWith(color: Theme.of(context).primary(context)),
                    )
                  ]))
            ],
          ),
        ],
      ),
    );
  }
}
