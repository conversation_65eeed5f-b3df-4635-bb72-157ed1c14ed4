import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';

class CardInfoDetails extends StatelessWidget {
  const CardInfoDetails({super.key, required this.name, required this.phone, required this.address});
  final String name,address;
  final int phone;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Delivery Information', style: Theme.of(context).textTheme.bodyMedium,),
          Gap(10.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(AppAssets.locationSvg, width: 20.w, height: 20.h,colorFilter: (
                  ColorFilter.mode(Theme.of(context).textPrimary(context), BlendMode.srcIn)
              )),
              Gap(10.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(name, style: Theme.of(context).textTheme.bodyMedium,),
                    Text('Phone: $phone', style: Theme.of(context).textTheme.bodyMedium,),
                    Text(address, style: Theme.of(context).textTheme.bodyMedium,maxLines: 2,),
                  ],
                ),
              ),
              Gap(5.w),
              Center(child: GestureDetector(
                onTap: (){},
                child: Container(
                  height: 35,
                  width: 92,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Theme.of(context).whitePrimary(context),
                    border: Border.all(color: Theme.of(context).textPrimary(context), width: 1),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Text('Change', style: Theme.of(context).textTheme.bodyMedium,),
                ),
              ),)
            ],
          )

        ],
      ),
    );
  }
}