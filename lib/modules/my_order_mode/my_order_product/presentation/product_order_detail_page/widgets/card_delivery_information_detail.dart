import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';

class CardDeliveryInformationDetail extends StatelessWidget {
  const CardDeliveryInformationDetail(
      {super.key, required this.cardOrderModel});
  final OrderProductModel? cardOrderModel;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(AppRadius.radius10)),
        color: Theme.of(context).greyScale50(context),
        border: Border.all(
          color: Theme.of(context).greyScale400(context),
          width: 1.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Delivery Information',
              style: Theme.of(context).textTheme.lightBodyLargeBold),
          Gap(5.h),
          Text(cardOrderModel?.customer?.fullName ?? '',
              style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                    color: Theme.of(context).greyScale600(context),
                  )),
          Gap(5.h),
          Text('+${cardOrderModel?.customer?.mobile ?? ''}',
              style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                    color: Theme.of(context).greyScale600(context),
                  )),
          Gap(5.h),
          Text('Postal Code: ${cardOrderModel?.address?.postalCode ?? '1020'}',
              style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                    color: Theme.of(context).greyScale600(context),
                  )),
          Gap(5.h),
          Text(
              '${cardOrderModel?.address?.street ?? ''} ${cardOrderModel?.address?.city ?? 'location'} ',
              style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                    color: Theme.of(context).greyScale600(context),
                  )),
          Gap(5.h),
          Text(
              'Additional Delivery Notes: ${cardOrderModel?.productSnapshot?.snapshotCountryOfOrigin ?? ''}',
              style: Theme.of(context).textTheme.lightBodyMediumMedium.copyWith(
                    color: Theme.of(context).greyScale600(context),
                  ))
        ],
      ),
    );
  }
}
