import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/utils/formatters.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';

class CardOrderDetailStatus extends StatelessWidget {
  CardOrderDetailStatus({
    super.key,
    required this.cardOrderModel,
  });

  OrderProductModel? cardOrderModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: Colors.white,
      ),
      child: Stack(
        children: [
          Positioned(
            left: 18.w,
            top: 18.h,
            bottom: 18.h,
            child: Container(
              width: 2.w,
              height: 300.h,
              color: Theme.of(context).successBase(context),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(40),
                      color: (cardOrderModel?.orderStatus == 'pending' &&
                              cardOrderModel?.shippingStatus == 'pending')
                          ? Theme.of(context).successBase(context)
                          : Colors.white,
                      border: Border.all(
                          color: Theme.of(context).successBase(context),
                          width: 2),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        AppAssets.bill,
                        width: 20,
                        height: 20,
                        colorFilter:
                            (cardOrderModel?.orderStatus == 'pending' &&
                                    cardOrderModel?.shippingStatus == 'pending')
                                ? const ColorFilter.mode(
                                    Colors.white, BlendMode.srcIn)
                                : ColorFilter.mode(
                                    Theme.of(context).successBase(context),
                                    BlendMode.srcIn),
                      ),
                    ),
                  ),
                  Gap(10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order placed',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Gap(4.h),
                        Text(
                          TFormatter.formatDateTime(cardOrderModel!.createdAt!),
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Gap(20.h),
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(40),
                      color: (cardOrderModel?.orderStatus == 'confirmed' &&
                              cardOrderModel?.shippingStatus == 'pending')
                          ? Theme.of(context).successBase(context)
                          : Colors.white,
                      border: Border.all(
                          color: Theme.of(context).successBase(context),
                          width: 2),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        AppAssets.moneyRecive,
                        width: 20,
                        height: 20,
                        colorFilter:
                            (cardOrderModel?.orderStatus == 'confirmed' &&
                                    cardOrderModel?.shippingStatus == 'pending')
                                ? const ColorFilter.mode(
                                    Colors.white, BlendMode.srcIn)
                                : ColorFilter.mode(
                                    Theme.of(context).successBase(context),
                                    BlendMode.srcIn),
                      ),
                    ),
                  ),
                  Gap(10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Paid \$${cardOrderModel?.total}',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Gap(4.h),
                        Text(
                          TFormatter.formatDateTime(cardOrderModel!.createdAt!),
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Gap(20.h),
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(40),
                      color: (cardOrderModel?.orderStatus == 'confirmed' &&
                              cardOrderModel?.shippingStatus == 'shipped')
                          ? Theme.of(context).successBase(context)
                          : Colors.white,
                      border: Border.all(
                          color: Theme.of(context).successBase(context),
                          width: 2),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        AppAssets.truckFast,
                        width: 20,
                        height: 20,
                        colorFilter:
                            (cardOrderModel?.orderStatus == 'confirmed' &&
                                    cardOrderModel?.shippingStatus == 'shipped')
                                ? const ColorFilter.mode(
                                    Colors.white, BlendMode.srcIn)
                                : ColorFilter.mode(
                                    Theme.of(context).successBase(context),
                                    BlendMode.srcIn),
                      ),
                    ),
                  ),
                  Gap(10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'In Transit',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Gap(4.h),
                        Text(
                          TFormatter.formatDateTime(cardOrderModel!.createdAt!),
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Gap(20.h),
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(40),
                      color: (cardOrderModel?.orderStatus == 'confirmed' &&
                              cardOrderModel?.shippingStatus == 'delivered')
                          ? Theme.of(context).successBase(context)
                          : Colors.white,
                      border: Border.all(
                          color: Theme.of(context).successBase(context),
                          width: 2),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        AppAssets.box1,
                        width: 20,
                        height: 20,
                        colorFilter: (cardOrderModel?.orderStatus ==
                                    'confirmed' &&
                                cardOrderModel?.shippingStatus == 'delivered')
                            ? const ColorFilter.mode(
                                Colors.white, BlendMode.srcIn)
                            : ColorFilter.mode(
                                Theme.of(context).successBase(context),
                                BlendMode.srcIn),
                      ),
                    ),
                  ),
                  Gap(10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Delivered',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Gap(4.h),
                        Text(
                          TFormatter.formatDateTime(cardOrderModel!.createdAt!),
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Gap(20.h),
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(40),
                      color: (cardOrderModel?.orderStatus == 'completed' &&
                              cardOrderModel?.shippingStatus == 'delivered')
                          ? Theme.of(context).successBase(context)
                          : Colors.white,
                      border: Border.all(
                          color: Theme.of(context).successBase(context),
                          width: 2),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        AppAssets.star1,
                        width: 20,
                        height: 20,
                        colorFilter: (cardOrderModel?.orderStatus ==
                                    'completed' &&
                                cardOrderModel?.shippingStatus == 'delivered')
                            ? const ColorFilter.mode(
                                Colors.white, BlendMode.srcIn)
                            : ColorFilter.mode(
                                Theme.of(context).successBase(context),
                                BlendMode.srcIn),
                      ),
                    ),
                  ),
                  Gap(10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Rating',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Gap(4.h),
                        Text(
                          TFormatter.formatDateTime(cardOrderModel!.createdAt!),
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
