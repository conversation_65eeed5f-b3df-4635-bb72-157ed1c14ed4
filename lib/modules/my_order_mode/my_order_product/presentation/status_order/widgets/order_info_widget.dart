import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';

class OrderInfoWidget extends StatelessWidget {
  OrderInfoWidget({super.key, required this.cardOrderModel});

  OrderProductModel? cardOrderModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Theme.of(context).whitePrimary(context),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Order Code',
                style:
                    Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                          color: Theme.of(context).greyScale600(context),
                        ),
              ),
              Gap(10.w),
              Text('${cardOrderModel?.id}',
                  style: Theme.of(context).textTheme.lightBodyMediumSemiBold),
              Gap(10.w),
              InkWell(
                onTap: () {
                  Clipboard.setData(
                    ClipboardData(text: '${cardOrderModel?.id ?? ''}'),
                  );
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        backgroundColor: Theme.of(context).successBase(context),
                        content: Text(
                          'Copied!',
                          style: Theme.of(context)
                              .textTheme
                              .lightBodyLargeMedium
                              .copyWith(
                                color: Theme.of(context).whitePrimary(context),
                              ),
                        )),
                  );
                },
                child: SvgPicture.asset(
                  AppAssets.copySvg,
                  color: Theme.of(context).greyScale500(context),
                ),
              )
            ],
          ),
          Gap(10.h),
          Row(
            children: [
              Text(
                'Order Status',
                style:
                    Theme.of(context).textTheme.lightBodyMediumRegular.copyWith(
                          color: Theme.of(context).greyScale600(context),
                        ),
              ),
              Gap(10.w),
              Text(
                titleOrderStatus(cardOrderModel!.orderStatus!,
                    cardOrderModel!.shippingStatus!),
                style: Theme.of(context)
                    .textTheme
                    .lightBodyMediumSemiBold
                    .copyWith(
                      color: _getStatusTextColor(
                          context,
                          cardOrderModel!.orderStatus!,
                          cardOrderModel!.shippingStatus!),
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String titleOrderStatus(String status, String shippingStatus) {
    if (status == 'pending' && shippingStatus == 'pending') {
      return 'Awaiting payment';
    } else if (status == 'confirmed' && shippingStatus == 'pending') {
      return 'Preparing Order';
    } else if (status == 'confirmed' && shippingStatus == 'shipped') {
      return 'In Transit';
    } else if (status == 'confirmed' && shippingStatus == 'delivered') {
      return 'Delivered';
    } else if (status == 'completed' && shippingStatus == 'delivered') {
      return 'Completed';
    } else
      return 'Request is being processed';
  }

  Color _getStatusTextColor(
      BuildContext context, String status, String shippingStatus) {
    if (status == 'pending' && shippingStatus == 'pending') {
      return Theme.of(context).alertInformationBase(context);
    } else if (status == 'confirmed' && shippingStatus == 'pending') {
      return Theme.of(context).alertAttentionBase(context);
    } else if (status == 'confirmed' && shippingStatus == 'shipped') {
      return Theme.of(context).alertAttentionBase(context);
    } else if (status == 'confirmed' && shippingStatus == 'delivered') {
      return Theme.of(context).alertAttentionBase(context);
    } else if (status == 'completed' && shippingStatus == 'delivered') {
      return Theme.of(context).successBase(context);
    } else {
      return Theme.of(context).primary(context);
    }
  }
}
