// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:gap/gap.dart';
// import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/fake_data/card_order_model.dart';

// import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/status_order/widgets/card_order_detail_status.dart';
// import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/status_order/widgets/order_info_widget.dart';


// class StatusOrder extends StatelessWidget {
//   const StatusOrder({super.key, required this.cardOrderModel});
//   final CardOrderModel cardOrderModel;

//   @override
//   Widget build(BuildContext context) {

//     return Scaffold(
//       backgroundColor: Colors.grey[50],
//       appBar: AppBar(
//         leading: IconButton(
//           icon: const Icon(Icons.arrow_back),
//           onPressed: () => Navigator.pop(context),
//         ),
//         title: const Text(
//           'Shipping Information',
//           style: TextStyle(fontWeight: FontWeight.bold),
//         ),
//         centerTitle: true,
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           children: [
//             Container(
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(6),
//                 color: Colors.white,
//               ),
//               child: CardOrderDetailStatus(cardOrderModel: cardOrderModel),
//             ),
//             Gap(20.h),
//             OrderInfoWidget(cardOrderModel: cardOrderModel),

//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildOrderInfo() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const Text(
//           'Order ID',
//           style: TextStyle(fontWeight: FontWeight.bold),
//         ),
//         const SizedBox(height: 5),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             const Text(
//               '2372845694875984',
//               style: TextStyle(color: Colors.black87),
//             ),
//             ElevatedButton(
//               onPressed: () {},
//               style: ElevatedButton.styleFrom(
//                 padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
//               ),
//               child: const Text('Copy'),
//             ),
//           ],
//         ),
//         const SizedBox(height: 10),
//         const Text(
//           'Order time Apr 19, 2024. 7:55 AM',
//           style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
//         ),
//       ],
//     );
//   }
// }
