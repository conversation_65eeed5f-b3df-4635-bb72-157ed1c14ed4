import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';

import '../fake_data/cancel_reason_data.dart';

class CancellationReasonPage extends StatefulWidget {
  const CancellationReasonPage({super.key});

  @override
  State<CancellationReasonPage> createState() => _CancellationReasonPageState();
}

class _CancellationReasonPageState extends State<CancellationReasonPage> {
  String selectedReason = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cancellation Reason'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            context.pop();
          },
        ),
      ),
      body: Column(
        children: [
          ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(vertical: 10.h),
              itemBuilder: (context, index) {
                return ListTile(
                  leading: ClipRRect(
                    borderRadius: BorderRadius.circular(5.r),
                    child: Radio(
                        value: cancelReasonData[index],
                        groupValue: selectedReason,
                        activeColor: Theme.of(context).informationBase(context),
                        onChanged: (value) {
                          setState(() {
                            selectedReason = value!;
                          });
                        }),
                  ),
                  title: Text(
                    cancelReasonData[index],
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: Theme.of(context).secondary(context),
                        ),
                  ),
                );
              },
              itemCount: cancelReasonData.length),
           Gap(20.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Gap(28.w),
              Expanded(child: StrongBodyButton(label: 'Cancel',
                  onPressed: (){
                context.pop();
              },
                textColor: Theme.of(context).whitePrimary(context),
                backgroundColor: Theme.of(context).secondary(context),
              )),
              Gap(14.w),
              Expanded(child: StrongBodyButton(label: 'Confirm',
                  onPressed: (){

                context.go(RouteName.orderProductPage);


              })),
              Gap(28.w),
            ],
          )
          
          
        ],
      ),
    );
  }
}
