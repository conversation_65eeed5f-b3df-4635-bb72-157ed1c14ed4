import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';

import '../fake_data/card_order_model.dart';

class ProductReview extends StatelessWidget {
  const ProductReview({super.key, required this.cardOrderModel});

  final CardOrderModel cardOrderModel;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).greyScale50(context),
      appBar: AppBar(
        title: const Text('Product Review'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            context.pop();
          },
        ),
      ),
      body: ListView.builder(
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              color: Theme.of(context).whitePrimary(context),
              border: Border.all(
                width: 1,
                color: Theme.of(context).whitePrimary(context),
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 24.h),
            margin: EdgeInsets.symmetric(vertical: 14.h, horizontal: 14.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      width: 32.w,
                      height: 32.h,
                      margin: EdgeInsets.symmetric(
                          horizontal: 14.w, vertical: 14.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        image: DecorationImage(
                          image: AssetImage(
                              cardOrderModel.cardOrderProducts[index].image),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    Expanded(
                        child:
                            Text(cardOrderModel.cardOrderProducts[index].name)),
                  ],
                ),
                Gap(14.h),
                Row(
                  children: [
                    Text(
                      'Product Rating:',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    RatingBar.builder(
                      initialRating: 4,
                      minRating: 1,
                      direction: Axis.horizontal,
                      allowHalfRating: true,
                      itemCount: 5,
                      itemSize: 20,
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      itemBuilder: (context, _) => Icon(
                        Icons.star,
                        color: Theme.of(context).warningBase(context),
                      ),
                      onRatingUpdate: (rating) {
                        print(rating);
                      },
                    ),
                  ],
                ),
                Gap(14.h),
                const StrongBodyTextField(labalText: 'Review'),
              ],
            ),
          );
        },
        itemCount: cardOrderModel.cardOrderProducts.length,
      ),
    );
  }
}
