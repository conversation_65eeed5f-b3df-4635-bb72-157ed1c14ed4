import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/fake_data/card_order_model.dart';

import 'card_order_product_model.dart';

List<CardOrderModel> cardOrderData = [
  // Đơn hàng 1 với nhiều sản phẩm
  CardOrderModel(
    id: 1,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 1,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image:AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 2,
        rating: 4.5,
      ),
      CardOrderProductModel(
        id: 2,
        name: 'GUM BALM GEL FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 150,
        quantity: 3,
        rating: 3.5,
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Awaiting payment',
    time: '21:27 02-11-2024',
  ),

  // Đơn hàng 2 với một sản phẩm
  CardOrderModel(
    id: 2,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 3,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 5,
        rating: 4.5,
      ),
      CardOrderProductModel(
        id: 4,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 5,
        rating: 3.5
      ),
      CardOrderProductModel(
        id: 5,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image:AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 5,
        rating: 4.5,
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Out for delivery',
    time: '21:27 02-11-2024',
  ),

  // Đơn hàng 3 với một sản phẩm
  CardOrderModel(
    id: 3,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 6,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 2,
        rating: 3.5
      ),
      CardOrderProductModel(
        id: 7,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 2,
        rating: 4.5
      ),
      CardOrderProductModel(
        id: 8,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 2,
        rating: 2.5
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Request is being processed',
    time: '21:27 02-11-2024',
  ),

  // Đơn hàng 4 với một sản phẩm
  CardOrderModel(
    id: 4,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 9,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 5,
        rating: 1.5
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Refund has been issued',
    time: '21:27 02-11-2024',
  ),

  // Đơn hàng 5 với một sản phẩm
  CardOrderModel(
    id: 5,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 10,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 3,
        rating: 3
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Cancelled',
    time: '21:27 02-11-2024',
  ),

  // Đơn hàng 6 với một sản phẩm
  CardOrderModel(
    id: 6,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 11,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 2,
        rating: 2.5
      ),
      CardOrderProductModel(
          id: 12,
          name: 'Vitamin E4',
          image: AppAssets.orderProductImg,
          description: 'Lorem Ipsum is simply dummy text of the',
          price: 210,
          quantity: 2,
          rating: 2.5
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Delivery successful',
    time: '21:27 02-11-2024',
  ),

  CardOrderModel(
    id: 7,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 13,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image:AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 2,
        rating: 4.5,
      ),
      CardOrderProductModel(
        id: 14,
        name: 'GUM BALM GEL FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 150,
        quantity: 3,
        rating: 3.5,
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Awaiting payment',
    time: '21:27 02-11-2024',
  ),

  CardOrderModel(
    id:8,
    cardOrderProducts: [
      CardOrderProductModel(
        id: 15,
        name: 'GUM BALM GEL CURR FOR KID CURR',
        image:AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 210,
        quantity: 2,
        rating: 4.5,
      ),
      CardOrderProductModel(
        id: 16,
        name: 'GUM BALM GEL FOR KID CURR',
        image: AppAssets.orderProductImg,
        description: 'Lorem Ipsum is simply dummy text of the',
        price: 150,
        quantity: 3,
        rating: 3.5,
      ),
    ],
    authShop: 'Lincoln Shop',
    status: 'Awaiting payment',
    time: '21:27 02-11-2024',
  ),
];
