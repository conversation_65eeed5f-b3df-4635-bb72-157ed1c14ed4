import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_button.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/fake_data/card_order_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/request_complaint/widgets/free_return.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/request_complaint/widgets/item_return_widget.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/request_complaint/widgets/reason_widget.dart';

class RequestComplaintPage extends StatelessWidget {
  const RequestComplaintPage({super.key, required this.cardOrderModel});

  final CardOrderModel cardOrderModel;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Request Complaint'),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const FreeReturn(),
              Gap(10.h),
              const ReasonWidget(),
              Gap(10.h),
               ItemReturnWidget(
                cardOrderModel: cardOrderModel,
              ),
              Gap(10.h),
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).whitePrimary(context),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Refund information',
                      style: Theme.of(context)
                          .textTheme
                          .displayLarge!
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                    Gap(16.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Refund: ',
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    color: Theme.of(context).textBlack(context),
                                  ),
                        ),
                        Gap(10.w),
                        Text(
                          'My Balance',
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge!
                              .copyWith(
                                  color: Theme.of(context).textBlack(context),
                                  fontWeight: FontWeight.bold),
                        ),
                        const Spacer(),
                      ],
                    ),
                    Gap(10.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Email: ',
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    color: Theme.of(context).textBlack(context),
                                  ),
                        ),
                        Gap(10.w),
                        Text(
                          '<EMAIL>',
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge!
                              .copyWith(
                                  color: Theme.of(context).textBlack(context),
                                  fontWeight: FontWeight.bold),
                        ),
                        const Spacer(),
                      ],
                    ),
                    Gap(10.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: 100.w,
                          child: Text(
                            'Refund amount: ',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  color: Theme.of(context).textBlack(context),
                                ),
                          ),
                        ),
                        Gap(10.w),
                        Text(
                          '400\$',
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge!
                              .copyWith(
                                  color: Theme.of(context).errorBase(context),
                                  fontWeight: FontWeight.bold),
                        ),
                        const Spacer(),
                      ],
                    ),
                  ],
                ),
              ),
              Gap(16.h),
              StrongBodyButton(
                  label: 'Confirm',
                  onPressed: () {},
                textColor: Theme.of(context).whitePrimary(context),
                backgroundColor: Theme.of(context).textBlack(context),
              ),
              Gap(16.h),
            ],
          ),
        ),
      ),
    );
  }
}
