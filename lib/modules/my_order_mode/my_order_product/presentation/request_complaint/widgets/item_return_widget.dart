import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/fake_data/card_order_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/request_complaint/widgets/card_item_return.dart';

class ItemReturnWidget extends StatelessWidget {
  const ItemReturnWidget({super.key, required this.cardOrderModel});
  final CardOrderModel cardOrderModel ;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Theme.of(context).whitePrimary(context),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Item(s) you want to return',style: Theme.of(context).textTheme.displayLarge!.copyWith(
            fontWeight: FontWeight.bold
          ),),
          Gap(16.h),
          ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context,index){
                return CardItemReturn(cardOrderModel: cardOrderModel);
              }, itemCount: 1)
        ],
      ),
    );
  }
}
