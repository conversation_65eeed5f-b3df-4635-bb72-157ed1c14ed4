import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import 'package:multime_app/modules/help_center_mode/presentation/widgets/custom_dropdown_button.dart';

class ReasonWidget extends StatefulWidget {
  const ReasonWidget({super.key});

  @override
  State<ReasonWidget> createState() => _ReasonWidgetState();
}

class _ReasonWidgetState extends State<ReasonWidget> {
  List<String> items = [
    'Missing part of the order',
    'Seller went wrong item',
    'Damaged item',
    'Product is defective or does not work',
    'Expired products',
    'Product is significantly different from description',
    'Product is used/refubished/rebuilt',
    'Counterfeit product',
    'Change of Mind'
  ];
  String selectedReason = '';
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Theme.of(context).whitePrimary(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reason',
            style: Theme.of(context).textTheme.labelLarge,
          ),
          Gap(16.h),
          CustomDropDownButton(
              arr: items,
              hintText: 'Select Reason',
              labelText: 'Reason',
              onChanged: (value) {
                setState(() {
                  selectedReason = value!;
                });
              }),
          Gap(16.h),
          const StrongBodyTextField(
            hintText: 'Describe',
            labalText: 'Describe',
            maxLines: 3,
          ),
          Gap(16.h),
          Row(
            children: [
              DottedBorder(
                dashPattern: const [12, 12],
                color: Theme.of(context).greyScale400(context),
                strokeWidth: 1.5,
                child: Container(
                  height: 104.h,
                  alignment: Alignment.center,
                  width: 104.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).greyScale100(context),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppAssets.gallerySvg,
                        width: 24.w,
                        height: 24.h,
                      ),
                      Gap(4.h),
                      Text(
                        'Add Photo',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              color: Theme.of(context).secondary(context),
                            ),
                      ),
                      Gap(4.h),
                      Text(
                        '0/6',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              color: Theme.of(context).secondary(context),
                            ),
                      ),
                    ],
                  ),
                ),
              ),
              Gap(16.w),
              DottedBorder(
                dashPattern: const [12, 12],
                color: Theme.of(context).greyScale400(context),
                strokeWidth: 1.5,
                strokeCap: StrokeCap.round,
                child: Container(
                  height: 104.h,
                  alignment: Alignment.center,
                  width: 104.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).greyScale100(context),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppAssets.battery,
                        width: 24.w,
                        height: 24.h,
                      ),
                      Gap(4.h),
                      Text(
                        'Add Video',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              color: Theme.of(context).secondary(context),
                            ),
                      ),
                      Gap(4.h),
                      Text(
                        '0/1',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              color: Theme.of(context).secondary(context),
                            ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
