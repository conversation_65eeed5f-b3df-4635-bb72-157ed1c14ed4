import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/theme.dart';

import '../../fake_data/card_order_model.dart';

class CardItemReturn extends StatelessWidget {
  const CardItemReturn({super.key, required this.cardOrderModel});
  final CardOrderModel cardOrderModel;

  @override
  Widget build(BuildContext context) {
    return   Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: cardOrderModel.cardOrderProducts.length ,
          itemBuilder: (context, index) {
            return  Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: const DecorationImage(
                      image: NetworkImage(
                          'https://imagedelivery.net/ZeGtsGSjuQe1P3UP_zk3fQ/ede24b65-497e-4940-ea90-06cc2757a200/storedata'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Gap(10.w),
                Expanded(
                  child:   Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 100.w,
                        child: Text(
                          cardOrderModel.cardOrderProducts[index].name,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: Theme
                              .of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(
                            color: Theme.of(context).textBlack(context),
                          ),
                        ),
                      ),
                      Text(
                        'x${ cardOrderModel.cardOrderProducts[index].quantity}',
                        style: Theme
                            .of(context)
                            .textTheme
                            .bodySmall,
                      ),
                      Gap(10.w),
                      Text(
                        '${ cardOrderModel.cardOrderProducts[index].price}',
                        style: Theme
                            .of(context)
                            .textTheme
                            .bodySmall,
                      ),
                    ],
                  ),
                ),

              ],

            );
          },
          separatorBuilder: (context, index) => const Divider(),
        ),
        Gap(10.h),
        const Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            RichText(
                text: TextSpan(
                    text:
                    'Total payment: ',
                    style: Theme.of(context).textTheme.labelLarge,
                    children: [
                      TextSpan(
                        text: '\$${_calculateTotalPrice()}',
                        style: Theme.of(context)
                            .textTheme
                            .labelLarge!
                            .copyWith(
                            color: Theme.of(context)
                                .errorBase(context)),
                      )
                    ]))
          ],
        ),
      ],
    );
  }
// Hàm tính tổng giá
  double _calculateTotalPrice() {
    return cardOrderModel.cardOrderProducts.fold(
      0.0,
          (previousValue, product) =>
      previousValue + (product.price * product.quantity),
    );
  }

}
