import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:multime_app/core/themes/app_text_style.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/strong_body_text_field.dart';
import '../../../../../auth_mode/presentation/widgets/strong_body_button.dart';

class FreeReturn extends StatefulWidget {
  const FreeReturn({super.key});

  @override
  State<FreeReturn> createState() => _FreeReturnState();
}

class _FreeReturnState extends State<FreeReturn> {
  late TextEditingController _controller;
  String? groupValue;

  List<String> cancellation = [
    'I have received all my of items but there are issues',
    'I dont receive some/all items',
  ];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    groupValue = cancellation[0];
    _controller.text = groupValue!;
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).whitePrimary(context),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Free Return. How Can We Help',
            style: Theme.of(context).textTheme.lightBodyLargeBold.copyWith(
                  fontSize: 18,
                ),
          ),
          Gap(14.h),
          const Divider(),
          Gap(14.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: StrongBodyTextField(
                  controller: _controller,
                  readOnly: true,
                  maxLines: 2,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  border: InputBorder.none,
                ),
              ),
              GestureDetector(
                onTap: () {
                  showDialog(
                      context: context,
                      builder: (context) {
                        return StatefulBuilder(
                          builder: (context, setState) {
                            return AlertDialog(
                              backgroundColor: Colors.white,
                              title: Text(
                                'Select cancellation Reason',
                                style: Theme.of(context).textTheme.labelLarge,
                                textAlign: TextAlign.center,
                              ),
                              content: SingleChildScrollView(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: List.generate(
                                    cancellation.length,
                                    (index) => ListTile(
                                      leading: ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(5.r),
                                        child: Radio<String>(
                                          value: cancellation[index],
                                          groupValue: groupValue,
                                          activeColor: Theme.of(context)
                                              .informationBase(context),
                                          onChanged: (value) {
                                            debugPrint('valuess: $value');
                                            setState(() {
                                              groupValue = value!;
                                            });
                                          },
                                        ),
                                      ),
                                      title: Text(
                                        cancellation[index],
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(
                                              color: Theme.of(context)
                                                  .secondary(context),
                                            ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              actions: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: StrongBodyButton(
                                        label: 'Cancel',
                                        onPressed: () {
                                          Navigator.pop(context);
                                        },
                                        textColor: Theme.of(context)
                                            .whitePrimary(context),
                                        backgroundColor: Theme.of(context)
                                            .secondary(context),
                                      ),
                                    ),
                                    Gap(14.w),
                                    Expanded(
                                      child: StrongBodyButton(
                                        label: 'Confirm',
                                        onPressed: () {
                                          _controller.text = groupValue!;
                                          Navigator.pop(context);
                                        },
                                        textColor: Theme.of(context)
                                            .whitePrimary(context),
                                        backgroundColor: Theme.of(context)
                                            .errorBase(context),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            );
                          },
                        );
                      });
                },
                child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Text(
                    'Change',
                    style: Theme.of(context).textTheme.labelLarge!.copyWith(
                          color: Theme.of(context).hyperlink(context),
                        ),
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
