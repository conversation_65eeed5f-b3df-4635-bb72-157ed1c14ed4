import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/core/domain/global_dependencies.dart';
import 'package:multime_app/core/themes/theme.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/show_dialog_confirm.dart';

class ShortcutIcons extends StatelessWidget {
  const ShortcutIcons({super.key});

  @override
  Widget build(BuildContext context) {
    final shortcuts = [
      {'icon': AppAssets.lion, 'label': 'StrongBody.ai'},
      {'icon': AppAssets.voice, 'label': 'Voice Hub'},
      {'icon': AppAssets.business, 'label': 'Business'},
      {'icon': AppAssets.newsHome, 'label': 'World News'},
      {'icon': AppAssets.user_hub, 'label': 'Used Hub'},
      {'icon': AppAssets.datingHome, 'label': 'Dating'},
    ];
    return GridView.builder(
      itemCount: shortcuts.length,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
      ),
      itemBuilder: (_, index) {
        return GestureDetector(
          onTap: () async {
            if (gs.isLoggedIn == false) {
              await showCustomConfirmDialog(context);
              return;
            }
            switch (index) {
              case 0:
                context.push(RouteName.homeStrongBody);
                break;
              case 1:
                break;
              case 2:
                context.push(RouteName.businesMode);
                break;
              case 3:
                context.push(RouteName.newPage);
                break;
              case 4:
                context.push(RouteName.socialPage);
                break;
              case 5:
                context.push(RouteName.datingPage);
                break;
            }
          },
          child: Container(
            width: double.infinity,
            height: double.infinity,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Theme.of(context).greyScale50(context),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SizedBox.expand(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    shortcuts[index]['icon'] as String,
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                  ),
                  SizedBox(height: 4),
                  Text(shortcuts[index]['label'] as String,
                      textAlign: TextAlign.center),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
