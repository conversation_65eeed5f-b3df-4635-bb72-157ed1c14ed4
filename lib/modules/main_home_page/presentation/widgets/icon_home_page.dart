// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:multime_app/core/constants/app_assets.dart';
import 'package:multime_app/modules/main_home_page/presentation/widgets/shortcut_Icons.dart';

class IconHomePage extends StatelessWidget {
  const IconHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.48,
      child: Stack(
        fit: StackFit.expand,
        clipBehavior: Clip.none,
        children: [
          Positioned.fill(
            child: SvgPicture.asset(
              width: double.infinity,
              fit: BoxFit.cover,
              AppAssets.backgroundHomeSvg,
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            top: 100,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SizedBox(
                width: double.infinity,
                child: ShortcutIcons(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
