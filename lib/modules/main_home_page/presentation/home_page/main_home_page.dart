import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/constants/app_radius.dart';
import 'package:multime_app/modules/main_home_page/data.dart';
import 'package:multime_app/modules/main_home_page/presentation/widgets/icon_home_page.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_feature_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/card/card_suggest_service.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/invite_friends.dart';
import 'package:multime_app/shared/widgets/list_card/List_view_generic.dart';

class MainHomePage extends StatefulWidget {
  const MainHomePage({super.key});

  @override
  State<MainHomePage> createState() => _MainHomePageState();
}

class _MainHomePageState extends State<MainHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            IconHomePage(),
            // Gap(20.h),
            Padding(
              padding: const EdgeInsets.all(16),
              child: InviteFriends(),
            ),
            Gap(16.h),
            ListViewGeneric<ProductModel>(
                isLoading: false,
                isSuccess: true,
                items: productList,
                onItemTap: (product) {},
                //  =>
                //     context.push(RouteName.productDetailScreen, extra: product),
                showMore: false,
                title: 'Comprehensive Healthcare',
                itemBuilder: (context, product, index) =>
                    CardFeatureProduct(product: product),
                isShowButton: true,
                isListHome: true,
                filterType: FilterType.service,
                onPressed: () => context
                    .push(
                      RouteName.allSuggestService,
                    )
                    .then(
                      (_) => context.read<HomeStrongbodyAiBloc>().add(
                          FilterEvent(
                              filterType: FilterType.service,
                              typeReset: 'reset')),
                    )),
            Gap(16.h),
            ListViewGeneric<ServiceModel>(
                isLoading: false,
                isSuccess: true,
                items: serviceList,
                onItemTap: (service) {},
                showMore: false,
                title: 'Recommended for you',
                itemBuilder: (context, service, index) =>
                    CardSuggestService(service: service),
                isShowButton: true,
                isListHome: true,
                filterType: FilterType.service,
                onPressed: () => context
                    .push(
                      RouteName.allSuggestService,
                    )
                    .then(
                      (_) => context.read<HomeStrongbodyAiBloc>().add(
                          FilterEvent(
                              filterType: FilterType.service,
                              typeReset: 'reset')),
                    )),
            Gap(16.h),
            ListViewGeneric<String>(
                isLoading: false,
                isSuccess: true,
                items: bannerImages,
                showMore: false,
                title: 'It start with a swipe',
                height: 200,
                itemBuilder: (context, imageUrl, index) => ClipRRect(
                      borderRadius: BorderRadius.circular(AppRadius.radius10),
                      child: Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        width: 300.w,
                      ),
                    ),
                isShowButton: false,
                isListHome: true,
                filterType: FilterType.product,
                onPressed: () {}),
            Gap(16.h),
            ListViewGeneric<ServiceModel>(
                isLoading: false,
                isSuccess: true,
                items: serviceList,
                onItemTap: (service) {},
                showMore: false,
                title: 'Top News of the Day',
                itemBuilder: (context, service, index) =>
                    CardSuggestService(service: service),
                isShowButton: true,
                isListHome: true,
                filterType: FilterType.service,
                onPressed: () => context
                    .push(
                      RouteName.allSuggestService,
                    )
                    .then(
                      (_) => context.read<HomeStrongbodyAiBloc>().add(
                          FilterEvent(
                              filterType: FilterType.service,
                              typeReset: 'reset')),
                    )),
            Gap(16.h),
            ListViewGeneric<String>(
                isLoading: false,
                isSuccess: true,
                items: bannerImages,
                showMore: false,
                title: 'Voice Hub and more stories',
                height: 200,
                itemBuilder: (context, imageUrl, index) => ClipRRect(
                      borderRadius: BorderRadius.circular(AppRadius.radius10),
                      child: Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        width: 300.w,
                      ),
                    ),
                isShowButton: false,
                isListHome: true,
                filterType: FilterType.product,
                onPressed: () {}),
            Gap(16.h),
            ListViewGeneric<String>(
                isLoading: false,
                isSuccess: true,
                items: bannerImages,
                showMore: false,
                title: 'More deals from Multime',
                height: 250,
                itemBuilder: (context, imageUrl, index) => ClipRRect(
                      borderRadius: BorderRadius.circular(AppRadius.radius10),
                      child: Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        width: 200.w,
                      ),
                    ),
                isShowButton: false,
                isListHome: true,
                filterType: FilterType.product,
                onPressed: () {}),
            Gap(16.h),
          ],
        ),
      ),
    );
  }
}
