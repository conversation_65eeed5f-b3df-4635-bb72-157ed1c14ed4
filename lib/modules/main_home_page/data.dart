import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/shared/models/user/user_model.dart';

final List<ServiceModel> serviceList = [
  // ServiceModel(
  //   id: 1,
  //   title: 'StrongBody PT',
  //   description: 'Personal training for strength and health.',
  //   rejectNotes: '',
  //   categoryId: 101,
  //   // category: Category(id: 101, name: 'Fitness'),
  //   userId: 1001,
  //   // user: UserModel(id: 1001, name: '<PERSON>', countryId: 84),
  //   address: '123 Main St, HCM City',
  //   views: 120,
  //   rating: 4.8,
  //   longitude: '106.700981',
  //   latitude: '10.776889',
  //   image: [
  //     'https://cdn.pixabay.com/photo/2018/08/04/11/30/draw-3583548_1280.png',
  //     'https://cdn.pixabay.com/photo/2019/10/23/20/40/landscape-4572804_1280.jpg',
  //   ],
  //   coverImage: [
  //     'https://cdn.pixabay.com/photo/2018/08/04/11/30/draw-3583548_1280.png'
  //   ],
  //   price: 500000,
  //   type: 'Online',
  //   deliverables: 'Workout plan, diet guide',
  //   deliveryLeadTimeDay: 2,
  //   goLiveDate: DateTime.now().subtract(Duration(days: 10)),
  //   slug: 'strongbody-pt',
  //   serviceDeliveryMethod: 'Video call',
  //   shopId: 1,
  //   shop: null,
  // ),
  // ServiceModel(
  //   id: 2,
  //   title: 'Yoga Class',
  //   description: 'Group yoga sessions for all levels.',
  //   rejectNotes: '',
  //   categoryId: 102,
  //   // category: Category(id: 102, name: 'Yoga'),
  //   userId: 1002,
  //   // user: UserModel(id: 1002, name: 'Jane Smith', countryId: 84),
  //   address: '456 Yoga St, HN City',
  //   views: 80,
  //   rating: 4.5,
  //   longitude: '105.854444',
  //   latitude: '21.028511',
  //   image: [
  //     'https://cdn.pixabay.com/photo/2018/08/04/11/30/draw-3583548_1280.png',
  //     'https://cdn.pixabay.com/photo/2019/10/23/20/40/landscape-4572804_1280.jpg',
  //   ],
  //   coverImage: [
  //     'https://cdn.pixabay.com/photo/2018/08/04/11/30/draw-3583548_1280.png'
  //   ],
  //   price: 300000,
  //   type: 'Offline',
  //   deliverables: 'Yoga mat, water bottle',
  //   deliveryLeadTimeDay: 1,
  //   goLiveDate: DateTime.now().subtract(Duration(days: 5)),
  //   slug: 'yoga-class',
  //   serviceDeliveryMethod: 'In-person',
  //   shopId: 1,
  //   shop: null,
  // ),
  // Thêm các service khác nếu cần
];

final List<ProductModel> productList = [
  ProductModel(
    id: 1,
    name: 'Yoga Mat',
    description: 'High-quality yoga mat for all levels.',
    sku: 'YM-001',
    categoryId: 201,
    userId: 1001,
    user: UserModel(id: 1001, firstName: 'John Doe', countryId: 84),
    price: 200000,
    brandtext: 'StrongBody',
    brandId: 301,
    // brand: BrandModel(id: 301, name: 'StrongBody'),
    isActive: true,
    expiryDate: DateTime.now().add(Duration(days: 365)),
    manufactureDate: DateTime.now().subtract(Duration(days: 30)),
    image: [
      'https://hoanghamobile.com/tin-tuc/wp-content/uploads/2024/11/tai-hinh-nen-dep-mien-phi.jpg',
    ],
    countryOfOrigin: 'Vietnam',
    licenseNo: 'VN-123456',
    licenseFile: null,
    pricingTiers: [
      PricingTier(
        id: 1,
        productId: 1,
        price: 200000,
        description: 'Standard',
        unit: 'pcs',
        stockQuantity: 50,
      ),
      PricingTier(
        id: 2,
        productId: 1,
        price: 250000,
        description: 'Premium',
        unit: 'pcs',
        stockQuantity: 50,
      ),
    ],
    createdAt: DateTime.now().subtract(Duration(days: 10)),
    createdBy: 'admin',
    shopId: 1,
    // shop: null,
    // shop: ShopModel(id: 1, shopName: 'Demo Shop', userId: 1001),
    // categoryId: 101,
  ),
  ProductModel(
    id: 2,
    name: 'Dumbbells Set',
    description: 'Adjustable dumbbells for home workouts.',
    sku: 'DB-002',
    categoryId: 202,
    userId: 1002,
    user: UserModel(id: 1002, firstName: 'Jane Smith', countryId: 84),
    price: 800000,
    brandtext: 'FitPro',
    brandId: 302,
    // brand: BrandModel(id: 302, name: 'FitPro'),
    isActive: true,
    expiryDate: DateTime.now().add(Duration(days: 730)),
    manufactureDate: DateTime.now().subtract(Duration(days: 60)),
    image: [
      'https://hoanghamobile.com/tin-tuc/wp-content/uploads/2024/11/tai-hinh-nen-dep-mien-phi.jpg',
    ],
    countryOfOrigin: 'China',
    licenseNo: 'CN-654321',
    licenseFile: 'https://example.com/license.pdf',
    pricingTiers: [
      PricingTier(
          id: 1,
          productId: 2,
          price: 800000,
          description: 'Standard',
          unit: 'pcs',
          stockQuantity: 50),
      PricingTier(
          id: 2,
          productId: 2,
          price: 950000,
          description: 'Pro',
          unit: 'pcs',
          stockQuantity: 50),
    ],
    createdAt: DateTime.now().subtract(Duration(days: 20)),
    createdBy: 'admin',
    shopId: 1,
    // shop: ShopModel(id: 1, shopName: 'Demo Shop', userId: 1001),
    // category: CategoryModel(id: 202, name: 'Fitness Equipment'),
  ),
  // Thêm các sản phẩm khác nếu cần
];

final List<String> bannerImages = [
  'https://hoanghamobile.com/tin-tuc/wp-content/uploads/2024/11/tai-hinh-nen-dep-mien-phi.jpg',
  'https://hoanghamobile.com/tin-tuc/wp-content/uploads/2024/11/tai-hinh-nen-dep-mien-phi-1.jpg',
  'https://hoanghamobile.com/tin-tuc/wp-content/uploads/2024/11/tai-hinh-nen-dep-mien-phi-2.jpg',
];
