// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_dev.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDGPwiuEgwMeJVnxtk_YjXuG9JXKYTAxck',
    appId: '1:382263610736:android:1bdfa80111e8ae56f213b5',
    messagingSenderId: '382263610736',
    projectId: 'multime23102024',
    storageBucket: 'multime23102024.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCsP8CHuyEMICkDk1TUXFu1s2bGTmsGJ24',
    appId: '1:382263610736:ios:d9490372e18ac4c7f213b5',
    messagingSenderId: '382263610736',
    projectId: 'multime23102024',
    storageBucket: 'multime23102024.firebasestorage.app',
    androidClientId:
        '382263610736-c1p103b9t6j0ffsa0spf18i5qklmkdnl.apps.googleusercontent.com',
    iosClientId:
        '382263610736-86b2gkrviep4q9n09o4d6bkbaq49fa4s.apps.googleusercontent.com',
    iosBundleId: 'com.multime.app.dev',
  );
}
