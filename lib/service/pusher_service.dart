import 'dart:convert';

import 'package:multime_app/core/config/environment.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';

class PusherService {
  static PusherChannelsFlutter? _pusher;
  static bool _isInitialized = false;

  static String _appkey = AppConfig.pusherKey;
  static String _cluster = AppConfig.pusherCluster;

  // ✅ Track channel đã subscribe
  static final Set<String> _subscribedChannels = {};

  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _pusher = PusherChannelsFlutter.getInstance();
      await _pusher!.init(
        apiKey: _appkey,
        cluster: _cluster,
        onConnectionStateChange: (currentState, previousState) {
          print('Pusher Connection: $previousState -> $currentState');
        },
        onError: (message, code, error) {
          print('Pusher Error: $message (Code: $code)');
        },
      );
      await _pusher!.connect();
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize Pusher: $e');
    }
  }

  static Future<void> subscribeToOrderChannel(
      int customerId, Function(dynamic) onUpdate) async {
    if (!_isInitialized) await initialize();

    final channelName = 'user-$customerId';

    // ✅ Nếu đã subscribe → bỏ qua. Đánh dấu trước để tránh race-condition subscribe song song
    if (_subscribedChannels.contains(channelName)) {
      print("⚠️ Already subscribed to $channelName → skip");
      return;
    }
    _subscribedChannels.add(channelName);

    try {
      await _pusher!.subscribe(
        channelName: channelName,
        onEvent: (event) {
          print('Pusher Event Received: ${event.eventName} - ${event.data}');
          if (event.eventName == 'order_paid') {
            try {
              final data = jsonDecode(event.data);
              final update = OrderModelUpdate.fromJson(data);
              onUpdate(update);
            } catch (e) {
              print('❌ Error parsing order update: $e');
            }
          } else if (event.eventName == 'offer_paid') {
            try {
              final data = jsonDecode(event.data);
              final update = OrderServiceModelUpdate.fromJson(data);
              onUpdate(update);
            } catch (e) {
              print('❌ Error parsing offer update: $e');
            }
          } else {
            print('Unknown event: ${event.eventName}');
          }
        },
      );
      print('✅ Subscribed to channel: $channelName');
    } catch (e) {
      _subscribedChannels.remove(channelName);
      print('❌ Failed to subscribe $channelName: $e');
      rethrow;
    }
  }

  static Future<void> unsubscribeFromOrderChannel(int customerId) async {
    if (!_isInitialized) return;

    final channelName = 'user-$customerId';
    await _pusher!.unsubscribe(channelName: channelName);
    _subscribedChannels.remove(channelName);
    print('🔌 Unsubscribed from channel: $channelName');
  }

  static Future<void> disconnect() async {
    if (!_isInitialized) return;

    await _pusher!.disconnect();
    _subscribedChannels.clear();
    _isInitialized = false;
    print('Pusher disconnected');
  }
}

class OrderModelUpdate {
  final double amount;
  final int customer_id;
  final String message;
  final int order_id;
  final String order_type;
  final String pay_status;
  final String order_no;
  final String order_status;
  OrderModelUpdate({
    required this.amount,
    required this.customer_id,
    required this.message,
    required this.order_id,
    required this.order_type,
    required this.pay_status,
    required this.order_no,
    required this.order_status,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'customer_id': customer_id,
      'message': message,
      'order_id': order_id,
      'order_type': order_type,
      'pay_status': pay_status,
      'order_no': order_no,
      'order_status': order_status,
    };
  }

  factory OrderModelUpdate.fromJson(Map<String, dynamic> json) {
    return OrderModelUpdate(
      amount: (json['total'] as num).toDouble(),
      customer_id: json['customer_id'] as int,
      message: json['message'] as String,
      order_id: json['order_id'] as int,
      order_type: json['order_type'] as String,
      pay_status: json['pay_status'] as String,
      order_no: json['order_no'] as String,
      order_status: json['order_status'] as String,
    );
  }

  bool get isPaymentSuccessful => pay_status == 'paid';
  bool get isPaymentFailed => pay_status == 'failed';
}

class OrderServiceModelUpdate {
  final double amount;
  final int customer_id;
  final String message;
  final int offer_id;
  final String pay_status;
  final String order_no;
  final String order_status;
  final String order_type;

  OrderServiceModelUpdate({
    required this.amount,
    required this.customer_id,
    required this.message,
    required this.offer_id,
    required this.pay_status,
    required this.order_no,
    required this.order_status,
    required this.order_type,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'customer_id': customer_id,
      'message': message,
      'offer_id': offer_id,
      'pay_status': pay_status,
      'order_no': order_no,
      'order_status': order_status,
      'order_type': order_type,
    };
  }

  factory OrderServiceModelUpdate.fromJson(Map<String, dynamic> json) {
    return OrderServiceModelUpdate(
      amount: (json['price'] as num?)?.toDouble() ?? 0.0,
      customer_id: json['customer_id'] as int? ?? 0,
      message: json['message'] as String? ?? '',
      offer_id: json['offer_id'] as int? ?? 0,
      pay_status: json['pay_status'] as String? ?? '',
      order_no: json['order_no'] as String? ?? '',
      order_status: json['order_status'] as String? ?? '',
      order_type: json['order_type'] as String? ?? '',
    );
  }

  bool get isPaymentSuccessful => pay_status == 'paid';
  bool get isPaymentFailed => pay_status == 'failed';
}
