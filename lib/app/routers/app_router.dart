part of 'route_imports.dart';

class AppRoutes {
  static final GoRouter router = GoRouter(
    initialLocation: RouteName.rootScreen,
    routes: [
      // ------------------------MODEL---------------------
      GoRoute(
          path: RouteName.rootScreen,
          builder: (context, state) {
            return BlocProvider(
                create: (context) => CountryBloc(
                    countryService: getIt<CountryService>(),
                    globalStorage: getIt<GlobalStorage>()),
                child: const SplashScreen());
          }),
      GoRoute(
        path: RouteName.selectionPage,
        builder: (context, state) => SelectionPage(),
      ),
      GoRoute(
        path: RouteName.privacyPolicyPage,
        name: RouteName.privacyPolicyPage,
        builder: (context, state) => PrivacyPolicyPage(),
      ),
      GoRoute(
        path: RouteName.socialMode,
        builder: (context, state) => const SocialHomePage(),
      ),
      GoRoute(
        path: RouteName.businesMode,
        builder: (context, state) {
          return BlocProvider(
            create: (_) => FindJobBloc(),
            child: const FindJobPage(),
          );
        },
      ),
      GoRoute(
        path: RouteName.newsMode,
        builder: (context, state) => const HomeNewScreen(),
      ),
      ShellRoute(
        builder: (context, state, child) {
          return MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) =>
                    MarketplaceHomeBloc(getIt<ProductRepositoryRemote>())
                      ..add(const MarketplaceGetFeaturedProducts()),
              ),
              BlocProvider(
                  create: (context) =>
                      LikedBloc(getIt<ProductRepositoryRemote>())
                        ..add(const GetLikeProducts())),
            ],
            child: Scaffold(
              body: child,
            ),
          );
        },
        routes: [
          GoRoute(
            path: RouteName.marketMode,
            builder: (context, state) {
              return const MarketPage();
            },
          ),
          GoRoute(
            path: RouteName.likedScreen,
            builder: (context, state) => const LikedPage(),
          ),
        ],
      ),
      GoRoute(
        path: RouteName.datingMode,
        builder: (context, state) => const DatingModePage(),
      ),
      // -----------------STRONGBODY--------------------

      ShellRoute(
          builder: (context, state, child) {
            return BlocProvider(
              create: (context) => SearHomeStrongBloc(
                servicesRepository: getIt<HomeStrongbodyAiRepository>(),
              ),
              child: Scaffold(
                body: child,
              ),
            );
          },
          routes: [
            GoRoute(
              path: RouteName.searchHomeStrongBody,
              name: RouteName.searchHomeStrongBody,
              pageBuilder: (context, state) => CustomTransitionPage(
                key: state.pageKey,
                transitionDuration: const Duration(milliseconds: 1000),
                child: const SearchTextFileStrongBodyPage(),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  final curved = CurvedAnimation(
                      parent: animation, curve: Curves.easeInOut);
                  return FadeTransition(
                    opacity: curved,
                    child: SlideTransition(
                      position: Tween<Offset>(
                              begin: const Offset(0, 0.05), end: Offset.zero)
                          .animate(curved),
                      child: child,
                    ),
                  );
                },
              ),
            ),
            GoRoute(
              path: RouteName.searchFilterStrongBody,
              name: RouteName.searchFilterStrongBody,
              pageBuilder: (context, state) => CustomTransitionPage(
                key: state.pageKey,
                barrierDismissible: true,
                opaque: false,
                transitionDuration: const Duration(milliseconds: 1000),
                barrierColor: Colors.black.withOpacity(0.15),
                child: const SearchFilterStrongBodyPage(),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  final curved = CurvedAnimation(
                      parent: animation, curve: Curves.easeInOut);
                  return FadeTransition(
                    opacity: curved,
                    child: SlideTransition(
                      position: Tween<Offset>(
                              begin: const Offset(0, 0.05), end: Offset.zero)
                          .animate(curved),
                      child: child,
                    ),
                  );
                },
              ),
            ),
          ]),
      GoRoute(
        path: RouteName.detailProductPage,
        name: RouteName.detailProductPage,
        builder: (context, state) {
          final product = state.extra as ProductModel;
          return BlocProvider(
            create: (context) => DetailProductBloc(
              homeStrongbodyAiRepository: getIt<HomeStrongbodyAiRepository>(),
            )
              ..add(FetchProductLikeEvent(
                categoryId: product.categoryId,
              ))
              ..add(FetchProductTrendingEvent()),
            child: DetailProductPage(productModel: product),
          );
        },
      ),
      GoRoute(
        path: RouteName.productDetailSeller,
        name: RouteName.productDetailSeller,
        builder: (context, state) {
          final productId = state.extra as int?;
          if (productId == null) {
            // Return error page or redirect
            return Scaffold(
              appBar: AppBar(title: Text('Error')),
              body: Center(child: Text('Product ID is required')),
            );
          }
          return BlocProvider(
            create: (context) => DetailProductSellerBloc(
              homeStrongbodyAiRepository: getIt<HomeStrongbodyAiRepository>(),
              profileSellerRepoRemote: getIt<ProfileSellerRepoRemote>(),
            )
              ..add(FetchDetailProductSeller(productId: productId))
              ..add(FetchProductLike(categoryId: 0))
              ..add(FetchProductTrending()),
            child: DetailProductSeller(),
          );
        },
      ),

      GoRoute(
        path: RouteName.detailBlogPage,
        name: RouteName.detailBlogPage,
        builder: (context, state) {
          final blog = state.extra as BlogModel;
          return BlocProvider(
            create: (context) => DetailBlogBloc(
              homeStrongbodyAiRepository: getIt<HomeStrongbodyAiRepository>(),
            )
              ..add(FetchDetailBlogLikeEvent(categoryId: blog.categoryId))
              ..add(FetchDetailBlogTrendingEvent()),
            child: DetailBlogPage(blog: blog),
          );
        },
      ),

      GoRoute(
          path: RouteName.postBuyerRequest,
          // name: RouteName.postRequiredHome,
          builder: (context, state) {
            return BlocProvider(
              create: (context) => PostBuyerRequestBloc(
                getIt<BuyerRequestRepository>(),
                getIt<CategoryRepositoryRemote>(),
              )..add(GetCategoriesBuyerRequest()),
              child: const PostRequestPage(),
            );
          }),
      GoRoute(
        path: RouteName.postBuyerRequestCheck,
        builder: (context, state) {
          final extra = state.extra as bool;
          return PostRequestCheck(
            checkSuccess: extra,
          );
        },
      ),
      GoRoute(
          path: RouteName.buyerRequestDetail,
          // name: RouteName.postRequiredHome,
          builder: (context, state) {
            final reqId = state.extra as String;

            return BlocProvider(
              create: (context) =>
                  RequestDetailBloc(getIt<BuyerRequestRepository>())
                    ..add(GetRequestDetailEvent(reqId: reqId)),
              child: const RequestDetailScreen(),
            );
          }),
      GoRoute(
          path: RouteName.viewAllBuyerRequest,
          // name: RouteName.postRequiredHome,
          builder: (context, state) {
            return BlocProvider(
              create: (context) =>
                  ViewAllBuyerRequestBloc(getIt<BuyerRequestRepository>())
                    ..add(const GetAllBuyerRequestEvent()),
              child: const ViewAllBuyerRequestScreen(),
            );
          }),

      // -----------------SETTING --------------------
      GoRoute(
        path: RouteName.settingAccount,
        builder: (context, state) => const SettingAccountPage(),
      ),
      GoRoute(
        path: RouteName.settingMode,
        builder: (context, state) => const SettingModelPage(),
      ),
      GoRoute(
        path: RouteName.settingPage,
        builder: (context, state) => SettingPage(),
      ),
      GoRoute(
        path: RouteName.settingDatingMode,
        builder: (context, state) => const SettingDatingMode(),
      ),
      GoRoute(
          path: RouteName.settingNewMode,
          builder: (context, state) {
            return const SettingNewMode();
          }),
      GoRoute(
          path: RouteName.settingSocialMode,
          builder: (context, state) {
            return const SettingSocialMode();
          }),
      GoRoute(
        path: RouteName.settingBusinessMode,
        builder: (context, state) => const SettingBusinessMode(),
      ),
      GoRoute(
        path: RouteName.settingMarketplanceMode,
        builder: (context, state) => const SettingMarketplaceMode(),
      ),
      GoRoute(
        path: RouteName.accountDeactivationPage,
        builder: (context, state) => const AccountDeactivationScreen(),
      ),
      GoRoute(
        path: RouteName.changePasswordPage,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => getIt<ChangePasswordBloc>(),
            child: const ChangePasswordPage(),
          );
        },
      ),
      GoRoute(
        path: RouteName.settingVerifyAccount,
        builder: (context, state) => const VerifyAccountPage(),
      ),
      GoRoute(
        path: RouteName.syncSetting,
        builder: (context, state) => const SettingSyncPage(),
      ),
      // ------------------BUSINES-----------------
      GoRoute(
        path: RouteName.businesHome,
        builder: (context, state) => const BusinesHomePage(),
      ),
      GoRoute(
        path: RouteName.businesOverView,
        builder: (context, state) => const OverViewBusinesPage(),
      ),
      // -------------------MARKETPLACE-----------------------------------
      GoRoute(
          path: RouteName.productDetailScreen,
          builder: (context, state) {
            String? pid = state.extra as String;

            return BlocProvider(
              create: (context) =>
                  ProductDetailBloc(getIt<ProductRepositoryRemote>())
                    ..add(GetProductApiEvent(pid: pid))
                    ..add(GetSimilarProductApiEvent(pid: pid)),
              child: const ProductDetailsScreen(),
            );
          }),
      GoRoute(
        path: RouteName.managePost,
        builder: (context, state) => const ManageBottomBar(),
      ),
      GoRoute(
        path: RouteName.productGridScreen,
        builder: (context, state) {
          final section = state.extra as Map<String, dynamic>;
          return BlocProvider(
            create: (context) =>
                ProductGridBloc(getIt<ProductRepositoryRemote>())
                  ..add(GetSeeMoreProductEvent(section: section['section']))
                  ..add(UpdateSectionTitle(title: section['title'])),
            child: ProductGridScreen(
              section: section['section'],
              title: section['title'],
            ),
          );
        },
      ),
      // GoRoute(
      //   path: RouteName.orderScreen,
      //   builder: (context, state) => OrderPage(),
      // ),
      GoRoute(
        path: RouteName.likedScreen,
        builder: (context, state) => BlocProvider(
          create: (context) => LikedBloc(getIt<ProductRepositoryRemote>())
            ..add(const GetLikeProducts()),
          child: const LikedPage(),
        ),
      ),
      GoRoute(
        path: RouteName.postProductScreen,
        builder: (context, state) => BlocProvider(
          create: (_) => PostProductBloc(getIt<CategoryRepositoryRemote>(),
              getIt<ProductRepositoryRemote>())
            ..add(const GetCategoriesEvent()),
          child: const PostProductPage(),
        ),
      ),
      // GoRoute(
      //   path: RouteName.orderDetailsScreen,
      //   builder: (context, state) => OrderDetailsScreen(),
      // ),
      GoRoute(
        path: RouteName.orderComplainsScreen,
        builder: (context, state) => const OrderSupportPage(),
      ),
      GoRoute(
        path: RouteName.marketplaceSearch,
        builder: (context, state) => const MarketplaceSearchPage(),
      ),
      ShellRoute(
        builder: (context, state, child) {
          return MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) =>
                    AddressBloc(addressRepository: getIt<AddressRepository>())
                      ..add(const GetAddressEvent()),
              ),
              BlocProvider(
                create: (context) =>
                    PaymentBloc(paymentRepository: getIt<PaymentRepository>()),
              ),
            ],
            child: Scaffold(
              body: child,
            ),
          );
        },
        routes: [
          GoRoute(
            path: RouteName.addressMarketplace,
            builder: (context, state) => const AddressMarketplace(),
          ),
          GoRoute(
              path: RouteName.checkoutScreen,
              builder: (context, state) => const CheckoutScreen()),
          GoRoute(
            path: RouteName.confirmOrderScreen,
            builder: (context, state) {
              var product = state.extra as Map<String, dynamic>?;
              if (product == null) {
                return const Scaffold(
                  body: Center(
                    child: Text('Product information not found'),
                  ),
                );
              }
              return ConfirmOrderScreen(
                product: product['product'] as ProductDetail,
              );
            },
          ),
          GoRoute(
            path: RouteName.personalInformationPage,
            builder: (context, state) {
              final arguments = state.extra as Map<String, dynamic>? ?? {};
              final bool isCheck = arguments['isCheck'] as bool? ?? false;
              final address = arguments['address'] as Address?;

              return BlocProvider(
                create: (_) =>
                    AddressBloc(addressRepository: getIt<AddressRepository>()),
                child: AddressEditInfoPage(
                  isCheckUpdate: isCheck,
                  stateAddress: address,
                ),
              );
            },
          )
        ],
      ),

      // ------------------Dating--------------------
      GoRoute(
        path: RouteName.datingHome,
        builder: (context, state) => const HomeDatingPage(),
      ),
      GoRoute(
        path: RouteName.datingPage,
        builder: (context, state) {
          return const DatingPage();
        },
      ),
      GoRoute(
        path: RouteName.editSeekingPage,
        builder: (context, state) {
          return BlocProvider(
            create: (_) => FindJobBloc(),
            child: const EditSeekingPage(),
          );
        },
      ),
      GoRoute(
        path: RouteName.editBioPage,
        builder: (context, state) => const UpdateBioDatingPage(),
      ),
      GoRoute(
        path: RouteName.myProductPage,
        builder: (context, state) {
          return const MyProductPage();
        },
      ),
      // GoRoute(
      //     path: RouteName.addPhotoPage,
      //     builder: (context, state) {
      //       final arguments = state.extra as Map<String, dynamic>;
      //       final int index = arguments['index'] as int;
      //       return BlocProvider(
      //           create: (_) => ImageCubit(), child: AddPhoto(index: index));
      //     }),
      ShellRoute(
          builder: (context, state, child) {
            return BlocProvider(
              create: (_) => InfoDatingBloc(),
              child: child,
            );
          },
          routes: [
            GoRoute(
              path: RouteName.inforPage,
              builder: (context, state) {
                return InforDatingPage();
              },
            ),
            GoRoute(
                path: RouteName.hobbiesPage,
                name: RouteName.hobbiesPage,
                builder: (context, state) {
                  return HobbiesPage();
                }),
          ]),
      // ------------------New--------------------
      GoRoute(
        path: RouteName.homeNew,
        builder: (context, state) => BlocProvider(
            create: (_) => FindJobBloc(), child: const HomeNewScreen()),
      ),
      GoRoute(
        path: RouteName.postNew,
        builder: (context, state) {
          return BlocProvider(
            create: (_) => PostNewsBloc(),
            child: const PostRequestNews(),
          );
        },
      ),
      GoRoute(
        path: RouteName.likedNew,
        builder: (context, state) => const LikedNews(),
      ),
      GoRoute(
        path: RouteName.reviewNew,
        builder: (context, state) => const ReviewNews(),
      ),
      GoRoute(
        path: RouteName.SearchNewScreen,
        builder: (context, state) => const SearchNewScreen(),
      ),
      // ------------------Social----------------------
      GoRoute(
        path: RouteName.homeSocial,
        builder: (context, state) => const SocialHomePage(),
      ),
      GoRoute(
        path: RouteName.socialPage,
        builder: (context, state) {
          return const SocialPage();
        },
      ),
      GoRoute(
        path: RouteName.createPostRecord,
        builder: (context, state) {
          final totalDuration =
              state.extra as int; // Nhận giá trị `reson` từ `extra`
          final recordedFilePath =
              state.extra as String; // Nhận giá trị `reson` từ `extra`.
          return CreatePostRecord(
            totalDuration: totalDuration,
            recordedFilePath: recordedFilePath,
            record: true,
          );
        },
      ),
      GoRoute(
        path: RouteName.socialAiAssistant,
        builder: (context, state) => const SocialAiAssistant(),
      ),
      GoRoute(
        path: RouteName.checkIn,
        builder: (context, state) => const SocialCheckIn(),
      ),
      GoRoute(
        path: RouteName.record,
        builder: (context, state) => SocialRecord(),
      ),
      GoRoute(
        path: RouteName.accessLocation,
        builder: (context, state) => const AccessLocation(),
      ),
      GoRoute(
        path: RouteName.homeSocialPost,
        builder: (context, state) => const CreatePost(),
      ),
      GoRoute(
        path: RouteName.profileSocial,
        builder: (context, state) => const SocialEditProfile(),
      ),
      GoRoute(
        path: RouteName.newPage,
        builder: (context, state) {
          return const NewPage();
        },
      ),

      GoRoute(
        path: RouteName.loginPage,
        builder: (context, state) => BlocProvider(
          create: (context) => LoginBloc(
            storage: getIt<GlobalStorage>(),
            authRepository: getIt<AuthRepository>(),
            chatRepository: getIt<ChatRepository>(),
          ),
          child: LoginPage(),
        ),
      ),
      GoRoute(
        path: RouteName.loginRedirect,
        name: RouteName.loginRedirect,
        builder: (context, state) => const LoginRedirect(),
      ),
      GoRoute(
        path: RouteName.forgotPasswordPage,
        builder: (context, state) => const ForgetPasswordPage(),
      ),
      GoRoute(
        path: RouteName.registerPage,
        builder: (context, state) => BlocProvider(
          create: (_) => RegisterBloc(authRepository: getIt<AuthRepository>()),
          child: const RegisterScreen(),
        ),
      ),

      GoRoute(
        path: RouteName.otpPage,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final resendOtp = extra['resendOtp'] as bool;
          return BlocProvider(
            create: (_) => OtpBloc(
              authRepository: getIt<AuthRepository>(),
              flowType: extra['flowType'] as OtpFlowType,
              userData: extra['userData'] as Map<String, dynamic>?,
              email: extra['email'] as String,
              storage: getIt<GlobalStorage>(),
            ),
            child: OTPScreen(resendOtp: resendOtp),
          );
        },
      ),
      // GoRoute(
      //   path: RouteName.forgotPasswordPage,
      //   builder: (context, state) {
      //     return const ForgetPasswordPage();
      //   },
      // ),
      // GoRoute(
      //   path: RouteName.changePasswordPage,
      //   builder: (context, state) {
      //     return ChangePasswordPage();GeneralSetting
      //   },
      // ),
      GoRoute(
        path: RouteName.createPasswordPage,
        builder: (context, state) {
          var currentPassword = state.extra! as String?;
          return BlocProvider(
            create: (context) => SetPasswordBloc(
              authRepository: getIt<AuthRepository>(),
              globalStorage: getIt<GlobalStorage>(),
            ),
            child: CreatePasswordPage(currentPassword: currentPassword),
          );
        },
      ),

      GoRoute(
        path: RouteName.createForgotPasswordPage,
        builder: (context, state) {
          var secretToken = state.extra as String;
          return BlocProvider(
            create: (context) => SetPasswordBloc(
              authRepository: getIt<AuthRepository>(),
              globalStorage: getIt<GlobalStorage>(),
            ),
            child: CreateForgotPasswordPage(
              secretToken: secretToken,
            ),
          );
        },
      ),
      ShellRoute(
        builder: (context, state, child) {
          return BlocProvider(
            create: (context) => CompleteUserRegistrationBloc(),
            child: Scaffold(
              body: child,
            ),
          );
        },
        routes: [
          GoRoute(
            path: RouteName.contactImport,
            builder: (context, state) => const ContactImportScreen(),
          ),
          GoRoute(
            path: RouteName.contactConnect,
            builder: (context, state) => const ContactConnectScreen(),
          ),
        ],
      ),
      GoRoute(
          path: RouteName.connectFriendPage,
          builder: (context, state) {
            return ConnectFriendPage();
          }),
      GoRoute(
          path: RouteName.bussinesBottomSheet,
          builder: (context, state) {
            return const BusinesBottomSheetItem();
          }),
      GoRoute(
          path: RouteName.recentDatingnPage,
          builder: (context, state) => const RecentDatingnPage()),
      GoRoute(
          path: RouteName.likedYouScreen,
          builder: (context, state) => const LikedYouScreen()),
      GoRoute(
          path: RouteName.matchesDatingPage,
          builder: (context, state) => const MatchesDatingPage()),
      GoRoute(
          path: RouteName.seekingScreen,
          builder: (context, state) {
            return BlocProvider(
              create: (_) => FindJobBloc(),
              child: const SeekingScreen(),
            );
          }),
      GoRoute(
          path: RouteName.matchesSuccessPage,
          builder: (context, state) => const MatchesSuccessPage()),
      GoRoute(
          path: RouteName.paymentSuccessPage,
          builder: (context, state) => const PaymentSuccessPage()),
      GoRoute(
          path: RouteName.addCardPage,
          builder: (context, state) => const AddCardPage()),
      GoRoute(
          path: RouteName.paymentFailPage,
          builder: (context, state) => const PaymentFailPage()),
      GoRoute(
          path: RouteName.orderSupportPage,
          builder: (context, state) => const OrderSupportPage()),
      GoRoute(
          path: RouteName.generalSetting,
          builder: (context, state) {
            return BlocProvider(
              create: (context) => UserBloc(
                authRepository: getIt<AuthRepository>(),
                globalStorage: getIt<GlobalStorage>(),
              ),
              child: const GeneralSetting(),
            );
          }),

      GoRoute(
          path: RouteName.settingPrivacyPage,
          builder: (context, state) => const SettingPrivacyPage()),
      GoRoute(
          path: RouteName.historySocialPage,
          builder: (context, state) => const History()),

      GoRoute(
        path: RouteName.success,
        builder: (context, state) {
          return const SuccessWidget();
        },
      ),

      // ------------------New Mode----------------------
      GoRoute(
        path: RouteName.NewsScreen,
        builder: (context, state) {
          final arguments = state.extra as Map<String, dynamic>;
          final userNew = arguments['userNew'];

          return NewsScreen(userNew: userNew);
        },
      ),
      GoRoute(
        path: RouteName.editNewPage,
        builder: (context, state) {
          final userNew = state.extra as UserNew;
          return EditNewScreen(userNew: userNew);
        },
      ),
      GoRoute(
        path: RouteName.aiAssistant,
        builder: (context, state) {
          return const AiAssistantPage();
        },
      ),
      GoRoute(
        path: RouteName.reportNew,
        builder: (context, state) {
          final reson =
              state.extra as String; // Nhận giá trị `reson` từ `extra`.
          return Report(reson: reson);
        },
      ),
// ------------------Shopping Cart----------------------
      GoRoute(
        path: RouteName.homeShoppingCart,
        name: RouteName.homeShoppingCart,
        builder: (context, state) {
          return BlocProvider(
            create: (_) => ShoppingCartBloc(),
            child: const HomeShoppingCartScreen(),
          );
        },
      ),
      GoRoute(
          path: RouteName.checkoutProductPage,
          name: RouteName.checkoutProductPage,
          builder: (context, state) {
            final data = state.extra as Map<String, dynamic>;

            final product = data['product'] as ProductModel;
            final quantity = data['quantity'] as int;
            final unit = data['unit'] as String;
            final total = data['total'] as double;
            return MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => CheckoutProductBloc(
                    getIt<CheckoutProductReponseRemote>(),
                  ),
                ),
                BlocProvider(
                  create: (context) =>
                      AddressBloc(addressRepository: getIt<AddressRepository>())
                        ..add(const GetAddressEvent()),
                ),
              ],
              child: CheckoutProductPage(
                productModel: product,
                quantity: quantity,
                unit: unit,
                total: total,
              ),
            );
          }),
      GoRoute(
        name: RouteName.paymentResultPage,
        path: RouteName.paymentResultPage,
        builder: (context, state) {
          final dataResult = state.extra as Map<String, dynamic>;
          final paymentResult =
              dataResult['paymentResult'] as PaymentResultData;
          final onPrimaryAction =
              dataResult['onPrimaryAction'] as VoidCallback?;
          final onSecondaryAction =
              dataResult['onSecondaryAction'] as VoidCallback?;
          return PaymentResultPage(
            data: paymentResult,
            onPrimaryAction: onPrimaryAction,
            onSecondaryAction: onSecondaryAction,
          );
        },
      ),

      GoRoute(
        path: RouteName.checkoutProduct,
        name: RouteName.checkoutProduct,
        builder: (context, state) {
          // Nhận dữ liệu từ state.extra
          final arguments = state.extra as Map<String, dynamic>;

          final total = arguments['total'];

          // Lấy dữ liệu danh sách các cửa hàng (shops)
          final List<Map<String, dynamic>> shopsData = arguments['shops'];

          // Chuyển dữ liệu từ Map<String, dynamic> sang List<Shop>
          final List<Shop> listShop = shopsData.map((shopData) {
            return Shop(
              shopName: shopData['shopName'],
              products: (shopData['products'] as List)
                  .map((productData) => Product(
                        productName: productData['productName'],
                        imageUrl: productData['imageUrl'],
                        oldPrice: productData['oldPrice'],
                        quantity: productData['quantity'],
                        price: productData['price'],
                      ))
                  .toList(),
            );
          }).toList();

          // Truyền listShop vào CheckoutProductScreen
          return BlocProvider(
            create: (context) => ShoppingCartBloc(),
            child: CheckoutProductScreen(
              total: total,
              shops: listShop,
            ),
          );
        },
      ),

      GoRoute(
          path: RouteName.orderSuccessScreen,
          builder: (context, state) {
            return const OrderSuccessScreen();
          }),
      GoRoute(
          path: RouteName.addressSelectionScreen,
          builder: (context, state) {
            return const AddressSelectionScreen();
          }),
      GoRoute(
          path: RouteName.editAddressSelectionScreen,
          builder: (context, state) {
            return const EditAddressSelectionScreen();
          }),
      // ------------------Profile View----------------------
      GoRoute(
        path: RouteName.profileView,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final sellerId = extra?['sellerId'] as int?;

          if (sellerId == null) {
            // Handle case where sellerId is null - redirect or show error
            return const Scaffold(
              body: Center(
                child: Text('Seller ID not found'),
              ),
            );
          }

          return BlocProvider(
            create: (context) => ProfileSellerBloc(
              getIt<ProfileSellerRepoRemote>(),
            )
              ..add(LoadProfileSellerData(sellerId, ProfileSellerTab.service))
              ..add(LoadProfileSeller(sellerId)),
            child: ProfileViewHome(profileId: sellerId),
          );
        },
      ),

      GoRoute(
          path: RouteName.searchSeller,
          builder: (context, state) {
            final extra = state.extra as Map<String, dynamic>?;
            final sellerId = extra?['sellerId'] as int?;

            if (sellerId == null) {
              // Handle case where sellerId is null - redirect or show error
              return const Scaffold(
                body: Center(
                  child: Text('Seller ID not found'),
                ),
              );
            }

            return BlocProvider(
              create: (context) => SearchSellerBloc(
                getIt<SearchSellerRepoRemote>(),
              ),
              child: SearchSeller(
                sellerId: sellerId,
              ),
            );
          }),

      // ------------------Help Center----------------------
      GoRoute(
          path: RouteName.helpCenter,
          builder: (context, state) => const HelpCenterHome()),
      GoRoute(
          path: RouteName.supportRequest,
          builder: (context, state) => const HelpCenterRequest()),
      GoRoute(
          path: RouteName.orderSupport,
          builder: (context, state) => const HelpCenterOrderSupport()),
      GoRoute(
        path: RouteName.helpCenterQuestion,
        builder: (context, state) {
          final selectedIndex = state.extra as int;
          return HelpCenterQuestion(selectedIndex: selectedIndex);
        },
      ),
      GoRoute(
          path: RouteName.helpSearch,
          builder: (context, state) => const HelpCenterSearch()),

      // ------------------Create Service----------------------
      ShellRoute(
          builder: (context, state, child) {
            return BlocProvider(
              create: (context) => HomeStrongbodyAiBloc(
                globalStorage: getIt<GlobalStorage>(),
                servicesRepository: getIt<HomeStrongbodyAiRepository>(),
              )
                ..add(FetchSuggestServiceEvent())
                ..add(FetchFeaturedProductEvent())
                ..add(FetchExpertEvent())
                ..add(FetchBlogEvent()),
              child: Scaffold(
                body: child,
              ),
            );
          },
          routes: [
            GoRoute(
              path: RouteName.homeStrongBody,
              builder: (context, state) {
                return const HomePage();
              },
            ),
            GoRoute(
                path: RouteName.allSuggestService,
                builder: (context, state) {
                  return AllSuggestService();
                }),
            GoRoute(
                path: RouteName.allFeaturedProducts,
                builder: (context, state) {
                  return AllFeaturedProducts();
                }),
            GoRoute(
                path: RouteName.allBlogs,
                builder: (context, state) {
                  return AllBlogsPage();
                }),
            GoRoute(
                path: RouteName.allExperts,
                builder: (context, state) {
                  return AllExpertsPage();
                }),
          ]),

      GoRoute(
          path: RouteName.createService,
          builder: (context, state) {
            return const CreateServiceHome();
          }),
      GoRoute(
          path: RouteName.checkoutService,
          name: RouteName.checkoutService,
          builder: (context, state) {
            final data = state.extra;
            return MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => CheckoutServiceBloc(
                    getIt<CheckoutServiceReponseRemoteImp>(),
                  ),
                ),
                BlocProvider(
                  create: (context) =>
                      AddressBloc(addressRepository: getIt<AddressRepository>())
                        ..add(const GetAddressEvent()),
                ),
              ],
              child: CheckoutServicePage(
                serviceModel: data as ServiceModel,
                quantity: 1,
                unit: "",
                total: data.price.toDouble(),
              ),
            );
          }),
      GoRoute(
          path: RouteName.serviceDetail,
          builder: (context, state) {
            final serviceModel = state.extra as ServiceModel;
            return BlocProvider(
              create: (context) => DetailServiceBloc(
                servicesRepository: getIt<HomeStrongbodyAiRepository>(),
              )
                ..add(
                    FetchServiceLikeEvent(categoryId: serviceModel.categoryId))
                ..add(FetchServiceTrendingEvent()),
              child: DetailServicePage(
                serviceModel: serviceModel,
              ),
            );
          }),

      GoRoute(
          path: RouteName.serviceDetailSeller,
          builder: (context, state) {
            final serviceId = state.extra as int;
            return BlocProvider(
              create: (context) => DetailServiceBloc(
                servicesRepository: getIt<HomeStrongbodyAiRepository>(),
              )..add(GetServiceDetailEvent(serviceId: serviceId)),
              child: DetailServiceSeller(),
            );
          }),

      // 'BALANCE' ROUTES
      GoRoute(
        path: RouteName.balancePage,
        builder: (context, state) => const BalancePage(),
      ),
      GoRoute(
        path: RouteName.withdrawPage,
        builder: (context, state) => const WithdrawPage(),
      ),
      GoRoute(
        path: RouteName.managePayout,
        builder: (context, state) => const ManagePayoutPage(),
      ),
      GoRoute(
        path: RouteName.addNewPayout,
        builder: (context, state) => const AddPayoutMethodPage(),
      ),
      GoRoute(
        path: RouteName.manageExistingPayout,
        builder: (context, state) => const ManagePayoutMethodPage(),
      ),
      GoRoute(
        path: RouteName.paymentHistory,
        builder: (context, state) => const PaymentHistoryPage(),
      ),
      GoRoute(
        path: RouteName.withdrawResult,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final amount = extra['amount'] as num;

          return WithdrawResultPage(status: 'success', amount: amount);
        },
      ),

      // ------------------User Home Page----------------------
      GoRoute(
          path: RouteName.homeUserPage,
          builder: (context, state) {
            return const HomeUserPage();
          }),

      // ------------------My Order----------------------
      GoRoute(
        path: RouteName.serviceReview,
        builder: (context, state) {
          final reson = state.extra
              as Map<String, dynamic>; // Nhận giá trị `reson` từ `extra`.
          return ReviewDetail(sellDetail: reson);
        },
      ),
      GoRoute(
          path: RouteName.serviceReviewSuccess,
          builder: (context, state) => const ReviewSuccess()),

      GoRoute(
        path: RouteName.orderServiceDetailPage,
        builder: (context, state) {
          final argument = state.extra as Map<String, dynamic>;
          final reson = argument['orderServiceModel'] as OrderServiceModel;
          final serviceHomePageBloc =
              argument['serviceHomePageBloc'] as ServiceHomePageBloc;
          return MultiBlocProvider(
              providers: [
                BlocProvider.value(value: serviceHomePageBloc),
                BlocProvider(
                    create: (context) => CheckoutServiceBloc(
                          getIt<CheckoutServiceReponseRemote>(),
                        )),
              ],
              child: DetailOrderServicePage(
                  sellDetail: reson, serviceHomePageBloc: serviceHomePageBloc));
        },
      ),

      GoRoute(
        path: RouteName.serviceReviewPage,
        builder: (context, state) {
          final argument = state.extra as Map<String, dynamic>;
          final serviceHomePageBloc =
              argument['serviceHomePageBloc'] as ServiceHomePageBloc;
          final reviewDetail =
              argument['orderServiceModel'] as OrderServiceModel;
          return BlocProvider.value(
            value: serviceHomePageBloc,
            child: ServiceReviewPage(
              serviceHomePageBloc: serviceHomePageBloc,
              orderServiceModel: reviewDetail,
            ),
          );
        },
      ),

      GoRoute(
        path: RouteName.cancelOrder,
        name: RouteName.cancelOrder,
        builder: (context, state) {
          return const CancellationReasonPage();
        },
      ),
      GoRoute(
        path: RouteName.ratingOrder,
        name: RouteName.ratingOrder,
        builder: (context, state) {
          final arguments = state.extra as CardOrderModel;
          return ProductReview(cardOrderModel: arguments);
        },
      ),
      GoRoute(
        path: RouteName.listOrderProductStatus,
        name: RouteName.listOrderProductStatus,
        builder: (context, state) {
          // final status = state.extra as String;
          return BlocProvider(
            create: (_) => ProductOrderBloc(getIt<MyOrderRepository>()),
            child: ListOrderProductStatus(),
          );
        },
      ),

      GoRoute(
        path: RouteName.orderProductPage,
        name: RouteName.orderProductPage,
        builder: (context, state) {
          return const ProductHomePage();
        },
      ),
      GoRoute(
        path: RouteName.productOrderDetailPage,
        name: RouteName.productOrderDetailPage,
        builder: (context, state) {
          final arguments = state.extra as Map<String, dynamic>;
          final productOrderBloc =
              arguments['productOrderBloc'] as ProductOrderBloc;
          final cardOrderModel =
              arguments['cardOrderModel'] as OrderProductModel?;
          return MultiBlocProvider(
            providers: [
              BlocProvider.value(value: productOrderBloc),
              BlocProvider(
                create: (context) => CheckoutProductBloc(
                  getIt<CheckoutProductReponseRemote>(),
                ),
              ),
            ],
            child: ProductOrderDetailPage(
              cardOrderModel: cardOrderModel,
              // productOrderBloc: productOrderBloc,
            ),
          );
        },
      ),

      // ------------------AppBar----------------------
      GoRoute(
        path: RouteName.notificationPage,
        name: RouteName.notificationPage,
        builder: (context, state) {
          return BlocProvider(
            create: (_) => getIt<NotificationBloc>(),
            child: const NotificationPage(),
          );
        },
      ),

      // CHAT FUNCTIONALITY USING PUBNUB
      GoRoute(
          path: RouteName.chatHomePage,
          name: RouteName.chatHomePage,
          builder: (context, state) {
            return ChatHomepage();
          }),
      GoRoute(
        path: RouteName.chatPage,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          if (extra['bloc'] != null) {
            return MaterialPage(
              child: BlocProvider.value(
                value: extra['bloc'] as ConversationListBloc,
                child: ChatPage(
                  partnerID: extra['partner_id'],
                  name: extra['name'],
                  avatar: extra['avatar'],
                ),
              ),
            );
          } else {
            return MaterialPage(
              child: ChatPage(
                partnerID: extra['partner_id'],
                name: extra['name'],
                avatar: extra['avatar'],
              ),
            );
          }
        },
      ),

      GoRoute(
        path: RouteName.interactiveImage,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return MaterialPage(
            child: BlocProvider.value(
              value: extra['bloc'] as ChatBloc,
              child: InteractiveImage(
                image: extra['image'],
                imageID: extra['image_id'],
                imageName: extra['image_name'],
              ),
            ),
          );
        },
      ),
      GoRoute(
        path: RouteName.chatOfferPage,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return ChatOfferPage(
            partnerID: extra['partner_id'],
            channelName: extra['channel_name'],
            chatBloc: extra['chat_bloc'] as ChatBloc,
          );
        },
      ),
      GoRoute(
        path: RouteName.chatInfoPage,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return ChatInfoPage(
            avatar: extra['avatar'],
            name: extra['name'],
          );
        },
      ),

      // OFFER FUNCTIONALITY
      GoRoute(
        path: RouteName.sellerOfferDetails,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return BlocProvider.value(
            value: extra['offer_bloc'] as OfferBloc,
            child: SellerOfferDetailsPage(
              offerID: extra['offer_id'],
              chatBloc: extra['chat_bloc'] as ChatBloc,
              partnerID: extra['partner_id'],
              channelName: extra['channel_name'],
            ),
          );
        },
      ),
      GoRoute(
        path: RouteName.buyerOfferDetails,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return BlocProvider.value(
            value: extra['offer_bloc'] as OfferBloc,
            child: BuyerOfferDetailsPage(
              offerID: extra['offer_id'],
              chatBloc: extra['chat_bloc'] as ChatBloc,
              partnerID: extra['partner_id'],
              channelName: extra['channel_name'],
            ),
          );
        },
      ),
      GoRoute(
        path: RouteName.offerCheckout,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return BlocProvider.value(
            value: extra['offer_bloc'] as OfferBloc,
            child: OfferCheckoutPage(
              offerID: extra['offer_id'],
              offer: extra['offer'] as Offers,
              bloc: extra['offer_bloc'] as OfferBloc,
            ),
          );
        },
      ),
      GoRoute(
        path: RouteName.editOfferPage,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          return EditOfferPage(
            partnerID: extra['partner_id'],
            channelName: extra['channel_name'],
            chatBloc: extra['chat_bloc'] as ChatBloc,
            offer: extra['offer'] as Offers,
          );
        },
      ),

      GoRoute(
        path: RouteName.mainHomePage,
        builder: (context, state) {
          return const MainHomePage();
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('No route defined for $state'),
      ),
    ),
  );
}
