class RouteName {
  static const String rootScreen = '/';
  static const String selectionPage = '/selectionPage';
  static const String privacyPolicyPage = '/privacyPolicyPage';

  //-----------------MODEL---------------
  static const String socialMode = '/socialMode';
  static const String businesMode = '/businesMode';
  static const String newsMode = '/newsMode';
  static const String marketMode = '/marketMode';
  static const String datingMode = '/datingMode';
  static const String strongBodyAiMode = '/strongBodyAiMode';

  //-----------------MARKETPLACE---------------

  static const String productGridScreen = '/productGridScreen';
  static const String productDetailScreen = '/productDetailScreen';
  static const String confirmOrderScreen = '/confirmOrderScreen';
  static const String likedScreen = '/likedScreen';
  static const String managePost = '/managePost';
  static const String orderScreen = '/orderScreen';
  static const String postProductScreen = '/postProductScreen';
  static const String orderDetailsScreen = '/orderDetailsScreen';
  static const String orderComplainsScreen = '/orderComplainsScreen';
  static const String checkoutScreen = '/checkoutScreen';
  static const String paymentSuccessPage = '/paymentSuccessPage';
  static const String personalInformationPage = '/personalInformationPage';
  static const String addCardPage = '/addCardPage';
  static const String orderSupportPage = '/orderSupportPage';
  static const String countryPickerDemo = '/countryPickerDemo';
  static const String marketplaceSearch = '/marketplaceSearch';
  static const String addressMarketplace = '/addressMarketplace';

  //-------------SETTING-------------
  static const String settingAccount = '/settingAccount';
  static const String settingMode = '/settingMode';
  static const String settingPage = '/settingPage';
  static const String generalSetting = '/generalSetting';
  static const String settingPrivacyPage = '/settingPrivacyPage';
  static const String syncSetting = '/syncSetting';

  static const String success = '/success';
  static const String settingDatingMode = '/settingDatingMode';
  static const String settingNewMode = '/settingNewMode';
  static const String settingSocialMode = '/settingSocialMode';
  static const String settingBusinessMode = '/settingBusinessMode';
  static const String settingMarketplanceMode = '/settingMarketplanceMode';
  static const String accountDeactivationPage = '/accountDeactivationPage';
  static const String settingVerifyAccount = '/settingVerifyAccount';
  static const String changePasswordPage = '/changePasswordPage';

//---------------------BUSINES--------------
  static const String businesHome = '/businesHome';
  static const String businesOverView = '/businesOverView';
  static const String bussinesBottomSheet = '/businesBottomSheetItem';

//---------------------DATING--------------
  static const String datingHome = '/datingHome';
  static const String datingPage = '/datingPage';
  static const String editSeekingPage = '/editSeekingPage';
  static const String inforPage = '/infoPage';
  static const String editBioPage = '/editBioPage';
  static const String addPhotoPage = '/addPhotoPage';
  static const String recentDatingnPage = '/recentDatingnPage';
  static const String likedYouScreen = '/likedYouScreen';
  static const String matchesDatingPage = '/matchesDatingPage';
  static const String seekingScreen = '/seekingScreen';
  static const String matchesSuccessPage = '/matchesSuccessPage';
  static const String hobbiesPage = '/hobbiesPage';

//---------------------NEW--------------
  static const String homeNew = '/homeNew';
  static const String bbcNew = '/bbcNew';
  static const String postNew = '/postNew';
  static const String likedNew = '/likedNew';
  static const String reviewNew = '/reviewNew';
  static const String editNew = '/editNew';
  static const String SearchNewScreen = '/SearchNewScreen';
  static const String editNewPage = '/editNewPage';
  static const String NewsScreen = '/NewsScreen';
  static const String reportNew = '/report';
  static const String aiAssistant = '/aiAssistant';

  //---------------------SOCIAL--------------
  static const String homeSocial = '/homeSocial';
  static const String socialPage = '/socialPage';
  static const String homeSocialPost = '/homeSocialPost';
  static const String homeSocialHistory = '/homeSocialHistory';
  static const String profileSocial = '/profileSocial';
  static const String accessLocation = '/accessLocation';
  static const String checkIn = '/checkIn';
  static const String record = '/record';
  static const String playBackScreen = '/playBackScreen';
  static const String createPostRecord = '/createPostRecord';
  static const String historySocialPage = '/historySocialPage';
  static const String aiAssistantSocial = '/aiAssistantSocial';
  static const String socialAiAssistant = '/socialAiAssistant';

  static const String newPage = '/newPage';

  //---------------------AUTH--------------
  static const String registerPage = '/registerPage';
  static const String loginPage = '/loginPage';
  static const String forgotPasswordPage = '/forgotPasswordPage';

  // static const String changePasswordPage = '/changePasswordPage';
  static const String createPasswordPage = '/createPasswordPage';
  static const String otpPage = '/otpPage';
  static const String contactImport = '/contactImport';
  static const String contactConnect = '/contactConnect';
  static const String verifyPhoneScreen = '/verifyPhoneScreen';
  static const String createForgotPasswordPage = '/createForgotPasswordPage';

  //---------------------ADMIN USER--------------
  static const String adminUser = '/adminUser';
  static const String adminUserProfile = '/adminUserProfile';
  static const String homeUserPage = '/homeUserPage';
  static const String connectFriendPage = '/connectFriendPage';

  //---------------------Profile View--------------
  static const String profileView = '/profileView';

  //---------------------Help Center--------------
  static const String helpCenter = '/helpCenter';
  static const String supportRequest = '/supportRequest';
  static const String orderSupport = '/orderSupport';
  static const String helpCenterQuestion = '/helpCenterQuestion';
  static const String helpSearch = '/helpSearch';

  //---------------------Create Service--------------
  static const String createService = '/createService';
  static const String allSuggestService = '/allSuggestService';
  static const String checkoutService = '/checkoutService';
  static const String paymentFailPage = '/paymentFailPage';

  //---------------------StrongBodyAiMode--------------
  static const String detailPage = '/DetailsPage';
  static const String detailProductPage = '/detailProductPage';
  static const String detailBlogPage = '/detailBlogPage';

  static const String homeStrongBody = '/homeStrongBody';
  static const String searchStrongBody = '/searchStrongBody';
  static const String allFeaturedProducts = '/allFeaturedProducts';
  static const String allBlogs = '/allBlogs';
  static const String allExperts = '/allExperts';

  static const String filterPage = '/filterPage';

  // static const String postRequiredHome = '/postRequiredHome';
  static const String postBuyerRequest = '/postBuyerRequest';
  static const String viewAllBuyerRequest = '/viewAllBuyerRequest';

  static const String postBuyerRequestCheck = '/postBuyerRequestCheck';

  static const String buyerRequestDetail = '/buyerRequestDetail';

  static const String myRequest = '/myRequestPage';
  static const String allService = '/allService';
  static const String requestDetail = '/requestDetail';

  // 'BALANCE' ROUTES
  static const String balancePage = '/balancePage';
  static const String withdrawPage = '/withdrawPage';
  static const String managePayout = '/managePayout';
  static const String addNewPayout = '/addNewPayout';
  static const String manageExistingPayout = '/manageExistingPayout';
  static const String paymentHistory = '/paymentHistory';
  static const String withdrawResult = '/withdrawResult';

  //---------------------My Order--------------
  static const String myOrderHomePage = '/myOrderHomePage';
  static const String productOrderDetailPage = '/productOrderDetailPage';
  static const String serviceDetail = '/serviceDetail';
  static const String serviceInprogressDetail = '/serviceInprogressDetail';
  static const String servicePendingDetail = '/servicePendingDetail';
  static const String serviceComletedDetail = '/serviceComletedDetail';
  static const String serviceDeliveredDetail = '/serviceDeliveredDetail';
  static const String serviceCancelDetail = '/serviceCancelDetail';
  static const String serviceLateDetail = '/serviceLateDetail';
  static const String serviceDisputedDetail = '/serviceDisputedDetail';
  static const String serviceRefundDetail = '/serviceRefundDetail';
  static const String serviceReview = '/serviceReview';
  static const String serviceReviewSuccess = '/serviceReviewSuccess';
  static const String cancelOrder = '/cancelOrder';
  static const String ratingOrder = '/ratingOrder';
  static const listOrderProductStatus = '/listOrderProductStatus';
  static const String orderProductPage = '/orderProductPage';
  static const String statusOrder = '/statusOrder';
  static const String orderServiceDetailPage = '/orderServiceDetailPage';
  static const String serviceReviewPage = '/serviceReviewPage';

  //---------------------SHOPPING CART--------------
  static const String homeShoppingCart = '/homeShoppingCart';
  static const String checkoutProduct = '/checkoutProduct';
  static const String orderSuccessScreen = '/orderSuccessScreen';
  static const String addressSelectionScreen = '/addressSelectionScreen';
  static const String editAddressSelectionScreen =
      '/editAddressSelectionScreen';

  static const String notificationPage = '/notificationPage';

  static const String shareMessage = '/shareMessage';

  // CHAT FUNCTIONALITY USING PUBNUB
  static const String chatHomePage = '/chatHomePage';
  static const String chatPage = '/chatPage';
  static const String interactiveImage = '/interactiveImage';
  static const String chatOfferPage = '/chatOfferPage';
  static const String chatInfoPage = '/chatInfoPage';

  // OFFER FUNCTIONALITY
  static const String sellerOfferDetails = '/sellerOfferDetails';
  static const String buyerOfferDetails = '/buyerOfferDetails';
  static const String offerCheckout = '/offerCheckout';
  static const String editOfferPage = '/editOfferPage';

  static const String loginRedirect = '/loginRedirect';

  static const String searchHomeStrongBody = '/searchHomeStrongBody';
  static const String searchFilterStrongBody = '/searchFilterStrongBody';
  static const String searchSeller = '/searchSeller';

  static const String serviceDetailSeller = '/serviceDetailSeller';
  static const String productDetailSeller = '/productDetailSeller';

  // Main Home Page
  static const String mainHomePage = '/mainHomePage';

  // CheckoutProduct
  static const String checkoutProductPage = '/checkoutProductPage';
  static const String paymentResultPage = '/paymentResultPage';

  //Seller
  static const String myProductPage = '/myProductPage';
}
