// ignore_for_file: unused_import, prefer_const_constructor
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:multime_app/app/routers/routers_name.dart';
import 'package:multime_app/core/components/payment_result_card.dart';
import 'package:multime_app/core/components/payment_result_page.dart';
import 'package:multime_app/core/data/param/order_product_param.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/model/blog_model.dart';
import 'package:multime_app/core/model/offer/offers.dart';
import 'package:multime_app/core/model/payment_result_data.dart';
import 'package:multime_app/modules/admin_user_mode/presentation/home/<USER>';
import 'package:multime_app/modules/auth_mode/data/repositories/auth_repository_remote.dart';
import 'package:multime_app/modules/auth_mode/presentation/change_password/bloc/change_password_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/complete_user_registration/bloc/complete_user_registration_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/complete_user_registration/screens/contact_connect_screen.dart';
import 'package:multime_app/modules/auth_mode/presentation/complete_user_registration/screens/contact_import_screen.dart';
import 'package:multime_app/modules/auth_mode/presentation/forget_password/forget_password.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/bloc/login_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/login/login_page.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/bloc/otp_flow_type.dart';
import 'package:multime_app/modules/auth_mode/presentation/otp/otp_screen.dart';
import 'package:multime_app/modules/auth_mode/presentation/set_password/bloc/set_password_bloc.dart';
import 'package:multime_app/modules/auth_mode/presentation/set_password/create_forgot_password.dart';
import 'package:multime_app/modules/auth_mode/presentation/set_password/create_password.dart';
import 'package:multime_app/modules/auth_mode/presentation/widgets/success_widget.dart';
import 'package:multime_app/modules/balance/presentation/home/<USER>';
import 'package:multime_app/modules/balance/presentation/manage_payout/add_payout_method_page.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/manage_payout_method_page.dart';
import 'package:multime_app/modules/balance/presentation/manage_payout/manage_payout_page.dart';
import 'package:multime_app/modules/balance/presentation/payment_history/payment_history_page.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/withdraw_page.dart';
import 'package:multime_app/modules/balance/presentation/withdraw/withdraw_result_page.dart';
import 'package:multime_app/modules/busines_mode/presentation/find_job/find_job_page.dart';
import 'package:multime_app/modules/busines_mode/presentation/home_busines/busines_page.dart';
import 'package:multime_app/modules/busines_mode/presentation/over_view_busines/over_view_busines_page.dart';
import 'package:multime_app/modules/chat/presentation/bloc/chat/chat_bloc.dart';
import 'package:multime_app/modules/chat/presentation/bloc/conversation_list/conversation_list_bloc.dart';
import 'package:multime_app/modules/chat/presentation/chat_info_page.dart';
import 'package:multime_app/modules/chat/presentation/chat_page.dart';
import 'package:multime_app/modules/chat/presentation/interactive_image.dart';
import 'package:multime_app/modules/country/bloc/country_bloc.dart';
import 'package:multime_app/modules/country/service/country/country_service.dart';
import 'package:multime_app/modules/dating_mode/presentation/edit/edit_seeking_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/home/<USER>';
import 'package:multime_app/modules/dating_mode/presentation/info/bloc/info_dating_bloc.dart';
import 'package:multime_app/modules/dating_mode/presentation/info/infor_dating_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/info/widgets/hobbies_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/like/like_dating_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/matches/matches_dating_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/matches/matches_success_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/recent/recent_dating_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/seeking/seeking_dating_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/update/update_bio_dating_page.dart';
import 'package:multime_app/modules/dating_mode/presentation/welcome/welcome_dating_page.dart';
import 'package:multime_app/modules/help_center_mode/presentation/help_center_search/help_center_search.dart';
import 'package:multime_app/modules/main_home_page/presentation/home_page/main_home_page.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/address.dart';
import 'package:multime_app/modules/marketplace_mode/data/models/product_detail_model.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/address_repository.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/category_repository.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/payment_repository.dart';
import 'package:multime_app/modules/marketplace_mode/data/repositories/product_repository.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_add_card/add_card_page.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_address/bloc/address_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_check_out/checkout_page.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_confirm_order/bloc/payment_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_confirm_order/confirm_order_screen.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_edit_address/address_edit_info_page.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_home/bloc/marketplace_home_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_home/marketplace_page.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_likes/bloc/liked_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_likes/liked_page.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_payment/payment_success_page.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/bloc/post_product_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/grid_bloc/product_grid_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/grid_bloc/product_grid_event.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product/post_product_page.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_product_detail/bloc/product_detail_bloc.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/marketplace_report/report_order.dart';
import 'package:multime_app/modules/marketplace_mode/presentation/search/marketplace_search_page.dart';
import 'package:multime_app/modules/my_order_mode/data/model/my_order_service_model.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_product_model.dart';
import 'package:multime_app/modules/my_order_mode/data/model/orderModel/order_service_model.dart';
import 'package:multime_app/modules/my_order_mode/data/repository/my_order_repository.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/fake_data/card_order_model.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_state.dart';
import 'package:multime_app/modules/my_order_mode/my_order_product/presentation/product_order_detail_page/product_order_detail_page.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/detail_order_service/detail_order_service_page.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_home_page/bloc/service_home_page_bloc.dart';
import 'package:multime_app/modules/my_order_mode/my_order_service/presentation/service_review_page/service_review_page.dart';
import 'package:multime_app/modules/news_mode/data/models/user_new.dart';
import 'package:multime_app/modules/news_mode/presentation/ai_assistant/ai_assistant_page.dart';
import 'package:multime_app/modules/news_mode/presentation/edit/edit_new_screen.dart';
import 'package:multime_app/modules/news_mode/presentation/home_page/home_page.dart';
import 'package:multime_app/modules/news_mode/presentation/home_page/new_page.dart';
import 'package:multime_app/modules/news_mode/presentation/like/liked_news.dart';
import 'package:multime_app/modules/news_mode/presentation/news/news_screen.dart';
import 'package:multime_app/modules/news_mode/presentation/post/bloc/post_bloc.dart';
import 'package:multime_app/modules/news_mode/presentation/post/post_request_news.dart';
import 'package:multime_app/modules/news_mode/presentation/review/review_news.dart';
import 'package:multime_app/modules/news_mode/presentation/search_new/search_new_screen.dart';
import 'package:multime_app/modules/offer/buyer_offer_details_page.dart';
import 'package:multime_app/modules/offer/chat_offer_page.dart';
import 'package:multime_app/modules/offer/edit_offer_page.dart';
import 'package:multime_app/modules/offer/offer_checkout_page.dart';
import 'package:multime_app/modules/offer/presentation/bloc/offer_bloc.dart';
import 'package:multime_app/modules/offer/seller_offer_details_page.dart';
import 'package:multime_app/modules/profile_view_mode/data/enum/enum.dart';
import 'package:multime_app/modules/profile_view_mode/data/repositories/profile_seller_repository.dart';
import 'package:multime_app/modules/profile_view_mode/data/repositories/search_seller_repository.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_event.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/profile_view_home/profile_view_home.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/seach/bloc/search_seller_bloc.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/seach/search_seller.dart';
import 'package:multime_app/modules/profile_view_mode/presentation/service_seller/detail_service_seller.dart';
import 'package:multime_app/modules/setting_mode/bloc/user_bloc.dart';
import 'package:multime_app/modules/setting_mode/presentation/account/setting_account_page.dart';
import 'package:multime_app/modules/setting_mode/presentation/change_password/change_password_page.dart';
import 'package:multime_app/modules/setting_mode/presentation/general/account_deactivation_page/account_deactivation_page.dart';
import 'package:multime_app/modules/setting_mode/presentation/general/general_setting.dart';
import 'package:multime_app/modules/setting_mode/presentation/home/<USER>';
import 'package:multime_app/modules/setting_mode/presentation/mode/mode_page/setting_dating_mode.dart';
import 'package:multime_app/modules/setting_mode/presentation/mode/mode_page/setting_marketplace_mode.dart';
import 'package:multime_app/modules/setting_mode/presentation/mode/setting_mode_page.dart';
import 'package:multime_app/modules/setting_mode/presentation/privacy/setting_privacy_page.dart';
import 'package:multime_app/modules/shopping_cart/model/product.dart';
import 'package:multime_app/modules/shopping_cart/model/shop.dart';
import 'package:multime_app/modules/shopping_cart/presentation/address/address_selection_screen.dart';
import 'package:multime_app/modules/shopping_cart/presentation/address/edit_address_selection_screen.dart';
import 'package:multime_app/modules/shopping_cart/presentation/checkout/checkout_product_screen.dart';
import 'package:multime_app/modules/shopping_cart/presentation/home_shopping_cart/home_shopping_cart.dart';
import 'package:multime_app/modules/shopping_cart/presentation/order_success/order_success_screen.dart';
import 'package:multime_app/modules/social_mode/presentation/social_ai_assistant/social_ai_assistant.dart';
import 'package:multime_app/modules/social_mode/presentation/social_history/social_history.dart';
import 'package:multime_app/modules/social_mode/presentation/social_report/social_report.dart';
import 'package:multime_app/modules/start_mode/splash_screen.dart';
import 'package:multime_app/modules/strongbody.ai_mode/data/models/services/service_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/all_blogs/all_blogs_page.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/all_experts/all_experts_page.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/all_featured_products/all_featured_products.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/all_suggest_service/all_suggest_service_import.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/bloc/check_out_product_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/check_out_product.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/checkout_product/response/check_out_product_reponse.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/bloc/detail_blog_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_blog/detail_blog_page.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/bloc/detail_product_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/detail_product_import.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_product/model/product_model.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/bloc/detail_service_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/detail_service/detail_service_import.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/home_strongbody_ai_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/home/<USER>/search_filter.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/post_buyer_request/bloc/post_buyer_request_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/post_buyer_request/bloc/post_buyer_request_event.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/post_buyer_request/post_buyer_request_page.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/requestDetails/bloc/request_detail_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/requestDetails/request_detail_screen.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/viewAllBuyerRequest/bloc/view_all_buyer_request_bloc.dart';
import 'package:multime_app/modules/strongbody.ai_mode/presentation/viewAllBuyerRequest/view_all_buyer_request.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/buyer_request_repository.dart';
import 'package:multime_app/modules/strongbody.ai_mode/repositories/home_strongbody_ai_repository.dart';

import '../../modules/admin_user_mode/presentation/connect_friend/connect_friend_page.dart';
import '../../modules/auth_mode/presentation/login/widgets/login_redirect.dart';
import '../../modules/auth_mode/presentation/otp/bloc/otp_bloc.dart';
import '../../modules/auth_mode/presentation/register/bloc/register_bloc.dart';
import '../../modules/auth_mode/presentation/register/register_screen.dart';
import '../../modules/busines_mode/presentation/find_job/cubit/find_job_cubit.dart';
import '../../modules/busines_mode/presentation/home_busines/busines_home_page.dart';
import '../../modules/chat/data/repository/chat_repository.dart';
import '../../modules/chat/presentation/chat_homepage.dart';
import '../../modules/create_service_mode/presentation/create_service_home/create_service_home.dart';
import '../../modules/dating_mode/presentation/home/<USER>';
import '../../modules/help_center_mode/presentation/help_center_home/help_center_home.dart';
import '../../modules/help_center_mode/presentation/help_center_order_support/help_center_order_support.dart';
import '../../modules/help_center_mode/presentation/help_center_question/help_center_question.dart';
import '../../modules/help_center_mode/presentation/help_center_request/help_center_request.dart';
import '../../modules/marketplace_mode/presentation/manage_post/manage_bottom_bar.dart';
import '../../modules/marketplace_mode/presentation/marketplace_address/address_marketplace.dart';
import '../../modules/marketplace_mode/presentation/marketplace_payment/payment_fail_page.dart';
import '../../modules/marketplace_mode/presentation/marketplace_product/product_grid_page.dart';
import '../../modules/marketplace_mode/presentation/marketplace_product_detail/product_detail_page.dart';
import '../../modules/my_order_mode/my_order_product/presentation/cancellation_reason/cancellation_reason_page.dart';
import '../../modules/my_order_mode/my_order_product/presentation/product_home_page/bloc/product_order_bloc.dart';
import '../../modules/my_order_mode/my_order_product/presentation/product_home_page/product_home_page.dart';
import '../../modules/my_order_mode/my_order_product/presentation/product_home_page/tab_bar/list_order_product_status.dart';
import '../../modules/my_order_mode/my_order_product/presentation/product_review/product_review.dart';
import '../../modules/my_order_mode/my_order_service/presentation/my_order_home_page/my_order_home_page.dart';
import '../../modules/my_order_mode/my_order_service/presentation/widgets/review_order/review_detail.dart';
import '../../modules/my_order_mode/my_order_service/presentation/widgets/review_order/review_success.dart';
import '../../modules/notification_mode/presentation/bloc/notification_bloc.dart';
import '../../modules/notification_mode/presentation/notification_page.dart';
import '../../modules/profile_view_mode/data/models/product_seller_model.dart';
import '../../modules/profile_view_mode/presentation/product_seller/detail_product_seller.dart';
import '../../modules/profile_view_mode/presentation/product_seller/bloc/detail_product_seller_bloc.dart';
import '../../modules/profile_view_mode/presentation/profile_view_home/bloc/profile_view_home_bloc.dart';
import '../../modules/seller/presentation/my_product/my_product_page.dart';
import '../../modules/setting_mode/presentation/mode/mode_page/setting_business_mode.dart';
import '../../modules/setting_mode/presentation/mode/mode_page/setting_new_mode.dart';
import '../../modules/setting_mode/presentation/mode/mode_page/setting_social_mode.dart';
import '../../modules/setting_mode/presentation/sync/setting_sync.dart';
import '../../modules/setting_mode/presentation/verify_account/verify_account_page.dart';
import '../../modules/shopping_cart/bloc/shopping_cart_bloc.dart';
import '../../modules/social_mode/presentation/social_check_in/social_check_in.dart';
import '../../modules/social_mode/presentation/social_create_post/social_create_post.dart';
import '../../modules/social_mode/presentation/social_create_post/social_create_post_record.dart';
import '../../modules/social_mode/presentation/social_home/social_home_page.dart';
import '../../modules/social_mode/presentation/social_home/social_page.dart';
import '../../modules/social_mode/presentation/social_profile/social_profile.dart';
import '../../modules/social_mode/presentation/social_record/social_record.dart';
import '../../modules/social_mode/presentation/widgets/create_post/access_location.dart';
import '../../modules/start_mode/privacy_policy_page.dart';
import '../../modules/start_mode/selection_page.dart';
import '../../modules/strongbody.ai_mode/data/response/checkout_service_repository.dart';
import '../../modules/strongbody.ai_mode/presentation/checkout_service/bloc/checkout_service_bloc.dart';
import '../../modules/strongbody.ai_mode/presentation/checkout_service/checkout_service_page.dart';
import '../../modules/strongbody.ai_mode/presentation/checkout_service/response/checkout_service_respose.dart';
import '../../modules/strongbody.ai_mode/presentation/home/<USER>/bloc/search_home_strong_bloc.dart';
import '../../modules/strongbody.ai_mode/presentation/home/<USER>/search_text_field.dart';
import '../../modules/strongbody.ai_mode/presentation/post_buyer_request/widgets/post_request_check.dart';

part 'app_router.dart';
