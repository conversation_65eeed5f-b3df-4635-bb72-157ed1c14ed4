import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multime_app/app.dart';
import 'package:multime_app/core/initialization/app_initializer.dart';
import 'package:multime_app/core/di/locator.dart';

ServiceLocator dependencyInjector = ServiceLocator();

void main() async {
  try {
    // Initialize all app services
    await AppInitializer.initialize();

    // Initialize localization
    await EasyLocalization.ensureInitialized();

    // Run the app
    runApp(
      EasyLocalization(
        supportedLocales: const [
          Locale('en'),
          Locale('ar'),
          Locale('hi'),
          Locale('zh'),
          Locale('es'),
          Locale('vi'),
        ],
        path: 'assets/translations',
        startLocale: const Locale('en'),
        fallbackLocale: const Locale('en'),
        child: const MyApp(),
      ),
    );
  } catch (e, stackTrace) {
    // Handle initialization errors
    print('❌ MAIN: Failed to initialize app: $e');
    print('❌ MAIN: Stack trace: $stackTrace');

    // Show error screen
    runApp(
      AppErrorScreen(
        error: e.toString(),
        onRetry: () {
          // Restart app
          main();
        },
      ),
    );
  }
}
