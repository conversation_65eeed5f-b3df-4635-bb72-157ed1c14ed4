import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:multime_app/app/routers/route_imports.dart';
import 'package:multime_app/core/config/check_network.dart';
import 'package:multime_app/core/di/locator.dart';
import 'package:multime_app/core/domain/storages/global_storage.dart';
import 'package:multime_app/core/global/global_presence/bloc/global_bloc.dart';
import 'package:multime_app/core/global/global_presence/bloc/global_event.dart';
import 'package:multime_app/modules/translate/bloc/language_bloc.dart';
import 'package:multime_app/modules/translate/bloc/language_state.dart';
import 'package:multime_app/core/services/network_service.dart';
import 'core/streams/presence_stream_controller.dart';
import 'modules/application/bottom_bar/bloc/application_bloc.dart';
import 'modules/chat/data/repository/chat_repository.dart';
import 'modules/dating_mode/presentation/info/bloc/info_dating_bloc.dart';
import 'modules/theme/bloc/theme_bloc.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  GlobalBloc? _globalBloc;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setupConnectivityListener();
    _autoEmitPresenceSubscribed();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  void _setupConnectivityListener() {
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      if (_globalBloc != null) {
        final result =
            results.isNotEmpty ? results.first : ConnectivityResult.none;

        if (result == ConnectivityResult.none) {
          _globalBloc!.add(NetworkLost());
        } else {
          _globalBloc!.add(NetworkReconnected());
        }
      }
    });
  }

  Future<void> _autoEmitPresenceSubscribed() async {
    try {
      final storage = getIt<GlobalStorage>();
      final uid = storage.uid;
      final accessToken = storage.accessToken;

      if (uid != null && accessToken != null) {
        final chatRepo = getIt<ChatRepository>();
        final presenceToken = await chatRepo.presenceToken(uid);
        PresenceStreamController.emit(PresenceSubscribed(
          authKey: presenceToken.token,
          userId: uid,
        ));
      }
    } catch (e) {
      // Silent fail for presence subscription
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  void _handleAppResumed() async {
    try {
      final storage = getIt<GlobalStorage>();
      final uid = storage.uid;

      if (uid != null && _globalBloc != null) {
        _globalBloc!.add(AppResumed(userId: uid));
      }
    } catch (e) {
      // Silent fail for app resumed handling
    }
  }

  void _onGlobalBlocCreated(GlobalBloc bloc) {
    _globalBloc = bloc;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: NetworkService(),
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(create: (_) => ThemeBloc(getIt<GlobalStorage>())),
            BlocProvider(
                create: (_) => LanguageBloc(getIt<GlobalStorage>(), context)),
            BlocProvider<GlobalBloc>(
              create: (context) {
                final bloc =
                    GlobalBloc(chatRepository: getIt<ChatRepository>());

                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (context.mounted) {
                    final state =
                        context.findAncestorStateOfType<_MyAppState>();
                    if (state != null) {
                      state._onGlobalBlocCreated(bloc);
                    }
                  }
                });
                return bloc;
              },
              lazy: false,
            ),
            BlocProvider(create: (_) => ApplicationBloc()),
            BlocProvider(create: (context) => InfoDatingBloc()),
          ],
          child: ScreenUtilInit(
            designSize: const Size(375, 812),
            builder: (context, child) {
              return BlocBuilder<ThemeBloc, ThemeState>(
                builder: (context, state) {
                  return BlocListener<LanguageBloc, LanguageState>(
                    listener: (context, state) {
                      if (state is LanguageChanged) {
                        EasyLocalization.of(context)?.setLocale(state.locale);
                      }
                    },
                    child: MaterialApp.router(
                      debugShowCheckedModeBanner: false,
                      showPerformanceOverlay: false,
                      theme: state.appTheme,
                      localizationsDelegates: context.localizationDelegates,
                      supportedLocales: context.supportedLocales,
                      locale: context.locale,
                      routerDelegate: AppRoutes.router.routerDelegate,
                      routeInformationParser:
                          AppRoutes.router.routeInformationParser,
                      routeInformationProvider:
                          AppRoutes.router.routeInformationProvider,
                      builder: (context, child) {
                        return Stack(
                          children: [
                            if (child != null) child,
                            if (!NetworkService().isConnected)
                              const CheckNetwork(),
                          ],
                        );
                      },
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

/// Error screen widget for initialization failures
class AppErrorScreen extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const AppErrorScreen({
    super.key,
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Failed to initialize app',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error: $error',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: onRetry,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
