import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

class ShareNative {
  static const platform = MethodChannel("com.multime.app/share");

// Hàm mở ứng dụng
  Future<void> shareToMessenger(String text, BuildContext context) async {
    try {
      await platform.invokeMethod("shareToMessenger", {"text": text});
      context.pop();
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToInstagram(String text, BuildContext context) async {
    try {
      await platform.invokeMethod("shareToInstagram", {"text": text});
      context.pop();
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToSnapchat(String text, BuildContext context) async {
    try {
      await platform.invokeMethod("shareToSnapchat", {"text": text});
      context.pop();
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToSMS(String text, BuildContext context) async {
    try {
      await platform.invokeMethod("shareToSMS", {"text": text});
      context.pop();
    } on PlatformException catch (e) {
      print("Error: ${e.message}");
    }
  }

  Future<void> shareToSMSWithPhone(
      String text, String phoneNumber, BuildContext context) async {
    try {
      print('ShareNative - Text: $text');
      print('ShareNative - Phone: $phoneNumber');

      final result = await platform.invokeMethod(
          "shareToSMSWithPhone", {"text": text, "phoneNumber": phoneNumber});
      print('ShareNative - Result: $result');
    } on PlatformException catch (e) {
      print("PlatformException: ${e.code} - ${e.message}");
      print("Details: ${e.details}");

      // Thử fallback method
      try {
        print("Trying fallback method...");
        final fallbackResult = await platform.invokeMethod(
            "shareToSMSWithPhoneFallback",
            {"text": text, "phoneNumber": phoneNumber});
        print('Fallback result: $fallbackResult');
      } catch (fallbackError) {
        print("Fallback also failed: $fallbackError");
      }
    } catch (e) {
      print("General Error: $e");
    }
  }

  // Test method để kiểm tra SMS functionality
  Future<void> testSMS(BuildContext context) async {
    try {
      print('Testing SMS functionality...');
      final result = await platform.invokeMethod("testSMS");
      print('Test SMS result: $result');
    } on PlatformException catch (e) {
      print("Test SMS PlatformException: ${e.code} - ${e.message}");
    } catch (e) {
      print("Test SMS General Error: $e");
    }
  }
}
